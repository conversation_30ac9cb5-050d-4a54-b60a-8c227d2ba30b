<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>7-Eleven APN Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #006400;
            border-bottom: 2px solid #FFA500;
            padding-bottom: 10px;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .panel {
            flex: 1;
            min-width: 300px;
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
        }
        .error {
            color: red;
            background-color: #ffeeee;
            padding: 10px;
            border-left: 4px solid red;
            margin: 10px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #006400;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #004d00;
        }
        #orderList {
            list-style: none;
            padding: 0;
        }
        #orderList li {
            background: white;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #006400;
        }
        .status-A {
            border-left-color: #FFA500 !important; /* Pending */
        }
        .status-B {
            border-left-color: #006400 !important; /* Completed */
        }
        .status-D {
            border-left-color: #FF0000 !important; /* Expired */
        }
    </style>
</head>
<body>
    <h1>7-Eleven APN Tester</h1>
    
    <div class="container">
        <div class="panel">
            <h2>Update Payment Status</h2>
            
            <div class="form-group">
                <label for="orderNumber">Order Number</label>
                <input type="text" id="orderNumber" placeholder="Enter order number">
            </div>
            
            <div class="form-group">
                <button id="generateRandom">Generate Random</button>
            </div>
            
            <div class="form-group">
                <label for="paymentStatus">Payment Status</label>
                <select id="paymentStatus">
                    <option value="A">A - Pending</option>
                    <option value="B">B - Completed</option>
                    <option value="D">D - Expired</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="paymentType">Payment Type</label>
                <select id="paymentType">
                    <option value="7-Eleven">7-Eleven</option>
                    <option value="Credit Card">Credit Card</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="storeName">Store Name</label>
                <input type="text" id="storeName" value="7-ELEVEN TEST">
            </div>
            
            <button id="sendCallback">Send Callback</button>
            
            <div id="callbackResult" class="form-group"></div>
        </div>
        
        <div class="panel">
            <h2>Order Status List</h2>
            <button id="refreshOrders">Refresh Orders</button>
            <div id="orderStatusList"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000'; // Base URL for APN server
        
        // DOM elements
        const orderNumberInput = document.getElementById('orderNumber');
        const generateRandomBtn = document.getElementById('generateRandom');
        const paymentStatusSelect = document.getElementById('paymentStatus');
        const paymentTypeSelect = document.getElementById('paymentType');
        const storeNameInput = document.getElementById('storeName');
        const sendCallbackBtn = document.getElementById('sendCallback');
        const callbackResultDiv = document.getElementById('callbackResult');
        const refreshOrdersBtn = document.getElementById('refreshOrders');
        const orderStatusListDiv = document.getElementById('orderStatusList');
        
        // Generate random order number
        generateRandomBtn.addEventListener('click', () => {
            const randomNum = Math.floor(10000000 + Math.random() * 90000000);
            orderNumberInput.value = randomNum;
        });
        
        // Send callback to APN server
        sendCallbackBtn.addEventListener('click', async () => {
            const orderNumber = orderNumberInput.value;
            const status = paymentStatusSelect.value;
            const paymentType = paymentTypeSelect.value;
            const storeName = storeNameInput.value;
            
            if (!orderNumber) {
                showError('Please enter an order number');
                return;
            }
            
            try {
                callbackResultDiv.innerHTML = '<p>Sending request...</p>';
                
                const response = await fetch(`${API_URL}/update-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        orderNumber,
                        status,
                        paymentType,
                        storeName
                    })
                });
                
                if (response.ok) {
                    const result = await response.text();
                    callbackResultDiv.innerHTML = `<p>Response: ${result}</p>`;
                } else {
                    const errorText = await response.text();
                    showError(`Error: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                showError(`Error: Failed to fetch`);
            }
        });
        
        // Refresh order list
        refreshOrdersBtn.addEventListener('click', fetchOrders);
        
        // Fetch orders from server
        async function fetchOrders() {
            try {
                orderStatusListDiv.innerHTML = '<p>Loading orders...</p>';
                
                const response = await fetch(`${API_URL}/orders`);
                
                if (response.ok) {
                    const orders = await response.json();
                    displayOrders(orders);
                } else {
                    showOrdersError('Error fetching orders: ' + await response.text());
                }
            } catch (error) {
                showOrdersError('Error fetching orders: Failed to fetch');
            }
        }
        
        // Display orders in the list
        function displayOrders(orders) {
            if (orders.length === 0) {
                orderStatusListDiv.innerHTML = '<p>No orders found</p>';
                return;
            }
            
            let html = '<ul id="orderList">';
            
            orders.forEach(order => {
                const statusText = getStatusText(order.status);
                
                html += `
                    <li class="status-${order.status}">
                        <strong>Order #:</strong> ${order.orderNumber}<br>
                        <strong>Status:</strong> ${statusText}<br>
                        <strong>Payment Type:</strong> ${order.paymentType}<br>
                        <strong>Store:</strong> ${order.storeName}<br>
                        <strong>Last Updated:</strong> ${new Date(order.updatedAt).toLocaleString()}
                    </li>
                `;
            });
            
            html += '</ul>';
            orderStatusListDiv.innerHTML = html;
        }
        
        // Get text representation of status code
        function getStatusText(status) {
            switch (status) {
                case 'A': return 'Pending';
                case 'B': return 'Completed';
                case 'D': return 'Expired';
                default: return 'Unknown';
            }
        }
        
        // Show error in callback result
        function showError(message) {
            callbackResultDiv.innerHTML = `<div class="error">${message}</div>`;
        }
        
        // Show error in orders list
        function showOrdersError(message) {
            orderStatusListDiv.innerHTML = `<div class="error">${message}</div>`;
        }
        
        // Initialize
        fetchOrders();
    </script>
</body>
</html> 