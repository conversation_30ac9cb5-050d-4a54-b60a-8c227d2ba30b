/**
 * Interactive 7-Eleven APN Callback Simulator
 * 
 * This script simulates the full lifecycle of a 7-Eleven payment:
 * 1. Pending payment (status A)
 * 2. Payment completed (status B)
 * 3. Payment expired (status D)
 * 
 * It updates the order in orders.json based on the APN callback response.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const axios = require('axios');
const readline = require('readline');

// Configuration
const CONFIG = {
  // APN callback endpoint URL - Now pointing to your real endpoint
  apnUrl: 'https://sim.dailoanshop.net/api/payment/apn-callback',
  
  // Path to orders.json file
  ordersFilePath: path.join(__dirname, 'data', 'orders.json'),
  
  // Order ID to update
  testOrderId: '711TEST-001',
  
  // 7-Eleven API credentials
  apiId: '827315300001',
  
  // Debug mode
  debug: true
};

// Test order info
let TEST_ORDER = null;

// Create readline interface for user interaction
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Generate MD5 checksum for APN verification
function generateChecksum(api_id, trans_id, amount, status, nonce) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  return crypto.createHash('md5').update(message).digest('hex');
}

// Generate random nonce
function generateNonce() {
  const now = new Date();
  const timeComponent = String(now.getHours()).padStart(2, '0') +
                        String(now.getMinutes()).padStart(2, '0') +
                        String(now.getSeconds()).padStart(2, '0');
  const randomComponent = String(Math.floor(1000 + Math.random() * 9000));
  return `${timeComponent}${randomComponent}`;
}

// Generate timestamp in required format
function generateTimestamp() {
  return new Date().toISOString().replace('T', ' ').substring(0, 19);
}

// Load orders from file
function loadOrders() {
  try {
    const data = fs.readFileSync(CONFIG.ordersFilePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error loading orders: ${error.message}`);
    process.exit(1);
  }
}

// Find test order
function findTestOrder(orders) {
  const testOrder = orders.find(order => order.id === CONFIG.testOrderId);
  
  if (!testOrder) {
    console.error(`Test order with ID ${CONFIG.testOrderId} not found in orders.json`);
    process.exit(1);
  }
  
  return testOrder;
}

// Save orders to file
function saveOrders(orders) {
  try {
    fs.writeFileSync(CONFIG.ordersFilePath, JSON.stringify(orders, null, 2), 'utf8');
    console.log(`✅ Orders file updated successfully`);
  } catch (error) {
    console.error(`Error saving orders: ${error.message}`);
  }
}

// Update order in orders.json
function updateOrderStatus(paymentStatus) {
  const orders = loadOrders();
  const orderIndex = orders.findIndex(order => order.id === CONFIG.testOrderId);
  
  if (orderIndex === -1) {
    console.error(`Test order with ID ${CONFIG.testOrderId} not found in orders.json`);
    return false;
  }
  
  // Map 7-11 status codes to our internal status
  let status, paymentStatusText;
  
  switch (paymentStatus) {
    case 'A': // Waiting for payment
      status = 'pending';
      paymentStatusText = 'waiting_payment';
      break;
    case 'B': // Paid by payer
      status = 'processing';
      paymentStatusText = 'paid';
      break;
    case 'D': // Overdue payment
      status = 'cancelled';
      paymentStatusText = 'expired';
      orders[orderIndex].isExpired = true;
      orders[orderIndex].expiryReason = 'payment_expired';
      break;
    default:
      status = 'pending';
      paymentStatusText = 'unknown';
  }
  
  // Update order
  orders[orderIndex].status = status;
  orders[orderIndex].paymentStatus = paymentStatusText;
  orders[orderIndex].updatedAt = new Date().toISOString();
  
  // Save updated orders
  saveOrders(orders);
  return true;
}

// Send APN callback
async function sendAPNCallback(status) {
  if (!TEST_ORDER) {
    console.error('Test order not loaded');
    return false;
  }
  
  console.log(`\n📤 Sending ${getStatusName(status)} notification to APN endpoint...`);
  
  const api_id = CONFIG.apiId;
  const trans_id = TEST_ORDER.trans_id;
  const order_no = TEST_ORDER.orderNumber;
  const amount = TEST_ORDER.totalAmount;
  const nonce = generateNonce();
  
  const payload = {
    api_id,
    trans_id,
    order_no,
    amount,
    status,
    payment_code: 2, // CVS payment (ibon)
    payment_detail: {
      pay_route: 'IbonPay',
      storeId: '123456',
      ibon_code: TEST_ORDER.paymentInfo.ibonPaymentCode
    },
    create_time: generateTimestamp(),
    modify_time: generateTimestamp(),
    expire_time: generateTimestamp(),
    nonce
  };
  
  // Add pay_date and pay_amount for completed payments
  if (status === 'B') {
    payload.pay_date = generateTimestamp();
    payload.pay_amount = amount;
  }
  
  // Generate and add checksum
  payload.checksum = generateChecksum(api_id, trans_id, amount, status, nonce);
  
  if (CONFIG.debug) {
    console.log('Request payload:');
    console.log(JSON.stringify(payload, null, 2));
  }
  
  try {
    console.log(`Sending request to: ${CONFIG.apnUrl}`);
    const response = await axios.post(CONFIG.apnUrl, payload);
    
    console.log(`Status Code: ${response.status}`);
    console.log(`Response: ${typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2)}`);
    
    // If successful, update the order in orders.json
    if (response.status === 200) {
      return updateOrderStatus(status);
    }
    
    return false;
  } catch (error) {
    console.error(`Error: ${error.message}`);
    
    if (error.response) {
      console.log(`Status Code: ${error.response.status}`);
      console.log(`Response: ${typeof error.response.data === 'string' ? error.response.data : JSON.stringify(error.response.data, null, 2)}`);
    }
    
    return false;
  }
}

// Get status name
function getStatusName(status) {
  switch (status) {
    case 'A': return 'Payment Pending';
    case 'B': return 'Payment Completed';
    case 'D': return 'Payment Expired';
    default: return 'Unknown Status';
  }
}

// Show current order status
function showCurrentStatus() {
  const orders = loadOrders();
  const order = findTestOrder(orders);
  
  console.log('\n📋 Current Order Status:');
  console.log('------------------------');
  console.log(`Order ID: ${order.id}`);
  console.log(`Order Number: ${order.orderNumber}`);
  console.log(`Status: ${order.status}`);
  console.log(`Payment Status: ${order.paymentStatus}`);
  console.log(`Last Updated: ${order.updatedAt}`);
  console.log(`Amount: ${order.totalAmount} ${order.currency}`);
  console.log(`ibon Code: ${order.paymentInfo.ibonPaymentCode}`);
  console.log(`Expired: ${order.isExpired ? 'Yes' : 'No'}`);
  if (order.expiryReason) {
    console.log(`Expiry Reason: ${order.expiryReason}`);
  }
  console.log('------------------------');
}

// Display menu and handle user choice
function showMenu() {
  console.log('\n🔄 7-Eleven APN Test Options:');
  console.log('1) Send "Payment Pending" notification (Status A)');
  console.log('2) Send "Payment Completed" notification (Status B)');
  console.log('3) Send "Payment Expired" notification (Status D)');
  console.log('4) Show current order status');
  console.log('0) Exit');
  
  rl.question('\nEnter your choice: ', async (choice) => {
    switch (choice) {
      case '1':
        await sendAPNCallback('A');
        showMenu();
        break;
      case '2':
        await sendAPNCallback('B');
        showMenu();
        break;
      case '3':
        await sendAPNCallback('D');
        showMenu();
        break;
      case '4':
        showCurrentStatus();
        showMenu();
        break;
      case '0':
        console.log('\nExiting APN Test. Goodbye! 👋');
        rl.close();
        break;
      default:
        console.log('\n❌ Invalid choice. Please try again.');
        showMenu();
    }
  });
}

// Main function
async function main() {
  console.log('🚀 Starting 7-Eleven APN Interactive Test...');
  
  // Check if orders.json exists
  if (!fs.existsSync(CONFIG.ordersFilePath)) {
    console.error(`Orders file not found at ${CONFIG.ordersFilePath}`);
    process.exit(1);
  }
  
  // Load orders and find test order
  const orders = loadOrders();
  TEST_ORDER = findTestOrder(orders);
  
  console.log(`\n✅ Found test order: ${TEST_ORDER.id} (${TEST_ORDER.orderNumber})`);
  showCurrentStatus();
  
  // Check if APN server is running
  try {
    console.log(`Checking if application is running at: http://localhost:3000`);
    await axios.get('http://localhost:3000');
    console.log('✅ Next.js application is running');
    console.log(`⚠️ Note: Will send APN callbacks to ${CONFIG.apnUrl}`);
    console.log(`⚠️ The API endpoint must exist and be properly configured to handle APN callbacks`);
  } catch (error) {
    console.log('⚠️ Warning: Could not connect to your Next.js application. Make sure it is running on port 3000.');
  }
  
  // Show menu
  showMenu();
}

// Start the program
main().catch(error => {
  console.error('Error running test:', error);
  rl.close();
}); 