/**
 * FamilyMart Payment Notification Test
 * 
 * This script simulates payment notifications from the FamilyMart payment system.
 * It allows testing the FamilyMart callback endpoint with different payment statuses.
 */

const axios = require('axios');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  // FamilyMart callback endpoint URL
  callbackUrl: 'http://localhost:3000/api/payment/familymart-callback',
  
  // Path to orders.json file
  ordersFilePath: path.join(__dirname, 'data', 'orders.json'),
  
  // FamilyMart merchant credentials
  ecId: 'FAMISHOPTEST123',
  
  // Test order ID to update
  testOrderId: '711TEST-001'
};

// Create readline interface for user interaction
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Load orders from file
function loadOrders() {
  try {
    const data = fs.readFileSync(CONFIG.ordersFilePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error loading orders: ${error.message}`);
    process.exit(1);
  }
}

// Find test order
function findTestOrder(orders) {
  const testOrder = orders.find(order => order.id === CONFIG.testOrderId);
  
  if (!testOrder) {
    console.error(`Test order with ID ${CONFIG.testOrderId} not found in orders.json`);
    process.exit(1);
  }
  
  return testOrder;
}

// Generate PIN CODE
function generatePinCode() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 10; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

// Show current order status
function showCurrentStatus() {
  const orders = loadOrders();
  const order = findTestOrder(orders);
  
  console.log('\n📋 Current Order Status:');
  console.log('------------------------');
  console.log(`Order ID: ${order.id}`);
  console.log(`Order Number: ${order.orderNumber}`);
  console.log(`Status: ${order.status}`);
  console.log(`Payment Status: ${order.paymentStatus}`);
  console.log(`Last Updated: ${order.updatedAt}`);
  console.log(`Amount: ${order.totalAmount} ${order.currency}`);
  if (order.paymentInfo && order.paymentInfo.pinCode) {
    console.log(`PIN Code: ${order.paymentInfo.pinCode}`);
  }
  console.log(`Expired: ${order.isExpired ? 'Yes' : 'No'}`);
  if (order.expiryReason) {
    console.log(`Expiry Reason: ${order.expiryReason}`);
  }
  console.log('------------------------');
}

// Format date in YYYY/MM/DD HH:MM:SS format
function formatDate(date) {
  const d = new Date(date);
  return d.toLocaleString('zh-TW', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '/');
}

// Send FamilyMart payment notification
async function sendNotification(statusCode) {
  const orders = loadOrders();
  const order = findTestOrder(orders);
  
  // Get or generate PIN_CODE
  const pinCode = (order.paymentInfo && order.paymentInfo.pinCode) 
    ? order.paymentInfo.pinCode 
    : generatePinCode();
  
  // Prepare notification payload
  const payload = {
    EC_ID: CONFIG.ecId,
    PIN_CODE: pinCode,
    ORDER_NO: order.orderNumber,
    ORDER_DATE: formatDate(order.createdAt),
    AMOUNT: order.totalAmount,
    STATUS_CODE: statusCode,
    PAYMENT_NO: `FM${Math.floor(100000000 + Math.random() * 900000000)}`,
    PAYMENT_DATE: formatDate(new Date()),
    STORE_ID: 'FM1234',
    BARCODE1: '2345678901234',
    BARCODE2: '2345678901235',
    BARCODE3: '2345678901236'
  };
  
  console.log('\n📤 Sending FamilyMart payment notification...');
  console.log('Payload:', JSON.stringify(payload, null, 2));
  
  try {
    const response = await axios.post(CONFIG.callbackUrl, payload);
    console.log(`Status Code: ${response.status}`);
    console.log(`Response: ${typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2)}`);
    return true;
  } catch (error) {
    console.error(`Error sending notification: ${error.message}`);
    
    if (error.response) {
      console.log(`Status Code: ${error.response.status}`);
      console.log(`Response: ${typeof error.response.data === 'string' ? error.response.data : JSON.stringify(error.response.data, null, 2)}`);
    }
    
    return false;
  }
}

// Display menu and handle user choice
function showMenu() {
  console.log('\n🔄 FamilyMart Payment Test Options:');
  console.log('1) Send "Payment Pending" notification (Status Code 1)');
  console.log('2) Send "Payment Completed" notification (Status Code 0)');
  console.log('3) Send "Payment Expired" notification (Status Code 2)');
  console.log('4) Send "Payment Cancelled" notification (Status Code 3)');
  console.log('5) Show current order status');
  console.log('0) Exit');
  
  rl.question('\nEnter your choice: ', async (choice) => {
    switch (choice) {
      case '1':
        await sendNotification('1');
        showMenu();
        break;
      case '2':
        await sendNotification('0');
        showMenu();
        break;
      case '3':
        await sendNotification('2');
        showMenu();
        break;
      case '4':
        await sendNotification('3');
        showMenu();
        break;
      case '5':
        showCurrentStatus();
        showMenu();
        break;
      case '0':
        console.log('\nExiting FamilyMart Test. Goodbye! 👋');
        rl.close();
        break;
      default:
        console.log('\n❌ Invalid choice. Please try again.');
        showMenu();
    }
  });
}

// Main function
async function main() {
  console.log('🚀 Starting FamilyMart Payment Notification Test...');
  
  // Load orders and find test order
  const orders = loadOrders();
  const testOrder = findTestOrder(orders);
  
  console.log(`\n✅ Found test order: ${testOrder.id} (${testOrder.orderNumber})`);
  showCurrentStatus();
  
  // Check if the FamilyMart callback endpoint is available
  try {
    await axios.get('http://localhost:3000');
    console.log('✅ Server is running');
  } catch (error) {
    console.log('⚠️ Warning: Could not connect to the server. Make sure it\'s running on port 3000.');
  }
  
  showMenu();
}

// Start the program
main().catch(error => {
  console.error('Error running test:', error);
  rl.close();
}); 