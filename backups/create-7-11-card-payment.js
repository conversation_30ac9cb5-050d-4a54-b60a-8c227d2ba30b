import axios from 'axios';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../components/taiwan/payment/methods/711/config.json';

// Helper for consistent logging
const log711CardAPI = (message, data = null) => {
  console.log(`[7-11 CARD API] ${message}`, data ? JSON.stringify(data) : '');
};

// Helper function to get authentication token
async function getAuthToken(merchantID, apiUrl) {
  // Get IBON password from config - this is the correct password for token authentication
  const { tw: { tw711: { ibon } } } = config;
  const IBON_PASSWORD = ibon.password; // W7529992P$
  
  log711CardAPI('Requesting authentication token', { 
    merchantID,
    tokenEndpoint: `${apiUrl}/Token`,
    usingIbonPassword: true
  });
  
  try {
    // Use the IBON_PASSWORD for authentication as it's the correct one
    const tokenResponse = await axios.post(
      `${apiUrl}/Token`,
      `grant_type=password&username=${merchantID}&password=${IBON_PASSWORD}`,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    if (!tokenResponse.data || !tokenResponse.data.access_token) {
      log711CardAPI('Failed to obtain authentication token', tokenResponse.data);
      throw new Error('Failed to obtain authentication token');
    }
    
    // Log success (but mask most of the token)
    const token = tokenResponse.data.access_token;
    log711CardAPI('Authentication token received', {
      tokenLength: token.length,
      tokenStart: token.substring(0, 10) + '...'
    });
    
    return token;
  } catch (error) {
    // If IBON_PASSWORD fails, try with CARD_PASSWORD as fallback
    if (error.response && error.response.status === 400) {
      const { tw: { tw711: { card } } } = config;
      const CARD_PASSWORD = card.merchantPassword;
      
      log711CardAPI('IBON password failed, trying with card password', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      
      const fallbackResponse = await axios.post(
        `${apiUrl}/Token`,
        `grant_type=password&username=${merchantID}&password=${CARD_PASSWORD}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );
      
      if (fallbackResponse.data && fallbackResponse.data.access_token) {
        const fallbackToken = fallbackResponse.data.access_token;
        log711CardAPI('Authentication token received with fallback password', {
          tokenLength: fallbackToken.length,
          tokenStart: fallbackToken.substring(0, 10) + '...'
        });
        
        return fallbackToken;
      }
    }
    
    // Re-throw if both attempts fail
    log711CardAPI('Authentication failed', {
      error: error.message,
      response: error.response?.data
    });
    throw error;
  }
}

const handler = async (req, res) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // Check if debug mode is enabled in the request
  const isDebugMode = req.body.debug === true || process.env.NODE_ENV !== 'production';
  
  // Collect all debug info to return to client in debug mode
  const debugInfo = {
    requestTime: new Date().toISOString(),
    requestBody: { ...req.body, CustomerEmail: req.body.CustomerEmail ? '***@***' : undefined },
    config: {},
    apiDetails: {},
    apiResponse: null,
    error: null
  };

  log711CardAPI('Received payment request', { 
    body: { ...req.body, CustomerEmail: req.body.CustomerEmail ? '***@***' : undefined },
    debug: isDebugMode
  });

  try {
    const { tw: { tw711: { card } } } = config;

    // Card payment specific configuration
    const CARD_MERCHANT_ID = card.merchantID;
    const CARD_PASSWORD = isDebugMode ? '[MASKED]' : card.merchantPassword;
    const CARD_LINK_ID = card.linkID;
    const CARD_HASH_BASE = isDebugMode ? '[MASKED]' : card.hashBase;
    const CARD_TEST_URL = card.testUrl;
    const CARD_LIVE_URL = card.liveUrl || card.testUrl; // Fallback to test URL if live URL is not set
    const CARD_RETURN_URL = card.returnURL;
    const CARD_NOTIFY_URL = card.notifyURL;
    const HASH_ALGORITHM = card.security.hashAlgorithm;
    const HASH_FORMAT = card.security.hashFormat;

    // Store config details for debug output
    if (isDebugMode) {
      debugInfo.config = {
        merchantID: CARD_MERCHANT_ID,
        linkID: CARD_LINK_ID,
        testUrl: CARD_TEST_URL,
        liveUrl: CARD_LIVE_URL, 
        returnURL: CARD_RETURN_URL,
        notifyURL: CARD_NOTIFY_URL,
        hashAlgorithm: HASH_ALGORITHM,
        // Don't include sensitive data like passwords and hash bases
      };
    }

    // Set the API URL based on environment - use test URL for development
    const API_URL = process.env.NODE_ENV === 'production' ? CARD_LIVE_URL : CARD_TEST_URL;
    
    log711CardAPI('Using API endpoint', { 
      url: API_URL,
      environment: process.env.NODE_ENV
    });

    debugInfo.apiDetails.endpoint = API_URL;
    debugInfo.apiDetails.environment = process.env.NODE_ENV;

    try {
      // Get authentication token first - only passing merchantID and API URL
      let authToken;
      try {
        authToken = await getAuthToken(CARD_MERCHANT_ID, API_URL);
        debugInfo.apiDetails.authentication = {
          tokenReceived: true,
          tokenLength: authToken.length
        };
      } catch (authError) {
        log711CardAPI('Authentication failed', { 
          error: authError.message,
          response: authError.response?.data
        });
        debugInfo.error = {
          type: 'AUTHENTICATION_ERROR',
          message: authError.message,
          response: authError.response?.data
        };
        return res.status(401).json({
          success: false,
          error: 'Failed to authenticate with payment provider',
          debug: isDebugMode ? debugInfo : undefined
        });
      }

      const {
        MerchantTradeNo,
        TotalAmount,
        TradeDesc,
        ItemName,
        CustomerName,
        CustomerPhone,
        CustomerEmail,
      } = req.body;

      // Validate required fields
      if (!MerchantTradeNo || !TotalAmount || !CustomerName || !CustomerEmail) {
        const missingFields = [];
        if (!MerchantTradeNo) missingFields.push('MerchantTradeNo');
        if (!TotalAmount) missingFields.push('TotalAmount');
        if (!CustomerName) missingFields.push('CustomerName');
        if (!CustomerEmail) missingFields.push('CustomerEmail');
        
        log711CardAPI('Validation error - missing fields', { missingFields });
        debugInfo.error = { type: 'VALIDATION_ERROR', missingFields };
        
        return res.status(400).json({
          success: false,
          error: `Missing required fields: ${missingFields.join(', ')}`,
          debug: isDebugMode ? debugInfo : undefined
        });
      }

      // Parse TotalAmount to ensure it's a number
      const amountInt = parseInt(TotalAmount, 10);
      
      // Log the amount conversion
      log711CardAPI('Amount conversion', {
        original: TotalAmount,
        parsed: amountInt,
        originalType: typeof TotalAmount
      });

      // Calculate expiry date (7 days from now)
      const now = new Date();
      const expiryDate = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days expiry
      const expireDateString = expiryDate.toISOString().split('T')[0]; // YYYY-MM-DD format
      
      log711CardAPI('Expiry date calculated', {
        now: now.toISOString(),
        expiryDate: expiryDate.toISOString(),
        expireDateString
      });

      // Generate timestamp in the required format
      const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14);
      
      // Create the data string for hash generation
      const dataString = `${MerchantTradeNo}|${amountInt}|${timestamp}|${CustomerName}|${CustomerEmail}`;
      
      if (isDebugMode) {
        debugInfo.apiDetails.hashData = {
          dataString,
          algorithm: HASH_ALGORITHM,
          format: HASH_FORMAT.replace('${hash_base}', '[MASKED]')
        };
      }
      
      // Generate hash using merchant-specific parameters
      const hashString = HASH_FORMAT
        .replace('${cust_id}', CARD_MERCHANT_ID)
        .replace('${data}', dataString)
        .replace('${hash_base}', CARD_HASH_BASE);
      
      const hash = crypto.createHash(HASH_ALGORITHM).update(hashString).digest('hex');
      
      if (isDebugMode) {
        debugInfo.apiDetails.hashResult = hash;
      }

      // Validate and format the phone number - must be digits only
      let formattedPhone = CustomerPhone ? CustomerPhone.replace(/\D/g, '') : '';
      if (formattedPhone && (formattedPhone.length < 8 || formattedPhone.length > 15)) {
        log711CardAPI('Phone number validation warning', {
          original: CustomerPhone,
          formatted: formattedPhone,
          length: formattedPhone.length
        });
      }
        
      // Prepare the API request payload
      const payload = {
        cmd: "CvsOrderAppend2",  // Required command name for the API
        cust_id: CARD_MERCHANT_ID,
        cust_order_no: MerchantTradeNo,
        order_amount: amountInt,
        expire_date: expireDateString,  // Required payment expiry date
        timestamp: timestamp,
        link_id: CARD_LINK_ID,
        password: CARD_PASSWORD, // This is masked in logs but sent in actual request
        payer_name: CustomerName,
        payer_email: CustomerEmail,
        payer_mobile: formattedPhone,
        customer_address: '',
        order_detail: ItemName || TradeDesc || 'Order items',
        test_mode: process.env.NODE_ENV !== 'production' ? '1' : '0',
        return_url: CARD_RETURN_URL,
        notify_url: CARD_NOTIFY_URL,
        payment_type: '0',
        hash: hash
      };
      
      // Store payload for debug output (masking sensitive data)
      if (isDebugMode) {
        debugInfo.apiDetails.payload = {
          ...payload,
          password: '[MASKED]',
          customer_email: '***@***'
        };
      }

      log711CardAPI(`Processing payment for order ${MerchantTradeNo}`, {
        timestamp,
        testMode: payload.test_mode,
        returnUrl: CARD_RETURN_URL,
        notifyUrl: CARD_NOTIFY_URL
      });
      
      try {
        // Make the API request to the 7-11 card payment gateway
        // The correct API endpoint path from the documentation is /api/Collect
        const apiPath = '/api/Collect';
        const fullEndpoint = `${API_URL}${apiPath}`;
        
        log711CardAPI('Making API request', { 
          endpoint: fullEndpoint,
          hasAuthToken: !!authToken
        });
        
        debugInfo.apiDetails.fullEndpoint = fullEndpoint;
        
        const paymentResponse = await axios.post(
          fullEndpoint, 
          payload, 
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authToken}`
            },
            timeout: 30000 // 30 second timeout
          }
        );
        
        // Update debug info with response
        debugInfo.apiResponse = {
          status: paymentResponse.status,
          statusText: paymentResponse.statusText,
          data: paymentResponse.data,
          responseTime: new Date().toISOString()
        };
        
        log711CardAPI('Raw response received', {
          status: paymentResponse.status,
          statusText: paymentResponse.statusText,
          data: paymentResponse.data
        });
        
        if (paymentResponse.data && paymentResponse.data.resultCode === '0000') {
          // Successful response
          log711CardAPI('Payment URL generated successfully', {
            paymentUrl: paymentResponse.data.paymentUrl?.substring(0, 50) + '...',
            shortUrl: paymentResponse.data.shortUrl
          });
          
          // Add success status to debug info
          debugInfo.status = 'SUCCESS';
          
          return res.status(200).json({
            success: true,
            data: {
              paymentUrl: paymentResponse.data.paymentUrl,
              expireDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
              billAmount: TotalAmount,
              fee: card.fee.amount,
              shortUrl: paymentResponse.data.shortUrl || ''
            },
            debug: isDebugMode ? debugInfo : undefined
          });
        } else {
          // API returned an error
          const errorMsg = paymentResponse.data?.message || 'Payment provider returned an error';
          const resultCode = paymentResponse.data?.resultCode || 'unknown';
          
          // Add error info to debug info
          debugInfo.error = {
            type: 'API_RESULT_ERROR',
            resultCode,
            message: errorMsg,
          };
          debugInfo.status = 'ERROR';
          
          log711CardAPI('Error processing payment', { 
            resultCode,
            message: errorMsg,
            data: paymentResponse.data
          });
          
          let message = errorMsg;
          
          // Extract error message from response if available
          if (paymentResponse.data && paymentResponse.data.msg) {
            message = paymentResponse.data.msg;

            // Specifically handle the "T-ibon data exchange exception"
            if (paymentResponse.data.msg.includes('T-ibon資料交換異常') || 
                paymentResponse.data.msg.includes('T-ibon data exchange exception')) {
              message = "Payment provider system (ibon) is temporarily unavailable. Please try again later.";
              log711CardAPI('T-ibon system error detected', { 
                originalMessage: paymentResponse.data.msg,
                action: 'This is usually a temporary issue with the 7-11 payment system. Retry again in a few minutes.'
              });
            }
          }
          
          return res.status(400).json({
            success: false,
            error: message,
            resultCode,
            debug: isDebugMode ? debugInfo : undefined
          });
        }
      } catch (apiError) {
        // Handle API request errors
        debugInfo.error = {
          type: 'API_REQUEST_ERROR',
          message: apiError.message,
          code: apiError.code || 'UNKNOWN',
          response: apiError.response ? {
            status: apiError.response.status,
            data: apiError.response.data
          } : null
        };
        debugInfo.status = 'ERROR';
        
        log711CardAPI('API request error', { 
          message: apiError.message,
          code: apiError.code,
          responseStatus: apiError.response?.status,
          responseData: apiError.response?.data
        });
        
        let message = 'Payment provider is temporarily unavailable. Please try again later.';
        
        // Extract error message from response if available
        if (apiError.response && apiError.response.data) {
          message = apiError.response.data.message || message;

          // Specifically handle the "T-ibon data exchange exception"
          if (apiError.response.data.msg && 
              (apiError.response.data.msg.includes('T-ibon資料交換異常') || 
               apiError.response.data.msg.includes('T-ibon data exchange exception'))) {
            message = "Payment provider system (ibon) is temporarily unavailable. Please try again later.";
            log711CardAPI('T-ibon system error detected', { 
              originalMessage: apiError.response.data.msg,
              action: 'This is usually a temporary issue with the 7-11 payment system. Retry again in a few minutes.'
            });
          }
        }
        
        return res.status(500).json({
          success: false,
          error: message,
          errorCode: apiError.code || 'NETWORK_ERROR',
          debug: isDebugMode ? debugInfo : undefined
        });
      }
    } catch (error) {
      // Catch any other errors in our code
      debugInfo.error = {
        type: 'PROCESSING_ERROR',
        message: error.message,
        stack: error.stack
      };
      debugInfo.status = 'ERROR';
      
      log711CardAPI('Unexpected error processing request', { 
        message: error.message,
        stack: error.stack
      });
      
      return res.status(500).json({
        success: false,
        error: 'An unexpected error occurred. Please try again later.',
        errorMessage: error.message,
        debug: isDebugMode ? debugInfo : undefined
      });
    }
  } catch (configError) {
    // Handle configuration errors
    debugInfo.error = {
      type: 'CONFIG_ERROR',
      message: configError.message
    };
    debugInfo.status = 'ERROR';
    
    log711CardAPI('Configuration error', { 
      message: configError.message
    });
    
    return res.status(500).json({
      success: false,
      error: 'Payment system configuration error. Please contact support.',
      debug: isDebugMode ? debugInfo : undefined
    });
  }
};

export default handler; 