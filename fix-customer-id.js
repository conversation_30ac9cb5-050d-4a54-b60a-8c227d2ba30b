/**
 * <PERSON><PERSON><PERSON> to manually set customer ID and trigger cart merging
 * 
 * Usage:
 * 1. Open your browser developer console (F12)
 * 2. Copy and paste this entire script
 * 3. Call the fixCustomerId function with your customer ID
 * 
 * Example:
 *   fixCustomerId('customer_12345');
 */

function fixCustomerId(customerId, customerName = '', customerEmail = '') {
  if (!customerId) {
    console.error('Please provide a customer ID');
    return;
  }

  console.log('Current authentication state:');
  console.log('- Customer ID:', localStorage.getItem('SHOPME_CUSTOMER_ID'));
  console.log('- User ID:', localStorage.getItem('SHOPME_USER_ID'));
  
  // Get current cart data
  const cartData = localStorage.getItem('SHOPME_');
  let cart = cartData ? JSON.parse(cartData) : null;
  console.log('- Current cart:', cart ? `${cart.cart?.length || 0} items` : 'None');
  
  // Store previous ID for cart merging
  const previousId = localStorage.getItem('SHOPME_USER_ID');
  
  // Set the customer ID
  localStorage.setItem('SHOPME_CUSTOMER_ID', customerId);
  console.log('✅ Set customer ID to:', customerId);
  
  // Set the user ID to be the same as customer ID
  localStorage.setItem('SHOPME_USER_ID', customerId);
  console.log('✅ Set user ID to match customer ID:', customerId);
  
  // Set customer info if provided
  if (customerName || customerEmail) {
    const customerInfo = { 
      name: customerName, 
      email: customerEmail 
    };
    localStorage.setItem('SHOPME_CUSTOMER_INFO', JSON.stringify(customerInfo));
    console.log('✅ Set customer info:', customerInfo);
  }
  
  // Trigger the merging event
  if (previousId && previousId !== customerId) {
    console.log('🔄 Triggering cart merge from', previousId, 'to', customerId);
    try {
      window.dispatchEvent(new CustomEvent('customer-identified', { 
        detail: { customerId, previousId }
      }));
      console.log('✅ Cart merge event dispatched');
    } catch (e) {
      console.error('❌ Error dispatching merge event:', e);
    }
  } else {
    console.log('ℹ️ No need to merge carts (previous ID is same as new ID)');
  }
  
  console.log('✅ Done! Please reload the page to see the changes.');
  console.log('ℹ️ After reloading, your cart should be linked to your customer ID.');
}

console.log('✅ Fix Customer ID script loaded!');
console.log('Please call fixCustomerId("your_customer_id") to set your customer ID.');
console.log('Example: fixCustomerId("customer_12345", "Your Name", "<EMAIL>")'); 