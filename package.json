{"name": "mag.shop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "serve": "next start serve", "custom-server": "node server.js", "promote": "node scripts/promote.js", "mobile:sync": "npx cap sync", "mobile:open:ios": "npx cap open ios", "mobile:open:android": "npx cap open android", "inventory": "node scripts/manage-inventory.js", "db:manage": "node scripts/db-management.js", "db:status": "./db-manage.sh status", "db:analyze": "./db-manage.sh analyze", "db:backup": "./db-manage.sh backup full", "db:cleanup": "./db-manage.sh cleanup all", "json:backup": "node scripts/backup.gitignore/backup.gitignore-data.cjs", "json:restore": "node scripts/backup.gitignore/backup.gitignore-data.cjs --restore --confirm"}, "dependencies": {"@capacitor/android": "^6.2.0", "@capacitor/cli": "^6.2.0", "@capacitor/core": "^6.2.0", "@capacitor/geolocation": "^6.1.0", "@capacitor/ios": "^6.2.0", "@capacitor/share": "^6.0.3", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@stripe/react-stripe-js": "^1.1.2", "@stripe/stripe-js": "^1.11.0", "@types/formidable": "^3.4.5", "@types/js-cookie": "^3.0.6", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "autoprefixer": "10.4.14", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "canvas": "^3.0.1", "cookie": "^1.0.2", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "emailjs-com": "^3.2.0", "formidable": "^3.5.4", "formik": "^2.4.6", "framer-motion": "^12.10.5", "google-auth-library": "^10.3.0", "google-spreadsheet": "^5.0.2", "googleapis": "^160.0.0", "html-react-parser": "^5.1.8", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.6", "jsonwebtoken": "^9.0.2", "kbar": "0.1.0-beta.45", "leaflet": "^1.9.4", "lucide-react": "^0.473.0", "mongodb": "^6.15.0", "multer": "^1.4.5-lts.2", "next": "^14.1.3", "next-connect": "^1.0.0", "next-i18next": "^15.4.2", "nodemailer": "^6.9.12", "postcss": "8.4.31", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.1", "react-icons": "^4.12.0", "react-markdown": "^9.0.1", "react-router-dom": "^7.5.0", "react-share": "^5.1.0", "react-toastify": "^6.2.0", "react-transition-group": "^4.4.5", "tailwindcss": "3.3.0", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@types/cookie": "^1.0.0", "typescript": "^5.7.2"}}