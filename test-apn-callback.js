/**
 * Test script for 7-11 APN (Active Notification Service) callback
 * 
 * This script simulates payment notifications from the 7-Eleven payment system
 * to test the APN callback endpoint.
 */

const crypto = require('crypto');
const axios = require('axios');

// Configuration - adjust these values as needed
const CONFIG = {
  // APN callback URL pointing to the real server
  apnUrl: 'https://sim.dailoanshop.net/api/payment/apn-callback',
  
  // API credentials
  apiId: '827315300001',
  
  // Set to true to log detailed request/response information
  debug: true
};

// Generate MD5 checksum for APN verification
function generateChecksum(api_id, trans_id, amount, status, nonce) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  return crypto.createHash('md5').update(message).digest('hex');
}

// Test cases for different payment scenarios
const TEST_CASES = [
  {
    name: 'Test Order Payment Success',
    request: () => {
      const api_id = CONFIG.apiId;
      const trans_id = '711apntest20250416001';
      const order_no = '711-TEST-12345';
      const amount = '1225';
      const status = 'B'; // Paid by payer
      const nonce = Date.now().toString();
      
      const payload = {
        api_id,
        trans_id,
        order_no,
        amount,
        status,
        payment_code: 2, // CVS payment
        payment_detail: {
          pay_route: 'IbonPay',
          storeId: '123456',
          ibon_code: '405300000960',
          ibon_barcode1: '0810025G5',
          ibon_barcode2: '091002QP13137602',
          ibon_barcode3: '171338770000671'
        },
        nonce,
        pay_date: new Date().toISOString()
      };
      
      // Generate and add checksum
      payload.checksum = generateChecksum(api_id, trans_id, amount, status, nonce);
      
      return payload;
    }
  }
];

// Run a single test
async function runTest(testCase) {
  console.log(`\n========== Running Test: ${testCase.name} ==========`);
  
  const requestData = testCase.request();
  
  if (CONFIG.debug) {
    console.log('Request payload:');
    console.log(JSON.stringify(requestData, null, 2));
  }
  
  try {
    const response = await axios.post(CONFIG.apnUrl, requestData);
    
    console.log(`Status Code: ${response.status}`);
    console.log(`Response: ${typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2)}`);
    
    // Check if response is as expected
    if (response.status === 200) {
      console.log('✅ Test passed!');
    } else {
      console.log('⚠️ Test completed but returned unexpected status code');
    }
    
    return { success: true, response: response.data };
  } catch (error) {
    console.log(`Status Code: ${error.response ? error.response.status : 'No response'}`);
    console.log(`Error: ${error.response ? error.response.data : error.message}`);
    console.log('❌ Test failed!');
    
    return { 
      success: false, 
      error: error.response ? error.response.data : error.message
    };
  }
}

// Run all test cases
async function runAllTests() {
  console.log('=================================================');
  console.log('  7-ELEVEN APN CALLBACK TEST                     ');
  console.log('=================================================');
  console.log(`APN URL: ${CONFIG.apnUrl}`);
  console.log(`API ID: ${CONFIG.apiId}`);
  console.log('=================================================');
  
  for (const testCase of TEST_CASES) {
    await runTest(testCase);
  }
  
  console.log('\n=================================================');
  console.log('  TEST SUITE COMPLETED                           ');
  console.log('=================================================');
}

// Run the tests
runAllTests(); 