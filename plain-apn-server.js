/**
 * Simple APN (Active Payment Notification) Server for 7-Eleven Payment Testing
 * 
 * This server provides endpoints for testing the 7-Eleven payment callback system.
 * It exposes REST APIs to manage payment status updates and retrieve order statuses.
 */

const http = require('http');
const url = require('url');

// In-memory storage for orders
const orders = [];

// Create HTTP server
const server = http.createServer((req, res) => {
  // Set CORS headers to allow requests from any origin
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle preflight OPTIONS requests
  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }
  
  // Parse URL
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  
  console.log(`${req.method} ${path}`);
  
  // Route handling
  if (path === '/orders' && req.method === 'GET') {
    // Return all orders
    handleGetOrders(req, res);
  } else if (path === '/update-order' && req.method === 'POST') {
    // Update order status
    handleUpdateOrder(req, res);
  } else if (path === '/apn-callback' && req.method === 'POST') {
    // Handle APN callback from 7-11
    handleApnCallback(req, res);
  } else {
    // Not found
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

// Handle GET /orders
function handleGetOrders(req, res) {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(orders));
}

// Handle POST /update-order
function handleUpdateOrder(req, res) {
  let body = '';
  
  req.on('data', chunk => {
    body += chunk.toString();
  });
  
  req.on('end', () => {
    try {
      const data = JSON.parse(body);
      const { orderNumber, status, paymentType, storeName } = data;
      
      if (!orderNumber || !status) {
        res.writeHead(400, { 'Content-Type': 'text/plain' });
        res.end('Missing required fields');
        return;
      }
      
      // Check if order exists, update it, or create new one
      const existingOrderIndex = orders.findIndex(o => o.orderNumber === orderNumber);
      
      if (existingOrderIndex !== -1) {
        // Update existing order
        orders[existingOrderIndex] = {
          ...orders[existingOrderIndex],
          status,
          paymentType,
          storeName,
          updatedAt: new Date().toISOString()
        };
      } else {
        // Create new order
        orders.push({
          orderNumber,
          status,
          paymentType,
          storeName,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
      
      console.log(`Order ${orderNumber} updated with status: ${status}`);
      
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end('OK');
    } catch (error) {
      console.error('Error parsing request body:', error);
      res.writeHead(400, { 'Content-Type': 'text/plain' });
      res.end('Invalid JSON');
    }
  });
}

// Handle POST /apn-callback (from 7-11)
function handleApnCallback(req, res) {
  let body = '';
  
  req.on('data', chunk => {
    body += chunk.toString();
  });
  
  req.on('end', () => {
    try {
      const data = JSON.parse(body);
      console.log('Received APN callback:', JSON.stringify(data, null, 2));
      
      // Extract the order number from the callback data
      const orderNumber = data.order_no;
      
      if (!orderNumber) {
        res.writeHead(400, { 'Content-Type': 'text/plain' });
        res.end('Missing order number');
        return;
      }
      
      // Map 7-11 status codes to our internal status codes
      let status;
      switch (data.status) {
        case 'A': // Waiting for payment
          status = 'A';
          break;
        case 'B': // Paid by payer
          status = 'B';
          break;
        case 'D': // Overdue payment
          status = 'D';
          break;
        default:
          status = 'Unknown';
      }
      
      // Determine payment type
      const paymentType = data.payment_code === 1 ? 'Credit Card' : '7-Eleven';
      
      // Update or create order
      const existingOrderIndex = orders.findIndex(o => o.orderNumber === orderNumber);
      
      if (existingOrderIndex !== -1) {
        // Update existing order
        orders[existingOrderIndex] = {
          ...orders[existingOrderIndex],
          status,
          paymentType,
          updatedAt: new Date().toISOString()
        };
      } else {
        // Create new order
        orders.push({
          orderNumber,
          status,
          paymentType,
          storeName: '7-ELEVEN (APN)',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
      
      console.log(`APN callback: Order ${orderNumber} updated with status: ${status}`);
      
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end('OK');
    } catch (error) {
      console.error('Error processing APN callback:', error);
      res.writeHead(400, { 'Content-Type': 'text/plain' });
      res.end('Invalid request data');
    }
  });
}

// Start the server
const PORT = 3000;
server.listen(PORT, () => {
  console.log(`APN Server running at http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  GET  /orders - List all orders');
  console.log('  POST /update-order - Update an order status');
  console.log('  POST /apn-callback - Receive APN callbacks from 7-11');
}); 