// Document types dictionary
export const DOCUMENT_TYPES = {
  idCard: { label: 'CMND/CCCD', description: 'Chứng minh nhân dân hoặc căn cước công dân' },
  passport: { label: '<PERSON><PERSON> chiếu', description: '<PERSON><PERSON> chiếu quốc tế' },
  driverLicense: { label: 'Bằng lái xe', description: 'Gi<PERSON>y phép lái xe' },
  birthCertificate: { label: 'Giấy khai sinh', description: 'Gi<PERSON>y khai sinh' },
  photo: { label: 'Ảnh chân dung', description: 'Ảnh chân dung chính chủ' },
  address: { label: 'Gi<PERSON>y tờ cư trú', description: '<PERSON><PERSON> hộ khẩu, tạm trú, KT3, xác nhận cư trú' },
  businessRegistration: { label: 'Đăng ký kinh doanh', description: 'Giấy phép đăng ký kinh doanh' },
  taxRegistration: { label: '<PERSON>ă<PERSON> ký thuế', description: '<PERSON><PERSON><PERSON><PERSON> đăng ký mã số thuế' },
  bankStatement: { label: 'Sao kê ngân hàng', description: 'Sao kê tài khoản ngân hàng' },
  insuranceCard: { label: 'Thẻ bảo hiểm', description: 'Thẻ bảo hiểm y tế/xã hội' },
  marriageCertificate: { label: 'Giấy kết hôn', description: 'Giấy chứng nhận kết hôn' },
  workPermit: { label: 'Giấy phép lao động', description: 'Giấy phép lao động cho người nước ngoài' },
  academicDegree: { label: 'Bằng cấp học thuật', description: 'Bằng đại học, cao đẳng, chứng chỉ' },
  utilityBill: { label: 'Hóa đơn tiện ích', description: 'Hóa đơn điện, nước, internet' },
  propertyOwnership: { label: 'Giấy tờ sở hữu', description: 'Giấy chứng nhận quyền sử dụng đất, sở hữu nhà' },
  other: { label: 'Khác', description: 'Các giấy tờ khác' }
};