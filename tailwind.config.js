/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './layouts/**/*.{js,ts,jsx,tsx,mdx}',
    './templates/**/*.{js,ts,jsx,tsx,mdx}',
    './App.tsx',
  ],
  theme: {
    extend: {
      spacing: {
        "72": "18rem",
        "80": "20rem",
        "88": "22rem",
        "96": "24rem",
        "104": "26rem",
        "112": "28rem",
        "120": "30rem",
        "124": "31rem",
        "128": "32rem",
        "132": "33rem",
        "136": "34rem",
        "140": "35rem",
        "144": "36rem",
        "fw": "1440px",
      },
      fontSize: {
        'xxs': '.6rem',
        'smaller': '.95rem'
      },
      fontFamily: {
        'light': ['ABN Light'],
        'semibold': ['ABN SemiBold'],
      },
      screens: {
        'mobile': '600px',
        'c_large': '1200px',
        'desktop': '1440px'
      },
      backgroundColor: {
        'primary': '#89bdf9',
        'light': '#f5f5f5',
        'light-200': '#f0f0f0',
        'light-300': '#e8e8e8'
      }
    },
  },
  plugins: [],
}