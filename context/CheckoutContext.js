/**
 * Checkout Context Provider
 * 
 * Centralized state management for the entire checkout process
 * Replaces scattered state management across checkout components
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { CurrencyManager } from '../services/CurrencyManager';
import { ProductTypeManager } from '../services/ProductTypeManager';
import { PaymentRegistry } from '../services/PaymentRegistry';
import { logCheckoutEvent, logCheckoutError } from '../utils/checkoutLogger';

// Action types
const CHECKOUT_ACTIONS = {
  // Cart actions
  SET_CART_ITEMS: 'SET_CART_ITEMS',
  UPDATE_ITEM_QUANTITY: 'UPDATE_ITEM_QUANTITY',
  REMOVE_ITEM: 'REMOVE_ITEM',
  CLEAR_CART: 'CLEAR_CART',
  
  // Currency actions
  SET_SELECTED_CURRENCY: 'SET_SELECTED_CURRENCY',
  SET_AVAILABLE_CURRENCIES: 'SET_AVAILABLE_CURRENCIES',
  
  // Payment actions
  SET_PAYMENT_METHOD: 'SET_PAYMENT_METHOD',
  SET_AVAILABLE_PAYMENT_METHODS: 'SET_AVAILABLE_PAYMENT_METHODS',
  
  // Customer actions
  SET_CUSTOMER_INFO: 'SET_CUSTOMER_INFO',
  SET_DELIVERY_ADDRESS: 'SET_DELIVERY_ADDRESS',
  
  // Order actions
  SET_ORDER_DATA: 'SET_ORDER_DATA',
  UPDATE_ORDER_STATUS: 'UPDATE_ORDER_STATUS',
  
  // UI state actions
  SET_CHECKOUT_STEP: 'SET_CHECKOUT_STEP',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  
  // Product type actions
  SET_PRODUCT_TYPE: 'SET_PRODUCT_TYPE',
  SET_CHECKOUT_FLOW: 'SET_CHECKOUT_FLOW'
};

// Initial state
const initialState = {
  // Cart state
  cartItems: [],
  filteredItems: [],
  
  // Currency state
  selectedCurrency: null,
  availableCurrencies: [],
  
  // Payment state
  selectedPaymentMethod: null,
  availablePaymentMethods: [],
  paymentData: null,
  
  // Customer state
  customerInfo: {
    name: '',
    email: '',
    phone: '',
    customerId: null
  },
  deliveryAddress: {
    address: '',
    city: '',
    postalCode: '',
    country: '',
    notes: ''
  },
  
  // Order state
  orderData: null,
  orderStatus: null,
  orderId: null,
  
  // UI state
  currentStep: 'cart-review',
  isLoading: false,
  error: null,
  
  // Product type state
  productType: ProductTypeManager.PRODUCT_TYPES.DEFAULT,
  checkoutFlow: null,
  
  // Totals
  subtotal: 0,
  tax: 0,
  shipping: 0,
  total: 0
};

// Reducer function
function checkoutReducer(state, action) {
  switch (action.type) {
    case CHECKOUT_ACTIONS.SET_CART_ITEMS: {
      const items = action.payload;
      const availableCurrencies = CurrencyManager.getAvailableCurrencies(items);
      const selectedCurrency = state.selectedCurrency || availableCurrencies[0] || null;
      const filteredItems = CurrencyManager.filterCartByCurrency(items, selectedCurrency);
      const productType = items.length > 0 ? ProductTypeManager.detect(items[0]).conclusion.type : ProductTypeManager.PRODUCT_TYPES.DEFAULT;
      const checkoutFlow = ProductTypeManager.getCheckoutFlow(productType);
      
      return {
        ...state,
        cartItems: items,
        availableCurrencies,
        selectedCurrency,
        filteredItems,
        productType,
        checkoutFlow,
        subtotal: CurrencyManager.calculateTotal(filteredItems, selectedCurrency)
      };
    }

    case CHECKOUT_ACTIONS.UPDATE_ITEM_QUANTITY: {
      const { itemId, quantity } = action.payload;
      const updatedItems = state.cartItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      );
      const filteredItems = CurrencyManager.filterCartByCurrency(updatedItems, state.selectedCurrency);
      
      return {
        ...state,
        cartItems: updatedItems,
        filteredItems,
        subtotal: CurrencyManager.calculateTotal(filteredItems, state.selectedCurrency)
      };
    }

    case CHECKOUT_ACTIONS.REMOVE_ITEM: {
      const itemId = action.payload;
      const updatedItems = state.cartItems.filter(item => item.id !== itemId);
      const filteredItems = CurrencyManager.filterCartByCurrency(updatedItems, state.selectedCurrency);
      
      return {
        ...state,
        cartItems: updatedItems,
        filteredItems,
        subtotal: CurrencyManager.calculateTotal(filteredItems, state.selectedCurrency)
      };
    }

    case CHECKOUT_ACTIONS.SET_SELECTED_CURRENCY: {
      const currency = action.payload;
      const filteredItems = CurrencyManager.filterCartByCurrency(state.cartItems, currency);
      const availablePaymentMethods = PaymentRegistry.getMethodsForCurrency(currency);
      
      return {
        ...state,
        selectedCurrency: currency,
        filteredItems,
        availablePaymentMethods,
        selectedPaymentMethod: null, // Reset payment method when currency changes
        subtotal: CurrencyManager.calculateTotal(filteredItems, currency)
      };
    }

    case CHECKOUT_ACTIONS.SET_PAYMENT_METHOD: {
      const method = action.payload;
      return {
        ...state,
        selectedPaymentMethod: method
      };
    }

    case CHECKOUT_ACTIONS.SET_CUSTOMER_INFO: {
      return {
        ...state,
        customerInfo: { ...state.customerInfo, ...action.payload }
      };
    }

    case CHECKOUT_ACTIONS.SET_DELIVERY_ADDRESS: {
      return {
        ...state,
        deliveryAddress: { ...state.deliveryAddress, ...action.payload }
      };
    }

    case CHECKOUT_ACTIONS.SET_ORDER_DATA: {
      return {
        ...state,
        orderData: action.payload,
        orderId: action.payload?.orderId || state.orderId
      };
    }

    case CHECKOUT_ACTIONS.UPDATE_ORDER_STATUS: {
      return {
        ...state,
        orderStatus: action.payload
      };
    }

    case CHECKOUT_ACTIONS.SET_CHECKOUT_STEP: {
      return {
        ...state,
        currentStep: action.payload
      };
    }

    case CHECKOUT_ACTIONS.SET_LOADING: {
      return {
        ...state,
        isLoading: action.payload
      };
    }

    case CHECKOUT_ACTIONS.SET_ERROR: {
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };
    }

    case CHECKOUT_ACTIONS.CLEAR_ERROR: {
      return {
        ...state,
        error: null
      };
    }

    case CHECKOUT_ACTIONS.CLEAR_CART: {
      return {
        ...initialState
      };
    }

    default:
      return state;
  }
}

// Context creation
const CheckoutContext = createContext();

// Custom hook to use checkout context
export const useCheckout = () => {
  const context = useContext(CheckoutContext);
  if (!context) {
    throw new Error('useCheckout must be used within a CheckoutProvider');
  }
  return context;
};

// Checkout Provider component
export const CheckoutProvider = ({ children }) => {
  const [state, dispatch] = useReducer(checkoutReducer, initialState);

  // Persist checkout state to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined' && state.cartItems.length > 0) {
      localStorage.setItem('checkout-state', JSON.stringify({
        cartItems: state.cartItems,
        selectedCurrency: state.selectedCurrency,
        customerInfo: state.customerInfo,
        deliveryAddress: state.deliveryAddress
      }));
    }
  }, [state.cartItems, state.selectedCurrency, state.customerInfo, state.deliveryAddress]);

  // Load persisted state on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('checkout-state');
      if (savedState) {
        try {
          const parsed = JSON.parse(savedState);
          if (parsed.cartItems && parsed.cartItems.length > 0) {
            dispatch({ type: CHECKOUT_ACTIONS.SET_CART_ITEMS, payload: parsed.cartItems });
            if (parsed.selectedCurrency) {
              dispatch({ type: CHECKOUT_ACTIONS.SET_SELECTED_CURRENCY, payload: parsed.selectedCurrency });
            }
            if (parsed.customerInfo) {
              dispatch({ type: CHECKOUT_ACTIONS.SET_CUSTOMER_INFO, payload: parsed.customerInfo });
            }
            if (parsed.deliveryAddress) {
              dispatch({ type: CHECKOUT_ACTIONS.SET_DELIVERY_ADDRESS, payload: parsed.deliveryAddress });
            }
          }
        } catch (error) {
          console.warn('Failed to load saved checkout state:', error);
          logCheckoutError('state_restoration_failed', error);
        }
      }
    }
  }, []);

  // Action creators
  const actions = {
    // Cart actions
    setCartItems: (items) => {
      logCheckoutEvent('cart_items_updated', { itemCount: items.length });
      dispatch({ type: CHECKOUT_ACTIONS.SET_CART_ITEMS, payload: items });
    },

    updateItemQuantity: (itemId, quantity) => {
      logCheckoutEvent('item_quantity_updated', { itemId, quantity });
      dispatch({ type: CHECKOUT_ACTIONS.UPDATE_ITEM_QUANTITY, payload: { itemId, quantity } });
    },

    removeItem: (itemId) => {
      logCheckoutEvent('item_removed', { itemId });
      dispatch({ type: CHECKOUT_ACTIONS.REMOVE_ITEM, payload: itemId });
    },

    clearCart: () => {
      logCheckoutEvent('cart_cleared');
      localStorage.removeItem('checkout-state');
      dispatch({ type: CHECKOUT_ACTIONS.CLEAR_CART });
    },

    // Currency actions
    setSelectedCurrency: (currency) => {
      logCheckoutEvent('currency_selected', { currency });
      dispatch({ type: CHECKOUT_ACTIONS.SET_SELECTED_CURRENCY, payload: currency });
    },

    // Payment actions
    setPaymentMethod: (method) => {
      logCheckoutEvent('payment_method_selected', { method });
      dispatch({ type: CHECKOUT_ACTIONS.SET_PAYMENT_METHOD, payload: method });
    },

    // Customer actions
    setCustomerInfo: (info) => {
      logCheckoutEvent('customer_info_updated');
      dispatch({ type: CHECKOUT_ACTIONS.SET_CUSTOMER_INFO, payload: info });
    },

    setDeliveryAddress: (address) => {
      logCheckoutEvent('delivery_address_updated');
      dispatch({ type: CHECKOUT_ACTIONS.SET_DELIVERY_ADDRESS, payload: address });
    },

    // Order actions
    setOrderData: (orderData) => {
      logCheckoutEvent('order_data_updated', { orderId: orderData?.orderId });
      dispatch({ type: CHECKOUT_ACTIONS.SET_ORDER_DATA, payload: orderData });
    },

    updateOrderStatus: (status) => {
      logCheckoutEvent('order_status_updated', { status, orderId: state.orderId });
      dispatch({ type: CHECKOUT_ACTIONS.UPDATE_ORDER_STATUS, payload: status });
    },

    // UI actions
    setCheckoutStep: (step) => {
      logCheckoutEvent('checkout_step_changed', { from: state.currentStep, to: step });
      dispatch({ type: CHECKOUT_ACTIONS.SET_CHECKOUT_STEP, payload: step });
    },

    setLoading: (loading) => {
      dispatch({ type: CHECKOUT_ACTIONS.SET_LOADING, payload: loading });
    },

    setError: (error) => {
      logCheckoutError('checkout_error_set', error);
      dispatch({ type: CHECKOUT_ACTIONS.SET_ERROR, payload: error });
    },

    clearError: () => {
      dispatch({ type: CHECKOUT_ACTIONS.CLEAR_ERROR });
    }
  };

  // Computed values
  const computedValues = {
    // Check if checkout is ready for next step
    canProceedToPayment: () => {
      return (
        state.filteredItems.length > 0 &&
        state.selectedCurrency &&
        (!ProductTypeManager.requiresAddress(state.productType) || 
         (state.deliveryAddress.address && state.deliveryAddress.city))
      );
    },

    // Check if order is ready to submit
    canSubmitOrder: () => {
      return (
        state.selectedPaymentMethod &&
        state.customerInfo.name &&
        state.customerInfo.email &&
        state.customerInfo.phone &&
        state.filteredItems.length > 0
      );
    },

    // Get current step progress
    getStepProgress: () => {
      if (!state.checkoutFlow) return { current: 0, total: 4 };
      
      const stepIndex = state.checkoutFlow.steps.indexOf(state.currentStep);
      return {
        current: stepIndex >= 0 ? stepIndex + 1 : 1,
        total: state.checkoutFlow.steps.length
      };
    },

    // Get formatted total
    getFormattedTotal: () => {
      return CurrencyManager.formatPrice(state.subtotal, state.selectedCurrency);
    }
  };

  const contextValue = {
    // State
    ...state,
    
    // Actions
    ...actions,
    
    // Computed values
    ...computedValues
  };

  return (
    <CheckoutContext.Provider value={contextValue}>
      {children}
    </CheckoutContext.Provider>
  );
};

export default CheckoutProvider;