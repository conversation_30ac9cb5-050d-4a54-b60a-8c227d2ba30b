import { toast } from "react-toastify"
import React from "react"
const STORAGE_KEY = "SHOPME_"
const USER_ID_KEY = "SHOPME_USER_ID"

const initialState = {
  cart: [],
  numberOfItemsInCart: 0,
  total: 0,
  affiliatearray: [],
}

const SiteContext = React.createContext()

function calculateTotal(cart) {
  const total = cart.reduce((acc, next) => {
    const quantity = next.quantity
    acc = acc + JSON.parse(next.price ? next.price : 0) * quantity
    return acc
  }, 0)
  return total
}

// Helper function to generate a unique user ID if none exists
function getUserId() {
  // Check if we're in the browser
  if (typeof window !== 'undefined') {
    // First try to get the user from localStorage (authenticated user)
    const user = localStorage.getItem('user');
    if (user) {
      try {
        const userData = JSON.parse(user);
        const authenticatedId = userData.id || userData._id;
        
        if (authenticatedId) {
          // Always ensure the USER_ID_KEY matches the customer ID for consistency
          const currentUserId = window.localStorage.getItem(USER_ID_KEY);
          if (currentUserId !== authenticatedId) {
            window.localStorage.setItem(USER_ID_KEY, authenticatedId);
            console.log('[CART_SYNC] Updated USER_ID_KEY to match authenticated user ID');
            
            // We should try to merge carts when a user logs in
            window.dispatchEvent(new CustomEvent('customer-identified', { 
              detail: { customerId: authenticatedId, previousId: currentUserId }
            }));
          }
          
          return authenticatedId;
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
    
    // Fallback to existing stored ID or generate new one
    let userId = window.localStorage.getItem(USER_ID_KEY);
    if (!userId) {
      userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
      console.log('[CART_SYNC] Created new user ID:', userId);
      window.localStorage.setItem(USER_ID_KEY, userId);
    } else {
      console.log('[CART_SYNC] Using existing user ID:', userId);
    }
    return userId;
  }
  return null;
}

// Helper function to get store ID from current URL
function getStoreIdFromUrl() {
  if (typeof window !== "undefined") {
    const pathSegments = window.location.pathname.split('/')
    if (pathSegments.length > 1) {
      console.log('[CART_SYNC] Store ID from URL:', pathSegments[1]);
      return pathSegments[1]
    }
  }
  console.log('[CART_SYNC] No store ID found in URL');
  return null
}

// Helper function to sync cart data with server
async function syncCartWithServer(cartData) {
  if (typeof window !== "undefined") {
    const userId = getUserId()
    const storeId = getStoreIdFromUrl() || 'default'
    
    console.log('[CART_SYNC] Syncing cart with server:', { 
      userId, 
      storeId, 
      cartItemCount: cartData.cart?.length,
      total: cartData.total
    });
    
    if (!userId) {
      console.log('[CART_SYNC] No user ID available, skipping sync');
      return;
    }
    
    if (!cartData.cart || !Array.isArray(cartData.cart)) {
      console.error('[CART_SYNC] Invalid cart data, missing or invalid cart array');
      return;
    }
    
    try {
      console.log('[CART_SYNC] Sending data to server');
      
      // Make sure numberOfItemsInCart is correct
      const numberOfItemsInCart = Array.isArray(cartData.cart) ? cartData.cart.length : 0;
      
      // Make sure we preserve essential data including images
      const cartWithImages = cartData.cart.map(item => ({
        id: item.id,
        sku: item.sku,
        name: item.name,
        price: item.price,
        currency: item.currency,
        quantity: item.quantity,
        store: item.store,
        image: item.image, // Preserve image data
        configInputs: item.configInputs || {},
      }));
      
      const response = await fetch('/api/cart/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          storeId,
          cart: cartWithImages,
          numberOfItemsInCart,
          total: cartData.total || 0
        })
      })
      
      if (!response.ok) {
        console.error('[CART_SYNC] Failed to sync cart with server, status:', response.status);
        const errorText = await response.text();
        console.error('[CART_SYNC] Error response:', errorText);
      } else {
        console.log('[CART_SYNC] Cart synced successfully with server');
      }
    } catch (error) {
      console.error('[CART_SYNC] Error syncing cart with server:', error)
    }
  }
}

class ContextProviderComponent extends React.Component {
  async componentDidMount() {
    if (typeof window !== "undefined") {
      // Initialize local storage if needed
      const storageState = window.localStorage.getItem(STORAGE_KEY)
      if (!storageState) {
        window.localStorage.setItem(STORAGE_KEY, JSON.stringify(initialState))
      }
      
      // Listen for customer identification events and merge carts
      window.addEventListener('customer-identified', this.handleCustomerIdentified)
      
      // Try to load cart data from server
      await this.loadCartFromServer()
      
      // Check if user is authenticated and update cart accordingly
      if (this.isUserAuthenticated()) {
        const userId = getUserId();
        // Get storeId safely - either from state, URL, or default to 'default'
        const storeId = this.state?.storeId || getStoreIdFromUrl() || 'default';
        console.log('[CART_SYNC] Authenticated user detected, forcing cart sync with ID:', userId);
        
        // Force refresh cart with authenticated user ID
        this.updateStateWithCartUpdate({
          userId,
          storeId
        });
      }
    }
  }
  
  componentWillUnmount() {
    if (typeof window !== "undefined") {
      // Clean up event listener
      window.removeEventListener('customer-identified', this.handleCustomerIdentified)
    }
  }
  
  // Handler for customer identification event
  handleCustomerIdentified = async (event) => {
    const { customerId, previousId } = event.detail
    console.log('[CART_MERGE] Customer identified, merging carts:', { customerId, previousId })
    
    try {
      const storeId = getStoreIdFromUrl()
      
      // Call the API to merge carts
      const response = await fetch('/api/cart/merge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId,
          previousId,
          storeId
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log('[CART_MERGE] Merge completed:', data)
        
        // Reload cart data from server to get the merged cart
        await this.loadCartFromServer()
      } else {
        console.error('[CART_MERGE] Merge failed:', await response.text())
      }
    } catch (error) {
      console.error('[CART_MERGE] Error merging carts:', error)
    }
  }
  
  // New method to load cart data from server
  loadCartFromServer = async () => {
    if (typeof window !== "undefined") {
      const userId = getUserId()
      const storeId = getStoreIdFromUrl() || 'default'
      
      console.log('[CART_LOAD] Loading cart from server:', { userId, storeId });
      
      if (!userId) {
        console.log('[CART_LOAD] No user ID available, skipping loading');
        return;
      }
      
      if (!storeId) {
        console.log('[CART_LOAD] No store ID available, using default');
      }
      
      try {
        console.log('[CART_LOAD] Fetching data from server');
        const url = `/api/cart/get?userId=${encodeURIComponent(userId)}&storeId=${encodeURIComponent(storeId)}`;
        console.log('[CART_LOAD] Request URL:', url);
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        
        console.log('[CART_LOAD] Response status:', response.status);
        
        if (response.ok) {
          const data = await response.json()
          console.log('[CART_LOAD] Data received from server:', data);
          
          if (data.success && data.data) {
            // Validate server data
            if (!data.data.cart || !Array.isArray(data.data.cart)) {
              console.error('[CART_LOAD] Invalid cart data from server, missing or invalid cart array');
              return;
            }
            
            // Only update local storage if server has more recent data
            const localData = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
            console.log('[CART_LOAD] Local data:', { 
              hasLocalData: !!localData,
              localTimestamp: localData?.lastUpdated,
              serverTimestamp: data.data.lastUpdated
            });
            
            if (!localData || !localData.lastUpdated || 
                (data.data.lastUpdated && new Date(data.data.lastUpdated) > new Date(localData.lastUpdated))) {
              console.log('[CART_LOAD] Updating local storage with server data');
              window.localStorage.setItem(
                STORAGE_KEY,
                JSON.stringify({
                  cart: data.data.cart,
                  numberOfItemsInCart: data.data.numberOfItemsInCart,
                  total: data.data.total,
                  lastUpdated: data.data.lastUpdated,
                  // Preserve other data like affiliatearray
                  ...(localData && { 
                    affiliatearray: localData.affiliatearray,
                    email: localData.email,
                    phone: localData.phone 
                  })
                })
              )
              console.log('[CART_LOAD] Local storage updated');
              this.forceUpdate()
            } else {
              console.log('[CART_LOAD] Local data is newer, not updating');
            }
          } else {
            console.log('[CART_LOAD] No valid data in server response');
          }
        } else {
          if (response.status === 404) {
            console.log('[CART_LOAD] No cart found on server (404), this is expected for new users');
          } else {
            console.log('[CART_LOAD] Failed to load cart from server');
            const errorText = await response.text();
            console.log('[CART_LOAD] Error response:', errorText);
          }
        }
      } catch (error) {
        console.error('[CART_LOAD] Error loading cart from server:', error)
      }
    }
  }

  setItemQuantity = (item) => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      const { cart } = storageState
      const index = cart.findIndex((cartItem) => cartItem.id === item.id)
      cart[index].quantity = item.quantity
      
      const updatedState = {
        cart,
        numberOfItemsInCart: cart.length,
        total: calculateTotal(cart),
        lastUpdated: new Date().toISOString()
      }
      
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      
      // Sync with server
      syncCartWithServer(updatedState)
      
      this.forceUpdate()
    }
  }

  addToCart = (item) => {
    if (typeof window !== "undefined") {
      console.log('[CART_ADD] Adding item to cart:', item);
      
      const userId = getUserId();
      
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      const { cart } = storageState
      
      // Ensure item has essential data, including image
      const cartItem = {
        id: item.id,
        sku: item.sku,
        name: item.name,
        price: item.price,
        currency: item.currency,
        quantity: item.quantity || 1,
        store: item.store,
        image: item.image, // Preserve image data
        configInputs: item.configInputs || {},
      };
      
      if (cart.length) {
        const index = cart.findIndex((cartItem) => cartItem.id === item.id)
        if (index >= Number(0)) {
          /* If this item is already in the cart, update the quantity */
          cart[index].quantity = cart[index].quantity + item.quantity
          
          // Make sure image is preserved
          if (item.image && !cart[index].image) {
            cart[index].image = item.image;
          }
          
          console.log('[CART_ADD] Updated existing item quantity:', cart[index].quantity);
        } else {
          /* If this item is not yet in the cart, add it */
          cart.push(cartItem)
          console.log('[CART_ADD] Added new item to cart');
        }
      } else {
        /* If no items in the cart, add the first item. */
        cart.push(cartItem)
        console.log('[CART_ADD] Added first item to cart');
      }

      const updatedState = {
        cart,
        numberOfItemsInCart: cart.length,
        total: calculateTotal(cart),
        lastUpdated: new Date().toISOString()
      }
      
      console.log('[CART_ADD] Updated cart state:', { 
        itemCount: updatedState.cart.length, 
        total: updatedState.total 
      });
      
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      
      // Ensure we're inside development or production environment
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production') {
        // Sync with server
        syncCartWithServer(updatedState)
      } else {
        console.log('[CART_ADD] Skipping server sync in test environment');
      }
      
      // Extract shop ID from item or URL
      let shopId = item.shopId; // Assuming item has shopId property
      if (!shopId) {
        // Try to get shop ID from URL
        const pathSegments = window.location.pathname.split('/');
        if (pathSegments.length > 1) {
          shopId = pathSegments[1];
        }
      }
      
      // Store shopId for reference in final variable to use in onClick
      const finalShopId = shopId;
      
      // Custom toast with PAY NOW button
      const ToastWithButton = () => (
        <div>
          <div>HÀNG ĐÃ BỎ VÀO GIỎ!</div>
          <button 
            onClick={() => this.openCart(finalShopId)} 
            className="mt-2 bg-white text-red-500 px-4 py-1 rounded font-bold hover:bg-gray-100"
          >
            PAY
          </button>
        </div>
      );
      
      toast(ToastWithButton, {
        position: toast.POSITION.TOP_LEFT,
        /* position: toast.POSITION.TOP_RIGHT, */
        className: "bg-red-500 font-bold text-white",
        autoClose: 5000,
      })
      
      this.forceUpdate()
      
      // Always use the authenticated user ID when available
      this.updateStateWithCartUpdate({
        userId,
        storeId: this.state?.storeId || getStoreIdFromUrl() || 'default',
        // other properties...
      });
    }
  }

  // Add a new method to handle opening the shop-specific cart
  openCart = (shopId) => {
    if (typeof window !== "undefined") {
      // Get current shop ID from the URL if not provided
      if (!shopId) {
        // Extract shop ID from the current URL path
        const pathSegments = window.location.pathname.split('/');
        // Assuming format like /[shopname]/product/...
        if (pathSegments.length > 1) {
          shopId = pathSegments[1]; // Get the shop name from URL path
        }
      }
      
      // Navigate to the shop-specific cart
      if (shopId) {
        window.location.href = `/${shopId}/cart`;
      } else {
        // Fallback to generic cart if shop ID can't be determined
        window.location.href = "/cart";
      }
    }
  }

  removeFromCart = (item) => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      let { cart } = storageState
      cart = cart.filter((c) => c.id !== item.id)

      const updatedState = {
        cart,
        numberOfItemsInCart: cart.length,
        total: calculateTotal(cart),
        lastUpdated: new Date().toISOString()
      }
      
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      
      // Sync with server
      syncCartWithServer(updatedState)
      
      this.forceUpdate()
    }
  }

  clearCart = () => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      
      const updatedState = {
        ...initialState,
        lastUpdated: new Date().toISOString(),
        // Preserve other data like affiliatearray
        affiliatearray: storageState.affiliatearray || []
      }
      
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      
      // Sync with server
      syncCartWithServer(updatedState)
      
      this.forceUpdate()
    }
  }
  recordUserDetails = (email, phone) => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      const updatedState = {
        ...storageState,
        email,
        phone,
        lastUpdated: new Date().toISOString()
      }
      
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      
      // Sync with server if cart data exists
      if (updatedState.cart && updatedState.cart.length > 0) {
        syncCartWithServer({
          cart: updatedState.cart,
          numberOfItemsInCart: updatedState.numberOfItemsInCart,
          total: updatedState.total,
          email,
          phone
        })
      }
      
      this.forceUpdate()
    }
  }

  removeUserDetails = () => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      const updatedState = {
        ...storageState,
        email: "",
        phone: "",
        lastUpdated: new Date().toISOString()
      }
      
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      
      // Sync with server if cart data exists
      if (updatedState.cart && updatedState.cart.length > 0) {
        syncCartWithServer({
          cart: updatedState.cart,
          numberOfItemsInCart: updatedState.numberOfItemsInCart,
          total: updatedState.total,
          email: "",
          phone: ""
        })
      }
      
      this.forceUpdate()
    }
  }

  // Force sync cart with customer ID when customer logs in
  forceCustomerCartSync = (customerId, customerInfo = {}) => {
    if (typeof window !== "undefined") {
      console.log('[CART_SYNC] Forcing cart sync for customer:', customerId);
      
      // Store the previous ID for merging
      const previousId = window.localStorage.getItem(USER_ID_KEY);
      
      // Set the customer ID and info
      if (customerId) {
        window.localStorage.setItem('SHOPME_CUSTOMER_ID', customerId);
        
        // Also update the user ID to match
        window.localStorage.setItem(USER_ID_KEY, customerId);
        
        // Store customer info if provided
        if (customerInfo && Object.keys(customerInfo).length > 0) {
          window.localStorage.setItem('SHOPME_CUSTOMER_INFO', JSON.stringify(customerInfo));
        }
        
        // Trigger cart merge if needed
        if (previousId && previousId !== customerId) {
          console.log('[CART_SYNC] Triggering cart merge from', previousId, 'to', customerId);
          window.dispatchEvent(new CustomEvent('customer-identified', { 
            detail: { customerId, previousId }
          }));
        }
        
        // Reload cart from server
        this.loadCartFromServer();
      }
    }
  }

  setAffiliateArray = (affiliatearray) => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      const updatedStorageState = {
        ...storageState,
        affiliatearray: affiliatearray,
      }
      window.localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify(updatedStorageState)
      )
      this.forceUpdate()
    }
  }

  emptyAffiliateArray = () => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      const updatedStorageState = {
        ...storageState,
        affiliatearray: [],
      }
      window.localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify(updatedStorageState)
      )
      this.forceUpdate()
    }
  }

  removeSpecificAffiliate = (affiliateIdToRemove) => {
    try {
      const storageState = JSON.parse(
        window.localStorage.getItem(STORAGE_KEY)
      ) || { affiliatearray: [] }
      const updatedAffiliates = storageState.affiliatearray.filter(
        (affiliate) => affiliate.affiliateid !== affiliateIdToRemove
      )
      const updatedStorageState = {
        ...storageState,
        affiliatearray: updatedAffiliates,
      }
      window.localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify(updatedStorageState)
      )
      // Add logic for force update if needed
      // this.forceUpdate();
      console.log("Specific affiliate removed successfully.")
    } catch (error) {
      /* console.error(       
        error
      )
 */    }
  }
  /* 
Example usage:
const affiliateIdToRemove = 'affiliate123';
removeSpecificAffiliate(affiliateIdToRemove);
*/

  /* addAffiliate = (affiliate) => {
  const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY)) || { affiliatearray: [] };
  const affiliatearray = Array.isArray(storageState.affiliatearray) ? storageState.affiliatearray : [];
  
  // Check if the affiliate already exists
  const isDuplicate = affiliatearray.some(existingAffiliate => 
      existingAffiliate.affiliateid === affiliate.affiliateid && existingAffiliate.shopid === affiliate.shopid
  );

  if (!isDuplicate) {
      const updatedStorageState = {
          ...storageState,
          affiliatearray: [...affiliatearray, affiliate],
      };
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedStorageState));
      // Add logic for force update if needed
      this.forceUpdate();
      console.log("Affiliate added successfully.");
  } else {
      console.log("Duplicate affiliate. Not added to the list.");
  }
}; */

  addAffiliate = (affiliate) => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(
        window.localStorage.getItem(STORAGE_KEY)
      ) || { affiliatearray: [] }
      const affiliatearray = Array.isArray(storageState.affiliatearray)
        ? storageState.affiliatearray
        : []

      // Check if the affiliate already exists
      const existingIndex = affiliatearray.findIndex(
        (existingAffiliate) =>
          existingAffiliate.affiliateid === affiliate.affiliateid &&
          existingAffiliate.shopid === affiliate.shopid
      )

      if (existingIndex !== -1) {
        // If affiliate exists, update its timestamp
        affiliatearray[existingIndex].timestamp = affiliate.timestamp
        console.log("Existing affiliate updated with new timestamp.")
      } else {
        // If not a duplicate, add the affiliate to the list
        affiliatearray.push(affiliate)
        console.log("New affiliate added successfully.")
      }
      /* alert(JSON.stringify(affiliatearray)) */

      const updatedStorageState = {
        ...storageState,
        affiliatearray: affiliatearray,
      }
      window.localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify(updatedStorageState)
      )
      // Add logic for force update if needed
      this.forceUpdate()
    }
  }

  getPreferredAffiliate = (currentStoreId, currentSKU) => {
    try {
      const storageState = JSON.parse(
        window.localStorage.getItem(STORAGE_KEY)
      ) || { affiliatearray: [] }

      // Sort affiliates by timestamp in descending order
      const sortedAffiliates = [...storageState.affiliatearray].sort(
        (a, b) => b.timestamp - a.timestamp
      )

      // If called without parameters, return the affiliate with the latest timestamp
      if (!currentSKU && !currentStoreId) {
        return sortedAffiliates[0]
      }

      // Check if any affiliate matches the SKU
      const skuMatch = sortedAffiliates.find(
        (affiliate) => affiliate.sku === currentSKU
      )
      if (skuMatch) {
        return skuMatch
      }

      // Check if any affiliate matches the store ID
      const storeIdMatch = sortedAffiliates.find(
        (affiliate) => affiliate.storeid === currentStoreId
      )
      if (storeIdMatch) {
        return storeIdMatch
      }

      // If no matches found, return the affiliate with the latest timestamp
      return sortedAffiliates[0]
    } catch (error) {
      /* console.error(        
        error
      ) */
      return null
    }
  }

  getAllAffiliateIDs = (currentStoreId, currentSKU) => {
    try {
      const storageState = JSON.parse(
        window.localStorage.getItem(STORAGE_KEY)
      ) || { affiliatearray: [] }

      // Sort affiliates by timestamp in descending order
      const sortedAffiliates = [...storageState.affiliatearray].sort(
        (a, b) => b.timestamp - a.timestamp
      )

      let bucket1 = []
      let bucket2 = []
      let bucket3 = []

      // Iterate through sorted affiliates to categorize them into buckets
      sortedAffiliates.forEach((affiliate) => {
        if (
          affiliate.storeid === currentStoreId &&
          affiliate.sku === currentSKU
        ) {
          bucket1.push(affiliate)
        } else if (affiliate.storeid === currentStoreId && !affiliate.sku) {
          bucket2.push(affiliate)
        } else {
          bucket3.push(affiliate)
        }
      })

      // Sort each bucket by timestamp in descending order
      bucket1.sort((a, b) => b.timestamp - a.timestamp)
      bucket2.sort((a, b) => b.timestamp - a.timestamp)
      bucket3.sort((a, b) => b.timestamp - a.timestamp)

      // Concatenate the buckets
      const result = [...bucket1, ...bucket2, ...bucket3]
      /* const resultWithDeleteOption = [
          ...result,
          { affiliateid: "DELETE_ALL", timestamp: Date.now(), storeid: "", sku: "" }
        ]; */

      // Return the IDs of the matched affiliates
      return result.map((affiliate) => affiliate.affiliateid)
      /* return resultWithDeleteOption.map(affiliate => affiliate.affiliateid);         */
    } catch (error) {
      //console.error("Error occurred while getting preferred affiliates:", error)
      return []
    }
  }

  isUserAuthenticated = () => {
    if (typeof window !== 'undefined') {
      // Check if user data exists in localStorage
      const userData = localStorage.getItem('user');
      return !!userData; // Returns true if user data exists
    }
    return false;
  }

  updateStateWithCartUpdate = (updateData) => {
    if (typeof window !== "undefined") {
      const storageState = JSON.parse(window.localStorage.getItem(STORAGE_KEY))
      const updatedState = {
        ...storageState,
        ...updateData,
        lastUpdated: new Date().toISOString()
      }
      
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState))
      
      // Sync with server
      syncCartWithServer(updatedState)
      
      this.forceUpdate()
    }
  }

  render() {
    let state = initialState
    if (typeof window !== "undefined") {
      const storageState = window.localStorage.getItem(STORAGE_KEY)
      if (storageState) {
        state = JSON.parse(storageState)
      }
    }

    return (
      <SiteContext.Provider
        value={{
          ...state,
          addToCart: this.addToCart,
          clearCart: this.clearCart,
          removeFromCart: this.removeFromCart,
          setItemQuantity: this.setItemQuantity,
          recordUserDetails: this.recordUserDetails,
          removeUserDetails: this.removeUserDetails,
          setAffiliateArray: this.setAffiliateArray,
          emptyAffiliateArray: this.emptyAffiliateArray,
          removeSpecificAffiliate: this.removeSpecificAffiliate,
          addAffiliate: this.addAffiliate,
          getPreferredAffiliate: this.getPreferredAffiliate,
          getAllAffiliateIDs: this.getAllAffiliateIDs,
          openCart: this.openCart,
          loadCartFromServer: this.loadCartFromServer,
          forceCustomerCartSync: this.forceCustomerCartSync,
        }}
      >
        {this.props.children}
      </SiteContext.Provider>
    )
  }
}

export { SiteContext, ContextProviderComponent }
