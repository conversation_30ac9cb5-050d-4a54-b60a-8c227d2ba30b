// Customer data model
const Customer = {
  id: String,         // Unique identifier
  email: String,      // Email address (used for login)
  password: String,   // Hashed password
  firstName: String,  // Customer's first name
  lastName: String,   // Customer's last name
  phone: String,      // Phone number
  address: String,    // Delivery address
  
  // Saved payment methods (references only)
  paymentMethods: [
    {
      id: String,     // Payment method ID
      type: String,   // Payment type (e.g., "card", "bank_account")
      lastFour: String, // Last four digits of card/account
      provider: String, // e.g., "visa", "mastercard", "sinopac"
      isDefault: Boolean // Whether this is the default payment method
    }
  ],
  
  // Order history (references to orders)
  orders: [String],   // Array of order IDs
  
  // Preferred currency
  preferredCurrency: String,  // e.g., "NT$", "VND", "USD"
  
  // Metadata
  createdAt: Date,    // When the account was created
  updatedAt: Date,    // When the account was last updated
  lastLogin: Date     // When the user last logged in
};

export default Customer; 