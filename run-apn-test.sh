#!/bin/bash

# 7-Eleven APN Test Runner
# This script automates testing the APN callback endpoint

# Color codes for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}====================================================${NC}"
echo -e "${BLUE}     7-ELEVEN APN INTEGRATION TEST RUNNER          ${NC}"
echo -e "${BLUE}====================================================${NC}"

# Check dependencies
echo -e "\n${YELLOW}Checking dependencies...${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js is not installed. Please install Node.js to continue.${NC}"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}npm is not installed. Please install npm to continue.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Node.js and npm are installed${NC}"

# Create test directory if it doesn't exist
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="${SCRIPT_DIR}/apn-test"

if [ ! -d "$TEST_DIR" ]; then
    echo -e "\n${YELLOW}Creating test directory...${NC}"
    mkdir -p "$TEST_DIR"
    echo -e "${GREEN}✓ Created test directory: $TEST_DIR${NC}"
fi

# Navigate to test directory
cd "$TEST_DIR" || { echo -e "${RED}Failed to navigate to test directory${NC}"; exit 1; }

# Initialize npm project if package.json doesn't exist
if [ ! -f "package.json" ]; then
    echo -e "\n${YELLOW}Initializing npm project...${NC}"
    echo '{
  "name": "7-eleven-apn-test",
  "version": "1.0.0",
  "description": "Test script for 7-Eleven APN integration",
  "main": "apn-test.js",
  "scripts": {
    "test": "node apn-test.js"
  },
  "author": "",
  "license": "ISC",
  "dependencies": {}
}' > package.json
    echo -e "${GREEN}✓ Initialized npm project${NC}"
fi

# Install dependencies
echo -e "\n${YELLOW}Installing dependencies...${NC}"
npm install axios crypto-js dotenv --save || { echo -e "${RED}Failed to install dependencies${NC}"; exit 1; }
echo -e "${GREEN}✓ Installed dependencies${NC}"

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo -e "\n${YELLOW}Creating .env file...${NC}"
    echo "# 7-Eleven APN Test Configuration
APN_URL=https://sim.dailoanshop.net/api/payment/apn-callback
API_ID=827315300001
TRANS_ID=TEST_TRANS_$(date +%s)
ORDER_NO=ORD-TEST-$(date +%s | cut -c6-13)
AMOUNT=299" > .env
    echo -e "${GREEN}✓ Created .env file${NC}"
fi

# Create the main test script
echo -e "\n${YELLOW}Creating test script...${NC}"
cat << 'EOF' > apn-test.js
/**
 * 7-Eleven APN Test Script
 * 
 * This script simulates the 7-Eleven APN callbacks to test
 * the integration with your system.
 */

require('dotenv').config();
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');

// Load configuration from .env
const APN_URL = process.env.APN_URL || 'https://sim.dailoanshop.net/api/payment/apn-callback';
const API_ID = process.env.API_ID || '827315300001';
let TRANS_ID = process.env.TRANS_ID || `TEST_TRANS_${Date.now()}`;
let ORDER_NO = process.env.ORDER_NO || `ORD-TEST-${Math.floor(Math.random() * 10000)}`;
const AMOUNT = process.env.AMOUNT || '299';

// Ensure the logs directory exists
const LOGS_DIR = './logs';
if (!fs.existsSync(LOGS_DIR)) {
  fs.mkdirSync(LOGS_DIR);
}

// Create a log file for this test run
const LOG_FILE = `${LOGS_DIR}/apn-test-${new Date().toISOString().replace(/:/g, '-')}.log`;
const logger = fs.createWriteStream(LOG_FILE, { flags: 'a' });

// Log function that writes to console and file
function log(message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  
  console.log(logMessage);
  logger.write(logMessage + '\n');
  
  if (data) {
    const dataStr = typeof data === 'object' ? JSON.stringify(data, null, 2) : data.toString();
    console.log(dataStr);
    logger.write(dataStr + '\n');
  }
}

// Generate checksum for APN validation
function generateChecksum(api_id, trans_id, amount, status, nonce) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  log(`Generating checksum with message: ${message}`);
  return crypto.createHash('md5').update(message).digest('hex');
}

// Function to simulate an APN call
async function simulateAPNCall(status, config = {}) {
  // Generate new nonce for each call to ensure uniqueness
  const nonce = Date.now().toString();
  
  // Use provided config or defaults
  const transId = config.trans_id || TRANS_ID;
  const orderNo = config.order_no || ORDER_NO;
  const amount = config.amount || AMOUNT;
  
  // Prepare APN payload
  const payload = {
    api_id: API_ID,
    trans_id: transId,
    order_no: orderNo,
    amount: amount,
    status: status,  // A = waiting, B = paid, D = expired
    nonce: nonce,
    checksum: generateChecksum(
      API_ID, 
      transId, 
      amount, 
      status, 
      nonce
    ),
    payment_code: 'PC' + Math.floor(Math.random() * 10000000),
    payment_detail: JSON.stringify({
      store_id: '12345',
      payment_time: new Date().toISOString(),
      receipt_no: 'R' + Math.floor(Math.random() * 1000000),
    })
  };

  log(`Sending ${getStatusName(status)} notification for order ${orderNo}...`, payload);
  
  try {
    const response = await axios.post(APN_URL, payload);
    log(`Response received (${response.status}):`, response.data);
    return { success: true, response: response.data };
  } catch (error) {
    const errorData = error.response ? error.response.data : error.message;
    log(`Error during APN call:`, errorData);
    return { 
      success: false, 
      error: errorData
    };
  }
}

// Helper to get status name
function getStatusName(statusCode) {
  switch(statusCode) {
    case 'A': return 'Waiting for payment';
    case 'B': return 'Payment completed';
    case 'D': return 'Payment expired';
    default: return 'Unknown status';
  }
}

// Run test scenarios in sequence
async function runAllTests() {
  log('=== 7-ELEVEN APN TEST SCENARIOS ===');
  log(`Target URL: ${APN_URL}`);
  log(`Test Order: ${ORDER_NO}`);
  log(`Transaction ID: ${TRANS_ID}`);
  log('===================================');
  
  // Scenario 1: Payment waiting
  log('\n🔄 SCENARIO 1: PAYMENT WAITING');
  await simulateAPNCall('A');
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Scenario 2: Payment completed
  log('\n✅ SCENARIO 2: PAYMENT COMPLETED');
  await simulateAPNCall('B');
  
  // Scenario 3: Payment expired (alternative flow)
  log('\n⏱️ SCENARIO 3: PAYMENT EXPIRED (NEW TRANSACTION)');
  
  // Create a new transaction ID for this scenario
  const expiredConfig = {
    trans_id: 'TEST_EXPIRED_' + Date.now(),
    order_no: 'ORD-EXPIRED-' + Math.floor(Math.random() * 10000),
    amount: AMOUNT
  };
  
  log(`Using new transaction for expired test:`, expiredConfig);
  
  // First set it to waiting
  await simulateAPNCall('A', expiredConfig);
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Then set it to expired
  await simulateAPNCall('D', expiredConfig);
  
  log('\n=== TEST COMPLETED ===');
  log(`Log file created at: ${LOG_FILE}`);
  
  // Close the log file
  logger.end();
}

// Run the tests
runAllTests();
EOF
echo -e "${GREEN}✓ Created test script${NC}"

# Create README file
echo -e "\n${YELLOW}Creating README file...${NC}"
cat << 'EOF' > README.md
# 7-Eleven APN Testing Tool

This tool helps you test the 7-Eleven APN (Active Payment Notification) integration with your system.

## Configuration

Edit the `.env` file to configure your test:

```
APN_URL=https://sim.dailoanshop.net/api/payment/apn-callback
API_ID=827315300001
TRANS_ID=TEST_TRANS_123456
ORDER_NO=ORD-TEST-123456
AMOUNT=299
```

## Running the Tests

Run the test script with:

```bash
npm test
```

This will simulate three scenarios:
1. Payment waiting (Status A)
2. Payment completed (Status B)
3. Payment expired (Status D) - using a new transaction

## Logs

All test results are stored in the `logs` directory.

## Understanding APN Statuses

- Status A: Waiting for payment
- Status B: Payment completed
- Status D: Payment expired

## Troubleshooting

If you encounter issues:

1. Check the log file for detailed error information
2. Verify your server is accessible from the internet
3. Ensure the checksum validation is working correctly
4. Check that the order ID exists in your system

## Customizing Tests

Edit the `apn-test.js` file to customize the test scenarios.
EOF
echo -e "${GREEN}✓ Created README file${NC}"

# Make the script executable
chmod +x apn-test.js

# Run the test
echo -e "\n${YELLOW}Running tests...${NC}"
npm test

echo -e "\n${BLUE}====================================================${NC}"
echo -e "${GREEN}Testing completed! Check the logs directory for detailed results.${NC}"
echo -e "${BLUE}====================================================${NC}" 