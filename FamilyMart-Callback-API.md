# FamilyMart Payment Callback API Documentation

## Overview
The FamilyMart Payment Callback API endpoint receives payment notifications from the FamilyMart payment system. When a customer completes, cancels, or has their payment expire at a FamilyMart store, the payment system sends a notification to this endpoint to update the order status.

## Endpoint Details
- **URL**: `/api/payment/familymart-callback`
- **Method**: POST
- **Content-Type**: application/x-www-form-urlencoded

## Request Parameters

| Parameter | Description | Required |
|-----------|-------------|----------|
| EC_ID | Merchant ID | Yes |
| PIN_CODE | Unique payment identification code | Yes |
| ORDER_NO | Order number | Yes |
| ORDER_DATE | Order creation date (YYYY-MM-DD HH:MM:SS) | Yes |
| AMOUNT | Transaction amount | Yes |
| STATUS_CODE | Payment status code | Yes |
| PAYMENT_NO | Payment transaction number | Yes |
| PAYMENT_DATE | Payment date (YYYY-MM-DD HH:MM:SS) | Yes |
| STORE_ID | FamilyMart store ID | Yes |
| BARCODE_1 | Barcode information (if applicable) | No |
| BARCODE_2 | Barcode information (if applicable) | No |
| BARCODE_3 | Barcode information (if applicable) | No |

## Status Codes

| STATUS_CODE | Description | Order Status Update |
|-------------|-------------|---------------------|
| 0 | Payment Completed | status: "processing", paymentStatus: "paid" |
| 1 | Payment Pending | status: "pending", paymentStatus: "waiting_payment" |
| 2 | Payment Expired | status: "cancelled", paymentStatus: "expired" |
| 3 | Payment Cancelled | status: "cancelled", paymentStatus: "cancelled" |

## Response

### Success Response
- **Status Code**: 200 OK
- **Content Type**: text/xml
- **Response Body**:
```xml
<?xml version="1.0" encoding="utf-8"?>
<EC_RESPONSE>
  <HEADER>
    <RESULT_CODE>0</RESULT_CODE>
    <RESULT_MSG>Success</RESULT_MSG>
  </HEADER>
  <BODY></BODY>
</EC_RESPONSE>
```

### Error Response
In case of error, the same structure is returned with appropriate error codes:
```xml
<?xml version="1.0" encoding="utf-8"?>
<EC_RESPONSE>
  <HEADER>
    <RESULT_CODE>1</RESULT_CODE>
    <RESULT_MSG>Error message</RESULT_MSG>
  </HEADER>
  <BODY></BODY>
</EC_RESPONSE>
```

## Implementation Notes

1. The system identifies orders using the PIN_CODE and ORDER_NO parameters.
2. Upon receiving a notification, the system:
   - Validates the request parameters
   - Locates the corresponding order in the database
   - Updates the order status based on the STATUS_CODE
   - Updates payment information with details from the notification
   - Responds with a success message

3. All notifications are logged for audit and troubleshooting purposes, as seen in the server logs:
   ```
   [FAMIPORT_CALLBACK] FamilyMart payment notification received: [Object: null prototype] {
     EC_ID: 'FAMISHOPTEST123',
     PIN_CODE: 'FM12345678',
     ORDER_NO: 'FM-TEST-12345',
     AMOUNT: '1380',
     STATUS_CODE: '0',
     PAYMENT_NO: 'FM1420057019',
     PAYMENT_DATE: '2025-04-16 16:32:58',
     STORE_ID: 'FAM9497',
     BARCODE_1: '',
     BARCODE_2: '',
     BARCODE_3: ''
   }
   [FAMIPORT_CALLBACK] Successfully updated order FM-TEST-12345 to status 0
   ```

4. For security, the system verifies that the EC_ID matches the configured merchant ID before processing the notification.

## Testing

The FamilyMart payment notification system can be tested using the `scripts/test-familymart-callback.js` utility, which simulates various payment notifications for a test order.

### Test Flow
1. Create a test order with FamilyMart payment method
2. Run the test script: `node scripts/test-familymart-callback.js`
3. Select options to simulate different payment statuses:
   - Option 1: Send Pending Payment Notification (STATUS_CODE=1)
   - Option 2: Send Completed Payment Notification (STATUS_CODE=0)
   - Option 3: Send Expired Payment Notification (STATUS_CODE=2)
   - Option 4: Send Cancelled Payment Notification (STATUS_CODE=3)
4. Observe the order status changes in the system

## Sample Flow

1. Customer selects FamilyMart as payment method during checkout
2. System creates an order with `paymentStatus: "waiting_payment"`
3. Customer receives a PIN code (e.g., FM12345678)
4. Customer pays at a FamilyMart store
5. FamilyMart payment system sends notification to the callback API
6. System updates the order status based on the payment result
7. Customer receives notification of successful payment 