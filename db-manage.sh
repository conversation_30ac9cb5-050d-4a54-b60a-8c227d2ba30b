#!/bin/bash

# MAG Shop Database Management Tool
# Simple wrapper script for easy execution

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Error: Node.js is not installed or not in PATH${NC}"
    echo "Please install Node.js to run this database management tool"
    exit 1
fi

# Check if the database management script exists
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DB_SCRIPT="$SCRIPT_DIR/scripts/db-management.js"

if [ ! -f "$DB_SCRIPT" ]; then
    echo -e "${RED}❌ Error: Database management script not found at $DB_SCRIPT${NC}"
    exit 1
fi

# Function to show quick help
show_help() {
    echo -e "${BLUE}🗄️  MAG Shop Database Management Tool${NC}"
    echo "======================================="
    echo ""
    echo "Usage:"
    echo "  $0                    - Run interactive menu"
    echo "  $0 help              - Show this help"
    echo "  $0 status            - Show quick database status"
    echo "  $0 analyze           - Analyze database"
    echo "  $0 backup [type]     - Create backup (full, data, logs)"
    echo "  $0 cleanup [type]    - Run cleanup (expired, test, orphaned, carts, logs, all)"
    echo ""
    echo "Examples:"
    echo "  $0 backup full       - Create full backup"
    echo "  $0 cleanup expired   - Clean expired orders (with dry run prompt)"
    echo "  $0 analyze          - Show detailed database analysis"
}

# Function to show quick status
show_status() {
    echo -e "${BLUE}📊 Quick Database Status${NC}"
    echo "======================="
    
    # Count orders
    if [ -f "$SCRIPT_DIR/data/orders.json" ]; then
        order_count=$(node -e "
            try {
                const data = require('./data/orders.json');
                console.log(data.length);
            } catch(e) {
                console.log('0');
            }
        " 2>/dev/null || echo "0")
        echo -e "📦 Orders: ${GREEN}$order_count${NC}"
    else
        echo -e "📦 Orders: ${YELLOW}No orders file found${NC}"
    fi
    
    # Count customers
    if [ -f "$SCRIPT_DIR/data/customers.json" ]; then
        customer_count=$(node -e "
            try {
                const data = require('./data/customers.json');
                console.log(data.length);
            } catch(e) {
                console.log('0');
            }
        " 2>/dev/null || echo "0")
        echo -e "👥 Customers: ${GREEN}$customer_count${NC}"
    else
        echo -e "👥 Customers: ${YELLOW}No customers file found${NC}"
    fi
    
    # Check logs
    if [ -d "$SCRIPT_DIR/logs" ]; then
        log_count=$(find "$SCRIPT_DIR/logs" -name "*.log" 2>/dev/null | wc -l | tr -d ' ')
        echo -e "📋 Log files: ${GREEN}$log_count${NC}"
    else
        echo -e "📋 Log files: ${YELLOW}No logs directory${NC}"
    fi
    
    # Check backups
    if [ -d "$SCRIPT_DIR/backups" ]; then
        backup_count=$(find "$SCRIPT_DIR/backups" -maxdepth 1 -type d ! -path "$SCRIPT_DIR/backups" 2>/dev/null | wc -l | tr -d ' ')
        echo -e "💾 Backups: ${GREEN}$backup_count${NC}"
    else
        echo -e "💾 Backups: ${YELLOW}No backups directory${NC}"
    fi
}

# Quick backup function
quick_backup() {
    local backup_type=${1:-full}
    echo -e "${BLUE}Creating $backup_type backup...${NC}"
    
    # Use direct backup script to avoid readline issues
    local DIRECT_BACKUP_SCRIPT="$SCRIPT_DIR/scripts/direct-backup.js"
    
    if [ ! -f "$DIRECT_BACKUP_SCRIPT" ]; then
        echo -e "${RED}❌ Error: Direct backup script not found at $DIRECT_BACKUP_SCRIPT${NC}"
        exit 1
    fi
    
    node "$DIRECT_BACKUP_SCRIPT" "$backup_type"
}

# Quick cleanup function  
quick_cleanup() {
    local cleanup_type=${1:-all}
    
    echo -e "${YELLOW}⚠️  Running $cleanup_type cleanup...${NC}"
    echo "You will be prompted for dry run in the interactive tool."
    
    case $cleanup_type in
        expired)
            node "$DB_SCRIPT" <<EOF
4
y
EOF
            ;;
        test)
            node "$DB_SCRIPT" <<EOF
5
y
EOF
            ;;
        orphaned)
            node "$DB_SCRIPT" <<EOF
6
y
EOF
            ;;
        carts)
            node "$DB_SCRIPT" <<EOF
7
y
EOF
            ;;
        logs)
            node "$DB_SCRIPT" <<EOF
8
y
EOF
            ;;
        all)
            node "$DB_SCRIPT" <<EOF
9
y
EOF
            ;;
        *)
            echo -e "${RED}❌ Invalid cleanup type: $cleanup_type${NC}"
            echo "Valid types: expired, test, orphaned, carts, logs, all"
            exit 1
            ;;
    esac
}

# Quick analyze function
quick_analyze() {
    echo -e "${BLUE}📊 Analyzing database...${NC}"
    node "$DB_SCRIPT" <<EOF
1
0
EOF
}

# Main script logic
case ${1:-} in
    help|--help|-h)
        show_help
        ;;
    status)
        show_status
        ;;
    analyze)
        quick_analyze
        ;;
    backup)
        quick_backup "$2"
        ;;
    cleanup)
        quick_cleanup "$2"
        ;;
    "")
        # No arguments - run interactive mode
        echo -e "${GREEN}🚀 Starting MAG Shop Database Management Tool...${NC}"
        echo ""
        node "$DB_SCRIPT"
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac 