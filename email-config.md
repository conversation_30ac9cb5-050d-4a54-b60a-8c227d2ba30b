# Email Notification Configuration

## Payment Alert Setup

The payment notification system will send you an email whenever someone:
- Initiates a payment (any method)
- Completes a payment
- Encounters payment failures

## SMTP Configuration (Hardcoded)

The email settings are hardcoded in `pages/api/notifications/payment-alert.js`. You need to replace the placeholder values with your actual SMTP details:

```javascript
// In pages/api/notifications/payment-alert.js
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: 'your-smtp-host.com',        // Your SMTP server host
    port: 587,                         // SMTP port (587 for TLS, 465 for SSL)
    secure: false,                     // true for 465, false for other ports
    auth: {
      user: 'your-smtp-username',      // Your SMTP username
      pass: 'your-smtp-password'       // Your SMTP password
    },
    tls: {
      rejectUnauthorized: false        // Accept self-signed certificates if needed
    }
  });
};

const ADMIN_EMAIL = '<EMAIL>';  // Email to receive notifications
```

## Common SMTP Settings

### Gmail SMTP
```javascript
host: 'smtp.gmail.com',
port: 587,
secure: false,
auth: {
  user: '<EMAIL>',
  pass: 'your-app-password'  // Use App Password, not regular password
}
```

### Outlook/Hotmail SMTP
```javascript
host: 'smtp-mail.outlook.com',
port: 587,
secure: false,
auth: {
  user: '<EMAIL>',
  pass: 'your-password'
}
```

### Yahoo SMTP
```javascript
host: 'smtp.mail.yahoo.com',
port: 587,
secure: false,
auth: {
  user: '<EMAIL>',
  pass: 'your-app-password'  // Use App Password
}
```

### Custom SMTP Server
```javascript
host: 'mail.yourdomain.com',
port: 587,                    // or 465 for SSL
secure: false,               // or true for SSL
auth: {
  user: '<EMAIL>',
  pass: 'your-email-password'
}
```

## Configuration Steps

1. **Edit the API file:**
   Open `pages/api/notifications/payment-alert.js`

2. **Replace placeholder values:**
   - `your-smtp-host.com` → Your SMTP server
   - `your-smtp-username` → Your email username
   - `your-smtp-password` → Your email password
   - `<EMAIL>` → Email where you want to receive notifications
   - `<EMAIL>` → Email address to send from

3. **Save and test**

## Supported Payment Methods

Email notifications will be sent for all payment methods:

- 🏪 **7-Eleven (iBON)** → "7-11 iBON"
- 💳 **7-Eleven (Credit/Debit Card)** → "7-11 Card" 
- 🛒 **FamilyMart** → "FamilyMart"
- 📱 **SINOPAC QR Payment** → "Sinopac Dynamic QR Code"

## Email Content

Each notification includes:
- ✅ Payment status (Initiated/Completed/Failed)
- 📋 Order details (ID, amount, timestamp)
- 💳 Payment method used
- 👤 Customer information (if available)
- 📦 Product details

## Testing

To test the email system:
1. Complete the SMTP configuration above
2. Go through a payment process on your site
3. Check your admin email for notifications

## Troubleshooting

### Common Issues:

1. **"Connection timeout"**
   - Check SMTP host and port settings
   - Verify firewall/network access to SMTP server

2. **"Authentication failed"**
   - Double-check username and password
   - For Gmail/Yahoo: Use App Passwords instead of regular passwords

3. **"No emails received"**
   - Check spam/junk folder
   - Verify ADMIN_EMAIL is correct
   - Check server logs for error messages

4. **"Self-signed certificate"**
   - Set `rejectUnauthorized: false` in TLS options
   - Or configure proper SSL certificates

### Port Information:
- **Port 25**: Unencrypted SMTP (often blocked by ISPs)
- **Port 587**: SMTP with STARTTLS (recommended)
- **Port 465**: SMTP over SSL (legacy but still used)

## API Endpoint

The email notification API is available at:
`POST /api/notifications/payment-alert`

This is called automatically by the payment monitoring system. 