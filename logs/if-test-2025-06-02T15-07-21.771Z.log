IF VRC System API Test - 2025-06-02T15:07:21.771Z

[2025-06-02T15:07:21.774Z] Configuration loaded successfully
[2025-06-02T15:07:21.776Z] Config:: {
  "merchantID": "01PE0016C",
  "merchantPassword": "[MASKED]",
  "merchantPasswordTest": "!Aa12345",
  "apiKey": "[MASKED]",
  "encryption": {
    "key": "[MASKED]",
    "iv": "[MASKED]",
    "algorithm": "aes-128-cbc"
  },
  "testUrl": "https://*************/vrc/VrcService/StoredValue",
  "liveUrl": "https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue",
  "notifyURL": "https://sim.dailoanshop.net/api/payment/if/callback",
  "productCodes": [
    {
      "id": "**********",
      "name": "<PERSON>an Hung Instant Recharge – Migrant $150",
      "price": 150
    },
    {
      "id": "**********",
      "name": "<PERSON><PERSON> Hung Instant Recharge – Migrant $300",
      "price": 300
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan 30 Days – $599 (60GB then throttle)",
      "price": 599
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan 30 Days – $698 (80GB then throttle)",
      "price": 698
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan (799)",
      "price": 799
    },
    {
      "id": "1000565472",
      "name": "4G Wireless Plan (1498)",
      "price": 1498
    },
    {
      "id": "1000559802",
      "name": "4G Wireless Plan 270 Days – $3594 (480GB)",
      "price": 3594
    },
    {
      "id": "1000559812",
      "name": "4G Wireless Plan 360 Days – $4792 (640GB)",
      "price": 4792
    },
    {
      "id": "1001690702",
      "name": "4G Wireless Plan (3600)",
      "price": 3600
    }
  ]
}
[2025-06-02T15:07:21.776Z] Starting IF VRC System API Test Script

================================================================================
ENVIRONMENT
================================================================================

[2025-06-02T15:07:21.776Z] Testing in test environment
[2025-06-02T15:07:21.776Z] API URL: https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:07:21.776Z] Mock API: No
[2025-06-02T15:07:21.776Z] Node.js version: v18.19.0
[2025-06-02T15:07:21.776Z] Platform: linux

================================================================================
TESTING ENCRYPTION
================================================================================

[2025-06-02T15:07:21.777Z] Testing AES-128-CBC encryption
[2025-06-02T15:07:21.778Z] SUCCESS - Encryption test completed successfully: [
  {
    "input": "[MASKED ACCOUNT]",
    "encrypted": "TewWTILrP+tMlue/DLLlMg=="
  },
  {
    "input": "[MASKED PASSWORD]",
    "encrypted": "nSHG8sc2I1f97sCMTilIwQ=="
  },
  {
    "input": "test1",
    "encrypted": "aCEjE/E/Oq4d4Z9c67DWcw=="
  }
]

================================================================================
TESTING PRODUCT LIST
================================================================================

[2025-06-02T15:07:21.778Z] Found 9 products in configuration
[2025-06-02T15:07:21.778Z] Product 1: ********** - Chuan Hung Instant Recharge – Migrant $150 (NT$150)
[2025-06-02T15:07:21.779Z] Product 2: ********** - Chuan Hung Instant Recharge – Migrant $300 (NT$300)
[2025-06-02T15:07:21.779Z] Product 3: ********** - 4G Wireless Plan 30 Days – $599 (60GB then throttle) (NT$599)
[2025-06-02T15:07:21.779Z] Product 4: ********** - 4G Wireless Plan 30 Days – $698 (80GB then throttle) (NT$698)
[2025-06-02T15:07:21.779Z] Product 5: ********** - 4G Wireless Plan (799) (NT$799)
[2025-06-02T15:07:21.779Z] Product 6: 1000565472 - 4G Wireless Plan (1498) (NT$1498)
[2025-06-02T15:07:21.779Z] Product 7: 1000559802 - 4G Wireless Plan 270 Days – $3594 (480GB) (NT$3594)
[2025-06-02T15:07:21.779Z] Product 8: 1000559812 - 4G Wireless Plan 360 Days – $4792 (640GB) (NT$4792)
[2025-06-02T15:07:21.779Z] Product 9: 1001690702 - 4G Wireless Plan (3600) (NT$3600)
[2025-06-02T15:07:21.779Z] SUCCESS - Product list retrieval successful

================================================================================
TESTING PHONE NUMBER VALIDATION
================================================================================

[2025-06-02T15:07:21.780Z] Phone validation as expected: 0912345678 - Valid 10-digit number: {
  "number": "0912345678",
  "expected": true,
  "actual": true,
  "reason": "Valid 10-digit number"
}
[2025-06-02T15:07:21.780Z] Phone validation as expected: 091234567 - Too short (9 digits): {
  "number": "091234567",
  "expected": false,
  "actual": false,
  "reason": "Too short (9 digits)"
}
[2025-06-02T15:07:21.780Z] Phone validation as expected: 09123456789 - Too long (11 digits): {
  "number": "09123456789",
  "expected": false,
  "actual": false,
  "reason": "Too long (11 digits)"
}
[2025-06-02T15:07:21.780Z] Phone validation as expected: ************ - Contains non-digit characters: {
  "number": "************",
  "expected": false,
  "actual": false,
  "reason": "Contains non-digit characters"
}
[2025-06-02T15:07:21.780Z] Phone validation as expected: abcdefghij - Non-numeric characters: {
  "number": "abcdefghij",
  "expected": false,
  "actual": false,
  "reason": "Non-numeric characters"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected:  - Empty string: {
  "number": "",
  "expected": false,
  "actual": false,
  "reason": "Empty string"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: 0901764431 - Generated test phone: {
  "number": "0901764431",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: 0902923121 - Generated test phone: {
  "number": "0902923121",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.781Z] Phone validation as expected: 0903561394 - Generated test phone: {
  "number": "0903561394",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone validation as expected: 0905021714 - Generated test phone: {
  "number": "0905021714",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone validation as expected: 0910064478 - Generated test phone: {
  "number": "0910064478",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone validation as expected: 0911545802 - Generated test phone: {
  "number": "0911545802",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone validation as expected: 0912349418 - Generated test phone: {
  "number": "0912349418",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone validation as expected: 0915546532 - Generated test phone: {
  "number": "0915546532",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone validation as expected: 0916239192 - Generated test phone: {
  "number": "0916239192",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone validation as expected: 0917498745 - Generated test phone: {
  "number": "0917498745",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:07:21.782Z] Phone numbers that will be used for testing:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "0901764431",
  "0902923121",
  "0903561394",
  "0905021714",
  "0910064478",
  "0911545802",
  "0912349418",
  "0915546532",
  "0916239192",
  "0917498745"
]

================================================================================
TESTING API CONNECTION
================================================================================

[2025-06-02T15:07:21.782Z] Testing API connection to https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:07:22.688Z] ERROR - API connection error: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testAPIConnection (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:260:22)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:155:5)"
}
[2025-06-02T15:07:22.689Z] ERROR - API connection test failed: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testAPIConnection (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:260:22)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:155:5)"
}

================================================================================
TESTING RECHARGE WORKFLOW
================================================================================

[2025-06-02T15:07:22.689Z] Testing recharge workflow with available test phone numbers:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********"
]
[2025-06-02T15:07:22.689Z] Note: The first number (**********) is the documented test number
[2025-06-02T15:07:22.689Z] Using product ID: **********
[2025-06-02T15:07:22.689Z] Attempt 1/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876842689",
  "environment": "test"
}
[2025-06-02T15:07:22.690Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:23.550Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:23.550Z] Attempt 2/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876843550",
  "environment": "test"
}
[2025-06-02T15:07:23.550Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:24.215Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:24.215Z] Attempt 3/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876844215",
  "environment": "test"
}
[2025-06-02T15:07:24.215Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:25.055Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:25.055Z] Attempt 4/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876845055",
  "environment": "test"
}
[2025-06-02T15:07:25.055Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:25.637Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:25.637Z] Attempt 5/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876845637",
  "environment": "test"
}
[2025-06-02T15:07:25.638Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:26.407Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:26.408Z] Attempt 6/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876846408",
  "environment": "test"
}
[2025-06-02T15:07:26.408Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:27.184Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:27.184Z] Attempt 7/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876847184",
  "environment": "test"
}
[2025-06-02T15:07:27.184Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:27.781Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:27.781Z] Attempt 8/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876847781",
  "environment": "test"
}
[2025-06-02T15:07:27.782Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:28.364Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:28.365Z] Attempt 9/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876848365",
  "environment": "test"
}
[2025-06-02T15:07:28.365Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:29.131Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:29.131Z] Attempt 10/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876849131",
  "environment": "test"
}
[2025-06-02T15:07:29.131Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:29.802Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:29.802Z] Attempt 11/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876849802",
  "environment": "test"
}
[2025-06-02T15:07:29.802Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:30.392Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:30.392Z] Attempt 12/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876850392",
  "environment": "test"
}
[2025-06-02T15:07:30.393Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:30.998Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:30.999Z] Attempt 13/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876850999",
  "environment": "test"
}
[2025-06-02T15:07:30.999Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:33.312Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:33.313Z] Attempt 14/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876853313",
  "environment": "test"
}
[2025-06-02T15:07:33.313Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:34.073Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:34.073Z] Attempt 15/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876854073",
  "environment": "test"
}
[2025-06-02T15:07:34.074Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:34.663Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:34.663Z] Attempt 16/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876854663",
  "environment": "test"
}
[2025-06-02T15:07:34.663Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:07:35.516Z] ERROR - API error with phone **********: {
  "message": "Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: ",
  "stack": "Error: Hostname/IP does not match certificate's altnames: IP: ************* is not in the cert's list: \n    at AxiosError.from (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:857:14)\n    at RedirectableRequest.handleRequestError (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:3169:25)\n    at RedirectableRequest.emit (node:events:529:35)\n    at eventHandlers.<computed> (/root/Workspace/mag.group.shop/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:517:28)\n    at TLSSocket.socketErrorListener (node:_http_client:501:9)\n    at TLSSocket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/root/Workspace/mag.group.shop/node_modules/axios/dist/node/axios.cjs:4258:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testRechargeWorkflow (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:391:24)\n    at async runTests (/root/Workspace/mag.group.shop/scripts/test-if-connection.js:163:5)"
}
[2025-06-02T15:07:35.517Z] Attempt 17/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748876855517",
  "environment": "test"
}
[2025-06-02T15:07:35.517Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
