IF VRC System API Test - 2025-06-02T15:23:49.062Z

[2025-06-02T15:23:49.063Z] Configuration loaded successfully
[2025-06-02T15:23:49.065Z] Config:: {
  "merchantID": "01PE0016C",
  "merchantPassword": "@Jing738123",
  "merchantPasswordTest": "@Jing738123",
  "apiKey": "pd2es73anm16zhe",
  "encryption": {
    "key": "ptWadzG6WfLMs7fi",
    "iv": "ptWadzG6WfLMs7fi",
    "algorithm": "aes-128-cbc"
  },
  "testUrl": "https://*************/vrc/VrcService/StoredValue",
  "liveUrl": "https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue",
  "notifyURL": "https://sim.dailoanshop.net/api/payment/if/callback",
  "productCodes": [
    {
      "id": "1000000001",
      "name": "<PERSON><PERSON> Instant Recharge – Migrant $150",
      "price": 150
    },
    {
      "id": "1000000002",
      "name": "<PERSON><PERSON> Hung Instant Recharge – Migrant $300",
      "price": 300
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan 30 Days – $599 (60GB then throttle)",
      "price": 599
    },
    {
      "id": "1002596642",
      "name": "4G Wireless Plan 30 Days – $698 (80GB then throttle)",
      "price": 698
    },
    {
      "id": "1004548392",
      "name": "4G Wireless Plan (799)",
      "price": 799
    },
    {
      "id": "1000565472",
      "name": "4G Wireless Plan (1498)",
      "price": 1498
    },
    {
      "id": "1000559802",
      "name": "4G Wireless Plan 270 Days – $3594 (480GB)",
      "price": 3594
    },
    {
      "id": "1000559812",
      "name": "4G Wireless Plan 360 Days – $4792 (640GB)",
      "price": 4792
    },
    {
      "id": "1001690702",
      "name": "4G Wireless Plan (3600)",
      "price": 3600
    }
  ]
}
[2025-06-02T15:23:49.065Z] Starting IF VRC System API Test Script

================================================================================
ENVIRONMENT
================================================================================

[2025-06-02T15:23:49.065Z] Testing in test environment
[2025-06-02T15:23:49.065Z] API URL: https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:23:49.066Z] Mock API: No
[2025-06-02T15:23:49.066Z] Node.js version: v18.19.0
[2025-06-02T15:23:49.066Z] Platform: linux

================================================================================
TESTING ENCRYPTION
================================================================================

[2025-06-02T15:23:49.066Z] Testing AES-128-CBC encryption
[2025-06-02T15:23:49.067Z] SUCCESS - Encryption test completed successfully: [
  {
    "input": "01PE0016C",
    "encrypted": "TewWTILrP+tMlue/DLLlMg=="
  },
  {
    "input": "@Jing738123",
    "encrypted": "nSHG8sc2I1f97sCMTilIwQ=="
  },
  {
    "input": "test1",
    "encrypted": "aCEjE/E/Oq4d4Z9c67DWcw=="
  }
]

================================================================================
TESTING PRODUCT LIST
================================================================================

[2025-06-02T15:23:49.067Z] Found 9 products in configuration
[2025-06-02T15:23:49.068Z] Product 1: 1000000001 - Chuan Hung Instant Recharge – Migrant $150 (NT$150)
[2025-06-02T15:23:49.068Z] Product 2: 1000000002 - Chuan Hung Instant Recharge – Migrant $300 (NT$300)
[2025-06-02T15:23:49.068Z] Product 3: ********** - 4G Wireless Plan 30 Days – $599 (60GB then throttle) (NT$599)
[2025-06-02T15:23:49.068Z] Product 4: 1002596642 - 4G Wireless Plan 30 Days – $698 (80GB then throttle) (NT$698)
[2025-06-02T15:23:49.068Z] Product 5: 1004548392 - 4G Wireless Plan (799) (NT$799)
[2025-06-02T15:23:49.068Z] Product 6: 1000565472 - 4G Wireless Plan (1498) (NT$1498)
[2025-06-02T15:23:49.068Z] Product 7: 1000559802 - 4G Wireless Plan 270 Days – $3594 (480GB) (NT$3594)
[2025-06-02T15:23:49.068Z] Product 8: 1000559812 - 4G Wireless Plan 360 Days – $4792 (640GB) (NT$4792)
[2025-06-02T15:23:49.068Z] Product 9: 1001690702 - 4G Wireless Plan (3600) (NT$3600)
[2025-06-02T15:23:49.069Z] SUCCESS - Product list retrieval successful

================================================================================
TESTING PHONE NUMBER VALIDATION
================================================================================

[2025-06-02T15:23:49.069Z] Phone validation as expected: 0912345678 - Valid 10-digit number: {
  "number": "0912345678",
  "expected": true,
  "actual": true,
  "reason": "Valid 10-digit number"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: 091234567 - Too short (9 digits): {
  "number": "091234567",
  "expected": false,
  "actual": false,
  "reason": "Too short (9 digits)"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: 09123456789 - Too long (11 digits): {
  "number": "09123456789",
  "expected": false,
  "actual": false,
  "reason": "Too long (11 digits)"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ************ - Contains non-digit characters: {
  "number": "************",
  "expected": false,
  "actual": false,
  "reason": "Contains non-digit characters"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: abcdefghij - Non-numeric characters: {
  "number": "abcdefghij",
  "expected": false,
  "actual": false,
  "reason": "Non-numeric characters"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected:  - Empty string: {
  "number": "",
  "expected": false,
  "actual": false,
  "reason": "Empty string"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.070Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0901024789 - Generated test phone: {
  "number": "0901024789",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0902191874 - Generated test phone: {
  "number": "0902191874",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0903682838 - Generated test phone: {
  "number": "0903682838",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0905917304 - Generated test phone: {
  "number": "0905917304",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0910912997 - Generated test phone: {
  "number": "0910912997",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0911095354 - Generated test phone: {
  "number": "0911095354",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0912817204 - Generated test phone: {
  "number": "0912817204",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0915495288 - Generated test phone: {
  "number": "0915495288",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0916777236 - Generated test phone: {
  "number": "0916777236",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone validation as expected: 0917604664 - Generated test phone: {
  "number": "0917604664",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:23:49.071Z] Phone numbers that will be used for testing:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "0901024789",
  "0902191874",
  "0903682838",
  "0905917304",
  "0910912997",
  "0911095354",
  "0912817204",
  "0915495288",
  "0916777236",
  "0917604664"
]

================================================================================
TESTING API CONNECTION
================================================================================

[2025-06-02T15:23:49.072Z] Testing API connection to https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:23:52.507Z] API connection response: {
  "status": 200,
  "headers": {
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "keep-alive": "timeout=20",
    "pragma": "no-cache",
    "content-length": "4309",
    "content-type": "text/html;charset=UTF-8",
    "content-language": "zh-TW",
    "expires": "0",
    "set-cookie": "JSESSIONID=FE047EE37522470E42F9FC537ED0873D; Path=/vrc; HttpOnly, TS010a4f4c=0128b2268eadb9c63fe85c25d7f4f868afb1a949d8d745b48fac79df34055d3571239fd6aea412eaa5a66db136ffe9273bde522c30; Path=/, TS014c0bb8=0128b2268eec92a418bce75f208d01de61e4f2e218d745b48fac79df34055d3571239fd6aedf4086dde90c3069854a8383388d0f7cd401f85a23d6667e253939d6d2b5e3b4; path=/vrc",
    "x-frame-options": "DENY",
    "x-content-type-options": "nosniff, nosniff",
    "x-xss-protection": "1; mode=block",
    "content-security-policy": "default-src 'self';frame-ancestors 'none';child-src 'self';form-action 'self';style-src 'unsafe-hashes' 'self' 'sha256-kQ9T+3qBuAV02dEclO9wTbycuZu3T2+Lj5/ZVzOYyQE=' 'sha256-oioPbIZ/LEg5Thf7wg21eCbltWcvsvdStlk38sKQvaE=' 'sha256-BBOGexNnujshehIQ4WlkijzyT1OZDSFMwde8dE1r6DE=' 'sha256-RbyBlbTXsmMhkwgIVR69ixRTZ07SHtCTKBXvAbX8Scw=' 'sha256-J6mngen95hg+HztCXrvF5nk7dMdPsNbVhgusMPpFrVU=' 'sha256-m3XTiIF20AAl/JoLbhZCLpVDCCo+QhhIqpqq9SZ30Dk=' 'sha256-BQ5eA/mw6jES31KSfh/A55TC7nzftLBWpZBzzDfwUrA=' 'sha256-iXc/FO8CIi70zP+WYtsqTeleUCtiC0LE0MBGJSukqAU=' 'sha256-iXc/FO8CIi70zP+WYtsqTeleUCtiC0LE0MBGJSukqAU=' 'sha256-LxdOQ2PAz1FZNXRvmzuREpPcaWGvFtzTzSNKUJWc5ZI=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-Wi3+8jbn12vus9Oq4FOqEUCOpuRG3clBaVvLZZ2b9Fs=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-Wi3+8jbn12vus9Oq4FOqEUCOpuRG3clBaVvLZZ2b9Fs=' 'sha256-DTHUrafealEzAIJ2HR8uKqgR0vK6l6BI0jiRCZ7QP/o=' 'sha256-aqNNdDLnnrDOnTNdkJpYlAxKVJtLt9CtFLklmInuUAE=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-tLhJLdhP/19dgmpWYiz9xIF+DCqxjOgU8dXIGTdF3Wg=' 'sha256-8f935d27GvUutRyY9yWScUMiFUk4WTdZURISiYfPOeQ=' 'sha256-9GpFGU1tDMbO06kZAytuOTLexT9K9kupCC1Y3byuj5I=' 'sha256-qy22kmEwMOU/jpzyoYI1quZY4XhnnZSZYySkvK6K4bM=' 'sha256-kBwH7wRTG5wZ7aC8kMy+YPVj/n/kH0nNRzE2Qhg3h1U=' ;script-src 'self';connect-src 'self';object-src 'self';",
    "referrer-policy": "no-referrer",
    "date": "Mon, 02 Jun 2025 15:23:44 GMT",
    "connection": "close"
  }
}
[2025-06-02T15:23:52.507Z] SUCCESS - API connection successful

================================================================================
TESTING RECHARGE WORKFLOW
================================================================================

[2025-06-02T15:23:52.508Z] Testing recharge workflow with available test phone numbers:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********"
]
[2025-06-02T15:23:52.508Z] Note: The first number (**********) is the documented test number
[2025-06-02T15:23:52.508Z] Using product ID: **********
[2025-06-02T15:23:52.508Z] Attempt 1/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877832508",
  "environment": "test"
}
[2025-06-02T15:23:52.509Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:23:53.521Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:23:53.521Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:23:53.522Z] Attempt 2/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877833522",
  "environment": "test"
}
[2025-06-02T15:23:53.522Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:23:54.571Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:23:54.571Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:23:54.571Z] Attempt 3/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877834571",
  "environment": "test"
}
[2025-06-02T15:23:54.572Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:23:55.702Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:23:55.702Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:23:55.702Z] Attempt 4/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877835702",
  "environment": "test"
}
[2025-06-02T15:23:55.702Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:23:57.040Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:23:57.040Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:23:57.040Z] Attempt 5/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877837040",
  "environment": "test"
}
[2025-06-02T15:23:57.040Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:23:58.143Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:23:58.143Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:23:58.143Z] Attempt 6/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877838143",
  "environment": "test"
}
[2025-06-02T15:23:58.143Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:00.274Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:00.275Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:00.275Z] Attempt 7/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877840275",
  "environment": "test"
}
[2025-06-02T15:24:00.275Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:01.288Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:01.288Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:01.288Z] Attempt 8/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877841288",
  "environment": "test"
}
[2025-06-02T15:24:01.288Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:03.290Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:03.290Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:03.290Z] Attempt 9/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877843290",
  "environment": "test"
}
[2025-06-02T15:24:03.290Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:04.365Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:04.366Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:04.366Z] Attempt 10/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877844366",
  "environment": "test"
}
[2025-06-02T15:24:04.366Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:05.503Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:05.503Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:05.503Z] Attempt 11/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877845503",
  "environment": "test"
}
[2025-06-02T15:24:05.503Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:08.294Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:08.295Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:08.295Z] Attempt 12/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877848295",
  "environment": "test"
}
[2025-06-02T15:24:08.295Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:09.424Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:09.424Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:09.424Z] Attempt 13/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877849424",
  "environment": "test"
}
[2025-06-02T15:24:09.425Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:10.426Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:10.427Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:10.427Z] Attempt 14/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877850427",
  "environment": "test"
}
[2025-06-02T15:24:10.427Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:11.865Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:11.866Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:11.866Z] Attempt 15/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877851866",
  "environment": "test"
}
[2025-06-02T15:24:11.866Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:12.787Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:12.787Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:12.787Z] Attempt 16/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877852787",
  "environment": "test"
}
[2025-06-02T15:24:12.787Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:13.927Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:13.927Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:13.927Z] Attempt 17/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877853927",
  "environment": "test"
}
[2025-06-02T15:24:13.927Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:15.271Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:15.271Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:15.271Z] Attempt 18/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877855271",
  "environment": "test"
}
[2025-06-02T15:24:15.271Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:24:16.248Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:24:16.249Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:24:16.249Z] ERROR - All recharge attempts failed: {}

================================================================================
TEST SUMMARY
================================================================================

[2025-06-02T15:24:16.249Z] Test completed at 2025-06-02T15:24:16.249Z
[2025-06-02T15:24:16.249Z] Log file: /root/Workspace/mag.group.shop/logs/if-test-2025-06-02T15-23-49.062Z.log

================================================================================
TESTS COMPLETED
================================================================================

