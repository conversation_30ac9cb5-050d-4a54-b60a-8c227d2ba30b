IF VRC System API Test - 2025-06-02T15:12:50.861Z

[2025-06-02T15:12:50.862Z] Configuration loaded successfully
[2025-06-02T15:12:50.864Z] Config:: {
  "merchantID": "01PE0016C",
  "merchantPassword": "[MASKED]",
  "merchantPasswordTest": "!Aa12345",
  "apiKey": "[MASKED]",
  "encryption": {
    "key": "[MASKED]",
    "iv": "[MASKED]",
    "algorithm": "aes-128-cbc"
  },
  "testUrl": "https://*************/vrc/VrcService/StoredValue",
  "liveUrl": "https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue",
  "notifyURL": "https://sim.dailoanshop.net/api/payment/if/callback",
  "productCodes": [
    {
      "id": "**********",
      "name": "<PERSON>an Hung Instant Recharge – Migrant $150",
      "price": 150
    },
    {
      "id": "**********",
      "name": "<PERSON><PERSON> Hung Instant Recharge – Migrant $300",
      "price": 300
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan 30 Days – $599 (60GB then throttle)",
      "price": 599
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan 30 Days – $698 (80GB then throttle)",
      "price": 698
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan (799)",
      "price": 799
    },
    {
      "id": "1000565472",
      "name": "4G Wireless Plan (1498)",
      "price": 1498
    },
    {
      "id": "1000559802",
      "name": "4G Wireless Plan 270 Days – $3594 (480GB)",
      "price": 3594
    },
    {
      "id": "1000559812",
      "name": "4G Wireless Plan 360 Days – $4792 (640GB)",
      "price": 4792
    },
    {
      "id": "1001690702",
      "name": "4G Wireless Plan (3600)",
      "price": 3600
    }
  ]
}
[2025-06-02T15:12:50.864Z] Starting IF VRC System API Test Script

================================================================================
ENVIRONMENT
================================================================================

[2025-06-02T15:12:50.865Z] Testing in test environment
[2025-06-02T15:12:50.865Z] API URL: https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:12:50.865Z] Mock API: No
[2025-06-02T15:12:50.865Z] Node.js version: v18.19.0
[2025-06-02T15:12:50.865Z] Platform: linux

================================================================================
TESTING ENCRYPTION
================================================================================

[2025-06-02T15:12:50.866Z] Testing AES-128-CBC encryption
[2025-06-02T15:12:50.867Z] SUCCESS - Encryption test completed successfully: [
  {
    "input": "[MASKED ACCOUNT]",
    "encrypted": "TewWTILrP+tMlue/DLLlMg=="
  },
  {
    "input": "[MASKED PASSWORD]",
    "encrypted": "nSHG8sc2I1f97sCMTilIwQ=="
  },
  {
    "input": "test1",
    "encrypted": "aCEjE/E/Oq4d4Z9c67DWcw=="
  }
]

================================================================================
TESTING PRODUCT LIST
================================================================================

[2025-06-02T15:12:50.867Z] Found 9 products in configuration
[2025-06-02T15:12:50.867Z] Product 1: ********** - Chuan Hung Instant Recharge – Migrant $150 (NT$150)
[2025-06-02T15:12:50.868Z] Product 2: ********** - Chuan Hung Instant Recharge – Migrant $300 (NT$300)
[2025-06-02T15:12:50.868Z] Product 3: ********** - 4G Wireless Plan 30 Days – $599 (60GB then throttle) (NT$599)
[2025-06-02T15:12:50.868Z] Product 4: ********** - 4G Wireless Plan 30 Days – $698 (80GB then throttle) (NT$698)
[2025-06-02T15:12:50.868Z] Product 5: ********** - 4G Wireless Plan (799) (NT$799)
[2025-06-02T15:12:50.868Z] Product 6: 1000565472 - 4G Wireless Plan (1498) (NT$1498)
[2025-06-02T15:12:50.868Z] Product 7: 1000559802 - 4G Wireless Plan 270 Days – $3594 (480GB) (NT$3594)
[2025-06-02T15:12:50.868Z] Product 8: 1000559812 - 4G Wireless Plan 360 Days – $4792 (640GB) (NT$4792)
[2025-06-02T15:12:50.868Z] Product 9: 1001690702 - 4G Wireless Plan (3600) (NT$3600)
[2025-06-02T15:12:50.868Z] SUCCESS - Product list retrieval successful

================================================================================
TESTING PHONE NUMBER VALIDATION
================================================================================

[2025-06-02T15:12:50.869Z] Phone validation as expected: 0912345678 - Valid 10-digit number: {
  "number": "0912345678",
  "expected": true,
  "actual": true,
  "reason": "Valid 10-digit number"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: 091234567 - Too short (9 digits): {
  "number": "091234567",
  "expected": false,
  "actual": false,
  "reason": "Too short (9 digits)"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: 09123456789 - Too long (11 digits): {
  "number": "09123456789",
  "expected": false,
  "actual": false,
  "reason": "Too long (11 digits)"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: ************ - Contains non-digit characters: {
  "number": "************",
  "expected": false,
  "actual": false,
  "reason": "Contains non-digit characters"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: abcdefghij - Non-numeric characters: {
  "number": "abcdefghij",
  "expected": false,
  "actual": false,
  "reason": "Non-numeric characters"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected:  - Empty string: {
  "number": "",
  "expected": false,
  "actual": false,
  "reason": "Empty string"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.870Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0901953858 - Generated test phone: {
  "number": "0901953858",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0902077420 - Generated test phone: {
  "number": "0902077420",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0903714706 - Generated test phone: {
  "number": "0903714706",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0905736033 - Generated test phone: {
  "number": "0905736033",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0910570972 - Generated test phone: {
  "number": "0910570972",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0911696209 - Generated test phone: {
  "number": "0911696209",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0912524037 - Generated test phone: {
  "number": "0912524037",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0915094033 - Generated test phone: {
  "number": "0915094033",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0916970061 - Generated test phone: {
  "number": "0916970061",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.871Z] Phone validation as expected: 0917265352 - Generated test phone: {
  "number": "0917265352",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:12:50.872Z] Phone numbers that will be used for testing:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "0901953858",
  "0902077420",
  "0903714706",
  "0905736033",
  "0910570972",
  "0911696209",
  "0912524037",
  "0915094033",
  "0916970061",
  "0917265352"
]

================================================================================
TESTING API CONNECTION
================================================================================

[2025-06-02T15:12:50.872Z] Testing API connection to https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:12:53.503Z] API connection response: {
  "status": 200,
  "headers": {
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "keep-alive": "timeout=20",
    "pragma": "no-cache",
    "content-length": "4309",
    "content-type": "text/html;charset=UTF-8",
    "content-language": "zh-TW",
    "expires": "0",
    "set-cookie": "JSESSIONID=B043C8B534423FF168E9563DA822C0B4; Path=/vrc; HttpOnly, TS010a4f4c=0128b2268eb893be2197ee4cbad4a22f9c4db3fec8fc13e890c0edb303828aa7f53b640466a8032358b0833262923b5e56d2f76530; Path=/, TS014c0bb8=0128b2268ec68c55c9fd6560f173bad3ddb183e206fc13e890c0edb303828aa7f53b640466282b1d007d707730b0974d2ff42a0c694b1184095d0f05530d8e2f2c8f426d32; path=/vrc",
    "x-frame-options": "DENY",
    "x-content-type-options": "nosniff, nosniff",
    "x-xss-protection": "1; mode=block",
    "content-security-policy": "default-src 'self';frame-ancestors 'none';child-src 'self';form-action 'self';style-src 'unsafe-hashes' 'self' 'sha256-kQ9T+3qBuAV02dEclO9wTbycuZu3T2+Lj5/ZVzOYyQE=' 'sha256-oioPbIZ/LEg5Thf7wg21eCbltWcvsvdStlk38sKQvaE=' 'sha256-BBOGexNnujshehIQ4WlkijzyT1OZDSFMwde8dE1r6DE=' 'sha256-RbyBlbTXsmMhkwgIVR69ixRTZ07SHtCTKBXvAbX8Scw=' 'sha256-J6mngen95hg+HztCXrvF5nk7dMdPsNbVhgusMPpFrVU=' 'sha256-m3XTiIF20AAl/JoLbhZCLpVDCCo+QhhIqpqq9SZ30Dk=' 'sha256-BQ5eA/mw6jES31KSfh/A55TC7nzftLBWpZBzzDfwUrA=' 'sha256-iXc/FO8CIi70zP+WYtsqTeleUCtiC0LE0MBGJSukqAU=' 'sha256-iXc/FO8CIi70zP+WYtsqTeleUCtiC0LE0MBGJSukqAU=' 'sha256-LxdOQ2PAz1FZNXRvmzuREpPcaWGvFtzTzSNKUJWc5ZI=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-Wi3+8jbn12vus9Oq4FOqEUCOpuRG3clBaVvLZZ2b9Fs=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-Wi3+8jbn12vus9Oq4FOqEUCOpuRG3clBaVvLZZ2b9Fs=' 'sha256-DTHUrafealEzAIJ2HR8uKqgR0vK6l6BI0jiRCZ7QP/o=' 'sha256-aqNNdDLnnrDOnTNdkJpYlAxKVJtLt9CtFLklmInuUAE=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-tLhJLdhP/19dgmpWYiz9xIF+DCqxjOgU8dXIGTdF3Wg=' 'sha256-8f935d27GvUutRyY9yWScUMiFUk4WTdZURISiYfPOeQ=' 'sha256-9GpFGU1tDMbO06kZAytuOTLexT9K9kupCC1Y3byuj5I=' 'sha256-qy22kmEwMOU/jpzyoYI1quZY4XhnnZSZYySkvK6K4bM=' 'sha256-kBwH7wRTG5wZ7aC8kMy+YPVj/n/kH0nNRzE2Qhg3h1U=' ;script-src 'self';connect-src 'self';object-src 'self';",
    "referrer-policy": "no-referrer",
    "date": "Mon, 02 Jun 2025 15:12:45 GMT",
    "connection": "close"
  }
}
[2025-06-02T15:12:53.504Z] SUCCESS - API connection successful

================================================================================
TESTING RECHARGE WORKFLOW
================================================================================

[2025-06-02T15:12:53.504Z] Testing recharge workflow with available test phone numbers:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********"
]
[2025-06-02T15:12:53.505Z] Note: The first number (**********) is the documented test number
[2025-06-02T15:12:53.505Z] Using product ID: **********
[2025-06-02T15:12:53.505Z] Attempt 1/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877173505",
  "environment": "test"
}
[2025-06-02T15:12:53.505Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:12.835Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:12.835Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:12.836Z] Attempt 2/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877192836",
  "environment": "test"
}
[2025-06-02T15:13:12.836Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:14.157Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:14.157Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:14.157Z] Attempt 3/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877194157",
  "environment": "test"
}
[2025-06-02T15:13:14.158Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:15.315Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:15.316Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:15.316Z] Attempt 4/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877195316",
  "environment": "test"
}
[2025-06-02T15:13:15.316Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:16.252Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:16.252Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:16.252Z] Attempt 5/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877196252",
  "environment": "test"
}
[2025-06-02T15:13:16.252Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:17.444Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:17.445Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:17.445Z] Attempt 6/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877197445",
  "environment": "test"
}
[2025-06-02T15:13:17.445Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:18.544Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:18.545Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:18.545Z] Attempt 7/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877198545",
  "environment": "test"
}
[2025-06-02T15:13:18.545Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:19.496Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:19.497Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:19.497Z] Attempt 8/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877199497",
  "environment": "test"
}
[2025-06-02T15:13:19.497Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:20.474Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:20.475Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:20.475Z] Attempt 9/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877200475",
  "environment": "test"
}
[2025-06-02T15:13:20.475Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:21.603Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:21.603Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:21.603Z] Attempt 10/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877201603",
  "environment": "test"
}
[2025-06-02T15:13:21.603Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:22.837Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:22.837Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:22.837Z] Attempt 11/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877202837",
  "environment": "test"
}
[2025-06-02T15:13:22.838Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:23.732Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:23.732Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:23.732Z] Attempt 12/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877203732",
  "environment": "test"
}
[2025-06-02T15:13:23.732Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:24.964Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:24.964Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:24.965Z] Attempt 13/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877204965",
  "environment": "test"
}
[2025-06-02T15:13:24.965Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:26.086Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:26.086Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:26.086Z] Attempt 14/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877206086",
  "environment": "test"
}
[2025-06-02T15:13:26.086Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:27.231Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:27.231Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:27.231Z] Attempt 15/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877207231",
  "environment": "test"
}
[2025-06-02T15:13:27.231Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:28.368Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:28.368Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:28.368Z] Attempt 16/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877208368",
  "environment": "test"
}
[2025-06-02T15:13:28.369Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:29.363Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:29.363Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:29.363Z] Attempt 17/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877209363",
  "environment": "test"
}
[2025-06-02T15:13:29.364Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:30.363Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:30.363Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:30.363Z] Attempt 18/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877210363",
  "environment": "test"
}
[2025-06-02T15:13:30.363Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:13:31.492Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:13:31.492Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:13:31.492Z] ERROR - All recharge attempts failed: {}

================================================================================
TEST SUMMARY
================================================================================

[2025-06-02T15:13:31.493Z] Test completed at 2025-06-02T15:13:31.493Z
[2025-06-02T15:13:31.493Z] Log file: /root/Workspace/mag.group.shop/logs/if-test-2025-06-02T15-12-50.861Z.log

================================================================================
TESTS COMPLETED
================================================================================

