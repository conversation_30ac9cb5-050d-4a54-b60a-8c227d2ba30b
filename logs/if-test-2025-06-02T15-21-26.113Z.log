IF VRC System API Test - 2025-06-02T15:21:26.114Z

[2025-06-02T15:21:26.114Z] Configuration loaded successfully
[2025-06-02T15:21:26.116Z] Config:: {
  "merchantID": "01PE0016C",
  "merchantPassword": "[MASKED]",
  "merchantPasswordTest": "@Jing738123",
  "apiKey": "[MASKED]",
  "encryption": {
    "key": "[MASKED]",
    "iv": "[MASKED]",
    "algorithm": "aes-128-cbc"
  },
  "testUrl": "https://*************/vrc/VrcService/StoredValue",
  "liveUrl": "https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue",
  "notifyURL": "https://sim.dailoanshop.net/api/payment/if/callback",
  "productCodes": [
    {
      "id": "**********",
      "name": "<PERSON><PERSON> Hung Instant Recharge – Migrant $150",
      "price": 150
    },
    {
      "id": "**********",
      "name": "<PERSON><PERSON> Hung Instant Recharge – Migrant $300",
      "price": 300
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan 30 Days – $599 (60GB then throttle)",
      "price": 599
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan 30 Days – $698 (80GB then throttle)",
      "price": 698
    },
    {
      "id": "**********",
      "name": "4G Wireless Plan (799)",
      "price": 799
    },
    {
      "id": "1000565472",
      "name": "4G Wireless Plan (1498)",
      "price": 1498
    },
    {
      "id": "1000559802",
      "name": "4G Wireless Plan 270 Days – $3594 (480GB)",
      "price": 3594
    },
    {
      "id": "1000559812",
      "name": "4G Wireless Plan 360 Days – $4792 (640GB)",
      "price": 4792
    },
    {
      "id": "1001690702",
      "name": "4G Wireless Plan (3600)",
      "price": 3600
    }
  ]
}
[2025-06-02T15:21:26.116Z] Starting IF VRC System API Test Script

================================================================================
ENVIRONMENT
================================================================================

[2025-06-02T15:21:26.117Z] Testing in test environment
[2025-06-02T15:21:26.117Z] API URL: https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:21:26.117Z] Mock API: No
[2025-06-02T15:21:26.117Z] Node.js version: v18.19.0
[2025-06-02T15:21:26.118Z] Platform: linux

================================================================================
TESTING ENCRYPTION
================================================================================

[2025-06-02T15:21:26.118Z] Testing AES-128-CBC encryption
[2025-06-02T15:21:26.121Z] SUCCESS - Encryption test completed successfully: [
  {
    "input": "[MASKED ACCOUNT]",
    "encrypted": "TewWTILrP+tMlue/DLLlMg=="
  },
  {
    "input": "[MASKED PASSWORD]",
    "encrypted": "nSHG8sc2I1f97sCMTilIwQ=="
  },
  {
    "input": "test1",
    "encrypted": "aCEjE/E/Oq4d4Z9c67DWcw=="
  }
]

================================================================================
TESTING PRODUCT LIST
================================================================================

[2025-06-02T15:21:26.121Z] Found 9 products in configuration
[2025-06-02T15:21:26.121Z] Product 1: ********** - Chuan Hung Instant Recharge – Migrant $150 (NT$150)
[2025-06-02T15:21:26.122Z] Product 2: ********** - Chuan Hung Instant Recharge – Migrant $300 (NT$300)
[2025-06-02T15:21:26.122Z] Product 3: ********** - 4G Wireless Plan 30 Days – $599 (60GB then throttle) (NT$599)
[2025-06-02T15:21:26.122Z] Product 4: ********** - 4G Wireless Plan 30 Days – $698 (80GB then throttle) (NT$698)
[2025-06-02T15:21:26.122Z] Product 5: ********** - 4G Wireless Plan (799) (NT$799)
[2025-06-02T15:21:26.122Z] Product 6: 1000565472 - 4G Wireless Plan (1498) (NT$1498)
[2025-06-02T15:21:26.122Z] Product 7: 1000559802 - 4G Wireless Plan 270 Days – $3594 (480GB) (NT$3594)
[2025-06-02T15:21:26.122Z] Product 8: 1000559812 - 4G Wireless Plan 360 Days – $4792 (640GB) (NT$4792)
[2025-06-02T15:21:26.122Z] Product 9: 1001690702 - 4G Wireless Plan (3600) (NT$3600)
[2025-06-02T15:21:26.123Z] SUCCESS - Product list retrieval successful

================================================================================
TESTING PHONE NUMBER VALIDATION
================================================================================

[2025-06-02T15:21:26.123Z] Phone validation as expected: 0912345678 - Valid 10-digit number: {
  "number": "0912345678",
  "expected": true,
  "actual": true,
  "reason": "Valid 10-digit number"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected: 091234567 - Too short (9 digits): {
  "number": "091234567",
  "expected": false,
  "actual": false,
  "reason": "Too short (9 digits)"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected: 09123456789 - Too long (11 digits): {
  "number": "09123456789",
  "expected": false,
  "actual": false,
  "reason": "Too long (11 digits)"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected: ************ - Contains non-digit characters: {
  "number": "************",
  "expected": false,
  "actual": false,
  "reason": "Contains non-digit characters"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected: abcdefghij - Non-numeric characters: {
  "number": "abcdefghij",
  "expected": false,
  "actual": false,
  "reason": "Non-numeric characters"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected:  - Empty string: {
  "number": "",
  "expected": false,
  "actual": false,
  "reason": "Empty string"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.124Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: ********** - Generated test phone: {
  "number": "**********",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: 0933333333 - Generated test phone: {
  "number": "0933333333",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: 0955555555 - Generated test phone: {
  "number": "0955555555",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: 0901999032 - Generated test phone: {
  "number": "0901999032",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: 0902612461 - Generated test phone: {
  "number": "0902612461",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: 0903755698 - Generated test phone: {
  "number": "0903755698",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.125Z] Phone validation as expected: 0905959687 - Generated test phone: {
  "number": "0905959687",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.126Z] Phone validation as expected: 0910132571 - Generated test phone: {
  "number": "0910132571",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.126Z] Phone validation as expected: 0911352765 - Generated test phone: {
  "number": "0911352765",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.126Z] Phone validation as expected: 0912702089 - Generated test phone: {
  "number": "0912702089",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.126Z] Phone validation as expected: 0915103190 - Generated test phone: {
  "number": "0915103190",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.126Z] Phone validation as expected: 0916546240 - Generated test phone: {
  "number": "0916546240",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.126Z] Phone validation as expected: 0917477543 - Generated test phone: {
  "number": "0917477543",
  "expected": true,
  "actual": true,
  "reason": "Generated test phone"
}
[2025-06-02T15:21:26.126Z] Phone numbers that will be used for testing:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "0933333333",
  "0955555555",
  "0901999032",
  "0902612461",
  "0903755698",
  "0905959687",
  "0910132571",
  "0911352765",
  "0912702089",
  "0915103190",
  "0916546240",
  "0917477543"
]

================================================================================
TESTING API CONNECTION
================================================================================

[2025-06-02T15:21:26.126Z] Testing API connection to https://*************/vrc/VrcService/StoredValue
[2025-06-02T15:21:28.534Z] API connection response: {
  "status": 200,
  "headers": {
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "keep-alive": "timeout=20",
    "pragma": "no-cache",
    "content-length": "4309",
    "content-type": "text/html;charset=UTF-8",
    "content-language": "zh-TW",
    "expires": "0",
    "set-cookie": "JSESSIONID=CE0A242BCEDA168DF634A35E25C25AFC; Path=/vrc; HttpOnly, TS010a4f4c=0128b2268e3d014dde88b2e22cc7825b789a8f1a509611082127e58057e062ecb7009acdbbfd3d393de69e0029664d2c3a8e1605a9; Path=/, TS014c0bb8=0128b2268eab0d2b375f7746bc72f3244bf38572f99611082127e58057e062ecb7009acdbb812ae3cc427a669488a06115b5f8a520aef86643e6e6fe3a952d0e924f7ae4cc; path=/vrc",
    "x-frame-options": "DENY",
    "x-content-type-options": "nosniff, nosniff",
    "x-xss-protection": "1; mode=block",
    "content-security-policy": "default-src 'self';frame-ancestors 'none';child-src 'self';form-action 'self';style-src 'unsafe-hashes' 'self' 'sha256-kQ9T+3qBuAV02dEclO9wTbycuZu3T2+Lj5/ZVzOYyQE=' 'sha256-oioPbIZ/LEg5Thf7wg21eCbltWcvsvdStlk38sKQvaE=' 'sha256-BBOGexNnujshehIQ4WlkijzyT1OZDSFMwde8dE1r6DE=' 'sha256-RbyBlbTXsmMhkwgIVR69ixRTZ07SHtCTKBXvAbX8Scw=' 'sha256-J6mngen95hg+HztCXrvF5nk7dMdPsNbVhgusMPpFrVU=' 'sha256-m3XTiIF20AAl/JoLbhZCLpVDCCo+QhhIqpqq9SZ30Dk=' 'sha256-BQ5eA/mw6jES31KSfh/A55TC7nzftLBWpZBzzDfwUrA=' 'sha256-iXc/FO8CIi70zP+WYtsqTeleUCtiC0LE0MBGJSukqAU=' 'sha256-iXc/FO8CIi70zP+WYtsqTeleUCtiC0LE0MBGJSukqAU=' 'sha256-LxdOQ2PAz1FZNXRvmzuREpPcaWGvFtzTzSNKUJWc5ZI=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-Wi3+8jbn12vus9Oq4FOqEUCOpuRG3clBaVvLZZ2b9Fs=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-Wi3+8jbn12vus9Oq4FOqEUCOpuRG3clBaVvLZZ2b9Fs=' 'sha256-DTHUrafealEzAIJ2HR8uKqgR0vK6l6BI0jiRCZ7QP/o=' 'sha256-aqNNdDLnnrDOnTNdkJpYlAxKVJtLt9CtFLklmInuUAE=' 'sha256-TVJNw8uhYM1WSQKLqn1BdM8woVJ4pNCFBNLTuq8tuLw=' 'sha256-tLhJLdhP/19dgmpWYiz9xIF+DCqxjOgU8dXIGTdF3Wg=' 'sha256-8f935d27GvUutRyY9yWScUMiFUk4WTdZURISiYfPOeQ=' 'sha256-9GpFGU1tDMbO06kZAytuOTLexT9K9kupCC1Y3byuj5I=' 'sha256-qy22kmEwMOU/jpzyoYI1quZY4XhnnZSZYySkvK6K4bM=' 'sha256-kBwH7wRTG5wZ7aC8kMy+YPVj/n/kH0nNRzE2Qhg3h1U=' ;script-src 'self';connect-src 'self';object-src 'self';",
    "referrer-policy": "no-referrer",
    "date": "Mon, 02 Jun 2025 15:21:20 GMT",
    "connection": "close"
  }
}
[2025-06-02T15:21:28.534Z] SUCCESS - API connection successful

================================================================================
TESTING RECHARGE WORKFLOW
================================================================================

[2025-06-02T15:21:28.535Z] Testing recharge workflow with available test phone numbers:: [
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "**********",
  "0933333333",
  "0955555555",
  "0901672316",
  "0902890185",
  "0903312813",
  "0905453083",
  "0910264209",
  "0911492238",
  "0912584318",
  "0915520280",
  "**********",
  "**********"
]
[2025-06-02T15:21:28.535Z] Note: The first number (**********) is the documented test number
[2025-06-02T15:21:28.535Z] Using product ID: **********
[2025-06-02T15:21:28.535Z] Attempt 1/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877688535",
  "environment": "test"
}
[2025-06-02T15:21:28.536Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:21:29.641Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:21:29.642Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:21:29.642Z] Attempt 2/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877689642",
  "environment": "test"
}
[2025-06-02T15:21:29.642Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:21:30.777Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:21:30.777Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:21:30.777Z] Attempt 3/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877690777",
  "environment": "test"
}
[2025-06-02T15:21:30.777Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:21:31.843Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:21:31.843Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:21:31.843Z] Attempt 4/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877691843",
  "environment": "test"
}
[2025-06-02T15:21:31.844Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:21:32.721Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:21:32.721Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:21:32.721Z] Attempt 5/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877692721",
  "environment": "test"
}
[2025-06-02T15:21:32.721Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
[2025-06-02T15:21:33.632Z] Recharge API response: {
  "ReturnCode": "0004",
  "ReturnMsg": "帳戶驗證失敗，請稍後再試",
  "ReturnTXID": null
}
[2025-06-02T15:21:33.633Z] ERROR - Recharge failed with phone **********: {}
[2025-06-02T15:21:33.633Z] Attempt 6/18 - Testing with phone: **********: {
  "phoneNumber": "**********",
  "productId": "**********",
  "orderId": "test_1748877693633",
  "environment": "test"
}
[2025-06-02T15:21:33.633Z] Sending recharge request: {
  "url": "https://*************/vrc/VrcService/StoredValue",
  "payload": {
    "Account": "[ENCRYPTED]",
    "Password": "[ENCRYPTED]",
    "PhoneNumber": "**********",
    "FETOfferID": "**********"
  }
}
