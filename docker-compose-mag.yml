networks:
  web:
    external: true

services:
  # Main ABN.green application (with Pages Router API endpoints)
  magshop:
    image: node:20
    container_name: magshop
    restart: always
    working_dir: /app
    command: sh -c "yarn serve"
    volumes:
      - .:/app
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SITE_URL=https://mag.group.shop
      - NEXT_PUBLIC_SITE_NAME=MAG Group Shop
      - GOOGLE_SHEETS_INVENTORY_ID=1iljoVGTSqNBphCV7ybTi67Z7areRRbf2ji74GSi-lN8
      - GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    labels:
      - traefik.enable=true
      - traefik.http.routers.magshop.rule=Host(`sim.dailoanshop.net`, `simserver.dailoanshop.net`, `sim.abnasia.org`, `dockersim.abnasia.org`)
      - traefik.http.routers.magshop.tls=true
      - traefik.http.routers.magshop.tls.certresolver=lets-encrypt
      - traefik.http.services.magshop.loadbalancer.server.port=3000
    networks:
      - web
