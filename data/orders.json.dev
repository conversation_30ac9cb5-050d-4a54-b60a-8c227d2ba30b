[{"id": "MAG-7608", "customerId": "**********", "customerName": "<PERSON><PERSON><PERSON>", "customerEmail": "<EMAIL>", "customerPhone": "**********", "items": [{"name": "OK  MẠNG 6 THÁNG", "price": 2000, "quantity": 1, "sku": "taiwan.yearsim.OK.dpRP"}], "paymentMethod": "convenience_store", "paymentSubMethod": "seven_eleven_ibon", "amount": 2000, "fee": 25, "totalAmount": 2025, "currency": "NT$", "status": "completed", "paymentStatus": "paid", "createdAt": "2025-06-10T11:45:30.291Z", "updatedAt": "2025-06-10T19:13:35.302Z", "storeId": "magshop", "paymentInfo": {"barcodes": {"barcode1": "1406105D7", "barcode2": "050610QPZMTDVL01", "barcode3": "431955870002025", "expiresInMinutes": 10080}, "ibonPaymentCode": "************", "ibonShopId": "CCAT", "orderExpireDate": "2025-06-17", "billAmount": 2025, "fee": 0, "paymentType": "IBON_REALTIME", "shortUrl": "https://s.ccat.com.tw/Q/aW78cRagb"}, "validUntil": "2025-06-17", "isExpired": false, "expiryReason": null, "shippingAddress": null, "notes": [{"id": "17573524705480x6k8es59", "text": "<PERSON><PERSON> chu", "createdAt": "2025-09-08T17:27:50.548Z", "updatedAt": "2025-09-08T17:32:52.613Z"}], "rechargeInfo": null, "trans_id": "****************", "paymentCallbacks": [{"timestamp": "2025-06-10T12:00:40.557Z", "paymentMethod": "7-eleven", "callbackType": "apn", "requestPayload": {"api_id": "************", "trans_id": "****************", "order_no": "MAG-7608", "amount": 2025, "expire_time": "2025-06-17", "status": "A", "payment_code": 2, "payment_detail": {"pay_route": "", "st_barcode1": null, "st_barcode2": null, "st_barcode3": null, "st_barcode_note": null, "bank_id": "", "virtual_account": null, "atm_note": null, "ibon_shopid": "CCAT", "ibon_code": "************", "ibon_note": "內含", "storeId": "", "ibon_barcode1": null, "ibon_barcode2": null, "ibon_barcode3": null, "bankno": ""}, "memo": "", "create_time": "2025-06-10 19:45:26", "modify_time": "2025-06-10 19:45:26", "nonce": "**********", "checksum": "6e1e0585ba79d2e8ef67fb8b5fe6aa2b", "pay_date": null, "pay_amount": null, "print_invoice": "0", "vehicle_type": "", "vehicle_barcode": "", "donate_invoice": "0", "love_code": "", "invoice_no": "", "invoice_date": "", "random_number": "", "invoice_discount_no": ""}, "responseStatus": 200, "responseBody": "OK", "processingTimeMs": 3, "clientIP": "***************", "userAgent": "unknown", "orderFound": true}]}, {"id": "MAG-3030", "customerId": "**********", "pinEntryInfo": {"phoneNumber": "**********", "requestDate": "2025-06-10T11:50:30.729Z", "status": "requested"}, "createdAt": "2025-06-10T11:50:31.159Z", "updatedAt": "2025-06-10T11:50:42.985Z", "accountInfo": {"fullName": "<PERSON><PERSON><PERSON>", "accountId": "", "email": "<EMAIL>", "phone": "**********", "lastUpdated": "2025-06-10T11:50:42.560Z"}}, {"id": "MAG-67957", "customerId": "**********", "recipientInfo": {"fullName": "<PERSON><PERSON><PERSON>", "phoneNumber": "**********", "email": "<EMAIL>", "address": "55 <PERSON>, Hai Ba Trung", "city": "<PERSON><PERSON>", "district": "<PERSON><PERSON>", "lastUpdated": "2025-06-10T15:53:13.193Z"}, "createdAt": "2025-06-10T15:53:13.650Z", "updatedAt": "2025-06-10T16:00:39.144Z", "deliveryInfo": {"orderId": "MAG-67957", "deliveryMethod": "store", "deliveryDate": "2025-06-11", "notes": "", "location": "yyyy"}, "status": "pending", "paymentStatus": "waiting_payment", "trans_id": "****************", "paymentCallbacks": [{"timestamp": "2025-06-10T16:00:39.144Z", "paymentMethod": "7-eleven", "callbackType": "apn", "requestPayload": {"api_id": "************", "trans_id": "****************", "order_no": "MAG-67957", "amount": 4025, "expire_time": "2025-06-17", "status": "A", "payment_code": 2, "payment_detail": {"pay_route": "", "st_barcode1": null, "st_barcode2": null, "st_barcode3": null, "st_barcode_note": null, "bank_id": "", "virtual_account": null, "atm_note": null, "ibon_shopid": "CCAT", "ibon_code": "************", "ibon_note": "內含", "storeId": "", "ibon_barcode1": null, "ibon_barcode2": null, "ibon_barcode3": null, "bankno": ""}, "memo": "", "create_time": "2025-06-10 23:53:54", "modify_time": "2025-06-10 23:53:54", "nonce": "**********", "checksum": "66d16716ba9b482658a7e3160b421cbb", "pay_date": null, "pay_amount": null, "print_invoice": "0", "vehicle_type": "", "vehicle_barcode": "", "donate_invoice": "0", "love_code": "", "invoice_no": "", "invoice_date": "", "random_number": "", "invoice_discount_no": ""}, "responseStatus": 200, "responseBody": "OK", "processingTimeMs": 0, "clientIP": "**************", "userAgent": "unknown", "orderFound": true}]}, {"id": "MAG-67957", "customerId": "**********", "customerName": "<PERSON><PERSON><PERSON>", "customerEmail": "<EMAIL>", "customerPhone": "**********", "items": [{"name": "OK MẠNG NĂM ", "price": 4000, "quantity": 1, "sku": "taiwan.yearsim.OK.1HUB"}], "paymentMethod": "convenience_store", "paymentSubMethod": "seven_eleven_ibon", "amount": 4000, "fee": 25, "totalAmount": 4025, "currency": "NT$", "status": "pending", "paymentStatus": "not_paid", "createdAt": "2025-06-10T15:53:58.382Z", "updatedAt": "2025-06-10T15:53:58.382Z", "storeId": "magshop", "paymentInfo": {"barcodes": {"barcode1": "1406115D7", "barcode2": "050610QPZMTHY001", "barcode3": "4X0003000004025", "expiresInMinutes": 10080}, "ibonPaymentCode": "************", "ibonShopId": "CCAT", "orderExpireDate": "2025-06-17", "billAmount": 4025, "fee": 0, "paymentType": "IBON_REALTIME", "shortUrl": "https://s.ccat.com.tw/Q/Ij46iMu5"}, "validUntil": "2025-06-17", "isExpired": false, "expiryReason": null, "shippingAddress": null, "notes": null, "rechargeInfo": null}, {"id": "MAG-87106", "customerId": "**********", "customerName": "<PERSON><PERSON><PERSON>", "customerEmail": "<EMAIL>", "customerPhone": "**********", "items": [{"name": "CHUNG HOA 1 NĂM ", "price": 3750, "quantity": 1, "sku": "taiwan.yearsim.CHUNGHOA.xzD3"}], "paymentMethod": "convenience_store", "paymentSubMethod": "family_mart", "amount": 3775, "fee": 0, "totalAmount": 0, "currency": "NT$", "status": "pending", "paymentStatus": "not_paid", "createdAt": "2025-06-10T15:55:04.563Z", "updatedAt": "2025-06-10T15:55:04.563Z", "storeId": "magshop", "paymentInfo": {"paymentCode": "PM11830557", "paymentBarcode": "B191687162,B2624762584923400,B3500029558915661", "paymentExpiry": "2025-06-17", "amount": 3775, "familyMartStore": "FamilyMart Taiwan", "storeCode": "FM95659"}, "validUntil": "2025-06-11T15:55:04.563Z", "isExpired": false, "expiryReason": null, "shippingAddress": null, "notes": null, "rechargeInfo": null}, {"id": "MAG-OFFLINE-1758104140345", "customerId": "**********", "customerInfo": {"name": "THANHSON", "email": "<EMAIL>", "phone": "**********"}, "items": [{"name": "Google Sheets Assignment - SKU: taiwan.prepaid.ok.ycco", "price": 0, "quantity": 1, "sku": "taiwan.prepaid.ok.ycco"}], "paymentMethod": "offline_purchase", "status": "completed", "paymentStatus": "paid", "amount": 0, "currency": "VND", "createdAt": "2025-09-17T10:15:40.345Z", "updatedAt": "2025-09-17T10:15:40.345Z", "metadata": {"type": "google_sheets_assignment", "sheetsId": "1iljoVGTSqNBphCV7ybTi67Z7areRRbf2ji74GSi-lN8", "assignedColumns": [3], "assignedSku": "taiwan.prepaid.ok.ycco"}}, {"id": "MAG-OFFLINE-1758113955877", "customerId": "**********", "customerInfo": {"name": "THANHSON", "email": "<EMAIL>", "phone": "**********"}, "items": [{"name": "Google Sheets Assignment - SKU: taiwan.prepaid.ok.ycco", "price": 0, "quantity": 1, "sku": "taiwan.prepaid.ok.ycco"}], "paymentMethod": "offline_purchase", "status": "completed", "paymentStatus": "paid", "amount": 0, "currency": "VND", "createdAt": "2025-09-17T12:59:15.877Z", "updatedAt": "2025-09-17T12:59:15.877Z", "metadata": {"type": "google_sheets_assignment", "sheetsId": "1iljoVGTSqNBphCV7ybTi67Z7areRRbf2ji74GSi-lN8", "assignedColumns": [3], "assignedSku": "taiwan.prepaid.ok.ycco"}}, {"id": "MAG-OFFLINE-1758113967100", "customerId": "**********", "customerInfo": {"name": "THANHSON", "email": "<EMAIL>", "phone": "**********"}, "items": [{"name": "Google Sheets Assignment - SKU: taiwan.prepaid.ok.ycco", "price": 0, "quantity": 1, "sku": "taiwan.prepaid.ok.ycco"}], "paymentMethod": "offline_purchase", "status": "completed", "paymentStatus": "paid", "amount": 0, "currency": "VND", "createdAt": "2025-09-17T12:59:27.100Z", "updatedAt": "2025-09-17T12:59:27.100Z", "metadata": {"type": "google_sheets_assignment", "sheetsId": "1iljoVGTSqNBphCV7ybTi67Z7areRRbf2ji74GSi-lN8", "assignedColumns": [3, 4], "assignedSku": "taiwan.prepaid.ok.ycco"}}]