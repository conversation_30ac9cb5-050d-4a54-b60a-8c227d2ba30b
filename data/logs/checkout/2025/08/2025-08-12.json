[{"timestamp": "2025-08-12T02:02:07.666Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-08-12T02:02:07.669Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-08-12T02:02:07.669Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-08-12T02:02:07.670Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-08-12T02:02:07.670Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-08-12T02:02:07.670Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}, {"timestamp": "2025-08-12T02:31:46.073Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-08-12T02:31:46.084Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-08-12T02:31:46.084Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-08-12T02:31:46.085Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-08-12T02:31:46.086Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-08-12T02:31:46.086Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}, {"timestamp": "2025-08-12T02:57:57.219Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-08-12T02:57:57.249Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-08-12T02:57:57.250Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-08-12T02:57:57.251Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-08-12T02:57:57.252Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-08-12T02:57:57.253Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}, {"timestamp": "2025-08-12T16:32:56.294Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Initializing Checkout", "data": {"storeId": "magshop"}}}}, {"timestamp": "2025-08-12T16:32:56.295Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Router query params", "data": {"store": "magshop"}}}}, {"timestamp": "2025-08-12T16:32:56.295Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Available affiliate IDs", "data": []}}}, {"timestamp": "2025-08-12T16:32:56.296Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Allowed payment methods", "data": ["cod", "7-11", "7-11-card", "family-mart", "ok-mart", "taiwanbanktransfer", "banktransfer", "vietnambanktransfer"]}}}, {"timestamp": "2025-08-12T16:32:56.296Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "Calculated store total for currency", "data": {"storeId": "magshop", "currency": "VND", "total": 0, "itemCount": 0}}}}, {"timestamp": "2025-08-12T16:32:56.296Z", "event": "CHECKOUT_PROCESS", "data": {"process": "general", "data": {"message": "<PERSON><PERSON><PERSON><PERSON> phần thanh toán đã chọn", "data": {"method": "cod", "componentExists": true, "availableMethods": ["cod", "7-11", "family-mart", "ok-mart"]}}}}]