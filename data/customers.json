[{"id": "**********", "createdAt": "2025-05-15T03:51:44.998Z", "lastUpdated": "2025-06-13T20:31:02.940Z", "personalDetails": {"name": "<PERSON><PERSON><PERSON>", "firstName": "Sơn", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "middleName": "Thanh", "dateOfBirth": "1970-05-06", "gender": "male", "maritalStatus": "married", "occupation": "Software Engineer", "employer": "ABN Asia Software", "taxId": "**********"}, "contactInfo": {"primaryPhone": "**********", "secondaryPhone": "**********", "workPhone": "***********", "email": "<EMAIL>", "alternateEmail": "<EMAIL>", "socialProfiles": {"facebook": "facebook.com/thanhson.nguyen", "linkedin": "linkedin.com/in/thanhson-nguyen"}}, "addresses": [{"street": "55 <PERSON>, Hai Ba Trung", "city": "<PERSON><PERSON>", "district": "<PERSON><PERSON>", "ward": "<PERSON><PERSON>", "postalCode": "100000", "country": "Vietnam", "lastUsed": "2025-06-10T15:53:13.402Z", "createdAt": "2025-06-10T15:53:13.402Z", "isDefault": true, "type": "home"}, {"street": "88 Trung Kinh, Cau Giay", "city": "<PERSON><PERSON>", "district": "Cau G<PERSON>y", "ward": "Trung Hoa", "postalCode": "100000", "country": "Vietnam", "lastUsed": "2025-05-01T10:15:22.460Z", "createdAt": "2025-01-10T09:45:32.123Z", "isDefault": false, "type": "work"}], "citizenship": {"currentCountry": "Vietnam", "nationality": "Vietnamese", "otherNationalities": ["None"], "residencyStatus": "citizen"}, "financialInfo": {"bankAccounts": [{"bankName": "Vietcombank", "accountNumber": "*************", "accountType": "savings", "branch": "Hai Ba Trung", "swiftCode": "BFTVVNVX"}], "taxResidency": "Vietnam", "creditScore": 780}, "familyMembers": [{"relationship": "spouse", "name": "<PERSON><PERSON>", "dateOfBirth": "1990-09-25", "nationalId": "************", "contactNumber": "**********", "sameAddress": true}, {"relationship": "child", "name": "<PERSON>", "dateOfBirth": "2020-12-10", "sameAddress": true}, {"relationship": "parent", "name": "<PERSON>", "dateOfBirth": "1960-03-18", "nationalId": "************", "contactNumber": "**********", "sameAddress": false, "address": {"street": "123 <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "city": "<PERSON><PERSON>", "district": "<PERSON><PERSON>", "country": "Vietnam"}}], "healthInfo": {"bloodType": "O+", "allergies": ["Penicillin"], "insuranceProvider": "Bao Viet Insurance", "insurancePolicyNumber": "BV987654321", "emergencyContact": {"name": "<PERSON><PERSON>", "relationship": "spouse", "phone": "**********"}}, "membershipInfo": {"memberSince": "2025-05-15T03:51:44.998Z", "membershipLevel": "gold", "loyaltyPoints": 2500, "referredBy": null, "referralCode": "SONNGUYEN2025"}, "travelHistory": [{"destination": "Japan", "arrivalDate": "2024-10-15", "departureDate": "2024-10-25", "purpose": "business", "visaNumber": "JP2024789456"}, {"destination": "Singapore", "arrivalDate": "2025-01-05", "departureDate": "2025-01-12", "purpose": "vacation"}], "profileImage": "/uploads/profiles/**********_profile.jpg", "preferences": {"language": "Vietnamese", "currency": "VND", "communicationPreferences": {"email": true, "sms": true, "phone": false, "promotions": true}}, "notes": "", "consent": {"marketingConsent": true, "dataProcessingConsent": true, "consentDate": "2025-05-15T03:52:10.123Z", "privacyPolicyVersion": "v2.3"}, "accessControl": {"accountStatus": "active", "verificationLevel": "full", "lastLogin": "2025-05-18T08:45:12.789Z", "loginHistory": [{"timestamp": "2025-05-18T08:45:12.789Z", "ipAddress": "************", "device": "iPhone 15 Pro", "browser": "Safari"}, {"timestamp": "2025-05-16T14:22:36.412Z", "ipAddress": "************", "device": "MacBook Pro", "browser": "Chrome"}]}, "name": "<PERSON><PERSON><PERSON>", "phone": "**********", "email": "<EMAIL>", "address": "55 <PERSON>, Hai Ba Trung", "city": "<PERSON><PERSON>", "district": "<PERSON><PERSON>", "password": "password123", "updatedAt": "2025-05-29T05:47:00.028Z", "passwordHash": "$2b$10$VJX6g.BBI8owRTSPTP2WW.dfn1gH4cSHTlxViYr82hkyVIWrgnFYu", "accountId": "9999", "documents": [{"documentType": "photo", "filename": "photo_1749554469822_f80333db-4a30-4f13-9f37-c41f80b44871.jpeg", "originalFilename": "_c47d66a2-0867-4794-bee2-6f820bfc798c.jpeg", "uploadDate": "2025-06-10T11:21:09.824Z", "size": 149500, "status": "approved", "path": "documents/customers/**********/photo_1749554469822_f80333db-4a30-4f13-9f37-c41f80b44871.jpeg", "statusUpdatedAt": "2025-06-10T15:51:09.059Z", "statusUpdatedBy": "admin1"}, {"documentType": "idCard", "filename": "idCard_1749554469876_0d3757cf-023f-4012-b14d-2b6701d772fa.jpeg", "originalFilename": "_8bcc5ec5-7a54-40a1-a756-0cee9eac2c1b.jpeg", "uploadDate": "2025-06-10T11:21:09.877Z", "size": 142857, "status": "approved", "path": "documents/customers/**********/idCard_1749554469876_0d3757cf-023f-4012-b14d-2b6701d772fa.jpeg", "statusUpdatedAt": "2025-06-10T15:51:09.940Z", "statusUpdatedBy": "admin1"}]}, {"id": "**********", "name": "THANHSON", "email": "<EMAIL>", "password": "ThanhSon@1", "phone": "**********", "addresses": [], "orders": [], "purchaseHistory": [], "preferredPaymentMethod": "", "createdAt": "2025-06-09T17:42:38.558Z", "updatedAt": "2025-06-09T17:42:38.558Z", "dateOfBirth": "1979-05-06", "gender": "male"}, {"id": "**********", "name": "ROSE", "email": "<EMAIL>", "password": "hP738123", "phone": "**********", "addresses": [], "orders": [], "purchaseHistory": [], "preferredPaymentMethod": "", "createdAt": "2025-08-06T09:54:23.826Z", "updatedAt": "2025-08-06T09:54:23.826Z", "dateOfBirth": "1983-05-25", "gender": "female"}]