{"version": "1.0", "lastUpdated": "2025-01-16T00:00:00.000Z", "description": "Loyalty program rules configuration for MAG Shop", "settings": {"enabled": true, "pointsExpiry": {"enabled": true, "expiryDays": 365, "warningDays": 30}, "minimumRedemption": 100, "maximumRedemptionPerOrder": 5000, "currency": "NT$"}, "customerTiers": {"bronze": {"name": "Đồng", "minPoints": 0, "maxPoints": 999, "multiplier": 1.0, "benefits": ["<PERSON><PERSON><PERSON> đi<PERSON><PERSON> c<PERSON> bản", "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> k<PERSON>n mãi"]}, "silver": {"name": "Bạc", "minPoints": 1000, "maxPoints": 4999, "multiplier": 1.2, "benefits": ["<PERSON><PERSON><PERSON> điểm x1.2", "Ưu tiên hỗ trợ", "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> sinh nh<PERSON>t 5%"]}, "gold": {"name": "<PERSON><PERSON><PERSON>", "minPoints": 5000, "maxPoints": 19999, "multiplier": 1.5, "benefits": ["<PERSON><PERSON><PERSON> điểm x1.5", "<PERSON><PERSON><PERSON> phí vận chuyển", "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> sinh nh<PERSON>t 10%", "<PERSON><PERSON><PERSON> c<PERSON>p sớm sản phẩm mới"]}, "platinum": {"name": "<PERSON><PERSON><PERSON>", "minPoints": 20000, "maxPoints": 99999999, "multiplier": 2.0, "benefits": ["<PERSON><PERSON><PERSON> điểm x2.0", "<PERSON><PERSON><PERSON> phí vận chuyển", "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> sinh nh<PERSON>t 15%", "Hỗ trợ VIP 24/7", "Quà tặng độc quyền"]}}, "earningRules": {"purchase": {"enabled": true, "description": "<PERSON><PERSON><PERSON> điểm từ mua hàng", "rules": [{"id": "base_purchase", "name": "<PERSON><PERSON> <PERSON> c<PERSON> bản", "type": "percentage", "value": 1.0, "description": "1% giá trị đơn hàng", "conditions": {"minAmount": 100, "maxAmount": null, "productCategories": [], "paymentMethods": [], "excludeCategories": ["gift_card", "topup_card"]}}, {"id": "sim_bonus", "name": "Bonus SIM", "type": "fixed", "value": 50, "description": "50 điểm cho mỗi SIM", "conditions": {"productCategories": ["sim", "esim"], "minAmount": 0}}, {"id": "high_value_bonus", "name": "Đơn hàng giá trị cao", "type": "percentage", "value": 2.0, "description": "2% cho đơn hàng trên 5000 NT$", "conditions": {"minAmount": 5000, "excludeCategories": ["gift_card"]}}]}, "referral": {"enabled": true, "description": "<PERSON><PERSON><PERSON> điểm từ giới thiệu", "rules": [{"id": "referral_bonus", "name": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u bạn bè", "type": "fixed", "value": 200, "description": "200 điểm khi bạn bè đặt hàng đầu tiên", "conditions": {"referredCustomerMinPurchase": 500}}]}, "birthday": {"enabled": true, "description": "<PERSON><PERSON><PERSON> đi<PERSON><PERSON> sinh nh<PERSON>t", "rules": [{"id": "birthday_bonus", "name": "<PERSON><PERSON><PERSON> sinh nh<PERSON>t", "type": "fixed", "value": 100, "description": "100 điểm trong tháng sinh nhật", "conditions": {"oncePerYear": true}}]}, "review": {"enabled": true, "description": "<PERSON><PERSON><PERSON> điểm từ đánh giá", "rules": [{"id": "product_review", "name": "<PERSON><PERSON><PERSON> gi<PERSON> sản phẩm", "type": "fixed", "value": 25, "description": "25 điểm cho mỗi đánh giá", "conditions": {"minRating": 3, "minCharacters": 50, "maxPerMonth": 5}}]}}, "redemptionRules": {"discount": {"enabled": true, "description": "<PERSON><PERSON>i điểm lấy giảm giá", "rules": [{"id": "discount_5_percent", "name": "Giảm giá 5%", "pointsCost": 500, "discountType": "percentage", "discountValue": 5, "maxDiscount": 500, "description": "Giảm 5% tối đa 500 NT$", "conditions": {"minOrderAmount": 1000, "maxUsesPerCustomer": 2, "validDays": 30}}, {"id": "discount_10_percent", "name": "Giảm giá 10%", "pointsCost": 1000, "discountType": "percentage", "discountValue": 10, "maxDiscount": 1000, "description": "Giảm 10% tối đa 1000 NT$", "conditions": {"minOrderAmount": 2000, "maxUsesPerCustomer": 1, "validDays": 30}}, {"id": "fixed_discount_100", "name": "Giảm 100 NT$", "pointsCost": 200, "discountType": "fixed", "discountValue": 100, "description": "G<PERSON>ảm cố định 100 NT$", "conditions": {"minOrderAmount": 500, "maxUsesPerCustomer": 5, "validDays": 30}}]}, "gifts": {"enabled": true, "description": "<PERSON><PERSON>i điểm lấy quà tặng", "rules": [{"id": "free_shipping", "name": "<PERSON><PERSON><PERSON> phí vận chuyển", "pointsCost": 150, "giftType": "shipping", "description": "<PERSON><PERSON><PERSON> phí vận chuyển cho đơn hàng tiếp theo", "conditions": {"validDays": 30, "maxUsesPerCustomer": 3}}, {"id": "sim_card_holder", "name": "Hộp đựng SIM", "pointsCost": 300, "giftType": "physical", "description": "<PERSON><PERSON><PERSON> đ<PERSON>ng SIM cao cấp", "conditions": {"stockRequired": true, "maxUsesPerCustomer": 2}}]}}, "specialEvents": {"doublePoints": {"enabled": false, "description": "<PERSON><PERSON> kiện tích điểm x2", "startDate": null, "endDate": null, "multiplier": 2.0, "conditions": {"productCategories": [], "minAmount": 0}}, "bonusWeekend": {"enabled": false, "description": "<PERSON><PERSON><PERSON><PERSON> tuần tích điểm thêm", "daysOfWeek": [6, 0], "bonusPoints": 50, "conditions": {"minAmount": 200}}}, "notifications": {"pointsEarned": {"enabled": true, "template": "Bạn đã nhận được {points} điểm từ đơn hàng #{orderId}. Tổng điểm hiện tại: {totalPoints}"}, "pointsExpiring": {"enabled": true, "template": "{points} điểm của bạn sẽ hết hạn vào {expiryDate}. <PERSON><PERSON><PERSON> sử dụng ngay!"}, "tierUpgrade": {"enabled": true, "template": "Chúc mừng! Bạn đã lên hạng {newTier}. Tận hưởng những ưu đãi mới!"}}}