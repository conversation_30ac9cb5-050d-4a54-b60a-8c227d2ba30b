import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Define public paths that don't require authentication
  const isPublicPath = path === '/customer/login';
  const isAdminLoginPath = path === '/admin/login';

  // Check if the path is under /customer/ but not login
  const isCustomerPath = path.startsWith('/customer/') && !isPublicPath;

  // Get the tokens from cookies
  const customerToken = request.cookies.get('customerToken')?.value || '';

  // Customer routes logic
  if (isPublicPath && customerToken) {
    // If user is already logged in and tries to access login page
    return NextResponse.redirect(new URL('/customer/dashboard', request.url));
  }

  if (isCustomerPath && !customerToken) {
    // If user is not logged in and tries to access protected page
    return NextResponse.redirect(new URL('/customer/login', request.url));
  }

  // Skip admin middleware for now - let client-side handle it

  return NextResponse.next();
}

// Configure which routes to run middleware on
export const config = {
  matcher: [
    '/customer/:path*',
    // Temporarily exclude admin routes to fix flickering
    // '/admin/:path*',
  ],
};