# MAG Shop Database Management Tools

This document describes the database management tools for MAG Shop, which help you maintain, backup, restore, and clean your shop's database.

## Overview

The database management system consists of two main tools:

1. **`scripts/db-management.js`** - Full-featured Node.js script with interactive menu
2. **`db-manage.sh`** - Simple shell wrapper for quick operations

Your shop uses JSON files for data storage:
- `data/orders.json` - Customer orders
- `data/customers.json` - Customer information  
- `data/staff.json` - Staff accounts
- `data/customers/` - Customer documents and files
- `data/carts/` - Shopping cart data
- `data/logs/` - Application logs
- `logs/` - Main application logs

## Quick Start

### Check Database Status
```bash
./db-manage.sh status
```

### Run Full Analysis
```bash
./db-manage.sh analyze
```

### Create Backup
```bash
./db-manage.sh backup full
```

### Interactive Mode
```bash
./db-manage.sh
# or
node scripts/db-management.js
```

## Available Operations

### 📊 Analysis Operations

#### 1. Database Analysis
Provides comprehensive analysis of your database including:
- Order statistics (completed, expired, cancelled, pending)
- Customer statistics (active, inactive, with/without orders)
- Log file analysis
- Storage usage breakdown

#### 2. Quick Statistics
Shows basic counts of orders, customers, and backups.

#### 3. List Backups
Displays all available backups with comprehensive information:
- **Basic Info**: Name, type (full/data/logs), creation date, size
- **📊 Content Statistics**: Record counts (orders, customers, staff, APN logs)
- **📁 Detailed File Listings**: Shows actual files in each backup
  - Data files: orders.json, customers.json, staff.json
  - Cart files: Lists cart files (shows first 5 if many)
  - Log files: Data logs and main application logs (shows first 3 if many)
  - Documents: Document files in backup
  - Callbacks: Callback files including APN logs
  - Customer files: Individual customer data files

### 🧹 Cleanup Operations

All cleanup operations support **dry run mode** - always run a dry run first to see what will be cleaned!

#### 1. Cleanup Expired Orders
- Removes orders with status 'expired' or 'cancelled'
- Default threshold: older than 30 days
- **Configurable in script**

#### 2. Cleanup Test Orders
Removes orders that appear to be test data:
- Email contains 'test' or 'example'
- Phone number is '0000000000'
- Order amount is 0
- Store ID is 'test'

#### 3. Cleanup Orphaned Customers
- Removes customers who have no orders
- Default threshold: inactive for 365 days
- **Configurable in script**

#### 4. Cleanup Old Carts
- Removes cart files older than threshold
- Default threshold: 7 days
- **Configurable in script**
- **Enhanced listing**: Shows files to be cleaned with dates (dry run)

#### 5. Cleanup Old Logs
- Removes log files older than threshold
- Default threshold: 30 days
- Covers all log directories
- **Configurable in script**

#### 6. Cleanup Old APN Logs
- Removes APN callback logs older than threshold
- Default threshold: 90 days (3 months)
- Cleans `data/callbacks/logsapn.json` file
- **Configurable in script**

#### 7. Cleanup Test APN Logs
Removes APN callback logs that appear to be test data:
- Transaction IDs containing 'TEST' or '711apntest'
- Order numbers containing 'TEST' or 'EXPIRED'
- Localhost requests (127.0.0.1)
- Test script user agents

#### 8. Full Cleanup
Runs all cleanup operations in sequence with dry run prompts.

### 💾 Backup & Restore Operations

#### Backup Types

1. **Full Backup**
   - All data files (orders.json, customers.json, staff.json)
   - All directories (customers/, carts/, callbacks/, logs/, documents/)
   - Main application logs
   - **Shows detailed progress**: Lists each file/directory being backed up with sizes

2. **Data Backup**
   - Only core data files (orders.json, customers.json, staff.json)
   - **Shows file details**: Displays file sizes during backup

3. **Logs Backup**  
   - All log files from all log directories
   - **Shows directory contents**: Lists file counts for each log directory

#### Restore Operations
- List available backups
- Select backup by number
- Restore files based on backup type
- Automatic validation of backup integrity

### 🗑️ Wipe Operations (DANGEROUS)

⚠️ **WARNING: These operations permanently delete data!**

All wipe operations require typing 'CONFIRM' to proceed.

1. **Wipe Orders** - Deletes all orders
2. **Wipe Customers** - Deletes all customers  
3. **Wipe Logs** - Deletes all log files
4. **Wipe Carts** - Deletes all cart files
5. **Wipe All Data** - Deletes everything

## Configuration

You can modify thresholds in `scripts/db-management.js`:

```javascript
const CONFIG = {
  // Cleanup thresholds (in days)
  oldOrdersThreshold: 180,      // 6 months
  oldLogsThreshold: 30,         // 1 month
  oldCartsThreshold: 7,         // 1 week
  expiredOrdersThreshold: 30,   // 1 month
  testOrdersThreshold: 7,       // 1 week
  orphanedCustomersThreshold: 365, // 1 year
  oldApnLogsThreshold: 90       // 3 months for APN callback logs
};
```

## Usage Examples

### Daily Maintenance
```bash
# Quick status check
./db-manage.sh status

# Weekly cleanup (dry run first)
./db-manage.sh cleanup all
```

### Before Major Updates
```bash
# Create full backup
./db-manage.sh backup full

# Analyze database
./db-manage.sh analyze
```

### Cleaning Test Data
```bash
# Remove test orders only
./db-manage.sh cleanup test

# Remove expired orders
./db-manage.sh cleanup expired
```

### Storage Management
```bash
# Clean old logs to free space
./db-manage.sh cleanup logs

# Clean old carts
./db-manage.sh cleanup carts
```

### Emergency Restore
```bash
# Run interactive mode
./db-manage.sh

# Select option 13 (Restore from backup)
# Choose backup from list
```

## Shell Wrapper Commands

The `db-manage.sh` script provides quick access to common operations:

```bash
./db-manage.sh help          # Show help
./db-manage.sh status        # Quick status
./db-manage.sh analyze       # Full analysis
./db-manage.sh backup full   # Create backup
./db-manage.sh cleanup all   # Run cleanup (with prompts)
```

## Interactive Menu

The full interactive menu (`node scripts/db-management.js`) provides:

```
🗄️  MAG Shop Database Management Tool
=====================================

📊 ANALYSIS
  1. Analyze database
  2. Show database statistics
  3. List backups

🧹 CLEANUP  
  4. Cleanup expired orders
  5. Cleanup test orders
  6. Cleanup orphaned customers  
  7. Cleanup old carts
  8. Cleanup old logs
  9. Cleanup old APN logs
  10. Cleanup test APN logs
  11. Full cleanup (all above)

💾 BACKUP & RESTORE
  12. Create full backup
  13. Create data backup  
  14. Create logs backup
  15. Restore from backup

🗑️  WIPE (DANGEROUS)
  16. Wipe orders
  17. Wipe customers
  18. Wipe logs
  19. Wipe carts
  20. Wipe all data

  0. Exit
```

## Best Practices

### Regular Maintenance
1. **Daily**: Check status and run analysis
2. **Weekly**: Run full cleanup (with dry run first)
3. **Monthly**: Create full backup before major operations
4. **Before updates**: Always backup data

### Safety Guidelines
1. **Always run dry run first** for cleanup operations
2. **Create backups** before major changes
3. **Test restore process** periodically
4. **Monitor storage usage** regularly
5. **Keep multiple backup generations**

### Troubleshooting

#### Script Won't Run
- Check Node.js is installed: `node --version`
- Verify script permissions: `ls -la scripts/db-management.js`
- Check file paths in error messages

#### Backup Fails
- Verify disk space available
- Check write permissions on backup directory
- Ensure source files exist and are readable

#### Restore Issues
- Verify backup integrity (check manifest.json)
- Ensure target directories exist
- Check file permissions

#### Large Database Performance
- Run operations during low-traffic periods
- Use specific cleanup types instead of "full cleanup"
- Monitor system resources during operations

## Security Notes

- Database files contain sensitive customer information
- Backup files should be stored securely
- Log files may contain sensitive data
- Consider encrypting backups for production use
- Wipe operations are irreversible - use with extreme caution

## Monitoring and Alerts

Consider setting up monitoring for:
- Database file sizes
- Log file growth
- Backup success/failure
- Cleanup operation results
- Storage space usage

## Automated Maintenance

### Setting Up Cron Jobs

For automated maintenance, you can use the provided cron example script:

```bash
# Copy and customize the example
cp scripts/cron-example.sh scripts/db-maintenance-cron.sh

# Edit the SHOP_DIR path in the script
nano scripts/db-maintenance-cron.sh

# Make it executable  
chmod +x scripts/db-maintenance-cron.sh

# Test the script manually first
./scripts/db-maintenance-cron.sh
```

### Example Cron Schedule

Add to your crontab (`crontab -e`):

```bash
# Daily maintenance at 2 AM
0 2 * * * /path/to/your/shop/scripts/db-maintenance-cron.sh

# Alternative: Run every 6 hours
0 */6 * * * /path/to/your/shop/scripts/db-maintenance-cron.sh
```

### Automated Tasks Include:

- **Daily**: Data backup, log cleanup, cart cleanup
- **Weekly**: Expired orders cleanup (Sundays)  
- **Monthly**: Orphaned customers cleanup, full backup (1st of month)
- **Continuous**: Old backup cleanup, disk space monitoring

### Monitoring Automated Tasks

Check maintenance logs:
```bash
tail -f logs/db-maintenance-$(date +%Y%m%d).log
```

View recent maintenance activity:
```bash
ls -la logs/db-maintenance-*.log
```

## Support

For issues or questions:
1. Check this documentation
2. Review error logs
3. Test with dry run mode first
4. Create backup before making changes

## Version History

- v1.0 - Initial release with full feature set
- Support for JSON-based database
- Interactive and command-line interfaces
- Comprehensive backup and restore capabilities
- Automated maintenance via cron jobs 