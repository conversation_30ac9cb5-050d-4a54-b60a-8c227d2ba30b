// Test the alt customer endpoint
const axios = require('axios');

const API_KEY = 'uX6HsVoPhmapndxrUhDn';
const BASE_URL = 'http://localhost:3000';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': API_KEY
  }
});

async function testAltEndpoint() {
  console.log('Testing alternative customer endpoint');
  
  try {
    // Try to get a customer with ID CUST001
    const customerId = 'CUST001';
    console.log(`Fetching customer with ID: ${customerId}`);
    
    const response = await api.get(`/api/customer?id=${customerId}`);
    
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response data:', error.response.data);
    }
  }
}

testAltEndpoint().catch(console.error); 