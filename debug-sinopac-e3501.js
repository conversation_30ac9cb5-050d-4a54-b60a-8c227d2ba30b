// Debug Script for SinoPAC E3501 Error
// Run this with: node debug-sinopac-e3501.js

const axios = require('axios');

// Test different payload combinations to isolate E3501 cause
const testCases = [
  {
    name: 'Original failing case',
    payload: {
      OrderNo: 'MAG-13677',
      Amount: '3750',
      PrdtName: 'CHUNG HOA 1 NĂM '
    }
  },
  {
    name: 'Cleaned product name',
    payload: {
      OrderNo: 'MAG-13677',
      Amount: '3750',
      PrdtName: 'CHUNG HOA 1 NAM'
    }
  },
  {
    name: 'Simple ASCII only',
    payload: {
      OrderNo: 'TEST-001',
      Amount: '1000',
      PrdtName: 'TEST PRODUCT'
    }
  },
  {
    name: 'Minimal payload',
    payload: {
      OrderNo: 'MIN-001',
      Amount: '100',
      PrdtName: 'PRODUCT'
    }
  },
  {
    name: 'Different amount format',
    payload: {
      OrderNo: 'AMT-001',
      Amount: 1000, // Number instead of string
      PrdtName: 'TEST'
    }
  }
];

async function testSinoPAC(testCase) {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log('Payload:', JSON.stringify(testCase.payload, null, 2));
  
  try {
    const response = await axios.post('http://localhost:3000/api/payment/sinopac/qrcode', testCase.payload, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log(`✅ SUCCESS:`, response.data);
    
  } catch (error) {
    console.log(`❌ FAILED:`, error.response?.data || error.message);
    
    if (error.response?.data?.Result?.OriginalError) {
      console.log(`📋 Original API Error:`, error.response.data.Result.OriginalError);
    }
  }
}

async function runAllTests() {
  console.log('🚀 Starting SinoPAC E3501 Debug Tests');
  console.log('======================================');
  
  for (const testCase of testCases) {
    await testCase(testCase);
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between tests
  }
  
  console.log('\n✅ All tests completed!');
  console.log('\n💡 DEBUGGING TIPS:');
  console.log('1. Check which test cases succeed vs fail');
  console.log('2. Compare the server logs for each test');
  console.log('3. Look for patterns in the failures');
  console.log('4. Check if callback URL is accessible');
  console.log('5. Verify SinoPAC credentials are still valid');
}

// Additional manual test function
async function manualTest() {
  console.log('\n🔧 MANUAL TEST');
  console.log('===============');
  
  const customPayload = {
    OrderNo: `DEBUG-${Date.now()}`,
    Amount: '500',
    PrdtName: 'MANUAL TEST'
  };
  
  console.log('Testing with:', customPayload);
  await testSinoPAC({ name: 'Manual Test', payload: customPayload });
}

// Run tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { testSinoPAC, runAllTests, manualTest }; 