/**
 * Checkout Flow Engine Service
 * 
 * Manages checkout flow progression, step validation, and business logic
 * Provides a centralized way to handle different product checkout flows
 */

import { ProductTypeManager } from './ProductTypeManager';
import { CurrencyManager } from './CurrencyManager';
import { PaymentRegistry } from './PaymentRegistry';
import { logCheckoutEvent, logCheckoutError } from '../utils/checkoutLogger';

export class CheckoutFlowEngine {
  
  /**
   * Checkout step definitions
   */
  static CHECKOUT_STEPS = {
    CART_REVIEW: 'cart-review',
    PRODUCT_CONFIG: 'product-config',
    CUSTOMER_INFO: 'customer-info',
    DELIVERY_ADDRESS: 'delivery-address',
    PAYMENT_METHOD: 'payment-method',
    PAYMENT_DETAILS: 'payment-details',
    ORDER_REVIEW: 'order-review',
    CONFIRMATION: 'confirmation'
  };

  /**
   * Step validation rules
   */
  static STEP_VALIDATION = {
    [this.CHECKOUT_STEPS.CART_REVIEW]: {
      required: ['cartItems'],
      validate: (data) => data.cartItems && data.cartItems.length > 0
    },
    [this.CHECKOUT_STEPS.PRODUCT_CONFIG]: {
      required: ['cartItems', 'productConfigurations'],
      validate: (data) => data.cartItems.every(item => item.configuration || !item.requiresConfiguration)
    },
    [this.CHECKOUT_STEPS.CUSTOMER_INFO]: {
      required: ['customerInfo'],
      validate: (data) => {
        const { name, email, phone } = data.customerInfo || {};
        return name && email && phone && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      }
    },
    [this.CHECKOUT_STEPS.DELIVERY_ADDRESS]: {
      required: ['deliveryAddress'],
      validate: (data) => {
        if (!ProductTypeManager.requiresAddress(data.productType)) return true;
        const { address, city } = data.deliveryAddress || {};
        return address && city;
      }
    },
    [this.CHECKOUT_STEPS.PAYMENT_METHOD]: {
      required: ['selectedPaymentMethod', 'selectedCurrency'],
      validate: (data) => {
        return PaymentRegistry.isMethodAvailable(data.selectedPaymentMethod, data.selectedCurrency);
      }
    },
    [this.CHECKOUT_STEPS.PAYMENT_DETAILS]: {
      required: ['paymentData'],
      validate: (data) => data.paymentData && data.paymentData.isValid
    },
    [this.CHECKOUT_STEPS.ORDER_REVIEW]: {
      required: ['orderData'],
      validate: (data) => data.orderData && data.orderData.total > 0
    }
  };

  /**
   * Get checkout flow configuration for product type
   * @param {string} productType - Product type
   * @returns {Object} Flow configuration
   */
  static getFlowForProductType(productType) {
    const baseFlow = ProductTypeManager.getCheckoutFlow(productType);
    
    // Map product type steps to our detailed steps
    const stepMapping = {
      'cart-review': this.CHECKOUT_STEPS.CART_REVIEW,
      'product-config': this.CHECKOUT_STEPS.PRODUCT_CONFIG,
      'customer-info': this.CHECKOUT_STEPS.CUSTOMER_INFO,  
      'address': this.CHECKOUT_STEPS.DELIVERY_ADDRESS,
      'payment': this.CHECKOUT_STEPS.PAYMENT_METHOD,
      'payment-details': this.CHECKOUT_STEPS.PAYMENT_DETAILS,
      'order-review': this.CHECKOUT_STEPS.ORDER_REVIEW,
      'confirmation': this.CHECKOUT_STEPS.CONFIRMATION
    };

    const detailedSteps = [];
    
    // Always start with cart review
    detailedSteps.push(this.CHECKOUT_STEPS.CART_REVIEW);
    
    // Add product configuration if required
    if (baseFlow.requiresConfiguration) {
      detailedSteps.push(this.CHECKOUT_STEPS.PRODUCT_CONFIG);
    }
    
    // Always need customer info
    detailedSteps.push(this.CHECKOUT_STEPS.CUSTOMER_INFO);
    
    // Add address if required
    if (baseFlow.requiresAddress) {
      detailedSteps.push(this.CHECKOUT_STEPS.DELIVERY_ADDRESS);
    }
    
    // Payment steps
    detailedSteps.push(this.CHECKOUT_STEPS.PAYMENT_METHOD);
    detailedSteps.push(this.CHECKOUT_STEPS.PAYMENT_DETAILS);
    detailedSteps.push(this.CHECKOUT_STEPS.ORDER_REVIEW);
    detailedSteps.push(this.CHECKOUT_STEPS.CONFIRMATION);

    return {
      ...baseFlow,
      steps: detailedSteps,
      currentStep: detailedSteps[0],
      totalSteps: detailedSteps.length
    };
  }

  /**
   * Advance to next step with validation
   * @param {string} currentStep - Current checkout step
   * @param {Object} checkoutData - Current checkout data
   * @param {Array} flowSteps - Array of flow steps
   * @returns {Object} Step advancement result
   */
  static advanceStep(currentStep, checkoutData, flowSteps) {
    try {
      // Validate current step
      const validation = this.validateStep(currentStep, checkoutData);
      if (!validation.isValid) {
        return {
          success: false,
          currentStep,
          nextStep: null,
          errors: validation.errors,
          reason: 'Current step validation failed'
        };
      }

      // Find next step
      const currentIndex = flowSteps.indexOf(currentStep);
      if (currentIndex === -1) {
        return {
          success: false,
          currentStep,
          nextStep: null,
          errors: [`Invalid current step: ${currentStep}`],
          reason: 'Current step not found in flow'
        };
      }

      // Check if already at last step
      if (currentIndex >= flowSteps.length - 1) {
        return {
          success: false,
          currentStep,
          nextStep: null,
          errors: ['Already at final step'],
          reason: 'Cannot advance past final step'
        };
      }

      const nextStep = flowSteps[currentIndex + 1];
      
      logCheckoutEvent('step_advanced', {
        from: currentStep,
        to: nextStep,
        progress: `${currentIndex + 2}/${flowSteps.length}`
      });

      return {
        success: true,
        currentStep: nextStep,
        nextStep,
        errors: [],
        progress: {
          current: currentIndex + 2,
          total: flowSteps.length,
          percentage: ((currentIndex + 2) / flowSteps.length) * 100
        }
      };

    } catch (error) {
      logCheckoutError('step_advancement_failed', error, {
        currentStep,
        flowSteps
      });

      return {
        success: false,
        currentStep,
        nextStep: null,
        errors: [error.message],
        reason: 'Step advancement error'
      };
    }
  }

  /**
   * Go back to previous step
   * @param {string} currentStep - Current checkout step
   * @param {Array} flowSteps - Array of flow steps
   * @returns {Object} Step retreat result
   */
  static previousStep(currentStep, flowSteps) {
    const currentIndex = flowSteps.indexOf(currentStep);
    
    if (currentIndex <= 0) {
      return {
        success: false,
        currentStep,
        previousStep: null,
        reason: 'Already at first step'
      };
    }

    const previousStep = flowSteps[currentIndex - 1];
    
    logCheckoutEvent('step_retreated', {
      from: currentStep,
      to: previousStep
    });

    return {
      success: true,
      currentStep: previousStep,
      previousStep,
      progress: {
        current: currentIndex,
        total: flowSteps.length,
        percentage: (currentIndex / flowSteps.length) * 100
      }
    };
  }

  /**
   * Validate specific checkout step
   * @param {string} step - Step to validate
   * @param {Object} checkoutData - Checkout data
   * @returns {Object} Validation result
   */
  static validateStep(step, checkoutData) {
    const result = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      const stepConfig = this.STEP_VALIDATION[step];
      if (!stepConfig) {
        result.errors.push(`No validation configuration found for step: ${step}`);
        result.isValid = false;
        return result;
      }

      // Check required fields
      for (const field of stepConfig.required) {
        if (!checkoutData[field]) {
          result.errors.push(`Missing required field for ${step}: ${field}`);
        }
      }

      // Run custom validation
      if (stepConfig.validate && !stepConfig.validate(checkoutData)) {
        result.errors.push(`Step validation failed for: ${step}`);
      }

      result.isValid = result.errors.length === 0;

    } catch (error) {
      logCheckoutError('step_validation_error', error, { step });
      result.errors.push(`Validation error: ${error.message}`);
      result.isValid = false;
    }

    return result;
  }

  /**
   * Validate entire checkout flow
   * @param {Object} checkoutData - Complete checkout data
   * @param {Array} flowSteps - Flow steps to validate
   * @returns {Object} Full flow validation result
   */
  static validateFullFlow(checkoutData, flowSteps) {
    const result = {
      isValid: true,
      stepResults: {},
      errors: [],
      completionPercentage: 0
    };

    let validSteps = 0;

    for (const step of flowSteps) {
      const stepResult = this.validateStep(step, checkoutData);
      result.stepResults[step] = stepResult;
      
      if (stepResult.isValid) {
        validSteps++;
      } else {
        result.errors.push(`Step ${step}: ${stepResult.errors.join(', ')}`);
      }
    }

    result.completionPercentage = (validSteps / flowSteps.length) * 100;
    result.isValid = validSteps === flowSteps.length;

    return result;
  }

  /**
   * Get step display information
   * @param {string} step - Step identifier
   * @returns {Object} Step display configuration
   */
  static getStepDisplayInfo(step) {
    const stepInfo = {
      [this.CHECKOUT_STEPS.CART_REVIEW]: {
        title: 'Review Cart',
        description: 'Review your selected items',
        icon: '🛒',
        canSkip: false
      },
      [this.CHECKOUT_STEPS.PRODUCT_CONFIG]: {
        title: 'Configure Products',
        description: 'Set up your product options',
        icon: '⚙️',
        canSkip: false
      },
      [this.CHECKOUT_STEPS.CUSTOMER_INFO]: {
        title: 'Customer Information',
        description: 'Provide your contact details',
        icon: '👤',
        canSkip: false
      },
      [this.CHECKOUT_STEPS.DELIVERY_ADDRESS]: {
        title: 'Delivery Address',
        description: 'Where should we deliver?',
        icon: '📍',
        canSkip: false
      },
      [this.CHECKOUT_STEPS.PAYMENT_METHOD]: {
        title: 'Payment Method',
        description: 'Choose how to pay',
        icon: '💳',
        canSkip: false
      },
      [this.CHECKOUT_STEPS.PAYMENT_DETAILS]: {
        title: 'Payment Details',
        description: 'Complete payment information',
        icon: '💰',
        canSkip: false
      },
      [this.CHECKOUT_STEPS.ORDER_REVIEW]: {
        title: 'Review Order',
        description: 'Final review before placing order',
        icon: '📋',
        canSkip: false
      },
      [this.CHECKOUT_STEPS.CONFIRMATION]: {
        title: 'Confirmation',
        description: 'Order confirmation and next steps',
        icon: '✅',
        canSkip: true
      }
    };

    return stepInfo[step] || {
      title: step,
      description: 'Checkout step',
      icon: '❓',
      canSkip: false
    };
  }

  /**
   * Calculate estimated completion time
   * @param {string} currentStep - Current step
   * @param {Array} flowSteps - Flow steps
   * @returns {Object} Time estimation
   */
  static estimateCompletionTime(currentStep, flowSteps) {
    // Average time per step in minutes
    const stepTimes = {
      [this.CHECKOUT_STEPS.CART_REVIEW]: 2,
      [this.CHECKOUT_STEPS.PRODUCT_CONFIG]: 5,
      [this.CHECKOUT_STEPS.CUSTOMER_INFO]: 3,
      [this.CHECKOUT_STEPS.DELIVERY_ADDRESS]: 2,
      [this.CHECKOUT_STEPS.PAYMENT_METHOD]: 1,
      [this.CHECKOUT_STEPS.PAYMENT_DETAILS]: 3,
      [this.CHECKOUT_STEPS.ORDER_REVIEW]: 2,
      [this.CHECKOUT_STEPS.CONFIRMATION]: 1
    };

    const currentIndex = flowSteps.indexOf(currentStep);
    const remainingSteps = flowSteps.slice(currentIndex + 1);
    
    const estimatedMinutes = remainingSteps.reduce((total, step) => {
      return total + (stepTimes[step] || 2);
    }, 0);

    return {
      remainingSteps: remainingSteps.length,
      estimatedMinutes,
      estimatedRange: `${Math.max(1, estimatedMinutes - 2)}-${estimatedMinutes + 3} minutes`
    };
  }

  /**
   * Check if step can be skipped
   * @param {string} step - Step to check
   * @param {Object} checkoutData - Checkout data
   * @returns {boolean} True if step can be skipped
   */
  static canSkipStep(step, checkoutData) {
    const stepInfo = this.getStepDisplayInfo(step);
    if (!stepInfo.canSkip) return false;

    // Additional logic for conditional skipping
    switch (step) {
      case this.CHECKOUT_STEPS.DELIVERY_ADDRESS:
        return !ProductTypeManager.requiresAddress(checkoutData.productType);
      case this.CHECKOUT_STEPS.PRODUCT_CONFIG:
        return !ProductTypeManager.requiresConfiguration(checkoutData.productType);
      default:
        return stepInfo.canSkip;
    }
  }
}

export default CheckoutFlowEngine;