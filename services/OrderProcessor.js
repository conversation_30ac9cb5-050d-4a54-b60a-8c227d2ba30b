/**
 * Order Processing Service
 * 
 * Centralized order validation, processing, and status management
 * Extracted from checkout.js to provide a clean service interface
 */

import axios from 'axios';
import { 
  logCheckoutEvent, 
  logPaymentApiCall, 
  logPaymentApiResponse,
  logTransaction, 
  logCheckoutError 
} from '../utils/checkoutLogger';

export class OrderProcessor {
  
  /**
   * Order validation rules
   */
  static VALIDATION_RULES = {
    required: ['items', 'currency', 'paymentMethod', 'customer'],
    itemFields: ['sku', 'name', 'price', 'quantity'],
    customerFields: ['name', 'email', 'phone'],
    maxItems: 50,
    maxNameLength: 100,
    maxDescriptionLength: 500
  };

  /**
   * Order statuses
   */
  static ORDER_STATUS = {
    PENDING: 'pending',
    PROCESSING: 'processing', 
    PAYMENT_PENDING: 'payment_pending',
    PAYMENT_CONFIRMED: 'payment_confirmed',
    PAYMENT_FAILED: 'payment_failed',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled',
    REFUNDED: 'refunded'
  };

  /**
   * Validate order data before processing
   * @param {Object} orderData - Order data to validate
   * @returns {Object} Validation result with errors
   */
  static validateOrder(orderData) {
    const result = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // Check required fields
      for (const field of this.VALIDATION_RULES.required) {
        if (!orderData[field]) {
          result.errors.push(`Missing required field: ${field}`);
        }
      }

      // Validate items
      if (orderData.items) {
        this._validateOrderItems(orderData.items, result);
      }

      // Validate customer information
      if (orderData.customer) {
        this._validateCustomerInfo(orderData.customer, result);
      }

      // Validate payment method
      if (orderData.paymentMethod) {
        this._validatePaymentMethod(orderData.paymentMethod, orderData.currency, result);
      }

      // Validate totals
      if (orderData.items && orderData.total !== undefined) {
        this._validateOrderTotals(orderData.items, orderData.total, result);
      }

      result.isValid = result.errors.length === 0;

      logCheckoutEvent('order_validation', {
        orderId: orderData.orderId,
        isValid: result.isValid,
        errorCount: result.errors.length,
        warningCount: result.warnings.length
      });

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Validation error: ${error.message}`);
      
      logCheckoutError('order_validation_exception', error, {
        orderId: orderData.orderId
      });
    }

    return result;
  }

  /**
   * Process and save order
   * @param {Object} orderData - Complete order data
   * @returns {Promise<Object>} Processing result
   */
  static async processOrder(orderData) {
    const processingStart = Date.now();
    
    try {
      logCheckoutEvent('order_processing_start', {
        orderId: orderData.orderId,
        itemCount: orderData.items?.length,
        currency: orderData.currency,
        paymentMethod: orderData.paymentMethod
      });

      // Step 1: Validate order
      const validation = this.validateOrder(orderData);
      if (!validation.isValid) {
        throw new Error(`Order validation failed: ${validation.errors.join(', ')}`);
      }

      // Step 2: Assign order ID if not present
      if (!orderData.orderId) {
        orderData.orderId = this._generateOrderId();
      }

      // Step 3: Set initial order status
      orderData.status = this.ORDER_STATUS.PROCESSING;
      orderData.createdAt = new Date().toISOString();
      orderData.updatedAt = orderData.createdAt;

      // Step 4: Save to central storage
      const saveResult = await this._saveOrderToCentral(orderData);

      // Step 5: Save locally for backup
      await this._saveOrderLocally(orderData);

      // Step 6: Log transaction
      logTransaction('order_created', {
        orderId: orderData.orderId,
        customerId: orderData.customer?.customerId,
        total: orderData.total,
        currency: orderData.currency,
        itemCount: orderData.items.length,
        processingTime: Date.now() - processingStart
      });

      return {
        success: true,
        orderId: orderData.orderId,
        status: orderData.status,
        saveResult,
        processingTime: Date.now() - processingStart
      };

    } catch (error) {
      logCheckoutError('order_processing_failed', error, {
        orderId: orderData.orderId,
        processingTime: Date.now() - processingStart
      });

      return {
        success: false,
        error: error.message,
        orderId: orderData.orderId,
        processingTime: Date.now() - processingStart
      };
    }
  }

  /**
   * Update order status
   * @param {string} orderId - Order ID
   * @param {string} newStatus - New status
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Update result
   */
  static async updateOrderStatus(orderId, newStatus, metadata = {}) {
    try {
      // Validate status
      if (!Object.values(this.ORDER_STATUS).includes(newStatus)) {
        throw new Error(`Invalid order status: ${newStatus}`);
      }

      const updateData = {
        orderId,
        status: newStatus,
        updatedAt: new Date().toISOString(),
        ...metadata
      };

      // Call API to update status
      const response = await axios.post('/api/orders/update-payment-status', updateData);

      logCheckoutEvent('order_status_updated', {
        orderId,
        oldStatus: metadata.oldStatus,
        newStatus,
        success: true
      });

      return {
        success: true,
        orderId,
        status: newStatus,
        response: response.data
      };

    } catch (error) {
      logCheckoutError('order_status_update_failed', error, {
        orderId,
        targetStatus: newStatus
      });

      return {
        success: false,
        orderId,
        error: error.message
      };
    }
  }

  /**
   * Process payment for order
   * @param {Object} paymentData - Payment processing data
   * @returns {Promise<Object>} Payment result
   */
  static async processPayment(paymentData) {
    const paymentStart = Date.now();
    
    try {
      logPaymentApiCall(paymentData.paymentMethod, 'payment_initiation', paymentData);

      let paymentResult;

      // Route to appropriate payment processor
      switch (paymentData.paymentMethod) {
        case '7-11':
          paymentResult = await this._process711Payment(paymentData);
          break;
        case '7-11-card':
          paymentResult = await this._process711CardPayment(paymentData);
          break;
        case 'family-mart':
          paymentResult = await this._processFamilyMartPayment(paymentData);
          break;
        case 'cod':
          paymentResult = await this._processCODPayment(paymentData);
          break;
        default:
          throw new Error(`Unsupported payment method: ${paymentData.paymentMethod}`);
      }

      // Update order status based on payment result
      if (paymentResult.success) {
        await this.updateOrderStatus(paymentData.orderId, this.ORDER_STATUS.PAYMENT_PENDING, {
          paymentId: paymentResult.paymentId,
          paymentMethod: paymentData.paymentMethod
        });
      } else {
        await this.updateOrderStatus(paymentData.orderId, this.ORDER_STATUS.PAYMENT_FAILED, {
          paymentError: paymentResult.error,
          paymentMethod: paymentData.paymentMethod
        });
      }

      logPaymentApiResponse(paymentData.paymentMethod, 'payment_completion', paymentResult);

      return {
        ...paymentResult,
        processingTime: Date.now() - paymentStart
      };

    } catch (error) {
      logCheckoutError('payment_processing_failed', error, {
        orderId: paymentData.orderId,
        paymentMethod: paymentData.paymentMethod,
        processingTime: Date.now() - paymentStart
      });

      return {
        success: false,
        error: error.message,
        orderId: paymentData.orderId,
        processingTime: Date.now() - paymentStart
      };
    }
  }

  /**
   * Get order by ID
   * @param {string} orderId - Order ID
   * @returns {Promise<Object|null>} Order data or null
   */
  static async getOrder(orderId) {
    try {
      const response = await axios.get(`/api/orders/${orderId}`);
      return response.data;
    } catch (error) {
      logCheckoutError('get_order_failed', error, { orderId });
      return null;
    }
  }

  /**
   * Private validation methods
   */

  static _validateOrderItems(items, result) {
    if (!Array.isArray(items)) {
      result.errors.push('Items must be an array');
      return;
    }

    if (items.length === 0) {
      result.errors.push('Order must contain at least one item');
      return;
    }

    if (items.length > this.VALIDATION_RULES.maxItems) {
      result.errors.push(`Too many items (max: ${this.VALIDATION_RULES.maxItems})`);
    }

    items.forEach((item, index) => {
      for (const field of this.VALIDATION_RULES.itemFields) {
        if (!item[field]) {
          result.errors.push(`Item ${index + 1} missing field: ${field}`);
        }
      }

      // Validate price and quantity
      if (item.price && (isNaN(item.price) || item.price < 0)) {
        result.errors.push(`Item ${index + 1} has invalid price`);
      }

      if (item.quantity && (isNaN(item.quantity) || item.quantity < 1)) {
        result.errors.push(`Item ${index + 1} has invalid quantity`);
      }
    });
  }

  static _validateCustomerInfo(customer, result) {
    for (const field of this.VALIDATION_RULES.customerFields) {
      if (!customer[field]) {
        result.errors.push(`Customer missing field: ${field}`);
      }
    }

    // Validate email format
    if (customer.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer.email)) {
      result.errors.push('Invalid email format');
    }

    // Validate phone format (basic)
    if (customer.phone && !/^[\d\s\-\+\(\)]{8,}$/.test(customer.phone)) {
      result.warnings.push('Phone number format may be invalid');
    }
  }

  static _validatePaymentMethod(paymentMethod, currency, result) {
    // This would integrate with PaymentRegistry for validation
    if (!paymentMethod) {
      result.errors.push('Payment method is required');
    }
    // Additional payment method validation would go here
  }

  static _validateOrderTotals(items, expectedTotal, result) {
    const calculatedTotal = items.reduce((total, item) => {
      return total + (parseFloat(item.price) * parseInt(item.quantity));
    }, 0);

    const difference = Math.abs(calculatedTotal - expectedTotal);
    if (difference > 0.01) { // Allow small floating point differences
      result.errors.push(`Total mismatch: expected ${expectedTotal}, calculated ${calculatedTotal}`);
    }
  }

  /**
   * Private processing methods
   */

  static _generateOrderId() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `MAG-${timestamp}-${random}`;
  }

  static async _saveOrderToCentral(orderData) {
    const response = await axios.post('/api/order/save-to-central', orderData);
    return response.data;
  }

  static async _saveOrderLocally(orderData) {
    // Save to local storage as backup
    if (typeof window !== 'undefined' && window.localStorage) {
      const existingOrders = JSON.parse(localStorage.getItem('orders') || '[]');
      existingOrders.push(orderData);
      localStorage.setItem('orders', JSON.stringify(existingOrders));
    }
  }

  static async _process711Payment(paymentData) {
    const response = await axios.post('/api/payment/create-7-11-payment', paymentData);
    return response.data;
  }

  static async _process711CardPayment(paymentData) {
    const response = await axios.post('/api/payment/create-7-11-card-payment', paymentData);
    return response.data;
  }

  static async _processFamilyMartPayment(paymentData) {
    const response = await axios.post('/api/payment/create-family-mart-payment', paymentData);
    return response.data;
  }

  static async _processCODPayment(paymentData) {
    // COD doesn't require immediate payment processing
    return {
      success: true,
      paymentId: `COD-${Date.now()}`,
      message: 'Cash on delivery order created successfully'
    };
  }
}

export default OrderProcessor;