/**
 * Payment Method Registry Service
 * 
 * Centralized payment method management and configuration
 * Consolidates paymentFormMapping.js and payment method logic from checkout.js
 */

import { FaMoneyBill, FaWallet, FaCreditCard, FaCcVisa } from "react-icons/fa";
import PaymentFormCOD from "../components/PaymentFormCOD";
import MasterPaymentForms from "../components/taiwan/payment/MasterPaymentForms";

export class PaymentRegistry {
  
  /**
   * Payment methods configuration by currency
   */
  static PAYMENT_METHODS_BY_CURRENCY = {
    'VND': [
      { 
        id: 'cod', 
        name: '<PERSON><PERSON> to<PERSON> khi nhận hàng', 
        icon: <FaMoneyBill className="text-green-600" />, 
        bgColor: 'bg-green-50', 
        available: true,
        component: PaymentFormCOD,
        displayName: 'Cash on Delivery',
        currencies: ['VND']
      },
      { 
        id: 'bank', 
        name: '<PERSON><PERSON><PERSON><PERSON> khoản', 
        icon: <FaWallet className="text-gray-600" />, 
        bgColor: 'bg-gray-50', 
        available: true 
      },
      { 
        id: 'momo', 
        name: '<PERSON><PERSON><PERSON>', 
        icon: '💰', 
        bgColor: 'bg-pink-50', 
        available: true 
      },
      { 
        id: 'vietnambanktransfer', 
        name: 'Chuyển khoản ngân hàng VN', 
        icon: <FaWallet className="text-blue-600" />, 
        bgColor: 'bg-blue-50', 
        available: true 
      },
      { 
        id: 'banktransfer', 
        name: 'Chuyển khoản quốc tế', 
        icon: <FaWallet className="text-purple-600" />, 
        bgColor: 'bg-purple-50', 
        available: true 
      }
    ],
    'NT$': [
      { 
        id: '7-11', 
        name: '7-Eleven (iBON)', 
        icon: '🏪', 
        bgColor: 'bg-green-100', 
        available: true,
        component: MasterPaymentForms,
        displayName: '7-11 iBON',
        currencies: ['NT$', 'NT', 'NTD', 'TWD']
      },
      { 
        id: '7-11-card', 
        name: '7-Eleven Card', 
        icon: '💳', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms,
        displayName: '7-11 Card Payment',
        currencies: ['NT$', 'NT', 'NTD', 'TWD']
      },
      { 
        id: 'family-mart', 
        name: 'FamilyMart', 
        icon: '🛒', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms,
        displayName: 'Family Mart Payment',
        currencies: ['NT$', 'NT', 'NTD', 'TWD']
      },
      { 
        id: 'taiwanbanktransfer', 
        name: 'Sinopac Dynamic QR Code', 
        icon: <FaWallet className="text-blue-600" />, 
        bgColor: 'bg-blue-50', 
        available: true 
      }
    ],
    'NT': [
      { 
        id: 'taiwanbanktransfer', 
        name: 'Chuyển khoản ngân hàng', 
        icon: <FaWallet className="text-blue-600" />, 
        bgColor: 'bg-blue-50', 
        available: true 
      },
      { 
        id: '7-11', 
        name: '7-Eleven (iBON)', 
        icon: '🏪', 
        bgColor: 'bg-green-100', 
        available: true,
        component: MasterPaymentForms
      },
      { 
        id: '7-11-card', 
        name: '7-Eleven Card', 
        icon: '💳', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms
      },
      { 
        id: 'family-mart', 
        name: 'FamilyMart', 
        icon: '🛒', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms
      }
    ],
    'NTD': [
      { 
        id: 'taiwanbanktransfer', 
        name: 'Chuyển khoản ngân hàng', 
        icon: <FaWallet className="text-blue-600" />, 
        bgColor: 'bg-blue-50', 
        available: true 
      },
      { 
        id: '7-11', 
        name: '7-11 (iBON)', 
        icon: '🏪', 
        bgColor: 'bg-green-100', 
        available: true,
        component: MasterPaymentForms
      },
      { 
        id: '7-11-card', 
        name: '7-11 Card', 
        icon: '💳', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms
      },
      { 
        id: 'family-mart', 
        name: 'FamilyMart', 
        icon: '🛒', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms
      }
    ],
    'TWD': [
      { 
        id: 'taiwanbanktransfer', 
        name: 'Chuyển khoản ngân hàng', 
        icon: <FaWallet className="text-blue-600" />, 
        bgColor: 'bg-blue-50', 
        available: true 
      },
      { 
        id: '7-11', 
        name: '7-11 (iBON)', 
        icon: '🏪', 
        bgColor: 'bg-green-100', 
        available: true,
        component: MasterPaymentForms
      },
      { 
        id: '7-11-card', 
        name: '7-11 Card', 
        icon: '💳', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms
      },
      { 
        id: 'family-mart', 
        name: 'FamilyMart', 
        icon: '🛒', 
        bgColor: 'bg-blue-100', 
        available: true,
        component: MasterPaymentForms
      }
    ],
    '$': [
      { 
        id: 'paypal', 
        name: 'PayPal', 
        icon: '🌐', 
        bgColor: 'bg-blue-50', 
        available: true 
      },
      { 
        id: 'stripe', 
        name: 'Credit Card (Stripe)', 
        icon: <FaCreditCard className="text-indigo-600" />, 
        bgColor: 'bg-indigo-50', 
        available: true 
      },
      { 
        id: 'inquiry', 
        name: 'Inquiry', 
        icon: '❓', 
        bgColor: 'bg-yellow-50', 
        available: true 
      }
    ],
    'USD': [
      { 
        id: 'paypal', 
        name: 'PayPal', 
        icon: '🌐', 
        bgColor: 'bg-blue-50', 
        available: true 
      },
      { 
        id: 'stripe', 
        name: 'Credit Card (Stripe)', 
        icon: <FaCreditCard className="text-indigo-600" />, 
        bgColor: 'bg-indigo-50', 
        available: true 
      },
      { 
        id: 'inquiry', 
        name: 'Inquiry', 
        icon: '❓', 
        bgColor: 'bg-yellow-50', 
        available: true 
      }
    ],
    'default': [
      { 
        id: 'bank', 
        name: 'Bank Transfer', 
        icon: <FaWallet className="text-gray-600" />, 
        bgColor: 'bg-gray-50', 
        available: true 
      },
      { 
        id: 'card', 
        name: 'Credit Card', 
        icon: <FaCcVisa className="text-blue-700" />, 
        bgColor: 'bg-blue-50', 
        available: true 
      },
      { 
        id: 'inquiry', 
        name: 'Inquiry', 
        icon: '❓', 
        bgColor: 'bg-yellow-50', 
        available: true 
      },
      { 
        id: 'banktransfer', 
        name: 'Bank Transfer', 
        icon: <FaWallet className="text-purple-600" />, 
        bgColor: 'bg-purple-50', 
        available: true 
      }
    ]
  };

  /**
   * Payment form components mapping
   */
  static PAYMENT_FORM_COMPONENTS = {
    'cod': PaymentFormCOD,
    '7-11': MasterPaymentForms,
    '7-11-card': MasterPaymentForms,
    'family-mart': MasterPaymentForms,
    'ok-mart': MasterPaymentForms
  };

  /**
   * Get available payment methods for a specific currency
   * @param {string} currency - Currency code
   * @returns {Array} Array of payment method configurations
   */
  static getMethodsForCurrency(currency) {
    if (!currency) return this.PAYMENT_METHODS_BY_CURRENCY.default;
    
    const normalizedCurrency = this._normalizeCurrency(currency);
    return this.PAYMENT_METHODS_BY_CURRENCY[normalizedCurrency] || 
           this.PAYMENT_METHODS_BY_CURRENCY.default;
  }

  /**
   * Get specific payment method configuration
   * @param {string} methodId - Payment method ID
   * @param {string} currency - Currency code
   * @returns {Object|null} Payment method configuration
   */
  static getMethod(methodId, currency) {
    const methods = this.getMethodsForCurrency(currency);
    return methods.find(method => method.id === methodId) || null;
  }

  /**
   * Check if payment method is available for currency
   * @param {string} methodId - Payment method ID
   * @param {string} currency - Currency code  
   * @returns {boolean} True if method is available
   */
  static isMethodAvailable(methodId, currency) {
    const method = this.getMethod(methodId, currency);
    return method && method.available;
  }

  /**
   * Get payment form component for method
   * @param {string} methodId - Payment method ID
   * @returns {React.Component|null} Payment form component
   */
  static getFormComponent(methodId) {
    return this.PAYMENT_FORM_COMPONENTS[methodId] || null;
  }

  /**
   * Create payment form props for method
   * @param {string} methodId - Payment method ID
   * @param {Object} baseProps - Base props to extend
   * @returns {Object} Enhanced props for payment form
   */
  static createFormProps(methodId, baseProps = {}) {
    const component = this.getFormComponent(methodId);
    
    if (!component) return baseProps;

    // Add method-specific configuration
    return {
      ...baseProps,
      paymentMethod: methodId,
      displayName: component.displayName || methodId,
      icon: component.icon,
      logo: component.logo,
      currencies: component.currencies || []
    };
  }

  /**
   * Validate payment method selection
   * @param {string} methodId - Payment method ID
   * @param {string} currency - Currency code
   * @param {Object} orderData - Order data for validation
   * @returns {Object} Validation result
   */
  static validateMethodSelection(methodId, currency, orderData = {}) {
    const result = {
      isValid: true,
      errors: []
    };

    // Check if method exists
    const method = this.getMethod(methodId, currency);
    if (!method) {
      result.isValid = false;
      result.errors.push(`Payment method "${methodId}" not found for currency "${currency}"`);
      return result;
    }

    // Check if method is available
    if (!method.available) {
      result.isValid = false;
      result.errors.push(`Payment method "${methodId}" is currently unavailable`);
    }

    // Check if component exists
    const component = this.getFormComponent(methodId);
    if (!component) {
      result.isValid = false;
      result.errors.push(`No form component found for payment method "${methodId}"`);
    }

    return result;
  }

  /**
   * Get all supported currencies
   * @returns {Array} Array of currency codes
   */
  static getSupportedCurrencies() {
    return Object.keys(this.PAYMENT_METHODS_BY_CURRENCY)
      .filter(currency => currency !== 'default');
  }

  /**
   * Get payment methods statistics
   * @returns {Object} Statistics about payment methods
   */
  static getStatistics() {
    const currencies = this.getSupportedCurrencies();
    const totalMethods = currencies.reduce((total, currency) => {
      return total + this.PAYMENT_METHODS_BY_CURRENCY[currency].length;
    }, 0);

    return {
      supportedCurrencies: currencies.length,
      totalPaymentMethods: totalMethods,
      availableComponents: Object.keys(this.PAYMENT_FORM_COMPONENTS).length
    };
  }

  /**
   * Normalize currency code for internal use
   * @private
   * @param {string} currency - Raw currency code
   * @returns {string} Normalized currency code
   */
  static _normalizeCurrency(currency) {
    // Handle Taiwan Dollar variations
    if (['NT', 'NT$', 'NTD', 'TWD'].includes(currency.toUpperCase())) {
      return 'NT$';
    }

    return currency.toUpperCase();
  }

  /**
   * Register a new payment method
   * @param {string} currency - Currency code
   * @param {Object} methodConfig - Payment method configuration
   */
  static registerMethod(currency, methodConfig) {
    const normalizedCurrency = this._normalizeCurrency(currency);
    
    if (!this.PAYMENT_METHODS_BY_CURRENCY[normalizedCurrency]) {
      this.PAYMENT_METHODS_BY_CURRENCY[normalizedCurrency] = [];
    }

    this.PAYMENT_METHODS_BY_CURRENCY[normalizedCurrency].push(methodConfig);

    // Register component if provided
    if (methodConfig.component) {
      this.PAYMENT_FORM_COMPONENTS[methodConfig.id] = methodConfig.component;
    }
  }

  /**
   * Unregister a payment method
   * @param {string} currency - Currency code
   * @param {string} methodId - Payment method ID to remove
   */
  static unregisterMethod(currency, methodId) {
    const normalizedCurrency = this._normalizeCurrency(currency);
    const methods = this.PAYMENT_METHODS_BY_CURRENCY[normalizedCurrency];
    
    if (methods) {
      const index = methods.findIndex(method => method.id === methodId);
      if (index > -1) {
        methods.splice(index, 1);
      }
    }

    // Remove component mapping
    delete this.PAYMENT_FORM_COMPONENTS[methodId];
  }
}

export default PaymentRegistry;