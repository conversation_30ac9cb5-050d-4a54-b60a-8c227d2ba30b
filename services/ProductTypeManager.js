/**
 * Product Type Management Service
 * 
 * Centralized product type detection and categorization logic
 * Extracted from checkout.js to improve modularity and reusability
 */

export class ProductTypeManager {
  static PRODUCT_TYPES = {
    CARD: 'CARD',
    SIM: 'SIM', 
    TRAVELSIM: 'TRAVELSIM',
    DEFAULT: 'DEFAULT'
  };

  static CATEGORY_MAPPINGS = {
    1: 'CARD',    // For products with CARD category
    2: 'SIM',     // For products with SIM category  
    3: 'TRAVELSIM' // For products with Travelsim category
  };

  /**
   * Detect product type from item data with detailed analysis
   * @param {Object} item - Product item with categories
   * @returns {Object} Analysis result with type and reasoning
   */
  static detect(item) {
    const analysis = {
      steps: [],
      conclusion: { type: 'DEFAULT', reason: 'No categories to analyze' }
    };

    // Step 1: Check if categories exist
    if (!item.categories || !Array.isArray(item.categories)) {
      analysis.steps.push({
        step: 'categories_check',
        result: 'no_categories',
        data: { categories: item.categories }
      });
      return analysis;
    }

    analysis.steps.push({
      step: 'categories_check', 
      result: 'found',
      data: { categories: item.categories, count: item.categories.length }
    });

    // Step 2: Analyze each category
    const categoryAnalysis = item.categories.map(category => {
      const normalized = category.toUpperCase();
      const isCard = normalized.includes('CARD');
      const isSim = normalized === 'SIM';  
      const isTravelSim = normalized.includes('TRAVELSIM');

      return {
        original: category,
        normalized,
        matches: { isCard, isSim, isTravelSim },
        priority: this._getCategoryPriority(normalized)
      };
    });

    analysis.steps.push({
      step: 'category_analysis',
      result: categoryAnalysis
    });

    // Step 3: Determine type based on priority
    const typeResult = this._determineTypeByPriority(categoryAnalysis);
    analysis.conclusion = typeResult;

    return analysis;
  }

  /**
   * Get category priority for type determination
   * @private
   */
  static _getCategoryPriority(normalizedCategory) {
    if (normalizedCategory.includes('TRAVELSIM')) return 3;
    if (normalizedCategory === 'SIM') return 2; 
    if (normalizedCategory.includes('CARD')) return 1;
    return 0;
  }

  /**
   * Determine final product type based on category priority
   * @private
   */
  static _determineTypeByPriority(categoryAnalysis) {
    // Find highest priority match
    let highestPriority = 0;
    let selectedType = 'DEFAULT';
    let reason = 'No matching categories found';

    for (const analysis of categoryAnalysis) {
      if (analysis.priority > highestPriority) {
        highestPriority = analysis.priority;
        
        if (analysis.matches.isTravelSim) {
          selectedType = 'TRAVELSIM';
          reason = `Matched TRAVELSIM category: ${analysis.original}`;
        } else if (analysis.matches.isSim) {
          selectedType = 'SIM';
          reason = `Matched SIM category: ${analysis.original}`;  
        } else if (analysis.matches.isCard) {
          selectedType = 'CARD';
          reason = `Matched CARD category: ${analysis.original}`;
        }
      }
    }

    return { type: selectedType, reason, priority: highestPriority };
  }

  /**
   * Get checkout method ID by product categories
   * @param {Array} categories - Product categories
   * @returns {number|null} Method ID or null
   */
  static getMethodByCategory(categories) {
    if (!categories || !Array.isArray(categories)) return null;
    
    const categoryMap = {
      "CARD": 1,
      "SIM": 2,
      "TRAVELSIM": 3
    };
    
    // Find matching category
    for (const category of categories) {
      const upperCategory = category.toUpperCase();
      if (categoryMap[upperCategory]) {
        return categoryMap[upperCategory];
      }
    }
    
    return null; // No matching method found
  }

  /**
   * Validate if product type is supported
   * @param {string} productType - Product type to validate
   * @returns {boolean} True if supported
   */
  static isValidType(productType) {
    return Object.values(this.PRODUCT_TYPES).includes(productType);
  }

  /**
   * Get checkout flow configuration for product type
   * @param {string} productType - Product type
   * @returns {Object} Flow configuration
   */
  static getCheckoutFlow(productType) {
    const flows = {
      [this.PRODUCT_TYPES.CARD]: {
        steps: ['product-config', 'address', 'payment', 'confirmation'],
        requiresAddress: true,
        requiresConfiguration: true,
        allowsMultipleItems: true
      },
      [this.PRODUCT_TYPES.SIM]: {
        steps: ['product-config', 'address', 'payment', 'confirmation'],
        requiresAddress: true, 
        requiresConfiguration: true,
        allowsMultipleItems: true
      },
      [this.PRODUCT_TYPES.TRAVELSIM]: {
        steps: ['product-info', 'payment', 'confirmation'],
        requiresAddress: false,
        requiresConfiguration: false,
        allowsMultipleItems: false
      },
      [this.PRODUCT_TYPES.DEFAULT]: {
        steps: ['product-info', 'address', 'payment', 'confirmation'],
        requiresAddress: true,
        requiresConfiguration: false, 
        allowsMultipleItems: true
      }
    };

    return flows[productType] || flows[this.PRODUCT_TYPES.DEFAULT];
  }

  /**
   * Check if product type requires address information
   * @param {string} productType - Product type
   * @returns {boolean} True if address required
   */
  static requiresAddress(productType) {
    const flow = this.getCheckoutFlow(productType);
    return flow.requiresAddress;
  }

  /**
   * Check if product type requires configuration
   * @param {string} productType - Product type  
   * @returns {boolean} True if configuration required
   */
  static requiresConfiguration(productType) {
    const flow = this.getCheckoutFlow(productType);
    return flow.requiresConfiguration;
  }
}

export default ProductTypeManager;