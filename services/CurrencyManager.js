/**
 * Currency Management Service
 * 
 * Centralized currency handling, normalization, and cart filtering
 * Extracted from checkout.js and paymentUtils.js for better organization
 */

export class CurrencyManager {
  static SUPPORTED_CURRENCIES = [
    'VND', 'NT$', 'NT', 'NTD', 'TWD', 'USD', '$'
  ];

  static CURRENCY_GROUPS = {
    TAIWAN_DOLLAR: ['NT$', 'NT', 'NTD', 'TWD'],
    US_DOLLAR: ['USD', '$'],
    VIETNAMESE_DONG: ['VND']
  };

  /**
   * Normalize currency codes to standard format
   * @param {string} currency - Raw currency code
   * @returns {string} Normalized currency code
   */
  static normalize(currency) {
    if (!currency) return currency;

    // Handle Taiwan Dollar variations
    if (['NT', 'NT$', 'NTD', 'TWD'].includes(currency.toUpperCase())) {
      return 'NT$';
    }

    // Handle US Dollar variations  
    if (['USD', '$'].includes(currency.toUpperCase())) {
      return 'USD';
    }

    // Handle Vietnamese Dong
    if (currency.toUpperCase() === 'VND') {
      return 'VND';
    }

    return currency.toUpperCase();
  }

  /**
   * Get display name for currency
   * @param {string} currencyCode - Currency code
   * @returns {string} Human readable currency name
   */
  static getDisplayName(currencyCode) {
    const currencyNames = {
      "VND": "Vietnamese Dong (VND)",
      "$": "US Dollar ($)", 
      "USD": "US Dollar (USD)",
      "NT$": "New Taiwan Dollar (NT$)",
      "NT": "New Taiwan Dollar (NT)",
      "NTD": "New Taiwan Dollar (NTD)", 
      "TWD": "Taiwan Dollar (TWD)"
    };
    
    return currencyNames[currencyCode] || currencyCode;
  }

  /**
   * Check if currency is supported
   * @param {string} currency - Currency to check
   * @returns {boolean} True if supported
   */
  static isSupported(currency) {
    return this.SUPPORTED_CURRENCIES.includes(this.normalize(currency));
  }

  /**
   * Get all available currencies from cart items
   * @param {Array} cartItems - Array of cart items
   * @returns {Array} Array of unique currencies
   */
  static getAvailableCurrencies(cartItems) {
    if (!cartItems || !Array.isArray(cartItems)) return [];

    const currencies = new Set();
    
    cartItems.forEach(item => {
      if (item.currency) {
        currencies.add(this.normalize(item.currency));
      }
    });

    return Array.from(currencies);
  }

  /**
   * Filter cart items by currency
   * @param {Array} cartItems - Array of cart items
   * @param {string} targetCurrency - Currency to filter by
   * @returns {Array} Filtered cart items
   */
  static filterCartByCurrency(cartItems, targetCurrency) {
    if (!cartItems || !Array.isArray(cartItems)) return [];
    if (!targetCurrency) return cartItems;

    const normalizedTarget = this.normalize(targetCurrency);

    return cartItems.filter(item => {
      if (!item.currency) return false;
      return this.normalize(item.currency) === normalizedTarget;
    });
  }

  /**
   * Group cart items by currency
   * @param {Array} cartItems - Array of cart items  
   * @returns {Object} Object with currency as key and items as value
   */
  static groupCartByCurrency(cartItems) {
    if (!cartItems || !Array.isArray(cartItems)) return {};

    const grouped = {};

    cartItems.forEach(item => {
      if (!item.currency) return;
      
      const normalizedCurrency = this.normalize(item.currency);
      
      if (!grouped[normalizedCurrency]) {
        grouped[normalizedCurrency] = [];
      }
      
      grouped[normalizedCurrency].push(item);
    });

    return grouped;
  }

  /**
   * Calculate total price for items in specific currency
   * @param {Array} cartItems - Array of cart items
   * @param {string} currency - Target currency
   * @returns {number} Total price
   */
  static calculateTotal(cartItems, currency) {
    const filteredItems = this.filterCartByCurrency(cartItems, currency);
    
    return filteredItems.reduce((total, item) => {
      const price = parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 1;
      return total + (price * quantity);
    }, 0);
  }

  /**
   * Get currency symbol
   * @param {string} currency - Currency code
   * @returns {string} Currency symbol
   */
  static getSymbol(currency) {
    const symbols = {
      'VND': '₫',
      'USD': '$',
      '$': '$',
      'NT$': 'NT$',
      'NT': 'NT',
      'NTD': 'NT$',
      'TWD': 'NT$'
    };

    return symbols[this.normalize(currency)] || currency;
  }

  /**
   * Format price with currency
   * @param {number} amount - Price amount
   * @param {string} currency - Currency code
   * @returns {string} Formatted price string
   */
  static formatPrice(amount, currency) {
    const symbol = this.getSymbol(currency);
    const normalizedCurrency = this.normalize(currency);
    
    // Format based on currency type
    if (normalizedCurrency === 'VND') {
      return `${amount.toLocaleString('vi-VN')} ${symbol}`;
    } else if (normalizedCurrency === 'USD') {
      return `${symbol}${amount.toFixed(2)}`;
    } else if (this.CURRENCY_GROUPS.TAIWAN_DOLLAR.includes(normalizedCurrency)) {
      return `${symbol} ${amount.toFixed(0)}`;
    }
    
    return `${symbol} ${amount.toFixed(2)}`;
  }

  /**
   * Validate currency format and value
   * @param {string} currency - Currency to validate
   * @param {number} amount - Amount to validate
   * @returns {Object} Validation result
   */
  static validate(currency, amount) {
    const result = {
      isValid: true,
      errors: []
    };

    // Check currency support
    if (!this.isSupported(currency)) {
      result.isValid = false;
      result.errors.push(`Currency "${currency}" is not supported`);
    }

    // Check amount validity
    if (typeof amount !== 'number' || isNaN(amount) || amount < 0) {
      result.isValid = false;
      result.errors.push('Amount must be a valid positive number');
    }

    return result;
  }
}

export default CurrencyManager;