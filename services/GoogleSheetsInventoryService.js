const { GoogleSpreadsheet } = require('google-spreadsheet');
const { JWT } = require('google-auth-library');

/**
 * Google Sheets Inventory Service
 * Handles synchronization between local inventory and Google Sheets
 * Uses service account authentication for secure access
 */
class GoogleSheetsInventoryService {
  constructor() {
    this.doc = null;
    this.sheet = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the Google Sheets connection
   */
  async initialize() {
    try {
      if (this.isInitialized) return;

      // Hardcoded Google Sheets credentials for testing
      const GOOGLE_SHEETS_INVENTORY_ID = 'your_actual_sheet_id_here';
      const GOOGLE_SERVICE_ACCOUNT_EMAIL = '<EMAIL>';
      const GOOGLE_PRIVATE_KEY = `-----BEGIN PRIVATE KEY-----
Your actual private key here
-----END PRIVATE KEY-----
`;

      // Validate credentials are set
      if (GOOGLE_SHEETS_INVENTORY_ID === 'your_actual_sheet_id_here' || 
          GOOGLE_SERVICE_ACCOUNT_EMAIL === '<EMAIL>' ||
          GOOGLE_PRIVATE_KEY.includes('Your actual private key here')) {
        throw new Error('Please replace the hardcoded credentials with actual Google Sheets credentials');
      }

      // Create service account auth
      const serviceAccountAuth = new JWT({
        email: GOOGLE_SERVICE_ACCOUNT_EMAIL,
        key: GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        scopes: [
          'https://www.googleapis.com/auth/spreadsheets',
          'https://www.googleapis.com/auth/drive.file'
        ]
      });

      // Initialize the document
      this.doc = new GoogleSpreadsheet(GOOGLE_SHEETS_INVENTORY_ID, serviceAccountAuth);
      await this.doc.loadInfo();

      // Get or create the inventory sheet
      this.sheet = this.doc.sheetsByTitle['Inventory'] || await this.doc.addSheet({
        title: 'Inventory',
        headerValues: ['SKU', 'Name', 'Brand', 'Price', 'Currency', 'Categories', 'Inventory_Type', 'Inventory_Count', 'Inventory_Items', 'Description', 'Last_Updated']
      });

      this.isInitialized = true;
      console.log('Google Sheets Inventory Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Google Sheets Inventory Service:', error);
      throw error;
    }
  }

  /**
   * Sync local inventory to Google Sheets
   * @param {Array} inventoryData - Local inventory data
   */
  async syncToSheets(inventoryData) {
    try {
      await this.initialize();

      // Clear existing data (except headers)
      await this.sheet.clear();
      await this.sheet.setHeaderRow(['SKU', 'Name', 'Brand', 'Price', 'Currency', 'Categories', 'Inventory_Type', 'Inventory_Count', 'Inventory_Items', 'Description', 'Last_Updated']);

      // Prepare data for Google Sheets
      const sheetData = inventoryData.map(item => {
        const inventoryType = item.inventory?.type || 'same';
        const inventoryCount = item.inventory?.count || item.currentInventory || 0;
        const inventoryItems = inventoryType === 'separate' && item.inventory?.items 
          ? JSON.stringify(item.inventory.items) 
          : '';

        return {
          SKU: item.sku || '',
          Name: item.name || '',
          Brand: item.brand || item.provider || '',
          Price: item.price || 0,
          Currency: item.currency || 'VND',
          Categories: Array.isArray(item.categories) ? item.categories.join(', ') : (item.categories || ''),
          Inventory_Type: inventoryType,
          Inventory_Count: inventoryCount,
          Inventory_Items: inventoryItems,
          Description: item.description || '',
          Last_Updated: new Date().toISOString()
        };
      });

      // Add rows to sheet
      if (sheetData.length > 0) {
        await this.sheet.addRows(sheetData);
      }

      console.log(`Successfully synced ${sheetData.length} items to Google Sheets`);
      return { success: true, count: sheetData.length };
    } catch (error) {
      console.error('Error syncing to Google Sheets:', error);
      throw error;
    }
  }

  /**
   * Sync from Google Sheets to local inventory
   * @returns {Array} Updated inventory data
   */
  async syncFromSheets() {
    try {
      await this.initialize();

      // Load all rows from the sheet
      const rows = await this.sheet.getRows();

      // Convert sheet data back to inventory format
      const inventoryData = rows.map(row => {
        const inventoryType = row.get('Inventory_Type') || 'same';
        const inventoryCount = parseInt(row.get('Inventory_Count')) || 0;
        const inventoryItemsStr = row.get('Inventory_Items') || '';
        
        let inventoryItems = [];
        if (inventoryType === 'separate' && inventoryItemsStr) {
          try {
            inventoryItems = JSON.parse(inventoryItemsStr);
          } catch (e) {
            console.warn(`Failed to parse inventory items for SKU ${row.get('SKU')}:`, e);
          }
        }

        const categories = row.get('Categories') || '';
        const categoriesArray = categories ? categories.split(',').map(cat => cat.trim()) : [];

        return {
          sku: row.get('SKU') || '',
          name: row.get('Name') || '',
          brand: row.get('Brand') || '',
          provider: row.get('Brand') || '', // For backward compatibility
          price: parseFloat(row.get('Price')) || 0,
          currency: row.get('Currency') || 'VND',
          categories: categoriesArray,
          description: row.get('Description') || '',
          inventory: {
            type: inventoryType,
            count: inventoryCount,
            ...(inventoryType === 'separate' && { items: inventoryItems })
          },
          currentInventory: inventoryCount, // For backward compatibility
          lastUpdated: row.get('Last_Updated') || new Date().toISOString()
        };
      });

      console.log(`Successfully synced ${inventoryData.length} items from Google Sheets`);
      return inventoryData;
    } catch (error) {
      console.error('Error syncing from Google Sheets:', error);
      throw error;
    }
  }

  /**
   * Update a specific item in Google Sheets
   * @param {string} sku - SKU of the item to update
   * @param {Object} itemData - Updated item data
   */
  async updateItem(sku, itemData) {
    try {
      await this.initialize();

      const rows = await this.sheet.getRows();
      const rowToUpdate = rows.find(row => row.get('SKU') === sku);

      if (!rowToUpdate) {
        throw new Error(`Item with SKU ${sku} not found in Google Sheets`);
      }

      // Update the row
      const inventoryType = itemData.inventory?.type || 'same';
      const inventoryCount = itemData.inventory?.count || itemData.currentInventory || 0;
      const inventoryItems = inventoryType === 'separate' && itemData.inventory?.items 
        ? JSON.stringify(itemData.inventory.items) 
        : '';

      rowToUpdate.set('Name', itemData.name || '');
      rowToUpdate.set('Brand', itemData.brand || itemData.provider || '');
      rowToUpdate.set('Price', itemData.price || 0);
      rowToUpdate.set('Currency', itemData.currency || 'VND');
      rowToUpdate.set('Categories', Array.isArray(itemData.categories) ? itemData.categories.join(', ') : (itemData.categories || ''));
      rowToUpdate.set('Inventory_Type', inventoryType);
      rowToUpdate.set('Inventory_Count', inventoryCount);
      rowToUpdate.set('Inventory_Items', inventoryItems);
      rowToUpdate.set('Description', itemData.description || '');
      rowToUpdate.set('Last_Updated', new Date().toISOString());

      await rowToUpdate.save();

      console.log(`Successfully updated item ${sku} in Google Sheets`);
      return { success: true };
    } catch (error) {
      console.error(`Error updating item ${sku} in Google Sheets:`, error);
      throw error;
    }
  }

  /**
   * Add a new item to Google Sheets
   * @param {Object} itemData - New item data
   */
  async addItem(itemData) {
    try {
      await this.initialize();

      const inventoryType = itemData.inventory?.type || 'same';
      const inventoryCount = itemData.inventory?.count || itemData.currentInventory || 0;
      const inventoryItems = inventoryType === 'separate' && itemData.inventory?.items 
        ? JSON.stringify(itemData.inventory.items) 
        : '';

      const newRow = {
        SKU: itemData.sku || '',
        Name: itemData.name || '',
        Brand: itemData.brand || itemData.provider || '',
        Price: itemData.price || 0,
        Currency: itemData.currency || 'VND',
        Categories: Array.isArray(itemData.categories) ? itemData.categories.join(', ') : (itemData.categories || ''),
        Inventory_Type: inventoryType,
        Inventory_Count: inventoryCount,
        Inventory_Items: inventoryItems,
        Description: itemData.description || '',
        Last_Updated: new Date().toISOString()
      };

      await this.sheet.addRow(newRow);

      console.log(`Successfully added item ${itemData.sku} to Google Sheets`);
      return { success: true };
    } catch (error) {
      console.error(`Error adding item ${itemData.sku} to Google Sheets:`, error);
      throw error;
    }
  }

  /**
   * Delete an item from Google Sheets
   * @param {string} sku - SKU of the item to delete
   */
  async deleteItem(sku) {
    try {
      await this.initialize();

      const rows = await this.sheet.getRows();
      const rowToDelete = rows.find(row => row.get('SKU') === sku);

      if (!rowToDelete) {
        throw new Error(`Item with SKU ${sku} not found in Google Sheets`);
      }

      await rowToDelete.delete();

      console.log(`Successfully deleted item ${sku} from Google Sheets`);
      return { success: true };
    } catch (error) {
      console.error(`Error deleting item ${sku} from Google Sheets:`, error);
      throw error;
    }
  }

  /**
   * Get the Google Sheets URL for easy access
   */
  getSheetUrl() {
    const GOOGLE_SHEETS_INVENTORY_ID = 'your_actual_sheet_id_here';
    if (GOOGLE_SHEETS_INVENTORY_ID === 'your_actual_sheet_id_here') {
      return null;
    }
    return `https://docs.google.com/spreadsheets/d/${GOOGLE_SHEETS_INVENTORY_ID}/edit`;
  }
}

// Export singleton instance
const googleSheetsInventoryService = new GoogleSheetsInventoryService();
module.exports = googleSheetsInventoryService;