/**
 * Address Management Service
 * 
 * Handles address validation, formatting, and management
 * Provides consistent address handling across the checkout process
 */

export class AddressManager {
  
  /**
   * Address field validation rules
   */
  static VALIDATION_RULES = {
    address: {
      required: true,
      minLength: 5,
      maxLength: 200,
      pattern: /^[a-zA-Z0-9\s,.-/#]+$/
    },
    city: {
      required: true,
      minLength: 2,
      maxLength: 50,
      pattern: /^[a-zA-ZÀ-ÿ\s.-]+$/
    },
    postalCode: {
      required: true,
      minLength: 3,
      maxLength: 10,
      pattern: /^[a-zA-Z0-9\s-]+$/
    },
    country: {
      required: true,
      minLength: 2,
      maxLength: 50
    },
    notes: {
      required: false,
      maxLength: 500
    }
  };

  /**
   * Supported countries and their configurations
   */
  static COUNTRIES = {
    'VN': {
      name: 'Vietnam',
      code: 'VN',
      postalCodePattern: /^\d{6}$/,
      postalCodeFormat: 'XXXXXX',
      addressFormat: '{address}, {city}, {country} {postalCode}'
    },
    'TW': {
      name: 'Taiwan',
      code: 'TW',
      postalCodePattern: /^\d{3}(\d{2})?$/,
      postalCodeFormat: 'XXX or XXXXX',
      addressFormat: '{postalCode} {city}, {address}, {country}'
    },
    'US': {
      name: 'United States',
      code: 'US',
      postalCodePattern: /^\d{5}(-\d{4})?$/,
      postalCodeFormat: 'XXXXX or XXXXX-XXXX',
      addressFormat: '{address}, {city}, {country} {postalCode}'
    },
    'JP': {
      name: 'Japan',
      code: 'JP',
      postalCodePattern: /^\d{3}-\d{4}$/,
      postalCodeFormat: 'XXX-XXXX',
      addressFormat: '{postalCode} {city}, {address}, {country}'
    },
    'SG': {
      name: 'Singapore',
      code: 'SG',
      postalCodePattern: /^\d{6}$/,
      postalCodeFormat: 'XXXXXX',
      addressFormat: '{address}, {country} {postalCode}'
    }
  };

  /**
   * Validate address object
   * @param {Object} address - Address object to validate
   * @returns {Object} Validation result
   */
  static validateAddress(address) {
    const result = {
      isValid: true,
      errors: [],
      warnings: []
    };

    if (!address || typeof address !== 'object') {
      result.isValid = false;
      result.errors.push('Address object is required');
      return result;
    }

    // Validate each field
    for (const [field, rules] of Object.entries(this.VALIDATION_RULES)) {
      const value = address[field];
      const fieldResult = this._validateField(field, value, rules);
      
      if (fieldResult.errors.length > 0) {
        result.errors.push(...fieldResult.errors);
      }
      if (fieldResult.warnings.length > 0) {
        result.warnings.push(...fieldResult.warnings);
      }
    }

    // Country-specific validation
    if (address.country && address.postalCode) {
      const countryValidation = this._validateCountrySpecific(address.country, address.postalCode);
      if (!countryValidation.isValid) {
        result.warnings.push(...countryValidation.errors);
      }
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Format address for display
   * @param {Object} address - Address object
   * @param {string} format - Format type ('full', 'short', 'shipping')
   * @returns {string} Formatted address
   */
  static formatAddress(address, format = 'full') {
    if (!address) return '';

    const countryConfig = this.COUNTRIES[address.country?.toUpperCase()];
    
    switch (format) {
      case 'full':
        return this._formatFullAddress(address, countryConfig);
      case 'short':
        return this._formatShortAddress(address);
      case 'shipping':
        return this._formatShippingLabel(address, countryConfig);
      default:
        return this._formatFullAddress(address, countryConfig);
    }
  }

  /**
   * Get country configuration
   * @param {string} countryCode - Country code (2-letter)
   * @returns {Object|null} Country configuration
   */
  static getCountryConfig(countryCode) {
    if (!countryCode) return null;
    return this.COUNTRIES[countryCode.toUpperCase()] || null;
  }

  /**
   * Get supported countries list
   * @returns {Array} Array of country objects
   */
  static getSupportedCountries() {
    return Object.values(this.COUNTRIES).map(country => ({
      code: country.code,
      name: country.name
    }));
  }

  /**
   * Normalize address object
   * @param {Object} address - Raw address object
   * @returns {Object} Normalized address
   */
  static normalizeAddress(address) {
    if (!address) return {};

    return {
      address: this._normalizeString(address.address),
      city: this._normalizeString(address.city),
      postalCode: this._normalizePostalCode(address.postalCode),
      country: this._normalizeCountry(address.country),
      notes: this._normalizeString(address.notes, false)
    };
  }

  /**
   * Check if address is complete
   * @param {Object} address - Address to check
   * @returns {boolean} True if address is complete
   */
  static isCompleteAddress(address) {
    if (!address) return false;
    
    const required = ['address', 'city', 'postalCode', 'country'];
    return required.every(field => address[field] && address[field].trim().length > 0);
  }

  /**
   * Calculate delivery zones/regions
   * @param {Object} address - Delivery address
   * @returns {Object} Zone information
   */
  static getDeliveryZone(address) {
    if (!address.country) {
      return { zone: 'unknown', deliveryTime: 'TBD', cost: 0 };
    }

    const zoneConfig = {
      'VN': { zone: 'domestic', deliveryTime: '1-3 days', cost: 0 },
      'TW': { zone: 'domestic', deliveryTime: '1-2 days', cost: 0 },
      'US': { zone: 'international', deliveryTime: '7-14 days', cost: 25 },
      'JP': { zone: 'asia', deliveryTime: '3-7 days', cost: 15 },
      'SG': { zone: 'asia', deliveryTime: '3-7 days', cost: 15 }
    };

    return zoneConfig[address.country.toUpperCase()] || {
      zone: 'international',
      deliveryTime: '7-21 days',
      cost: 35
    };
  }

  /**
   * Private validation methods
   */

  static _validateField(fieldName, value, rules) {
    const result = { errors: [], warnings: [] };

    // Required field check
    if (rules.required && (!value || value.trim().length === 0)) {
      result.errors.push(`${fieldName} is required`);
      return result;
    }

    // Skip further validation if field is empty and not required
    if (!value || value.trim().length === 0) {
      return result;
    }

    // Length validation
    if (rules.minLength && value.length < rules.minLength) {
      result.errors.push(`${fieldName} must be at least ${rules.minLength} characters`);
    }
    if (rules.maxLength && value.length > rules.maxLength) {
      result.errors.push(`${fieldName} must be less than ${rules.maxLength} characters`);
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      result.warnings.push(`${fieldName} format may be invalid`);
    }

    return result;
  }

  static _validateCountrySpecific(country, postalCode) {
    const countryConfig = this.COUNTRIES[country.toUpperCase()];
    
    if (!countryConfig) {
      return { isValid: true, errors: [] };
    }

    if (countryConfig.postalCodePattern && !countryConfig.postalCodePattern.test(postalCode)) {
      return {
        isValid: false,
        errors: [`Postal code format should be: ${countryConfig.postalCodeFormat}`]
      };
    }

    return { isValid: true, errors: [] };
  }

  /**
   * Private formatting methods
   */

  static _formatFullAddress(address, countryConfig) {
    if (countryConfig?.addressFormat) {
      return countryConfig.addressFormat
        .replace('{address}', address.address || '')
        .replace('{city}', address.city || '')
        .replace('{postalCode}', address.postalCode || '')
        .replace('{country}', countryConfig.name || address.country || '');
    }

    // Default format
    const parts = [
      address.address,
      address.city,
      address.postalCode,
      address.country
    ].filter(Boolean);

    return parts.join(', ');
  }

  static _formatShortAddress(address) {
    const parts = [address.city, address.country].filter(Boolean);
    return parts.join(', ');
  }

  static _formatShippingLabel(address, countryConfig) {
    const lines = [];
    
    if (address.address) lines.push(address.address);
    if (address.city && address.postalCode) {
      lines.push(`${address.city} ${address.postalCode}`);
    } else if (address.city) {
      lines.push(address.city);
    }
    if (countryConfig?.name) {
      lines.push(countryConfig.name);
    } else if (address.country) {
      lines.push(address.country);
    }

    return lines.join('\n');
  }

  /**
   * Private normalization methods
   */

  static _normalizeString(str, capitalize = true) {
    if (!str) return '';
    
    let normalized = str.trim();
    if (capitalize) {
      normalized = normalized.charAt(0).toUpperCase() + normalized.slice(1).toLowerCase();
    }
    
    return normalized;
  }

  static _normalizePostalCode(postalCode) {
    if (!postalCode) return '';
    return postalCode.trim().toUpperCase().replace(/\s+/g, '');
  }

  static _normalizeCountry(country) {
    if (!country) return '';
    
    // Convert country names to codes if needed
    const countryMapping = {
      'vietnam': 'VN',
      'taiwan': 'TW',
      'united states': 'US',
      'usa': 'US',
      'japan': 'JP',
      'singapore': 'SG'
    };

    const normalized = country.trim().toLowerCase();
    return countryMapping[normalized] || country.trim().toUpperCase();
  }
}

export default AddressManager;