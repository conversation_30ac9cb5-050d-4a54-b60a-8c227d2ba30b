// API Configuration
const API_CONFIGS = {
  groq: {
    url: 'https://api.groq.com/openai/v1/chat/completions',
    defaultModel: 'mixtral-8x7b-32768',
    apiKey: process.env.NEXT_PUBLIC_GROQ_API_KEY,
    headers: () => ({
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_GROQ_API_KEY}`,
      'Content-Type': 'application/json',
    })
  },
  openai: {
    url: 'https://api.openai.com/v1/chat/completions',
    defaultModel: 'gpt-3.5-turbo',
    apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
    headers: () => ({
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    })
  },
  // Add more providers here
};

// Default configuration
const DEFAULT_PROVIDER = 'groq';
//const DEFAULT_PROVIDER = 'openai';

class ChatApiService {
  constructor(provider = DEFAULT_PROVIDER, customConfig = {}) {
    this.setProvider(provider, customConfig);
  }

  setProvider(provider, customConfig = {}) {
    const config = API_CONFIGS[provider];
    if (!config) {
      throw new Error(`Unsupported provider: ${provider}`);
    }
    
    this.config = {
      ...config,
      ...customConfig,
    };
    this.provider = provider;
  }

  async streamCompletion({ messages, onToken, onError, onFinish }) {
    try {
      const response = await fetch(this.config.url, {
        method: 'POST',
        headers: this.config.headers(),
        body: JSON.stringify({
          model: this.config.defaultModel,
          messages: messages,
          stream: true,
          temperature: 0.7,
          max_tokens: 1024,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No reader available');

      let accumulatedMessage = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Convert the chunk to text
        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n').filter(line => line.trim() !== '');

        for (const line of lines) {
          if (line.includes('[DONE]')) continue;
          if (!line.startsWith('data:')) continue;

          try {
            const data = JSON.parse(line.slice(5));
            if (data.choices[0]?.delta?.content) {
              accumulatedMessage += data.choices[0].delta.content;
              onToken(accumulatedMessage);
            }
          } catch (e) {
            console.error('Error parsing chunk:', e);
          }
        }
      }

      onFinish(accumulatedMessage);
    } catch (error) {
      console.error('API Error:', error);
      onError(error);
    }
  }

  // Add other API methods as needed
  async getModels() {
    // Implementation for getting available models
  }
}

export const chatApi = new ChatApiService();
export default ChatApiService;
