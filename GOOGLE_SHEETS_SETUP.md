# Google Sheets Integration Setup Guide

This guide will help you set up Google Sheets integration for inventory management.

## Prerequisites

- A Google account
- Access to Google Cloud Console
- A Google Sheet for inventory data

## Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Sheets API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Sheets API"
   - Click on it and press "Enable"

## Step 2: Create a Service Account

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Fill in the service account details:
   - Name: `inventory-sheets-service`
   - Description: `Service account for inventory Google Sheets integration`
4. Click "Create and Continue"
5. Skip the optional steps and click "Done"

## Step 3: Generate Service Account Key

1. In the "Credentials" page, find your service account
2. Click on the service account email
3. Go to the "Keys" tab
4. Click "Add Key" > "Create New Key"
5. Select "JSON" format and click "Create"
6. Download the JSON file and keep it secure

## Step 4: Create Google Sheet

1. Create a new Google Sheet
2. Set up the following column headers in the first row:
   - A1: `SKU`
   - B1: `Name`
   - C1: `Brand`
   - D1: `Price`
   - E1: `Currency`
   - F1: `Categories`
   - G1: `Inventory_Type`
   - H1: `Inventory_Count`
   - I1: `Inventory_Items`
   - J1: `Description`
   - K1: `Last_Updated`

3. Copy the Google Sheet ID from the URL:
   - URL format: `https://docs.google.com/spreadsheets/d/SHEET_ID/edit`
   - Copy the `SHEET_ID` part

## Step 5: Share Sheet with Service Account

1. In your Google Sheet, click "Share"
2. Add the service account email (from the JSON file) as an editor
3. Make sure "Notify people" is unchecked
4. Click "Share"

## Step 6: Configure Environment Variables

Update your `.env` file with the following variables:

```env
# Google Sheets Integration
GOOGLE_SHEETS_INVENTORY_ID=your_actual_sheet_id_here
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour actual private key here\n-----END PRIVATE KEY-----\n"
```

### Getting the values:

- **GOOGLE_SHEETS_INVENTORY_ID**: The Sheet ID from Step 4
- **GOOGLE_SERVICE_ACCOUNT_EMAIL**: The `client_email` field from the JSON file
- **GOOGLE_PRIVATE_KEY**: The `private_key` field from the JSON file (keep the quotes and newlines)

## Step 7: Test the Integration

1. Restart your development server: `npm run dev`
2. Go to the inventory management page: `http://localhost:3000/magshop/inventory-management`
3. Click the "Google Sheets" button
4. You should see the sync options if everything is configured correctly

## Data Structure

The Google Sheet will contain the following data:

- **SKU**: Product SKU (unique identifier)
- **Name**: Product name
- **Brand**: Product brand
- **Price**: Product price (number)
- **Currency**: Currency code (e.g., VND, USD)
- **Categories**: Comma-separated list of categories
- **Inventory_Type**: Either "separate" or "same"
- **Inventory_Count**: Total inventory count (number)
- **Inventory_Items**: JSON string of items (for "separate" type inventory)
- **Description**: Product description
- **Last_Updated**: ISO timestamp of last update

## Sync Options

1. **Sync to Sheets**: Push local inventory data to Google Sheets
2. **Sync from Sheets**: Pull data from Google Sheets to local inventory
3. **Bidirectional Sync**: Merge data from both sources (recommended)

## Troubleshooting

### "Chưa cấu hình Google Sheets" (Not Configured)
- Check that all environment variables are set correctly
- Ensure the private key is properly formatted with newlines
- Restart the development server after changing environment variables

### "Permission denied" errors
- Make sure the service account email has editor access to the Google Sheet
- Verify the Google Sheets API is enabled in your Google Cloud project

### "Invalid credentials" errors
- Check that the service account email and private key match the JSON file
- Ensure the private key includes the full content with BEGIN/END markers

## Security Notes

- Never commit the `.env` file to version control
- Keep the service account JSON file secure
- Regularly rotate service account keys
- Use environment-specific service accounts for production