const axios = require('axios');

/**
 * Test script for IF activation feature
 * This script will call the IF activation API directly to test the integration
 * Since we have no valid test phone numbers available, we expect to get an error
 */
async function testIFActivation() {
  try {
    console.log('Testing IF activation API...');
    
    // Try with a few different phone numbers to ensure we get the error message
    const testPhoneNumbers = ['0912345678', '0922222222', '0933333333', '0944444444'];
    const productId = '1000000001'; // Using the first product from the config
    
    for (const phoneNumber of testPhoneNumbers) {
      console.log(`\nTesting with phone number: ${phoneNumber}`);
      
      try {
        const response = await axios.post('http://localhost:3000/api/payment/if/activate', {
          phoneNumber,
          productId,
          debug: true // Enable debug mode to get more details
        });
        
        console.log('Response status:', response.status);
        console.log('Response data:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success) {
          console.log('✅ Activation successful (unexpected)');
        } else {
          console.log('❌ Activation failed (expected)');
          console.log('Error message:', response.data.error);
          
          // Check if we got the target error message
          if (response.data.error.includes('門號有誤請重新輸入') || 
              response.data.error.includes('Phone number error')) {
            console.log('✅ Got the expected error message about invalid phone number!');
          }
        }
      } catch (error) {
        console.error('Request error:', error.message);
        
        if (error.response) {
          console.log('Response status:', error.response.status);
          console.log('Response data:', JSON.stringify(error.response.data, null, 2));
          
          // Check if we got the target error message in the error response
          if (error.response.data.error && 
             (error.response.data.error.includes('門號有誤請重新輸入') || 
              error.response.data.error.includes('Phone number error'))) {
            console.log('✅ Got the expected error message about invalid phone number!');
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Script error:', error);
  }
}

// Run the test
testIFActivation(); 