/**
 * 7-Eleven APN Test Script for Existing Order
 * 
 * This script tests the APN callback endpoint with an existing order from the database.
 * It uses the order "711TEST-001" with orderNumber "711-TEST-12345" and trans_id "711apntest20250416001".
 */

const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Create logs directory if it doesn't exist
const LOGS_DIR = path.join(__dirname, 'apn-test-logs');
if (!fs.existsSync(LOGS_DIR)) {
  fs.mkdirSync(LOGS_DIR, { recursive: true });
}

// Create a log file for this test run
const LOG_FILE = path.join(LOGS_DIR, `apn-test-${new Date().toISOString().replace(/:/g, '-')}.log`);
const logger = fs.createWriteStream(LOG_FILE, { flags: 'a' });

// Configuration
const CONFIG = {
  // Target order details from the orders.json file
  order: {
    trans_id: "711apntest20250416001",
    orderNumber: "711-TEST-12345",
    amount: "1225",
  },
  // APN endpoint URL
  apnUrl: 'https://sim.dailoanshop.net/api/payment/apn-callback',
  // APN API credentials
  apiId: '827315300001',
};

// Log function that writes to console and file
function log(message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  
  console.log(logMessage);
  logger.write(logMessage + '\n');
  
  if (data) {
    const dataStr = typeof data === 'object' ? JSON.stringify(data, null, 2) : data.toString();
    console.log(dataStr);
    logger.write(dataStr + '\n');
  }
}

// Generate checksum for APN validation
function generateChecksum(api_id, trans_id, amount, status, nonce) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  log(`Generating checksum with message: ${message}`);
  return crypto.createHash('md5').update(message).digest('hex');
}

// Function to simulate an APN call
async function simulateAPNCall(status) {
  // Generate new nonce for each call to ensure uniqueness
  const nonce = Date.now().toString();
  
  // Prepare APN payload using the existing order data
  const payload = {
    api_id: CONFIG.apiId,
    trans_id: CONFIG.order.trans_id,
    order_no: CONFIG.order.orderNumber,
    amount: CONFIG.order.amount,
    status: status,  // A = waiting, B = paid, D = expired
    nonce: nonce,
    checksum: generateChecksum(
      CONFIG.apiId, 
      CONFIG.order.trans_id, 
      CONFIG.order.amount, 
      status, 
      nonce
    ),
    payment_code: 'PC' + Math.floor(Math.random() * 10000000),
    payment_detail: JSON.stringify({
      store_id: '12345',
      payment_time: new Date().toISOString(),
      receipt_no: 'R' + Math.floor(Math.random() * 1000000),
    })
  };

  log(`Sending ${getStatusName(status)} notification for order ${CONFIG.order.orderNumber}...`, payload);
  
  try {
    const response = await axios.post(CONFIG.apnUrl, payload);
    log(`Response received (${response.status}):`, response.data);
    return { success: true, response: response.data };
  } catch (error) {
    const errorData = error.response ? error.response.data : error.message;
    log(`Error during APN call:`, errorData);
    return { 
      success: false, 
      error: errorData,
      status: error.response ? error.response.status : 'No response'
    };
  }
}

// Helper to get status name
function getStatusName(statusCode) {
  switch(statusCode) {
    case 'A': return 'Waiting for payment';
    case 'B': return 'Payment completed';
    case 'D': return 'Payment expired';
    default: return 'Unknown status';
  }
}

// Run test scenarios in sequence
async function runAllTests() {
  log('=== 7-ELEVEN APN TEST WITH EXISTING ORDER ===');
  log(`Target URL: ${CONFIG.apnUrl}`);
  log(`Test Order: ${CONFIG.order.orderNumber}`);
  log(`Transaction ID: ${CONFIG.order.trans_id}`);
  log(`Amount: ${CONFIG.order.amount}`);
  log('=============================================');
  
  // Scenario 1: Payment waiting
  log('\n🔄 SCENARIO 1: PAYMENT WAITING');
  await simulateAPNCall('A');
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Scenario 2: Payment completed
  log('\n✅ SCENARIO 2: PAYMENT COMPLETED');
  await simulateAPNCall('B');
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Scenario 3: Payment expired
  log('\n⏱️ SCENARIO 3: PAYMENT EXPIRED');
  await simulateAPNCall('D');
  
  log('\n=== TEST COMPLETED ===');
  log(`Log file created at: ${LOG_FILE}`);
  
  // Close the log file
  logger.end();
}

// Run the tests
runAllTests(); 