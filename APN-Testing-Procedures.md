# 7-Eleven APN Testing Procedures

This document outlines the procedures for testing the 7-Eleven Active Payment Notification (APN) callback system. The APN system allows your application to receive real-time payment status updates from 7-Eleven convenience stores.

## Overview

7-Eleven's payment system sends notifications to your server in the following scenarios:
- When a payment is waiting to be processed (Status A)
- When a payment is completed successfully (Status B)
- When a payment has expired (Status D)

## Test Configuration

### Endpoint

The APN callback endpoint is:
```
https://sim.dailoanshop.net/api/payment/apn-callback
```

### API Credentials

The test uses the following API credentials:
- API ID: `827315300001`

### Test Order Data

The tests use an existing order from the database:
- Order ID: `711TEST-001`
- Order Number: `711-TEST-12345`
- Transaction ID: `711apntest20250416001`
- Amount: `1225` NT

## Test Scenarios

The test scripts simulate the following scenarios:

1. **Payment Waiting (Status A)**
   - Simulates a notification when the customer initiates a payment at 7-Eleven
   - Order status should change to "pending"
   - Payment status should change to "waiting_payment"

2. **Payment Completed (Status B)**
   - Simulates a notification when the customer successfully pays at 7-Eleven
   - Order status should change to "processing"
   - Payment status should change to "paid"

3. **Payment Expired (Status D)**
   - Simulates a notification when the payment time has expired
   - Order status should change to "cancelled"
   - Payment status should change to "expired"
   - Order should be marked as expired with reason "payment_expired"

## Test Scripts

We have created several test scripts in different languages to accommodate different environments:

### 1. Python Script (Recommended)

**File**: `apn-simple-test.py`

This script uses only Python standard libraries and is the most portable option.

```python
#!/usr/bin/env python3
import urllib.request
import urllib.parse
import json
import hashlib
import time
import random
import ssl
from datetime import datetime

# Order details
ORDER_NO = "711-TEST-12345"
TRANS_ID = "711apntest20250416001"
AMOUNT = "1225"

# APN configuration
APN_URL = "https://sim.dailoanshop.net/api/payment/apn-callback"
API_ID = "827315300001"

def generate_checksum(api_id, trans_id, amount, status, nonce):
    message = f"{api_id}:{trans_id}:{amount}:{status}:{nonce}"
    print(f"Generating checksum for: {message}")
    return hashlib.md5(message.encode()).hexdigest()

def send_apn_notification(status, status_name):
    # Generate unique nonce
    nonce = str(int(time.time() * 1000))
    
    # Generate checksum
    checksum = generate_checksum(API_ID, TRANS_ID, AMOUNT, status, nonce)
    
    # Random payment details
    payment_code = f"PC{random.randint(1000000, 9999999)}"
    store_id = f"ST{random.randint(100, 999)}"
    receipt_no = f"R{random.randint(100000, 999999)}"
    payment_time = datetime.now().isoformat()
    
    # Prepare request payload
    payload = {
        "api_id": API_ID,
        "trans_id": TRANS_ID,
        "order_no": ORDER_NO,
        "amount": AMOUNT,
        "status": status,
        "nonce": nonce,
        "checksum": checksum,
        "payment_code": payment_code,
        "payment_detail": json.dumps({
            "store_id": store_id,
            "payment_time": payment_time,
            "receipt_no": receipt_no
        })
    }
    
    print(f"\n=== SENDING {status_name} NOTIFICATION ===")
    print(f"Order Number: {ORDER_NO}")
    print(f"Transaction ID: {TRANS_ID}")
    print(f"Status: {status}")
    print(f"Checksum: {checksum}")
    
    # Convert payload to JSON and prepare request
    data = json.dumps(payload).encode('utf-8')
    
    # Create request
    req = urllib.request.Request(APN_URL, data=data)
    req.add_header('Content-Type', 'application/json')
    req.add_header('Content-Length', len(data))
    
    # Send request
    try:
        context = ssl._create_unverified_context()
        response = urllib.request.urlopen(req, context=context)
        
        status_code = response.getcode()
        response_text = response.read().decode('utf-8')
        
        print(f"\nResponse (HTTP {status_code}):")
        print(response_text)
        
        print("\n✓ APN notification successfully sent!")
        
    except urllib.error.HTTPError as e:
        print(f"\nResponse (HTTP {e.code}):")
        print(e.read().decode('utf-8'))
        print("\n✗ APN notification failed!")
        
    except Exception as e:
        print(f"\nError: {e}")
        print("\n✗ APN notification failed!")
    
    time.sleep(2)

# Run all test scenarios
print("=" * 52)
print("     7-ELEVEN APN TEST SCRIPT                      ")
print("=" * 52)
print(f"Testing with order {ORDER_NO} on {APN_URL}")

# Scenario 1: Payment Waiting
send_apn_notification("A", "PAYMENT WAITING")

# Scenario 2: Payment Completed
send_apn_notification("B", "PAYMENT COMPLETED")

# Scenario 3: Payment Expired
send_apn_notification("D", "PAYMENT EXPIRED")

print("\nTest completed! All notifications sent.")
```

### 2. JavaScript Version (Node.js)

**File**: `test-apn-existing-order.js`

This script provides more detailed logging but requires Node.js and the axios package.

```javascript
/**
 * 7-Eleven APN Test Script for Existing Order
 */
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  order: {
    trans_id: "711apntest20250416001",
    orderNumber: "711-TEST-12345",
    amount: "1225",
  },
  apnUrl: 'https://sim.dailoanshop.net/api/payment/apn-callback',
  apiId: '827315300001',
};

// Generate checksum for APN validation
function generateChecksum(api_id, trans_id, amount, status, nonce) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  console.log(`Generating checksum with message: ${message}`);
  return crypto.createHash('md5').update(message).digest('hex');
}

// Function to simulate an APN call
async function simulateAPNCall(status) {
  // Generate new nonce for each call to ensure uniqueness
  const nonce = Date.now().toString();
  
  // Prepare APN payload using the existing order data
  const payload = {
    api_id: CONFIG.apiId,
    trans_id: CONFIG.order.trans_id,
    order_no: CONFIG.order.orderNumber,
    amount: CONFIG.order.amount,
    status: status,
    nonce: nonce,
    checksum: generateChecksum(
      CONFIG.apiId, 
      CONFIG.order.trans_id, 
      CONFIG.order.amount, 
      status, 
      nonce
    ),
    payment_code: 'PC' + Math.floor(Math.random() * 10000000),
    payment_detail: JSON.stringify({
      store_id: '12345',
      payment_time: new Date().toISOString(),
      receipt_no: 'R' + Math.floor(Math.random() * 1000000),
    })
  };

  console.log(`Sending ${getStatusName(status)} notification for order ${CONFIG.order.orderNumber}...`);
  console.log(JSON.stringify(payload, null, 2));
  
  try {
    const response = await axios.post(CONFIG.apnUrl, payload);
    console.log(`Response received (${response.status}):`, response.data);
    return { success: true, response: response.data };
  } catch (error) {
    const errorData = error.response ? error.response.data : error.message;
    console.log(`Error during APN call:`, errorData);
    return { 
      success: false, 
      error: errorData,
      status: error.response ? error.response.status : 'No response'
    };
  }
}

// Helper to get status name
function getStatusName(statusCode) {
  switch(statusCode) {
    case 'A': return 'Waiting for payment';
    case 'B': return 'Payment completed';
    case 'D': return 'Payment expired';
    default: return 'Unknown status';
  }
}

// Run all test scenarios
async function runAllTests() {
  console.log('=== 7-ELEVEN APN TEST WITH EXISTING ORDER ===');
  console.log(`Target URL: ${CONFIG.apnUrl}`);
  console.log(`Test Order: ${CONFIG.order.orderNumber}`);
  console.log(`Transaction ID: ${CONFIG.order.trans_id}`);
  console.log(`Amount: ${CONFIG.order.amount}`);
  console.log('=============================================');
  
  // Scenario 1: Payment waiting
  console.log('\n🔄 SCENARIO 1: PAYMENT WAITING');
  await simulateAPNCall('A');
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Scenario 2: Payment completed
  console.log('\n✅ SCENARIO 2: PAYMENT COMPLETED');
  await simulateAPNCall('B');
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Scenario 3: Payment expired
  console.log('\n⏱️ SCENARIO 3: PAYMENT EXPIRED');
  await simulateAPNCall('D');
  
  console.log('\n=== TEST COMPLETED ===');
}

// Run the tests
runAllTests();
```

### 3. Shell Script Version (Bash)

**File**: `simple-apn-test.sh`

A bash script that uses curl for API calls.

```bash
#!/bin/bash

# Simple 7-Eleven APN Test Script
# This script tests updating order status via APN callbacks

# Order details
ORDER_NO="711-TEST-12345"
TRANS_ID="711apntest20250416001"
AMOUNT="1225"

# APN configuration
APN_URL="https://sim.dailoanshop.net/api/payment/apn-callback"
API_ID="827315300001"

# Function to generate MD5 checksum 
generate_checksum() {
    local api_id=$1
    local trans_id=$2
    local amount=$3
    local status=$4
    local nonce=$5
    local message="${api_id}:${trans_id}:${amount}:${status}:${nonce}"
    
    echo "Generating checksum for: ${message}"
    echo -n "$message" | md5sum | awk '{print $1}'
}

# Function to send APN notification
send_apn_notification() {
    local status=$1
    local status_name=$2
    
    # Generate unique nonce
    local nonce=$(date +%s%N)
    
    # Generate checksum
    local checksum=$(generate_checksum "$API_ID" "$TRANS_ID" "$AMOUNT" "$status" "$nonce")
    
    # Random payment details
    local payment_code="PC$RANDOM$RANDOM"
    local store_id="ST$RANDOM"
    local receipt_no="R$RANDOM"
    local payment_time=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
    
    echo -e "\n=== SENDING ${status_name} NOTIFICATION ==="
    echo -e "Order Number: ${ORDER_NO}"
    echo -e "Transaction ID: ${TRANS_ID}"
    echo -e "Status: ${status}"
    echo -e "Checksum: ${checksum}"
    
    # Make the API call using curl
    local response=$(curl -s -w "\n%{http_code}" -X POST "$APN_URL" \
        -H "Content-Type: application/json" \
        -d '{
            "api_id": "'"$API_ID"'",
            "trans_id": "'"$TRANS_ID"'",
            "order_no": "'"$ORDER_NO"'",
            "amount": "'"$AMOUNT"'",
            "status": "'"$status"'",
            "nonce": "'"$nonce"'",
            "checksum": "'"$checksum"'",
            "payment_code": "'"$payment_code"'",
            "payment_detail": {
                "store_id": "'"$store_id"'",
                "payment_time": "'"$payment_time"'",
                "receipt_no": "'"$receipt_no"'"
            }
        }')
    
    # Extract response body and status code
    local status_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | sed '$d')
    
    echo -e "\nResponse (HTTP $status_code):"
    echo "$body"
    
    if [[ $status_code -ge 200 && $status_code -lt 300 ]]; then
        echo -e "\n✓ APN notification successfully sent!"
    else
        echo -e "\n✗ APN notification failed!"
    fi
    
    # Sleep for 2 seconds before the next request
    sleep 2
}

# Main execution
echo -e "===================================================="
echo -e "     7-ELEVEN APN TEST SCRIPT                      "
echo -e "===================================================="
echo -e "Testing with order $ORDER_NO on $APN_URL"

# Scenario 1: Payment Waiting
send_apn_notification "A" "PAYMENT WAITING"

# Scenario 2: Payment Completed
send_apn_notification "B" "PAYMENT COMPLETED"

# Scenario 3: Payment Expired
send_apn_notification "D" "PAYMENT EXPIRED"

echo -e "\nTest completed! All notifications sent successfully."
```

## Running the Tests

### Python Script

1. Make the script executable:
   ```
   chmod +x apn-simple-test.py
   ```

2. Run the script:
   ```
   ./apn-simple-test.py
   ```

### JavaScript Script

1. Install dependencies:
   ```
   npm install axios crypto fs path
   ```

2. Run the script:
   ```
   node test-apn-existing-order.js
   ```

### Bash Script

1. Make the script executable:
   ```
   chmod +x simple-apn-test.sh
   ```

2. Run the script:
   ```
   ./simple-apn-test.sh
   ```

## Expected Results

For each scenario, the script should:

1. Generate a valid MD5 checksum
2. Create and send a properly formatted JSON payload
3. Receive a HTTP 200 response with "OK" as the body
4. Update the order status accordingly

## Verifying Test Results

After running the tests, you can verify the changes to the order by checking the order details:

1. Order status should be "cancelled" (from the last test - expired payment)
2. Payment status should be "expired"
3. isExpired flag should be set to true
4. expiryReason should be "payment_expired"

## Troubleshooting

### Common Issues

1. **Invalid JSON Error**
   - Check the format of the payment_detail field - it should be a JSON string in some implementations
   - Make sure all quotes are properly escaped in your JSON payload

2. **Invalid Checksum Error**
   - Ensure the message format is exactly: `api_id:trans_id:amount:status:nonce`
   - Check that you're using MD5 for the hash algorithm
   - Verify that all parameters match exactly with what's expected

3. **Order Not Found Error**
   - Verify that the order ID and transaction ID are correct
   - Ensure the order exists in the database

## Notes

- The APN system requires valid checksums to verify the authenticity of the requests
- Each request should use a unique nonce to prevent replay attacks
- The order of parameters in the checksum calculation is critical
- Order data changes with each test, so running all tests will result in the order having the final (expired) status

---

**Document Created**: April 24, 2025  
**Last Updated**: April 24, 2025 