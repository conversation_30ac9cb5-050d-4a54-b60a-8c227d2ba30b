const axios = require('axios');
const { AES, enc, HmacSHA256, mode, pad } = require('crypto-js');

// Official SinoPAC API credentials
const SINOPAC_API = {
  MERCHANT_ID: 'NA0511_001',
  A1_KEY: 'F342DAABD58249D8',
  A2_KEY: 'D3E28D4E9A4E4EE2',
  B1_KEY: 'C61852BEBDA44676',
  B2_KEY: '1BD9BDB007E34418',
  X_KEY: 'b5e6986d-8636-4aa0-8c93-441ad14b2098',
  SANDBOX_ENDPOINT: 'https://sandbox.sinopac.com/QPay.WebAPI/api'
};

// Format date for SinoPAC API (Taiwan timezone)
const formatSinoPacDate = (date) => {
  const taiwanTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
  const year = taiwanTime.getUTCFullYear();
  const month = String(taiwanTime.getUTCMonth() + 1).padStart(2, '0');
  const day = String(taiwanTime.getUTCDate()).padStart(2, '0');
  const hours = String(taiwanTime.getUTCHours()).padStart(2, '0');
  const minutes = String(taiwanTime.getUTCMinutes()).padStart(2, '0');
  const seconds = String(taiwanTime.getUTCSeconds()).padStart(2, '0');
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
};

// Different encryption approaches
const encryptApproaches = {
  // Current approach - sorted keys
  approach1: (data) => {
    const jsonData = JSON.stringify(data, Object.keys(data).sort());
    const key = enc.Hex.parse(SINOPAC_API.A1_KEY);
    const iv = enc.Hex.parse(SINOPAC_API.A2_KEY);
    const encrypted = AES.encrypt(jsonData, key, { 
      iv: iv, mode: mode.CBC, padding: pad.Pkcs7
    });
    return encrypted.toString().replace(/=+$/, '');
  },
  
  // Natural key order
  approach2: (data) => {
    const jsonData = JSON.stringify(data);
    const key = enc.Hex.parse(SINOPAC_API.A1_KEY);
    const iv = enc.Hex.parse(SINOPAC_API.A2_KEY);
    const encrypted = AES.encrypt(jsonData, key, { 
      iv: iv, mode: mode.CBC, padding: pad.Pkcs7
    });
    return encrypted.toString().replace(/=+$/, '');
  },
  
  // With padding
  approach3: (data) => {
    const jsonData = JSON.stringify(data, Object.keys(data).sort());
    const key = enc.Hex.parse(SINOPAC_API.A1_KEY);
    const iv = enc.Hex.parse(SINOPAC_API.A2_KEY);
    const encrypted = AES.encrypt(jsonData, key, { 
      iv: iv, mode: mode.CBC, padding: pad.Pkcs7
    });
    return encrypted.toString(); // Keep padding
  }
};

// Different HMAC approaches
const hmacApproaches = {
  // Current approach - HMAC of encrypted data
  approach1: (encryptedData, baseRequest) => {
    return HmacSHA256(encryptedData, SINOPAC_API.B1_KEY).toString(enc.Hex).toUpperCase();
  },
  
  // HMAC of full request string
  approach2: (encryptedData, baseRequest) => {
    const requestString = Object.keys(baseRequest)
      .sort()
      .map(key => `${key}=${baseRequest[key]}`)
      .join('&');
    return HmacSHA256(requestString, SINOPAC_API.B1_KEY).toString(enc.Hex).toUpperCase();
  },
  
  // HMAC of request JSON
  approach3: (encryptedData, baseRequest) => {
    const requestJson = JSON.stringify(baseRequest, Object.keys(baseRequest).sort());
    return HmacSHA256(requestJson, SINOPAC_API.B1_KEY).toString(enc.Hex).toUpperCase();
  },
  
  // HMAC with B2 key instead of B1
  approach4: (encryptedData, baseRequest) => {
    return HmacSHA256(encryptedData, SINOPAC_API.B2_KEY).toString(enc.Hex).toUpperCase();
  },
  
  // HMAC lowercase
  approach5: (encryptedData, baseRequest) => {
    return HmacSHA256(encryptedData, SINOPAC_API.B1_KEY).toString(enc.Hex).toLowerCase();
  }
};

async function testComprehensiveApproaches() {
  const timestamp = formatSinoPacDate(new Date());
  const orderNo = `MAG-COMP-${Date.now()}`;
  
  console.log('=== COMPREHENSIVE SINOPAC API TEST ===');
  console.log('Testing multiple encryption and HMAC approaches...\n');
  
  // Base payload
  const payload = {
    Amount: 100,
    CurrencyID: "TWD",
    CustomerAddress: "Test Address",
    CustomerEmail: "<EMAIL>",
    CustomerName: "Test Customer",
    CustomerPhone: "0912345678",
    DueDate: "",
    Memo: "Comprehensive test",
    OrderDesc: "Testing all approaches",
    OrderNo: orderNo,
    PayType: "A",
    PrdtName: "Test Product",
    ReturnURL: "http://localhost:3000/api/payment/sinopac-callback",
    ShopNo: SINOPAC_API.MERCHANT_ID,
    ShowType: "1",
    TimeStamp: timestamp
  };
  
  let testNumber = 1;
  
  // Test all combinations of encryption and HMAC approaches
  for (const [encName, encFunc] of Object.entries(encryptApproaches)) {
    for (const [hmacName, hmacFunc] of Object.entries(hmacApproaches)) {
      console.log(`--- Test ${testNumber}: ${encName} + ${hmacName} ---`);
      
      try {
        // Encrypt data
        const encryptedData = encFunc(payload);
        
        // Create base request
        const baseRequestData = {
          APIService: "Order",
          EncryptData: encryptedData,
          MerchantID: SINOPAC_API.MERCHANT_ID,
          Message: "Payment request",
          Version: "1.0.0"
        };
        
        // Generate HMAC
        const hmacData = hmacFunc(encryptedData, baseRequestData);
        
        // Final request
        const requestData = {
          ...baseRequestData,
          HashData: hmacData
        };
        
        console.log(`Encrypted length: ${encryptedData.length}`);
        console.log(`HMAC: ${hmacData}`);
        
        // Make API request
        const response = await axios.post(
          `${SINOPAC_API.SANDBOX_ENDPOINT}/Order`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
              'X-KeyId': SINOPAC_API.X_KEY,
              'Accept': 'application/json'
            },
            timeout: 10000
          }
        );
        
        console.log('🎉 SUCCESS!');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        
        // If successful, we found the right approach
        if (response.data.Status === 'S') {
          console.log(`\n✅ FOUND WORKING APPROACH: ${encName} + ${hmacName}`);
          return;
        }
        
      } catch (error) {
        if (error.response) {
          console.log(`❌ Error: ${error.response.data}`);
        } else {
          console.log(`❌ Network Error: ${error.message}`);
        }
      }
      
      console.log('');
      testNumber++;
      
      // Add small delay between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  console.log('All approaches tested. None successful with current credentials.');
}

// Test different merchant ID formats as well
async function testMerchantIdFormats() {
  console.log('\n=== TESTING DIFFERENT MERCHANT ID FORMATS ===');
  
  const merchantFormats = [
    'NA0511_001',  // Current
    'NA0511001',   // Without underscore
    'na0511_001',  // Lowercase
    'na0511001',   // Lowercase without underscore
  ];
  
  const timestamp = formatSinoPacDate(new Date());
  
  for (const merchantId of merchantFormats) {
    console.log(`\n--- Testing Merchant ID: ${merchantId} ---`);
    
    const payload = {
      Amount: 100,
      CurrencyID: "TWD",
      CustomerAddress: "Test Address",
      CustomerEmail: "<EMAIL>",
      CustomerName: "Test Customer",
      CustomerPhone: "0912345678",
      DueDate: "",
      Memo: "Merchant ID test",
      OrderDesc: "Testing merchant ID format",
      OrderNo: `MAG-MID-${Date.now()}`,
      PayType: "A",
      PrdtName: "Test Product",
      ReturnURL: "http://localhost:3000/api/payment/sinopac-callback",
      ShopNo: merchantId,
      ShowType: "1",
      TimeStamp: timestamp
    };
    
    try {
      const encryptedData = encryptApproaches.approach1(payload);
      
      const baseRequestData = {
        APIService: "Order",
        EncryptData: encryptedData,
        MerchantID: merchantId,
        Message: "Payment request",
        Version: "1.0.0"
      };
      
      const hmacData = hmacApproaches.approach1(encryptedData, baseRequestData);
      
      const requestData = {
        ...baseRequestData,
        HashData: hmacData
      };
      
      const response = await axios.post(
        `${SINOPAC_API.SANDBOX_ENDPOINT}/Order`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-KeyId': SINOPAC_API.X_KEY,
            'Accept': 'application/json'
          },
          timeout: 10000
        }
      );
      
      console.log('🎉 SUCCESS with merchant ID:', merchantId);
      console.log('Response:', JSON.stringify(response.data, null, 2));
      
    } catch (error) {
      if (error.response) {
        console.log(`❌ Error with ${merchantId}: ${error.response.data}`);
      } else {
        console.log(`❌ Network Error with ${merchantId}: ${error.message}`);
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// Run all tests
async function runAllTests() {
  try {
    await testComprehensiveApproaches();
    await testMerchantIdFormats();
  } catch (error) {
    console.error('Test failed:', error);
  }
}

runAllTests(); 