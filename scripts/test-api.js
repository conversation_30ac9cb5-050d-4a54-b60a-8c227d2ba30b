// test-api.js
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load API key directly from .env file - use the known value as fallback
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Make sure we have the API key - hardcode as last resort
const API_KEY = process.env.API_KEY || 
                process.env.NEXT_PUBLIC_API_KEY || 
                process.env.ADMIN_API_KEY || 
                "uX6HsVoPhmapndxrUhDn"; // Hardcoded fallback from .env

// Base URL - change this to your server's URL
const BASE_URL = 'http://localhost:3000';

// Create axios instance with API key in headers
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': API_KEY
  }
});

console.log('API Key being used:', API_KEY);

// Helper to log responses
const logResponse = (title, response) => {
  console.log('\n==========================');
  console.log(`✅ ${title} - Status: ${response.status}`);
  console.log('Response:');
  console.log(JSON.stringify(response.data, null, 2));
  console.log('==========================\n');
  return response.data;
};

// Helper to log errors
const logError = (title, error) => {
  console.error('\n==========================');
  console.error(`❌ ${title} - Error`);
  if (error.response) {
    console.error(`Status: ${error.response.status}`);
    console.error('Response:');
    console.error(JSON.stringify(error.response.data, null, 2));
  } else {
    console.error(error.message);
  }
  console.error('==========================\n');
};

// Test all API endpoints
const testApi = async () => {
  console.log('🔍 Starting API tests...');
  console.log(`Using API key: ${API_KEY}`);
  
  // Test variables to store created IDs
  let customerId;
  let orderId;
  
  try {
    // 1. Test customer creation
    console.log('Testing customer creation...');
    const customerData = {
      name: 'Test Customer',
      email: `test${Date.now()}@example.com`,
      password: 'testPassword123',
      phone: '************',
      address: '123 Test Street, Test City, 12345'
    };
    
    try {
      console.log('Sending customer data:', JSON.stringify(customerData));
      const createCustomerResponse = await api.post('/api/customers', customerData);
      console.log('Raw customer response:', createCustomerResponse.data);
      const customerResult = logResponse('Create Customer', createCustomerResponse);
      
      // Try to extract customer ID from various possible response formats
      customerId = customerResult.id || customerResult.customerId;
      console.log('Extracted customer ID:', customerId);
      
      if (customerId) {
        console.log(`Created customer with ID: ${customerId}`);
      } else {
        console.error('Failed to extract customer ID from response:', customerResult);
        
        // If we couldn't extract the ID but the customer was created, use a default
        if (createCustomerResponse.status === 201) {
          customerId = 'CUST001';
          console.log(`Using default customer ID: ${customerId}`);
        }
      }
    } catch (error) {
      logError('Create Customer', error);
    }
    
    // 2. Test get all customers
    console.log('Testing get all customers...');
    try {
      const getAllCustomersResponse = await api.get('/api/customers');
      logResponse('Get All Customers', getAllCustomersResponse);
    } catch (error) {
      logError('Get All Customers', error);
    }
    
    // 3. Test get customer by ID
    if (customerId) {
      console.log(`Testing get customer by ID (${customerId})...`);
      try {
        const getCustomerResponse = await api.get(`/api/customers/${customerId}`);
        logResponse('Get Customer by ID', getCustomerResponse);
      } catch (error) {
        logError('Get Customer by ID', error);
      }
    }
    
    // 4. Test update customer
    if (customerId) {
      console.log(`Testing update customer (${customerId})...`);
      const updateData = {
        name: 'Updated Test Customer',
        phone: '************'
      };
      
      try {
        const updateCustomerResponse = await api.put(`/api/customers/${customerId}`, updateData);
        logResponse('Update Customer', updateCustomerResponse);
      } catch (error) {
        logError('Update Customer', error);
      }
    }
    
    // 5. Test create order
    if (customerId) {
      console.log('Testing order creation...');
      const orderData = {
        customerId,
        items: [
          {
            sku: 'TEST-001',
            name: 'Test Product 1',
            price: 19.99,
            quantity: 2
          },
          {
            sku: 'TEST-002',
            name: 'Test Product 2',
            price: 29.99,
            quantity: 1
          }
        ],
        billingAddress: '123 Test Street, Test City, 12345',
        paymentMethod: 'credit_card',
        currency: 'USD'
      };
      
      try {
        const createOrderResponse = await api.post('/api/orders', orderData);
        const orderResult = logResponse('Create Order', createOrderResponse);
        orderId = orderResult.orderId || orderResult.order?.id;
      } catch (error) {
        logError('Create Order', error);
      }
    }
    
    if (orderId) {
      console.log(`Created order with ID: ${orderId}`);
    }
    
    // 6. Test get all orders
    console.log('Testing get all orders...');
    try {
      const getAllOrdersResponse = await api.get('/api/orders');
      logResponse('Get All Orders', getAllOrdersResponse);
    } catch (error) {
      logError('Get All Orders', error);
    }
    
    // 7. Test get order by ID
    if (orderId) {
      console.log(`Testing get order by ID (${orderId})...`);
      try {
        const getOrderResponse = await api.get(`/api/orders/${orderId}`);
        logResponse('Get Order by ID', getOrderResponse);
      } catch (error) {
        logError('Get Order by ID', error);
      }
    }
    
    // 8. Test get orders by customer ID
    if (customerId) {
      console.log(`Testing get orders by customer ID (${customerId})...`);
      try {
        const getCustomerOrdersResponse = await api.get(`/api/customers/${customerId}/orders`);
        logResponse('Get Customer Orders', getCustomerOrdersResponse);
      } catch (error) {
        logError('Get Orders by Customer ID', error);
      }
    }
    
    // 9. Test alternative endpoint for customer orders
    if (customerId) {
      console.log(`Testing alternative endpoint for customer orders (${customerId})...`);
      try {
        const getCustomerOrdersAltResponse = await api.get(`/api/orders/customer/${customerId}`);
        logResponse('Get Orders by Customer ID (Alt)', getCustomerOrdersAltResponse);
      } catch (error) {
        logError('Get Orders by Customer ID (Alt)', error);
      }
    }
    
    // 10. Test update order
    if (orderId) {
      console.log(`Testing update order (${orderId})...`);
      const updateOrderData = {
        status: 'processing',
        notes: 'Customer called to confirm order details'
      };
      
      try {
        const updateOrderResponse = await api.put(`/api/orders/${orderId}`, updateOrderData);
        logResponse('Update Order', updateOrderResponse);
      } catch (error) {
        logError('Update Order', error);
      }
    }
    
    // 11. Test update order items
    if (orderId) {
      console.log(`Testing update order items (${orderId})...`);
      const updateItemsData = {
        items: [
          {
            sku: 'TEST-001',
            name: 'Test Product 1',
            price: 19.99,
            quantity: 3 // Increased quantity
          },
          {
            sku: 'TEST-002',
            name: 'Test Product 2',
            price: 29.99,
            quantity: 1
          }
        ]
      };
      
      try {
        const updateItemsResponse = await api.put(`/api/orders/${orderId}`, updateItemsData);
        logResponse('Update Order Items', updateItemsResponse);
      } catch (error) {
        logError('Update Order Items', error);
      }
    }
    
    // 12. Test delete order (optional - comment out if you want to keep the test order)
    if (orderId) {
      console.log(`Testing delete order (${orderId})...`);
      try {
        const deleteOrderResponse = await api.delete(`/api/orders/${orderId}`);
        logResponse('Delete Order', deleteOrderResponse);
        orderId = null;
      } catch (error) {
        logError('Delete Order', error);
      }
    }
    
    // 13. Test delete customer (optional - comment out if you want to keep the test customer)
    if (customerId) {
      console.log(`Testing delete customer (${customerId})...`);
      try {
        const deleteCustomerResponse = await api.delete(`/api/customers/${customerId}`);
        logResponse('Delete Customer', deleteCustomerResponse);
        customerId = null;
      } catch (error) {
        logError('Delete Customer', error);
      }
    }
    
    console.log('🎉 API testing completed!');
    
  } catch (error) {
    console.error('❌ Fatal error during API testing:');
    console.error(error);
  }
};

// Run the tests
testApi();
