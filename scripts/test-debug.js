// Test debug path endpoint
const axios = require('axios');

const API_KEY = 'uX6HsVoPhmapndxrUhDn';
const BASE_URL = 'http://localhost:3000';

async function testDebugPath() {
  console.log('Testing debug path endpoint');
  
  try {
    console.log('Testing GET /api/debug-path');
    const response = await axios.get(`${BASE_URL}/api/debug-path`, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Message:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

testDebugPath().catch(console.error); 