#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readline = require('readline');

// Promisify fs functions
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const copyFile = promisify(fs.copyFile);
const mkdir = promisify(fs.mkdir);

// Configuration
const CONFIG = {
  dataDir: path.join(__dirname, '..', 'data'),
  logsDir: path.join(__dirname, '..', 'logs'),
  backupDir: path.join(__dirname, '..', 'backups'),
  documentsDir: path.join(__dirname, '..', 'data', 'documents'),
  customersDir: path.join(__dirname, '..', 'data', 'customers'),
  cartsDir: path.join(__dirname, '..', 'data', 'carts'),
  callbacksDir: path.join(__dirname, '..', 'data', 'callbacks'),
  dataLogsDir: path.join(__dirname, '..', 'data', 'logs'),
  apnTestLogsDir: path.join(__dirname, '..', 'apn-test-logs'),
  
  // File paths
  ordersFile: path.join(__dirname, '..', 'data', 'orders.json'),
  customersFile: path.join(__dirname, '..', 'data', 'customers.json'),
  staffFile: path.join(__dirname, '..', 'data', 'staff.json'),
  // Legacy callback log files (keeping for backward compatibility and migration)
  // Legacy callback log files (deprecated - callbacks now stored in orders.json)
  logsApnFile: path.join(__dirname, '..', 'data', 'callbacks', 'logsapn.json'),
  logsFamilyMartFile: path.join(__dirname, '..', 'data', 'callbacks', 'logsFamilyMart.json'),
  
  // Cleanup thresholds (in days)
  oldOrdersThreshold: 180,      // 6 months
  oldLogsThreshold: 30,         // 1 month
  oldCartsThreshold: 7,         // 1 week
  expiredOrdersThreshold: 30,   // 1 month
  testOrdersThreshold: 7,       // 1 week
  orphanedCustomersThreshold: 365, // 1 year
  oldCallbackLogsThreshold: 90  // 3 months for payment callback logs
};

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Utility functions
const question = (query) => new Promise(resolve => rl.question(query, resolve));

const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
};

const ensureDir = async (dirPath) => {
  try {
    await mkdir(dirPath, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') throw error;
  }
};

const readJsonFile = async (filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      log(`File not found: ${filePath}`, 'warning');
      return null;
    }
    const data = await fs.promises.readFile(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    log(`Error reading ${filePath}: ${error.message}`, 'error');
    return null;
  }
};

const writeJsonFile = async (filePath, data) => {
  try {
    await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    log(`Error writing ${filePath}: ${error.message}`, 'error');
    return false;
  }
};

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDaysAgo = (dateString) => {
  const now = new Date();
  const date = new Date(dateString);
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return `${diffDays} days ago`;
};

// Database management functions
class DatabaseManager {
  constructor() {
    this.stats = {
      cleanedOrders: 0,
      cleanedCustomers: 0,
      cleanedLogs: 0,
      backupSize: 0,
      totalSpaceSaved: 0
    };
  }

  async init() {
    log('Initializing Database Manager...');
    await ensureDir(CONFIG.backupDir);
    await ensureDir(CONFIG.dataDir);
    await ensureDir(CONFIG.logsDir);
  }

  // BACKUP OPERATIONS
  async createBackup(type = 'full') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `backup-${type}-${timestamp}`;
    const backupPath = path.join(CONFIG.backupDir, backupName);
    
    await ensureDir(backupPath);
    let totalSize = 0;
    const backedUpFiles = [];

    try {
      log(`Creating ${type} backup: ${backupName}`);
      
      if (type === 'full') {
        // Backup all data files
        const files = ['orders.json', 'customers.json', 'staff.json'];
        log('📄 Backing up data files...');
        for (const file of files) {
          const srcPath = path.join(CONFIG.dataDir, file);
          const destPath = path.join(backupPath, file);
          if (fs.existsSync(srcPath)) {
            await copyFile(srcPath, destPath);
            const stats = await stat(srcPath);
            totalSize += stats.size;
            backedUpFiles.push(`${file} (${formatBytes(stats.size)})`);
            log(`  ✓ ${file}`);
          }
        }

        // Backup directories
        const dirs = ['customers', 'carts', 'callbacks', 'logs', 'documents'];
        log('📁 Backing up directories...');
        for (const dir of dirs) {
          const srcDir = path.join(CONFIG.dataDir, dir);
          const destDir = path.join(backupPath, dir);
          if (fs.existsSync(srcDir)) {
            const dirFiles = await this.getDirectoryFileList(srcDir);
            if (dirFiles.length > 0) {
              await this.copyDirectory(srcDir, destDir);
              const dirSize = await this.getDirectorySize(srcDir);
              totalSize += dirSize;
              backedUpFiles.push(`${dir}/ (${dirFiles.length} files, ${formatBytes(dirSize)})`);
              log(`  ✓ ${dir}/ (${dirFiles.length} files)`);
            } else {
              log(`  ⚠ ${dir}/ (empty)`);
            }
          }
        }

        // Backup main logs
        if (fs.existsSync(CONFIG.logsDir)) {
          log('📋 Backing up main logs...');
          const destLogsDir = path.join(backupPath, 'main-logs');
          const logFiles = await this.getDirectoryFileList(CONFIG.logsDir);
          if (logFiles.length > 0) {
            await this.copyDirectory(CONFIG.logsDir, destLogsDir);
            const logSize = await this.getDirectorySize(CONFIG.logsDir);
            totalSize += logSize;
            backedUpFiles.push(`main-logs/ (${logFiles.length} files, ${formatBytes(logSize)})`);
            log(`  ✓ main-logs/ (${logFiles.length} files)`);
          } else {
            log(`  ⚠ main-logs/ (empty)`);
          }
        }

      } else if (type === 'data') {
        // Backup only data files
        const files = ['orders.json', 'customers.json', 'staff.json'];
        log('📄 Backing up data files...');
        for (const file of files) {
          const srcPath = path.join(CONFIG.dataDir, file);
          const destPath = path.join(backupPath, file);
          if (fs.existsSync(srcPath)) {
            await copyFile(srcPath, destPath);
            const stats = await stat(srcPath);
            totalSize += stats.size;
            backedUpFiles.push(`${file} (${formatBytes(stats.size)})`);
            log(`  ✓ ${file}`);
          }
        }
      } else if (type === 'logs') {
        // Backup only logs
        const dirs = [CONFIG.logsDir, CONFIG.dataLogsDir, CONFIG.apnTestLogsDir];
        log('📋 Backing up log directories...');
        for (const srcDir of dirs) {
          if (fs.existsSync(srcDir)) {
            const dirName = path.basename(srcDir);
            const destDir = path.join(backupPath, dirName);
            const logFiles = await this.getDirectoryFileList(srcDir);
            if (logFiles.length > 0) {
              await this.copyDirectory(srcDir, destDir);
              const dirSize = await this.getDirectorySize(srcDir);
              totalSize += dirSize;
              backedUpFiles.push(`${dirName}/ (${logFiles.length} files, ${formatBytes(dirSize)})`);
              log(`  ✓ ${dirName}/ (${logFiles.length} files)`);
            } else {
              log(`  ⚠ ${dirName}/ (empty)`);
            }
          }
        }
      }

      // Create backup manifest
      const manifest = {
        type,
        timestamp: new Date().toISOString(),
        size: totalSize,
        files: await this.getBackupManifest(backupPath)
      };
      await writeJsonFile(path.join(backupPath, 'manifest.json'), manifest);

      this.stats.backupSize = totalSize;
      
      // Show summary
      log('📦 Backup Summary:', 'success');
      backedUpFiles.forEach(file => log(`  • ${file}`));
      log(`Backup created: ${backupName} (${formatBytes(totalSize)})`, 'success');
      
      return backupPath;

    } catch (error) {
      log(`Backup failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async getDirectoryFileList(dirPath) {
    try {
      const files = await readdir(dirPath, { withFileTypes: true });
      return files.filter(file => !file.name.startsWith('.')).map(file => file.name);
    } catch (error) {
      return [];
    }
  }

  async copyDirectory(src, dest) {
    await ensureDir(dest);
    const entries = await readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await copyFile(srcPath, destPath);
      }
    }
  }

  async getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const entries = await readdir(dirPath, { withFileTypes: true });
      for (const entry of entries) {
        const entryPath = path.join(dirPath, entry.name);
        if (entry.isDirectory()) {
          totalSize += await this.getDirectorySize(entryPath);
        } else {
          const stats = await stat(entryPath);
          totalSize += stats.size;
        }
      }
    } catch (error) {
      // Directory might not exist or be accessible
    }
    
    return totalSize;
  }

  async getBackupManifest(backupPath) {
    const manifest = [];
    const entries = await readdir(backupPath, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.name === 'manifest.json') continue;
      
      const entryPath = path.join(backupPath, entry.name);
      const stats = await stat(entryPath);
      
      manifest.push({
        name: entry.name,
        type: entry.isDirectory() ? 'directory' : 'file',
        size: entry.isDirectory() ? await this.getDirectorySize(entryPath) : stats.size,
        modified: stats.mtime.toISOString()
      });
    }
    
    return manifest;
  }

  async listBackups() {
    try {
      const backups = await readdir(CONFIG.backupDir, { withFileTypes: true });
      const backupList = [];
      
      for (const backup of backups) {
        if (backup.isDirectory()) {
          const manifestPath = path.join(CONFIG.backupDir, backup.name, 'manifest.json');
          const manifest = await readJsonFile(manifestPath);
          
          if (manifest) {
            // Get detailed statistics for this backup
            const stats = await this.getBackupStatistics(path.join(CONFIG.backupDir, backup.name));
            
            backupList.push({
              name: backup.name,
              ...manifest,
              statistics: stats
            });
          }
        }
      }
      
      return backupList.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      log(`Error listing backups: ${error.message}`, 'error');
      return [];
    }
  }

  async getBackupStatistics(backupPath) {
    const stats = {
      orders: 0,
      customers: 0,
      staff: 0,
      apnLogs: 0,
      cartFiles: 0,
      logFiles: 0,
      documentFiles: 0,
      callbackFiles: 0,
      customerFiles: 0,
      files: {
        dataFiles: [],
        cartFiles: [],
        logFiles: [],
        documentFiles: [],
        callbackFiles: [],
        customerFiles: [],
        mainLogFiles: []
      }
    };

    try {
      // Analyze main data files
      const dataFiles = ['orders.json', 'customers.json', 'staff.json'];
      for (const file of dataFiles) {
        const filePath = path.join(backupPath, file);
        if (fs.existsSync(filePath)) {
          stats.files.dataFiles.push(file);
          
          // Get record counts
          const data = await readJsonFile(filePath);
          if (file === 'orders.json') stats.orders = data ? data.length : 0;
          if (file === 'customers.json') stats.customers = data ? data.length : 0;
          if (file === 'staff.json') stats.staff = data ? data.length : 0;
        }
      }

      // Analyze payment callbacks from orders (new centralized system)
      if (stats.orders > 0) {
        const ordersPath = path.join(backupPath, 'orders.json');
        if (fs.existsSync(ordersPath)) {
          const orders = await readJsonFile(ordersPath);
          if (orders && Array.isArray(orders)) {
            let totalCallbacks = 0;
            orders.forEach(order => {
              if (order.paymentCallbacks && Array.isArray(order.paymentCallbacks)) {
                totalCallbacks += order.paymentCallbacks.length;
              }
            });
            stats.paymentCallbacks = totalCallbacks;
          }
        }
      }
      
      // Legacy APN logs (for backward compatibility with old backups)
      const apnLogsPath = path.join(backupPath, 'callbacks', 'logsapn.json');
      if (fs.existsSync(apnLogsPath)) {
        const apnLogs = await readJsonFile(apnLogsPath);
        stats.apnLogs = apnLogs ? apnLogs.length : 0;
      }

      // Get detailed file listings for directories
      const directories = [
        { name: 'carts', stat: 'cartFiles', filesList: 'cartFiles' },
        { name: 'logs', stat: 'logFiles', filesList: 'logFiles' },
        { name: 'documents', stat: 'documentFiles', filesList: 'documentFiles' },
        { name: 'callbacks', stat: 'callbackFiles', filesList: 'callbackFiles' },
        { name: 'customers', stat: 'customerFiles', filesList: 'customerFiles' }
      ];

      for (const dir of directories) {
        const dirPath = path.join(backupPath, dir.name);
        if (fs.existsSync(dirPath)) {
          try {
            const files = await readdir(dirPath);
            const filteredFiles = files.filter(file => !file.startsWith('.'));
            stats[dir.stat] = filteredFiles.length;
            stats.files[dir.filesList] = filteredFiles;
          } catch (error) {
            // Directory might not be readable, keep count at 0
          }
        }
      }

      // Get main log files
      const mainLogsPath = path.join(backupPath, 'main-logs');
      if (fs.existsSync(mainLogsPath)) {
        try {
          const files = await readdir(mainLogsPath);
          const filteredFiles = files.filter(file => !file.startsWith('.'));
          stats.logFiles += filteredFiles.length;
          stats.files.mainLogFiles = filteredFiles;
        } catch (error) {
          // Directory might not be readable
        }
      }

    } catch (error) {
      log(`Error getting backup statistics for ${backupPath}: ${error.message}`, 'error');
    }

    return stats;
  }

  async restoreBackup(backupName) {
    const backupPath = path.join(CONFIG.backupDir, backupName);
    const manifestPath = path.join(backupPath, 'manifest.json');
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup not found: ${backupName}`);
    }
    
    const manifest = await readJsonFile(manifestPath);
    if (!manifest) {
      throw new Error(`Invalid backup: manifest not found`);
    }
    
    log(`Restoring backup: ${backupName} (${manifest.type})`);
    
    try {
      // Restore files based on backup type
      if (manifest.type === 'full' || manifest.type === 'data') {
        const dataFiles = ['orders.json', 'customers.json', 'staff.json'];
        for (const file of dataFiles) {
          const srcPath = path.join(backupPath, file);
          const destPath = path.join(CONFIG.dataDir, file);
          if (fs.existsSync(srcPath)) {
            await copyFile(srcPath, destPath);
            log(`Restored ${file}`);
          }
        }
      }
      
      if (manifest.type === 'full') {
        // Restore directories
        const dirs = ['customers', 'carts', 'callbacks', 'logs', 'documents'];
        for (const dir of dirs) {
          const srcDir = path.join(backupPath, dir);
          const destDir = path.join(CONFIG.dataDir, dir);
          if (fs.existsSync(srcDir)) {
            await this.copyDirectory(srcDir, destDir);
            log(`Restored ${dir}/`);
          }
        }
        
        // Restore main logs
        const srcLogsDir = path.join(backupPath, 'main-logs');
        if (fs.existsSync(srcLogsDir)) {
          await this.copyDirectory(srcLogsDir, CONFIG.logsDir);
          log(`Restored main logs`);
        }
      }
      
      log(`Backup restored successfully: ${backupName}`, 'success');
      
    } catch (error) {
      log(`Restore failed: ${error.message}`, 'error');
      throw error;
    }
  }

  // CLEANUP OPERATIONS
  async cleanupExpiredOrders(dryRun = false) {
    const orders = await readJsonFile(CONFIG.ordersFile);
    if (!orders) return 0;

    const now = new Date();
    const threshold = new Date(now.getTime() - (CONFIG.expiredOrdersThreshold * 24 * 60 * 60 * 1000));
    
    const expiredOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return (order.status === 'expired' || order.status === 'cancelled') && orderDate < threshold;
    });
    
    if (dryRun) {
      log(`Found ${expiredOrders.length} expired orders to clean (older than ${CONFIG.expiredOrdersThreshold} days)`);
      return expiredOrders.length;
    }
    
    const cleanedOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return !((order.status === 'expired' || order.status === 'cancelled') && orderDate < threshold);
    });
    
    if (await writeJsonFile(CONFIG.ordersFile, cleanedOrders)) {
      this.stats.cleanedOrders = expiredOrders.length;
      log(`Cleaned ${expiredOrders.length} expired orders`, 'success');
      return expiredOrders.length;
    }
    
    return 0;
  }

  async cleanupTestOrders(dryRun = false) {
    const orders = await readJsonFile(CONFIG.ordersFile);
    if (!orders) return 0;

    const testOrders = orders.filter(order => 
      order.customerEmail && (
        order.customerEmail.includes('test') ||
        order.customerEmail.includes('example') ||
        order.customerPhone === '0000000000' ||
        order.amount === 0 ||
        order.storeId === 'test'
      )
    );
    
    if (dryRun) {
      log(`Found ${testOrders.length} test orders to clean`);
      return testOrders.length;
    }
    
    const cleanedOrders = orders.filter(order => 
      !(order.customerEmail && (
        order.customerEmail.includes('test') ||
        order.customerEmail.includes('example') ||
        order.customerPhone === '0000000000' ||
        order.amount === 0 ||
        order.storeId === 'test'
      ))
    );
    
    if (await writeJsonFile(CONFIG.ordersFile, cleanedOrders)) {
      this.stats.cleanedOrders += testOrders.length;
      log(`Cleaned ${testOrders.length} test orders`, 'success');
      return testOrders.length;
    }
    
    return 0;
  }

  async cleanupOrphanedCustomers(dryRun = false) {
    const customers = await readJsonFile(CONFIG.customersFile);
    const orders = await readJsonFile(CONFIG.ordersFile);
    
    if (!customers || !orders) return 0;

    const now = new Date();
    const threshold = new Date(now.getTime() - (CONFIG.orphanedCustomersThreshold * 24 * 60 * 60 * 1000));
    
    // Get customer IDs that have orders
    const customerIdsWithOrders = new Set(orders.map(order => order.customerId));
    
    const orphanedCustomers = customers.filter(customer => {
      const customerDate = new Date(customer.lastUpdated || customer.createdAt);
      return !customerIdsWithOrders.has(customer.id) && customerDate < threshold;
    });
    
    if (dryRun) {
      log(`Found ${orphanedCustomers.length} orphaned customers to clean (no orders for ${CONFIG.orphanedCustomersThreshold} days)`);
      return orphanedCustomers.length;
    }
    
    const activeCustomers = customers.filter(customer => {
      const customerDate = new Date(customer.lastUpdated || customer.createdAt);
      return customerIdsWithOrders.has(customer.id) || customerDate >= threshold;
    });
    
    if (await writeJsonFile(CONFIG.customersFile, activeCustomers)) {
      this.stats.cleanedCustomers = orphanedCustomers.length;
      log(`Cleaned ${orphanedCustomers.length} orphaned customers`, 'success');
      return orphanedCustomers.length;
    }
    
    return 0;
  }

  async cleanupOldCarts(dryRun = false) {
    if (!fs.existsSync(CONFIG.cartsDir)) return 0;

    const now = new Date();
    const threshold = new Date(now.getTime() - (CONFIG.oldCartsThreshold * 24 * 60 * 60 * 1000));
    
    try {
      const cartFiles = await readdir(CONFIG.cartsDir);
      let cleanedCount = 0;
      const filesToClean = [];
      
      log(`🛒 Analyzing ${cartFiles.length} cart files...`);
      
      for (const cartFile of cartFiles) {
        const cartPath = path.join(CONFIG.cartsDir, cartFile);
        const stats = await stat(cartPath);
        
        if (stats.mtime < threshold) {
          filesToClean.push(`${cartFile} (${formatDaysAgo(stats.mtime.toISOString())})`);
          if (!dryRun) {
            await fs.promises.unlink(cartPath);
            log(`  ✓ Removed ${cartFile}`);
          }
          cleanedCount++;
        }
      }
      
      if (dryRun) {
        log(`📋 Found ${cleanedCount} old cart files to clean (older than ${CONFIG.oldCartsThreshold} days):`);
        filesToClean.slice(0, 10).forEach(file => log(`  • ${file}`));
        if (filesToClean.length > 10) {
          log(`  ... and ${filesToClean.length - 10} more files`);
        }
      } else {
        log(`Cleaned ${cleanedCount} old cart files`, 'success');
      }
      
      return cleanedCount;
      
    } catch (error) {
      log(`Error cleaning carts: ${error.message}`, 'error');
      return 0;
    }
  }

  async cleanupOldLogs(dryRun = false) {
    const logDirs = [CONFIG.logsDir, CONFIG.dataLogsDir, CONFIG.apnTestLogsDir];
    let totalCleaned = 0;
    
    const now = new Date();
    const threshold = new Date(now.getTime() - (CONFIG.oldLogsThreshold * 24 * 60 * 60 * 1000));
    
    for (const logDir of logDirs) {
      if (!fs.existsSync(logDir)) continue;
      
      try {
        const logFiles = await readdir(logDir);
        let cleanedCount = 0;
        
        for (const logFile of logFiles) {
          const logPath = path.join(logDir, logFile);
          const stats = await stat(logPath);
          
          if (stats.mtime < threshold) {
            if (dryRun) {
              cleanedCount++;
            } else {
              await fs.promises.unlink(logPath);
              cleanedCount++;
            }
          }
        }
        
        totalCleaned += cleanedCount;
        
        if (cleanedCount > 0) {
          const dirName = path.basename(logDir);
          if (dryRun) {
            log(`Found ${cleanedCount} old log files in ${dirName}/ to clean`);
          } else {
            log(`Cleaned ${cleanedCount} old log files from ${dirName}/`, 'success');
          }
        }
        
      } catch (error) {
        log(`Error cleaning logs in ${logDir}: ${error.message}`, 'error');
      }
    }
    
    this.stats.cleanedLogs = totalCleaned;
    return totalCleaned;
  }

  // Legacy function - now redirects to order-based cleanup
  async cleanupOldCallbackLogs(dryRun = false) {
    log('⚠️  Legacy function: Redirecting to order-based callback cleanup', 'warning');
    return await this.cleanupOldCallbacksFromOrders(dryRun);
  }

  // ANALYSIS OPERATIONS
  async analyzeDatabase() {
    log('Analyzing database...');
    
    const analysis = {
      orders: await this.analyzeOrders(),
      customers: await this.analyzeCustomers(),
      logs: await this.analyzeLogs(),
      paymentCallbacks: await this.analyzePaymentCallbacks(),
      storage: await this.analyzeStorage()
    };
    
    return analysis;
  }

  async analyzeOrders() {
    const orders = await readJsonFile(CONFIG.ordersFile);
    if (!orders) return null;
    
    const now = new Date();
    const stats = {
      total: orders.length,
      expired: 0,
      cancelled: 0,
      completed: 0,
      pending: 0,
      testOrders: 0,
      oldOrders: 0,
      recentOrders: 0
    };
    
    orders.forEach(order => {
      // Status analysis
      if (order.status === 'expired') stats.expired++;
      else if (order.status === 'cancelled') stats.cancelled++;
      else if (order.status === 'completed') stats.completed++;
      else stats.pending++;
      
      // Test orders
      if (order.customerEmail && (
        order.customerEmail.includes('test') ||
        order.customerEmail.includes('example') ||
        order.customerPhone === '0000000000' ||
        order.amount === 0
      )) {
        stats.testOrders++;
      }
      
      // Age analysis
      const orderDate = new Date(order.createdAt);
      const daysDiff = (now - orderDate) / (1000 * 60 * 60 * 24);
      
      if (daysDiff > CONFIG.expiredOrdersThreshold) {
        stats.oldOrders++;
      } else if (daysDiff <= 7) {
        stats.recentOrders++;
      }
    });
    
    return stats;
  }

  async analyzeCustomers() {
    const customers = await readJsonFile(CONFIG.customersFile);
    const orders = await readJsonFile(CONFIG.ordersFile);
    
    if (!customers) return null;
    
    const customerOrderCounts = {};
    if (orders) {
      orders.forEach(order => {
        customerOrderCounts[order.customerId] = (customerOrderCounts[order.customerId] || 0) + 1;
      });
    }
    
    const now = new Date();
    const stats = {
      total: customers.length,
      active: 0,
      inactive: 0,
      withOrders: 0,
      withoutOrders: 0,
      recentlyUpdated: 0
    };
    
    customers.forEach(customer => {
      const hasOrders = customerOrderCounts[customer.id] > 0;
      if (hasOrders) {
        stats.withOrders++;
        stats.active++;
      } else {
        stats.withoutOrders++;
        
        const lastUpdate = new Date(customer.lastUpdated || customer.createdAt);
        const daysDiff = (now - lastUpdate) / (1000 * 60 * 60 * 24);
        
        if (daysDiff > CONFIG.orphanedCustomersThreshold) {
          stats.inactive++;
        } else {
          stats.active++;
        }
      }
      
      const lastUpdate = new Date(customer.lastUpdated || customer.createdAt);
      const daysDiff = (now - lastUpdate) / (1000 * 60 * 60 * 24);
      if (daysDiff <= 7) {
        stats.recentlyUpdated++;
      }
    });
    
    return stats;
  }

  async analyzeLogs() {
    const logDirs = [CONFIG.logsDir, CONFIG.dataLogsDir, CONFIG.apnTestLogsDir];
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      oldFiles: 0,
      recentFiles: 0
    };
    
    const now = new Date();
    const threshold = new Date(now.getTime() - (CONFIG.oldLogsThreshold * 24 * 60 * 60 * 1000));
    
    for (const logDir of logDirs) {
      if (!fs.existsSync(logDir)) continue;
      
      try {
        const logFiles = await readdir(logDir);
        
        for (const logFile of logFiles) {
          const logPath = path.join(logDir, logFile);
          const logStats = await stat(logPath);
          
          stats.totalFiles++;
          stats.totalSize += logStats.size;
          
          if (logStats.mtime < threshold) {
            stats.oldFiles++;
          } else {
            stats.recentFiles++;
          }
        }
        
      } catch (error) {
        log(`Error analyzing logs in ${logDir}: ${error.message}`, 'error');
      }
    }
    
    return stats;
  }

  async analyzePaymentCallbacks() {
    const orders = await readJsonFile(CONFIG.ordersFile);
    if (!orders || !Array.isArray(orders)) {
      return null;
    }

    const now = new Date();
    const oldThreshold = new Date(now.getTime() - (CONFIG.oldCallbackLogsThreshold * 24 * 60 * 60 * 1000));
    const recentThreshold = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000)); // 7 days

    const stats = {
      totalOrders: orders.length,
      ordersWithCallbacks: 0,
      totalCallbacks: 0,
      // 7-Eleven APN stats
      apnCallbacks: 0,
      apnTestLogs: 0,
      apnProductionLogs: 0,
      apnStatusA: 0, // Waiting for payment
      apnStatusB: 0, // Paid
      apnStatusD: 0, // Overdue/expired
      apnResponseOK: 0,
      apnResponseNotFound: 0,
      // FamilyMart stats
      familyMartCallbacks: 0,
      familyMartTestLogs: 0,
      familyMartProductionLogs: 0,
      familyMartStatus0: 0, // Success
      familyMartStatus1: 0, // Pending
      familyMartStatus2: 0, // Expired
      familyMartStatus3: 0, // Cancelled
      familyMartResponseSuccess: 0,
      familyMartResponseError: 0,
      // Age and performance
      oldCallbacks: 0,
      recentCallbacks: 0,
      avgProcessingTime: 0
    };

    let totalProcessingTime = 0;

    orders.forEach(order => {
      if (order.paymentCallbacks && Array.isArray(order.paymentCallbacks)) {
        stats.ordersWithCallbacks++;
        stats.totalCallbacks += order.paymentCallbacks.length;

        order.paymentCallbacks.forEach(callback => {
          // Age analysis
          const logDate = new Date(callback.timestamp);
          if (logDate < oldThreshold) {
            stats.oldCallbacks++;
          } else if (logDate > recentThreshold) {
            stats.recentCallbacks++;
          }

          // Performance analysis
          if (callback.processingTimeMs) {
            totalProcessingTime += callback.processingTimeMs;
          }

          // Payment method specific analysis
          if (callback.paymentMethod === '7-eleven' || callback.callbackType === 'apn') {
            stats.apnCallbacks++;

            // Test vs Production for APN
            if (callback.requestPayload && (
              callback.requestPayload.trans_id?.includes('TEST') ||
              callback.requestPayload.order_no?.includes('TEST') ||
              callback.requestPayload.order_no?.includes('EXPIRED') ||
              callback.requestPayload.trans_id?.includes('711apntest') ||
              callback.clientIP === '127.0.0.1' ||
              callback.userAgent === 'Log Test Script'
            )) {
              stats.apnTestLogs++;
            } else {
              stats.apnProductionLogs++;
            }

            // APN Status analysis
            if (callback.requestPayload?.status === 'A') stats.apnStatusA++;
            else if (callback.requestPayload?.status === 'B') stats.apnStatusB++;
            else if (callback.requestPayload?.status === 'D') stats.apnStatusD++;

            // APN Response analysis
            if (callback.responseBody === 'OK') stats.apnResponseOK++;
            else if (callback.responseBody?.includes('Not Found')) stats.apnResponseNotFound++;

          } else if (callback.paymentMethod === 'familymart') {
            stats.familyMartCallbacks++;

            // Test vs Production for FamilyMart
            if (callback.requestPayload && (
              callback.requestPayload.EC_ID?.includes('TEST') ||
              callback.requestPayload.ORDER_NO?.includes('TEST') ||
              callback.requestPayload.ORDER_NO?.includes('FMTEST') ||
              callback.requestPayload.PIN_CODE?.includes('TEST') ||
              callback.clientIP === '127.0.0.1' ||
              callback.userAgent?.includes('test')
            )) {
              stats.familyMartTestLogs++;
            } else {
              stats.familyMartProductionLogs++;
            }

            // FamilyMart Status analysis
            if (callback.requestPayload?.STATUS_CODE === '0') stats.familyMartStatus0++;
            else if (callback.requestPayload?.STATUS_CODE === '1') stats.familyMartStatus1++;
            else if (callback.requestPayload?.STATUS_CODE === '2') stats.familyMartStatus2++;
            else if (callback.requestPayload?.STATUS_CODE === '3') stats.familyMartStatus3++;

            // FamilyMart Response analysis
            if (callback.responseStatus === 200) stats.familyMartResponseSuccess++;
            else stats.familyMartResponseError++;
          }
        });
      }
    });

    stats.avgProcessingTime = stats.totalCallbacks > 0 ? Math.round(totalProcessingTime / stats.totalCallbacks) : 0;

    return stats;
  }



  async cleanupOldCallbacksFromOrders(dryRun = false) {
    const orders = await readJsonFile(CONFIG.ordersFile);
    if (!orders || !Array.isArray(orders)) {
      log('No orders found', 'warning');
      return 0;
    }

    const now = new Date();
    const threshold = new Date(now.getTime() - (CONFIG.oldCallbackLogsThreshold * 24 * 60 * 60 * 1000));
    
    let totalOldCallbacks = 0;
    let modifiedOrders = 0;

    // Analyze which callbacks would be cleaned
    orders.forEach(order => {
      if (order.paymentCallbacks && Array.isArray(order.paymentCallbacks)) {
        const oldCallbacks = order.paymentCallbacks.filter(callback => {
          const logDate = new Date(callback.timestamp);
          return logDate < threshold;
        });
        if (oldCallbacks.length > 0) {
          totalOldCallbacks += oldCallbacks.length;
          modifiedOrders++;
        }
      }
    });

    if (totalOldCallbacks === 0) {
      log('No old callback logs to cleanup from orders', 'info');
      return 0;
    }

    log(`Found ${totalOldCallbacks} old callback logs in ${modifiedOrders} orders (older than ${CONFIG.oldCallbackLogsThreshold} days)`);

    if (dryRun) {
      log(`DRY RUN: Would clean ${totalOldCallbacks} old callback logs from ${modifiedOrders} orders`, 'warning');
      return totalOldCallbacks;
    }

    // Clean old callbacks from orders
    let cleanedCallbacks = 0;
    orders.forEach(order => {
      if (order.paymentCallbacks && Array.isArray(order.paymentCallbacks)) {
        const oldCount = order.paymentCallbacks.length;
        order.paymentCallbacks = order.paymentCallbacks.filter(callback => {
          const logDate = new Date(callback.timestamp);
          return logDate >= threshold;
        });
        cleanedCallbacks += (oldCount - order.paymentCallbacks.length);
      }
    });

    if (await writeJsonFile(CONFIG.ordersFile, orders)) {
      log(`✅ Cleaned ${cleanedCallbacks} old callback logs from ${modifiedOrders} orders`, 'success');
      return cleanedCallbacks;
    } else {
      log('Failed to update orders file', 'error');
      return 0;
    }
  }

  async analyzeStorage() {
    const paths = [
      { name: 'Data', path: CONFIG.dataDir },
      { name: 'Logs', path: CONFIG.logsDir },
      { name: 'Backups', path: CONFIG.backupDir },
      { name: 'Documents', path: CONFIG.documentsDir },
      { name: 'Customers', path: CONFIG.customersDir },
      { name: 'Carts', path: CONFIG.cartsDir }
    ];
    
    const storage = [];
    let totalSize = 0;
    
    for (const { name, path: dirPath } of paths) {
      const size = await this.getDirectorySize(dirPath);
      storage.push({ name, size, path: dirPath });
      totalSize += size;
    }
    
    return { directories: storage, total: totalSize };
  }

  // WIPE OPERATIONS
  async wipeDatabase(type) {
    const confirmation = await question(`Are you sure you want to wipe ${type}? This action cannot be undone! (type 'CONFIRM' to proceed): `);
    
    if (confirmation !== 'CONFIRM') {
      log('Wipe operation cancelled', 'warning');
      return false;
    }
    
    try {
      switch (type) {
        case 'orders':
          await writeJsonFile(CONFIG.ordersFile, []);
          log('Orders database wiped', 'success');
          break;
          
        case 'customers':
          await writeJsonFile(CONFIG.customersFile, []);
          log('Customers database wiped', 'success');
          break;
          
        case 'logs':
          await this.wipeLogs();
          await writeJsonFile(CONFIG.logsApnFile, []);
          await writeJsonFile(CONFIG.logsFamilyMartFile, []);
          log('All logs wiped', 'success');
          break;
          
        case 'carts':
          await this.wipeDirectory(CONFIG.cartsDir);
          log('Carts wiped', 'success');
          break;
          
        case 'all':
          await writeJsonFile(CONFIG.ordersFile, []);
          await writeJsonFile(CONFIG.customersFile, []);
          await writeJsonFile(CONFIG.logsApnFile, []);
          await writeJsonFile(CONFIG.logsFamilyMartFile, []);
          await this.wipeLogs();
          await this.wipeDirectory(CONFIG.cartsDir);
          await this.wipeDirectory(CONFIG.callbacksDir);
          log('Entire database wiped', 'success');
          break;
          
        default:
          throw new Error(`Unknown wipe type: ${type}`);
      }
      
      return true;
      
    } catch (error) {
      log(`Wipe failed: ${error.message}`, 'error');
      return false;
    }
  }

  async wipeLogs() {
    const logDirs = [CONFIG.logsDir, CONFIG.dataLogsDir, CONFIG.apnTestLogsDir];
    
    for (const logDir of logDirs) {
      await this.wipeDirectory(logDir);
    }
  }

  async wipeFamilyMartLogs() {
    const confirmation = await question(`Are you sure you want to wipe legacy FamilyMart log files? This action cannot be undone! (type 'CONFIRM' to proceed): `);
    
    if (confirmation !== 'CONFIRM') {
      log('Wipe operation cancelled', 'warning');
      return false;
    }
    
    try {
      // Only wipe legacy files - new callbacks are in orders.json
      await writeJsonFile(CONFIG.logsFamilyMartFile, []);
      await writeJsonFile(CONFIG.logsApnFile, []);
      log('Legacy payment callback log files wiped (new callbacks are stored in orders.json)', 'success');
      return true;
    } catch (error) {
      log(`Wipe failed: ${error.message}`, 'error');
      return false;
    }
  }

  async wipeDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) return;
    
    try {
      const files = await readdir(dirPath);
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = await stat(filePath);
        
        if (stats.isDirectory()) {
          await this.wipeDirectory(filePath);
          await fs.promises.rmdir(filePath);
        } else {
          await fs.promises.unlink(filePath);
        }
      }
    } catch (error) {
      log(`Error wiping directory ${dirPath}: ${error.message}`, 'error');
    }
  }
}

// CLI Interface
async function showMenu() {
  console.log(`
🗄️  MAG Shop Database Management Tool
=====================================

📊 ANALYSIS
  1. Analyze database
  2. Show database statistics
  3. List backups

🧹 CLEANUP  
  4. Cleanup expired orders
  5. Cleanup test orders
  6. Cleanup orphaned customers  
  7. Cleanup old carts
  8. Cleanup old logs
  9. Cleanup old payment callbacks
  10. Full cleanup (all above)

💾 BACKUP & RESTORE
  11. Create full backup
  12. Create data backup  
  13. Create logs backup
  14. Restore from backup

🗑️  WIPE (DANGEROUS)
  15. Wipe orders
  16. Wipe customers
  17. Wipe logs
  18. Wipe carts
  19. Wipe legacy callback logs
  20. Wipe all data

  0. Exit
`);
}

async function main() {
  const dbManager = new DatabaseManager();
  await dbManager.init();
  
  while (true) {
    await showMenu();
    const choice = await question('Select an option: ');
    
    try {
      switch (choice) {
        case '1':
          log('Analyzing database...');
          const analysis = await dbManager.analyzeDatabase();
          console.log('📊 Database Analysis:');
          console.log('==================');
          
          if (analysis.orders) {
            console.log(`\n📦 Orders: ${analysis.orders.total} total`);
            console.log(`  ✅ Completed: ${analysis.orders.completed}`);
            console.log(`  ⏳ Pending: ${analysis.orders.pending}`);
            console.log(`  ❌ Expired: ${analysis.orders.expired}`);
            console.log(`  🚫 Cancelled: ${analysis.orders.cancelled}`);
            console.log(`  🧪 Test orders: ${analysis.orders.testOrders}`);
            console.log(`  📅 Recent (7 days): ${analysis.orders.recentOrders}`);
            console.log(`  🕐 Old (>${CONFIG.expiredOrdersThreshold} days): ${analysis.orders.oldOrders}`);
          }
          
          if (analysis.customers) {
            console.log(`\n👥 Customers: ${analysis.customers.total} total`);
            console.log(`  ✅ Active: ${analysis.customers.active}`);
            console.log(`  💤 Inactive: ${analysis.customers.inactive}`);
            console.log(`  🛒 With orders: ${analysis.customers.withOrders}`);
            console.log(`  🚫 Without orders: ${analysis.customers.withoutOrders}`);
            console.log(`  📅 Recently updated: ${analysis.customers.recentlyUpdated}`);
          }
          
          if (analysis.logs) {
            console.log(`\n📋 Logs: ${analysis.logs.totalFiles} files`);
            console.log(`  📦 Total size: ${formatBytes(analysis.logs.totalSize)}`);
            console.log(`  🕐 Old files: ${analysis.logs.oldFiles}`);
            console.log(`  📅 Recent files: ${analysis.logs.recentFiles}`);
          }
          
          if (analysis.paymentCallbacks) {
            console.log(`\n💳 Payment Callbacks: ${analysis.paymentCallbacks.totalCallbacks} total from ${analysis.paymentCallbacks.ordersWithCallbacks}/${analysis.paymentCallbacks.totalOrders} orders`);
            console.log(`  🕐 Old callbacks (>${CONFIG.oldCallbackLogsThreshold} days): ${analysis.paymentCallbacks.oldCallbacks}`);
            console.log(`  📅 Recent callbacks: ${analysis.paymentCallbacks.recentCallbacks}`);
            console.log(`  ⚡ Avg processing: ${analysis.paymentCallbacks.avgProcessingTime}ms`);
            
            if (analysis.paymentCallbacks.apnCallbacks > 0) {
              console.log(`\n  🔄 7-Eleven APN: ${analysis.paymentCallbacks.apnCallbacks} callbacks`);
              console.log(`    🧪 Test: ${analysis.paymentCallbacks.apnTestLogs} | 🏭 Production: ${analysis.paymentCallbacks.apnProductionLogs}`);
              console.log(`    ⏳ Waiting (A): ${analysis.paymentCallbacks.apnStatusA} | ✅ Paid (B): ${analysis.paymentCallbacks.apnStatusB} | ⏰ Expired (D): ${analysis.paymentCallbacks.apnStatusD}`);
              console.log(`    📊 Responses - OK: ${analysis.paymentCallbacks.apnResponseOK} | Not Found: ${analysis.paymentCallbacks.apnResponseNotFound}`);
            }
            
            if (analysis.paymentCallbacks.familyMartCallbacks > 0) {
              console.log(`\n  🏪 FamilyMart: ${analysis.paymentCallbacks.familyMartCallbacks} callbacks`);
              console.log(`    🧪 Test: ${analysis.paymentCallbacks.familyMartTestLogs} | 🏭 Production: ${analysis.paymentCallbacks.familyMartProductionLogs}`);
              console.log(`    ✅ Success (0): ${analysis.paymentCallbacks.familyMartStatus0} | ⏳ Pending (1): ${analysis.paymentCallbacks.familyMartStatus1}`);
              console.log(`    ⏰ Expired (2): ${analysis.paymentCallbacks.familyMartStatus2} | 🚫 Cancelled (3): ${analysis.paymentCallbacks.familyMartStatus3}`);
              console.log(`    📊 Responses - Success: ${analysis.paymentCallbacks.familyMartResponseSuccess} | Error: ${analysis.paymentCallbacks.familyMartResponseError}`);
            }
          }
          
          if (analysis.storage) {
            console.log(`\n💾 Storage: ${formatBytes(analysis.storage.total)} total`);
            analysis.storage.directories.forEach(dir => {
              console.log(`  ${dir.name}: ${formatBytes(dir.size)}`);
            });
          }
          break;
          
        case '2':
          // Show quick stats
          const orders = await readJsonFile(CONFIG.ordersFile);
          const customers = await readJsonFile(CONFIG.customersFile);
          
          console.log('\n📊 Quick Statistics:');
          console.log('==================');
          console.log(`Orders: ${orders ? orders.length : 0}`);
          console.log(`Customers: ${customers ? customers.length : 0}`);
          
          const backups = await dbManager.listBackups();
          console.log(`Backups: ${backups.length}`);
          break;
          
        case '3':
          const backupList = await dbManager.listBackups();
          console.log('\n💾 Available Backups:');
          console.log('===================');
          
          if (backupList.length === 0) {
            console.log('No backups found');
          } else {
            backupList.forEach((backup, index) => {
              console.log(`${index + 1}. ${backup.name}`);
              console.log(`   Type: ${backup.type}`);
              console.log(`   Date: ${new Date(backup.timestamp).toLocaleString()}`);
              console.log(`   Size: ${formatBytes(backup.size)}`);
              
              if (backup.statistics) {
                const stats = backup.statistics;
                console.log(`   📊 Contents:`);
                console.log(`      Orders: ${stats.orders}`);
                console.log(`      Customers: ${stats.customers}`);
                console.log(`      Staff: ${stats.staff}`);
                if (stats.paymentCallbacks) {
                  console.log(`      Payment Callbacks: ${stats.paymentCallbacks} (in orders)`);
                }
                if (stats.apnLogs) {
                  console.log(`      Legacy APN Logs: ${stats.apnLogs}`);
                }
                
                // Show detailed file listings
                if (stats.files) {
                  console.log(`   📁 Files:`);
                  
                  if (stats.files.dataFiles.length > 0) {
                    console.log(`      Data: ${stats.files.dataFiles.join(', ')}`);
                  }
                  
                  if (stats.files.cartFiles.length > 0) {
                    console.log(`      Carts: ${stats.files.cartFiles.slice(0, 5).join(', ')}${stats.files.cartFiles.length > 5 ? ` (+${stats.files.cartFiles.length - 5} more)` : ''}`);
                  }
                  
                  if (stats.files.logFiles.length > 0) {
                    console.log(`      Data Logs: ${stats.files.logFiles.slice(0, 3).join(', ')}${stats.files.logFiles.length > 3 ? ` (+${stats.files.logFiles.length - 3} more)` : ''}`);
                  }
                  
                  if (stats.files.mainLogFiles.length > 0) {
                    console.log(`      Main Logs: ${stats.files.mainLogFiles.slice(0, 3).join(', ')}${stats.files.mainLogFiles.length > 3 ? ` (+${stats.files.mainLogFiles.length - 3} more)` : ''}`);
                  }
                  
                  if (stats.files.documentFiles.length > 0) {
                    console.log(`      Documents: ${stats.files.documentFiles.slice(0, 3).join(', ')}${stats.files.documentFiles.length > 3 ? ` (+${stats.files.documentFiles.length - 3} more)` : ''}`);
                  }
                  
                  if (stats.files.callbackFiles.length > 0) {
                    console.log(`      Callbacks: ${stats.files.callbackFiles.slice(0, 3).join(', ')}${stats.files.callbackFiles.length > 3 ? ` (+${stats.files.callbackFiles.length - 3} more)` : ''}`);
                  }
                  
                  if (stats.files.customerFiles.length > 0) {
                    console.log(`      Customer Files: ${stats.files.customerFiles.slice(0, 3).join(', ')}${stats.files.customerFiles.length > 3 ? ` (+${stats.files.customerFiles.length - 3} more)` : ''}`);
                  }
                }
              }
              console.log('');
            });
          }
          break;
          
        case '4':
          const dryRun1 = await question('Dry run first? (y/n): ');
          const expired = await dbManager.cleanupExpiredOrders(dryRun1.toLowerCase() === 'y');
          if (dryRun1.toLowerCase() !== 'y' && expired > 0) {
            log(`Cleaned ${expired} expired orders`);
          }
          break;
          
        case '5':
          const dryRun2 = await question('Dry run first? (y/n): ');
          const testOrders = await dbManager.cleanupTestOrders(dryRun2.toLowerCase() === 'y');
          if (dryRun2.toLowerCase() !== 'y' && testOrders > 0) {
            log(`Cleaned ${testOrders} test orders`);
          }
          break;
          
        case '6':
          const dryRun3 = await question('Dry run first? (y/n): ');
          const orphaned = await dbManager.cleanupOrphanedCustomers(dryRun3.toLowerCase() === 'y');
          if (dryRun3.toLowerCase() !== 'y' && orphaned > 0) {
            log(`Cleaned ${orphaned} orphaned customers`);
          }
          break;
          
        case '7':
          const dryRun4 = await question('Dry run first? (y/n): ');
          const carts = await dbManager.cleanupOldCarts(dryRun4.toLowerCase() === 'y');
          if (dryRun4.toLowerCase() !== 'y' && carts > 0) {
            log(`Cleaned ${carts} old carts`);
          }
          break;
          
        case '8':
          const dryRun5 = await question('Dry run first? (y/n): ');
          const logs = await dbManager.cleanupOldLogs(dryRun5.toLowerCase() === 'y');
          if (dryRun5.toLowerCase() !== 'y' && logs > 0) {
            log(`Cleaned ${logs} old log files`);
          }
          break;
          
        case '9':
          const dryRun6 = await question('Dry run first? (y/n): ');
          const oldCallbackLogs = await dbManager.cleanupOldCallbacksFromOrders(dryRun6.toLowerCase() === 'y');
          if (dryRun6.toLowerCase() !== 'y' && oldCallbackLogs > 0) {
            log(`Cleaned ${oldCallbackLogs} old callback logs from orders`);
          }
          break;
          
        case '10':
          const dryRunAll = await question('Dry run first? (y/n): ');
          const isDryRun = dryRunAll.toLowerCase() === 'y';
          
          log('Running full cleanup...');
          await dbManager.cleanupExpiredOrders(isDryRun);
          await dbManager.cleanupTestOrders(isDryRun);
          await dbManager.cleanupOrphanedCustomers(isDryRun);
          await dbManager.cleanupOldCarts(isDryRun);
          await dbManager.cleanupOldLogs(isDryRun);
          await dbManager.cleanupOldCallbacksFromOrders(isDryRun);
          
          if (!isDryRun) {
            log('Full cleanup completed', 'success');
          }
          break;
          
        case '11':
          log('Creating full backup...');
          await dbManager.createBackup('full');
          break;
          
        case '12':
          log('Creating data backup...');
          await dbManager.createBackup('data');
          break;
          
        case '13':
          log('Creating logs backup...');
          await dbManager.createBackup('logs');
          break;
          
        case '14':
          const backupsForRestore = await dbManager.listBackups();
          if (backupsForRestore.length === 0) {
            log('No backups available for restore', 'warning');
            break;
          }
          
          console.log('\nAvailable backups:');
          backupsForRestore.forEach((backup, index) => {
            console.log(`${index + 1}. ${backup.name} (${backup.type}, ${formatBytes(backup.size)})`);
            if (backup.statistics) {
              const stats = backup.statistics;
              let statText = `    📊 ${stats.orders} orders, ${stats.customers} customers`;
              if (stats.paymentCallbacks) {
                statText += `, ${stats.paymentCallbacks} payment callbacks`;
              }
              if (stats.apnLogs) {
                statText += `, ${stats.apnLogs} legacy APN logs`;
              }
              console.log(statText);
            }
          });
          
          const backupIndex = await question('Select backup number: ');
          const selectedBackup = backupsForRestore[parseInt(backupIndex) - 1];
          
          if (selectedBackup) {
            await dbManager.restoreBackup(selectedBackup.name);
          } else {
            log('Invalid backup selection', 'error');
          }
          break;
          
        case '15':
          await dbManager.wipeDatabase('orders');
          break;
          
        case '16':
          await dbManager.wipeDatabase('customers');
          break;
          
        case '17':
          await dbManager.wipeDatabase('logs');
          break;
          
        case '18':
          await dbManager.wipeDatabase('carts');
          break;
          
        case '19':
          await dbManager.wipeFamilyMartLogs();
          break;
          
        case '20':
          await dbManager.wipeDatabase('all');
          break;
          
        case '0':
          log('Goodbye!');
          rl.close();
          return;
          
        default:
          log('Invalid option', 'warning');
      }
      
    } catch (error) {
      log(`Error: ${error.message}`, 'error');
    }
    
    await question('\nPress Enter to continue...');
  }
}

// Export for use as module
module.exports = { DatabaseManager, CONFIG };

// Run CLI if executed directly
if (require.main === module) {
  main().catch(error => {
    log(`Fatal error: ${error.message}`, 'error');
    process.exit(1);
  });
}