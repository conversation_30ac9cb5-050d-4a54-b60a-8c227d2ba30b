#!/usr/bin/env node

/**
 * Test script for IF Mobile Topup provider integration
 * This script tests the provider management integration for IF Mobile Topup
 */

const axios = require('axios');

const BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://sim.dailoanshop.net' 
  : 'http://localhost:3000';

async function testIFProvider() {
  console.log('🧪 Testing IF Mobile Topup Provider Integration');
  console.log('=' .repeat(50));
  
  try {
    // Test 1: Check IF Products API
    console.log('\n1️⃣ Testing IF Products API...');
    const productsResponse = await axios.get(`${BASE_URL}/api/payment/if/products`, {
      timeout: 10000
    });
    
    if (productsResponse.data.success) {
      console.log('✅ Products API working');
      console.log(`   - Found ${productsResponse.data.products.length} products`);
      console.log(`   - Merchant ID: ${productsResponse.data.config.merchantID}`);
      console.log(`   - API URL: ${productsResponse.data.config.apiUrl}`);
      
      // Show sample products
      const sampleProducts = productsResponse.data.products.slice(0, 3);
      console.log('   - Sample products:');
      sampleProducts.forEach(product => {
        console.log(`     * ${product.name} - NT$${product.price}`);
      });
    } else {
      console.log('❌ Products API failed');
      console.log('   Error:', productsResponse.data.error);
    }

    // Test 2: Check Provider Stats API
    console.log('\n2️⃣ Testing Provider Stats API...');
    const statsResponse = await axios.get(`${BASE_URL}/api/admin/provider-stats`, {
      timeout: 10000
    });
    
    if (statsResponse.data.success) {
      console.log('✅ Provider Stats API working');
      
      const ifStats = statsResponse.data.providers.if_topup;
      if (ifStats) {
        console.log('   - IF Provider found in stats');
        console.log(`   - Connection Status: ${ifStats.connection.status}`);
        console.log(`   - Response Time: ${ifStats.connection.responseTime}ms`);
        console.log(`   - Product Count: ${ifStats.connection.productCount || 'N/A'}`);
        console.log(`   - API Version: ${ifStats.connection.apiVersion || 'N/A'}`);
        console.log(`   - Total Transactions: ${ifStats.statistics.totalTransactions}`);
        console.log(`   - Success Rate: ${ifStats.statistics.successRate}%`);
      } else {
        console.log('⚠️  IF Provider not found in stats');
      }
    } else {
      console.log('❌ Provider Stats API failed');
    }

    // Test 3: Test Provider Management Page Data
    console.log('\n3️⃣ Testing Provider Management Integration...');
    
    // This would normally be tested by checking the page directly
    // For now, we'll verify the configuration is accessible
    try {
      const configPath = require('path').join(process.cwd(), 'components/taiwan/operators/if/config.json');
      const fs = require('fs');
      
      if (fs.existsSync(configPath)) {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log('✅ IF Configuration accessible');
        console.log(`   - Merchant ID: ${config.merchantID}`);
        console.log(`   - Live URL: ${config.liveUrl}`);
        console.log(`   - Test URL: ${config.testUrl}`);
        console.log(`   - Products: ${config.productCodes.length} available`);
        
        // Calculate price range
        const prices = config.productCodes.map(p => p.price);
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);
        console.log(`   - Price Range: NT$${minPrice} - NT$${maxPrice}`);
      } else {
        console.log('❌ IF Configuration file not found');
      }
    } catch (error) {
      console.log('❌ Error accessing IF configuration:', error.message);
    }

    console.log('\n🎉 IF Mobile Topup Provider Integration Test Complete!');
    console.log('\n📋 Summary:');
    console.log('   - Products API: Working');
    console.log('   - Provider Stats: Working');
    console.log('   - Configuration: Accessible');
    console.log('\n💡 Next Steps:');
    console.log('   1. Visit http://localhost:3000/admin/provider-management');
    console.log('   2. Look for "IF Mobile Topup" provider');
    console.log('   3. Click "Kiểm tra kết nối" to test connection');
    console.log('   4. Click "Chi tiết" to see product list');

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the development server is running:');
      console.log('   npm run dev');
    }
  }
}

// Run the test
if (require.main === module) {
  testIFProvider();
}

module.exports = { testIFProvider };
