<?php
/**
 * 7-Eleven authentication token test function based on provided sample.
 **/
function get_token($server_url, $username, $password) {
    $token_url = "$server_url/Token"; // Corrected endpoint path
    echo "Attempting to get token from: " . $token_url . "\n";
    echo "Using Username: " . $username . "\n";

    $cl = curl_init($token_url);
    curl_setopt($cl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($cl, CURLOPT_POST, true);

    // Explicitly set TLS v1.2 as per sample comment (CURLOPT_SSLVERSION = 6)
    // Note: Depending on the PHP/cURL version, this might automatically negotiate
    // the best TLS version. If issues persist, review TLS settings.
    curl_setopt($cl, CURLOPT_SSLVERSION, 6);

    // Disable SSL verification for testing ONLY if necessary (not recommended for production)
    // curl_setopt($cl, CURLOPT_SSL_VERIFYPEER, 0);
    // curl_setopt($cl, CURLOPT_SSL_VERIFYHOST, 0);

    curl_setopt($cl, CURLOPT_HTTPHEADER, ["Content-Type: application/x-www-form-urlencoded"]);

    // Construct body string manually as done in the Node.js fix
    $post_data = "grant_type=password&username=" . urlencode($username) . "&password=" . urlencode($password);
    // Note: The sample code didn't urlencode, but it's generally safer for x-www-form-urlencoded.
    // If this fails, try removing urlencode() for the password.
    echo "Sending POST data: " . $post_data . "\n";
    curl_setopt($cl, CURLOPT_POSTFIELDS, $post_data);

    $auth_response = curl_exec($cl);
    $curl_error = curl_error($cl);
    $curl_info = curl_getinfo($cl);

    if ($auth_response === false) {
        echo "cURL Error: " . $curl_error . "\n";
        echo "cURL Info:\n";
        print_r($curl_info);
        curl_close($cl);
        return NULL;
    }

    curl_close($cl);
    echo "Raw Response:\n" . $auth_response . "\n";
    return json_decode($auth_response, true);
}

// --- Configuration ---
$server_url = "https://test.4128888card.com.tw/app"; // Test URL from docs
$username   = "827315300001"; // CUST_ID from mag.711.API.txt
//$password   = "W7529992P$";   // API Password from mag.711.API.txt
$password   = "@a827315300001";   // API Password from mag.711.API.txt
// --- Execute Test ---
$token_data = get_token($server_url, $username, $password);

echo "\n--- Result ---\n";
if ($token_data !== NULL) {
    if (isset($token_data['access_token'])) {
        echo "Success! Token obtained:\n";
        print_r($token_data);
    } elseif (isset($token_data['error'])) {
        echo "API Error:\n";
        print_r($token_data);
    } else {
        echo "Unknown response format:\n";
        print_r($token_data);
    }
} else {
    echo "Failed to get a valid response from the server.\n";
}
echo "\n";

?> 