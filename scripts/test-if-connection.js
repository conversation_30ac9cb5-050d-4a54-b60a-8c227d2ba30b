#!/usr/bin/env node

/**
 * <PERSON><PERSON> VRC System API Test Script
 * 
 * Tests the VRC system API connection, encryption, and activation
 * Run on a whitelisted server to test the actual connection
 * 
 * Usage:
 *   node test-if-connection.js [--env=production|test|development] [--verbose] [--mock] [--ignore-ssl]
 *   
 * Arguments:
 *   --env=production|test|development  - Environment to test (default: test)
 *   --verbose                          - Enable verbose logging
 *   --mock                             - Mock API calls (for local testing)
 *   --ignore-ssl                       - Ignore SSL certificate validation (use with caution)
 * 
 * Examples:
 *   node test-if-connection.js --mock --verbose
 *   node test-if-connection.js --env=production
 *   node test-if-connection.js --env=development --ignore-ssl
 */

const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const ENV = args.find(arg => arg.startsWith('--env='))?.split('=')[1] || 'test';
const VERBOSE = args.includes('--verbose');
const MOCK_API = args.includes('--mock');
const IGNORE_SSL = ENV === 'development' || args.includes('--ignore-ssl');

// Create log directory if it doesn't exist
const LOG_DIR = path.join(__dirname, '../logs');
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

const LOG_FILE = path.join(LOG_DIR, `if-test-${new Date().toISOString().replace(/:/g, '-')}.log`);
fs.writeFileSync(LOG_FILE, `IF VRC System API Test - ${new Date().toISOString()}\n\n`);

// Logging functions
function log(message, data = null) {
  const timestamp = new Date().toISOString();
  const logEntry = data
    ? `[${timestamp}] ${message}: ${JSON.stringify(data, null, 2)}`
    : `[${timestamp}] ${message}`;
    
  console.log(logEntry);
  fs.appendFileSync(LOG_FILE, logEntry + '\n');
}

function logError(message, error) {
  const timestamp = new Date().toISOString();
  const errorDetails = error.response 
    ? {
        status: error.response.status,
        data: error.response.data,
        message: error.message
      }
    : { message: error.message, stack: error.stack };
    
  const logEntry = `[${timestamp}] ERROR - ${message}: ${JSON.stringify(errorDetails, null, 2)}`;
  
  console.error('\x1b[31m%s\x1b[0m', logEntry); // Red color for errors
  fs.appendFileSync(LOG_FILE, logEntry + '\n');
}

function logSuccess(message, data = null) {
  const timestamp = new Date().toISOString();
  const logEntry = data
    ? `[${timestamp}] SUCCESS - ${message}: ${JSON.stringify(data, null, 2)}`
    : `[${timestamp}] SUCCESS - ${message}`;
    
  console.log('\x1b[32m%s\x1b[0m', logEntry); // Green color for success
  fs.appendFileSync(LOG_FILE, logEntry + '\n');
}

function logSection(title) {
  const separator = '='.repeat(80);
  const message = `\n${separator}\n${title}\n${separator}\n`;
  
  console.log('\x1b[36m%s\x1b[0m', message); // Cyan color for section headers
  fs.appendFileSync(LOG_FILE, message + '\n');
}

// Load configuration
let config;
try {
  const configPath = path.join(__dirname, '../components/taiwan/operators/if/config.json');
  config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  log('Configuration loaded successfully');
  if (VERBOSE) {
    log('Config:', {
      ...config,
      merchantPassword: config.merchantPassword,
      apiKey: config.apiKey,
      encryption: {
        ...config.encryption,
        key: config.encryption.key,
        iv: config.encryption.iv
      }
    });
  }
} catch (error) {
  logError('Failed to load configuration', error);
  process.exit(1);
}

// Set up API URL
const API_URL = ENV === 'production' ? config.liveUrl : config.testUrl;

// Test Functions
async function runTests() {
  logSection('ENVIRONMENT');
  log(`Testing in ${ENV} environment`);
  log(`API URL: ${API_URL}`);
  log(`Mock API: ${MOCK_API ? 'Yes' : 'No'}`);
  log(`Node.js version: ${process.version}`);
  log(`Platform: ${process.platform}`);

  // Test encryption
  logSection('TESTING ENCRYPTION');
  try {
    const encryptionTest = testEncryption();
    logSuccess('Encryption test completed successfully', encryptionTest);
  } catch (error) {
    logError('Encryption test failed', error);
    process.exit(1);
  }

  // Test product list retrieval
  logSection('TESTING PRODUCT LIST');
  try {
    const products = config.productCodes;
    log(`Found ${products.length} products in configuration`);
    products.forEach((product, index) => {
      log(`Product ${index + 1}: ${product.id} - ${product.name} (NT$${product.price})`);
    });
    logSuccess('Product list retrieval successful');
  } catch (error) {
    logError('Product list retrieval failed', error);
  }

  // Test phone number validation
  logSection('TESTING PHONE NUMBER VALIDATION');
  testPhoneNumberValidation();

  // Test API connection (mock or real)
  logSection('TESTING API CONNECTION');
  try {
    await testAPIConnection();
  } catch (error) {
    logError('API connection test failed', error);
  }

  // Test full recharge workflow
  logSection('TESTING RECHARGE WORKFLOW');
  try {
    await testRechargeWorkflow();
  } catch (error) {
    logError('Recharge workflow test failed', error);
  }

  // Summary
  logSection('TEST SUMMARY');
  log(`Test completed at ${new Date().toISOString()}`);
  log(`Log file: ${LOG_FILE}`);
}

function testEncryption() {
  log('Testing AES-128-CBC encryption');
  
  const testValues = [
    { input: config.merchantID, expected: null },
    { input: config.merchantPassword, expected: null },
    { input: 'test1', expected: 'aCEjE/E/Oq4d4Z9c67DWcw==' } // Example from documentation
  ];

  const results = testValues.map(({ input, expected }) => {
    try {
      const cipher = crypto.createCipheriv(
        config.encryption.algorithm,
        config.encryption.key,
        config.encryption.iv
      );
      
      let encrypted = cipher.update(input, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      const result = {
        input: input === config.merchantID ? config.merchantID : 
               input === config.merchantPassword ? config.merchantPassword : input,
        encrypted
      };
      
      if (expected && encrypted !== expected) {
        log(`Warning: Encryption result doesn't match expected value for '${input}'`, {
          expected,
          actual: encrypted
        });
      }
      
      return result;
    } catch (error) {
      logError(`Encryption failed for input: ${input}`, error);
      throw error;
    }
  });

  return results;
}

function testPhoneNumberValidation() {
  const testPhones = generateTestPhoneNumbers();
  
  const testCases = [
    { number: '0912345678', valid: true, reason: 'Valid 10-digit number' },
    { number: '091234567', valid: false, reason: 'Too short (9 digits)' },
    { number: '09123456789', valid: false, reason: 'Too long (11 digits)' },
    { number: '************', valid: false, reason: 'Contains non-digit characters' },
    { number: 'abcdefghij', valid: false, reason: 'Non-numeric characters' },
    { number: '', valid: false, reason: 'Empty string' },
    ...testPhones.map(phone => ({ number: phone, valid: true, reason: 'Generated test phone' }))
  ];

  testCases.forEach(test => {
    const isValid = /^\d{10}$/.test(test.number);
    const result = {
      number: test.number,
      expected: test.valid,
      actual: isValid,
      reason: test.reason
    };

    if (isValid === test.valid) {
      log(`Phone validation as expected: ${test.number} - ${test.reason}`, result);
    } else {
      logError(`Phone validation unexpected result: ${test.number} - ${test.reason}`, result);
    }
  });
  
  log('Phone numbers that will be used for testing:', testPhones);
}

async function testAPIConnection() {
  log(`Testing API connection to ${API_URL}`);
  
  if (MOCK_API) {
    log('Using mock API mode - will simulate the connection');
    logSuccess('Mock API connection successful');
    return;
  }
  
  try {
    // Try a basic HEAD request to check if the server is reachable
    const response = await axios({
      method: 'head',
      url: API_URL,
      timeout: 15000, // Increased from 5000ms to 15000ms (15 seconds)
      headers: { 'APIKEY': config.apiKey },
      validateStatus: () => true, // Don't throw on any status
      httpsAgent: IGNORE_SSL ? new (require('https').Agent)({ rejectUnauthorized: false }) : undefined
    });
    
    log('API connection response', {
      status: response.status,
      headers: response.headers
    });
    
    if (response.status >= 200 && response.status < 500) {
      logSuccess('API connection successful');
    } else {
      logError('API connection failed', { status: response.status });
    }
  } catch (error) {
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      logError('API server not reachable - check network connection and whitelist status', error);
    } else {
      logError('API connection error', error);
    }
    throw error;
  }
}

function generateTestPhoneNumbers() {
  // Generate an array of potentially valid Taiwan phone numbers to try
  // Common Taiwan mobile prefixes for different carriers:
  // Chunghwa Telecom: 0905, 0906, 0910, 0911, 0912, 0913...
  // Taiwan Mobile: 0903, 0904, 0916, 0917, 0918, 0919...
  // Far EasTone: 0906, 0909, 0932, 0937, 0938, 0939...
  // Taiwan Star: 0955, 0975, 0979...
  // GT: 0945, 0946, 0947, 0948, 0958...
  // Also include some special test numbers: 0910000001, 0910000002, etc.
  
  // Some common prefixes for testing
  const prefixes = [
    '0901', '0902', '0903', '0905', '0910', '0911', '0912', 
    '0915', '0916', '0917', '0920', '0922', '0930', '0931',
    '0932', '0937', '0938', '0939', '0955', '0958', '0970', '0982', '0988'
  ];
  
  const phoneNumbers = [];
  
  // Add the primary test number from documentation
  phoneNumbers.push('0900000001');
  
  // Add special test numbers (often used in telecom testing)
  const specialTestNumbers = [
    '0910000000', '0910000001', '0910000002', '0911111111',
    '0970123456', '0933333333', '0955555555'
  ];
  
  phoneNumbers.push(...specialTestNumbers);
  
  // Generate random phone numbers with different prefixes
  for (let i = 0; i < Math.min(10, prefixes.length); i++) {
    const randomDigits = String(Math.floor(Math.random() * 1000000)).padStart(6, '0');
    const phone = `${prefixes[i]}${randomDigits}`.substring(0, 10);
    phoneNumbers.push(phone);
  }
  
  return phoneNumbers;
}

async function testRechargeWorkflow() {
  // Test data - Using multiple phone numbers to try
  const testPhoneNumbers = generateTestPhoneNumbers();
  let successfulRecharge = false;
  let lastResponse = null;
  
  // Ensure the documented test number is tested first
  const documentedNumber = '0900000001';
  const prioritizedNumbers = [
    documentedNumber,
    ...testPhoneNumbers.filter(num => num !== documentedNumber)
  ];
  
  // Find the documented product ID or fall back to the first one
  const documentedProductId = '1003554212'; // As seen in the documentation
  const productId = documentedProductId || config.productCodes[0].id;
  
  log('Testing recharge workflow with available test phone numbers:', prioritizedNumbers);
  log('Note: The first number (0900000001) is the documented test number');
  log(`Using product ID: ${productId}`);
  
  if (MOCK_API) {
    log('Using mock API mode - simulating recharge workflow');
    await mockRechargeWorkflow(documentedNumber, productId, `test_${Date.now()}`);
    return;
  }
  
  // Try each phone number in sequence until success or all fail
  for (let i = 0; i < prioritizedNumbers.length; i++) {
    const phoneNumber = prioritizedNumbers[i];
    const orderId = `test_${Date.now()}`;
    
    log(`Attempt ${i+1}/${prioritizedNumbers.length} - Testing with phone: ${phoneNumber}`, {
      phoneNumber,
      productId,
      orderId,
      environment: ENV
    });
    
    try {
      // Encrypt credentials
      const encryptedAccount = encryptAES(config.merchantID);
      const encryptedPassword = encryptAES(config.merchantPassword);
      
      // Create the payload
      const payload = {
        Account: encryptedAccount,
        Password: encryptedPassword,
        PhoneNumber: phoneNumber,
        FETOfferID: productId
      };
      
      log('Sending recharge request', {
        url: API_URL,
        payload: {
          ...payload,
          Account: '[ENCRYPTED]',
          Password: '[ENCRYPTED]'
        }
      });
      
      // Make the API call
      const response = await axios.post(
        API_URL,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            'APIKEY': config.apiKey
          },
          timeout: 60000, // 60 second timeout (increased from 30s)
          httpsAgent: IGNORE_SSL ? new (require('https').Agent)({ rejectUnauthorized: false }) : undefined
        }
      );
      
      log('Recharge API response', response.data);
      lastResponse = response.data;
      
      if (response.data && response.data.ReturnCode === '0000') {
        logSuccess(`Recharge success with phone ${phoneNumber}`, {
          returnCode: response.data.ReturnCode,
          returnMsg: response.data.ReturnMsg,
          transactionId: response.data.ReturnTXID
        });
        successfulRecharge = true;
        break; // Exit loop on success
      } else {
        const translatedMsg = translateErrorMessage(response.data.ReturnMsg);
        logError(`Recharge failed with phone ${phoneNumber}`, {
          returnCode: response.data.ReturnCode,
          returnMsg: response.data.ReturnMsg,
          translatedMsg,
          transactionId: response.data.ReturnTXID || 'N/A'
        });
      }
    } catch (error) {
      logError(`API error with phone ${phoneNumber}`, error);
      // Continue to the next phone number
    }
  }
  
  // Summarize the results
  if (successfulRecharge) {
    logSuccess('Recharge test successful with at least one phone number');
  } else {
    const errorMsg = lastResponse ? 
      `${lastResponse.ReturnCode}: ${lastResponse.ReturnMsg} (${translateErrorMessage(lastResponse.ReturnMsg)})` : 
      'All attempts failed';
    
    logError('All recharge attempts failed', { errorMessage: errorMsg });
  }
}

async function mockRechargeWorkflow(phoneNumber, productId, orderId) {
  log('Mocking recharge workflow start');
  
  // Simulate API latency
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock successful response
  const mockResponse = {
    ReturnCode: '0000',
    ReturnMsg: 'Success (Mock)',
    ReturnTXID: `${new Date().toISOString().slice(0, 10).replace(/-/g, '')}12345`
  };
  
  log('Mock recharge API response', mockResponse);
  logSuccess('Mock recharge workflow completed successfully', mockResponse);
}

function encryptAES(text) {
  try {
    const cipher = crypto.createCipheriv(
      config.encryption.algorithm,
      config.encryption.key,
      config.encryption.iv
    );
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  } catch (error) {
    logError('Encryption error', error);
    throw error;
  }
}

function translateErrorMessage(msg) {
  // Common error messages translated from Chinese
  const translations = {
    '門號有誤請重新輸入': 'Phone number error, please re-enter',
    '處理中': 'Processing',
    '此門號已經兌換過': 'This phone number has already been redeemed',
    '交易失敗': 'Transaction failed',
    '系統維護中': 'System maintenance'
  };
  
  return translations[msg] || 'Unknown error message';
}

// Run the tests
log('Starting IF VRC System API Test Script');
runTests()
  .then(() => {
    logSection('TESTS COMPLETED');
    process.exit(0);
  })
  .catch((error) => {
    logSection('TESTS FAILED');
    logError('Unhandled error in test script', error);
    process.exit(1);
  }); 