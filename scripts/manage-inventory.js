const readline = require('readline');

// List of SKUs to manage
const skusToManage = [
  'taiwan.prepaid.ok.ycco',  // OK150 NT
  'taiwan.prepaid.ok.S3Ey',  // 5 thẻ 150 NT
  'taiwan.prepaid.ok.xSjr',  // OK350NT
  'taiwan.prepaid.ok.bnvF',  // 5 thẻ OK350 NT
  'taiwan.prepaid.ok.d37h'   // OK mạng 30 ngày 599
];

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function queryInventory() {
  try {
    const response = await fetch('http://localhost:3000/api/inventory');
    if (!response.ok) {
      throw new Error('Failed to fetch inventory');
    }
    const inventoryData = await response.json();
    
    // Debug logging
    console.log('\nDebug: First few items in inventory:');
    console.log(inventoryData.slice(0, 3));
    console.log('\nDebug: Looking for SKUs:', skusToManage);
    
    console.log('\nCurrent Inventory Levels:\n');
    
    skusToManage.forEach(sku => {
      const item = inventoryData.find(item => item.sku === sku);
      if (item) {
        console.log(`SKU: ${sku}`);
        console.log(`Name: ${item.name}`);
        
        // Handle new inventory structure
        const inventoryCount = item.inventory ? item.inventory.count : item.currentInventory;
        const inventoryType = item.inventory ? item.inventory.type : 'same';
        
        console.log(`Current Inventory: ${inventoryCount} (type: ${inventoryType})`);
        
        if (item.inventory && item.inventory.type === 'separate' && item.inventory.items) {
          console.log(`Available Items: ${item.inventory.items.length}`);
        }
        
        console.log(`Price: ${item.price} ${item.currency}`);
        console.log('------------------------\n');
      } else {
        console.log(`SKU ${sku} not found in inventory\n`);
      }
    });
  } catch (error) {
    console.error('Error querying inventory:', error.message);
  }
}

async function deductInventory() {
  try {
    const response = await fetch('http://localhost:3000/api/inventory');
    if (!response.ok) {
      throw new Error('Failed to fetch inventory');
    }
    const inventoryData = await response.json();
    
    console.log('\nDeducting inventory...\n');
    
    for (const sku of skusToManage) {
      const item = inventoryData.find(item => item.sku === sku);
      if (item) {
        // Handle new inventory structure
        const currentCount = item.inventory ? item.inventory.count : item.currentInventory;
        const inventoryType = item.inventory ? item.inventory.type : 'same';
        
        if (currentCount > 0) {
          const oldInventory = currentCount;
          const newInventory = oldInventory - 1;
          
          // Prepare inventory update data
          let updateData = {
            id: item.sku,
            price: item.price
          };
          
          if (item.inventory) {
            // Use new inventory structure
            updateData.inventory = {
              ...item.inventory,
              count: newInventory
            };
          } else {
            // Fallback to old structure
            updateData.currentInventory = newInventory;
          }
          
          // Update inventory via API
          const updateResponse = await fetch('http://localhost:3000/api/inventory', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData),
          });
          
          if (!updateResponse.ok) {
            throw new Error(`Failed to update inventory for SKU ${sku}`);
          }
          
          console.log(`SKU: ${sku}`);
          console.log(`Name: ${item.name}`);
          console.log(`Inventory reduced from ${oldInventory} to ${newInventory} (type: ${inventoryType})`);
          console.log('------------------------\n');
        } else {
          console.log(`SKU: ${sku}`);
          console.log(`Name: ${item.name}`);
          console.log('Cannot deduct: Out of stock');
          console.log('------------------------\n');
        }
      } else {
        console.log(`SKU ${sku} not found in inventory\n`);
      }
    }
    
    console.log('Inventory updated successfully');
  } catch (error) {
    console.error('Error updating inventory:', error.message);
  }
}

function showMenu() {
  console.log('\nInventory Management System');
  console.log('------------------------');
  console.log('1. Query Inventory Levels');
  console.log('2. Deduct Inventory');
  console.log('3. Exit');
  console.log('------------------------\n');
}

function startInteractiveSession() {
  showMenu();
  
  rl.question('Please select an option (1-3): ', (answer) => {
    switch (answer.trim()) {
      case '1':
        queryInventory();
        startInteractiveSession();
        break;
      case '2':
        deductInventory();
        startInteractiveSession();
        break;
      case '3':
        console.log('\nGoodbye!');
        rl.close();
        break;
      default:
        console.log('\nInvalid option. Please try again.');
        startInteractiveSession();
    }
  });
}

// Start the interactive session
startInteractiveSession();