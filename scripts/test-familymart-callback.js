/**
 * FamilyMart Payment Notification Simulator
 * 
 * This script simulates payment notifications from the FamilyMart payment system.
 * It allows testing the callback endpoint with different payment statuses.
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const readline = require('readline');

// Configuration
const config = {
  callbackUrl: 'http://localhost:3000/api/payment/familymart-callback',
  ordersJsonPath: path.join(__dirname, '../data/orders.json'),
  ecId: 'FAMISHOPTEST123',
  testOrderId: 'FMTEST-001'
};

// Function to load orders from JSON file
function loadOrders() {
  try {
    const data = fs.readFileSync(config.ordersJsonPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading orders:', error.message);
    return [];
  }
}

// Function to find the test order
function findTestOrder(orders) {
  return orders.find(order => order.id === config.testOrderId);
}

// Function to generate a random PIN Code
function generatePinCode() {
  return 'FM' + Math.floor(10000000 + Math.random() * 90000000);
}

// Function to show current order status
function showOrderStatus() {
  const orders = loadOrders();
  const order = findTestOrder(orders);
  
  if (!order) {
    console.log(`\nOrder with ID ${config.testOrderId} not found.`);
    return;
  }

  console.log('\nCurrent Order Status:');
  console.log('-------------------');
  console.log(`Order ID: ${order.id}`);
  console.log(`Order Number: ${order.orderNumber}`);
  console.log(`Status: ${order.status}`);
  console.log(`Payment Status: ${order.paymentStatus}`);
  if (order.paymentInfo?.pinCode) {
    console.log(`PIN Code: ${order.paymentInfo.pinCode}`);
  }
  console.log('-------------------');
}

// Format date to YYYY-MM-DD HH:MM:SS
function formatDate(date = new Date()) {
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, '0');
  const dd = String(date.getDate()).padStart(2, '0');
  const hh = String(date.getHours()).padStart(2, '0');
  const min = String(date.getMinutes()).padStart(2, '0');
  const ss = String(date.getSeconds()).padStart(2, '0');
  
  return `${yyyy}-${mm}-${dd} ${hh}:${min}:${ss}`;
}

// Function to send a payment notification to the callback URL
function sendNotification(status) {
  const orders = loadOrders();
  const order = findTestOrder(orders);
  
  if (!order) {
    console.log(`Order with ID ${config.testOrderId} not found.`);
    return;
  }

  const pinCode = order.paymentInfo?.pinCode || generatePinCode();
  const statusCode = status.toString();
  const now = new Date();
  const paymentDate = formatDate(now);
  const orderDate = formatDate(new Date(order.createdAt));
  
  const postData = `
    EC_ID=${config.ecId}
    &PIN_CODE=${pinCode}
    &ORDER_NO=${order.orderNumber}
    &ORDER_DATE=${orderDate}
    &AMOUNT=${order.totalAmount}
    &STATUS_CODE=${statusCode}
    &PAYMENT_NO=FM${Math.floor(1000000000 + Math.random() * 9000000000)}
    &PAYMENT_DATE=${paymentDate}
    &STORE_ID=FAM${Math.floor(1000 + Math.random() * 9000)}
    &BARCODE_1=${order.paymentInfo?.barcodes?.barcode1 || ''}
    &BARCODE_2=${order.paymentInfo?.barcodes?.barcode2 || ''}
    &BARCODE_3=${order.paymentInfo?.barcodes?.barcode3 || ''}
  `.trim().replace(/\n\s+/g, '');

  // Create the request options
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/payment/familymart-callback',
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  // Create the request
  const req = http.request(options, (res) => {
    let responseData = '';
    
    res.on('data', (chunk) => {
      responseData += chunk;
    });
    
    res.on('end', () => {
      console.log(`\nResponse status: ${res.statusCode}`);
      console.log(`Response headers: ${JSON.stringify(res.headers)}`);
      console.log('Response body:', responseData);
      
      console.log(`\nSent ${getStatusName(statusCode)} notification for order ${order.orderNumber}`);
      showOrderStatus();
      displayMenu();
    });
  });
  
  req.on('error', (e) => {
    console.error(`\nProblem with request: ${e.message}`);
    displayMenu();
  });
  
  // Write data to request body
  req.write(postData);
  req.end();
  
  console.log(`\nSending ${getStatusName(statusCode)} notification...`);
  console.log('Payload:', postData);
}

// Helper to get status name from code
function getStatusName(code) {
  const statuses = {
    '0': 'Payment Completed',
    '1': 'Payment Pending',
    '2': 'Payment Expired',
    '3': 'Payment Cancelled'
  };
  
  return statuses[code] || `Unknown (${code})`;
}

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Check if the server is available
function checkServerAvailability(callback) {
  const req = http.request({
    hostname: 'localhost',
    port: 3000,
    path: '/',
    method: 'HEAD'
  }, (res) => {
    if (res.statusCode >= 200 && res.statusCode < 400) {
      callback(true);
    } else {
      callback(false);
    }
  });
  
  req.on('error', () => {
    callback(false);
  });
  
  req.end();
}

// Display menu and get user input
function displayMenu() {
  console.log('\nFamilyMart Payment Notification Simulator');
  console.log('========================================');
  console.log('1. Send Pending Payment Notification (STATUS_CODE=1)');
  console.log('2. Send Completed Payment Notification (STATUS_CODE=0)');
  console.log('3. Send Expired Payment Notification (STATUS_CODE=2)');
  console.log('4. Send Cancelled Payment Notification (STATUS_CODE=3)');
  console.log('5. Show Current Order Status');
  console.log('0. Exit');
  
  rl.question('\nSelect an option: ', (answer) => {
    switch(answer) {
      case '1':
        sendNotification(1);
        break;
      case '2':
        sendNotification(0);
        break;
      case '3':
        sendNotification(2);
        break;
      case '4':
        sendNotification(3);
        break;
      case '5':
        showOrderStatus();
        displayMenu();
        break;
      case '0':
        console.log('Exiting...');
        rl.close();
        break;
      default:
        console.log('Invalid option. Please try again.');
        displayMenu();
    }
  });
}

// Start the application
console.log('Starting FamilyMart Payment Notification Simulator...');

checkServerAvailability((isAvailable) => {
  if (!isAvailable) {
    console.log('\n❌ The server at http://localhost:3000 is not available.');
    console.log('Make sure to start the Next.js app with `yarn dev` before running this script.');
    rl.close();
    return;
  }
  
  // Show initial order status
  showOrderStatus();
  
  // Display the menu
  displayMenu();
}); 