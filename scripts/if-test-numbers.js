#!/usr/bin/env node

/**
 * Helper script to generate test phone numbers for IF VRC testing
 * 
 * Usage:
 *   node if-test-numbers.js [count] [prefix]
 * 
 * Arguments:
 *   count  - Number of phone numbers to generate (default: 5)
 *   prefix - Prefix for phone numbers (default: '0912')
 * 
 * Example:
 *   node if-test-numbers.js 10 0933
 */

// Parse command line arguments
const args = process.argv.slice(2);
const count = parseInt(args[0], 10) || 5;
const prefix = args[1] || '0912';

// Generate test phone numbers
console.log(`\nGenerating ${count} test phone numbers with prefix ${prefix}:\n`);

const phoneNumbers = [];
for (let i = 0; i < count; i++) {
  // Generate random digits to complete the phone number
  const randomDigits = String(Math.floor(Math.random() * 1000000)).padStart(6, '0');
  
  // Create the phone number ensuring it's exactly 10 digits
  const phoneNumber = `${prefix}${randomDigits}`.substring(0, 10);
  
  phoneNumbers.push(phoneNumber);
  console.log(`${i + 1}. ${phoneNumber}`);
}

// Output in JSON format for easy copying
console.log("\nJSON format:");
console.log(JSON.stringify(phoneNumbers, null, 2));

console.log("\nCSV format:");
console.log(phoneNumbers.join(","));

console.log("\nCopy these numbers for testing the IF VRC System."); 