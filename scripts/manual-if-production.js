const axios = require('axios');
const crypto = require('crypto');
const config = require('../components/taiwan/operators/if/config.json');

// Function to encrypt data using AES-128-CBC
function encryptAES(text, key, iv) {
  try {
    // Make sure the key and IV are proper length (16 bytes for AES-128)
    const usedKey = Buffer.from(key).slice(0, 16).toString().padEnd(16, ' ');
    const usedIv = Buffer.from(iv || key).slice(0, 16).toString().padEnd(16, ' ');
    
    const cipher = crypto.createCipheriv('aes-128-cbc', Buffer.from(usedKey), Buffer.from(usedIv));
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  } catch (error) {
    console.error(`Encryption error with key "${key}": ${error.message}`);
    return 'ERROR';
  }
}

// Function to make an API call to the IF service using merchantPassword
async function topUpWithMerchantPassword() {
  try {
    console.log('=== Attempting single topup with merchantPassword as key ===');
    
    // Use merchantPassword as the encryption key
    const encryptionKey = config.merchantPassword;
    
    // Encrypt account and password
    const encryptedAccount = encryptAES(config.merchantID, encryptionKey, encryptionKey);
    const encryptedPassword = encryptAES(config.merchantPassword, encryptionKey, encryptionKey);
    
    console.log('Encrypted Account:', encryptedAccount);
    console.log('Encrypted Password:', encryptedPassword);
    
    // Prepare request data
    const requestData = {
      Account: encryptedAccount,
      Password: encryptedPassword,
      PhoneNumber: '**********',
      FETOfferID: '**********'
    };
    
    console.log('Request Data:', JSON.stringify(requestData, null, 2));
    
    // Make the API call to the production server
    const response = await axios.post(
      'https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue',
      requestData,
      {
        headers: {
          'Content-Type': 'application/json',
          'APIKEY': config.apiKey
        },
        timeout: 30000 // 30 second timeout
      }
    );
    
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    // Verify the result
    if (response.data.ReturnCode === '0000') {
      console.log('\n✅ SUCCESS: Top-up was successful!');
      console.log('Transaction ID:', response.data.ReturnTXID);
    } else {
      console.log('\n❌ FAILED: Top-up was not successful');
      console.log(`Error code: ${response.data.ReturnCode}`);
      console.log(`Error message: ${response.data.ReturnMsg}`);
    }
    
    return response.data;
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Error Response Data:', JSON.stringify(error.response.data, null, 2));
      return error.response.data;
    }
    return { ReturnCode: 'ERROR', ReturnMsg: error.message };
  }
}

// Main function
async function main() {
  console.log('\n=== SINGLE PRODUCTION API CALL ===');
  console.log('Attempting to top up phone number: **********');
  console.log('Product: ********** - Chuan Hung Instant Recharge – Migrant $150');
  console.log('Using merchantPassword as encryption key');

  try {
    // Make single call with merchantPassword
    await topUpWithMerchantPassword();
    console.log('\nScript execution complete. One top-up attempt was made.');
  } catch (error) {
    console.error('Failed to complete API call:', error.message);
  }
}

// Run the main function
main().catch(console.error); 