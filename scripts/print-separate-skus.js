const fs = require('fs');
const path = require('path');

// Path to the inventory JSON file
const inventoryPath = path.join(__dirname, 'utils', 'inventory.json');

try {
  // Read and parse the JSON file
  const inventoryData = fs.readFileSync(inventoryPath, 'utf8');
  const products = JSON.parse(inventoryData);
  
  // Filter products with inventory type 'separate'
  const separateProducts = products.filter(product => 
    product.inventory && product.inventory.type === 'separate'
  );
  
  console.log(`Found ${separateProducts.length} products with inventory type 'separate':\n`);
  
  // Print SKUs of matching products
  separateProducts.forEach((product, index) => {
    console.log(`${product.sku}, ${product.name}`);
  });
  
  console.log(`\nTotal SKUs printed: ${separateProducts.length}`);
  
} catch (error) {
  if (error.code === 'ENOENT') {
    console.error('Error: inventory.json file not found at', inventoryPath);
  } else if (error instanceof SyntaxError) {
    console.error('Error: Invalid JSON format in inventory.json');
  } else {
    console.error('Error reading inventory file:', error.message);
  }
  process.exit(1);
}