/**
 * FamilyMart Payment Test with Test Order Creation
 * 
 * This script creates a test order in orders.json and then
 * sends a FamilyMart payment notification for that order.
 * 
 * Run with: node scripts/test-familymart-with-test-order.js
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const crypto = require('crypto');

// Configuration
const config = {
  // Callback endpoint URL
  callbackUrl: 'http://localhost:3000/api/payment/familymart-callback',
  
  // Path to orders.json file
  ordersFilePath: path.join(process.cwd(), 'data', 'orders.json'),
  
  // Merchant credentials
  merchantId: 'MAGSHOP_TEST',
  merchantSecret: 'Mw9p7QxR6aT2sZ8e'
};

// Generate test order details
const testOrderDetails = {
  id: `FMTEST-${Date.now()}`,
  orderNumber: `FM-TEST-${Date.now()}`,
  customerId: '0912345678',
  customerName: 'FamilyMart Test Customer',
  customerEmail: '<EMAIL>',
  customerPhone: '0912345678',
  items: [
    {
      name: 'FamilyMart Test Product',
      price: 1480,
      quantity: 1
    }
  ],
  paymentMethod: 'convenience_store',
  paymentSubMethod: 'family_mart',
  amount: 1480,
  fee: 30,
  totalAmount: 1510,
  currency: 'NT',
  status: 'pending',
  paymentStatus: 'waiting_payment',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  paymentInfo: {
    pinCode: `FM${Math.floor(10000000 + Math.random() * 90000000)}`
  },
  validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days later
  isExpired: false,
  expiryReason: null,
  notes: 'Test order for FamilyMart payment notification simulation'
};

// Generate security token
function generateSecurityToken() {
  return crypto
    .createHash('md5')
    .update(`${config.merchantId}|${config.merchantSecret}`)
    .digest('hex')
    .toUpperCase();
}

// Load orders from file
function loadOrders() {
  try {
    const data = fs.readFileSync(config.ordersFilePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error loading orders: ${error.message}`);
    return null;
  }
}

// Save orders to file
function saveOrders(orders) {
  try {
    fs.writeFileSync(config.ordersFilePath, JSON.stringify(orders, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error(`Error saving orders: ${error.message}`);
    return false;
  }
}

// Create test order
function createTestOrder() {
  console.log('📝 Creating test order...');
  
  const orders = loadOrders();
  if (!orders) {
    console.error('❌ Failed to load orders.');
    return null;
  }
  
  // Check if a similar test order already exists and remove it
  const existingIndex = orders.findIndex(o => o.orderNumber === testOrderDetails.orderNumber);
  if (existingIndex !== -1) {
    console.log('ℹ️ Removing existing test order with the same orderNumber.');
    orders.splice(existingIndex, 1);
  }
  
  // Add new test order
  orders.push(testOrderDetails);
  
  // Save updated orders
  const saved = saveOrders(orders);
  if (!saved) {
    console.error('❌ Failed to save orders.');
    return null;
  }
  
  console.log('✅ Test order created successfully:');
  console.log(`   Order Number: ${testOrderDetails.orderNumber}`);
  console.log(`   PIN Code: ${testOrderDetails.paymentInfo.pinCode}`);
  
  return testOrderDetails;
}

// Send FamilyMart notification
async function sendNotification(statusCode, order) {
  console.log(`\n📤 Sending FamilyMart notification with status code ${statusCode}...`);
  
  // Current date and time
  const now = new Date();
  const orderDate = now.toISOString().replace(/T/, ' ').replace(/\..+/, '');
  const paymentDate = new Date(now.getTime() + 1000 * 60 * 5) // 5 minutes later
    .toISOString().replace(/T/, ' ').replace(/\..+/, '');
  
  // Calculate security token
  const securityToken = generateSecurityToken();
  
  // Create payload
  const payload = {
    EC_ID: config.merchantId,
    PIN_CODE: order.paymentInfo.pinCode,
    ORDER_NO: order.orderNumber,
    ORDER_DATE: orderDate,
    AMOUNT: order.totalAmount.toString(),
    STATUS_CODE: statusCode,
    PAYMENT_NO: `PAY${Date.now()}`,
    PAYMENT_DATE: paymentDate,
    STORE_ID: 'FAM1234',
    BARCODE1: `B1${Math.floor(Math.random() * 1000000000000)}`,
    BARCODE2: `B2${Math.floor(Math.random() * 1000000000000)}`,
    BARCODE3: `B3${Math.floor(Math.random() * 1000000000000)}`,
    SECURITY_TOKEN: securityToken
  };
  
  try {
    // Make the POST request
    const response = await axios.post(config.callbackUrl, payload, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    // Display response
    console.log('\n✅ Response received:');
    console.log(`Status: ${response.status}`);
    console.log('Headers:', response.headers);
    console.log('Body:', response.data);
    
    console.log('\n🎉 Notification sent successfully!');
    
    // Check for successful result code
    if (response.data.includes('<RESULT_CODE>0</RESULT_CODE>')) {
      console.log('✅ Server confirmed successful processing.');
    } else {
      console.log('⚠️ Server returned a non-zero result code.');
    }
    
    return response;
  } catch (error) {
    console.error('\n❌ Error sending notification:');
    
    if (error.response) {
      // The request was made and the server responded with a status code outside 2xx
      console.error(`Status: ${error.response.status}`);
      console.error('Headers:', error.response.headers);
      console.error('Body:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received. Is your server running?');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }
    
    return null;
  }
}

// Run the complete test
async function runTest() {
  console.log('🚀 Starting FamilyMart Payment Test with Test Order Creation...');
  
  // Step 1: Create a test order
  const order = createTestOrder();
  if (!order) {
    console.error('❌ Failed to create test order. Exiting test.');
    return;
  }
  
  // Step 2: Check if the server is running
  try {
    console.log('\n🔍 Checking if callback endpoint is available...');
    await axios.options(config.callbackUrl);
    console.log('✅ Endpoint is available!');
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('❌ Callback endpoint is not available. Make sure your server is running.');
      return;
    }
    console.log('✅ Endpoint exists (received response).');
  }
  
  // Step 3: Send a payment notification with status 0 (success)
  await sendNotification('0', order);
  
  console.log('\n🔍 To check the results, examine the orders.json file.');
  console.log('The test order should now have paymentStatus: "paid" and status: "processing"');
}

// Run the test
runTest(); 