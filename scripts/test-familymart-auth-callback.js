/**
 * FamilyMart Payment Notification Test Script with Authentication
 * 
 * This script simulates payment notifications from the FamilyMart payment system
 * to test the authentication mechanism in the callback endpoint.
 * 
 * Run with: node scripts/test-familymart-auth-callback.js
 */

const axios = require('axios');
const crypto = require('crypto');
const readline = require('readline');

// Configuration
const config = {
  // Callback endpoint URL - update this to your actual URL when testing in production
  callbackUrl: 'http://localhost:3000/api/payment/familymart-callback',
  
  // Merchant credentials - these should match what's in your callback endpoint
  merchantId: 'MAGSHOP_TEST',
  merchantSecret: 'Mw9p7QxR6aT2sZ8e',
  
  // Test order details
  testOrder: {
    orderNo: `TEST-FM-${Date.now()}`,
    pinCode: `FM${Math.floor(10000000 + Math.random() * 90000000)}`,
    amount: '1500',
    storeId: 'FAM1234'
  }
};

// Create readline interface for interactive prompts
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Generate security token using the same algorithm as in the callback endpoint
function generateSecurityToken() {
  return crypto
    .createHash('md5')
    .update(`${config.merchantId}|${config.merchantSecret}`)
    .digest('hex')
    .toUpperCase();
}

// Function to send a test notification
async function sendTestNotification(statusCode, includeToken = true, useCorrectMerchantId = true) {
  // Current date and time
  const now = new Date();
  const orderDate = now.toISOString().replace(/T/, ' ').replace(/\..+/, '');
  const paymentDate = new Date(now.getTime() + 1000 * 60 * 5) // 5 minutes later
    .toISOString().replace(/T/, ' ').replace(/\..+/, '');
  
  // Calculate security token
  const securityToken = includeToken ? generateSecurityToken() : undefined;
  
  // Create payload
  const payload = {
    EC_ID: useCorrectMerchantId ? config.merchantId : 'WRONG_MERCHANT_ID',
    PIN_CODE: config.testOrder.pinCode,
    ORDER_NO: config.testOrder.orderNo,
    ORDER_DATE: orderDate,
    AMOUNT: config.testOrder.amount,
    STATUS_CODE: statusCode,
    PAYMENT_NO: `PAY${Date.now()}`,
    PAYMENT_DATE: paymentDate,
    STORE_ID: config.testOrder.storeId,
    BARCODE1: `B1${Math.floor(Math.random() * 1000000000000)}`,
    BARCODE2: `B2${Math.floor(Math.random() * 1000000000000)}`,
    BARCODE3: `B3${Math.floor(Math.random() * 1000000000000)}`
  };
  
  // Add security token if needed
  if (securityToken) {
    payload.SECURITY_TOKEN = securityToken;
  }
  
  console.log('\n📤 Sending FamilyMart payment notification with payload:', payload);
  
  try {
    // Make the POST request
    const response = await axios.post(config.callbackUrl, payload, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    // Display response
    console.log('\n✅ Response received:');
    console.log(`Status: ${response.status}`);
    console.log('Headers:', response.headers);
    console.log('Body:', response.data);
    
    return response;
  } catch (error) {
    console.error('\n❌ Error sending notification:');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Status: ${error.response.status}`);
      console.error('Headers:', error.response.headers);
      console.error('Body:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }
    
    return null;
  }
}

// Test scenarios
async function testValidSuccessful() {
  console.log('\n🧪 TEST CASE: Valid successful payment with authentication');
  return sendTestNotification('0', true, true);
}

async function testValidPending() {
  console.log('\n🧪 TEST CASE: Valid pending payment with authentication');
  return sendTestNotification('1', true, true);
}

async function testValidExpired() {
  console.log('\n🧪 TEST CASE: Valid expired payment with authentication');
  return sendTestNotification('2', true, true);
}

async function testValidCancelled() {
  console.log('\n🧪 TEST CASE: Valid cancelled payment with authentication');
  return sendTestNotification('3', true, true);
}

async function testInvalidMerchantId() {
  console.log('\n🧪 TEST CASE: Invalid merchant ID');
  return sendTestNotification('0', true, false);
}

async function testMissingSecurityToken() {
  console.log('\n🧪 TEST CASE: Missing security token');
  return sendTestNotification('0', false, true);
}

// Main menu
function showMenu() {
  console.log('\n🔄 FamilyMart Payment Test Options:');
  console.log('1. Test Valid Successful Payment (STATUS_CODE=0)');
  console.log('2. Test Valid Pending Payment (STATUS_CODE=1)');
  console.log('3. Test Valid Expired Payment (STATUS_CODE=2)');
  console.log('4. Test Valid Cancelled Payment (STATUS_CODE=3)');
  console.log('5. Test Invalid Merchant ID');
  console.log('6. Test Missing Security Token');
  console.log('0. Exit\n');
  
  rl.question('Select an option: ', async (answer) => {
    switch (answer.trim()) {
      case '1':
        await testValidSuccessful();
        setTimeout(showMenu, 1000);
        break;
      case '2':
        await testValidPending();
        setTimeout(showMenu, 1000);
        break;
      case '3':
        await testValidExpired();
        setTimeout(showMenu, 1000);
        break;
      case '4':
        await testValidCancelled();
        setTimeout(showMenu, 1000);
        break;
      case '5':
        await testInvalidMerchantId();
        setTimeout(showMenu, 1000);
        break;
      case '6':
        await testMissingSecurityToken();
        setTimeout(showMenu, 1000);
        break;
      case '0':
        console.log('\nExiting FamilyMart Test. Goodbye! 👋');
        rl.close();
        break;
      default:
        console.log('Invalid option. Please try again.');
        showMenu();
        break;
    }
  });
}

// Check if the callback endpoint is available before starting
async function checkEndpoint() {
  try {
    console.log(`🔍 Checking if callback endpoint is available at ${config.callbackUrl}...`);
    
    await axios.options(config.callbackUrl);
    console.log('✅ Endpoint is available!');
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('❌ Callback endpoint is not available. Make sure your server is running.');
      return false;
    }
    
    // Even if we get an error response (like 405 Method Not Allowed), the endpoint exists
    console.log('✅ Endpoint exists (received response).');
    return true;
  }
}

// Start the application
async function start() {
  console.log('🚀 Starting FamilyMart Payment Authentication Test...');
  console.log(`📋 Test Order: ${config.testOrder.orderNo}, PIN Code: ${config.testOrder.pinCode}`);
  
  const endpointAvailable = await checkEndpoint();
  
  if (endpointAvailable) {
    showMenu();
  } else {
    console.log('Please start your Next.js application and try again.');
    rl.close();
  }
}

// Run the application
start(); 