// Test API key authentication
const axios = require('axios');

// Different API keys to test
const validKey = 'uX6HsVoPhmapndxrUhDn';
const invalidKey = 'invalid-key';
const noKey = '';

// Base URL
const BASE_URL = 'http://localhost:3000';

// Test a simple endpoint with different keys
async function testApiKey() {
  console.log('Testing API key authentication');
  
  // Test with valid key
  try {
    console.log('\nTesting with valid key:', validKey);
    const validResponse = await axios.get(`${BASE_URL}/api/customer?id=CUST001`, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': validKey
      }
    });
    console.log('Status:', validResponse.status);
  } catch (error) {
    console.error('Error with valid key:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Message:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
  
  // Test with invalid key
  try {
    console.log('\nTesting with invalid key:', invalidKey);
    const invalidResponse = await axios.get(`${BASE_URL}/api/customer?id=CUST001`, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': invalidKey
      }
    });
    console.log('Status:', invalidResponse.status);
  } catch (error) {
    console.error('Error with invalid key:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Message:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
  
  // Test with no key
  try {
    console.log('\nTesting with no key');
    const noKeyResponse = await axios.get(`${BASE_URL}/api/customer?id=CUST001`, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    console.log('Status:', noKeyResponse.status);
  } catch (error) {
    console.error('Error with no key:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Message:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

// Run the test
testApiKey().catch(console.error); 