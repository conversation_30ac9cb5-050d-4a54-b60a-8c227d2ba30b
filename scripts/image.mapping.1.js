// Script to replace default images with appropriate ones from the image list

const fs = require('fs');
const path = require('path');

// Fix path resolution - use paths relative to project root, not scripts directory
const projectRoot = path.resolve(__dirname, '..');

// Read the inventory JSON file
const inventoryPath = path.join(projectRoot, 'utils', 'inventory.json');
const inventory = require(inventoryPath);

// Read and parse the image URLs from the log file
const imageLogPath = path.join(projectRoot, 'scripts', 'mag.sim.images.txt');
const imageLog = fs.readFileSync(imageLogPath, 'utf8');

// Extract all CDN URLs and their filenames
const imageUrls = [];
const urlRegex = /https:\/\/cdn\.jsdelivr\.net\/gh\/abncharts\/abncharts\.public\.1\/abnasia\.org\/([^\s]+)/g;
let match;
while ((match = urlRegex.exec(imageLog)) !== null) {
  const url = match[0];
  const filename = match[1];
  
  // Skip logos and special files like .DS_Store
  if (!filename.includes('logo') && !filename.includes('.DS_Store')) {
    imageUrls.push({ url, filename });
  }
}

// Default image URL to look for
const defaultImageUrl = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.1.png";

// Function to extract information from an image filename
function parseImageInfo(filename) {
  let provider = null;
  let price = null;
  let duration = null;
  
  // Extract provider
  if (filename.startsWith('if')) provider = 'IF';
  else if (filename.startsWith('ok')) provider = 'OK';
  else if (filename.startsWith('cw')) provider = 'CW';
  
  // Extract price - several patterns to check
  // Pattern 1: simple price like if150.webp
  const simplePriceMatch = filename.match(/^[a-z]+-?(\d+)\.webp$/);
  if (simplePriceMatch) {
    price = parseInt(simplePriceMatch[1]);
  } 
  // Pattern 2: duration and price like if30d699.webp
  else {
    const durationPriceMatch = filename.match(/(\d+)d(\d+)\.webp$/);
    if (durationPriceMatch) {
      duration = parseInt(durationPriceMatch[1]);
      price = parseInt(durationPriceMatch[2]);
    } 
    // Pattern 3: duration in days like if90days.webp
    else {
      const daysMatch = filename.match(/(\d+)days\.webp$/);
      if (daysMatch) {
        duration = parseInt(daysMatch[1]);
      }
    }
  }
  
  return { provider, price, duration };
}

// Function to find the best matching image for an item
function findMatchingImage(item, imageList) {
  // Extract item details (assuming these properties exist)
  const itemProvider = item.provider || '';
  const itemPrice = item.price || 0;
  const itemName = item.name || '';
  const itemDescription = item.description || '';
  
  // Will store best match and its score
  let bestMatch = null;
  let bestScore = 0;
  
  for (const imageInfo of imageList) {
    const { filename, url } = imageInfo;
    const { provider, price, duration } = parseImageInfo(filename);
    
    // Skip if provider doesn't match
    if (!provider || provider.toUpperCase() !== itemProvider.toUpperCase()) {
      continue;
    }
    
    // Calculate match score
    let score = 1; // Base score for provider match
    
    // Price matching
    if (price && Math.abs(itemPrice - price) < 10) {
      score += 3; // Very close price match
    } else if (price && Math.abs(itemPrice - price) < 50) {
      score += 1; // Somewhat close price match
    }
    
    // Duration matching (if duration exists in image)
    if (duration) {
      // Check if duration appears in item name or description
      const durationStr = duration.toString();
      if (itemName.includes(durationStr) || 
          itemDescription.includes(durationStr + ' ngày') ||
          itemDescription.includes(durationStr + ' day')) {
        score += 2;
      }
    }
    
    // Update best match if this one is better
    if (score > bestScore) {
      bestScore = score;
      bestMatch = url;
    }
  }
  
  // Return the best match if score is at least 2 (provider + something else)
  return bestScore >= 2 ? bestMatch : null;
}

// Count of updated items
let updatedCount = 0;

// Check each inventory item
inventory.forEach((item, index) => {
  // Check if item has the default image
  if (item.image && item.image.includes(defaultImageUrl)) {
    // Find a matching image
    const matchingImageUrl = findMatchingImage(item, imageUrls);
    
    if (matchingImageUrl && !item.image.includes(matchingImageUrl)) {
      // Insert the matching image before the default image
      const defaultIndex = item.image.indexOf(defaultImageUrl);
      item.image.splice(defaultIndex, 0, matchingImageUrl);
      updatedCount++;
      
      console.log(`Updated ${item.name || 'Item #' + index} with image: ${matchingImageUrl}`);
    }
  }
});

// Write the updated inventory back to the file
fs.writeFileSync(inventoryPath, JSON.stringify(inventory, null, 2), 'utf8');
console.log(`\nDone! Updated ${updatedCount} items with appropriate images.`);
