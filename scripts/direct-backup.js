#!/usr/bin/env node

const { DatabaseManager } = require('./db-management.js');

async function main() {
  const backupType = process.argv[2] || 'full';
  
  if (!['full', 'data', 'logs'].includes(backupType)) {
    console.log('❌ Invalid backup type. Valid types: full, data, logs');
    process.exit(1);
  }
  
  const dbManager = new DatabaseManager();
  
  try {
    await dbManager.init();
    console.log(`Creating ${backupType} backup...`);
    await dbManager.createBackup(backupType);
    console.log('✅ Backup completed successfully');
    process.exit(0);
  } catch (error) {
    console.log(`❌ Backup failed: ${error.message}`);
    process.exit(1);
  }
}

main(); 