const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Load configuration
try {
  const configPath = path.join(__dirname, '../components/taiwan/operators/if/config.json');
  console.log('Looking for config at:', configPath);
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

  // Test different encryption methods
  console.log('\n=== IF ENCRYPTION TEST ===');
  console.log('Testing both encryption methods to verify which one works\n');
  
  // Data from documentation
  const expectedEncryptedAccount = 'TewWTILrP+tMlue/DLLlMg==';
  const expectedEncryptedPassword = 'aipi3tjX6V8QXrrr7O4lCg==';
  const sampleText = 'test1';
  const sampleTextExpected = 'aCEjE/E/Oq4d4Z9c67DWcw==';

  // 1. Try using config key and IV (ptWadzG6WfLMs7fi)
  console.log('=== Method 1: Using fixed key from config ===');
  console.log('Key:', config.encryption.key);
  console.log('IV:', config.encryption.iv);
  
  try {
    // Encrypt using config key/iv
    const encryptedAccountConfig = encryptAES(config.merchantID, config.encryption.key, config.encryption.iv);
    const encryptedPasswordConfig = encryptAES(config.merchantPassword, config.encryption.key, config.encryption.iv);
    const encryptedSampleConfig = encryptAES(sampleText, config.encryption.key, config.encryption.iv);
    
    console.log('\nAccount:');
    console.log('- Original:', config.merchantID);
    console.log('- Encrypted:', encryptedAccountConfig);
    console.log('- Expected:', expectedEncryptedAccount);
    console.log('- Matches:', encryptedAccountConfig === expectedEncryptedAccount);
    
    console.log('\nPassword:');
    console.log('- Original:', config.merchantPassword);
    console.log('- Encrypted:', encryptedPasswordConfig);
    console.log('- Expected:', expectedEncryptedPassword);
    console.log('- Matches:', encryptedPasswordConfig === expectedEncryptedPassword);
    
    console.log('\nSample "test1":');
    console.log('- Encrypted:', encryptedSampleConfig);
    console.log('- Expected:', sampleTextExpected);
    console.log('- Matches:', encryptedSampleConfig === sampleTextExpected);
  } catch (error) {
    console.error('Method 1 error:', error.message);
  }
  
  // 2. Try using password as key and IV
  console.log('\n=== Method 2: Using password as key ===');
  const password = config.merchantPassword;
  const passwordKey = password.padEnd(16, ' ').substring(0, 16);
  const passwordIV = password.padEnd(16, ' ').substring(0, 16);
  
  console.log('Password:', password);
  console.log('Key (from password):', passwordKey);
  console.log('IV (from password):', passwordIV);
  
  try {
    // Encrypt using password as key/iv
    const encryptedAccountPassword = encryptAES(config.merchantID, passwordKey, passwordIV);
    const encryptedPasswordPassword = encryptAES(config.merchantPassword, passwordKey, passwordIV);
    const encryptedSamplePassword = encryptAES(sampleText, passwordKey, passwordIV);
    
    console.log('\nAccount:');
    console.log('- Original:', config.merchantID);
    console.log('- Encrypted:', encryptedAccountPassword);
    console.log('- Matches doc:', encryptedAccountPassword === expectedEncryptedAccount);
    
    console.log('\nPassword:');
    console.log('- Original:', config.merchantPassword);
    console.log('- Encrypted:', encryptedPasswordPassword);
    console.log('- Matches doc:', encryptedPasswordPassword === expectedEncryptedPassword);
    
    console.log('\nSample "test1":');
    console.log('- Encrypted:', encryptedSamplePassword);
    console.log('- Matches doc:', encryptedSamplePassword === sampleTextExpected);
  } catch (error) {
    console.error('Method 2 error:', error.message);
  }
  
  // 3. Try using hardcoded key from docs
  console.log('\n=== Method 3: Using hardcoded key from documentation ===');
  const docsKey = 'ptWadzG6WfLMs7fi';
  const docsIV = 'ptWadzG6WfLMs7fi';
  
  console.log('Key (from docs):', docsKey);
  console.log('IV (from docs):', docsIV);
  
  try {
    // Encrypt using docs key/iv
    const encryptedAccountDocs = encryptAES(config.merchantID, docsKey, docsIV);
    const encryptedPasswordDocs = encryptAES(config.merchantPassword, docsKey, docsIV);
    const encryptedSampleDocs = encryptAES(sampleText, docsKey, docsIV);
    
    console.log('\nAccount:');
    console.log('- Original:', config.merchantID);
    console.log('- Encrypted:', encryptedAccountDocs);
    console.log('- Matches doc:', encryptedAccountDocs === expectedEncryptedAccount);
    
    console.log('\nPassword:');
    console.log('- Original:', config.merchantPassword);
    console.log('- Encrypted:', encryptedPasswordDocs);
    console.log('- Matches doc:', encryptedPasswordDocs === expectedEncryptedPassword);
    
    console.log('\nSample "test1":');
    console.log('- Encrypted:', encryptedSampleDocs);
    console.log('- Expected:', sampleTextExpected);
    console.log('- Matches:', encryptedSampleDocs === sampleTextExpected);
  } catch (error) {
    console.error('Method 3 error:', error.message);
  }
    
  console.log('\n=== CONCLUSION ===');
  console.log('Check the results above to determine which encryption method matches the expected values.');
  console.log('Use the matching method in your production code.');
  
} catch (error) {
  console.error('Configuration error:', error.message);
}

// Helper encryption function
function encryptAES(text, key, iv, algorithm = 'aes-128-cbc') {
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(text, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  return encrypted;
} 