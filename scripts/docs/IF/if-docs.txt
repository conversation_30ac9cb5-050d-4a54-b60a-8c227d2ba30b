

全虹VRC系統驗證API 測試用資訊For 京佳
帳號: 01PE0016C
密碼:!Aa12345
APIKEY: pd2es73anm16zhe

API範例
Header


Input

※建立AES128使用網站:  https://www.javainuse.com/aesgenerator
Key / IV : ptWadzG6WfLMs7fi

帳號密碼請使用加密後的資訊
加密後帳號: TewWTILrP+tMlue/DLLlMg==
加密後密碼: aipi3tjX6V8QXrrr7O4lCg==




價格	料號
150	**********
300	**********
599	**********
698	**********
799	1004548392
1498	1000565472
3594	**********
4792	**********
3600	1001690702

全虹VRC系統儲值API介接說明文件

1. 儲值API的交易流程	3
1.1 身分驗證	3
1.2 商品驗證	3
1.3 CALL遠傳CRM完成交易 	3


2. VRC API	4
2.1  production 	4
2.2  staging	4


3. VRC API輸入(出)	5
3.1  api mode	5
3.2  output	5
3.3  output	5


4.API回傳錯誤訊息說明	6

















1. 儲值API交易流程
身分驗證
APIKey 驗證
VRC登入驗證

商品驗證
驗證帳戶是否有可購買的商品
驗證商品ID是否正確
驗證帳戶額度是否可購買該商品

Call遠傳CRM完成交易
驗證門號、商品
儲值預付卡





2. VRC API


API
備註
Production
https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue
正式環境
Staging
https://*************/vrc/VrcService/StoredValue
https://vrc-stg.arcoa.com.tw/vrc/VrcService/StoredValue
測試環境

Option1與Option2擇一使用
[Option1]
 		Call https://*************/vrc/VrcService/StoredValue
[Option2]
因為測試環境domain沒有購買，測試環境要設定DNS解析
到C:\Windows\System32\drivers\etc\hotst 加上下面這行
*************  vrc-stg.arcoa.com.tw

3. VRC API輸入(出)
   Header中需要加上APIKEY以及設定測試環境帳號密碼，需要在全虹測試環境設定，請聯絡VRC負責人，國外IP訪問API或透過國外IP訪問VRC網站也需要向VRC負責人申請白名單
API Mode
Data Format
加密方式
POST
 JSON
Mode :        AES128
CipherMode :  CBC
Key :                ptWadzG6WfLMs7fi
Iv :                  ptWadzG6WfLMs7fi
Ex:
test1加密後 >>>
aCEjE/E/Oq4d4Z9c67DWcw==
Header
Key
Value
Content-Type
application/json
APIKEY
請聯絡VRC負責人添加






Input
Field Name
Field Content
Data Type
Size
Description
Account
登入帳號
nvarchar
50
加密欄位
Password
登入密碼
nvarchar
100
加密欄位
PhoneNumber
門號
nvarchar
10
 
FETOfferID
預付卡商品ID
nvarchar
10




Output
Field Name
Field Content
Data Type
Size
Description
ReturnCode
回覆代碼
nvarchar
4
0000、0004
ReturnMsg
回覆信息
nvarchar
200
請參考錯誤回傳說明
ReturnTXID
交易序號
nvarchar
13
yyyyMMdd+五位數序號

   FETOfferID請參考下表
FETOfferID
Name
PriceNormal
**********
全虹立即儲_外勞語音$150
150
**********
全虹立即儲_外勞語音$300
300
**********
超4代外勞無線飆網30日_$599 (60GB後降速)_立即儲
599
**********
超4代外勞無線飆網30日_$698 (80G後降速)_立即儲
698
**********
超4代外勞無線飆網270日_$3594 (480GB後降速) _立即儲
3594
**********
超4代外勞無線飆網360日_$4792 (640GB後降速) _立即儲
4792

範例

4. API回傳錯誤訊息說明
錯誤訊息
訊息描述
回傳錯誤訊息的系統
APIKey錯誤，請確認是否正確
APIKey與該帳號所屬APIKey不符
全虹VRC
驗證帳戶失敗，請確認帳戶是否正確
系統中無此帳戶
全虹VRC
帳戶尚未啟用，請聯繫全虹 IT
該帳號已存在但尚未被啟用
全虹VRC
帳戶驗證過久，請稍後再試
登入timeout
全虹VRC
EOS系統維護中,請稍後再試
系統目前維護中
全虹VRC
帳戶驗證失敗，請稍後再試
帳號或密碼錯誤
全虹VRC
帳戶額度不足或尚未設定購買群組
該帳號因額度不足或未設定商品群組，導致沒有任何可購買的商品
全虹VRC
請確認商品代號是否有誤
在系統中找不到相符的FETOfferID
全虹VRC
帳戶額度不足或無購買該商品權限
該帳號因額度不足或所屬的商品群組無法購買該商品
全虹VRC
門號為空值，請輸入門號
傳入PhoneNumber為空值
全虹VRC
此門號為4G國人卡,不適用此產品
此門號為4G外勞卡,不適用此產品
此門號為5G國人卡,不適用此產品
此門號為5G外勞卡,不適用此產品
該門號的身分無法儲值所選的商品
全虹VRC
購買商品編號無法儲值
輸入的FETOfferID無法儲值
遠傳CRM
門號有誤請重新輸入
找不到相符的門號
遠傳CRM
門號有誤請確認後再試


遠傳CRM
此門號狀況無法儲值


遠傳CRM
CRM系統維護中,請稍後再試


遠傳CRM


全虹VRC系統驗證API介接說明文件

1. 驗證API流程	3
1.1 身分驗證	3
1.2 商品驗證	3
1.3 CALL遠傳CRM完成驗證 	3


2. VRC API	4
2.1  production 	4
2.2  staging	4


3. VRC API輸入(出)	5
3.1  api mode	5
3.2  output	5
3.3  output	5


4.API回傳錯誤訊息說明	6

















1. 驗證API流程
身分驗證
APIKey 驗證
VRC登入驗證

商品驗證
驗證帳戶是否有可購買的商品
驗證商品ID是否正確
驗證帳戶額度是否可購買該商品

Call遠傳CRM完成驗證
驗證門號、商品





2. VRC API


API
備註
Production
https://vrc.arcoa.com.tw/vrc/VrcService/Storedvalidation
正式環境
Staging
https://*************/vrc/VrcService/Storedvalidation
測試環境













3. VRC API輸入(出)
API Mode
Data Format
加密方式
POST
JSON
Mode :        AES128
CipherMode :  CBC
Key :                ptWadzG6WfLMs7fi
Iv :                  ptWadzG6WfLMs7fi

Ex:
test1加密後 >>>
aCEjE/E/Oq4d4Z9c67DWcw==


Input
Field Name
Field Content
Data Type
Size
Description
Account
登入帳號
nvarchar
50
加密欄位
Password
登入密碼
nvarchar
100
加密欄位
PhoneNumber
門號
nvarchar
10


FETOfferID
預付卡商品ID
nvarchar
10




Output
Field Name
Field Content
Data Type
Size
Description
ReturnCode
回覆代碼
nvarchar
4
0000、0004
ReturnMsg
回覆信息
nvarchar
200




4. API回傳錯誤訊息說明
錯誤訊息
訊息描述
回傳錯誤訊息的系統
APIKey錯誤，請確認是否正確
APIKey與該帳號所屬APIKey不符
全虹VRC
驗證帳戶失敗，請確認帳戶是否正確
系統中無此帳戶
全虹VRC
帳戶尚未啟用，請聯繫全虹 IT
該帳號已存在但尚未被啟用
全虹VRC
帳戶驗證過久，請稍後再試
登入timeout
全虹VRC
EOS系統維護中,請稍後再試
系統目前維護中
全虹VRC
帳戶驗證失敗，請稍後再試
帳號或密碼錯誤
全虹VRC
帳戶額度不足或尚未設定購買群組
該帳號因額度不足或未設定商品群組，導致沒有任何可購買的商品
全虹VRC
請確認商品代號是否有誤
在系統中找不到相符的FETOfferID
全虹VRC
帳戶額度不足或無購買該商品權限
該帳號因額度不足或所屬的商品群組無法購買該商品
全虹VRC
門號為空值，請輸入門號
傳入PhoneNumber為空值
全虹VRC
此門號為4G國人卡,不適用此產品
此門號為4G外勞卡,不適用此產品
此門號為5G國人卡,不適用此產品
此門號為5G外勞卡,不適用此產品
該門號的身分無法儲值所選的商品
全虹VRC
購買商品編號無法儲值
輸入的FETOfferID無法儲值
遠傳CRM
門號有誤請重新輸入
找不到相符的門號
遠傳CRM
門號有誤請確認後再試


遠傳CRM
此門號狀況無法儲值


遠傳CRM
CRM系統維護中,請稍後再試


遠傳CRM



