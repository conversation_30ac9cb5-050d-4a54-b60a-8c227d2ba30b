Here is the English translation of the provided technical API integration documentation for the **<PERSON><PERSON> (全虹) VRC System Verification API**, intended for testing by **京佳**:

---

## <PERSON><PERSON> VRC System Verification API – Test Information for 京佳

**Account:** 01PE0016C
**Password:** !Aa12345
**APIKEY:** pd2es73anm16zhe

---

### API Example

#### Header

#### Input

※ Use this AES128 encryption tool: [https://www.javainuse.com/aesgenerator](https://www.javainuse.com/aesgenerator)
**Key / IV:** `ptWadzG6WfLMs7fi`

Use **encrypted** credentials.

* **Encrypted Account:** `TewWTILrP+tMlue/DLLlMg==`
* **Encrypted Password:** `aipi3tjX6V8QXrrr7O4lCg==`

---

### Price and Product Codes

| Price | Product Code |
| ----- | ------------ |
| 150   | **********   |
| 300   | **********   |
| 599   | **********   |
| 698   | **********   |
| 799   | **********   |
| 1498  | **********   |
| 3594  | **********   |
| 4792  | **********   |
| 3600  | **********   |

---

## Chuan Hung VRC Stored Value API Integration Manual

---

### 1. Stored Value API Transaction Flow

#### 1.1 Identity Verification

* APIKey verification
* VRC login verification

#### 1.2 Product Verification

* Check if the account can purchase any products
* Verify the correctness of the product ID
* Check if account has enough quota for the product

#### 1.3 Call FarEasTone CRM to Complete Transaction

* Verify phone number and product
* Recharge prepaid card

---

### 2. VRC API URLs

| Environment    | URL                                                       |
| -------------- | --------------------------------------------------------- |
| **Production** | `https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue`     |
| **Staging**    | `https://*************/vrc/VrcService/StoredValue`        |
|                | `https://vrc-stg.arcoa.com.tw/vrc/VrcService/StoredValue` |

Use one of the following:

* **Option 1**: Call directly: `https://*************/vrc/VrcService/StoredValue`
* **Option 2**: (If staging domain not purchased)
  Add DNS mapping in `C:\Windows\System32\drivers\etc\hosts`:

  ```
  *************  vrc-stg.arcoa.com.tw
  ```

---

### 3. VRC API Input/Output

#### API Mode

| Method                     | Format | Encryption        |
| -------------------------- | ------ | ----------------- |
| POST                       | JSON   | AES128 (CBC Mode) |
| Key/IV: `ptWadzG6WfLMs7fi` |        |                   |

Example:
`test1` encrypted → `aCEjE/E/Oq4d4Z9c67DWcw==`

#### Request Header

| Key          | Value                         |
| ------------ | ----------------------------- |
| Content-Type | application/json              |
| APIKEY       | Contact VRC admin to register |

---

### Input Parameters

| Field       | Description    | Type     | Size | Note      |
| ----------- | -------------- | -------- | ---- | --------- |
| Account     | Login Account  | nvarchar | 50   | Encrypted |
| Password    | Login Password | nvarchar | 100  | Encrypted |
| PhoneNumber | Phone Number   | nvarchar | 10   |           |
| FETOfferID  | Product ID     | nvarchar | 10   |           |

---

### Output Parameters

| Field      | Description      | Type     | Size |                                     |
| ---------- | ---------------- | -------- | ---- | ----------------------------------- |
| ReturnCode | Response Code    | nvarchar | 4    | e.g., 0000, 0004                    |
| ReturnMsg  | Response Message | nvarchar | 200  |                                     |
| ReturnTXID | Transaction ID   | nvarchar | 13   | Format: `yyyyMMdd` + 5-digit serial |

---

### FETOfferID Reference Table

| FETOfferID | Name                                                  | Price |
| ---------- | ----------------------------------------------------- | ----- |
| ********** | Chuan Hung Instant Recharge – Migrant \$150           | 150   |
| ********** | Chuan Hung Instant Recharge – Migrant \$300           | 300   |
| ********** | 4G Wireless Plan 30 Days – \$599 (60GB then throttle) | 599   |
| ********** | 4G Wireless Plan 30 Days – \$698 (80GB then throttle) | 698   |
| ********** | 4G Wireless Plan 270 Days – \$3594 (480GB)            | 3594  |
| ********** | 4G Wireless Plan 360 Days – \$4792 (640GB)            | 4792  |

---

### 4. API Error Message Description

| Error Message                              | Description                     | Source         |
| ------------------------------------------ | ------------------------------- | -------------- |
| APIKey error                               | APIKey mismatch with account    | VRC            |
| Account validation failed                  | No such account                 | VRC            |
| Account not activated                      | Contact VRC IT                  | VRC            |
| Account validation timeout                 | Try again later                 | VRC            |
| EOS system under maintenance               | Try again later                 | VRC            |
| Account or password incorrect              | Login failed                    | VRC            |
| Insufficient quota or group not assigned   | No products to purchase         | VRC            |
| Invalid product code                       | FETOfferID not found            | VRC            |
| No permission to buy product               | Quota/group issue               | VRC            |
| Phone number missing                       | Provide PhoneNumber             | VRC            |
| Not applicable for 4G/5G local/migrant SIM | Product not compatible with SIM | VRC            |
| Product ID not rechargeable                | Invalid FETOfferID              | FarEasTone CRM |
| Phone number incorrect                     | Retry or check number           | FarEasTone CRM |
| Phone number ineligible                    | Cannot recharge this SIM        | FarEasTone CRM |
| CRM system under maintenance               | Try again later                 | FarEasTone CRM |

---

If you'd like this organized into a formatted PDF or developer-ready markdown, let me know!



09:50 Shi Chengyi VRC stored-value card material number table 3.xlsx
09:52 Shi Chengyi The above are two API documents, a call API format example and a product number table, please refer to
09:52 Cherry Zhang @steven Nguyen
09:55 Shi Chengyi API is divided into verification and recharge, the format is the same, because the test number is not available at present, so please use the verification API during testing first, and after using the random number you enter, as long as you get the message "The number is incorrect, please re-enter", it means the connection is successful. When it is officially launched, you only need to call the recharge API directly.
09:57 Shi Chengyi In addition, if the IP used is not from Taiwan, you need to provide it so that our infra team can add a whitelist
09:59 Shi Chengyi Currently the test account, password, and API KEY are all provided in this sample document. When the official launch comes, please use your current password to encrypt it. The API key is the same in the official environment.
10:00 Shi Chengyi If you have any other questions, please feel free to ask. Thank you
10:03 Cherry Zhang @steven Nguyen
10:03 Cherry Zhang Thanks
10:07 Steven Nguyen Thank you. Let me study that file and then I may have questions. Please help ask your infrastructure team to whitelist this IP address: **************
10:13 Cherry Zhang @施誠奕
10:16 Shi Chengyi Got it. I need a top order for our infra team to add it. It will take some working days. I will let you know when it is completed. Thank you.
10:21 Steven Nguyen Thank you very much
2025.05.08 Thursday
16:33 Shi Chengyi Hello, the whitelist has been added, thank you
17:16 Cherry Zhang @steven Nguyen
17:23 Steven Nguyen Thank you very much. I'll test the connection and update.
2025.05.18 Sunday
10:56 Steven Nguyen Hello. Could you please help clarify later: Do we have to use a specific phone number when testing the API connection? At this point our API connection appears to be successfully connected to your system, but the API response appears to be complaining about an invalid phone number. I've used multiple phone numbers, including the one I see in your test case (0900000001).
10:56 Steven Nguyen Photos
11:06 Cherry Zhang @施誠奕
13:38 Yuxin-IF Because engineers are on holiday or on duty to maintain our company's system,
So if you have any problems with your system, the contact time is between 09:00 and 18:00 on weekdays (Monday to Friday).
2025.05.19 Monday
08:37 Shi Chengyi Because there is no available test number at the moment, as long as you get the message "Incorrect number, please re-enter", the connection is successful.
09:06 Steven Nguyen Photos
09:06 Steven Nguyen So it looks like my API test is returning that message.
