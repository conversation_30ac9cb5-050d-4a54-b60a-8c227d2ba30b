pdfconvertme_attachment.xMOFCv-html.html

Updated:  March  14,  2025
System  version:  1.24.1
Unify  Kelede's  multiple  payment  platforms
WEB  API  Specification  Document
1  
Machine Translated by Google

12.  
15.  
10.  
16.  
1.  
13.  
1.  
Document  modification  history..................................................................................................  4  
Description..........................................................................................................................................  9  Format  
description..........................................................................................................................  9  
URL..........................................................................................................................................  9  API  
specification..................................................................................................................  10  I.  Get  Token  verification  
code.................................................................................  10  1.  PHP  code  
example................................................................................................  11  2. .Net  Framework  (C#)  code  
example................................................................  12  3.  PostMan  setting  
example.................................................................................  13  II.  CVS  convenience  store  bank  
payment.................................................................  14  Qike  new  order.................................................................................................  
14  2.  Order  query.......................................................................................................  19  3.  Order  date  range  
query.................................................................................  23  4.  Change  ibon  payment  
amount.................................................................................  28  5.  6.  APN  proactive  
notification..................................................................................  35  III.  COCS  online  card  
payment...........................................................................  40  Qike  new  card  payment  
order..................................................................  40  2.  Bank  authorization  completion  
report.................................................................  46  3.  Bank  authorization  failure  report.................................................................  
48  4.  Qike  new  card  payment  order-value-added  notification  service.......................  49  5.  Order  
query.................................................................................................  55  6.  Order  date  range  
query.................................................................................  59  7.  Order  authorization  
cancellation.................................................................  63  8.  Specify  the  amount  of  payment  (full  or  partial  
payment) ...............................  66  9.  Order  cancellation  transaction  (full  or  partial  return  refund) ...............................  
69  Qike  new  card  payment  order-Yushan  UnionPay  card.................................................  72  11.  Order  Cancellation  
Transaction  (Return,  Refund)  -  Yushan  UnionPay  Card...................  77  Renewal  Collection  -  Qike  New  Card  
Swipe  Order........................................  80  Renewal  Collection  -  First  Authorization  Completion  
Report..................................  85  14.  Renewal  Collection  -  Status  Modification  (Enable/Suspend/Terminate/Re-
Authorize)................  87  Renewal  Collection  -  Authorization  Card  Number  Modification................................  
90  Renewal  Collection  -  Order  Content  Modification................................................  92  17.  APN  Active  
Notification..........................................................................................  95  IV.  Master  File  
Management..........................................................................................  99
content
2  
Machine Translated by Google

3  
1.  
1.  Change  the  abbreviation  of  the  payment  service  message.................................  109  V.  DPH  mobile  
payment.................................................................................  111  Qike  adds  a  new  mobile  payment  
order.................................................  111  2.  Report  on  bank  authorization  
completion.................................................  113  3.  Report  on  bank  authorization  failure.................................................  
114  4.  Order  query.................................................................................................  115  5.  Order  date  range  
query.................................................................................  117  6.  Order  authorization  
cancellation.......................................................................  119  7.  Order  cancellation  transaction  (return,  
refund).............................................  121  8.  Order  payment  request  (supports  OPEN  wallet  
only).............................................  123  9.  APN  active  notification.................................................................................  
124  
VI.  Attachments................................................................................................  126  1.  2.  Online  card  payment  
parameters ..................................................................  127  3.  CVS  convenience  bank  payment  -  APN  active  
notification  (pay_route)  parameters ..................  128
Machine Translated by Google

1.9  
2020-06-22  
API  Specifications:  Create  order,  add  short_url  field
Customers  add  new  credit  card  orders  -  Value-added  services,  COCS  Customers  add  new  credit  card  orders  -  Yushan
2020-04-22  
2017-12-11  
API  Specification:  Create  order,  add  apn_url  field
2021-06-22  
1.3.4  
2019-09-10  
2017-07-18  
2018-01-30  
1.3.2  
2019-04-29  
COCS  adds  minute-by-minute  feature  to  payment  due  date
1.  Payment  at  CVS  Bank
Payment  by  code  (you  can  receive  payment  notification  immediately,  but  only  supports  7-11)
2021-08-19  
1.  The  information  is  incorrect.  "Payer's  postal  code"  is  required.
2018-06-25  
CVS  Qike  adds  a  new  payment  slip  detail  field
CVS  Qike  adds  new  orders,  and  the  payment  method  adds  the  China  Trust  convenience  store  three-stage  clause
2.  Added  CVS,  COCS,  and  mobile  payment  order  date  range  query  function
1.7.1  Layout  Adjustment
2021-07-20  
API  Specifications
1.3.1  
1.3  
1.3.3  Added  the  function  of  changing  the  abbreviation  of  the  financial  service  message
1.4  Added  ibon  function  to  change  payment  due  date
1.1  Add  APN  file
CVS  Qike  new  orders,  COCS  Qike  new  card  orders,  COCS  Qike  new
2019-07-15  
Add  order  status  13  (card  swipe  confirmation  page)
2020-12-29  
1.0  First  edition
3.  Order  Query  -  Added  parameter  cvs_acquirer_type
2018-04-11  
2018-10-12  
1.6  
1.10  Layout  Adjustment
1.  Added  test  machine  URL  and  text  modification
1.11  
2018-01-11  
CVS  convenience  store  bank  payment-create  order  and  add  check  conditions
2018-06-13  
2.  The  information  is  incorrect.  "Payment  Due  Date"  is  required.
Date  Version  Description
2.  Qike  adds  a  new  order  -  add  a  new  parameter  payment_acquirerType
UnionPay  card  API  adds  electronic  invoice  issuance  function
1.5  
1.7  
1.2  Card  swiping  API  added
2018-03-20  
1.3.5  Added  card_no  field  to  card  order  query
1.8  Added  mobile  payment  service
File  modification  history
4  
Machine Translated by Google

5  
2022-10-26  
1.14  
CVS-APN  proactive  notification
Added  description  of  the  upper  limit  of  each  payment  amount--P.10,  P.37,  P.45,  P.79
CVS  convenience  bank  payment,  new  change  of  ATM  payment  amount
3.  Layout  adjustment  and  text  modification
2022-01-12  
1.15  
2022-04-29  
1.  payment_detail  Add  payment  date  (pay_date)  --P.74
1.  Change  the  test  machine  URL
(payment_detail  ->  storeId)  --P.34  
1.  Added  authorization  code  (auth_code)  for  COCS  order  query  P.51
2.  Add  payment  date  (pay_date)  --P.35
2022-07-07  
3.  Add  code  examples  for  obtaining  Token  in  PHP,  ASP,  and  C#
2.  Add  ibon  payment  store  number  (storeId)  to  CVS  order  date  range  query
1.15.3  Added  explanation  of  the  conditions  for  canceling  transactions  with  E.Sun  UnionPay  cards--P.74
(card_no)  --P.54  
--P.34  
1.13.3  
1.15.2  Change  of  ATM  payment  amount  instructions  adjustment--P.37
--P41ÿP49ÿP54ÿP62ÿP64  
2022-11-10  
1.12  
P.51  
4.COCS  order  query  credit  card  number  is  changed  to  the  first  6  and  last  4  digits  (card_no)  --
2.  Added  authorization  code  (auth_code)  for  COCS  order  date  range  query  P.54
1.13.1  
2.  Do  not  support  TLS1.2,  remove  APS  code  example
3.  Add  payment  amount  (pay_amount)  --P.35
COCS-APN  proactive  notification
1.13.4  
2022-09-29  
2021-09-22  
3.  CVS  APN  proactively  notifies  new  ibon  payment  store  numbers
(payment_detail  ->  auth_card_no)  --P.73  
1.13.2  
2.  payment_detail  Add  payment  amount  (pay_amount)  --P.74
2022-08-16  
2022-11-15  
1.  Add  ibon  payment  store  number  (storeId)  to  CVS  order  query  --P.17
1.  Added  ibon  three-segment  barcode  (payment_detail  ->  ibon_barcode1~3)
5.COCS  order  date  range  query  credit  card  number  changed  to  the  first  6  and  last  4  digits
6.  COCS  APN  proactively  notifies  that  the  credit  card  number  is  changed  to  the  first  6  and  last  4  digits
1.11.1  Layout  Adjustment  and  Text  Modification
--P.21  
Notes  on  adding  payment  information--P.33
2022-05-05  
COCS  adds  PAYUNi  as  acquiring  bank
2021-11-05  
1.13  
2022-01-13  
1.15.1  Adjustment  of  6.  Appendix  Description  Contents--P108-P109
Machine Translated by Google

6  
1.15.4  
2023-02-01  
1.17.3  
2023-04-20  
(yyyy-MM-dd  HH:mm:ss)  --P34,  P78,  P106  COCS  online  card  1.  Add  
card  swiping  order  -  add  
memory  card  number,  UnionPay  card  related  information  P.42  2.  Add  card  swiping  
order  -  value-added  service  -  add  memory  card  number,  UnionPay  card  related  information
1.17  
1.18  
2023-07-07  
2023-03-20  
1.19  
1.19.1  
2022-12-19  
1.  CVS  payment  agency  renamed  "CVS  convenience  store  bank  payment"  
2.  CVS  order  inquiry  reply  data--P19,  P23
1.16  
1.17.1  
--P.51ÿP112  
2023-03-29  
2.1  Add  the  actual  payment  amount  column  in  pay_amount  2.2  Adjust  the  
grant_date  (estimated)  grant  date.  After  payment  is  completed,  the  estimated  funding  date  will  be  
calculated  according  to  the  funding  cycle  of  the  financial  service;  after  the  finance  department  completes  
the  funding  operation,  it  will  be  updated  to  the  actual  funding  date
In  order  to  comply  with  regulations  and  bank  audit  requirements,  starting  from  March  1,  2023,  when  creating  orders  on  the  
Black  Cat  PAY  multi-payment  platform,  you  must  fill  in  the  transaction  name.  The  order_detail  of  the  original  newly  added  
order  has  been  changed  to  be  required,  and  the  maximum  length  has  been  changed  to  500  characters.
1.17.2  
2023-08-16  
Adjust  the  return  time  format  of  APN  active  notification
1.  CVS  Bank  convenience  store  payment  --  P12  
2.  COCS  online  card  payment  --  P42,  P51  3.  DPH  
mobile  payment  --  P86  For  CVS  Bank  
convenience  store  payment,  if  the  payment  service  has  the  PDF  (comprehensive  payment  
slip)  function  enabled,  the  maximum  length  of  order_detail  is  150  characters  to  avoid  
typesetting  errors;  if  there  is  no  PDF  function,  it  is  maintained  at  500  characters.  --P12
CVS  adjusts  the  
payment  date  (pay_date)  and  payment  amount  (pay_amount)  fields  and  puts  them  
below  the  checksum  to  avoid  misunderstanding  as  an  invoice  function  --
P36  CVS  Qike  adds  new  orders,  China  Trust  convenience  store  three-stage  barcode  payment  
(instant  payment  notification,  added  FamilyMart  support)  --
P11~P12  1.  CVS  convenience  store  bank  payment  >  APN  
proactive  notification  In  response  to  China  Trust  convenience  store  three-stage  barcode  
payment  (instant  payment  notification),  add  payment  route  (pay_route)  and  payment  
store  number  (storied;  shared  with  ibon)  --P35
2023-04-17  
2022-12-15  
2.  Attachment  3.  Collection  and  payment  APN  active  notification  (pay_route)  parameters--P112  
1.  Add  new  API  host  URL  and  adjustment  precautions--P7  2.  Add  
PostMan  test  to  obtain  Token  example--P11  3.  APN  reissue  button  
function  description--P30,  P84  4.  When  adding  an  order,  if  the  
B2C  electronic  invoice  function  is  used,  please  fill  in  the  following  two  fields:  Payee's  mobile  
phone  (for  SMS  
notification  after  winning  the  invoice),  payee's  email  (for  B2C  electronic  invoice  issuance  
and  invoice  winning  EMAIL  notification)
Machine Translated by Google

7  
Transfer  payment  account  number  field
Specify  the  payment  amount  (full  or  partial)
Account  field
1.21.4  For  text  modification,  only  one  set  of  mobile  phone  number  can  be  filled  in,  and  the  length  is  limited  to  20  characters.
1.21  
1.20.2  
2023-09-05  
2024-12-31  
(If  this  field  is  not  filled  in,  please  log  in  to  the  Black  Cat  PAY  platform  to  set  the  return  URL
Remove  cr_amount  to  specify  the  payment  amount
2.  CVS  order  date  range  query:  When  payment  is  completed  using  CITIC  ATM,
COCS  online  card  payment,  acquiring  bank  "unified  cash  flow"  adds  ApplePay,
2024-04-03  
CITIC  convenience  store  instant  payment  notification  shares  the  storeId  field
1.21.2  
1.21.3  
3.  APN  active  notification:  When  payment  is  completed  using  CITIC  ATM,  payment  will  be  sent  back
2024-04-30  
Query.  --P15
2024-06-05  
2024-11-21  
APN  designated  delivery  URL  description  text  adjustment:
Order  payment  (support  OPEN  wallet  only)
2024-10-18  
Number  field
2023-10-03  
2.3  Compare  with  APN,  adjust  the  pay_route  and  storeId  fields,  ibon  and
When  APN  is  rolled  back,  the  actual  payment  amount  pay_amount  will  be  returned  to  the  merchant.
1.20  
Payment  amount  change  function
Adjust  the  specification,  CVS  adds  a  new  order,  reply  data  fields  are  the  same  as  order
1.20.1  
1.23  
The  goods  are  then  distributed  to  consumers.
2.  CVS  order  date  range  query:  add  bank  code  field
2.  DPH  Mobile  Payment
1.22.1  Remove  "ChinaTrust"  acquiring  bank  from  CVS  convenience  store  bank  payment
1.21.1  
1.19.3  Remove  old  host  BaseURL  --P8
1.  CVS  order  inquiry:  When  payment  is  completed  using  CITIC  ATM,  the  payment  account  number  will  be  sent  back.
1.  Remove  CVS  convenience  store  bank  payment  "E.Sun  Bank"  acquiring  bank  and  ATM  payment
APN  actual  payment  amount  column  description:  --P34,  P36
2023-12-28  
1.  COCS  online  card  payment
2024-02-18  
The  technology  needs  to  use  the  actual  payment  amount  to  determine  whether  the  payment  is  consistent  with  the  actual  amount  received.
1.CVS  order  inquiry:  add  bank  code  field
Remove  cr_amount  to  specify  the  payment  amount
1.22  Added  the  function  of  adding  new  orders  for  renewal  collection--  P81~P88
2024-10-17  
Can--P51
1.19.2  
2024-12-31  
Add  text  description  to  report  bank  authorization  failure.  Unified  Payment  does  not  support  this  function.
Certainly)
GooglePay,  SamsungPay  payment  method  correspondence--P43
2024-11-08  
Machine Translated by Google

8  
P14~P39  
1.24.1  Appendix  2.  Added  description  of  authorized  products  (period_type)  --  P127
1.24  
CVS  convenience  bank  payment,  add  corresponding  unified  cash  flow  ATM  payment  method  -
2024-02-24  
Order  content  modification  function--P84~P91
2.  Add  renewal  collection,  suspension,  activation,  change  card  number,  reauthorization  and  renewal  collection
2024-03-14  
Machine Translated by Google

Official  environment  BaseUrl  
https://cocs.4128888card.com.tw/
Test  environment  BaseUrl
Ex:  Authorization:  Bearer  VKJ0qMXkD27J5ZaDu8ygWrFuK…  6.  The  system  environment  uses  
TLS1.2  transmission  and  strong  encryption  components.  Please  confirm  whether  the  program  and  system  support  it.
2.  The  URL  for  calling  the  API  consists  of  BaseUrl  plus  the  function  name,  for  example:
urlencodedÿ  
5.  When  the  API  requires  Token  authentication,  the  Token  should  be  placed  in  the  HTTP  Header  for  
transmission  (note:  there  should  be  a  space  between  Bearer  and  Token).
1.  All  transmitted  data  is  processed  in  JSON  format  and  UTF-8  encoding.  2.  If  the  
Content-Type  is  not  specifically  specified  in  the  specification ,  application/json  is  used .  3.  Use .NET's  built-in  
OAuth  authentication  protocol  for  authorization.  4.  The  program  
needs  to  obtain  a  token.  Note  that  the  Content-Type  must  be  specified  as  application/x-www-form-
Provide  contract  customers  with  API  connection  services  for  using  multiple  payment  platforms  CVS  (convenience  store  bank  payment),  
COCS  (online  card  payment)  and  DPH  (mobile  payment  including  OPEN  Wallet  and  iCashPay).
Notes  1.  The  
official  environment  and  the  test  environment  data  are  not  interoperable.  Please  activate  the  required  account  and  password  separately.
https://test.4128888card.com.tw/app  
-  The  official  URL  for  getting  Token  is  "https://cocs.4128888card.com.tw/Token"  -  The  test  URL  for  
getting  Token  is  "https://test.4128888card.com.tw/app/Token"  -  The  official  URL  for  adding  a  new  order  is  
"https://cocs.4128888card.com.tw/api/Collect"  -  The  test  URL  for  adding  a  new  order  is  "https://
test.4128888card.com.tw/app/api/Collect"  3.  If  you  need  to  verify  in  advance,  it  is  recommended  to  use  API  testing  
tools  (such  as  PostMan)  to  perform  a  connection  test  first.
Format  description
illustrate
Website
9  
Machine Translated by Google

expires_in  
string  Member  account
Type  Description
Until  new  Tokens  are  produced  secretly.
grant_type  string  20  Y  
.expires  
Authorize  the  account  to  access  related  functions;  the  token  is  valid  for  3  hours  (the  expiration  time  indicated  by  the .expires  field
Authorization  method  (fixed  password)
string  credential  type
Fields
ÿTransmit  data:
.issued  
Fields
ÿSample  
Please  use  the  same  customer  payment  code  to  obtain  tokens  and  add  new  orders.
API  Password
account  number
token_type  
string  validity  period  (unit:  "seconds",  default  is  1  day)
datetime  Expiration  time  (GMT)
After  the  Token  expires,  the  customer  will  no  longer  be  able  to  use  the  Token  to  access  related  functions  until  the  account  is  re-verified.
Error  
invalid_grant  
Provide  contract  customer  API  account  and  password  verification,  and  generate  a  set  of  time-limited  tokens  for  this  account  after  verification.
Field  Type  Length  Required  Encryption  Description
access_token  string  access  credentials
ÿWrong  reply
ÿReply  information
userName  
error_description  string  The  username  or  password  is  incorrect.
Type  Description
Content-Type:  application/x-www-form-urlencoded  
string  
This  function  obtains  the  username  used  by  toke,  which  is  the  same  as  the  cust_id  used  for  adding  new  orders.
username  string  20  Y  password  string  
20  Y  
datetime  creation  time  (GMT)
1.  Obtain  Token  Verification  Code
API  Specifications
POST /Token  
10  
Machine Translated by Google

“grant_type”:  “password”,  
“username”:  “***********”,  
“password”:  “1q2w”  
{  
}  
"error":  "invalid_grant"  
"error_description":  "The  username  or  password  is  incorrect."
}  
Response  
Success:
{  
1.  PHP  code  examples
"access_token":  "nII7I64yQLVIY8OBeKAn1K…",  "token_type":  
"bearer",  “expires_in":  86399,  
<?php /
**  ÿÿÿÿ  authentication  token  function  **/  function  
get_token($server_url,  $username,  $password)  { $cl  =  
curl_init("$server_url/token");  curl_setopt($cl,  
CURLOPT_SSL_VERIFYPEER,  0);  curl_setopt($cl,  
CURLOPT_SSLVERSION,  6); //TLS  v1.2  curl_setopt($cl,  
CURLOPT_RETURNTRANSFER,  true);  curl_setopt($cl,  
CURLOPT_HTTPHEADER,  "Content-Type:  application/x-www-form-urlencoded");  curl_setopt($cl,  
CURLOPT_POST,  
true);  curl_setopt($cl,  CURLOPT_POSTFIELDS,  
"userName":  "***********",  
".issued":  "  
"grant_type=password&username=$username&password=$password");  
Send  
Tue,  18  Jul  2017  02:53:12  GMT",  
".expires":  "  Wed,  19  Jul  2017  02:53:12  GMT"  
}  
{  
fail:
11  
Machine Translated by Google

2. .Net  Framework  (C#)  code  examples
string  username  =  "Account";  
string  apiPassword  =  "API  Password";  
string  apiURL  =  "https://test.4128888card.com.tw/app/";
} /*  Call  Token,  please  set  the  server_url  according  to  the  official  or  test  
environment*/  $server_url  =  "https://test.4128888card.com.tw/
app";  $token  =  get_token($server_url,  "Account",  "API  Password");
var  client  =  new  HttpClient();  
ServicePointManager.SecurityProtocol  =  SecurityProtocolType.Tls  |  
SecurityProtocolType.Tls11  |  SecurityProtocolType.Tls12;  
var_dump(curl_getinfo($cl));  
curl_close($cl);  
return  NULL;  
}  curl_close($cl);  
return  json_decode($auth_response,  true);  
echo  "Failed  to  authenticate\n";  
$auth_response  =  curl_exec($cl);  if  
($auth_response  ===  false)  {  
string  resultJSON  =  response.Content.ReadAsStringAsync().Result;  
client.BaseAddress  =  new  Uri(apiURL);  
HttpResponseMessage  response  =  client.PostAsync("Token",  
new  
StringContent(string.Format("grant_type=password&username={0}&password={1}",  
HttpUtility.UrlEncode(username),  
HttpUtility.UrlEncode(apiPassword)),  Encoding.UTF8,  
"application/x-www-form-urlencoded")).Result;  
12  
Machine Translated by Google

1.  The  API  URL  to  be  tested  (including  the  complete  BaseUrl  +  function  name)  
2.  It  needs  to  be  specified  as  application/x-www-form-urlencoded  3.  
Please  fill  in  your  financial  service  code  and  API  password
3.  PostMan  configuration  example
13  
Machine Translated by Google

string  
order_amount  
expire_date  
Content-Type:  application/json  
Acquiring  bank  Payment  type  Amount  limit  Payment  receipt  method  Anyuan  Ibon  
convenience  store  payment  20,000  yuan  1.  E-mail  payment  receipt  (PDF)
4.  Designated  Ibon  payment  outlets
only)
payer_name  
DD)  
Codename
The  identification  number  specified  in
If  you  have  enabled  the  function  of  automatically  adding  new  orders  for  repeated  payments  in  Anyuan  ibon,  please  keep  the  maximum  length  of  the  order  number  as  it  is  30  characters.
2.  API  changes  payment  due  date
string  20  Y  
4.  Mobile  phone  short  URL  (mobile  phone  display
string  20  Y  string  
cmd  
Payment  barcode)
CvsOrderAppend)
10  And
string  
6.  Check  payment  QP  code
3.  Mobile  phone  short  URL  (QRCode  column
Type  length  required  description
3.  Automatically  add  new  orders  for  repeated  payments
ÿTransmit  data:
2~3  characters  are  used  for  system  numbering;  the  upper  limit  of  unified  cash  flow  order  length  is  25.
The  same  contract  customer  code  must  be
Payment  Due  Date  (YYYY-MM-
25  Y
50  Y
1.API  changes  payment  amount
Allow  contract  customers  to  add  new  orders  via  API.
(Get  the  payment  code  from  Yijinliu)
Contract  customer  order  number  (contract  customer's
cust_id  
cust_order_no  
Payee's  name
Transaction  code  (fixed  entry
Print  barcode)
Tongyi  Jinliu  ATM  virtual  account  49,999  yuan  mobile  phone  short  URL  (redirect  to  Tongyi  Jinliu
none
1.  Qike  adds  new  orders
Value-added  functions
Fields
Order  amount
2.  SMS  notification
5.  Check  the  payment  store  code
Authorization:  Bearer  [token…]  
number  
AND
POST /api/Collect  
2.  Payment  at  CVS  Bank
14  
Machine Translated by Google

15  
APN  specifies  the  delivery  URL  (if  this  column
(b2c  electronic  invoice  and  invoice
The  platform  sets  the  default  URL  for  postback
1:  Print
Is  an  electronic  invoice  issued  for  this  order?
order_detail  
Acquiring  Bank
string  
string  240  Y  string  20  Y  
Required)
bill
Single  function,  to  avoid  version  error,  limit
payer_email  
string  
AND
(B2C  invoice  winning  SMS  notification)
AND
(Required  if  b2c  is  1)
b2c  
1  
No  fields  are  filled  in.  Please  log  in  to  Black  Cat  PAY
1:  ATM  bank  transfer
payer_postcode  
payer_address  
string  250  
0:  Do  not  print
bill
electronic
Service  is  enabled  PDF  Comprehensive  payment
payer_mobile  
print_invoice  
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
payment_acquirerType  string  2  
Payee's  mobile  phone
(Required  when  acquiring  a  single  payment)
To  comply  with  government  regulations,  please
payment_type  
bill
Payee's  postal  code
0:  ibon  payment
Y  Electronics
apn_url
Payment  slip/commodity  details;  if  there  is  cash  flow
Payment  Methods
1:  Open
string  
500  Y
Payee  Address
3:  Unified  financial  flow
60  Y  Electronic
(When  acquiring  a  single  transaction,  it  is  necessary  to  unify  the  cash  flow
string  2  
(Required  when  acquiring  a  single  payment)
Whether  to  print  paper  invoice  (b2c  is  1
Required  product  details)
value)
Award  Email  Notification)
0:  Do  not  open  (default)
string  
2:  Anyuan
10  And
product_name  
Product  Name  (required  if  b2c  is  1)
string  240  Y  
Only  150  characters  can  be  entered.
Payee  Email
1  
string  
Machine Translated by Google

16  
donate_invoice  
bill
string  20  
buyer_invoice_title  
electronic
electronic
2:  Mobile  phone  barcode
2Incorrect  data,  "Amount  paid  on  behalf  of"  must  be  a  numeric  value.
5  
buyer_bill_no  
fill)
5Qike  has  not  set  a  response  URL.
bill
1  
Leave  it  blank,  and  it  will  be  defaulted  to  Genesis  Fund.
electronic
bill
The  reply  fields  are  the  same  as  (Order  Inquiry)
1  
Buyer's  Uniform  Number
1:  Member  (invoice  platform)  carrier
The  postal  code  to  which  the  buyer's  invoices  are  sent
1  The  cmd  data  is  incorrect.
buyer_invoice_zip  
love_code  
Whether  to  donate  invoice  (b2c  is  1  if  required
4Information  is  incorrect.  "Payment  Due  Date"  is  required.
string  240  Electronic
Y  Electronics
vehicle_type  
Vehicle  Type
Invoice  Title
Love  code  (please  fill  in  when  donating,  such  as
NULL  or  0.
bill
Meeting  919)
Project  Project  Name
bill
bill
string  20  
string  
For:  XXXXX
Buyer's  unified  invoice  delivery  address
string  
string  
Since  this  specification  is  provided  for  use  with  CVS  and  COCS,  the  reply  data  fields  are  shared  and  unused  fields  will  be  marked  with
8  
1.  Donate
electronic
ÿ  Abnormal  message  list
161  Electronics
electronic
vehicle_barcode  
string  
bill
bill
3:  Natural  person  certificate
3Incorrect  data.  "Payment  Due  Date"  must  be  a  date  or  time.  The  system  interprets  the  payment  due  date  you  entered.
buyer_invoice_addr  
0:  Do  not  donate
string  
Carrier  barcode
6.  The  contract  customer  information  is  incorrect.
ÿReply  information
Machine Translated by Google

The  payment  due  date  you  entered  is:  XXXXX
7Contract  customer  data  does  not  exist.  
8  cust_id  (XXXX)  and  token  (XXXX)  do  not  match.  9User  login  
failed,  user  has  been  deactivated.  10Data  error,  "Contract  
customer  order  number"  must  be  filled  in.  11Data  error,  you  have  uploaded  
this  "Contract  customer  order  number":  XXXXX,  and  cannot  upload  it  again.  12Data  error,  "Contract  customer  order  number"  
exceeds  the  length  limit  of  30  characters.  13Data  error,  "Amount  paid  on  behalf  of"  
must  be  a  numeric  value.  14Data  error,  "Amount  paid  on  behalf  
of"  cannot  have  decimal  places .  15Data  error,  "Amount  paid  on  
behalf  of"  must  be  less  than  XXXXX  16Data  error,  "Amount  paid  on  behalf  
of"  must  be  greater  than  XXXXX  17Data  error,  "Payment  due  date"  must  be  
between  XXXXX  and  XXXXX  in  the  future.
18Information  error,  "Payer's  Postal  Code"  exceeds  the  length  limit  of  10  characters.  
19Information  error,  "Payer's  Postal  Code"  is  required.  
20Information  error,  "Payer's  Mobile  Number"  exceeds  the  length  limit  of  30  characters.  
21Information  error,  "Payment  method  is  incorrect"  
22Information  error,  "Amount  paid  on  behalf  of"  must  be  greater  than  the  contract  handling  
fee  XXXXX  23Payment  service  
has  been  deactivated  24Payment  
service  has  not  yet  started  
25Payment  service  has  expired  26Payment  due  date  exceeds  the  effective  
date  of  the  payment  service  
[XXXXX]  27Payer  's  mobile  number  is  required  28Payment  service  includes  
email,  payee's  email  is  required  29Payment  service  includes  PDF  attachment,  payee's  
name  and  address  are  required  
30Add  order  failed!!  31  Electronic  invoice  
function  has  not  been  enabled  32  Whether  to  print  paper  invoices  
is  not  entered  or  the  format  is  incorrect  33  Whether  to  donate  
invoices  is  not  entered  or  the  format  is  incorrect  34  Only  one  of  donation  invoices,  unified  (company  account)  invoices,  
printed  paper  invoices  or  common  carriers  can  be  selected  35  Please  enter  
donation,  paper  printed  invoices  
or  use  invoice  carriers  36  Please  enter  the  carrier  type  
37  The  data  is  incorrect,  "Carrier  
barcode"  is  required  38  The  carrier  barcode  format  is  
incorrect  39  The  data  is  incorrect,  "Product  name"  is  
required  40  The  data  is  incorrect,  the  buyer's  unified  number  is  incorrect  41  The  data  is  incorrect,  "Invoice  header"  is  required
17  
Machine Translated by Google

42Information  error,  "Buyer's  unified  invoice  delivery  postal  code"  is  required  
43Information  error,  "Buyer's  unified  invoice  delivery  postal  code"  format  is  incorrect  
44Information  error,  "Buyer's  unified  invoice  delivery  address"  is  required
18  
Machine Translated by Google

19  
Response  Status
Anyuan  ibon  manufacturer  code
Payment  information  establishment  time
Authorization:  Bearer  [token…]  
ÿReply  information
cust_order_no  
order_amount  
expire_date  
ibon_code  
ibon_shopid  
virtual_account  
st_barcode1  
st_barcode2  
st_barcode3  
bill_amount  
Bill  amount
ÿTransmit  data:
illustrate
cmd  
Payment  due  date  (yyyy-MM-dd)
Handling  Fee
2.  Order  Inquiry
30  Y  Customer  order  number
string  
Type
Customer  order  number
Convenience  store  barcode  3
number  
number  
string  20  Y  Transaction  code  (fixed  into  CvsOrderQuery)
msg  
cust_id  
cust_order_no  
Exception  message,  with  null  value  if  no  exception
Status,  OK  means  successful  processing,  ERROR  means  error
Content-Type:  application/json  
ibon  convenience  store  code
Convenience  store  barcode  1
Convenience  store  fees
Fields
string  
string  
number  order  amount
(The  bill  amount  to  be  printed  on  the  bill  includes  additional
string  20  Y  Contractor  code
Abnormal  situation  occurs  during  processing
status  
string  
Contract  customers  can  query  payment  information  through  API.
string  
ATM  transfer  virtual  account  number
Convenience  store  barcode  2
string  
string  
string  
string  
string  
string  
string  
Type  length  required  description
Fields
There  are  only  two  message  states:  OK  and  ERROR
cs_fee  
create_time  
POST /api/Collect  
Machine Translated by Google

string  
(yyyy-MM-dd  HH:mm:ss )  
3:  Unified  financial  flow
0:  Do  not  print
banknote
number  
(yyyy-MM-dd  HH:mm:ss)  
Do  you  want  to  donate  the  invoice?
2:  Mobile  phone  barcode
Payment  channels  (refer  to  Appendix  3)
Calculate  the  estimated  grant  amount;  Finance  completes  grant  work
cvs_acquirer_type  
pay_amount  
Estimated  funding  date  for  cycle  calculation;  financial  completion  of  funding
number  Order  process  status  code  (refer  to  Attachment  1)
Print  paper  invoice
string  
2:  Anyuan
Acquiring  Bank
Order  process  status  change  date
(The  content  will  be  masked,  only  the  last  5  digits  will  be  displayed)
string  
string  
1:  Member  vehicle  (invoice  platform)
string  
string  
(After  payment  is  completed,  the  service  fee  will  be  charged  according  to  the  payment  service  fee.
string  
bank_id  
Electronic  invoice  related  information  (null  value  if  the  invoice  function  is  not  enabled)
grant_date  
string  
(yyyy-MM-dd  HH:mm:ss )  
string  
(After  payment  is  completed,  the  funds  will  be  allocated  according  to  the  payment  service
3:  Natural  person  certificate
process_code  
process_code_update_time  string  
After  the  operation,  it  will  be  updated  to  the  actual  funding  date)
vehicle_barcode  
donate_invoice  
ATM  payment  transfer  to  account
short_url  
pay_route  
NULL;  does  not  contain  refund  amount
Vehicle  Type
Bank  Routing  Number
number  
string  
vehicle_type  
storeId
Estimated  funding  date  (yyyy-MM-dd)
Payment  date
Carrier  barcode
(ibon  payment)
Short  URL
Estimated  grant  amount.  If  no  value  is  given,  enter  0  or
1:  Print
string  
grant_amount  
Payment  amount,  if  no  value  is  specified,  enter  0  or  NULL
0:  Do  not  donate
string  
After  the  completion  of  the  project,  it  will  be  updated  with  the  actual  amount  of  funding)
Payment  store  number
pay_date  
print_invoice  
20  
Machine Translated by Google

Response  
Success:
string  
string  
string  
invoice_date  
{  
“status”:  “OK”  
random_number  
string  
“cust_order_no”:  “**************”,  “order_amount”:  
50,  “expire_date”:  
“2017-07-18”,  “ibon_code”:  “************”,  
“ibon_shopid”:  “CCAT”,  “virtual_account”:  “”,  
“st_barcode1”:  “”,  “st_barcode2”:  “”,  
“st_barcode3”:  “”,  “bill_amount”:  
50,  “cs_fee”:  0,  
“create_time”:  “2017-07-18  
13:46:55”,  “process_code”:  
2,  “process_code_update_time”:  
“2017-07-18  
13:46:59”,  “pay_amount”:  0,  “pay_date”:  “”,  
“grant_amount”:  0,  
“grant_date”:  “”,  “cvs_acquirer_type”:  “0”,  “short_url“:  “https://bit.ly/3uMMPol“,  
“storeId“:  “110909“,  
ÿSample  
Send  
1:  
Donation  
love  code  
invoice  number  
invoice  date  random  code
“cmd”:  “CvsOrderQuery”,  “cust_id”:  
“***********”,  “cust_order_no”:  
“**************”  
{  
love_code  
invoice_no  
}  
21  
Machine Translated by Google

22  
}  
ÿ  Abnormal  message  list
“status”:  “ERROR”,  “msg”:  
“Failed  to  obtain  the  payment  information  query”
1  The  cmd  data  is  incorrect.  2  
The  contract  customer  data  is  incorrect.  
3  The  contract  customer  data  does  not  
exist.  4  The  user  password  cannot  exceed  40  
characters.  5  cust_id  (XXXX)  and  token  (XXXX)  do  not  match  6  
User  login  failed,  the  user  has  been  deactivated.  7  Parameter  
+  cust_order_no  is  required  8  The  payment  
information  cannot  be  found  9  Failed  to  
obtain  the  payment  information  query
fail:
{  
}  
“ibon_designate_storeid“:  ““,  “bankno“:  ““,  
“bank_id  “:  “808“,  
“print_invoice ”:  “0”,  
“vehicle_type”:  “1”,  
“vehicle_barcode”:  “”,  
“donate_invoice”:  “0”,  “love_code”:  
“”,  “invoice_no”:  “”,  “invoice_date”:  
“”,  “random_number”:  
“”  
Project  Project  Name
Machine Translated by Google

POST /api/Collect  
ÿReply  information
Abnormal  situation  occurs  during  processing
“cmd”:  “CvsOrderListQuery”,  
Content-Type:  application/json  
cust_id  
order_start_date  
Number  of  response  orders
Array  
string  20  Y  
string  20  Y  
There  are  only  two  message  states:  OK  and  ERROR
Send  
3.  Order  date  range  query
(Fixed  into  CvsOrderListQuery)
Order  date  to  date
Type  Description
order_list  
“order_start_date”:  “2021/05/19  00:00:00”,  
Type  length  required  description
Order  date
Codename
(yyyy-MM-dd  HH:mm:ss)  
msg  
ÿTransmit  data:
Status,  OK  means  successful  processing,  ERROR  means  error
(Contents  are  the  same  as  the  order  inquiry  column)
{  
A  maximum  of  800  orders  are  presented  each  time.
cmd  
order_end_date  
Response  Status
ÿSample  
Transaction  Code
status  
(yyyy-MM-dd  HH:mm:ss)  
20  Y
Contract  customers  can  query  payment  information  through  API  and  set  the  order  start  and  end  dates.
Fields
Order  list,  presented  in  Array  format
string  
string  
“cust_id”:  “CV0100000008”,  
Authorization:  Bearer  [token…]  
string  20  Y  string  
Fields
23  
Machine Translated by Google

24  
}  
{  
Response  
Success:
“order_end_date”:  “2021/05/20  00:00:00”,  
"msg":  "",  
"status":  "OK",  
"cust_order_no":  "****************",  
"order_amount":  154,  
"expire_date":  "2021-05-19",  
"ibon_code":  "",  
"ibon_shopid":  "",  
"virtual_account":  "",  
"st_barcode1":  "",  
"st_barcode2":  "",  
"st_barcode3":  "",  
"bill_amount":  0,  
"cs_fee":  0,  
"create_time":  "2021-05-19  16:17:27",  
"process_code":  4,  
"process_code_update_time":  "2021-05-19  17:45:38",  
"pay_amount":154",  
"pay_date":  "2021-05-19  17:45:38",  
"grant_amount":  119,  
"grant_date":  "2021-05-28",  
"cvs_acquirer_type":  null,  
"short_url":  null,  
"storeId":"",  
"bankno":"",  
"bank_id":"808",  
"print_invoice":  "0",  
"vehicle_type":  "1",  
"vehicle_barcode":  "",  
"donate_invoice":  "0",  
"msg":  "Count:2",  
"status":  "OK",  
"order_list":  [ {  
Machine Translated by Google

25  
"msg":  "",  
"status":  "OK",  
"cust_order_no":  "****************",  
"order_amount":  151,  
"expire_date":  "2021-05-19",  
"ibon_code":  "************",  
"ibon_shopid":  "CCAT",  
"virtual_account":  "",  
"st_barcode1":  "",  
"st_barcode2":  "",  
"st_barcode3":  "",  
"bill_amount":  181,  
"cs_fee":  0,  
"create_time":  "2021-05-19  16:15:29",  
"process_code":  4,  
"process_code_update_time":  "2021-05-19  17:45:04",  
"pay_amount":  181,  
"pay_date":  "2021-05-19  17:45:04",  
"grant_amount":  146,  
"grant_date":  "2021-05-28",  
"cvs_acquirer_type":  "2",  
"short_url":  "https://collectmpsf-
uat.azurewebsites.net/Home/ibonBarCodeScan/1661510",  
},  
{  
"love_code":  "",  
"invoice_no":  "",  
"invoice_date":  "",  
"random_number":  ""  
"storeId":  "110909",  
"bankno":"",  
"print_invoice":  "0",  
"vehicle_type":  "1",  
"vehicle_barcode":  "",  
"donate_invoice":  "0",  
"love_code":  "",  
"invoice_no":  "",  
"invoice_date":  "",  
Machine Translated by Google

26  
"msg":  "",  
"status":  "OK",  
"cust_order_no":  "****************",  "order_amount":  
235,  "expire_date":  "2021-05-19",  
"ibon_code":  "",  "ibon_shopid":  "",  
"virtual_account":  "",  
"st_barcode1":  "",  
"st_barcode2":  "",  "st_barcode3":  
"",  "bill_amount":  0,  
"cs_fee":  0,  "create_time":  
"2021-05-19  16:12:23",  
"process_code":  4,  
"process_code_update_time":  "2021-05-19  17:44:40",  
"pay_amount":235,  
"pay_date":  "2021-05-19  17:44:40",  "grant_amount":  200,  "grant_date":  
"2021-05-28",  
"cvs_acquirer_type":  null,  "short_url":  null,  "storeId":  "",  
"bankno":"",  "print_invoice":  "0",  
"vehicle_type":  "1",  "vehicle_barcode":  "",  
"donate_invoice":  "0",  "love_code":  "",  
"invoice_no":  "",  
"invoice_date":  "",  
"random_number":  
""  
}  
},  
{  
"random_number":  ""  
}  
]  
Machine Translated by Google

27  
"msg":  "Count:0",  "status":  
"OK",  "order_list":  []  
ÿ  Abnormal  message  list
}  
No  data:  {
1  The  cmd  data  is  incorrect.  2  
The  contract  customer  data  is  incorrect.  
3  The  contract  customer  data  does  not  
exist.  4  The  user  password  cannot  exceed  40  
characters.  5  cust_id  (XXXX)  and  token  (XXXX)  do  not  match  6  
User  login  failed,  the  user  has  been  deactivated.  7  Parameters  
+  cust_order_no,  order_start_date,  order_end_date  are  required  8  The  payment  information  cannot  be  
found  9  Failed  to  obtain  the  payment  
information  query
Project  Project  Name
Machine Translated by Google

POST /api/Collect  
(YYYY-MM-DD)  
Manufacturer  code  (CCAT,  BCAT)
Fields
Customer  order  number
No
cmd  
ibon_code  
20  Y
Response  Status
ÿOnly  supports  orders  with  Anyuan  as  the  acquirer,  not  supported  by  Unified  Payment
(Fixed  into  CvsIbonUpdate)
Payment  slip
ibon  code
Status,  OK  means  successful  processing,  ERROR  means  error
4.  Change  ibon  payment  amount
Authorization:  Bearer  [token…]  
Amount  paid  on  behalf  of  others
cust_id  
cust_order_no  
14  And
illustrate
string  
string  
ÿTransmit  data:
Codename
Fields
The  amount  of  the  payment  slip  after  the  change
number  
ÿPlease  do  not  change  the  payment  amount  after  the  consumer  prints  the  payment  slip  at  the  ibon  machine,  as  this  may  cause  the  actual  payment  amount  to
ÿReply  information
Type
Abnormal  situation  occurs  during  processing
ibon  code
ibon  can  verify  the  order  amount  online  when  the  machine  prints  the  payment  slip,  so  this  API  is  only  for  ibon  orders.
Transaction  Code
30  Y
string  
string  
There  are  only  two  message  states:  OK  and  ERROR
Payment  Due  Date
Content-Type:  application/json  
AND
The  order  number  must  be  an  established
string  20  Y  string  
Contract  customers  can  change  the  payment  amount  for  newly  added  orders  with  ibon  payment  method  through  API;
Type  length  required  description
status  
string  
cust_order_no  
order_amount  
expire_date  
string  20  Y  
order_amount  
bird_shopid  
bird_code
number  
string  
28  
Machine Translated by Google

29  
“order_amount”:  80,  
“order_amount”:  50,  
“cs_fee”:  0  
st_barcode2  
string  
number  
success:
“st_barcode1”:  “”,  
virtual_account  
string  
string  
string  
“cust_id”:  “***********”,  
“status”:  “OK”  
“st_barcode3”:  “”,  
Anyuan  ibon  manufacturer  code
Convenience  store  barcode  3
Send  
Response  
“virtual_account”:  “”,  
“status”:  “ERROR”,  
fail:
Convenience  store  barcode  1
cs_fee  
st_barcode3  
bill_amount  
“cust_order_no”:  “**************”,  
“ibon_code”:  “************”  
st_barcode1  
“cust_order_no”:  “**************”,  
“ibon_code”:  “************”,  
“bill_amount”:  80,  
bird_shopid
Convenience  store  fees
“cmd”:  “CvsIbonUpdate”,  
{  
“st_barcode2”:  “”,  
Convenience  store  barcode  2
}  
ÿSample  
{  
string  
Bill  amount
“expire_date”:  “2017-07-18”,  
“ibon_shopid”:  “CCAT”,  
}  
Payment  Account  Number  (ATM  Transfer  Account  Number)
number  
“ibon_shopid”:  “CCAT”,  
{  
Machine Translated by Google

30  
ÿ  Abnormal  message  list
Project  Project  Name
}  
“msg”:  “Change  ibon  payment  amount  failed”
For:  XXXXX
9  The  customer  has  not  set  a  response  
URL.  10  The  parameter  +cust_order_number  is  required  11  The  
parameter  +order_amount  is  required  12  The  parameter  
+ibon_code  is  required  13  The  parameter  
+ibon_shopid  is  required  14  The  contract  customer  
cannot  change  the  amount  15  The  payment  
slip  data  does  not  exist  16  The  payment  
slip  does  not  allow  the  amount  to  be  changed  
17  The  parameter  ibon_shopid  is  incorrect  18  The  
payment  slip  status  cannot  change  the  amount  19  The  
"amount  paid  on  behalf  of"  for  the  payment  slip  cannot  be  less  than  0  yuan  
20  The  "amount  paid  on  behalf  of"  for  the  payment  slip  must  be  greater  than  the  customer  handling  
fee  XXXXX  21  The  bill  amount  after  the  change  exceeds  the  single  payment  limit  of  20,000  yuan
1  The  cmd  data  is  incorrect.  2  The  
contract  customer  data  is  incorrect.  3  The  
contract  customer  data  does  not  exist.  4  
The  user  password  cannot  exceed  40  characters.  5  
cust_id  (XXXX)  and  token  (XXXX)  do  not  match  6  The  user  login  failed,  
the  user  has  been  deactivated.  7  The  data  is  incorrect,  "Amount  
paid  on  behalf  of"  must  be  a  value.  8  The  data  is  incorrect,  "Payment  
due  date"  must  be  a  date  or  time.  The  system  interprets  the  payment  due  date  you  entered
Machine Translated by Google

POST /api/Collect  
32  Y
Payment  Due  Date
ibon  code
Verify  the  verification  code  against  the  checksum
ÿTransmit  data:
Codename
(cust_order_no  +  ":"  +  
(YYYY-MM-DD)  
Raw:HHNNSSRRRR
ÿPlease  do  not  change  the  payment  due  date  after  the  consumer  prints  the  payment  slip  at  the  ibon  machine,  as  this  may  cause  data  discrepancies.
cmd  
Payment  slip
string  
string  20  Y  string  
Number  of  digits)
5.  Change  of  ibon  payment  due  date
Transaction  Code
checksum  
bird_shopid  
bird_code
AND
nonce  
symbol.
Same,  same  MD5  result  means
Authorization:  Bearer  [token…]  
cust_id  
cust_order_no  
string  20  Y  
The  amount  of  the  payment  slip  after  the  change
string  
14  And
The  number  combination  uses  hours,  minutes,  seconds  +  random  numbers
RRRR:  Four-digit  random  number
checksum  =  MD5  
ÿOnly  supports  orders  with  Anyuan  as  the  acquirer,  not  supported  by  Unified  Payment
Fields
30  Y
Manufacturer  code  (CCAT,  BCAT)
HHNNSS:  host  time  in  hours,  minutes,  and  seconds  (6
string  
Type  length  required  description
AND
order_amount  
expire_date  
string  20  Y  string  
Contract  customers  can  change  the  payment  due  date  for  newly  added  orders  with  payment  method  of  ibon  through  API.
(Fixed  into  CvsIbonUpdateDate)
Random  numbers,  no  repeating  time  +  random
10  And
Does  the  content  match  the  result  of  the  following  operation?
Content-Type:  application/json  
The  order  number  must  be  an  established
number  
The  content  of  this  message  is  consistent  with  the  original  data
31  
Machine Translated by Google

32  
“cmd”:  “CvsIbonUpdateDate”,  
ÿSample  
Payment  Account  Number  (ATM  Transfer  Account  Number)
virtual_account  
Acquiring  bank  (default  0:  E.Sun)
string  
(YYYY-MM-DD)  
{  
Convenience  store  fees
number  
status  
Amount  paid  on  behalf  of  others
bird_code  
bird_shopid
string  
number  Order  status
(Estimated)  Grant  Amount
order_amount  +":"  +  nonce )  
If  the  processing  is  successful,  ERROR  means  an  error  occurred  during  processing.
short_url  
string  
number  
Bill  amount
number  
Short  URL
“order_amount”:  250,  
number  
“cust_id”:  “***********”,  
Response  Status
Anyuan  ibon  manufacturer  code
Payment  Due  Date
string  
string  
string  
Convenience  store  barcode  2
Type
Order  status  change  time
string  
string  
string  
Disbursement  Date  (yyyy-MM-dd)
Send  
Fields
Customer  order  number
expire_date  
Order  creation  date
Payment  date  (yyyy-MM-dd  HH:mm:ss )
string  
“cust_order_no”:  “**************”,  
There  are  only  two  message  states:  OK  and  ERROR.
Convenience  store  barcode  3
cust_order_no  
order_amount  
string  
ÿReply  information
Normal  condition
st_barcode1  
st_barcode2  
st_barcode3  
bill_amount  
cs_fee  
create_time  
process_code  
process_code_update_time  string  string  pay_date  
grant_amount  
grant_date  
cvs_acquirer_type  
0:  Yushan,  1:  CITIC,  2:  ibon  (Anyuan)
illustrate
ibon  code
Convenience  store  barcode  1
string  
string  
Machine Translated by Google

"msg":  "",  
"status":  "OK",  
"cust_order_no":  "**************",  
"order_amount":  250,  
"expire_date":  "2019-04-07",  
"ibon_code":  "************",  
"ibon_shopid":  "CCAT",  
"virtual_account":  "",  
"st_barcode1":  "",  
"st_barcode2":  "",  
"st_barcode3":  "",  
"bill_amount":  280,  
"cs_fee":  0,  
"create_time":  "2019-04-02  17:34:05",  
"process_code":  6,  
"process_code_update_time":  "2019-04-02  18:14:31",  "pay_date":  
"2019-04-02  18:14:31",  "grant_amount":  220,  
"grant_date":  "",  
"cvs_acquirer_type":  
"2",  "short_url":  null  
}  
{  
fail:
In  this  example,  the  checksum  is  generated  using  MD5(**************:250:21)
Response  
Success:
“checksum”:”  e309160d46bcefaa7dd8db18a23f179f”  
“expire_date”:  “2019-04-07”,  
“ibon_shopid”:  “CCAT”,  
“ibon_code”:  “************”,  
“nonce”:”21”,  
{  
“status”:  “ERROR”,  
“msg”:  “Failed  to  change  ibon  payment  date”
}  
33  
Machine Translated by Google

Project  Project  Name
For:  XXXXX
1  The  cmd  data  is  incorrect.  2  
The  contract  customer  data  is  incorrect.  
3  The  contract  customer  data  does  not  
exist.  4  The  user  password  cannot  exceed  40  
digits.  5  cust_id  (XXXX)  and  token  (XXXX)  do  not  match  6  The  
user  login  failed,  the  user  has  been  deactivated.  7  The  
verification  code  is  incorrect.  8  The  
data  is  wrong,  "Payment  Due  Date"  must  be  a  date  or  time.  The  system  parses  the  payment  due  date  you  entered
ÿ  Abnormal  message  list
9  Parameter  +  cust_order_number  is  required  10  Parameter  
+  order_amount  is  required  11  Parameter  +  
ibon_code  is  required  12  Parameter  +  
ibon_shopid  is  required  13  Payment  slip  data  
does  not  exist  14  Parameter  
ibon_shopid  is  incorrect
34  
Machine Translated by Google

If  you  want  to  enable  this  function,  please  prepare  a  URL  to  receive  notifications  and  apply  to  the  relevant  personnel.  Our  system  will  
use  HTTP  POST  to  send  notifications  to  the  URL  you  specify  when  the  payment  status  of  each  transaction  changes.  The  content  of  
the  notification  includes  all  the  necessary  fields  and  content  of  the  payment  information.  Your  host  program  will  receive  the  payment  
slip  information  encapsulated  in  json  from  the  system,  and  you  can  get  the  payment  slip  content  you  need  from  the  parsed  data.
POST  
api_id  
trans_id  
order_no  
Notes:  1.  If  there  
is  repeated  payment,  the  ibon  payment  three-segment  barcode  and  payment  date  and  amount  will  only  provide  the  last  payment  
information.
Retrun  1.  After  payment  is  completed,  the  APN  transmitter  will  send  a  notification  immediately
2.  After  that,  the  sender  will  send  a  notification  message  every  15  minutes.  A  status  code  will  be  sent  up  to  3  
times.
2.  When  APN  is  rolled  back,  the  actual  payment  amount  pay_amount  will  be  thrown  back  to  the  merchant.  Please  use  the  actual  payment  amount  to  determine  whether  
the  payment  is  consistent  with  the  actual  amount  received  before  disbursing  the  goods  to  the  consumer.  3.  
Uni-President  Payment  does  not  provide  a  way  to  query  ibon  payment  outlets  and  payment  barcodes.
ÿTransmission  data  format
3.  If  the  user  replies  with  a  plain  text  "OK"  message,  it  will  not  be  sent  again.
Field  Description
ÿTransmit  data:  
Field
Description  1.  Encapsulate  the  payment  slip  data  in  json  format  as  the  transmitted  message.
Type  Length  Description  32  
Customer  code,  the  payment  code  issued  when  applying  for  this  service  32  
Each  payment  slip  has  a  unique  transaction  identification  code
6.  APN  active  notification
2.ContentType  =  application/json.  3.  Use  checksum  
verification  code  to  ensure  the  security  of  data.
URL  is  the  Qike  return  URL  set  in  the  backend  management  system  of  the  multi-payment  platform
string  
string  
string  20  Customer  order  number
When  you  use  our  payment  service  and  become  a  contracted  customer,  you  can  use  the  payment  status  update  notification  function  we  provide  for  
the  back-end  system  interface  (Server  To  Server),  so  that  you  can  receive  the  fastest  notification  when  the  status  of  each  payment  transaction  
changes  (for  example:  payment  completed,  payment  order  overdue...),  so  that  your  front-end  and  back-end  systems  can  update  the  order  information  
in  real  time.
Method  
35  
Machine Translated by Google

modify_time  
"st_barcode1":"Convenience  store  barcode  1",
"ibon_barcode2":"ibon  second  barcode",
create_time  
Format:  yyyy-MM-dd  (year-month-day)
string  
nonce  
"ibon_shopid":"ibon  shop  id",  "ibon_code":"ibon  
code",
Reserved  for  use
amount  
I=Invoice  Notification
payment_detail  
"storeId":"payment  store  number",  (Anyuan  ibon)
Object  
string  
Payment  amount
C  =  The  customer  is  destined  to  be  deregistered
(year-month-day  hour:minute:second)
number  
"bank_id":"Bank  code",  
"virtual_account":"Account  number  for  
payment",  "atm_note":"Bank  note",
}  
ÿÿ:  yyyy-MM-dd  HH:mm:ss  
(year-month-day  hour:minute:second)
A  =  Waiting  for  the  payer  to  pay
Kelede  Collection  and  Payment  Service  =  2
J=Invoice  Discount  Number  Notice
"pay_route":"Payment  route",  (refer  to  Attachment  3)
"st_barcode3":"Convenience  store  barcode  3",
expire_time  
"ibon_barcode1":"ibon  first  barcode",
"bankno":"  ATM  payment  transfer  account  number  (partially  obscured)
memo  
string  
number  
E  =  Funds  have  been  reserved  for  the  contractor
payment_code  
"ibon_note":"ibon  note",
25  Payment  order  creation  time,
10  Random  numbers,  non-repeating  time  +  random  number  combination  using  hours  and  minutes
B  =  Payer  has  paid
"st_barcode_note":"Convenience  store  barcode  notes",
{  
Object  
string  25  Payment  overdue  time  (CVS  due  date  in  days)
D  =  Overdue  payment  slip
"ibon_barcode3":"ibon  third  barcode"
code)"
ÿÿ:  yyyy-MM-dd  HH:mm:ss  
1  Status  code
status  
"st_barcode2":"Convenience  store  barcode  2",
string  25  Status  update  time,
36  
Machine Translated by Google

1  Vehicle  Type
1.  Donate
Response  
The  content  of  the  message  is  consistent  with  the  original  data.
pay_amount  to  the  merchant,  please  use  the  actual  payment  amount
vehicle_type  
16  Discount  Order  Number
32  Check  the  verification  code  to  see  if  the  checksum  matches  the  following
pay_amount  
0:  Do  not  print
1  Do  you  want  to  donate  the  invoice?
love_code  
invoice_no  
invoice_date  
random_number  
invoice_discount_no  string  
Seconds  +  random  number  generation:  HHNNSSRRRR
25  Payment  Time
1  Whether  to  print  paper  invoice
string  
string  
string  4  random  code
{  
string  
Goods  to  consumers.
When  APN  falls  back,  the  actual  payment  amount  will  be  returned
1:  Print
2:  Mobile  phone  barcode
The  same  MD5  result  means  that  the
string  0:  
Do  not  donate
10  Invoice  Number
ÿSample  
RRRR:  Four-digit  random  number
pay_date  
string  
string  20  Carrier  barcode
vehicle_barcode  
donate_invoice  
checksum  =  MD5  ( api_id  +":"  +  trans_id  +":"  +  amount+":"  +  status  +":"  
+  nonce )  
3:  Natural  person  certificate
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
print_invoice  
HHNNSS:  Hours,  minutes,  and  seconds  of  the  host  time  (6  digits)
string  
number  The  actual  amount  paid
string  20  Love  code
string  
10  Invoice  date
When  the  customer  uses  the  payment  collection  service,  the  response  message  from  APN
checksum  
The  payment  will  be  made  after  the  actual  amount  is  determined  to  be  consistent  with  the  actual  amount  received.
1:  Member  Vehicle
"api_id":"CV0000000000",  
37  
Machine Translated by Google

"pay_route” :  “IbonPay”,  
"st_barcode1":"*********",  
"st_barcode2":"****************",  
"st_barcode3":"***************",  
"st_barcode_note":"ÿÿ",  
"bank_id":"808",  
"virtual_account":"**************",  
"atm_note":"ÿÿ",  
"ibon_shopid":"CCAT",  
"ibon_code":"************",  
"ibon_note":"ÿÿ",  
“storeId”:  “123456”,  
},  
"memo":"",  
"create_time":"2016ÿ04ÿ03  08:00:00",  
"modify_time":"2016ÿ04ÿ08  08:30:00",  
"nonce":"**********",  
"checksum":"3579609ba3914a49441e98cb7e8a55de",  
"pay_date":"2016ÿ04ÿ08  08:35:00",  
"pay_amount":"1250",  
"print_invoice":"0",  
"vehicle_type":"2",  
"vehicle_barcode":"/1234567",  
"donate_invoice":"",  
"love_code":"",  
"invoice_no":"",  
"ibon_barcode1":"0810025G5",  
"ibon_barcode2":"091002QP13137602",  
"ibon_barcode3":"***************",  
"bankno":""  
"trans_id":"550e8400e29b41d4a716446655440000",  
"order_no":"*********",  
"amount":1250,  
"expire_time":"2013ÿ09ÿ28",  
"status":"D",  
"payment_code":2,  
"payment_detail":  {  
38  
Machine Translated by Google

MD5(CV0000000000:550e8400e29b41d4a716446655440000:1250:D:**********)  
I.  APN  Reissue
}  In  this  example,  the  checksum  is  generated  by
"invoice_date":"",  
"random_number":"",  
"invoice_discount_no":""  
Or  to  supplement  APN  information.
In  "Payment  Collection  Operation  >  Order  Inquiry  >  Details",  an  APN  reissue  button  is  provided  so  that  users  can  test  APN  repeatedly
39  
Machine Translated by Google

50  
The  first  eight  digits  are  the  year,  month  and  day,  and  the  last  six  digits  are
Designated  acquiring  bank
order_detail  
Content-Type:  application/json  
Contract  order  number  (customer-defined)
The  upper  limit  of  the  amount  is  based  on  the  contract  specifications  (preset
When  the  field  is  empty,  it  means  that  all  products  are  allowed.
string  20  Y  
When  empty,  the  system  will  automatically  generate
Order/Product  Details
number  
limit_product_id  
(Fixed  into  CocsOrderAppend)
Identification  number,  which  is  the  same  as
Order/Transaction  Amount
Limited  product  type  (optional,  when  this
string  20  Y  
500  Y
Type  length  required  description
string  20  Y  string  
Codename
The  number  is  YYYYMMDD######,
1  Starts  to  accumulate,  for  example:
ÿTransmit  data:
Required  to  comply  with  government  regulations
chinatrust:  China  Trust
AND
Contract  customers  add  credit  card  orders  through  the  API  without  SMS  or  email  notifications.
cmd  
one).  No  entry  is  required.
100,000)
order_amount  
string  
Transaction  Code
30  Y
**************  
The  contract  customer  code  must  be
1.  Qike  adds  credit  card  orders
Fields
esun:  E.Sun  Bank
payuni:  unified  financial  flow
string  
Authorization:  Bearer  [token…]  
cust_id  
cust_order_no  
The  order  serial  number  within  the  date,  starting  from
acquirer_type  
POST /api/Collect  
3.  COCS  online  card  payment
40  
Machine Translated by Google

string  
If  this  parameter  has  an  incoming  URL,  authorization  is  successful.
Is  an  electronic  invoice  issued  for  this  order?
vehicle_barcode  
1:  Print
product_name  
string  
New  time,  in  the  format  of  yyyy-MM-dd
string  20  
electronic
1:  Member  Vehicle
bill
Postback
1  
Whether  to  donate  invoice  (required  if  b2c  is  1)
b2c  
10  And
string  
(See  Appendix  2)
print_invoice  
1  
1  
250  
The  transmission  time  must  be  the  longest  during  the  transmission
bill
string  
love_code  
Carrier  barcode
bill
electronic
The  URL  specified  in  the  service  settings  for  successful  authorization
string  
bill
0:  Do  not  print
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
string  
Category.  Each  product  is  separated  by  "|"
60  Y  Electronic
Set  the  return  URL)
vehicle_type  
For  example:  esun.normal|esun.m3
success_url  
The  default  is  Genesis  Foundation  919)
1  
Y  Electronics
1:  Open
If  no  input  is  made,  the  service  will  be  based  on  the  customer  service
electronic
bill
donate_invoice  
Y  Electronics
Required)
send_time  
250  
3:  Natural  person  certificate
string  
No  information,  please  log  in  to  the  Black  Cat  PAY  platform
Order  authorization  successfully  specifies  the  return  URL
Love  code  (select  Donate.  If  the  love  code  is  not  entered
string  
bill
0:  Do  not  open  (default)
The  successful  reply  URL  will  bring  in  this  URL.
electronic
20  
Vehicle  Type
Whether  to  print  paper  invoice  (b2c  is  1
apn_url
HH:mm:ss,  for  example:  2017-07-18
bill
2:  Mobile  phone  barcode
APN  specifies  the  delivery  URL  (if  this  field
Product  Name  (required  if  b2c  is  1)
07:17:25  
1.  Donate
0:  Do  not  donate
string  
41  
Machine Translated by Google

string  240  Y  Electronic
Payee  address  (required  if  b2c  is  1)
payer_mobile  
string  
string  
Card  number  function  is  1,  this  field  is  required)
Please  confirm  that  the  payment  service  is  activated  and  the  acquiring  bank  is  designated  as  payuni  unified  payment  service  before  using  this  function;
electronic
10  Y  Electronic
Please  use  the  following  fields  as  the  basis  for  your  response.  Unused  fields  will  be  filled  with  NULL  or  0.
string  
8  
payer_address  
Abnormal  situation  occurs  during  processing
status  
bill
payer_name  
(b2c  electronic  invoice  and  invoice  winning
string  
0:  Disable  (default)
Payee's  mobile  phone  number  (if  memory  is  enabled)
bill
Buyer's  Uniform  Number
bill
Enable  the  feature  to  remember  credit  card  numbers
Exception  message,  with  null  value  if  no  exception
Since  this  specification  is  available  for  both  CVS  and  COCS,  the  reply  data  fields  are  shared.  If  you  use  COCS  order
is_memory  
string  
string  
Status,  OK  means  successful  processing,  ERROR  means  error
msg  
Whether  to  enable  the  memory  card  number  function
20  Y  Electronic
bill
Payee's  name  (required  if  b2c  is  1)
bill
Payee  Email
payer_mobile  
50  Y  Electronic
EMAIL  Notification)
string  
ÿReply  information
161  
buyer_bill_no  
string  
Other  code
There  are  only  two  message  states:  OK  and  ERROR
Fields
The  materials  have  not  been  tested  by  the  unified  Kelede  system
240  Y  Electronic
string  
string  
electronic
(B2C  invoice  winning  SMS  notification)
fill)
cust_order_no  
20  
buyer_invoice_title  string  
payer_email  
payer_postcode  
Response  Status
This  is  used  as  the  PAYUNi  platform  system  identification
A  value-added  payment  service,  where  all  card  information  is  stored  on  a  unified  payment  platform  to  provide  consumers  with  fast  checkout  and
bill
bill
Type
1  
Payee's  mobile  phone
Invoice  Title
Payee's  postal  code  (required  if  b2c  is  1)
Customer  order  number
illustrate
1:  Enable
42  
Machine Translated by Google

url  
}  fail:
5  cust_id(XXXX)  and  token(XXXX)  do  not  match  6  User  login  failed,  
user  has  been  deactivated.  7  Failed  to  obtain  financial  service  
information  8  Required  fields  are  missing.
ÿSample  
{  
“status”:  “ERROR”,  “msg”:  
“Add  new  card  order  failed”
Send  
{  
}  
“cmd”:  “CocsOrderAppend”,  “cust_id”:  
“***********”,  “cust_order_no”:  
“**************”,  “order_amount”:  50,  “order_detail”:  
“Test  Order  Notes”,  
“acquirer_type”:  “esun”,  “limit_product_id”:  
“esun.normal|esun.m3”,  “send_time”:  
“2017-07-18  01:07:23”
ÿ  Abnormal  message  list
}  
Project  Project  Name
Generate  the  online  credit  card  payment  URL  corresponding  to  the  order
Response  
Success:
{  
1  The  cmd  data  is  incorrect.  2  
The  contract  customer  data  is  incorrect.  
3  The  contract  customer  data  does  not  
exist.  4  The  user  password  cannot  exceed  40  characters.
string  
“status”:  “OK”,  
“cust_order_no”:  “**************”,  “url”:  “https://
goo.gl/K616G6”  
43  
Machine Translated by Google

44  
9  
The  order_detail  format  is  incorrect,  the  content  cannot  contain  Html  Tag.  10  The  
order_amount  format  is  incorrect.  11  The  send_time  
format  is  incorrect.  12  The  send_time  is  abnormal.  
13  The  customer  data  does  not  exist.  
14  The  acquiring  bank  setting  is  
incorrect.  15  You  have  not  applied  for  E.Sun  
Bank  as  the  acquiring  bank.  16  You  have  not  applied  for  China  Trust  
as  the  acquiring  bank.  17  The  customer  has  not  set  the  response  
URL.  18  cust_order_no  must  be  more  than  
three  digits  or  a  minus  sign.  19  The  order  has  expired.  20  The  data  is  incorrect.  You  have  uploaded  
this  "contract  order  number":  
XXXXX.  You  cannot  upload  it  again.  21  The  financial  service  has  been  disabled .  22  The  data  is  incorrect.  The  "amount  paid  on  behalf  of"  must  
be  less  than  XXXXX.  23  The  data  is  
incorrect.  The  "amount  paid  on  behalf  of"  must  be  greater  than  XXXXX.  24  The  bank  
fee  rate  has  not  been  set.  25  The  special  store  code  and  the  deposit  code  KEY  cannot  
be  blank .  26  The  electronic  invoice  function  has  
not  been  enabled.  27  Whether  to  print  a  paper  invoice  is  not  entered  
or  the  format  is  incorrect.  28Whether  the  donation  
invoice  is  not  entered  or  the  format  is  incorrect.  29You  can  only  select  one  
of  the  donation  invoice,  unified  (company  account)  invoice,  printed  
paper  invoice  or  common  carrier.  30Please  enter  the  donation,  paper  printed  invoice  or  invoice  carrier .  31Please  enter  the  carrier  
type.  32Incorrect  data,  "Carrier  barcode"  is  required.  33Incorrect  carrier  barcode  
format.  34Incorrect  data,  "Carrier  
barcode"  is  required.  35Incorrect  carrier  barcode  format.  
36Incorrect  data,  "Product  name"  is  
required.  37Incorrect  data,  "Payer's  name"  is  required.  
38Incorrect  data,  "Payer's  postal  code"  is  
in  the  wrong  format.  39Incorrect  data,  "Payer's  postal  code"  
is  required.  40Incorrect  data,  "Payer's  address"  is  required.  
41Incorrect  data,  invalid  payer's  mobile  phone  number.  42Incorrect  data,  "Payer's  
mobile  phone  number"  is  required.  43Incorrect  data,  invalid  payer's  email  
address.  44Incorrect  data,  "Payer's  Email"  is  required.
Machine Translated by Google

45  
45  The  data  is  incorrect,  the  buyer's  unified  number  is  
incorrect.  46  The  data  is  incorrect,  "Invoice  Header"  is  required.
Machine Translated by Google

46  
The  format  is  yyyy-MM-dd  HH:mm:ss,
For  example:  2017-09-14  10:37:08
hash_base:  used  as  the  base  for  generating  the  chk  check  code
right
(The  delivery  time  entered  in  the  new  order  by  the  customer).
Last  four  digits  of  credit  card  number
Verification  code,  the  verification  code  is  generated  by
ÿReply  information
Order/Transaction  Amount
string  
(Updated  with  every  transmission),
First  concatenate  each  field  with  '$'  and  then  use  MD5  to  perform
2.  Bank  authorization  to  complete  return
Response  Status
When  authorization  is  completed,  redirect  to  the  cocs  URL:  http://10.10.10.100/cocs/authSuccess.php
cust_order_no  
Authorization  Code
string  
string  
ÿSample  
string  
string  
For  example:  2017-09-14  10:31:25
Original  order  delivery  time
Acquiring  transaction  time  (authorization  time),
acquire_time  
Fields
The  format  is  yyyy-MM-dd  HH:mm:ss,
auth_code  
Algorithm  obtained.
Direction  to  Qike  website
Customer  order  number
send_time  
Notification  time,  which  is  the  time  when  the  message  was  sent.
MD5(hash_base+'$'+order_amount+'$'+  
send_time+'$'+ret+'$'+acquire_time  
+'$'+auth_code+'$'+card_no+'$'+  
notify_time+'$'+cust_order_no)  
illustrate
string  
string  
string  
order_amount  
OK  if  authorization  is  successful
When  bank  authorization  is  completed,  redirect  the  client  browser  to  the  URL  specified  by  the  contract  customer:  "Redirect  when  authorization  is  completed"
string  
card_no  
notify_time  
The  system  will  issue  a
Type
The  format  is  yyyy-MM-dd  HH:mm:ss,
For  example:  2017-09-14  10:36:38
chk  
Machine Translated by Google

47  
http://10.10.10.100/cocs/authSuccess.php?ret=OK&cust_order_no=C201709141001&order  
Response  
_amount=2&send_time=2017-09-14  10:31:25&acquire_time=2017-09-14  
10:36:38&auth_code=951294&card_no=1849&notify_time=2017-09-14  
10:37:08&chk=9ea4928734f4099b387c75a344fe3e50  
Machine Translated by Google

48  
(Updated  with  every  transmission),
hash_base:  used  as  the  base  for  generating  the  chk  check  code
3aa9a501ae2c8e58f8dfdcb07af4b7c3
Fields
Original  order  delivery  time
Verification  code,  the  verification  code  is  generated  by
Redirect  to  the  cocs  website  when  authorization  fails:  http://10.10.10.100/cocs/authFail.php
Use  this  function.
Customer  order  number
string  
First  concatenate  each  field  with  '$'  and  then  use  MD5  to  perform
http://10.10.10.100/cocs/authFail.php?ret=FAIL&cust_order_no=C201709141001&order_a  
3.  Report  of  bank  authorization  failure
illustrate
cust_order_no  
order_amount  
string  
ÿSample  
Type
The  format  is  yyyy-MM-dd  HH:mm:ss,
Order/Transaction  Amount
Notification  time,  which  is  the  time  when  the  message  was  sent.
For  example:  2017-09-14  10:37:08
ÿReply  information
Algorithm  obtained.
string  
mount=2&send_time=2017-09-14  10:31:25&notify_time=2017-09-14  10:37:08&chk=  
Redirect  to  Qike  website";  only  E.Sun  Bank  and  China  CITIC  Bank  are  available.  If  the  unified  financial  flow  authorization  fails,  it  will  not  redirect,  so  it  is  impossible
FAIL  when  authorization  fails
send_time  
MD5(hash_base+'$'+order_amount+'$'+  
send_time+'$'+ret+'$'+  
notify_time+'$'+cust_order_no)  
Response  
string  
notify_time  
For  example:  2017-09-14  10:31:25
string  
string  
Response  Status
When  bank  authorization  fails,  redirect  the  client  browser  to  the  URL;  the  contract  customer  specifies  the  "Redirect  when  authorization  fails"  URL.
The  system  will  issue  a
chk  
right
(The  delivery  time  entered  in  the  new  order  by  the  customer).
The  format  is  yyyy-MM-dd  HH:mm:ss,
Machine Translated by Google

POST /api/Collect  
50  
payer_mobile  
30  
Required  to  comply  with  government  regulations
(B2C  invoice  winning  SMS  notification)
ÿTransmit  data:
Codename
string  
order_amount  
Payee's  name
Online  credit  card  payment  link  shortened  URL.
cmd  
cust_id  
cust_order_no  
AND
(If  the  value-added  service  is  enabled  or  memory  is  turned  on
Transaction  Code
4.  Qike  added  credit  card  order-value-added  notification  service
50  Y
The  first  eight  digits  are  the  year,  month  and  day,  and  the  last  six  digits  are
(Default  is  100,000)
string  
string  20  
(The  recipient  of  the  unified  payment  flow  will  be  automatically  brought  in
(If  value-added  services  are  enabled,  this  field  is
Authorization:  Bearer  [token…]  
The  system  will  automatically  generate
string  20  Y  
string  20  Y  string  
Order  amount
Order/Product  Details
string  
Card  number  function,  this  field  is  required)
payer_email  
The  system  will  automatically  send  a  text  message  or  email  to  the  payee  according  to  the  settings.  The  notification  content  includes
Fields
1  Starts  to  accumulate,  for  example:
number  
Payee's  mobile  phone  number
payer_name  
Type  length  required  description
The  upper  limit  of  the  amount  is  subject  to  the  contract  specifications.
The  number  is  YYYYMMDD######,
The  order  serial  number  within  the  date,  starting  from
Contract  customers  add  new  orders  through  the  API  and  provide  value-added  notification  services  as  specified  when  signing  the  contract.
(Fixed  into  CocsOrderAppend2)
order_detail  
500  Y
Payee  Email
Content-Type:  application/json  
The  order  number  of  the  customer.  If  this  field  is  empty
**************  
Required)
49  
Machine Translated by Google

50  
The  URL  specified  in  the  service  settings  for  successful  authorization
(See  Appendix  2)
APN  specifies  the  delivery  URL  (if  this  field
1  
bill
Is  an  electronic  invoice  issued  for  this  order?
07:17:25  
string  20  Y  
1:  Print
string  
string  250  
HH:mm:ss,  for  example:  2017-07-18
bill
If  no  input  is  made,  the  service  will  be  based  on  the  customer  service
Limited  product  type  (optional,  when  this
string  
EMAIL  Notification)
bill
60  Y  Electronic
Postback
10  And
acquirer_type  
1  
string  
1  
product_name  
success_url  
New  time,  in  the  format  of  yyyy-MM-dd
1:  Member  Vehicle
electronic
The  successful  reply  URL  will  bring  in  this  URL.
payuni:  unified  financial  flow
Notification  EMAL  field)  
(b2c  electronic  invoice  and  invoice  winning
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
50  Y
0:  Do  not  print
Designated  acquiring  bank
send_time  
string  
string  
Set  the  return  URL)
The  transmission  time  must  be  the  longest  during  the  transmission
bill
Vehicle  Type
1:  Open
chinatrust:  China  Trust
If  this  parameter  has  an  incoming  URL,  authorization  is  successful.
Required)
string  250  
limit_product_id  
Category.  Each  product  is  separated  by  "|"
print_invoice  
b2c  
No  information,  please  log  in  to  the  Black  Cat  PAY  platform
For  example:  esun.normal|esun.m3
Y  Electronics
0:  Do  not  open  (default)
Order  authorization  successfully  specifies  the  return  URL
esun:  E.Sun  Bank
electronic
Whether  to  print  paper  invoice  (b2c  is  1
string  
apn_url
When  the  field  is  empty,  it  means  that  all  products  are  allowed.
vehicle_type  
Product  Name  (required  if  b2c  is  1)
Machine Translated by Google

51  
love_code  
1.  Donate
string  
Whether  to  enable  the  memory  card  number  function
payer_email  
bill
bill
bill
illustrate
8  
electronic
Payee  address  (required  if  b2c  is  1)
string  
is_memory  
Please  confirm  that  the  payment  service  is  activated  and  the  acquiring  bank  is  designated  as  payuni  unified  payment  service  before  using  this  function;
bill
string  20  
electronic
3:  Natural  person  certificate
payer_mobile  
bill
20  
50  Y  Electronic
electronic
string  20  Y  Electronic
The  materials  have  not  been  tested  by  the  unified  Kelede  system
Type
buyer_bill_no  
Buyer's  Uniform  Number
fill)
Payee's  name  (required  if  b2c  is  1)
string  
Enable  the  feature  to  remember  credit  card  numbers
fill)
240  Y  Electronic
vehicle_barcode  
2:  Mobile  phone  barcode
string  
string  
string  
string  
electronic
Carrier  barcode
bill
Fields
string  
Payee's  mobile  phone  number  (required  if  b2c  is  1)
Payee's  postal  code  (required  if  b2c  is  1)
The  default  is  Genesis  Foundation  919)
EMAIL  Notification
status  
1:  Enable
161  
Payee  Email  (Required  if  b2c  is  1)
bill
bill
Response  Status
Invoice  Title
string  
1  
0:  Do  not  donate
ÿReply  information
bill
payer_address  
payer_name  
Love  code  (select  Donate.  If  the  love  code  is  not  entered
msg  
0:  Disable  (default)
Issue  electronic  invoices  and  win  invoice  prizes
SMS  notification  after  winning  the  invoice
10  Y  Electronic
Y  Electronics
buyer_invoice_title  string  
Exception  message,  with  null  value  if  no  exception
bill
donate_invoice  
payer_postcode  
Whether  to  donate  invoice  (required  if  b2c  is  1)
1  
A  value-added  payment  service,  where  all  card  information  is  stored  on  a  unified  payment  platform  to  provide  consumers  with  fast  checkout  and
string  240  Y  Electronic
Machine Translated by Google

52  
“status”:  “OK”,  
“cust_order_no”:  “**************”,  “url”:  “https://
goo.gl/K616G6”  
url  
Project  Project  Name
1  The  cmd  data  is  incorrect.
string  
string  
}  
fail:
ÿSample  
Send  
{  
{  
“status”:  “ERROR”,  “msg”:  
“Add  new  card  order  failed”
}  
“cmd”:  “CocsOrderAppend2”,  “cust_id”:  
“***********”,  “cust_order_no”:  
“**************”,  “order_amount”:  50,  “order_detail”:  
“Test  Order  Notes”,  
“payer_name”:  “ÿÿÿ”,  “payer_mobile”:  
“0970325698”,  “payer_email”:  
“<EMAIL>”,  “acquirer_type”:  “esun”,  
“limit_product_id”:  “esun.normal|esun.m3”,  “send_time”:  
“2017-07-18  01:07:23”
There  are  only  two  message  states:  OK  and  ERROR.  OK  means  the  
processing  is  successful,  and  ERROR  means  an  abnormal  situation  occurs  
during  processing.  The  customer's  
order  number  is  generated.  
The  corresponding  online  credit  card  payment  website  of  the  order
Response  
Success:
}  
{  
cust_order_no  
ÿ  Abnormal  message  list
Machine Translated by Google

53  
9  
2Contract  customer  information  is  incorrect.  
3Contract  customer  information  does  not  
exist.  4User  password  cannot  exceed  40  digits.  5  cust_id  
(XXXX)  and  token  (XXXX)  do  not  match  6User  login  failed,  user  has  been  
deactivated.  7Failed  to  obtain  financial  service  information  8Missing  
required  fields.  The  order_detail  format  is  incorrect,  
and  the  content  cannot  contain  Html  
Tag.  10Unauthorized  use  of  service.  11The  payee's  mobile  phone  number  must  be  
entered,  or  the  information  you  entered  
is  incorrect.  12The  payee's  E-mail  must  be  entered,  or  the  information  you  entered  is  incorrect.  
13  The  order_amount  format  is  incorrect.  14  The  send_time  format  is  incorrect.  15  Send_time  is  
abnormal.  16Contract  customer  information  does  not  
exist.  17Acquiring  bank  settings  are  incorrect.  
18You  have  not  yet  applied  for  E.Sun  
Bank  as  an  acquiring  bank.  19You  
have  not  yet  applied  for  China  Trust  as  an  
acquiring  bank.  20The  contract  customer  has  not  set  a  response  
URL.  21  cust_order_no  must  be  an  alphanumeric  character  with  
more  than  three  digits  or  a  minus  sign.  22The  
order  has  expired.  23Incorrect  data.  You  have  already  uploaded  this  "Contract  Order  Number":  
XXXXX.  You  cannot  upload  it  again.  
24The  financial  service  has  been  disabled .  25Incorrect  data.  "Amount  paid  on  behalf  of"  must  be  less  than  XXXXX.  26Incorrect  data.  "Amount  
paid  on  behalf  of"  must  be  greater  
than  XXXXX .  27The  bank  fee  rate  has  not  been  set.  28The  store  code  and  deposit  
code  KEY  cannot  be  blank .  29The  electronic  invoice  function  has  not  been  enabled.  
30Whether  to  print  paper  invoices  has  not  been  
entered  or  the  format  is  incorrect.  31Whether  to  donate  invoices  has  
not  been  entered  or  the  format  is  incorrect.  32You  
can  only  select  one  of  donation  invoices,  unified  (company  account)  invoices,  
printed  paper  invoices  or  common  carriers.  33Please  enter  donation,  
paper  printing  invoices  or  use  invoice  carriers.  34Please  enter  the  carrier  type.  35Incorrect  data.  "Carrier  barcode"  is  required.  36The  
carrier  barcode  format  is  incorrect.  37Incorrect  data.  "Carrier  barcode"  is  required.
Machine Translated by Google

54  
38  The  format  of  the  carrier  barcode  
is  incorrect.  39  The  data  is  incorrect,  "Product  Name"  
is  required.  40  The  data  is  incorrect,  "Payer's  Name"  is  
required.  41  The  data  is  incorrect,  "Payer's  Postal  Code"  exceeds  the  length  limit  of  
10  characters.  42  The  data  is  incorrect,  "Payer's  Postal  Code"  
is  required.  43  The  data  is  incorrect,  "Payer's  Address"  
is  required.  44  The  data  is  incorrect,  invalid  payee's  mobile  
phone  number.  45  The  data  is  incorrect,  "Payer's  Mobile  Phone  
Number"  is  required.  46  The  data  is  incorrect,  invalid  payee's  
Email.  47  The  data  is  incorrect,  "Payer's  Email"  is  required.  48  
The  data  is  incorrect,  buyer's  unified  number  is  
incorrect.  49  The  data  is  incorrect,  "Invoice  Header"  is  required.
Machine Translated by Google

POST /api/Collect  
number  
Estimated  grant  amount
string  
string  
number  Amount  of  payment
Estimated  payment  amount
Authorization:  Bearer  [token…]  
cust_id  
cust_order_no  
grant_amount  
Status,  OK  means  successful  processing,  ERROR  means  error
flow)
Codename
Type
ÿTransmit  data:
Customer  order  number
Estimated  payment  request  date
5.  Order  Inquiry
Fields
string  
Fields
There  are  only  two  message  states:  OK  and  ERROR
Authorized  Bank  (E.Sun  Bank/China  Trust/Uni-President  Financial
number  
period_type  
request_date  
Transaction  Code
30  Y
Customer  order  number
string  
Exception  message,  with  null  value  if  no  exception
Content-Type:  application/json  
cust_order_no  
order_amount  
expire_date  
acquirer_type  
string  
string  
(yyyy-MM-dd )  
(The  system  pre-calculates  the  actual  amount  of  funding
string  20  Y  
status  
Abnormal  situation  occurs  during  processing
Authorized  products  (see  Appendix  2)
(yyyy-MM-dd)  
(Fixed  into  CocsOrderQuery)
Response  Status
ÿReply  information
msg  
Contract  customers  query  card  payment  information  through  API.
cmd  
string  
string  
Payment  due  date  (yyyy-MM-dd  HH:mm:ss)
Expected  funding  date
Type  length  required  description
string  20  Y  string  
illustrate
request  _amount  
grant_date  
55  
Machine Translated by Google

56  
“cust_order_no”:  “**************”,  
Carrier  barcode
string  
“cust_id”:  “***********”,  
Order  creation  time
print_invoice  
Invoice  Number
ÿSample  
Order  process  status  change  date
Print  paper  invoice
string  
Random  code
{  
Subject  to  the  bank)
string  
{  
1:  Member  Vehicle
Love  Code
string  
string  
string  
string  
success:
}  
First  6  and  last  4  digits  of  credit  card  number
1:  Print
0:  Do  not  print
3:  Natural  person  certificate
0:  Do  not  donate
(yyyy-MM-dd  HH:mm:ss)  
string  
donate_invoice  
“cmd”:  “CocsOrderQuery”,  
process_code  
process_code_update_time  string  
card_no  string  string  auth_code  Electronic  invoice  
related  
information  
(please  ignore  if  the  invoice  function  is  not  enabled)
vehicle_type  
Invoice  Date
Send  
“status”:  “OK”  
Authorization  Code
1.  Donate
Vehicle  Type
2:  Mobile  phone  barcode
create_time  
number  Order  process  status  code  (refer  to  Attachment  1)
vehicle_barcode  
love_code  
invoice_no  
invoice_date  
random_number  
“cust_order_no”:  “**************”  
(yyyy-MM-dd  HH:mm:ss )  
string  
Do  you  want  to  donate  the  invoice?
Response  
Machine Translated by Google

57  
}  
ÿ  Abnormal  message  list
“status”:  “ERROR”,  “msg”:  
“Failed  to  obtain  card  swipe  information”
fail:
{  
}  
“order_amount”:  50,  
“expire_date”:  “2017-08-25  13:31:00”,  “acquirer_type ”:  
“ÿÿÿÿ”,  “period_type”:  “ÿÿÿÿ”,  “request_date”:  
“2017-08-27”,  “request_amount”:  50,  
“grant_date”:”2017-08-29”,  “grant_amount”:  
0,  “process_code”:  15,  
“process_code_update_time”:  
“2017-08-25  10:31:55”,  
“create_time”:  “2017-08-25  
10:30:44”,  “card_no”:  “552199******1864”,  “print_invoice”:  “0”,  “vehicle_type”:  
“”,  “vehicle_barcode”:  “”,  “donate_invoice”:  “1”,  
“love_code”:  “919”,  “invoice_no”:  “”,  “invoice_date”:  
“”,  “random_number”:  “”,  
“auth_code”:  “684349”  
1  The  cmd  data  is  incorrect.  2  
The  contract  customer  data  is  incorrect.  
3  The  contract  customer  data  does  not  
exist.  4  The  user  password  cannot  exceed  40  
characters.  5  cust_id  (XXXX)  and  token  (XXXX)  do  not  match  6  
The  user  login  failed,  the  user  has  been  disabled.  7  The  
parameter  +  cust_order_no  is  required
Project  Project  Name
Machine Translated by Google

58  
8Cannot  find  the  card  information  
9Failed  to  obtain  the  card  information
Machine Translated by Google

POST /api/Collect  
Fields
Order  list,  presented  as  an  Array
“cust_id”:  “CV0100000008”,  
Authorization:  Bearer  [token…]  
string  20  Y  string  
Response  Status
ÿSample  
ÿTransmit  data:
(yyyy-MM-dd  HH:mm:ss)  
Codename
Status,  OK  means  successful  processing,  ERROR  means  error
{  
6.  Order  date  range  query
Fields
20  Y
Number  of  response  orders
Array  
“order_end_date”:  “2021/05/20  00:00:00”,  
Transaction  Code
(yyyy-MM-dd  HH:mm:ss)  
cust_id  
order_start_date  
ÿReply  information
status  
Content-Type:  application/json  
Abnormal  situation  occurs  during  processing
string  
string  
“cmd”:  “CocsOrderListQuery”,  
string  20  Y  
string  20  Y  
There  are  only  two  message  states:  OK  and  ERROR
Send  
(Fixed  into  CocsOrderListQuery)
Type  Description
Order  date  to  date
order_end_date  
Contract  customers  query  card  payment  information  through  API.
cmd  
(Contents  are  the  same  as  the  order  query  column)
order_list  
“order_start_date”:  “2021/05/19  00:00:00”,  
Type  length  required  description
Order  date
msg  
59  
Machine Translated by Google

60  
{  
"msg":  "  Count:2",  "status":  
"OK",  "order_list":  [ {  
Response  
Success:
}  
},  
{  
"msg":  "",  
"status":  "OK",  
"cust_order_no":  "****************",  "order_amount":  
235,  
"msg":  "",  
"status":  "OK",  
"cust_order_no":  "****************",  "order_amount":  
154,  "expire_date":  "2021-05-19  
16:27:00",  "acquirer_type":  "ÿÿÿÿ",  "period_type":  "",  
"request_date":  "",  "request_amount":  null,  
"grant_date":  "",  
"grant_amount":  null,  
"process_code":  4,  
"process_code_update_time":  
"2021-05-19  17:45:38",  
"create_time":  "2021-05-19  
16:17:27",  "card_no":  null,  "print_invoice":  "0",  "vehicle_type":  "1",  "vehicle_barcode":  
"",  "donate_invoice":  "0",  "love_code":  "",  "invoice_no":  "",  
"invoice_date":  "",  
"random_number":  "",  
"auth_code":  ""  
Machine Translated by Google

61  
"msg":  "Count:0",  "status":  
"OK",  "order_list":  []  
}  
No  data:  {
]  
}  
}  
"expire_date":  "2021-05-19  16:22:00",  "acquirer_type":  
"ÿÿÿÿ",  "period_type":  "",  "request_date":  "",  
"request_amount":  null,  
"grant_date":  "",  
"grant_amount":  null,  
"process_code":  4,  
"process_code_update_time":  
"2021-05-19  17:44:40",  
"create_time":  "2021-05-19  16:12:23",  "card_no":  "552199******1864",  
"print_invoice":  "0",  "vehicle_type":  "1",  "vehicle_barcode":  
"",  "donate_invoice":  "0",  "love_code":  "",  
"invoice_no":  "",  "invoice_date":  
"",  "random_number":  "",  
"auth_code":  "684349"  
Project  Project  Name
1  The  cmd  data  is  incorrect.  2  
The  contract  customer  data  is  incorrect.  
3  The  contract  customer  data  does  not  
exist.  4  The  user  password  cannot  exceed  40  characters.
ÿ  Abnormal  message  list
Machine Translated by Google

62  
5  cust_id(XXXX)  and  token(XXXX)  do  not  match  6  
User  login  failed,  user  has  been  deactivated.  7  
Parameters  +  cust_order_no,  order_start_date,  order_end_date  are  required  8  
Cannot  find  this  card  information  
9  Failed  to  obtain  card  information
Machine Translated by Google

POST /api/Collect  
string  
number  
string  
illustrate
string  20  Y  
payuni:  unified  financial  flow
Fields
Banks  only  support  full  amount  cancellation  authorization.  If  you  want  to  cancel  partial  amount  authorization,  please  use  the  partial  payment  request  function  or  wait  for  payment  request.
Fields
Designated  acquiring  bank
New  time,  in  the  format  of  yyyy-MM-dd
Type
7.  Order  Cancellation  Authorization
Type  length  required  description
Customer  order  number
string  20  Y  string  
chinatrust:  China  Trust
ÿReply  information
Abnormal  situation  occurs  during  processing
There  are  only  two  message  states:  OK  and  ERROR
Content-Type:  application/json  
cust_id  
cust_order_no  
cmd  
30  Y
string  20  Y  
Then  partially  cancel  the  transaction.
HH:mm:ss,  for  example:  2017-07-18
10  And
string  
Unable  to  cancel  authorization,  please  use  the  cancel  transaction  function  instead.
(Fixed  into  CocsOrderCancel)
Order  amount
The  transmission  time  must  be  the  longest  during  the  transmission
status  
cust_order_no  
Authorization:  Bearer  [token…]  
esun:  E.Sun  Bank
order_amount  
acquirer_type  
Original  order  number
The  contract  customer  cancels  the  order  authorization  through  API;  this  function  must  be  executed  before  the  order  has  been  paid.  If  the  order  has  been  paid,
Transaction  Code
send_time  
07:17:25  
Response  Status
ÿTransmit  data:
Codename
AND
Status,  OK  means  successful  processing,  ERROR  means  error
63  
Machine Translated by Google

64  
“status”:  “ERROR”,  “msg”:  
“Order  cancellation  authorization  failed”
{  
“cmd”:  “CocsOrderCancel”,  “cust_id”:  
“***********”,  “cust_order_no”:  
“**************”,  “order_amount”:  50,  “acquirer_type”:  
“esun”,  “send_time”:  
“2017-07-18  03:07:23”  
}  
ÿ  Abnormal  message  list
}  
Response  
Success:
Project  Project  Name
{  
1  Incorrect  cmd  data.  2  Required  
fields  are  missing.  3  Incorrect  
order_amount  format.  4  Incorrect  send_time  
format.  5  Send_time  exception.  6  Customer  
data  does  not  exist.  7  Acquiring  
bank  settings  are  incorrect.  8  You  
have  not  applied  for  E.Sun  Bank  as  
an  acquiring  bank.  9  You  have  not  applied  for  China  Trust  
as  an  acquiring  bank.  10  The  current  order  procedure  does  
not  allow  cancellation  of  authorization.  11  Incorrect  order  
amount.  12  Cancellation  of  
authorization  failed,  no  such  order.
“status”:  “OK”,  
“cust_order_no”:  “**************”  
ÿSample  
fail:
}  
Send  
{  
Machine Translated by Google

65  
13  cust_order_no  does  not  exist.  14  
Cancellation  of  authorization  failed,  communication  
abnormality.  15  Cancellation  of  authorization  failed.  Bank  response:  XXXXX
Machine Translated by Google

POST /api/Collect  
cmd  
string  
ÿReply  information
1.  E.Sun  Bank:  Request  payment  every  30  minutes.  Within  2-3  hours  after  the  payment  is  requested,  the  bank  will  reply  with  payment  settlement  information.
string  20  Y  string  
The  transmission  time  must  be  the  longest  during  the  transmission
The  amount  will  be  refunded  to  the  consumer.
Therefore,  this  system  sends  a  payment  request  file  to  the  acquiring  bank  every  30  minutes  between  16:55  and  19:55  every  day.
cust_id  
cust_order_no  
order_amount  
cr_amount  
send_time  
number  
HH:mm:ss,  for  example:  2017-07-18
8.  Specify  the  payment  amount  (full  or  partial  payment)
Therefore,  this  system  sends  a  payment  request  file  to  the  acquiring  bank  every  30  minutes  between  15:55  and  19:55  every  day.
Type  length  required  description
Order  amount
Specify  the  amount  to  be  paid
2.  China  Trust:  The  daily  payment  request  deadline  is  20:00,  and  the  payment  request  settlement  will  be  replied  at  05:00  the  next  morning.
Content-Type:  application/json  
ÿCredit  card  installment  and  UnionPay  card  can  only  be  refunded  in  full
Fields
Codename
The  system  will  process  payment  requests  based  on  the  settlement  cycle  and  acquiring  bank  at  the  time  of  contract  signing.  The  daily  payment  request  times  are  as  follows:
number  
AND
07:17:25  
To  consumers.  For  example,  if  the  order  amount  is  2,000  yuan  and  the  API  specifies  a  payment  amount  of  1,700  yuan,  then  the  remaining  300  yuan
Therefore,  orders  that  exceed  the  payment  request  time  will  be  requested  the  next  day.
(Fixed  into  CocsCashRequest)
30  Y
New  time,  in  the  format  of  yyyy-MM-dd
As  a  result,  all  orders  that  exceed  the  payment  request  time  will  be  requested  the  next  day.
Customer  order  number
Authorization:  Bearer  [token…]  
Transaction  Code
The  contracted  customer  specifies  the  amount  to  be  paid  through  the  API,  and  the  system  will  pay  part  of  the  amount,  and  the  amount  not  paid  will  be  refunded
3.  Unified  cash  flow:  The  daily  payment  deadline  is  20:00,  and  the  payment  will  be  completed  after  12:00  noon  the  next  day.
AND
10  And
fruit.
ÿTransmit  data:
string  20  Y  
66  
Machine Translated by Google

67  
Fields
“cmd”:  “CocsCashRequest”,  “cust_id”:  
“***********”,  “cust_order_no”:  
“**************”,  “order_amount”:  50,  “cr_amount”:  
50,  “send_time”:  “2017-07-18  
03:07:23”  
{  
“status”:  “ERROR”,  “msg”:  
“Failed  to  specify  the  amount  to  request  payment”
status  
}  
}  
ÿ  Abnormal  message  list
Response  
Success:
Project  Project  Name
illustrate
1  The  cmd  data  is  incorrect.  2  A  
required  field  is  missing.  3  The  
order_amount  format  is  incorrect.  4  The  
cr_amount  format  is  incorrect.  5  The  
send_time  format  is  incorrect.
The  response  
status  has  only  two  message  statuses:  OK  and  ERROR.  
OK  means  the  processing  is  successful,  and  ERROR  means  
an  abnormal  situation  occurred  
during  the  processing.
{  
string  
“status”:  “OK”,  
“cust_order_no”:  “**************”  
cust_order_no  
}  
Type
ÿSample  
Send  
fail:
string  
{  
Machine Translated by Google

68  
6  send_time  is  abnormal.  7  The  
customer  data  does  not  exist.  8  The  
specified  order  does  not  allow  the  specified  amount  of  payment.  
9  The  current  order  procedure  does  not  allow  the  specified  amount  of  
payment.  10  The  order  amount  is  
incorrect.  11  The  specified  amount  of  payment  cannot  be  greater  
than  the  order  amount.  12  The  specified  amount  of  payment  service  is  
not  enabled  for  this  account.  13  The  specified  amount  of  payment  operation  failed.  There  is  no  such  order,  or  the  payment  is  not  
made  through  E.SUN  or  CTBC.  14  cust_order_no  does  not  exist.
Machine Translated by Google

POST /api/Collect  
cust_order_no  
Status,  OK  means  successful  processing,  ERROR  means  error
cmd  
AND
Fields
Content-Type:  application/json  
equal  to  or  equal  to  the  order/transaction  amount)
Customer  order  number
30  Y
string  
Order  amount
HH:mm:ss,  for  example:  2017-07-18
number  
07:17:25  
Response  Status
9.  Order  cancellation  transaction  (full  or  partial  return  refund)
(Fixed  into  CocsOrderRefund)
payuni:  unified  financial  flow
string  20  Y  string  
send_time  
illustrate
Type
string  
Type  length  required  description
esun:  E.Sun  Bank
Cancel  transaction  amount  (must  be  entered,  small
Fields
cust_id  
cust_order_no  
ÿTransmit  data:
AND
acquirer_type  
ÿReply  information
Abnormal  situation  occurs  during  processing
ÿCredit  card  installment  and  UnionPay  card  can  only  be  refunded  in  full
Customer  order  number
New  time,  in  the  format  of  yyyy-MM-dd
number  
10  And
There  are  only  two  message  states:  OK  and  ERROR
Transaction  Code
order_amount  
refund_amount  
chinatrust:  China  Trust
The  transmission  time  must  be  the  longest  during  the  transmission
Contract  customers  cancel  order  transactions  through  API;  this  function  can  only  be  used  after  the  order  has  been  paid.
Codename
string  
20  Y
status  
Authorization:  Bearer  [token…]  
Designated  acquiring  bank
string  20  Y  
string  
69  
Machine Translated by Google

70  
{  
“status”:  “ERROR”,  “msg”:  
“Order  cancellation  transaction  failed”
“cmd”:  “CocsOrderRefund”,  “cust_id”:  
“***********”,  “cust_password”:  
“1q2w”,  “cust_order_no”:  
“**************”,  “order_amount”:  50,  
“refund_amount”:  50,  
“acquirer_type”:  “esun”,  
“send_time”:  “2017-07-18  03:07:23”  
}  
}  
ÿ  Abnormal  message  list
Cancelled  transaction  (return,  refund)  amount
Response  
Success:
{  
Project  Project  Name
string  
“status”:  “OK”,  
“cust_order_no”:  “**************”,  “refund_amount”:  
50  
1  The  cmd  data  is  incorrect.  2  
Required  fields  are  missing.  3  
The  order_amount  format  is  incorrect.  4  The  
refund_amount  format  is  incorrect.  5  The  
send_time  format  is  incorrect.  6  The  
send_time  is  abnormal.  7  The  
customer  data  does  not  exist.
refund_amount  
}  
ÿSample  
fail:
{  
Send  
Machine Translated by Google

71  
8Acquiring  bank  settings  are  incorrect.  
9You  have  not  applied  for  E.Sun  Bank  as  an  acquiring  bank.  10You  
have  not  applied  for  China  Trust  as  an  acquiring  bank.  11You  have  not  
applied  for  China  UnionPay  as  an  acquiring  bank.  12China  UnionPay  
acquiring  bank  information  is  missing.  13Special  store  
code  or  deposit  key  is  missing.  14The  current  order  
procedure  does  not  allow  transaction  cancellation.  15Order  amount  is  
incorrect.  16The  cancellation  transaction  
amount  cannot  be  greater  than  the  order  amount.  17The  cancellation  
transaction  amount  must  be  greater  than  0  yuan.  18The  specified  
cancellation  transaction  amount  cannot  be  greater  than  the  payment  amount.  
19The  cumulative  cancellation  transaction  amount  cannot  be  greater  than  the  
order  amount.  20The  cumulative  cancellation  transaction  amount  cannot  be  greater  than  
the  specified  payment  amount.  21Partial  
cancellation  of  transactions  is  not  allowed.  22The  cancellation  transaction  operation  failed.  There  is  no  such  order,  or  the  payment  
is  not  made  by  E.Sun  or  China  Trust.  
23cust_order_no  does  not  exist.  24The  cancellation  transaction  operation  failed .  There  is  no  such  order,  or  
the  payment  is  not  made  by  E.Sun.  25The  cancellation  transaction  operation  failed.  There  is  no  such  order,  or  the  payment  is  not  made  by  China  Trust.
Machine Translated by Google

POST /api/Collect  
The  first  eight  digits  are  the  year,  month  and  day,  and  the  last  six  digits  are
order_amount  
order_detail  
send_time  
Successful  reply  URL  will  bring  this  URL
Content-Type:  application/json  
string  20  Y  
Order  Details
string  
string  
Fields
When  empty,  the  system  will  automatically  generate
New  time,  in  the  format  of  yyyy-MM-dd
Order  authorization  successfully  specifies  the  return  URL
string  250  Y  
10.  Qike  added  new  card  order  -  E.SUN  UnionPay  card
(bring  in  CocsUnionpayAppend)
Identification  number,  which  is  the  same  as
Order/Transaction  Amount
10  And
success_url  
The  network  specified  in  the  service  settings  for  successful  authorization
Type  length  required  description
string  20  Y  string  
cmd  
The  number  is  YYYYMMDD######,
1  Starts  to  accumulate,  for  example:
ÿTransmit  data:
HH:mm:ss,  for  example:  2017-07-18
AND
If  this  parameter  has  an  incoming  URL,  authorization  is  successful.
The  function  can  be  used  only  after  it  is  turned  off.
Contract  order  number  (customer-defined)
one).  No  entry  is  required.
The  transmission  time  must  be  the  longest  during  the  transmission
07:17:25  
Transaction  Code
**************  
30  Y
The  contract  customer  code  must  be
This  function  is  only  for  "E.SUN  UnionPay  Card".  Contract  customers  add  E.SUN  UnionPay  card  orders  through  API.
Codename
number  
150  Y
If  there  is  no  input,  the  cash  flow  will  be  based  on  the  customer's
Authorization:  Bearer  [token…]  
cust_id  
cust_order_no  
The  order  serial  number  within  the  date,  starting  from
URL  return
72  
Machine Translated by Google

73  
Payee  Email
buyer_bill_no  
bill
Vehicle  type  (required  if  b2c  is  1)
0:  Do  not  donate
string  240  Y  Electronic
EMAIL  Notification)
apn_url
Whether  to  print  paper  invoice  (b2c  is  1
bill
bill
payer_address  
Product  Name  (required  if  b2c  is  1)
2:  Mobile  phone  barcode
Payee's  name  (required  if  b2c  is  1)
string  
Is  an  electronic  invoice  issued  for  this  order?
string  
vehicle_barcode  
bill
payer_mobile  
payer_email  
Y  Electronics
1:  Print
Whether  to  donate  invoice  (required  if  b2c  is  1)
No  information,  please  log  in  to  the  Black  Cat  PAY  platform
bill
(b2c  electronic  invoice  and  invoice  winning
string  
product_name  
electronic
50  Y  Electronic
string  
1:  Open
1:  Member  Vehicle
The  default  is  Genesis  Foundation  919)
string  20  
bill
string  
print_invoice  
string  
string  240  Y  Electronic
APN  specifies  the  delivery  URL  (if  this  field
bill
string  
1  
fill)
string  
8  
Set  the  return  URL)
bill
Carrier  barcode
bill
payer_postcode  
1  
bill
Love  code  (select  Donate.  If  the  love  code  is  not  entered
love_code  
electronic
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
bill
0:  Do  not  print
bill
(B2C  invoice  winning  SMS  notification)
60  Y  Electronic
vehicle_type  
string  
Payee's  postal  code  (required  if  b2c  is  1)
electronic
1  
electronic
Payee  address  (required  if  b2c  is  1)
string  
b2c  
Y  Electronics
1.  Donate
bill
Buyer's  Uniform  Number
250  
Required)
Y  Electronics
10  Y  Electronic
Payee's  mobile  phone
payer_name  
string  
donate_invoice  
3:  Natural  person  certificate
0:  Not  open
1  
20  
20  Y  Electronic
Machine Translated by Google

74  
“cmd”:  “CocsUnionpayAppend”,  
{  
}  
ÿReply  information
Abnormal  situation  occurs  during  processing
“send_time”:  “2017-07-18  01:07:23”  
fail:
buyer_invoice_title  string  
There  are  only  two  message  states:  OK  and  ERROR
Send  
Response  
“status”:  “ERROR”,  
Invoice  Title
string  
url  
“order_detail”:  “Test  order  notes”,
}  
Project  Project  Name
status  
Generate  the  online  credit  card  payment  URL  corresponding  to  the  order
Status,  OK  means  successful  processing,  ERROR  means  error
{  
“cust_order_no”:  “**************”,  
161  
success:
“cust_order_no”:  “**************”,  
“msg”:  “Adding  a  new  card  order  failed”
bill
Response  Status
ÿSample  
}  
{  
Type
“order_amount”:  50,  
cust_order_no  
string  
string  
electronic
illustrate
“status”:  “OK”,  
“url”:  “https://goo.gl/K616G6”  
ÿ  Abnormal  message  list
Fields
Customer  order  number
“cust_id”:  “***********”,  
Machine Translated by Google

75  
9  
1  The  cmd  data  is  incorrect.  2  The  
contract  customer  data  is  incorrect.  3  The  
contract  customer  data  does  not  exist.  4  The  
user  password  cannot  exceed  40  digits.  5  cust_id  (XXXX)  
and  token  (XXXX)  do  not  match  6  User  login  failed,  the  user  has  been  
deactivated.  7  Failed  to  obtain  the  financial  service  information  8  The  
required  fields  are  missing.  The  order_detail  
format  is  incorrect,  and  the  content  
cannot  contain  Html  Tag.  10  The  order_amount  format  is  incorrect.  11  The  send_time  
format  is  incorrect.  12  The  send_time  is  abnormal.  13  
The  contract  customer  data  does  not  exist.  14  
You  have  not  yet  applied  for  E.Sun  
Bank  as  the  acquiring  bank.  15  The  
contract  customer  has  not  set  a  response  URL.  16  cust_order_no  
must  be  an  alphanumeric  number  or  minus  sign  
with  more  than  three  digits.  17  The  order  has  expired.  18  The  data  is  incorrect.  You  have  already  
uploaded  this  "contract  order  
number":  XXXXX,  and  cannot  upload  it  again.  19  The  financial  service  has  been  deactivated  20  The  data  is  incorrect,  the  "amount  paid  on  
behalf  of"  must  be  less  than  XXXXX  
21Incorrect  data,  "Amount  to  be  paid  on  behalf  of"  must  be  greater  than  XXXXX  
22Bank  handling  fee  rate  has  not  been  set  23Special  store  code  and  deposit  code  
KEY  cannot  be  blank  24Electronic  invoice  function  
has  not  been  enabled.  25Whether  to  print  paper  invoices  is  not  
entered  or  the  format  is  incorrect.  26Whether  to  
donate  invoices  is  not  entered  or  the  format  is  incorrect.  27Only  one  of  
donation  invoices,  unified  (company  account)  invoices,  printed  paper  
invoices  or  common  carriers  can  be  selected.  28Please  enter  donation,  paper  printing  invoice  or  use  invoice  carrier.  29Please  enter  
the  carrier  type.  30Incorrect  data,  "Carrier  barcode"  is  required.  31Carrier  barcode  
format  is  incorrect.  32Incorrect  data,  
"Carrier  barcode"  is  required.  33Carrier  barcode  format  is  
incorrect.  34Incorrect  data,  "Product  name"  
is  required.  35Incorrect  data,  "Payer  name"  is  required.  
36Incorrect  data,  "Payer  postal  code"  
exceeds  the  length  limit  of  10  characters.
Machine Translated by Google

76  
37Incorrect  data,  "Payer's  postal  code"  is  required.  
38Incorrect  data,  "Payer's  address"  is  
required.  39Incorrect  data,  invalid  payee's  mobile  
number.  40Incorrect  data,  "Payer's  mobile  number"  
is  required.  41Incorrect  data,  invalid  payee's  email  
address.  42Incorrect  data,  "Payer's  email"  is  
required.  43Incorrect  data,  incorrect  buyer's  
unified  number.  44Incorrect  data,  "Invoice  header"  is  required.
Machine Translated by Google

POST /api/Collect  
ÿSample  
There  are  only  two  message  states:  OK  and  ERROR
number  
Content-Type:  application/json  
(Must  enter,  less  than  or  equal  to  order/
10  And
cust_order_no  
refund_amount  
New  time,  in  the  format  of  yyyy-MM-dd
30  Y
Order  amount
string  
Type
11.  Order  cancellation  (return,  refund)  -  E.SUN  UnionPay  Card
(Bring  in  CocsUnionpayRefund)
illustrate
status  
string  20  Y  
The  transmission  time  must  be  the  longest  during  the  transmission
Cancelled  transaction  (return,  refund)  amount
Abnormal  situation  occurs  during  processing
Fields
Type  length  required  description
AND
Cancelled  transaction  (return,  refund)  amount
order_amount  
refund_amount  
ÿTransmit  data:
send_time  
ÿReply  information
Response  Status
Send  
Customer  order  number
*Note:  UnionPay  card  transactions  can  only  be  canceled  after  payment  has  been  completed.
string  20  Y  string  
HH:mm:ss,  for  example:  2017-07-18
string  
string  
string  
AND
Transaction  Code
cust_id  
cust_order_no  
cmd  
Codename
The  contract  customer  cancels  the  E.Sun  UnionPay  card  order  transaction  through  the  API.
Fields
07:17:25  
Authorization:  Bearer  [token…]  
Transaction  Amount)
number  
Status,  OK  means  successful  processing,  ERROR  means  error
Customer  order  number
77  
Machine Translated by Google

“status”:  “OK”,  
“cust_order_no”:  “**************”,  “refund_amount”:  
50  
}  
{  
{  
}  
Response  
Success:
“cmd”:  “CocsUnionpayRefund”,  “cust_id”:  
“***********”,  “cust_password”:  
“1q2w”,  “cust_order_no”:  
“**************”,  “order_amount”:  50,  “refund_amount”:  
50,  “send_time”:  “2017-07-18  
03:07:23”  
{  
“status”:  “ERROR”,  “msg”:  
“Order  cancellation  transaction  failed”
}  
1  The  cmd  data  is  incorrect.  2  The  
required  field  is  missing.  3  The  
order_amount  format  is  incorrect.  4  The  
refund_amount  format  is  incorrect.  5  The  send_time  
format  is  incorrect.  6  The  send_time  is  
abnormal.  7  The  customer  data  
does  not  exist.  8  You  have  not  
applied  for  China  UnionPay  as  the  acquiring  bank.  9  The  
China  UnionPay  acquiring  bank  information  is  
missing.  10  The  special  store  code  or  deposit  key  
is  missing.  11  The  current  order  process  does  not  allow  the  
transaction  to  be  cancelled.  12  The  order  amount  is  incorrect.
ÿ  Abnormal  message  list
fail:
Project  Project  Name
78  
Machine Translated by Google

13The  amount  of  the  transaction  to  be  cancelled  cannot  be  greater  than  the  
amount  of  the  order.  14The  amount  of  the  transaction  to  be  cancelled  
must  be  greater  than  0  yuan.  15The  amount  of  the  transaction  to  be  cancelled  cannot  be  
greater  than  the  amount  of  the  payment  requested.  16The  cumulative  amount  of  the  
transaction  to  be  cancelled  cannot  be  greater  than  the  amount  of  the  order.  17The  cumulative  amount  
of  the  transaction  to  be  cancelled  cannot  be  greater  than  
the  amount  of  the  payment  requested.  18Partial  cancellation  of  transactions  is  not  allowed.  19The  transaction  cancellation  operation  failed .  There  is  
no  such  order,  or  the  payment  is  not  made  using  E.SUN  
or  China  Trust.  20  cust_order_no  does  not  exist.  21The  transaction  cancellation  operation  failed.  There  is  no  such  order,  or  
the  payment  is  not  made  using  UnionPay.  22The  transaction  cancellation  
operation  failed .  Communication  abnormality.  23The  initial  verification  of  the  transaction  cancellation  operation  failed.  Bank  response:  XXXXX
79  
Machine Translated by Google

POST /api/Period  
10  And
Between  100~100,000)
To  cooperate  with  the  government  in  preventing  fraud,  product  details  and  payee  contact  information  are  required.
Codename  of  Qike  Jinliu
period_amount  
cust_id  
cust_order_no  
Amount  per  issue
The  renewal  payment  is  authorized  in  one  lump  sum  for  each  period,  so  the  same  lump  sum  payment  method  can  be  used  to  query  and  cancel  the  authorization.
Type  length  required  description
25  
12.  Renewal  of  payment  -  Qike  adds  new  credit  card  order
ÿTransmit  data:
10:17:25  
string  
string  
string  
string  20  Y  
The  renewal  order  number  will  be  different  according  to  the  period.
HH:mm:ss,  for  example:  2024-12-12
(Please  select  either  mobile  or  landline.  Both  are  available.
payer_mobile  
Only  English  and  numbers  can  be  entered.  If  not  entered
E-MAIL  to  avoid  missing  relevant  information.
The  transmission  time  must  be  the  longest  during  the  transmission
Fields
The  amount  limit  is  based  on  the  contract  specifications  (preset
rights,  request  payment,  cancel  transaction  and  other  operations.
number  
order_detail  
send_time  
50  Y
Authorization:  Bearer  [token…]  
The  order  number  is  based  on  the  suffix,  so  only  letters  and  numbers  can  be  used  in  the  order  number.  Do  not  use  symbols.
And  so  on.
string  20  Y  string  
Payee's  name
New  time,  in  the  format  of  yyyy-MM-dd
Automatically  numbered  by  the  system.
Add  _1,  _2,  _3  after  the  order  number...
Contract  customers  add  renewal  payment  card  orders  through  the  API.  Due  to  the  renewal  payment  order  number,  the  system  will  automatically
Content-Type:  application/json  
500  Y
Payee's  mobile  phone  number
AND
After  each  deduction  is  completed,  PAYUNi  will  send  a  system  notification  letter  to  the  consumer,  so  please  fill  in  the  correct  and  valid  payee
Contract  order  number  (renewal  receipt  number)
Product  Details
payer_name  
80  
Machine Translated by Google

81  
string  
string  
Down  payment  amount
If  the  first  deduction  is  set  as  date,  it  is  required.
(Please  select  either  mobile  or  landline)
first_type  
month  Please  enter  a  number  from  1  to  31,  representing
Consumers  are  redirected  to  this  URL  and  brought  into  the  homepage
AND
string  
First  payment  setting
Deduction  period  (up  to  three  years)
Deduction  cycle
First  deduction  date
number  
Payee's  local  phone  number
On  today's  date
Please  choose  month  between  2  and  36
Monday  to  Sunday
string  20  Y  
first_amount  
If  it  is  filled  in  and  authorized  successfully,  it  will  be  deleted.
240  Y
first_date  
The  amount  per  period  is  equal  to
Payment  Date
Payee  Email
(If  not  filled  in,  the  default  will  be  taken  into  the  build)
When  entering,  mobile  phone  numbers  are  given  priority)
number  
period_date  
return_url  
AND
pay_tel
URL  
number  
Between  100~100,000),  if  not  filled  in
period_type  
Authorize  to  bind  credit  card,  wait  for  the  specified  time
For  example:  02-2222-2222#222
The  date  is  based  on  the  last  day  of  the  month.
date:  Deduction  date  specified
period_times  
week  Please  enter  the  number  1~7,  representing  the  day  of  the  week
The  first  order  authorization  is  successfully  sent  back
month:  every  month
string  250  
AND
The  amount  limit  is  based  on  the  contract  specifications  (preset
payer_email  
If  filled  in,  consumers  must  first  1
Brackets  are  not  allowed,  -#  is  allowed
string  
From  the  1st  to  the  31st  of  each  month,  if  there  is  no
Deduction  date
Build:  Deduction  on  the  day  the  order  is  established
The  system  will  automatically  deduct  the  payment  when  the  date  is  reached.
week:  every  week
The  format  is  yyyy-MM-dd,  please  fill  in  the
Please  choose  a  week  between  2  and  156
Machine Translated by Google

82  
"period_date":  31,  
"payer_email":  "<EMAIL>",  
Send  
Status,  OK  means  successful  processing,  ERROR  means  error
The  result  page  displays  a  Return  to  Store  button.  Click
250  
string  
"period_type":  "month",  
"payer_name":  "Wang  Daming",
The  first  issue  ends  with  _1)
Response  Status
Back  to  store  button  URL
string  
(For  subsequent  batch  order  operations)
Authorization  completion  report  parameter.
APN  specifies  the  delivery  URL  (if  this  field
string  
url  
more_trade_no
Type
(The  original  customer  order  number  plus  the  suffix  is  automatically  numbered,
"order_detail":  "Test  Renewal  Payment",
"cust_id":  "CV0100000008",  
Fields
back_url  
Exception  message,  with  null  value  if  no  exception
apn_url
There  are  only  two  message  states:  OK  and  ERROR
If  filled  in,  payment  will  be  made  via  PAYUNi
Generate  the  online  credit  card  payment  URL  corresponding  to  the  order
msg  
ÿSample  
"send_time":  "yyyy-MM-dd  HH:mm:ss",  
Set  the  return  URL)
Results  page.
illustrate
Store  Order  Number
string  
"payer_mobile":  "0900123123",  
Renewal  Order  Number
string  250  
string  
status  
No  information,  please  log  in  to  the  Black  Cat  PAY  platform
If  you  don't  bring  it,  it  will  show  PAYUNi  payment
cust_order_no  
string  
Click  to  return  to  the  specified  URL
ÿReply  information
Abnormal  situation  occurs  during  processing
{  
"period_amount":  1000,  
Machine Translated by Google

}  
fail:
"status":  "OK",  "msg":  
"",  
"cust_order_no":  "P241213061050753_1",  “mer_trade_no”:  
“P241213061050753”,  "url":  "https://test.4128888card.com.tw/
test/To8LZfA6"  
ÿ  Abnormal  message  list
Response  
Success:
{  
}  
"period_times":  3,  
"first_type":  "build",  "return_url":  
"https://test.gg.com.tw/api_receiver.php"  
“status”:  “ERROR”,  “msg”:  
“Each  period  amount  is  required”
}  
1Renewal  payment  -  Failed  to  obtain  payment  service  
information  2Contract  customer  
information  is  incorrect  3Contract  
customer  information  does  not  exist  4User  payment  code  login  failed,  user  
has  been  deactivated  
5Amount  per  period  is  required  6Mobile  phone  number  
and  landline  number,  please  
select  one  to  fill  in  7Payer  name  is  
required  8Payer  E  -MAIL  is  required  9Payer  E-MAIL  
format  is  incorrect  10Product  
description  is  required  
11Deduction  cycle  is  required  
12Deduction  date  is  required  
13Deduction  period  number  is  required  14Weekly  deduction:  Please  
enter  a  number  from  1  to  7  for  the  deduction  date  15Weekly  deduction:  Please  enter  a  number  from  2  to  104  for  the  deduction  period
{  
Project  Project  Name
83  
Machine Translated by Google

16Monthly  deduction:  Please  enter  a  number  from  1  to  31  for  the  deduction  
date17Monthly  deduction :  Please  enter  a  number  from  2  to  24  for  the  number  of  
deduction  periods18The  deduction  period  format  
is  incorrect19Please  enter  a  number  greater  than  10  for  the  first  
installment  amount20The  first  installment  amount  is  filled  in,  but  the  first  installment  deduction  
setting  parameters  are  incorrect21The  first  installment  deduction  is  set  to  date,  and  
the  first  installment  deduction  date  is  required22The  first  
installment  deduction  date  is  in  an  incorrect  format23The  first  installment  deduction  is  set  to  date,  and  the  first  
installment  deduction  date  must  be  greater  than  today's  date24The  order  number  for  the  
renewal  collection  can  only  use  English  and  numbers25The  order  number  for  the  renewal  collection  must  be  less  than  25
84  
Machine Translated by Google

string  
string  
string  
send_time  
ÿReply  information
Authorized  amount
Notification  time,  which  is  the  time  when  the  message  was  sent.
acquire_time  
cust_order_no  
order_amount  
*If  the  first  payment  of  the  renewal  is  not  authorized  on  the  same  day,  you  must  first  authorize  1  yuan  to  bind  your  credit  card,  so  the  authorized  amount  will  be  included
OK  if  authorization  is  successful
Authorization  Code
13.  Renewal  Collection-Report  on  First  Authorization  Completion
string  
MD5(hash_base+'$'+order_amount+'$'+  
send_time+'$'+ret+'$'+acquire_time  
+'$'+auth_code+'$'+card_no+'$'+  
notify_time+'$'+cust_order_no)  
Algorithm  obtained.
Verification  code,  the  verification  code  is  generated  by
(The  delivery  time  entered  in  the  new  order  by  the  customer).
For  example:  2017-09-14  10:36:38
string  
Amount  1  yuan)
right
Acquiring  transaction  time  (authorization  time),
Renewal  order  number  (first  order  ends  with  _1)
string  
string  
1  yuan,  not  an  actual  transaction  completed.
Last  four  digits  of  credit  card  number
The  format  is  yyyy-MM-dd  HH:mm:ss,
hash_base:  used  as  the  base  for  generating  the  chk  check  code
Response  Status
Then  you  will  stay  on  the  unified  payment  result  page.
For  example:  2017-09-14  10:31:25
string  
First  concatenate  each  field  with  '$'  and  then  use  MD5  to  perform
The  format  is  yyyy-MM-dd  HH:mm:ss,
Type
The  format  is  yyyy-MM-dd  HH:mm:ss,
Original  order  delivery  time
When  the  consumer  completes  the  first  authorization,  if  ReturnURL  is  filled  in,  it  will  redirect  to  the  URL  specified  by  the  customer  API.
illustrate
For  example:  2017-09-14  10:37:08
(Updated  with  every  transmission),
Fields
(If  the  payment  is  not  deducted  on  the  day  the  order  is  established,  you  will  receive
string  
auth_code  
card_no  
notify_time  
chk  
85  
Machine Translated by Google

ÿNote:  If  the  credit  
card  bound  to  the  order  of  1  yuan  has  been  authorized,  but  the  first  order  has  not  been  officially  deducted,  although  you  can  still  enter  the  
PAYUNi  renewal  payment  page  through  the  short  URL,  after  entering  the  card  number  and  submitting,  a  message  "Authorization  failed,  the  
same  store  order  number  already  exists"  will  appear.  Therefore,  if  this  message  appears,  it  means  that  it  has  been  authorized.
The  system  will  issue  a
ÿSample  
When  authorization  is  completed,  redirect  to  the  customer  website:  https://test.gg.com.tw/
api_receiver.php  Response  https://test.gg.com.tw/api_receiver.php?
ret=OK&cust_order_no=P241213061050753_1&order_amount=3063&send_time=2024-12-13  
14:10:49&acquire_time=2024-12-13  
14:12:02&auth_code=000000&card_no=0001&notify_time=2024-12-13  14:12:06&chk=88e7b6961a2cd12e1156f89e59b89abe
86  
Machine Translated by Google

POST /api/Period/MdfStatus  
Enable=restart
End=end
Suspend  the  entire  batch  status  modification  =  suspend
1.  The  original  order  is  suspended,  and  the  status  can  be  modified  to  [Enable]  or  [Terminate];
*You  must  complete  the  first  authorization  or  1  yuan  authorization  before  you  can  modify  
the  status.  *Automatic  deductions  will  be  executed  before  09:30  every  morning.  To  avoid  interfering  with  the  automatic  deduction  process,  single-period  orders  are  not  
allowed  to  modify  their  status  on  the  day  of  deduction.  If  you  need  to  modify  the  order  status,  please  complete  the  operation  one  day  before  the  deduction  date.
Available  states:
Provides  status  changes  for  renewal  collection  orders  that  have  not  yet  been  deducted.  The  status  of  the  entire  batch  of  orders  or  the  status  of  a  specific  order  can  be  
modified  according  to  needs.
14.  Renewal  of  payment  -  Status  modification  (activation/suspend/termination/reauthorization)
The  authorization  will  start  from  the  latest  period.  2.  
If  the  original  status  of  a  certain  issue  is  enabled,  you  can  modify  the  status  [Pause].  
3.  If  the  original  status  of  a  certain  issue  is  re-authorized,  you  can  only  modify  the  status  [Re-authorize].  
4.  If  a  certain  issue  is  overdue,  the  status  cannot  be  modified.
ÿTransmitted  data:  
Content-Type:  application/json  Authorization:  
Bearer  [token…]
Enable  =  restart  
Reauthorize  =  reauth  (If  
authorization  fails  due  to  card  expiration  or  insufficient  credit,  you  can  use  this  function  to  request  
authorization  again  after  the  problem  is  solved)  1.  The  
original  status  of  a  certain  issue  is  suspended,  and  the  status  can  be  modified  to  [Enable];  restart  after  suspension
The  authorization  will  start  from  the  latest  period.  2.  
The  original  order  is  activated,  and  the  status  can  be  modified  to  [pause]  or  [terminate]  
3.  The  original  order  is  terminated,  and  the  entire  order  cannot  be  activated  or  paused  again
Single  issue  status  modification  suspend  =  suspend
87  
Machine Translated by Google

88  
Reauthorize  =  reauth
Store  Order  Number
Fields
Modify  order  status
string  
illustrate
Send  
period_no  
string  
Response  Status
Abnormal  situation  occurs  during  processing
30  Y
more_trade_no
status  
Fields
ÿReply  information
If  successful,  this  field  will  be  left  blank.
string  
Store  Order  Number
Store  Order  Number
ÿSample  
Ex:  To  modify  the  6th  issue,  please  enter  6
string  
illustrate
Status,  OK  means  successful  processing,  ERROR  means  error
cust_id  string  20  Y  string  mer_trade_no  revise_trade_status  
string  
int  
Type  length  required  description
Abnormal  situation  occurs  during  processing
Fields
string  
Type
Codename  of  Qike  Jinliu
Modify  the  period,  if  not,  leave  this  column  blank
If  successful,  this  field  will  be  left  blank.
If  no,  this  field  is  blank
Modify  the  first  issue
Type
Enable=restart
There  are  only  two  message  states:  OK  and  ERROR
string  
Status,  OK  means  successful  processing,  ERROR  means  error
ÿReply  information
mer_trade_no  
period_no  
End=end
int  
Response  Status  Description
Modify  the  period
Enter  the  period  to  be  modified.
status  
suspend
msg  
Response  Status
int  
There  are  only  two  message  states:  OK  and  ERROR
msg  
AND
period_no  
Response  Status  Description
Machine Translated by Google

89  
"status":  "OK",  
"msg":  "",  
"mer_trade_no":  "P241224111116508"  
}  
{  
{  
"cust_id":  "CV0100000008",  
"mer_trade_no":  "P241224111116508",  
"revise_trade_status":  "end"  
Response  
Success:
}  
{  
"status":  "ERROR",  "msg":  
"Cannot  adjust  the  status  of  terminated  orders"
}  
fail:
Machine Translated by Google

POST /api/Period/  Exchange  
success:
Status,  OK  means  successful  processing,  ERROR  means  error
ÿTransmit  data:
30  Y
string  
string  
string  
Response  Status
*You  must  complete  the  first  authorization  or  1  yuan  authorization  to  modify  the  authorization  card  number.
cust_id  
mer_trade_no  
more_trade_no
15.  Renewal  of  payment  -  Authorization  card  number  modification
Fields
"cust_id":  "CV0100000008",  
}  
{  
Type
Store  Order  Number
Fields
Authorization:  Bearer  [token…]  
Response  Status  Description
string  20  Y  string  
There  are  only  two  message  states:  OK  and  ERROR
url  
ÿSample  
Response  
Store  Order  Number
Date;  Provide  consumers  with  the  ability  to  re-authorize  1  yuan  through  the  payment  page  of  the  PAYUNi  platform.
illustrate
msg  
"mer_trade_no":  "P241223102129334"  
If  successful,  this  field  will  be  left  blank.
Type  length  required  description
string  
status  
When  consumers  need  to  change  their  card  numbers  due  to  card  expiration,  insufficient  credit  limit,  etc.,  they  can  use  this  function  to  modify  the  credit  card  number  and  the
Codename  of  Qike  Jinliu
Send  
Generate  a  short  URL  for  reauthorizing  the  card  number
Content-Type:  application/json  
ÿReply  information
Abnormal  situation  occurs  during  processing
{  
90  
Machine Translated by Google

91  
fail:
{  
"https://test.4128888card.com.tw/period/cocs/PayuniPeriodExchange/0hBxwHRhQ" }  
"status":  "OK",  
"msg":  "",  
"mer_trade_no":  "P241223102129334",  "url":  
}  
"status":  "ERROR",  
"msg":  "mer_trade_no  does  not  exist"
Machine Translated by Google

POST /api/Period/  Modify  
month  Please  enter  a  number  from  1  to  31,  representing
period_date  
string  
Payee's  local  phone  number
Content-Type:  application/json  
Payee's  name
Down  payment  amount
Monday  to  Sunday
Deduction  date
Payee  Email
string  
cust_id  
mer_trade_no  
The  amount  limit  is  based  on  the  contract  specifications  (preset
payer_email  
period_amount  
16.  Renewal  Collection-Order  Content  Modification
Fields
first_amount  
number  
Payee's  mobile  phone  number
For  example:  02-2222-2222#222
period_type  
week:  every  week
payer_name  
Type  length  required  description
string  20  
25  Y
10  
ÿTransmit  data:
Between  100~100,000)
The  amount  will  not  take  effect
number  
number  
Store  Order  Number
And  period  number.
Mobile  phone  number  is  preferred)
Amount  per  issue
From  the  1st  to  the  31st  of  each  month,  if  there  is  no
240  
week  Please  enter  the  number  1~7,  representing  the  day  of  the  week
Brackets  are  not  allowed,  -#  is  allowed
string  20  Y  string  string  
(If  both  mobile  and  landline  are  input,
payer_mobile  
Provide  unauthorized  renewal  collection  orders.  Stores  can  modify  order  content  according  to  needs,  including  amount  and  deduction  cycle
Codename  of  Qike  Jinliu
string  
If  the  first  payment  has  been  authorized,  please  fill  in  this  amount.
Authorization:  Bearer  [token…]  
50  
pay_tel
Deduction  cycle
month:  every  month
92  
Machine Translated by Google

"status":  "OK",  
more_trade_no
period_times  
There  are  only  two  message  states:  OK  and  ERROR
"payer_email":  "<EMAIL>"  
"mer_trade_no":  "P250108142823888",  
msg  
Please  choose  a  week  between  2  and  156
illustrate
"payer_name":  "MrTest",  
The  date  is  based  on  the  last  day  of  the  month.
status  
}  
success:
"first_amount":  2345  
If  successful,  this  field  will  be  left  blank.
"cust_id":  "CV0100000008",  
"mer_trade_no":  "P250108142823888"  
Abnormal  situation  occurs  during  processing
ÿReply  information
Send  
Response  Status
string  
Please  choose  month  between  2  and  36
"payer_tel":  "02-88888888",  
"period_date":  2,  
{  
string  
Deduction  Period
string  
"period_amount":  1234,  
Response  
{  
Fields
Store  Order  Number
Response  Status  Description
Payment  Date
Type
"period_times":  8,  
"period_type":  "week",  
number  
Status,  OK  means  successful  processing,  ERROR  means  error
ÿSample  
"msg":  "",  
}  
93  
Machine Translated by Google

"status":  "ERROR",  
"msg":  "mer_trade_no  does  not  exist"
}  
{  
fail:
94  
Machine Translated by Google

string  
32  1.  Unique  transaction  identification  code  for  each  online  card  order.
ÿTransmission  data  format
2.  After  that,  the  sender  will  send  a  notification  message  every  15  minutes.  A  status  code  will  be  sent  up  to  3  times.
(Cannot  cancel  authorization  during  payment  request)
number  Order/Transaction  Amount  Transaction  Amount
illustrate
The  renewal  payment  is  not  deducted  on  the  same  day.  There  is  only  a  1  yuan  authorization  to  bind  the  credit  card.  It  is  not  an  actual  transaction,  so  there  will  be  no  APN
POST  
B=Authorization  Completed
17.  APN  active  notification
3.  Use  checksum  verification  code  to  ensure  the  security  of  data.
order_no  
amount  
string  
string  
Fields
Number)
3.  If  the  user  replies  with  a  plain  text  "OK"  message,  it  will  not  be  sent  again.
Description  1.  Encapsulate  order  data  in  json  format  as  the  transmitted  message.
20  1.  Customer  order  number
Retrun  1.  After  payment  is  completed,  the  APN  transmitter  will  send  a  notification  immediately
32  Customer  code,  the  payment  code  assigned  when  applying  for  this  service
notify.
O=Payment  request  in  progress
F=Authorization  failed
status  
Method  
"APN-Proactive  message  URL".
Degree
1  Order  status  code
string  
2.  Renewal  payment:  reply  with  the  renewal  order  number  (with  suffix  _1,  _2
2.ContentType  =  application/jsonÿ  
Long  shape
ÿTransmit  data:
When  the  order  status  of  the  contract  customer  changes,  the  notification  message  will  be  sent  to  the  client  website:  the  contract  customer  specifies
URL  is  in  the  backend  management  system  of  the  multi-payment  platform,  logged  in  APN-active  message  URL
api_id  
trans_id  
E=Payment  completed
Field  Description
Second-rate
2.  Renewal  payment:  reply  with  the  store  order  number  (without  the  last  digit)
95  
Machine Translated by Google

96  
create_time  
{  
(year-month-day  hour:minute:second)
R=Cancel  authorization  failed
}  
}  
25  Payment  overdue  time,
N=Cancel  transaction  failed
"auth_code":"Card  swipe  authorization  code  (6  digits)",
ÿÿ:  yyyy-MM-dd  HH:mm:ss  
D  =  Order  overdue
Object  
memo  
25  Status  update  time,
modify_time  
"auth_card_no":null,  
J=Invoice  Discount  Number  Notice
string  ÿ
ÿ:  yyyy-MM-dd  HH:mm:ss  
"auth_card_no":"The  first  six  and  last  four  digits  of  the  credit  card  number"
Example  of  failed  card  swipe  information
Q=Deauthorization  completed
ÿÿ:  yyyy-MM-dd  HH:mm:ss  
expire_time  
{  
M=Cancel  transaction  completed
Object  Reserved
(year-month-day  hour:minute:second)
(year-month-day  hour:minute:second)
number  Kelede  card  payment  service  (fixed  value  is  1)
"pay_date":"2022-07-07  08:08:08",  
P=Payment  request  failed
payment_code  
payment_detail  
string  
25  Payment  order  creation  time,
I=Invoice  Notification
Example  of  card  swiping  information  after  swiping  the  card
"auth_code":null,  
string  
"auth_card_no":"123456******1234",  
}  
"auth_code":"123456",  
{  
"pay_amount":888  
"pay_amount":null  
"pay_date":null,  
Machine Translated by Google

"trans_id":"550e8400e29b41d4a716446655440000",  
string  
The  same  MD5  result  means  that  the
string  
random_number  16  
Discount  order  number
20  Love  Code
3:  Natural  person  certificate
RRRR:  Four-digit  random  number
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
invoice_no  string  10  Invoice  date
10  Random  numbers,  non-repeating  time  +  random  number  combination  using  hours  and  minutes
checksum  
Response  
{  
ÿSample  
1  Vehicle  Type
1.  Donate
"payment_code":1,  
"amount":1250,  
0:  Do  not  print
string  
1  Do  you  want  to  donate  the  invoice?
1  Whether  to  print  paper  invoice
vehicle_type  
32  Check  the  verification  code  to  see  if  the  checksum  matches  the  following
string  
invoice_date  
string  4  random  code
vehicle_barcode  
donate_invoice  
"api_id":"CC0000000001",  
checksum  =  MD5  ( api_id  +":"  +  trans_id  +":"  +  amount+":"  +  
status  +":"  +  nonce )  
HHNNSS:  Hours,  minutes,  and  seconds  of  the  host  time  (6  digits)
2:  Mobile  phone  barcode
love_code  string  10  Invoice  number
When  the  customer  uses  the  card  payment  service,  the  response  message  from  APN  is
0:  Do  not  donate
nonce  
1:  Member  Vehicle
1:  Print
Seconds  +  random  number  generation:  HHNNSSRRRR
string  
string  
string  
invoice_discount_no  string  
The  content  of  the  message  is  consistent  with  the  original  data.
print_invoice  
20  Carrier  barcode
"order_no":"*********",  
"status":"B",  
97  
Machine Translated by Google

I.  APN  Reissue
In  "Payment  Collection  Operation  >  Order  Inquiry  >  Details",  an  APN  reissue  button  is  provided  so  that  users  can  test  APN  repeatedly
MD5(CC0000000001:  550e8400e29b41d4a716446655440000:1250:B:**********)  
"payment_detail":{"auth_code":"123456","auth_card_no":"552199******1864"},  "memo":"",  
"expire_time":"2013-09-28  08:15:00",  
"create_time":"2013-09-28  08:00:00",  
"modify_time":"2013-09-28  08:30:00",  
"nonce":"**********",  
"checksum":"d09d5532767453ad4c6ba9b649034187",  "print_invoice":"0",  
"vehicle_type":"2",  
"vehicle_barcode":"/
1234567",  "donate_invoice":"",  
"love_code":"",  
"invoice_no":"",  
"invoice_date":"",  
"random_number":"",  
"invoice_discount_no":"" }  ÿÿ
ÿÿÿÿchecksum  ÿÿÿÿÿÿ  
Or  to  supplement  APN  information.
98  
Machine Translated by Google

“cmd”:  “SmsShortNameUpdate”,  
sms_short_name  
ÿTransmit  data:
1:  COCS  online  card  payment
There  are  only  two  message  states:  OK  and  ERROR
Type
AND
The  customer's  abbreviation  is  used  to  facilitate  identification  of  the  source  store.
Financial  service  type
illustrate
Transaction  Code
msg  
Send  
string  
cust_id  
service_type  
status  
“service_type”:  “0”,  
cmd  
Authorization:  Bearer  [token…]  
ÿReply  information
0:  CVS  payment  agency
Adjusted  SMS  abbreviation
Response  Status
Abnormal  situation  occurs  during  processing
{  
Codename
The  contract  customer  adjusts  the  SMS  abbreviation  information  through  the  API.  If  the  contract  customer  has  enabled  the  SMS  sending  function,  the  abbreviation  will  be  included  in  the  content.
1  
string  
ÿSample  
Fields
Type  length  required  description
string  20  Y  string  
string  20  Y  
1.  Change  the  abbreviation  of  the  financial  service  message
(bring  in  SmsShortNameUpdate)
Reply  to  message
Status,  OK  means  successful  processing,  ERROR  means  error
Content-Type:  application/json  
Fields
string  20  Y  
“cust_id”:  “***********”,  
“sms_short_name”:  “SMS  short  name  XXXX”
POST /api/Collect  
4.  Master  File  Management
99  
Machine Translated by Google

100  
fail:
{  
}  
“status”:  “ERROR”,  “msg”:  
“Failed  to  change  the  short  name  of  the  payment  service  message”
{  
“status”:  “OK”  
Response  
Success:
}  
}  
Project  Project  Name
1  The  cmd  data  is  incorrect.  2  The  
contract  customer  data  is  incorrect.  3  The  
contract  customer  data  does  not  exist.  4  
The  user  password  cannot  exceed  40  characters.  5  
cust_id  (XXXX)  and  token  (XXXX)  do  not  match  6  User  login  failed,  the  
user  has  been  deactivated.  7  Data  error,  "Payment  service  type  
specified  incorrectly".  8  Data  error,  "CVS  convenience  bank  payment  
payment  service  type  has  not  been  applied  for".  9  Data  error,  "COCS  online  card  payment  service  type  
has  not  been  applied  for".  10  Data  error,  "SMS  abbreviation  cannot  be  blank".  11  SMS  abbreviation  
cannot  exceed  20  characters.  12  Failed  to  obtain  payment  service.  13  
Failed  to  change  the  SMS  abbreviation  of  payment  
service
ÿ  Abnormal  message  list
Machine Translated by Google

POST /api/Collect  
If  this  parameter  has  an  incoming  URL,  authorization  is  successful.
10  And
opw:  OPEN  Wallet
Order  amount
Content-Type:  application/json  
string  20  Y  
string  
Order  authorization  successfully  specifies  the  return  URL
send_time  
AND
1  Starts  to  accumulate,  for  example:
Fields
Required  to  comply  with  government  regulations
20  Y
(Fixed  into  DphOrderAppend)
acquirer_type  
50  
The  number  is  YYYYMMDD######,
order_amount  
HH:mm:ss,  for  example:  2017-07-18
The  transmission  time  must  be  the  longest  during  the  transmission
string  
Type  length  required  description
(Default  is  100,000)
cmd  
**************  
ÿTransmit  data:
order_detail  
Payee's  name
Designated  acquiring  bank
07:17:25  
The  system  will  automatically  generate
Contract  customers  add  mobile  payment  orders  through  the  API.
The  order  serial  number  within  the  date,  starting  from
Order/Product  Details
success_url  
payer_name  
string  
string  250  
number  
Transaction  Code
The  first  eight  digits  are  the  year,  month  and  day,  and  the  last  six  digits  are
30  Y
1.  Qike  adds  mobile  payment  orders
The  order  number  of  the  customer.  If  this  field  is  empty
string  
string  
500  Y
Authorization:  Bearer  [token…]  
cust_order_no  
The  upper  limit  of  the  amount  is  subject  to  the  contract  specifications.
icp:  icash  Pay  
New  time,  in  the  format  of  yyyy-MM-dd
5.  DPH  Mobile  Payment
101  
Machine Translated by Google

1  
electronic
1  
electronic
string  20  
(B2C  invoice  winning  SMS  notification)
APN  specifies  the  delivery  URL  (if  this  field
1  
Y  Electronics
Payee  address  (required  if  b2c  is  1)
20  
1:  Open
1.  Donate
electronic
10  Y  Electronic
string  
string  250  
Y  Electronics
string  
string  
60  Y  Electronic
If  no  input  is  made,  the  service  will  be  based  on  the  customer  service
vehicle_barcode  
string  
string  
payer_mobile  
Whether  to  print  paper  invoice  (if  b2c  is  1
Postback
vehicle_type  
Required)
love_code  
0:  Do  not  open  (default)
0:  Do  not  donate
Vehicle  Type
bill
product_name  
apn_url
donate_invoice  
bill
The  successful  reply  URL  will  bring  in  this  URL.
payer_postcode  
2:  Mobile  phone  barcode
Product  Name  (required  if  b2c  is  1)
Payee  name  (required  if  b2c  is  1)
bill
bill
The  URL  specified  in  the  service  settings  for  successful  authorization
Payee's  postal  code  (if  b2c  is  1  then
string  
bill
1:  Print
Is  an  electronic  invoice  issued  for  this  order?
fill)
50  Y  Electronic
Set  the  return  URL)
Required)
Payee's  mobile  phone
electronic
payer_name  
1:  Member  Vehicle
1  
string  240  Y  Electronic
The  default  is  Genesis  Foundation  919)
string  
fill)
3:  Natural  person  certificate
string  20  Y  Electronic
0:  Do  not  print
bill
bill
Whether  to  donate  invoice  (if  b2c  is  1,  it  is  required
No  information,  please  log  in  to  the  Black  Cat  PAY  platform
Carrier  barcode
bill
fill)
string  
b2c  
bill
bill
Love  code  (select  Donate.  If  the  love  code  is  not  entered
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
bill
print_invoice  
payer_address  
102  
Machine Translated by Google

“url”:  “https://dph.ccat.com.tw/K616G6”  
{  
“order_amount”:  500,  
status  
bill
electronic
string  
“cust_order_no”:  “20201126110723”,  
Response  
Abnormal  situation  occurs  during  processing
Type
Buyer's  Uniform  Number
8  
Generate  the  transaction  URL  corresponding  to  the  order
Payee  Email
payer_email  
Send  
“cmd”:  “DphOrderAppend”,  
ÿSample  
ÿReply  information
Status,  OK  means  successful  processing,  ERROR  means  error
}  
“acquirer_type”:  “opw”,  
buyer_invoice_title  string  
50  Y  Electronic
Response  Status
Invoice  Title
Fields
electronic
string  
cust_order_no  
“cust_order_no”:  “20201126110723”,  
“status”:  “OK”,  
string  
EMAIL  Notification)
For  DPH  orders,  please  use  the  following  reply  fields  as  the  primary  ones.  Unused  fields  will  be  filled  with  NULL  or  0.
Customer  order  number
}  
{  
success:
fail:
There  are  only  two  message  states:  OK  and  ERROR
bill
Since  this  specification  is  provided  for  use  by  CVS,  COCS  and  DPH,  the  reply  data  fields  are  shared.
161  
(b2c  electronic  invoice  and  invoice  winning
buyer_bill_no  
url  
string  
string  
bill
illustrate
“order_detail”:  “Test  order  notes”,
“send_time”:  “2020-11-26  11:07:23”  
103  
Machine Translated by Google

104  
1  The  cmd  data  is  incorrect.  2  The  
contract  customer  data  is  incorrect.  3  The  
contract  customer  data  does  not  exist.  4  
cust_id  (XXXX)  and  token  (XXXX)  do  not  match .  5  User  login  failed,  the  
user  has  been  deactivated.  6  Failed  to  obtain  financial  service  
information
The  order_detail  format  is  incorrect,  and  the  content  cannot  contain  Html  Tag.  8The  
required  order_amount  field  is  missing.  9The  order_amount  
format  is  incorrect.  10The  required  send_time  field  is  
missing .  11The  send_time  format  is  incorrect.  12The  
send_time  is  abnormal.  13The  payee's  mobile  
phone  number  must  be  entered,  or  
the  information  you  entered  is  incorrect.  14The  payee's  email  must  be  entered,  or  the  information  
you  entered  is  incorrect.  15The  customer  information  does  not  exist  or  the  mobile  payment  function  
is  not  enabled.  16The  acquiring  bank  setting  is  incorrect.  17You  have  not  
applied  for  OPEN  Wallet  as  the  
acquiring  bank.  18You  have  not  applied  for  icash  Pay  as  the  acquiring  
bank.  19The  customer  has  not  set  the  API  response  URL.  20  
cust_order_no  must  be  an  alphanumeric  character  with  
more  than  three  digits  or  a  minus  sign.  21The  order  has  expired.  22The  data  is  incorrect.  You  
have  already  uploaded  this  
"contract  order  number":  XXXXX,  and  you  cannot  upload  it  again.  23The  financial  service  has  been  disabled .  24The  data  is  incorrect.  The  
"amount  paid  on  behalf  of"  must  be  
less  than  XXXXX  25  Data  error,  "Amount  paid  on  behalf  of"  must  be  greater  than  
XXXXX  26  Financial  service  does  not  have  this  acquiring  bank  information
Project  Project  Name
7  
}  
ÿ  Abnormal  message  list
“status”:  “ERROR”,  “msg”:  
“send_time  abnormal.”
{  
27  Bank  KEY,  MID  and  TID  cannot  be  blank  28  Convenience  store  
unified  number  field  cannot  be  blank
29  BU  Store  Name,  Store  Code  and  POS  Number  cannot  be  blank
Machine Translated by Google

105  
30The  electronic  invoice  function  has  not  
been  enabled.  31Whether  to  print  paper  invoices  has  not  been  
entered  or  the  format  is  incorrect.  32Whether  to  donate  
invoices  has  not  been  entered  or  the  format  is  incorrect.  33You  can  only  select  one  of  donation  invoices,  
unified  (company  account)  invoices,  printed  paper  invoices  or  common  
carriers.  34Please  enter  
donation,  paper  printing  invoices  or  use  invoice  
carriers .  35Please  enter  the  carrier  
type.  36Incorrect  data,  "Carrier  barcode"  is  
required.  37Incorrect  carrier  
barcode  format.  38Incorrect  data,  "Carrier  barcode"  
is  required.  39Incorrect  carrier  barcode  format.  
40Incorrect  data,  "Product  name"  is  required.  41Incorrect  data,  
"Payer  name"  is  required.  42Incorrect  data,  "Payer  postal  
code"  is  in  the  wrong  format.  43Incorrect  data,  "Payer  
postal  code"  is  required.  44Incorrect  data,  "Payer  address"  
is  required.  45Incorrect  data,  invalid  payee  mobile  number.  
46Information  error,  "Payer's  mobile  phone  number"  is  
required.  47Information  error,  invalid  payer's  email  address.  
48Information  error,  "Payer's  email  address"  is  
required.  49Information  error,  buyer's  unified  number  is  incorrect.  50Information  error,  "Invoice  header"  is  required.
Machine Translated by Google

106  
The  system  will  issue  a
The  format  is  yyyy-MM-dd  HH:mm:ss,
right
(The  delivery  time  entered  in  the  new  order  by  the  customer).
string  
Response  
Notification  time,  which  is  the  time  when  the  message  was  sent.
string  
ÿReply  information
Order  amount
The  format  is  yyyy-MM-dd  HH:mm:ss,
2.  Bank  authorization  to  complete  return
Response  Status
MD5(hash_base+'$'+order_amount+'$'+  
send_time+'$'+ret+'$'+acquire_time  
+'$'+auth_code+'$'+card_no+'$'+  
notify_time+'$'+cust_order_no)  
Algorithm  obtained.
Verification  code,  the  verification  code  is  generated  by
cust_order_no  
Authorization  Code
When  authorization  is  completed,  redirect  to  the  customer  website:  http://10.10.10.100//authSuccess.php
string  
For  example:  2017-09-14  10:31:25
string  
acquire_time  
Original  order  delivery  time
Acquiring  transaction  time  (authorization  time),
Fields
For  example:  2017-09-14  10:37:08
auth_code  
hash_base:  used  as  the  base  for  generating  the  chk  check  code
Customer  order  number
When  completed,  redirect  to  Qike  website"
send_time  
(Updated  with  every  transmission),
First  concatenate  each  field  with  '$'  and  then  use  MD5  to  perform
string  
illustrate
order_amount  
string  
string  
When  the  bank  authorization  is  completed,  the  consumer  is  redirected  to  the  client  browser  URL;  the  "Authorization  Completed"  URL  specified  by  the  contract  customer
OK  if  authorization  is  successful
notify_time  
string  
Type
The  format  is  yyyy-MM-dd  HH:mm:ss,
For  example:  2017-09-14  10:36:38
chk  
ÿSample  
Machine Translated by Google

107  
ount=2&send_time=2017-09-14  10:31:25&acquire_time=2017-09-14  
10:36:38&auth_code=951294&card_no=1849&notify_time=2017-09-14  
10:37:08&chk=9ea4928734f4099b387c75a344fe3e50  
http://10.10.10.100/authSuccess.php?ret=OK&cust_order_no=C201709141001&order_am  
Machine Translated by Google

108  
(Updated  with  every  transmission),
right
(The  delivery  time  entered  in  the  new  order  by  the  customer).
hash_base:  used  as  the  base  for  generating  the  chk  check  code
Verification  code,  the  verification  code  is  generated  by
string  
ÿReply  information
Order/Transaction  Amount
First  concatenate  each  field  with  '$'  and  then  use  MD5  to  perform
3.  Report  of  bank  authorization  failure
Response  Status
Redirect  to  the  Qike  website  when  authorization  fails:  http://10.10.10.100/authFail.php
http://10.10.10.100/authFail.php?ret=FAIL&cust_order_no=C201709141001&order_amou  
ÿSample  
cust_order_no  
string  
For  example:  2017-09-14  10:31:25
string  
For  example:  2017-09-14  10:37:08
Original  order  delivery  time
Notification  time,  which  is  the  time  when  the  message  was  sent.
Fields
Algorithm  obtained.
string  
nt=2&send_time=2017-09-14  10:31:25&notify_time=2017-09-14  10:37:08&chk=  
3aa9a501ae2c8e58f8dfdcb07af4b7c3  
Customer  order  number
Direction  to  Qike  website
send_time  
MD5(hash_base+'$'+order_amount+'$'+  
send_time+'$'+ret+'$'+  
notify_time+'$'+cust_order_no)  
Response  
notify_time  
illustrate
order_amount  
string  
string  
When  bank  authorization  fails,  redirect  the  client  browser  to  the  URL  specified  by  the  contract  customer:  "Redirect  when  authorization  fails"
FAIL  when  authorization  fails
chk  
The  system  will  issue  a
Type
The  format  is  yyyy-MM-dd  HH:mm:ss,
The  format  is  yyyy-MM-dd  HH:mm:ss,
Machine Translated by Google

POST /api/Collect  
Order  process  status  change  date
string  
number  Order  process  status  code  (refer  to  Attachment  1)
Fields
Authorization:  Bearer  [token…]  
Abnormal  situation  occurs  during  processing
Acquiring  Bank
process_code  
process_code_update_time  string  
request  _amount  
grant_date  
grant_amount  
Payment  Due  Date
30  Y
ÿTransmit  data:
There  are  only  two  message  states:  OK  and  ERROR
string  
4.  Order  Inquiry
Customer  order  number
Disbursement  Date  (yyyy-MM-dd)
(This  is  a  pre-calculation  by  the  system.  The  actual  amount  of  funding  is
Estimated  payment  amount
string  20  Y  
string  
number  order  amount
string  
acquirer_type  
Fields
Transaction  Code
Type
Status,  OK  means  successful  processing,  ERROR  means  error
ÿReply  information
Content-Type:  application/json  
(yyyy-MM-dd  HH:mm:ss)  
Estimated  payment  date
Bank’s
number  
Response  Status
string  
cust_order_no  
order_amount  
expire_date  
Estimated  grant  amount
number  
string  
(Fixed  into  DphOrderQuery)
cust_order_no  
cmd  
Contract  customers  can  query  mobile  payment  order  information  through  API.
illustrate
(yyyy-MM-dd )  
(opw:  OPEN  Wallet/icp:  icashPay)
Type  length  required  description
Customer  order  number
status  
string  
request_date  
109  
Machine Translated by Google

110  
“cust_order_no”:  “**************”,  
Invoice  Date
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
vehicle_type  
Send  
“request_amount”:  50,  
“grant_date”:”2017-08-29”,  
string  
string  
string  
Love  Code
string  
1:  Member  Vehicle
string  
(yyyy-MM-dd  HH:mm:ss)  
print_invoice  
Response  
{  
}  
Do  you  want  to  donate  the  invoice?
love_code  
invoice_no  
invoice_date  
“request_date”:  “2017-08-27”,  
“expire_date”:  “2017-08-25  13:31:00”,  
3:  Natural  person  certificate
Print  paper  invoice
vehicle_barcode  
donate_invoice  
2:  Mobile  phone  barcode
Invoice  Number
(yyyy-MM-dd  HH:mm:ss )  
ÿSample  
“cmd”:  “DphOrderQuery”,  
“status”:  “OK”  
Vehicle  Type
create_time  
1.  Donate
random_number  
success:
string  
string  
0:  Do  not  print
0:  Do  not  donate
Carrier  barcode
Order  creation  time
1:  Print
“cust_order_no”:  “**************”  
{  
string  
string  
Random  code
“order_amount”:  50,  
“acquirer_type ”:  “opw”,  
Machine Translated by Google

111  
}  
ÿ  Abnormal  message  list
“status”:  “ERROR”,  “msg”:  
“Failed  to  obtain  query  transaction  information”
1  cmd  data  is  incorrect.  2Contract  
customer  data  is  incorrect.  3Contract  
customer  data  does  not  exist.  4Parameter  
+  cust_order_no  is  required  5Cannot  find  this  
transaction  information  6Failed  to  
obtain  query  transaction  information
}  
{  
fail:
“grant_amount”:  0,  
“process_code”:  15,  
“process_code_update_time”:  “2017-08-25  10:31:55”,  “create_time”:  
“2017-08-25  10:30:44”,  “card_no”:  “1243”,  “print_invoice”:  
“0”,  “vehicle_type”:  “”,  
“vehicle_barcode”:  “”,  
“donate_invoice”:  “1”,  
“love_code”:  “919”,  “invoice_no”:  
“”,  “invoice_date”:  “”,  
“random_number”:  “”  
Project  Project  Name
Machine Translated by Google

POST /api/Collect  
“cust_order_no”:  “”,  
Fields
Content-Type:  application/json  
order_start_date  
Order  list,  presented  in  Array  format
Response  Status
20  Y
ÿTransfer  URL:  https://4128888card.com.tw/api/Collect
cmd  
Status,  OK  means  successful  processing,  ERROR  means  error
5.  Order  date  range  query
(Fixed  into  DphOrderListQuery)
ÿSample  
{  
Array  
(yyyy-MM-dd  HH:mm:ss)  
Number  of  response  orders
"order_end_date":  "2021/04/21  11:00:00"  
(yyyy-MM-dd  HH:mm:ss)  
Type  length  required  description
status  
string  20  Y  
ÿReply  information
ÿTransmit  data:
Abnormal  situation  occurs  during  processing
string  
string  
“cmd”:  “DphOrderQuery”,  
Fields
order_end_date  
There  are  only  two  message  states:  OK  and  ERROR
Send  
Type  Description
Transaction  Code
string  
Order  date  to  date
Contract  customers  can  query  mobile  payment  order  information  through  API.
Order  date
order_list  
(Contents  are  the  same  as  the  order  inquiry  column)
Authorization:  Bearer  [token…]  
string  20  Y  
msg  
"order_start_date":  "2021/04/01  00:00:00",  
112  
Machine Translated by Google

113  
{  
"msg":  "Count:2",  "status":  
"OK",  "order_list":  [ {  
Response  
Success:
}  
},  
{  
"msg":  "",  
"status":  "OK",  
"cust_order_no":  "2021042000661284",  "order_amount":  
3920,  "expire_date":  "2021-04-20  
19:07:00",  "acquirer_type":  "opw",  "request_date":  "",  
"msg":  "",  
"status":  "OK",  
"cust_order_no":  "2021042100661289",  "order_amount":  
1140,  "expire_date":  "2021-04-21  
11:46:00",  "acquirer_type":  "icp",  "request_date":  "",  
"request_amount":  null,  
"grant_date":  "",  
"grant_amount":  null,  "process_code":  
6,  
"process_code_update_time":  
"2021-04-21  11:57:47",  
"create_time":  "2021-04-21  10:46:28",  "print_invoice":  "0",  "vehicle_type":  "",  
"vehicle_barcode":  "",  "donate_invoice":  "1",  "love_code":  
"919",  "invoice_no":  "",  
"invoice_date":  "",  
"random_number":  ""  
Machine Translated by Google

"msg":  "Count:0",  "status":  
"OK",  "order_list":  []  
}  
No  data:  {
ÿ  Abnormal  message  list
]  
}  
}  
"request_amount":  null,  
"grant_date":  "",  
"grant_amount":  null,  
"process_code":  6,  
"process_code_update_time":  "2021-04-20  19:27:47",  "create_time":  
"2021-04-20  18:06:31",  "print_invoice":  "0",  
"vehicle_type":  "",  
"vehicle_barcode":  "",  
"donate_invoice":  "1",  
"love_code":  "919",  "invoice_no":  
"",  "invoice_date":  "",  
"random_number":  ""  
1  cmd  data  is  incorrect.  2Contract  
customer  data  is  incorrect.  3Contract  
customer  data  does  not  exist.  4Must  
have  parameters  +  cust_order_no,  order_start_date,  order_end_date  5Cannot  find  this  transaction  
information  6Failed  to  obtain  query  
transaction  information
Project  Project  Name
114  
Machine Translated by Google

115  
cust_order_no  
string  20  Y  
Authorization:  Bearer  [token…]  
New  time,  in  the  format  of  yyyy-MM-dd
Type
“cust_order_no”:  “**************”,  
07:17:25  
30  Y
ÿTransmit  data:
(opw:  OPEN  Wallet/icp:  icashPay)
Fields
6.  Order  Cancellation  Authorization
Customer  order  number
Status,  OK  means  successful  processing,  ERROR  means  error
Customer  order  number
There  are  only  two  message  states:  OK  and  ERROR
string  20  Y  
10  And
“cmd”:  “DphOrderCancel”,  
Send  
Fields
Transaction  Code
send_time  
The  transmission  time  must  be  the  longest  during  the  transmission
AND
Content-Type:  application/json  
status  
illustrate
string  
Acquiring  Bank
string  
number  
ÿReply  information
Abnormal  situation  occurs  during  processing
“order_amount”:  500,  
string  
(Fixed  into  DphOrderCancel)
cust_order_no  
order_amount  
cmd  
Before  the  order  is  paid,  the  contracted  customer  can  cancel  the  order  authorization  through  API.
Order  amount
Response  Status
string  
Type  length  required  description
HH:mm:ss,  for  example:  2017-07-18
acquirer_type  
ÿSample  
{  
POST /api/Collect  
Machine Translated by Google

116  
}  
fail:
“status”:  “OK”,  
“cust_order_no”:  “**************”  
{  
Response  
Success:
{  
}  
“acquirer_type”:  “opw”,  “send_time”:  
“2017-07-18  03:07:23”  
Project  Project  Name
“status”:  “ERROR”,  “msg”:  
“Order  cancellation  authorization  failed”
}  
1  The  cmd  data  is  incorrect.  2  The  
required  field  is  missing.  3  The  
order_amount  format  is  incorrect.  4  The  send_time  
format  is  incorrect.  5  The  send_time  is  
abnormal.  6  The  customer  data  
does  not  exist.  7  The  acquiring  
bank  is  set  incorrectly.  8  You  have  
not  applied  for  OPEN  Wallet  as  the  acquiring  bank.  9  The  
current  order  procedure  does  not  allow  cancellation  of  
authorization.  10  The  order  amount  
is  incorrect.  11  The  cancellation  of  authorization  failed,  
and  there  is  no  such  order.  12  cust_order_no  
does  not  exist.  13  The  cancellation  of  authorization  failed,  
and  the  communication  is  abnormal.  14  The  cancellation  of  authorization  failed.  Bank  reply:  XXXXX
ÿ  Abnormal  message  list
Machine Translated by Google

POST /api/Collect  
Cancelled  transaction  (return,  refund)  amount
AND
Authorization:  Bearer  [token…]  
The  transmission  time  must  be  the  longest  during  the  transmission
status  
{  
10  And
string  
number  
ÿTransmit  data:
opw:  OPEN  Wallet
ÿReply  information
7.  Order  cancellation  transaction  (return,  refund)
Customer  order  number
There  are  only  two  message  states:  OK  and  ERROR
Abnormal  situation  occurs  during  processing
Response  Status
cmd  
string  
Send  
cust_order_no  
refund_amount  
HH:mm:ss,  for  example:  2017-07-18
Transaction  Code
acquirer_type  
icp:  iCashPay  
30  Y
Content-Type:  application/json  
Fields
string  
Customer  order  number
Designated  acquiring  bank
cust_order_no  
order_amount  
07:17:25  
Status,  OK  means  successful  processing,  ERROR  means  error
“cmd”:  “DphOrderRefund”,  
send_time  
(Fixed  into  DphOrderRefund)
string  20  Y  
Fields
When  the  order  has  been  paid,  the  contracted  customer  can  cancel  the  transaction  (return  the  goods)  through  the  API  and  only  a  full  refund  will  be  provided.
Order  amount
illustrate
Type
Type  length  required  description
New  time,  in  the  format  of  yyyy-MM-dd
string  20  Y  
string  
string  
ÿSample  
117  
Machine Translated by Google

118  
}  fail:
{  
“status”:  “OK”,  
“cust_order_no”:  “**************”,  “refund_amount”:  
500  
“status”:  “ERROR”,  “msg”:  
“Order  cancellation  transaction  failed”
Response  
Success:
{  
}  
“cust_order_no”:  “**************”,  “order_amount”:  
500,  “acquirer_type”:  “opw”,  
“send_time”:  “2017-07-18  03:07:23”  
}  
Project  Project  Name
1  Incorrect  cmd  data.  2  Required  
fields  are  missing.  3  Incorrect  
order_amount  format.  4  Incorrect  send_time  format.  5  
Send_time  exception.  6  Customer  data  does  
not  exist.  7  Acquirer  bank  settings  
are  incorrect.  8  You  have  not  applied  
for  OPEN  Wallet  as  an  acquirer.  9  
You  have  not  applied  for  iCashPay  as  an  acquirer.  10  The  current  
order  process  does  not  allow  transaction  cancellation.  11  
Incorrect  order  amount.  12  The  transaction  cancellation  amount  
cannot  be  greater  than  the  order  
amount.  13  The  transaction  cancellation  amount  must  be  greater  
than  0  yuan.  14  The  specified  transaction  cancellation  
amount  cannot  be  greater  than  the  payment  request  amount.  15  The  
cumulative  transaction  cancellation  amount  cannot  be  greater  than  the  
order  amount.  16  The  cumulative  transaction  cancellation  amount  cannot  be  
greater  than  the  specified  payment  request  amount.  17  Partial  transaction  cancellation  is  not  allowed.
ÿ  Abnormal  message  list
Machine Translated by Google

119  
18  cust_order_no  does  not  exist.  19  Failed  
to  cancel  the  transaction.  There  is  no  such  order  or  the  payment  is  not  made  using  OPEN  Wallet.  20  
Failed  to  cancel  the  transaction.  There  is  no  such  order  or  the  payment  is  not  made  using  iCashPay.
Machine Translated by Google

POST /api/Collect  
cust_order_no  
AND
Content-Type:  application/json  
string  20  Y  
Type
“cust_order_no”:  “**************”,  
07:17:25  
number  
Fields
Fields
Status,  OK  means  successful  processing,  ERROR  means  error
8.  Order  payment  (support  OPEN  wallet  only )
(Fixed  into  DphCashRequest)
Customer  order  number
There  are  only  two  message  states:  OK  and  ERROR
30  Y
HH:mm:ss,  for  example:  2017-07-18
“cmd”:  “CocsCashRequest”,  
Send  
cr_amount  
send_time  
Type  length  required  description
The  transmission  time  must  be  the  longest  during  the  transmission
cmd  
string  
ÿTransmit  data:
status  
illustrate
string  
Order  amount
Submit  a  payment  request  through  the  API  or  UI  interface;  ICP  is  an  automatic  payment  request,  and  only  OPEN  Wallet  supports  automatic  payment  requests.
Request  amount
ÿReply  information
Abnormal  situation  occurs  during  processing
“order_amount”:  50,  
New  time,  in  the  format  of  yyyy-MM-dd
Transaction  Code
AND
string  
number  
If  the  contracted  customer  activates  the  manual  payment  request  process,  the  system  will  not  automatically  assist  in  sending  the  payment  request  file.  The  customer  needs  to  manually
Customer  order  number
Response  Status
string  
Authorization:  Bearer  [token…]  
cust_order_no  
order_amount  
10  And
ÿSample  
{  
120  
Machine Translated by Google

121  
}  
fail:
“status”:  “OK”,  
“cust_order_no”:  “**************”  
“status”:  “ERROR”,  “msg”:  
“Failed  to  specify  the  amount  to  request  payment”
Response  
Success:
{  
}  
“cr_amount”:  50,  
“send_time”:  “2017-07-18  03:07:23”  
}  
ÿ  Abnormal  message  list
Project  Project  Name
1  The  cmd  data  is  incorrect.  2  The  
required  field  is  missing.  3  The  
order_amount  format  is  incorrect.  4  The  cr_amount  
format  is  incorrect.  5  The  send_time  format  is  
incorrect.  6  The  send_time  is  abnormal.  7  
The  customer  data  does  not  exist.  
8  The  specified  order  does  not  
allow  the  specified  amount  of  payment.  9  The  current  order  
procedure  does  not  allow  the  specified  amount  of  payment.  10  The  
order  amount  is  incorrect.  11  The  
specified  amount  of  payment  cannot  be  greater  than  the  order  
amount.  12  The  specified  amount  of  payment  service  is  not  enabled  
for  this  account.  13  The  specified  amount  of  payment  operation  
failed.  There  is  no  such  order.  14  cust_order_no  does  not  exist.
{  
Machine Translated by Google

122  
payment_code  
O=Payment  request  in  progress
Description  1.  Encapsulate  order  data  in  json  format  as  the  transmitted  message.
Fields
R=Cancel  authorization  failed
P=Payment  request  failed
1  Order  status  code
ÿTransmission  data  format
The  APN  transmitter  will  send  notification  messages  every  15  minutes,  a  total  of  3  times.
N=Cancel  transaction  failed
9.  APN  active  notification
Method  
api_id  
trans_id  
order_no  
status  
illustrate
string  32  
Bank  order  number.
D  =  Order  overdue
Degree
3.  Use  checksum  verification  code  to  ensure  the  security  of  data.
E=Payment  completed
ÿTransmit  data:
B=Authorization  Completed
Field  Description
Q=Deauthorization  completed
J=Invoice  Discount  Number  Notice
string  
POST  
"APN-Proactive  message  URL".
string  
number  Order/transaction  amount  Transaction  amount
M=Cancel  transaction  completed
amount  
F=Authorization  failed
URL  is  in  the  backend  management  system  of  the  multi-payment  platform,  logged  in  APN-active  message  URL
string  
20  Customer  order  number
32  Customer  code,  the  payment  code  assigned  when  applying  for  this  service
When  the  order  status  of  the  contract  customer  changes,  the  notification  message  will  be  sent  to  the  client  website:  the  contract  customer  specifies
Retrun
number  Kelede  payment  service  (fixed  value  is  1)
I=Invoice  Notification
2.ContentType  =  application/jsonÿ  
Long  shape
(Cannot  cancel  authorization  during  payment  request)
Machine Translated by Google

123  
vehicle_type  
random_number  16  
Discount  order  number
1  Do  you  want  to  donate  the  invoice?
modify_time  
25  Payment  order  creation  time,
RRRR:  Four-digit  random  number
print_invoice  
string  
invoice_no  string  10  Invoice  date
checksum  
string  
string  ÿ
ÿ:  yyyy-MM-dd  HH:mm:ss  
Seconds  +  random  number  generation:  HHNNSSRRRR
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
Object  Reserved
ÿÿ:  yyyy-MM-dd  HH:mm:ss  
1:  Member  Vehicle
3:  Natural  person  certificate
1  Vehicle  Type
payment_detail  
string  
love_code  string  10  Invoice  number
1.  Donate
The  same  MD5  result  means  that  the
(year-month-day  hour:minute:second)
string  
HHNNSS:  Hours,  minutes,  and  seconds  of  the  host  time  (6  digits)
create_time  
(year-month-day  hour:minute:second)
1  Whether  to  print  paper  invoice
0:  Do  not  print
20  Carrier  barcode
invoice_discount_no  string  
10  Random  numbers,  non-repeating  time  +  random  number  combination  using  hours  and  minutes
25  Payment  overdue  time,
expire_time  
checksum  =  MD5  ( api_id  +":"  +  trans_id  +":"  +  amount+":"  +  
status  +":"  +  nonce )  
vehicle_barcode  
donate_invoice  
2:  Mobile  phone  barcode
invoice_date  string  string  4  random  
code
string  
string  
nonce  
25  Status  update  time,
memo  
The  content  of  the  message  is  consistent  with  the  original  data.
Object  Reserved
(year-month-day  hour:minute:second)
1:  Print
string  
ÿÿ:  yyyy-MM-dd  HH:mm:ss  
32  Check  the  verification  code  to  see  if  the  checksum  matches  the  following
string  
0:  Do  not  donate
20  Love  Code
Machine Translated by Google

124  
Response  
When  the  customer  uses  the  mobile  payment  service,  the  response  message  from  APN
MD5(CC0000000001:  550e8400e29b41d4a716446655440000:1250:B:**********)  
{ "api_id":"CC0000000001",  
"trans_id":"550e8400e29b41d4a716446655440000",  
"order_no":"*********",  
"amount":1250,  
"status":"B",  
"payment_code":1,  
"payment_detail":null,  
"memo":"",  
"expire_time":"2020-09-28  08:15:00",  
"create_time":"2020-09-28  08:00:00",  
"modify_time":"2020-09-28  08:30:00",  
"nonce":"**********",  
"checksum":"d09d5532767453ad4c6ba9b649034187",  
"print_invoice":"0",  
"vehicle_type":"2",  
"vehicle_barcode":"/1234567",  
"donate_invoice":"",  
"love_code":"",  
"invoice_no":"",  
"invoice_date":"",  
"random_number":"",  
"invoice_discount_no":"" }  
ÿÿÿÿÿÿchecksum  ÿÿÿÿÿÿ  
ÿSample  
Machine Translated by Google

4Payer  has  paid
7  The  payment  has  been  made  to  the  contractor  
(account  settlement  is  in  progress)  8  
The  payment  has  been  made  to  the  contractor  
(account  settlement  has  been  completed,  and  the  payment  is  ready)
(The  payment  slip  details  have  been  confirmed,  waiting  for  SMS  or  email  to  
be  sent)  (If  the  contract  customer's  payment  flow  settings  do  not  have  SMS  or  email  notification  functions  set,  this  status  will  be  
automatically  skipped)  3  Waiting  for  the  payer  to  pay
(The  contract  customer  has  requested  payment  on  his  behalf,  and  is  waiting  for  the  contract  customer  
to  confirm)  1  Waiting  for  the  payment  notification  to  be  sent
5  The  payment  slip  has  been  cancelled  (only  the  payment  confirmation  function  can  cancel  the  
payment  slip)  6  The  payment  slip  is  overdue
Value  Description  0  
Payment  order  to  be  confirmed
1.  Order  process  status  list
(This  status  is  triggered  after  the  payee  clicks  the  "Confirm"  button  on  the  page)  (If  "Skip  
Card  Confirmation  Page"  is  enabled,  or  the  acquiring  behavior  is  "Unified  Payment  Flow",  this  status  will  be  automatically  triggered)  
15  Authorization  
completed  16  Authorization  
failed  17  Cancel  authorization  
completed  18  Cancel  authorization  
failed  20  Request  payment  21  Payment  
in  progress  22  Payment  
completed  23  Payment  failed  24  
Request  to  cancel  transaction  
25  Cancel  transaction  (return)  
in  progress  26  Cancel  transaction  
(return)  in  progress  27  Cancel  transaction  (return)  
completed
14Confirmation  by  payee
13  Card  swiping  confirmation  page
(The  payer  has  entered  the  card  swiping  confirmation  page  but  has  not  yet  taken  any  action)
VI.  Attachments
125  
Machine Translated by Google

126  
28  Cancellation  of  transaction  (return)  failed  
29  Cancellation  of  transaction  (return)  application  failed
Machine Translated by Google

127  
Samsung  Pay  (foreign  cards)
Google  Pay  
Six  phases
esun.m9
One-time  payment
Unified  financial  flow  12  installments  in  12  installments
payuni.answer
Three-stage
One-time  payment
esun.m3
payuni.normal
One-time  payment  with  unified  payment  flow
2.  Online  card  payment  parameters
chinatrust.m6
pay.m3  pay.m6  
pay.m9  pay.m12  
pay.m18  
pay.m24  pay.m30  
pay.unionpay  
pay.applepay
Apple  Pay  
Unified  payment  method:  UnionPay  card  
payment  Unified  payment  method:  Apple  Pay
Six  phases
Foreign  Cards
Unified  payment  method:  Samsung  Pay
Unified  payment  flow  Google  Pay
E.Sun  Bank  One-time  
PaymentE.Sun  Bank  3  
PeriodsE.Sun  Bank  6  
PeriodsE.Sun  Bank  9  
PeriodsE.Sun  Bank  12  Periods
chinatrust.normal  
Twelve  installments
Licensed  product  (period_type)
Three-stage
esun.m6
Unified  Financial  Flow  3rd  Period  
Unified  Financial  Flow  6th  Period  
Unified  Financial  Flow  9th  Period  Installment  9th  Period
Unified  payment  for  24  installments  (depending  on  the  number  of  installments  provided  by  the  card  issuer)
Apple  Pay  (foreign  cards)
chinatrust.m12  
normal
Twelve  installments
Six  phases
UnionPay  Card
Samsung  Pay  
One-time  payment
chinatrust.m3  
Nine  phases
China  Trust  One-time  Payment  
China  Trust  3rd  Period  
China  Trust  6th  Period  
China  Trust  9th  Period  
China  Trust  12th  Period
Product  type  (limit_product_id)  description
chinatrust.m9  
Unified  payment  for  30  installments  (depending  on  the  number  of  installments  provided  by  the  card  issuer)
Unified  payment  for  18  installments  (depending  on  the  number  of  installments  provided  by  the  card  issuer)
esun.m12
Three-stage
Nine  phases
Google  Pay  (foreign  cards)
payuni.googlepay
Machine Translated by Google

128  
Bank  
FamilyStore  
SevenStore  
HiLifeStore  
OkStore  
illustrate
Bank  ATM
IbonPay
7-11  
Family
3.  CVS  convenience  bank  payment-APN  active  notification  (pay_route)  parameters
Hi-Life
OK  convenience  store
Numeric
Ibon  Payment
Machine Translated by Google