pdfconvertme_attachment.20ONaO-html.html

System  version:  
1.0  Update  date:  August  8,  2023
WEB  API  Customized  Version  Specification  Document
Unify  Kelede's  multiple  payment  platforms
1  
Machine Translated by Google

2  
2023-08-08  
File  modification  
record  date  version  description
1.0  First  edition
Machine Translated by Google

3.  PostMan  Setting  Example..................................................................................  8  II.  ibon  real-time  
three-segment  code.................................................................................  9  1.  Send  ibon  real-time  three-
segment  code  together  with  the  new  order  of  the  customer........  9  2.  Re-obtain  ibon  real-time  three-
segment  code.................................................  15
Description................................................................................................................................  4  Format  
Description..........................................................................................................................  4  
URL.........................................................................................................................................  4  API  
Specifications..................................................................................................................................  5  1.  Get  Token  
Verification  Code................................................................................  5  1.  PHP  Code  
Example................................................................................................  6  2. .Net  Framework  (C#)  
Code  Example................................................................
7  
content
3  
Machine Translated by Google

Ex:  Authorization:  Bearer  VKJ0qMXkD27J5ZaDu8ygWrFuK…  6.  The  official  environment  uses  TLS1.2  
transmission  encryption  protocol.  Please  confirm  whether  the  program  and  system  support  it.
Official  environment  BaseUrl  
Old:  https://4128888card.com.tw/  (expected  to  be  removed  in  October  2023)  New:  https://
cocs.4128888card.com.tw/
Test  environment  BaseUrl
5.  If  you  want  to  verify  in  advance,  it  is  recommended  to  use  API  testing  tools  (such  as  PostMan)  to  perform  connection  testing  first.
urlencodedÿ  
5.  When  the  API  requires  Token  authentication,  the  Token  should  be  placed  in  the  HTTP  Header  for  transmission  
(note:  there  should  be  a  space  between  Bearer  and  Token).
1.  All  transmitted  data  is  processed  in  JSON  format  and  UTF-8  encoding.  2.  If  the  
Content-Type  is  not  specifically  specified  in  the  specification ,  application/json  is  used .  3.  Use .NET's  built-in  
OAuth  authentication  protocol  for  authorization.  4.  The  program  needs  
to  obtain  a  token.  Note  that  the  Content-Type  must  be  specified  as  application/x-www-form-
This  version  only  provides  customized  functions  for  special  contract  customers  and  is  not  available  to  general  contract  customers.
Notes  1.  The  
official  environment  and  the  test  environment  data  are  not  interoperable.  Please  activate  the  required  account  and  password  
separately.  2.  The  old  official  environment  will  be  removed  from  the  shelves  in  October  2023.  Please  use  the  new  
URL  to  connect.  3.  Tokens  generated  in  the  new  and  old  
environments  cannot  be  shared.  4.  The  URL  for  calling  the  API  is  composed  of  BaseUrl  plus  the  function  name.  For  
example,  when  obtaining  a  token:  The  official  API  URL  is  "https://cocs.4128888card.com.tw/
Token"  The  test  API  URL  is  "http://test.4128888card.com.tw/app/Token"
http://test.4128888card.com.tw/app  
Format  description
illustrate
Website
4  
Machine Translated by Google

1.  Obtain  Token  Verification  Code
Provide  contract  customer  API  account  and  password  verification,  and  generate  a  set  of  time-limited  tokens  for  this  account  after  verification.
Field  Type  Length  Required  Encryption  Description
access_token  string  access  credentials
.issued  
ÿWrong  reply
ÿSample  
account  number
token_type  
string  validity  period  (unit:  "seconds",  default  is  1  day)
Error  
After  the  Token  expires,  the  customer  will  no  longer  be  able  to  use  the  Token  to  access  related  functions  until  re-verification
string  
This  function  obtains  the  username  used  by  toke,  which  is  the  same  as  the  cust_id  used  for  adding  new  orders.
username  string  20  Y  password  string  20  Y  
datetime  creation  time  (GMT)
ÿReply  information
userName  
error_description  string  The  username  or  password  is  incorrect.
Content-Type:  application/x-www-form-urlencoded  
Type  Description
.expires  
Authorize  the  account  to  access  related  functions;  the  token  is  valid  for  24  hours  (the  expiration  time  indicated  by  the .expires  field
string  credential  type
Authorization  method  (fixed  password)
Fields
expires_in  
string  Member  account
Type  Description
The  account  and  password  are  used  to  generate  new  Tokens.
grant_type  string  20  Y  
Please  use  the  same  customer  payment  code  to  obtain  tokens  and  add  new  orders.
API  Password
datetime  Expiration  time  (GMT)
ÿTransmit  data:
invalid_grant  
Fields
API  Specifications
5  
POST /Token  
Machine Translated by Google

{  
“grant_type”:  “password”,  
“username”:  “***********”,  
“password”:  “1q2w”  
}  
"error":  "invalid_grant"  
"error_description":  "The  username  or  password  is  incorrect."
}  
Response  
Success:
{  
1.  PHP  code  examples
"access_token":  "nII7I64yQLVIY8OBeKAn1K…",  "token_type":  
"bearer",  “expires_in":  86399,  
<?php /
**  ÿÿÿÿ  authentication  token  function  **/  function  
get_token($server_url,  $username,  $password)  { $cl  =  
curl_init("$server_url/token");  curl_setopt($cl,  
CURLOPT_SSL_VERIFYPEER,  0);  curl_setopt($cl,  
CURLOPT_SSLVERSION,  6); //TLS  v1.2  curl_setopt($cl,  
CURLOPT_RETURNTRANSFER,  true);  curl_setopt($cl,  
CURLOPT_HTTPHEADER,  "Content-Type:  application/x-www-form-urlencoded");  curl_setopt($cl,  
CURLOPT_POST,  
true);  curl_setopt($cl,  CURLOPT_POSTFIELDS,  
"grant_type=password&username=$username&password=$password");  
"userName":  "***********",  
".issued":  "  
Send  
}  
Tue,  18  Jul  2017  02:53:12  GMT",  
".expires":  "  Wed,  19  Jul  2017  02:53:12  GMT"  
fail:
{  
6  
Machine Translated by Google

7  
} /*  Call  Token,  please  set  the  server_url  according  to  the  official  or  test  
environment*/  $server_url  =  "http://test.4128888card.com.tw/
app";  $token  =  get_token($server_url,  "Account",  "API  Password");
2. .Net  Framework  (C#)  code  examples
string  username  =  "Account";  
string  apiPassword  =  "API  Password";  
string  apiURL  =  "http://test.4128888card.com.tw/app/";
StringContent(string.Format("grant_type=password&username={0}&password={1}",  
HttpUtility.UrlEncode(username),  
HttpUtility.UrlEncode(apiPassword)),  Encoding.UTF8,  
"application/x-www-form-urlencoded")).Result;  
echo  "Failed  to  authenticate\n";  
}  curl_close($cl);  
return  json_decode($auth_response,  true);  
var_dump(curl_getinfo($cl));  
curl_close($cl);  
return  NULL;  
$auth_response  =  curl_exec($cl);  if  
($auth_response  ===  false)  {  
client.BaseAddress  =  new  Uri(apiURL);  
HttpResponseMessage  response  =  client.PostAsync("Token",  
var  client  =  new  HttpClient();  
ServicePointManager.SecurityProtocol  =  SecurityProtocolType.Tls  |  
SecurityProtocolType.Tls11  |  SecurityProtocolType.Tls12;  
new  
string  resultJSON  =  response.Content.ReadAsStringAsync().Result;  
Machine Translated by Google

8  
1.  The  API  URL  to  be  tested  (including  the  complete  BaseUrl  +  function  name)  
2.  It  needs  to  be  specified  as  application/x-www-form-urlencoded  3.  
Please  fill  in  your  financial  service  code  and  API  password
3.  PostMan  configuration  example
Machine Translated by Google

POST /api/Collect  
ÿ  Contract  customers  add  orders  through  the  API  and  send  back  the  ibon  real-time  three-segment  code.
Type  length  required  description
plus  handling  fee)  as  follows
order_amount  
AND
fill)
Payee  Address
50  Y
CvsOrderAppend2)
cmd  
ATM:  30,000  
ÿ  The  ibon  instant  three-segment  code  is  valid  for  10  minutes.
(B2C  invoice  winning  SMS  notification)
payer_postcode  
payer_address  
payer_mobile  
10  And
Payment  Due  Date  (YYYY-MM-
string  
Generate  payment  barcode,  no  need  to  wait  for  SMS  to  send  short  URL.
Contract  customer  order  number  (contract  customer's
cust_id  
cust_order_no  
ÿTransmit  data:
30  Y
The  same  contract  customer  code  must  be
Payment  amount,  upper  limit  (including  foreign
Authorization:  Bearer  [token…]  
1.  Qike  sends  back  the  ibon  real-time  three-segment  code  together  with  the  new  order
Payee  name  (required  if  b2c  is  1)
string  
number  
Payee's  postal  code
ÿ  Only  when  ibon  payment  is  selected  as  the  payment  method,  the  ibon  instant  payment  three-segment  code  will  be  sent  back,  and  the  customer  can  then
Transaction  code  (fixed  entry
Fields
Birds:  20,000
Payee's  mobile  phone
payer_name  
string  
string  240  Y  string  
string  20  Y  
Three-segment  barcode:  20,000
expire_date  
ÿ  The  rest  are  the  same  as  the  original  Qike  new  order  API.
Codename
30  Y
10  And
The  identification  number  specified  in
string  20  Y  string  
Content-Type:  application/json  
DD)  
only)
2.  ibon  instant  three-segment  code
9  
Machine Translated by Google

string  
payment_type  
bill
Only  150  characters  can  be  entered.
string  
1:  CITIC
Is  an  electronic  invoice  issued  for  this  order?
1:  Print
1  
1:  ATM  bank  transfer
Award  Email  Notification)
APN  URL  as  default)
electronic
bill
Notice:  Only  7-11  and  FamilyMart  are  supported.
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
print_invoice  
vehicle_barcode  
0:  Jade  Mountain  (default)
0:  Do  not  print
1  
string  
0:  ibon  payment
apn_url
bill
electronic
1:  Open
electronic
9:  Three-segment  barcode  (CITIC  instant  payment
500  Y
string  
Y  Electronic  donation  invoice  (b2c  is  1  if  required
payer_email  
Single  function,  to  avoid  version  error,  limit
60  Y  Electronic
3:  Natural  person  certificate
Acquiring  Bank
b2c  
1  
donate_invoice  
AND
string  250  
Y  Electronics
1  
Fill  in  the  form  and  use  the
(b2c  electronic  invoice  and  invoice
2:  Three-segment  barcode
string  
product_name  
bill
string  240  Y  
Service  is  enabled  PDF  Comprehensive  payment
Product  Name  (required  if  b2c  is  1)
2:  Mobile  phone  barcode
string  20  
string  2  
payment_acquirerType  string  2  
Whether  to  print  paper  invoice  (b2c  is  1
vehicle_type  
Payee  Email
APN  specifies  the  delivery  URL  (if  no
0:  Do  not  open  (default)
Vehicle  Type
order_detail  
Required)
Carrier  barcode
Payment  Methods
Payment  slip/commodity  details;  if  there  is  cash  flow
bill
1:  Member  Vehicle
Merchant  payment)
string  
10  
Machine Translated by Google

Anyuan  ibon  manufacturer  code
bill
Abnormal  situation  occurs  during  processing
bill
number  
161  Electronics
string  
Convenience  store  barcode  1
Buyer's  Uniform  Number
1.  Donate
electronic
illustrate
Bill  amount
string  
ÿReply  information
ibon  code
Invoice  Title
Payment  Account  Number  (ATM  Transfer  Account  Number)
Type
919)  
buyer_invoice_zip  
number  
1:  CITIC
status  
Convenience  store  barcode  3
bill
buyer_invoice_addr  
Payment  Due  Date  (YYYY-MM-DD)
electronic
string  240  Electronic
Status,  OK  means  successful  processing,  ERROR  means  error
0:  Yushan
8  
When  ibon  payment  is  selected  as  the  payment  method,  the  ibon  instant  payment  three-segment  code  will  directly  use  the  st_barcode1~3  fields.
string  
string  
string  
string  
string  
string  
string  
string  20  
buyer_invoice_title  
string  
string  
The  postal  code  to  which  the  buyer's  invoices  are  sent
0:  Do  not  donate
electronic
5  
Amount  of  payment
No  input,  default  is  Genesis  Foundation
Buyer's  unified  invoice  delivery  address
There  are  only  two  message  states:  OK  and  ERROR
Acquiring  bank  (default  0:  E.Sun)
love_code  
string  
Customer  order  number
number  
Invoice  Fill)
bill
Fields
Convenience  store  barcode  2
string  
cust_order_no  
order_amount  
expire_date  
ibon_code  
ibon_shopid  
virtual_account  
st_barcode1  
st_barcode2  
st_barcode3  
bill_amount  
cs_fee  
cvs_acquirer_type  
Love  code  (select  Donate.  Love  code  is
bill
Response  Status
Convenience  store  fees
buyer_bill_no  
When  using  the  ibon  real-time  three-segment  code,  please  use  the  following  reply  fields  as  the  main  ones.  Unused  fields  will  be  filled  with  NULL  or  0.
11  
Machine Translated by Google

“expire_date”:  “2023-08-31”,  
“b2c”:  “0”  
string  
string  
0:  Do  not  donate
love_code  
1:  Print
Short  URL  information.  If  this  feature  is  not  enabled,  it  will  return
0:  Do  not  print
ÿSample  
Carrier  barcode
“payer_postcode”:  “105”,  
“payer_email”:  “<EMAIL>”,  
Response  
Overwrite  NULL.
Vehicle  Type
1.  Donate
“cmd”:  “CvsOrderAppend2”,  
2:  Mobile  phone  barcode
vehicle_barcode  
donate_invoice  
“cust_order_no”:  “CVSTEST20230808135121501”,  
string  
“payment_acquirerType”:  “0”,  
}  
Print  paper  invoice
3:  Natural  person  certificate
string  
“payer_name”:  “Wang  Daming”,
Do  you  want  to  donate  the  invoice?
Send  
“payer_address”:  “Songshan  District,  Taipei  City”,
2:  Anyuan  ibon
print_invoice  
success:
Information  about  electronic  invoices  (please  ignore  if  the  invoice  function  is  not  enabled)
1:  Member  Vehicle
Love  Code
“cust_id”:  “***********”,  
“payment_type”:  “0”,  
{  
“payer_mobile”:  “**********”,  
string  
string  
“order_amount”:  50,  
“apn_url”:  “”,
short_url  
vehicle_type  
{  
12  
Machine Translated by Google

{  
“status”:  “ERROR”,  “msg”:  
“Add  new  order  failed”
}  
For:  XXXXX
}  
fail:
“cust_order_no”:  “CVSTEST20230808135121501”,  “order_amount”:  50,  
“expire_date”:  “2023-08-31”,  
“ibon_code”:  “************”,  
“ibon_shopid”:  “CCAT”,  “virtual_account”:  “”,  
“st_barcode1”:  “1208085G4”,  
“st_barcode2”:  
“030808C7ZHV0I601”,  “st_barcode3”:  
“***************”,  “bill_amount”:  75,  “cs_fee”:  0,  
“cvs_acquirer_type”:  “2”,  “short_url”:  “https://
collectsc-
uat.azurewebsites.net/Q/1667398”,  
“print_invoice”:  “0”,  “vehicle_type”:  “”,  “vehicle_barcode”:  “”,  “donate_invoice”:  “0”,  
“love_code”:  “”  
“status”:  “OK”  
Project  Project  Name
ÿ  Abnormal  message  list
1  cmd  The  data  is  incorrect  2  
The  data  is  incorrect,  "Amount  to  be  paid"  must  be  a  value  3  
The  data  is  incorrect,  "Payment  due  date"  must  be  a  date  or  time.  The  system  interprets  the  payment  due  date  you  entered
4Information  error,  "Payment  Due  Date"  is  required  5The  
contract  customer  has  not  set  a  response  
URL  6Contract  customer  information  is  
incorrect  7Contract  customer  information  
does  not  exist  8User  password  cannot  exceed  40  characters
13  
Machine Translated by Google

The  payment  due  date  you  entered  is:  XXXXX
9  cust_id  (XXXX)  and  token  (XXXX)  do  not  match  10  User  login  
failed,  user  has  been  deactivated  11  Data  error,  "Contract  
Order  Number"  must  be  filled  in  12  Data  error,  you  have  uploaded  this  
"Contract  Order  Number":  XXXXX,  can  not  upload  again  13  Data  error,  "Contract  Order  Number"  exceeds  the  length  limit  of  
30  characters  14  Data  error,  "Amount  paid  on  behalf  of"  must  be  a  numeric  value  15  
Data  error,  "Amount  paid  on  behalf  of"  cannot  have  decimal  
places  16  Data  error,  "Amount  paid  on  behalf  of"  must  be  less  than  
XXXXX  17  Data  error,  "Amount  paid  on  behalf  of"  must  be  greater  than  
XXXXX  18  Data  error,  "Payment  due  date"  must  be  between  XXXXX  and  
XXXXX  in  the  future.
19Information  error,  "Payer's  Postal  Code"  exceeds  the  length  limit  of  10  characters  
20Information  error,  "Payer's  Postal  Code"  is  required  
21Information  error,  "Payer's  Mobile  Number"  exceeds  the  length  limit  of  30  characters  
22Information  error,  "Payment  method  is  incorrect"  
23Information  error,  "Amount  paid  on  behalf  of"  must  be  greater  than  the  contract  handling  
fee  XXXXX  24Payment  service  
has  been  deactivated  25Payment  
service  has  not  yet  started  
26Payment  service  has  expired  27Payment  due  date  exceeds  the  payment  
service  validity  date  [XXXXX]  
28Payee  's  mobile  number  is  required  29Payment  service  includes  email,  payee's  
email  is  required  30Payment  service  includes  PDF  attachment,  payee's  name  and  
address  are  required  31Add  
order  failed!!  32  Electronic  invoice  function  
has  not  been  enabled  33  Whether  to  print  paper  invoices  is  not  
entered  or  the  format  is  incorrect  34  Whether  to  donate  
invoices  is  not  entered  or  the  format  is  incorrect  35  Only  one  of  donation  invoices,  unified  (company  account)  
invoices,  printed  paper  invoices  or  common  carriers  can  be  selected  36  Please  
enter  donation,  paper  printing  
invoice  or  use  invoice  carrier  37  Please  enter  the  
carrier  type  38  The  data  is  incorrect,  
"Carrier  barcode"  is  required  39  The  carrier  barcode  
format  is  incorrect  40  The  data  is  incorrect,  "Product  
name"  is  required  41  The  data  is  incorrect,  the  buyer's  
unified  number  is  incorrect  42  The  data  is  incorrect,  "Invoice  header"  is  required  43  The  data  is  incorrect,  "Buyer's  unified  invoice  delivery  zip  code"  is  required
14  
Machine Translated by Google

POST /api/Collect  
Calling  Anyuan  generates  ibon  instant  three-segment  code  related  errors
T-ibon  data  exchange  exceptions  e.g.  timeout,  message  format  error,  etc.
string  20  Y  Transaction  code  (fixed  into  IbonRegetBarcode)
Fields
Type
Customer  order  number
string  
string  
2.  Re-obtain  the  ibon  real-time  three-segment  code
string  30  Y  Customer  order  number
illustrate
2  2000-Product  suspension  message
There  are  only  two  message  states:  OK  and  ERROR
4  4000-No  such  internal  product  number  found
cmd  
6  6000-Incorrect  operator  code
ÿReply  information
Content-Type:  application/json  
Type  length  required  description
45Information  error,  "Buyer's  unified  invoice  delivery  address"  is  required
8  F-Failure  ex:  No  such  order  found,  or  the  order  has  been  paid
Abnormal  situation  occurs  during  processing
status  
cust_order_no  
ibon_code  
1  1000-Data  format  error  (e.g.,  the  content  of  the  message  is  incorrect)
9  
string  20  Y  Contractor  code
string  
ibon  code
Fields
Response  Status
3  3000-No  such  code  found
The  ibon  instant  three-segment  barcode  is  valid  for  10  minutes.  If  it  expires,  you  need  to  obtain  a  new  barcode.
5  5000-System  abnormality  (e.g.,  including  file  creation  and  program  errors)
ÿTransmit  data:
cust_id  
cust_order_no  
44  Data  error,  "Buyer's  unified  invoice  mailing  postal  code"  format  is  incorrect
7  7000-This  service  has  been  terminated
Status,  OK  means  successful  processing,  ERROR  means  error
Authorization:  Bearer  [token…]  
15  
Machine Translated by Google

{  
bird_shopid
“status”:  “ERROR”,  “msg”:  
“Error  in  obtaining  the  ibon  real-time  three-segment  code”
}  
ÿ  Abnormal  message  list
st_barcode1  
st_barcode2  
{  
Project  Project  Name
“status”:  “OK”  
string  
string  
string  
1.  Incorrect  cmd  data  2.  
cust_id(XXXX)  and  token(XXXX)  do  not  match  3.  Error  in  obtaining  
the  ibon  real-time  three-segment  code  4.  Failed  to  
obtain  the  ibon  real-time  three-segment  code  5.  
No  order  found
“cmd”:  “IbonRegetBarcode”,  “cust_id”:  
“***********”,  “cust_order_no”:  
“ CVSTEST20230808135121501”  
“cust_order_no”:  “CVSTEST20230808135121501”,  “ibon_code”:  
“************”,  “ibon_shopid”:  “CCAT”,  
“st_barcode1”:  “1208085G4”,  
“st_barcode2”:  “030808C7ZHV0I701”,  
“st_barcode3”:  “981743710000075”  
}  
}  
fail:
Response  
Success:
Anyuan  ibon  Manufacturer  code  Convenience  
store  barcode  1  
Convenience  store  
barcode  2  Convenience  store  barcode  3
ÿSample  
st_barcode3  
string  
Send  
{  
16  
Machine Translated by Google

1  1000-Data  format  error  (e.g.,  incorrect  content  of  the  message)  2  2000-Product  
suspension  message  3  3000-No  such  code  
4  4000-No  such  internal  product  
number  5  5000-System  abnormality  (e.g.,  including  file  
creation  and  program  errors)  6  6000-Incorrect  business  code  7  7000-This  
service  has  been  terminated
T-ibon  data  exchange  exceptions  e.g.  timeout,  message  format  error,  etc.
8  F-Failure  ex:  No  such  order  found,  or  the  order  has  been  paid
6Payment  has  been  made  and  no  
change  is  required  7Order  
has  expired  8Order  is  not  ibon  Payment  
call  Anyuan  generates  ibon  instant  three-segment  code  related  errors
9  
17  
Machine Translated by Google