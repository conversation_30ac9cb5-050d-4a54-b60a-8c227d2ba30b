This file is about card payment:


工程師
您好~

測試帳號已設定完成
京佳企業行        
帳號：************
密碼：@a************

測試環境的網址
https://test.4128888card.com.tw/app
測試區信用卡測試卡號
一次付清：****************，****************
分期付款：****************，****************，****************
銀聯卡：****************
卡片到期日及背面末三碼可任意填入



Card Payment:
engineer
Hello~

The test account has been set up
Jingjia Enterprise Bank
Account number: ************
Password: @a************

URL of the test environment
https://test.4128888card.com.tw/app
Test area credit card test card number
One-time payment: ****************, ****************
Installment: ****************, ****************, ****************
UnionPay card: ****************
The expiration date of the card and the last three digits on the back can be filled in arbitrarily



契客名稱	京佳企業行
線上刷卡資料01檔	
登入網址：	www.ccat.com.tw
登入帳號：	************
平台登入密碼：【第一次登入請修改密碼】	W7529992P$
API密碼：(系統串接用勿更改)	W7529992P$
取消交易&退刷密碼:	W7529992P$
*API - link_id (連線識別)	izmIFZckOoHe
*API - hash_base (連線識別)	l9TH7sAMJsAZSJXg
服務狀態	已開通
	
代收代付(ibon、ATM)&線上刷卡	
API帳號：(CUST_ID)	************
API密碼：	W7529992P$
服務狀態	已開通
	
	
代收代付API設定相關-由系統商設定	
加值服務     ☑主動通知服務(APN)	
APN - 主動通知接收網址	
	
線上刷卡API設定相關-由系統商設定	
API - 重新導向回契客網址(完成)	
API - 重新導向回契客網址(失敗)	
加值服務     ☑主動通知服務(APN)	
APN - 主動通知接收網址	