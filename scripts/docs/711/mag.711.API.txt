This file is about API payment:

契客名稱	京佳企業行
線上刷卡資料01檔	
登入網址：	www.ccat.com.tw
登入帳號：	************
平台登入密碼：【第一次登入請修改密碼】	W7529992P$
API密碼：(系統串接用勿更改)	W7529992P$
取消交易&退刷密碼:	W7529992P$
*API - link_id (連線識別)	izmIFZckOoHe
*API - hash_base (連線識別)	l9TH7sAMJsAZSJXg
服務狀態	已開通
	
代收代付(ibon、ATM)&線上刷卡	
API帳號：(CUST_ID)	************
API密碼：	W7529992P$
服務狀態	已開通
	
	
代收代付API設定相關-由系統商設定	
加值服務     ☑主動通知服務(APN)	
APN - 主動通知接收網址	
	
線上刷卡API設定相關-由系統商設定	
API - 重新導向回契客網址(完成)	
API - 重新導向回契客網址(失敗)	
加值服務     ☑主動通知服務(APN)	
APN - 主動通知接收網址	


Client Name: Jingjia Enterprise Co., Ltd.
Online credit card information file 01
Login URL: www.ccat.com.tw
Login account: ************
Platform login password: [Please change the password for the first login] W7529992P$
API password: (Do not change for system serial connection) W7529992P$
Cancel transaction & refund password: W7529992P$
*API - link_id (link identification) izmIFZckOoHe
*API - hash_base (connection identification) l9TH7sAMJsAZSJXg
Service Status: Activated

Payment and collection (ibon, ATM) & online card payment
API account: (CUST_ID) ************
API password: W7529992P$
Service Status: Activated


Payment and collection API settings - set by the system provider
Value-added services ☑Active Notification Service (APN)
APN - Active Notification Receiving URL

Online card swiping API settings - set by the system provider
API - Redirect back to the Qike website (completed)
API - Redirect back to the customer URL (failed)
Value-added services ☑Active Notification Service (APN)
APN - Active Notification Receiving URL

