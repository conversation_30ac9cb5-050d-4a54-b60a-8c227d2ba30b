EC  number  assignment  document  
(mobile  phone  barcode)
Code  payment


Modifications
Version  modification  date
2019.06.14  Corrected  the  description  of  the  transaction  cancellation  field  on  the  website
10  mins
2023.02.18  Adjustment  of  the  time  for  providing  and  querying  the  account  cancellation  files
V3.02
2014.01.29  Corrected  the  EC  payment  slip  printing  diagram  and  the  deadline  for  shops  to  print  payment  slips  to
2022.03.02  Adjust  service  website
V3.04
V1.03
2016.06.15  Added  FamiWeb
V2.00
2014.04.10  Case  closed  dot  net  instructions  Added  farmework  4.0  and  above  settings  instructions
Document  revision  record
2014.06.19  Fixed  the  problem  that  the  TERMINO  field  in  the  telegram  cannot  be  a  unique  value
V1.02
V3.01
V1.01
Correct  the  part  that  has  been  adjusted
2019.05.30  Added  application  document  list  and  system  flow  description,  and  revised  the  specification
V1.00
2011.07.01  First  edition
2020.04.06  Added  error  code  3013  "The  order  validity  period  or  time  format  is  incorrect"
V3.00
V3.03


Notes..................................................................................................  26
6  7  Web  Service  Program  Example.......................................................  7
13  
Transport  Communication  Mechanisms..................................................  6
12  
Document  List..................................................................................  1
9  
10  Mobile  APP  Obtaining  a  three-segment  barcode..................................................  21
XML  Document  Schema .................................................................  10  
8  EC  Payment  Slip  Printing  (PC  Version)..................................................................  17
3  Functional  Description..................................................................................  4
Daily  Account  Settlement..................................................................................  24
11  XML  Error  Codes..................................................................  23
2  EC  Number  Assignment  System  Process..................................................................  2
5  Job  Processing  Instructions..................................................................  4
1  
4  Operation  timing: .................................................................................  4
Table  of  contents


1.  Document  List
1 /  26  
Code  Payment  Manufacturer  Instructions
*  Not  required.  If  the  manufacturer  has  not  set  up  a  file  host  and  wants  to  obtain  the  cancellation  file  from  the  entire  network,  this  is  the  application  form  for  opening  an  IP.
ÿ  HKK1-********.TXT  
ÿ  NI04-0006-03_SFTP  Account  Application  Form_V1.2.doc
ÿ  06.Window  information  and  basic  information  of  manufacturers  (version  150330).xls  *  Manufacturers  
fill  in  the  information  to  facilitate  the  setting  of  information  units  for  the  entire  network
*  Write-off  file  example  file


EC  Platform
Manufacturer
FamilyMart
EC  numbering  system  process
Famiport  
The  consumer  enters  the  PINCODE  and  
sends  it  to  the
START  
Printing  a  white  sheet
Write-off  information
Receive  the  message  and  convert  the  shopping  
payment  information  into  XML  message  
for  return
Write-off  file
POS  Checkout
Receive  message  generation
Convert  case  closing  information  into  XML  
and  send  to  the  manufacturer
Carry  out  comparison  work
Consumer  Shopping
Transaction  data  is  converted  into  XML  messages  
and  sent  to  the  EC  platform
Upload  the  successfully  entered  account  to  
the  manufacturer's  FTP
Receiving  telegram  note  cancellation
Return  PINCODE
PINCODE  is  converted  into  XML  message  and  
sent  to  the  manufacturer
Receive  telegrams  to  close  the  case  and  
convert  the  case  closing  results  into  
XML  for  return
EC  platform  query  order
Store  checkout  details  output
2  EC  number  allocation  system  process
2 /  26  
Code  Payment  Manufacturer  Instructions
POS  
Consumers  
Daily  
END  


EC  platform  query  order
Consumer  Shopping
POS  Checkout
Write-off  file
START  
Carry  out  comparison  work
Upload  the  successfully  entered  account  to  
the  manufacturer's  FTP
Receive  telegrams  to  close  the  case  and  
convert  the  case  closing  results  into  
XML  for  return
PINCODE  is  converted  into  XML  message  and  
sent  to  the  manufacturer
Receiving  telegram  note  cancellation
END  
Transaction  data  is  converted  into  XML  messages  
and  sent  to  the  EC  platform
Receive  message  generation
Convert  case  closing  information  into  XML  
and  send  to  the  manufacturer
Store  checkout  details  output
The  consumer  enters  the  PINCODE  and  
sends  it  to  the
Return  PINCODE
Receive  the  message  and  convert  the  shopping  
payment  information  into  XML  message  
for  return
Write-off  information
Daily  
Consumers  
POS  
FamilyMart
EC  Platform
Mobile  APP  display
Get  the  three-segment  barcode  via  mobile  APP
Manufacturer
3 /  26  
Code  Payment  Manufacturer  Instructions


4  Operation  time:
3.  Functional  Description
5Job  Processing  Instructions
4 /  26  
Code  Payment  Manufacturer  Instructions
The  following  processes  are  
included:  ÿ  Obtain  PIN  CODE  ÿ  
Obtain  three-segment  barcode  via  mobile  APP  ÿ  POS  
payment  and  cancellation  ÿ  
Cancel  order
Below  is  the  correspondence  between  the  process  and  the  Web  Service
---------------------------------------------------------------------  
project
---------------------------------------------------------------------  
ÿ  POS  payment  cancellation:  Consumers  take  the  receipt  or  mobile  phone  barcode  to  the  store  POS  to  cancel  the  payment  ÿ  
Cancel  the  order:  After  the  consumer  cancels  the  order  on  the  merchant's  website,  the  EC  platform  will  send  a  message  to  cancel  the  order
ÿ  Obtain  PIN  CODE:  After  the  consumer  places  a  shopping  order  on  the  business  website,  a  PIN  CODE  is  generated.  ÿ  Obtain  
three-segment  barcode  via  mobile  APP:  After  the  business  obtains  the  PIN  CODE  through  the  website,  the  three-segment  barcode  sent  back  by  this  service  can  
be  converted  to  a  barcode  format  on  the  mobile  phone  (please  refer  to  the  relevant  instructions  in  item  10  of  the  specification)
ÿ  EC  code  payment  system  operator  specification  ÿ  The  
entire  process  is  divided  into:  website  number  issuance,  POS  payment  cancellation,  website  order  cancellation  and  mobile  barcode  generation.
EC  provides  Web  Service  WEB  vendor  provides  Web  Service  WebEC.NewOrder()
Mobile  APP  obtains  the  three-segment  barcode  FamiWeb.QueryBarcode()
Website  number
Waiting  for  the  WEB  manufacturer  to  provide  the  website  for  closing  the  case
WebEC.CancelOrder()  
POS  payment  cancellation  -  cancellation  
website  order  cancellation


Process  Description
STEP  (6)  Confirm  payment  information  STEP  (7)  Print  payment  slip  STEP  (8)  Obtain  payment  slip
STEP  (3)  Payment  instructions  STEP  (4)  Enter  PIN  CODE
OK
ÿEC  Platformÿ
Code  Payment  Manufacturer  Instructions
Click  "Payment"
5 /  26  
STEP  (2)  Submit  payment  information  immediately
Notify  the  manufacturer  to  process
ÿEC  Platformÿ
ÿSHOPÿ  
ÿWEB_SERVERÿ  
Consumers  get  customer  connection
ÿWEB_SERVERÿ  
ÿWEB_SERVERÿ  
After  payment,  payment  information  will  be
ÿWEB_SERVERÿ  
STEP(5)  Confirm  the  entered  PIN
Through  EC  platform  and  manufacturer  end
ÿTMÿ  
ÿFamilyMart  Headquartersÿ
Account  cancellation  operation
STEP(1)SHOP  Home  Page  –
CODE  
ÿManufacturerÿ
STEP(2)  Function  
homepage  -  click  "Payment  code"
ÿManufacturerÿ
ÿWEB_SERVERÿ  
ÿTMÿ  
STEP  (3)  Account  cancellation  operation,
ÿSHOPÿ  
ÿWEB_SERVERÿ  
STEP  (1)  Send  the  order  to  TM
ÿ  Waste  disposal  work
ÿ  Check  the  printed  order  on  the  Famiport  operation  page
ÿ  Accounting  work


ÿ  When  manufacturers  receive  enquiry  messages  or  case  cancellation  messages,  they  must  respond  immediately.
ÿ  Transmission  method  &  communication  interface:
ÿ  Security  protocol:  TLS  1.2
ÿ  Connection  method:  You  can  connect  to  the  EC  code  platform  via  the  Internet  or  a  
dedicated  line.  ÿ  Information  
security  mechanism:  ÿ  Security  certificate:  SHA256
HTTPS/SFTP  TXT  
Payment  instant  case  settlement  instant  message  Form  POST  XML  URL  provided  by  your  company
Batch  write-off  batch
FTP/FTPS/  
EC  Instant  message  to  number  Form  POST  XML  URL  provided  by  the  whole  network
Cancel  order  instant  message  Form  POST  XML  URL  provided  by  the  whole  network
illustrate
Project  transmission  method  communication  interface  format
SFTP  
TXT  
6Transmission  Communication  Mechanism
The  entire  network  is  set  up  with  automatic  scheduling  to  cancel  the  account  data  via  FTP,
FTPS  or  SFTP  transfer  protocol  (choose  one  of  the  three)  to  connect  to  the  factory
Files  provided  by  the  host
File  (*.TXT)
Scheduling:  Schedule  the  export  of  account  closing  files,  and  provide  manufacturers  
with  access  to  the  entire  network  via  SFTP  transmission  protocol
Manual:  The  manufacturer  manually  downloads  the  text  from  the  EC  management  platform
Code  Payment  Manufacturer  Instructions
6 /  26  


7  Web  Service  Program  Examples
m_in.AP.ORDER_NO  =  "TEST********1456"  'Customer  order  number
'Edge  version
'Note  1
Code  Payment  Manufacturer  Instructions
'Manufacturer  code  +  collection  code
'Total  amount  of  goods,  if  less  than  the  amount,  add  0
'Note  3
'Note  4
Payment  Type
ÿ  VB.NET  Examples
'Message  transmission  date  (YYYYMMDD)
"  
m_in.AP  =  New  W  ebReference.TX_W  EB_AP  
'Status  code
'Payment  vendor
m_in.HEADER.XML_VER  =  "05.01"  
m_in.HEADER.XML_FROM  =  "********"  m_in.HEADER.TERMINO  
=  "ABC1BC1"  m_in.HEADER.XML_TO  =  "99027"  
m_in.HEADER.BUSINESS  =  "B000001"  
m_in.HEADER.XML_DATE  =  "********"  
m_in.HEADER.XML_TIME  =  "145600"  m_in.HEADER.STATCODE  
=  "0000"  m_in.HEADER.STATDESC  =  
'Status  Description
'Function
7 /  26  
'Note  2
'Manufacturer  unified  compilation
m_in.AP.ACCOUNT  =  "00005"  
m_in.AP.END_DATE  =  "********"  m_in.AP.END_TIME  
=  "235959"  m_in.AP.PAY_TYPE  =  "FamiPay"  
m_in.AP.PRD_DESC  =  "TEST"  m_in.AP.PAY_COMP  
=  "ÿ  ÿ  m_in.AP.TRADE_TYPE  =  "1"  
m_in.AP.DESC1  =  "TEST1"  m_in.AP.DESC2  
=  "TEST2"  m_in.AP.DESC3  =  "TEST3"  
m_in.AP.DESC4  =  "TEST4"  
'destination
'Payment  deadline  (YYYYMMDD)
By  business
'Payment  deadline  (HH24MMSS)
""  
Dim  m_ws  As  New  W  ebReference.W  ebECSoapClient  Dim  m_in  As  New  W  
ebReference.TX_W  EB  m_in.HEADER  =  New  W  
ebReference.TX_HEADER  
Dim  a  As  String  =  m_ws.NewOrder(m_in,  "Incoming  parameter  2",  "Incoming  parameter  3")
'Message  transmission  time  (HH24MMSS)
'Product  Description
7.1  Example  of  calling  EC  code  platform  Web  Service  from  the  manufacturer


This  sample  program  is  provided  for  manufacturers  to  develop  a  sales  project  webpage  program  for  the  EC  platform  to  transmit  sales  project  data.
7.2  Example  of  EC  code  platform  calling  manufacturer  end  sales  case  (Form  Post)
Inherits  System.W  eb.UI.Page  
ResponseEncoding="UTF-8"  is  the  set  encoding  principle)
'Get  the  case  cancellation  message  sent  
from  the  entire  network  Dim  xml  As  String  =  Request.Form("d")
'---------------------------  
(ValidateRequest="false"  will  not  cause  an  error  when  receiving  XML;
Handles  Me.Load  
Dim  result  As  String  =  "0000"  
Response.Clear()  
'Your  company's  case  handling  logic
Or  you  can  customize  the  validation  type  (replace  the  default  RequestValidator),  please  refer  to  the  instructions  and  examples  on  
madn  to  modify  the  URL:  http://msdn.microsoft.com/zh-tw/library/system.web.util.requestvalidator.aspx
'~  
8 /  26  
(2)  For .NET  Framework  4.0  and  above,  in  addition  to  the  page  property  settings,  add  the  following  settings  to  web.config:
<configuration>  
<system.web>  
<compilation  debug="false"  targetFramework="4.0" />  <!--Return  validation  to  2.0  
mode-->  <httpRuntime  
requestValidationMode="2.0"/>  <!--Disable  request  validation-->  
<pages  validateRequest="false" /
>  </system.web>  </configuration>
Code  Payment  Manufacturer  Instructions
'---------------------------  
'~  
ÿ  VB.NET  Examples
Partial  Class  CloseCase  
(3)  The  manufacturer  receives  the  webpage  code:
'~  
Please  set  ValidateRequest="false"  ResponseEncoding="UTF-8"  in  Page  properties
Protected  Sub  Page_Load(ByVal  sender  As  Object,  ByVal  e  As  System.EventArgs)  
'Processing  result  is  successfully  returned  0000/failed  return  =  4001
(1)  Assume  that  the  receiving  web  page  is  CloseCase.aspx  (as  shown  in  the  figure  below),


Response.ContentType  =  "text/plain"  
Response.End()  
Response.ContentEncoding  =  Encoding.UTF8  Response.Write(result)  
Code  Payment  Manufacturer  Instructions
End  Sub  
End  Class  
9 /  26  


R/O :  1:  Must  lose
0:  Not  necessarily  lost
ÿ  MethodÿNewOrder  
8.1  EC  numbering  (website  to  EC  code  platform)
,  
PIN  CODE  
Related  column  information  such  as:  Website  to  EC  Code  Platform
ÿ  ÿ  XML_TO  destination  10
ÿ  END_DATE  
6  
String  1  
TX_WEB  
String  1  Payment  type
PIN  CODE  (order)  validity  period  YYYYMMDD
WebReference.TX_WEB  returns  an  XML  message,  the  format  is  as  follows:  EC  code  platform  to  website
ÿ  ÿ  STATDESC  Status  description  60
String  1  Manufacturer  code  (to  be  provided  later)
(The  final  payment  deadline  for  this  online  operator  order)
ÿ  ÿ  XML_VER  Telegram  version  5
Incoming  parameter  1  WebReference.TX_WEB
String  1  Record  the  time  when  the  message  is  sent  (HH24MMSS)
ÿ  ORDER_NO  Customer  order  number  32  ÿ  ACCOUNT  
Payment  amount  5
Item  Description  Length  Type  R/O
8  
ÿ  ÿ  TERMINO  Terminal  number  26
Validity  period
structure  
ÿTest  area  website:  https://ect.familynet.com.tw/pin/webec.asmx
Other  channels  (to  be  provided  later)
String  1  The  total  amount  of  the  product  (excluding  handling  fees).  If  it  is  less  than  the  amount,  add  0  on  the  left.
String  1  
Parameter  2  ACCOUNT_NO  Parameter  3  
PASSWORD  Return
ÿ  ÿ  STATCODE  Status  code  4
String  1  
PIN  CODE  (order)  valid  time  HH24MMSS
ÿ  HEADER  
project
String  
String  1  Record  the  date  when  the  message  was  sent  (YYYYMMDD)
Code  Payment  Manufacturer  Instructions
10  
Description  of  the  business  status  that  occurs  when  data  is  transmitted
Validity  period
Remark
String  1  Customer  order  number  (non-duplicate)
If  the  channel  is  FamilyMart,  please  dial  99027
10  
Fixing  belt  05.01
Password,  manufacturer  password  (to  be  provided  later)
(0000  means  success)
String  1  
Fields
1  
It  must  be  less  than  the  code  validity  period  set  by  the  system.
String  1  Fixed  belt  B000001
Collection  code  (3  digits)  (to  be  provided  later)
ÿ  END_TIME  
String  1  
Incoming  XML  message
(Default  is  blank)
String  1  
ÿ  PAY_TYPE  Payment  type  18
(Version)  is  used  when  the  business  or  content  changes
Account  number,  manufacturer  ID  (to  be  provided  later)
10  
Status  codes  that  occur  during  data  transmission
ÿ  ÿ  BUSINESS  Business  type  ÿ  ÿ  
XML_DATE  Date  ÿ  ÿ  XML_TIME  Time
1  
(The  final  payment  deadline  for  this  online  operator  order)
10 /  26  
Manufacturer  Code  (4  digits)+
PIN  CODE  
ÿ  AP  
illustrate
7  
ÿ  ÿ  XML_FROM  source
ÿOfficial  website:  https://ec.fami.life/pin/webec.asmx
8  XML  Document  Schema  


20  
String  1  (1:  Give  number
Order  Number
Payment  vendor,  maximum  10  Chinese  characters
Code  Payment  Manufacturer  Instructions
String  0  Remark  2  (maximum  10  Chinese  characters)
Customer  order  number  (non-duplicate)
20  
Product  Name
PRD_DESC
Product  description,  maximum  20  Chinese  characters
Payment  Manufacturer
structure  
3:  Query)
ACCOUNT  
Automatically  brought  out  by  the  whole  network  system
String  1  Product  description  (up  to  20  Chinese  characters)
,  
Note  1~Note  4
Payment  Type
String  1  Payment  vendor  (maximum  10  Chinese  characters)
20  
PAY_TYPE  
Notes
11 /  26  
ORDER_NO  
String  0  Remark  1  (maximum  10  Chinese  characters)
20  
Payment  Type
String  0  Remark  3  (maximum  10  Chinese  characters)
Item  Description  Length  Type  R/O
String  0  Remark  4  (maximum  10  Chinese  characters)
Remark
Payment  amount
Total  amount  of  goods  (excluding  handling  fee)
ÿ  PRD_DESC  Product  description  40  ÿ  PAY_COMP  
Payment  vendor  20  ÿ  TRADE_TYPE  Function  ÿ  DESC1  
Note  1  ÿ  DESC2  Note  2  ÿ  DESC3  
Note  3  ÿ  DESC4  Note  4
Fees
1  
PAY_COMP  
DESC1~  DESC4
Page  field  name  Telegram  corresponding  field
ÿ  Famiport  operation  page  -  confirm  that  the  payment  amount  and  project  content  correspond  to  the  telegram  fields
illustrate


String  0  Remark  1  (maximum  10  Chinese  characters)
(Version)  is  used  when  the  business  or  content  changes
6  
String  0  Remark  3  (maximum  10  Chinese  characters)
PIN  CODE  (order)  validity  period  YYYYMMDD
(This  is  the  error  message  description)
String  1  PIN_CODE  (payment  receipt  number)
ÿ  ÿ  XML_FROM  source
20  
String  1  Product  description  (up  to  20  Chinese  characters)
String  1  Manufacturer  code  (4  digits)  +  Collection  code  (3  digits)
PIN  CODE  
Description  of  the  business  status  that  occurs  when  data  is  transmitted
ÿ  END_TIME  
ÿ  ÿ  TERMINO  Terminal  number  26  ÿ  ÿ  XML_TO  Destination  10  
ÿ  ÿ  BUSINESS  Business  type  ÿ  ÿ  XML_DATE  Date  ÿ  ÿ  
XML_TIME  Time
TX_WEB  ÿ  
HEADER  
ÿ  ACCOUNT  Payment  amount  6
String  1  
ÿ  DESC  
String  1  Record  the  time  when  the  message  is  sent  (HH24MMSS)
String  1  
8  
String  0  Remark  2  (maximum  10  Chinese  characters)
If  status  is  F,  this  will  be  used  to  set  the  error  description.
ÿ  ÿ  XML_VER  Telegram  version  5
ÿ  PIN_CODE  PIN_CODE  14  ÿ  ORDER_NO  Customer  
order  number  32
20  
String  1  Payment  type
Other  channels  (to  be  provided  later)
Validity  period
For  error  codes,  please  refer  to  XML  Error  Codes
12 /  26  
F:  Processing  failed  (error)
String  1  
structure  
3:  Query)
Left  Compensation  0
ÿ  STATUS  Processing  status  2
Code  Payment  Manufacturer  Instructions
ÿ  AP  
String  1  Record  the  date  when  the  message  was  sent  (YYYYMMDD)
String  1  
ÿ  PAY_TYPE  Payment  type  18  ÿ  PRD_DESC  Product  
description  40  ÿ  PAY_COMP  Payment  manufacturer  20  ÿ  
TRADE_TYPE  Function  ÿ  DESC1  Note  1  ÿ  DESC2  Note  2  
ÿ  DESC3  Note  3  ÿ  DESC4  Note  4
Status  codes  that  occur  during  data  transmission
(The  final  payment  deadline  for  this  online  operator  order)
(Default  is  blank)
,  
(0000  means  success)
If  the  channel  is  FamilyMart,  please  dial  99027
PIN  CODE  
S:  Processing  successful  (normal)
String  1  
Remark
10  
(1:  Give  number
Total  amount  of  goods  (excluding  handling  fees),
20  
ÿ  ÿ  STATDESC  Status  description  60
String  1  Fixed  belt  B000001
10  
10  
1  
PIN  CODE  (order)  valid  time  HH24MMSS
String  
1  
ÿ  ÿ  STATCODE  Status  code  4
Fixing  belt  05.02
String  0  Remark  4  (maximum  10  Chinese  characters)
(The  final  payment  deadline  for  this  online  operator  order)
Processing  Description  20
Item  Description  Length  Document  R/O
String  1  Customer  order  number  (non-duplicate)
String  1  
String  1  Payment  vendor  (maximum  10  Chinese  characters)
20  
String  1  Manufacturer  code
String  1  
String  1  
Validity  period
1  
ÿ  END_DATE  
7  
String  1  
R/O :  1:  Must  lose
,  
0:  Not  necessarily  lost
8.2  EC  numbering  (EC  code  platform  to  website)


String  1  PIN_CODE  (payment  receipt  number)
Transaction  number  (10  digits),  18  digits  in  total
String  1  The  second  barcode
ÿ  TO  ÿ  
BUSINESS  ÿ  DATE  ÿ  TIME
d  
5  
ÿ  
String  
1  
6  
String  1  Record  the  time  when  the  message  is  sent  (HH24MMSS)
String  1  
If  the  channel  is  FamilyMart,  please  dial  99027
String  1  
String  1  The  total  amount  of  the  product  (excluding  handling  fees).  If  it  is  less  than  the  amount,  add  0  on  the  left.
ÿ  SEE
ÿ  
ÿ  
ÿ  
ÿ  
Store  number  (6  digits)  +  Machine  number  (2  digits)  +
String  1  The  first  barcode
ÿ  FROM  
Fields
(Version)  
String  1  
OLTP  
8  
String  1  Record  the  date  when  the  message  was  sent  (YYYYMMDD)
S  (Normal)
Fixing  belt  05.05
Passing  in  parameters
String  1  Customer  order  number  (non-duplicate)
10  
Code  Payment  Manufacturer  Instructions
Description  of  the  business  status  that  occurs  when  data  is  transmitted
ÿ  
ÿ  
Processing  Description  20
ÿ  
ÿ  AP  
Telegram  version
13 /  26  
String  1  Fixed  belt  B000001
structure  
7  
String  1  Store  name
Incoming  XML  message,  the  format  is  as  follows  (H1:  EC  code  platform  to  website)
Used  when  business  or  content  changes
String  1  KK1~KKZ  
Source
(0000  means  success)
ÿ  HEADER  
15  
ÿ  
Item  Description  Length  Type  R/O
ÿ  STATDESC  Status  description  60
(Default  is  blank)
String  0  default  blank
String  1  Manufacturer  code
Remark
String  1  The  third  barcode
Destination  10
illustrate
String  1  
ÿ  TERMINO  Terminal  number  26
ÿ  OL_OI_NO  Collection  code  3  ÿ  ORDER_NO  Customer  
order  number  32  ÿ  ACCOUNT  Payment  amount  6  ÿ  PIN_CODE  
PIN_CODE  ÿ  OL_Code_1  First  barcode  9  ÿ  OL_Code_2  
Second  barcode  16  ÿ  OL_Code_3  Third  barcode  
15  ÿ  STORE_DESC  Store  name  ÿ  STATUS  Processing  
status  2  ÿ  DESC
1  
Status  codes  that  occur  during  data  transmission
14  
ÿ  STATCODE  Status  code  4
String  1  
Other  channels  (to  be  provided  later)
R/O :  1:  Must  lose
0:  Not  necessarily  lost
EC  to  the  vendor  (FORM  POST):  http://vendorIP/xxx.xxx  (provided  by  the  vendor,  allowing  EC  to  close  the  case)
8.3  EC  Cancellation  (H1:  EC  Code  Platform  to  Website)
,  


8.4  POS  payment  cancellation  (H2:  website  to  EC  code  platform)  The  cancellation  URL  
provided  by  the  business  allows  the  EC  code  platform  to  send  the  pyramid  scheme  case  information  to  the  business.  After  the  business  completes  the  
processing,  it  will  directly  return  the  string  using  HttpResponse
PS.  If  the  case  closing  message  received  by  the  operator  is  garbled,  it  is  caused  by  different  encoding  methods.  
After  receiving  the  data,  please  convert  UTF-8  to  the  encoding  of  the  provider  (ex:  big5)  before  processing.
ÿÿÿÿÿ  Sample  ÿÿ  <?xml  version="1.0"  
encoding="UTF-8"?>  <OLTP>  <HEADER>  
<VER>05.05</
VER>  <FROM>99027</
FROM>  
<TERMINO>12345601********90</TERMINO>  <TO>ÿÿÿÿ</
TO>  <BUSINESS>B000001</
BUSINESS>  <DATE>********</DATE>  
<TIME>080858</TIME>  
<STATCODE>0000</
STATCODE>  <STATDESC />  </HEADER>  
<AP>  
<OL_OI_NO>KK1</
OL_OI_NO>  <PIN_CODE>********901234</
PIN_CODE>  <ORDER_NO>********</ORDER_NO>  
<ACCOUNT>02000</ACCOUNT>  <OL_Code_1>********9</OL_Code_1>  
<OL_Code_2>********90123456</OL_Code_2>  
<OL_Code_3>********9012345</OL_Code_3>  <STORE_DESC>
ÿÿÿ</STORE_DESC>  <STATUS>S</STATUS>  <DESC />  </
AP>  </OLTP>  
PS.  When  the  operator  sends  a  non-successful  code,  it  will  try  again  every  30  minutes,  for  a  total  of  six  times  (including  the  first  transmission).
Feedback
String  
eg  
0000  Case  closed  
successfully  4001  No  data  found  (case  closed)
14 /  26  
Fields
The  return  string  represents  success  or  failure.  
The  first  four  digits  of  the  string  are  codes,  the  fifth  digit  is  blank,  and  the  sixth  digit  and  later  are  
system-defined  codes.  "0000"  represents  success,  and  "4001"  represents  failure.
illustrate
Code  Payment  Manufacturer  Instructions


ÿ  MethodÿCancelOrder  
R/O :  1:  Must  lose
ÿOfficial  website:  https://ec.fami.life/pin/webec.asmx
8.5  Website  Cancellation  of  Transaction  (E1:  Website  to  EC  Code  Platform)
0:  Not  necessarily  lost
,  
WebReference.TX_WEB  returns  an  XML  message  in  the  following  format  (E2:  EC  code  platform  to  website)
If  the  channel  is  FamilyMart,  please  dial  99027
ÿ  XML_TIME  time
Total  amount  of  goods  (excluding  handling  fees),
(Version)  is  used  when  the  business  or  content  changes
ÿ  
ÿ  
ÿ  
ÿ  STATDESC  Status  description  60
Item  Description  Length  Document  R/O
String  1  Fixed  belt  B000001
6  
String  1  
WebReference.TX_WEB  passes  in  XML  messages,  and  the  relevant  field  information  is  as  follows  (E1:  website  to  EC  code  platform)
ÿ  XML_VER  Telegram  version  5
ÿ  
PIN_CODE  14  ÿ  ORDER_NO  
Customer  order  number  32
TX_WEB  ÿ  
HEADER  
String  1  
ÿ  XML_DATE  date
String  1  Customer  order  number  (non-duplicate)
1  
String  1  
ÿ  
String  1  Manufacturer  code
String  1  
ÿ  TRADE_TYPE  function
Pass  in  parameter  
1  Pass  in  parameter  
2  Pass  in  parameter  
3  Return
ÿ  
ÿ  
ÿ  PIN_CODE  
Code  Payment  Manufacturer  Instructions
structure  
String  1  Record  the  time  when  the  message  is  sent  (HH24MMSS)
String  1  
15 /  26  
ÿTest  area  website:  https://ect.familynet.com.tw/pin/webec.asmx
1  
10  
(Default  is  blank)
String  1  Manufacturer  code  (4  digits)  +  Collection  code  (3  digits)
8  
ÿ  ACCOUNT  Payment  amount  5
illustrate
ÿ  
7  
ÿ  AP  
Remark
(YYYYMMDD)
For  error  codes,  please  refer  to  XML  Error  Codes
1  
password  password,  manufacturer  password  (to  be  provided  later)
Status  codes  that  occur  during  data  transmission
Description  of  the  business  status  that  occurs  when  data  is  transmitted
account  
Other  channels  (to  be  provided  later)
ÿ  STATCODE  Status  code  4
Left  Compensation  0
Fields
Fixing  belt  05.03
ÿ  TERMINO  terminal  number  26  ÿ  XML_TO  destination  10  ÿ  
BUSINESS  business  type
String  0  
Record  the  date  the  message  was  sent
(0000  means  success)
String  1  2:  Order  canceled
Account  number,  manufacturer  ID  (to  be  provided  later)
ÿ  XML_FROM  source
ÿ  
String  1  PIN_CODE  (payment  receipt  number)


R/O :  1:  Must  enter  0:  Not  required
8.6  Website  Cancellation  of  Transaction  (E2:  EC  Code  Platform  to  Website)
,  
ÿ  ÿ  XML_VER  Telegram  version  5
Status  codes  that  occur  during  data  transmission
PIN  CODE  (order)  validity  period  (default  blank)
20  
Item  Description  Length  Document  R/O
String  1  Manufacturer  code
String  1  PIN_CODE  (payment  receipt  number)
String  1  2:  Order  canceled
String  1  
Description  of  the  business  status  that  occurs  when  data  is  transmitted
PIN  CODE  (order)  validity  period  (default  blank)
If  status  is  F,  this  will  be  used  to  set  the  error  description.
1  
7  
Validity  period
String  0  Note  4  (blank  by  default)
String  1  
6  
ÿ  END_DATE  
1  
String  1  Manufacturer  code  (4  digits)  +  Collection  code  (3  digits)
ÿ  PIN_CODE  PIN_CODE  14  ÿ  ORDER_NO  Customer  
order  number  32  ÿ  ACCOUNT  Payment  amount  6
String  0  Payment  vendor  (blank  by  default)
Fixing  belt  05.04
For  error  codes,  please  refer  to  XML  Error  Codes
ÿ  END_TIME  
20  
TX_WEB  
String  1  Time  when  the  message  is  sent  (HH24MMSS)
PIN  CODE  
String  0  Note  3  (blank  by  default)
Code  Payment  Manufacturer  Instructions
10  
ÿ  ÿ  STATDESC  Status  description  60
Validity  time  ÿ  
PAY_TYPE  Payment  type  18  ÿ  PRD_DESC  Product  
description  40  ÿ  PAY_COMP  Payment  manufacturer  
20  ÿ  TRADE_TYPE  Function  ÿ  DESC1  Note  1  ÿ  DESC2  
Note  2  ÿ  DESC3  Note  3  ÿ  DESC4  
Note  4  ÿ  STATUS  Processing  status  
2
Processing  Description  20
ÿ  ÿ  TERMINO  Terminal  number  26  ÿ  ÿ  XML_TO  Destination  
10  ÿ  ÿ  BUSINESS  Business  type  ÿ  ÿ  XML_DATE  Date  ÿ  ÿ  
XML_TIME  Time
String  
String  0  Product  description  (blank  by  default)
16 /  26  
(Version)  is  used  when  the  business  or  content  changes
(0000  means  success)
10  
20  
structure  
String  1  The  date  the  message  was  sent  (YYYYMMDD)
String  1  The  total  amount  of  the  product  (excluding  handling  fees).  If  it  is  less  than  the  amount,  add  0  on  the  left.
String  0  Note  2  (blank  by  default)
Other  channels  (to  be  provided  later)
String  1  
String  0  
ÿ  DESC  
ÿ  HEADER  
ÿ  AP  
String  0  Payment  type  (blank  by  default)
String  0  
ÿ  ÿ  XML_FROM  source
ÿ  ÿ  STATCODE  Status  code  4
String  0  
20  
Remark
String  1  Fixed  belt  B000001
String  1  Customer  order  number
String  0  Note  1  (blank  by  default)
The  access  number  for  FamilyMart  is  99027
(Default  is  blank)
10  
(This  is  the  error  message  description)
1  
8  
PIN  CODE  
String  1  S:  Processing  success  (normal);  F:  Processing  failure  (error)


The  PINCODE  preview  screen  will  be  displayed  in  Figure  1.  When  the  customer  
presses  the  [Show  Bill]  button  in  Figure  1,  the  PINCODE  preview  screen  will  be  displayed  in  Figure  2.  When  the  customer  clicks  the  [Next  Print]  button  in  
Figure  1,  the  window  will  be  closed.
After  the  business  successfully  assigns  the  number  through  the  website,  the  business  can  use  the  following  URL  to  send  the  order  and  PINCODE  information  to  the  EC  
code  payment  system  through  the  FORM  POST  method.  The  EC  code  payment  system  will  display  the  PINCODE  preview  screen  and  the  payment  slip.
When  the  customer  presses  the  [Print]  button  in  Figure  2,  the  payment  slip  in  Figure  2  will  be  printed  out  on  the  customer's  printer.  
Customers  can  pay  at  FamilyMart  with  the  payment  slip.  Alternatively,  customers  
can  record  their  PINCODE  and  print  out  a  payment  slip  (as  shown  in  Figure  3)  at  the  FamiPort  machine  to  pay.  After  payment,  customers  will  receive  a  payment  receipt  
(as  shown  in  Figure  4).
<form  action="https://xxx.com/xxx"  method="POST"  id="form1"  name="form1"  >  <input  type="hidden"  
name="VD_ACCOUNT"  value=""> ...Account,  same  as  WebService  parameter  2  <input  type="hidden"  name="VD_ORDERNO"  
value=""> ...This  is  the  order  information  <input  type="hidden"  name="VD_PINCODE"  
value=""> ...This  is  the  PINCODE  information  </form>
9  EC  Payment  Slip  Printing  (PC  Version)
The  URL  of  the  test  machine  is  https://ect.familynet.com.tw/familyec/barcode_guide2.aspx  The  URL  of  the  official  
machine  is  https://ecb.famiport.com.tw/familyec/barcode_guide2.aspx
Code  Payment  Manufacturer  Instructions
17 /  26  


18 /  26  
Code  Payment  Manufacturer  Instructions
<Figure  1>


19 /  26  
Code  Payment  Manufacturer  Instructions
<Figure  2>


20 /  26  
Code  Payment  Manufacturer  Instructions
Payment  proof  for  collection  (customer  copy)  <Figure  4>
FamiPort  Payment  Slip  Printing  Format  <Figure  3>


illustrate
Chinese
<TOTAL_AMT>1000</TOTAL_AMT>  
illustrate
<ORDER_NO>183A0635505481</ORDER_NO>  
<STATUS>S</STATUS>  
Chinese
ÿ  MethodÿQueryBarcode  
Passing  in  parameters
<END_DATETIME>2016/06/08  15:30:00</END_DATETIME>  
<?xml  version="1.0"  encoding="utf-8"?>  
<PAY_DATA>  
Return:  String  (XML  message):
The  EC  code  payment  system  will  send  back  a  three-segment  barcode  (string).
Example  of  reply  message:
</PAY_DATA>  
ID  
After  the  business  has  successfully  obtained  the  number  through  EC  or  the  manufacturer,  the  following  Web  Service  can  be  used  to  transmit  the  order  number  and  BARCODE_2
Obtain  three-segment  barcode  (from  manufacturer  to  EC  code  platform)
<BARCODE_3>203884000000478</BARCODE_3>  
ID  
The  segment  barcode  (string)  is  automatically  converted  to  a  barcode  format  and  sent  to  the  mobile  phone.
<BARCODE_1>060601KI2</BARCODE_1>  
<BARCODE_2>00183A0635505481</BARCODE_2>  
PAY_DATA  
BARCODE_ÿ  
string  status
Code  Payment  Manufacturer  Instructions
TOTAL_AMT
The  manufacturer's  web  service  password  (Web  Service  connection  password)
string  
string  The  first  barcode  9  digits
STATUS  
string  The  second  barcode  16  digits
END_DATETIME  
string  Manufacturer  order  number
ACCOUNT_NO  
ÿÿÿYYYY/MM/DD  HH:mm:ss  
BARCODE_2  
PIN  CODE  
XML  ROOT  
ORDER_NO  
ÿTest  area  website:  https://ect.familynet.com.tw/pin/FamiWeb.asmx
BARCODE_ÿ  
(Order)  Validity  Period
BARCODE_ÿ  
The  vendor's  web  service  account  (Web  Service  connection  account)
ORDER_NO  
string  
string  Account  
number  string  
Password  string  The  second  barcode  00  plus  PINCODE,  a  total  of  16  codes
string  Manufacturer  order  number
string  The  third  barcode  15  digits
21 /  26  
Order  amount  +  handling  fee
S:  success;  F:  failure
PASSWORD  
string  Total  amount
ÿOfficial  website:  https://ec.fami.life/pin/FamiWeb.asmx
10Mobile  APP  obtains  three-segment  barcode


2.  Phone  brightness:  You  can  directly  adjust  the  brightness  to  the  brightest  through  the  APP.  Please  refer  to  FamilyMart’s  My  FamiPort  APP.  When  there  is  a  barcode,  the  APP  directly  
forces  the  screen  brightness  to  the  maximum.  3.  Regardless  of  the  size  of  the  mobile  
phone,  the  barcode  size  needs  to  be  controlled  the  same.  4.  Barcode  position:  Because  the  mobile  phone  
screen  has  a  border,  the  barcode  must  be  placed  in  the  center,  otherwise  the  machine  will  easily  scan  the  border  and  fail  to  read  the  barcode  content.  5.  Barcode  height  (up  and  down):  
0.6~0.8  cm.  6.  Barcode  width  (left  and  
right):  3.5~4  cm.  7.  Barcode  spacing:  0.6~1  cm.  8.  Please  use  Code  39  
to  generate  the  barcode.
Call  the  API  again  to  generate  a  new  Barcode  value  and  then  convert  it  into  a  barcode.
Three-segment  barcode  instructions
22 /  26  
Code  Payment  Manufacturer  Instructions
ÿ  Mobile  phone  barcode  design  considerations  1.  The  
barcode  display  is  regarded  as  a  small  white  list  pulled  out  by  FamiPort,  so  each  set  of  barcodes  is  limited  to  only  10  minutes,  and  must  be  used  after  the  expiration


11  XML  error  codes
STATCODE  
HEADER  
Return  message  content  Actual  message  content  AP  STATUS
Message  Classification
The  meaning  of  the  STATCODE  status  code  in  the  HEADER  of  the  Web  Service  response  message
3007  
Order  already  exists
Please  contact  the  counter  staff  if  the  collection  number  is  overdue
3009  
0005  
Please  contact  the  counter  staff  Web  Exception
Correct  (returns  blank)
Please  contact  the  counter  staff  XML  Missing  a  required  field
F  
3002  
PIN  Code  Generation  Failed
1003  
Please  contact  the  counter  staff.  Wrong  code  type
3013  
Please  contact  the  counter  staff.  The  manufacturer  code  is  wrong.
No  data  found
0002  
3006  
F  
Network  connection  failed.  Connection  timed  out.
0004  
Please  contact  the  counter  staff.  The  manufacturer  code  has  expired.
3008  
Please  contact  the  counter  staff  Exception
Please  contact  the  counter  staff  XML  content  error
F  
Verification/Information  3001
Failed  to  obtain  number
1002  
Please  contact  the  counter  staff.  The  manufacturer's  information  is  incorrect.
3012  
Please  contact  the  counter  staff.  Wrong  account/password
No  data  found  No  data  found
3005  
Data  0001
F  
Code  Payment  Manufacturer  Instructions
Please  contact  the  counter  staff.  The  amount  exceeds  the  upper  limit  of  the  collection  amount.
Network  connection  failed,  data  transmission  is  incomplete
2003  
9002  
Please  contact  the  counter  staff  XML  format  parsing  failed
2005  
No  data  found  (case  closed)  No  data  found  (case  closed)
0000  
Please  contact  the  counter  staff  to  remove  the  collection  code
No  collection  number
Network  1001
3011  
S  
Please  contact  the  counter  staff  XML  format  validation  reference  is  not  defined
3004  
correct
F  
Please  contact  the  counter  staff.  The  company  information  is  invalid.
Network  connection  failed  Data  connection  error
Exception  9001  
2002  
Failed  to  obtain  number
2004  
Please  contact  the  counter  staff.  The  order  validity  period  or  time  format  is  incorrect.
9003  
Please  contact  the  counter  staff  to  remove  the  manufacturer  code
Barcode  failed
3010  
0006  
23 /  26  
(blank)
3003  
Please  contact  the  counter  staff  XML  format  verification  failed
F  
PIN  Code  Repeat  PIN  Code  Repeat
Please  contact  the  counter  staff.  The  manufacturer  code  is  invalid.
Manufacturer  returns  4001
XML  Format  2001
Unresolvable  case
0003  
Please  contact  the  counter  staff.  Wrong  collection  code
Please  contact  the  counter  staff  Oracle  Exception


ÿ  Natural  day:  daily  timeline
ÿ  Transaction  date:  the  date  on  which  the  consumer  actually  pays  the  fee  (D)
ÿ  Transfer  date:  The  time  when  the  EC  platform  generates  files  for  manufacturers  to  download  (D+1)
ÿ  Debit/credit  date:  The  date  agreed  by  the  channel  and  the  manufacturer  for  each  transaction  in  the  debit  file
ÿ  Batch  transfer  of  write-off  data
ÿ  Checkout:  Based  on  the  transfer  date
ÿManufacturers  can  download  transaction  information  from  the  platform  after  13:00  every  day
ÿ  Summarize  the  store  information  and  compare  it  with  the  case  data,  and  pass  the  successful  account  data  to  the  manufacturer.
ÿ  Payment  date:  Based  on  the  contract
|  
|  
|  
|  
<Closing  work>
12  Daily  Account  Settlement
Trading  day:  5/20
5/21  13:00  
Manufacturer  to  EC
Platform  Download
Case  Dismissal  Information
Natural  day:  5/21
0  1  2  3  4  5  6  7  8  9  10  11  12  13  14  15  16  17  18  19  20  21  22  23  24  1  2  3  
Code  Payment  Manufacturer  Instructions
(Natural  day  5/20  00:00~5/20  24:00)
24 /  26  
Debit/credit  date:  5/20
Case  Dismissal  Information
Write-off  information
Manufacturer
EC  Code  Platform
Comparison  work
D+1  
D  


1.  File  name:  Manufacturer  code-transmission  date.TXT
3.  Fields  and  instructions  for  manufacturer  downloads
2.  Download  location:  EC  code  platform  (account  number  and  password  will  be  provided  again)
[Download  File  Instructions]
4.  Sample  
20  Transaction  Date  The  date  on  which  the  consumer  actually  made  the  payment
File  Type
7  26  
eg  1KK0
This  information  will  be  included  in  the  next  day's  audit  file
99  100  
100  
Code  Payment  Manufacturer  Instructions
Fill  the  blank  left  and  right
4.  Collecting  manufacturer  code  Collecting  manufacturer  code
27  29  
e.g.  "1  
ÿDuplicate  channel  (5):  Consumers  pay  the  same  PINCODE  twice
3  
5  
0D0A  
5.  Collection  amount:  the  amount  actually  paid  by  the  consumer
6  Store  Number
34  53  
Right  and  left  fill  0
8  Data  Type
Collection  vendor  
code-AD  year  transmission  
date.TXT  File  name  Daily  write-off  file  File  length  (table  body)  File  purpose  
FOR  EC  Account  daily  write-off  comparison  use
1  
ÿNo  case  but  accounts  (2):  Immediate  case  closure  failure
Remark
File  Code
1  
6  
YYYY/MM/DD  
HH:MM:SS  
e.g.  KK0
(It  may  be  due  to  network  disconnection,  etc.)
"  
8  
2  
TEXT  FILE
3  Collection  code  Collection  code
e.g.  ********901234
Fill  the  blank  left  and  right
2  LF  
Watch  body
20  The  order  number  of  the  whole  network  is  the  second  segment  of  the  barcode  PINCODE
No  BE  Length  Field  Name
32  Manufacturer  Order  Number  Order  number  generated  by  the  manufacturer
4  30  33  
6  54  85  7  86  90  
Skip  code
25 /  26  
e.g.  "009999"  
Store  number
Field  Description
ÿWith  records  and  accounts  (1):  Normal
91  98  


13.  Notes
26 /  26  
Code  Payment  Manufacturer  Instructions
The  payment  has  been  received,  so  the  manufacturer  must  fully  recognize  it  and  close  the  case.
6.  The  EC  code  platform  provides  write-off  file  information  for  manufacturers  to  download,  and  the  write-off  file  is  retained  for  a  maximum  of  20  days.  
7.  The  contents  of  the  account  closing  file  are  based  on  the  natural  day  (00:00~24:00)  and  will  be  used  as  the  basis  for  remittances  with  manufacturers.  8.  If  the  
inquiry  response  data  contains  confidential  information  of  consumers,  it  must  be  controlled  by  the  manufacturer.  9.  
When  consumers  conduct  payment  collection  business  at  the  channel,  if  the  host  of  the  manufacturer  is  under  maintenance,  the  channel  will  still  regard  the  transaction  as  normal.  
When  the  network  is  normal,  the  manufacturer  will  link  the  entire  network  to  make  up  the  transmission  and  sales.  10.  After  the  
transaction  is  completed,  the  system  will  not  cancel  the  transaction.  If  there  is  a  need  to  cancel  the  transaction,  the  account  will  be  adjusted  manually  after  obtaining  the  manufacturer's  approval.
work.
Print  the  payment  slip,  which  is  valid  until  00:05  on  5/21.
5.  In  order  to  avoid  the  network  being  disconnected  when  the  manufacturer  sends  back  the  MLM  success  message,  there  will  be  a  mechanism  for  supplementing  the  MLM  case,  so  the  manufacturer  must  
be  able  to  accept  the  supplementary  MLM  case  message.  Note  on  the  time  for  retransmission  and  cancellation  of  supplementary  cases:  retransmission  is  conducted  every  30  minutes,  and  will  
stop  after  6  retransmissions.
2.  The  validity  period  of  the  payment  slip  printed  by  the  customer  on  the  website  is  set  according  to  the  agreement  between  the  two  parties  and  is  preset  to  3  
days.  For  example,  if  the  consumer  prints  the  payment  slip  at  13:06  on  3/1,  the  validity  period  
of  the  payment  slip  is  13:06  on  3/4.  3.  If  the  payment  deadline  of  the  manufacturer's  order  (telegram:  END_DATE  &  END_TIME)  is  less  than  the  validity  period  of  
the  payment  order,  the  payment  deadline  of  the  manufacturer's  order  shall  prevail.  Same  as  the  above  case,  if  the  payment  deadline  is  3/2  18:00,  the  
consumer  must  pay  within  this  time.  4.  When  
the  channel  sends  the  merchant  a  case  closing  message  through  the  EC  code  platform,  it  means  that  the  consumer  has  successfully  paid  at  the  store  and  the  store
1.  The  payment  slip  printed  by  the  customer  at  the  store  is  valid  for  10  minutes  after  printing.  For  example,  if  a  consumer  prints  a  payment  slip  at  23:55  on  5/20,
11.  The  information  on  case  closing  and  account  closing  is  inconsistent.  There  is  no  case  but  there  is  account  information.  The  operator  needs  to  close  the  
case  by  himself.  12.  If  there  is  a  discrepancy  between  the  account  closing  and  the  remittance,  please  report  it  to  the  network  marketing  window  
within  7  days  for  clarification.  13.  Daily  accounting  files  shall  be  exchanged  in  the  manner  agreed  upon  by  both  parties.
