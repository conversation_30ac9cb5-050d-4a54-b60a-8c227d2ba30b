﻿<?php
	include "QPayToolkit.php";
	
	AllotQuery();
	
	//建立信用卡訂單
	function OrderCreate4Card()
	{
		global $ShopNo;
	
		$Service = new OrderCreate;
		$Service->ShopNo = $ShopNo;
		$Service->Amount = '50000';
		$Service->OrderNo = 'C' . date("YmdHis");
		$Service->PayType = 'C';
		$Service->PrdtName = '信用卡訂單';
		$Service->ReturnURL = 'http://10.11.22.113:8803/QPay.ApiClient-Sandbox/Store/Return';
		$Service->BackendURL = 'https://sandbox.sinopac.com/funBIZ.ApiClient/AutoPush/PushSuccess';
		$Service->CardParam['AutoBilling'] = 'Y';
		
		echo APIService("OrderCreate", $Service);
	}
	
	//建立虛擬帳號訂單
	function OrderCreate4ATM()
	{
		global $ShopNo;
	
		$date = new DateTime(date('Y-m-d'));
		$date->add(new DateInterval('P10D'));
		$ExpireDate = $date->format('Ymd');
	
		$Service = new OrderCreate;
		$Service->ShopNo = $ShopNo;
		$Service->Amount = '50000';
		$Service->OrderNo = 'A' . date("YmdHis");
		$Service->PayType = 'A';
		$Service->PrdtName = '虛擬帳號訂單';
		$Service->ReturnURL = 'http://10.11.22.113:8803/QPay.ApiClient-Sandbox/Store/Return';
		$Service->BackendURL = 'https://sandbox.sinopac.com/funBIZ.ApiClient/AutoPush/PushSuccess';
		$Service->ATMParam['ExpireDate'] = $ExpireDate;
		
		echo APIService("OrderCreate", $Service);
	}
  
	//建立行動支付訂單
	function OrderCreate4Mobile()
	{
		global $ShopNo;
    
		$Service = new OrderCreate;
		$Service->ShopNo = $ShopNo;
		$Service->Amount = '50000';
		$Service->OrderNo = 'M' . date("YmdHis");
		$Service->PayType = 'M';
		$Service->PrdtName = '行動支付訂單';
		$Service->ReturnURL = 'http://10.11.22.113:8803/QPay.ApiClient-Sandbox/Store/Return';
		$Service->BackendURL = 'https://sandbox.sinopac.com/funBIZ.ApiClient/AutoPush/PushSuccess';
		$Service->MobileParam['ExpMinutes'] = '10';
		
		echo APIService("OrderCreate", $Service);
	}
	
	//信用卡 - 待請款訂單查詢
	function OrderUnCapturedQuery()
	{
		global $ShopNo;
		
		$Service = new OrderUnCapturedQuery;
		$Service->ShopNo = $ShopNo;
		
		echo APIService("OrderUnCapturedQuery", $Service);
	}
	
	//信用卡 – 訂單維護服務
	function OrderMaintain()
	{
		global $ShopNo;
		
		$Service = new OrderMaintain;
		$Service->ShopNo = $ShopNo;
		$Service->OrderNo = 'C201805250005';
		$Service->Command = 'R';
		
		echo APIService("OrderMaintain", $Service);
	}
	
	//虛擬帳號退款 – 首次發動API
	function OrderMaintain()
	{
		global $ShopNo;
		
		$Service = new OrderMaintain;
		$Service->ShopNo = $ShopNo;
		$Service->OrderNo = 'A202209250001';
		$Service->Command = 'R';
		
		echo APIService("OrderMaintain", $Service);
	}
	
	//虛擬帳號退款 – 第二次發動API
	function OrderMaintain()
	{
		global $ShopNo;
		
		$Service = new OrderMaintain;
		$Service->ShopNo = $ShopNo;
		$Service->OrderNo = 'A202209250001';
		$Service->Command = 'R';
		$Service->RefundToken = '68b58e2a4c1bd52be54f8d602d7bb3fefccb38c02d1519eb987507b6d1622567';
		
		echo APIService("OrderMaintain", $Service);
	}
	
	//訂單交易查詢
	function OrderQuery()
	{
		global $ShopNo;
		
		$Service = new OrderQuery;
		$Service->ShopNo = $ShopNo;
		$Service->OrderNo = 'A20180706100451';
		
		echo APIService("OrderQuery", $Service);
	}
	
	//訊息查詢服務
	function OrderPayQuery()
	{
		global $ShopNo;
		
		$Service = new OrderPayQuery;
		$Service->ShopNo = $ShopNo;
		$Service->PayToken = 'c7b0d2b994251d730f4a2feb24e584c509264e92829c9194d1952da08898e44f';
		
		echo APIService("OrderPayQuery", $Service);
	}
	
	//交易對帳檔查詢服務
	function BillQuery()
	{
		global $ShopNo;
		
		$Service = new BillQuery;
		$Service->ShopNo = $ShopNo;
		$Service->BillDate = '20180525';
		
		echo APIService("BillQuery", $Service);
	}
	
	//撥款檔查詢服務
	function AllotQuery()
	{
		global $ShopNo;
		
		$Service = new AllotQuery;
		$Service->ShopNo = $ShopNo;
		$Service->AllotDateS = '20180625';
		$Service->AllotDateE = '20180625';
		$Service->PayType = 'A';
		
		echo APIService("AllotQuery", $Service);
	}

	//信用卡價金 撥款檔查詢服務
	function AllotTrustQuery()
	{
		global $ShopNo;
		
		$Service = new AllotTrustQuery;
		$Service->ShopNo = $ShopNo;
		$Service->AllotDateS = '20180625';
		$Service->AllotDateE = '20180625';
		
		echo APIService("AllotTrustQuery", $Service);
	}

	//信用卡綁卡授權交易
	function CardBindCreate()
	{
		global $ShopNo;
		
		$Service = new CardBindCreate;
		$Service->ShopNo = $ShopNo;
		$Service->BindNo = date("YmdHis");
		$Service->ReturnURL = 'http://10.11.22.113:8803/QPay.ApiClient-Sandbox/Store/Return';
				
		echo APIService("CardBindCreate", $Service);
	}
?>

