﻿PHP SampleCode 使用方法

1.請開啟 QPayToolkit.php 調整參數
	(1) $targetUrl(行數 3)
		正式環境使用, 請改成 ==> https://api.sinopac.com/funBIZ/QPay.WebAPI/api/Order
		測試環境使用, 請改成 ==> https://apisbx.sinopac.com/funBIZ-Sbx/QPay.WebAPI/api/
		測試環境備援使用, 請改成 ==> https://sandbox.sinopac.com/QPay.WebAPI/api/
		
	(2) $ShopNo (行數 6)
		請依信件內提供商店編號更改
	
	(3) $Hash (行數 8)
		請依信件內提供雜湊值, 更改陣列內 A1、A2、B1、B2 參數

	(4) $QPayApimAuth (行數 15)
		請依信件內提供API 授權認證金鑰更改
		
2.完成參數調整後, 請將要使 API 服務 include QPayToolkit.php 進去, 呼叫方法請參考 SampleCode.php, 相關 Sample 如下
	(1) 建立信用卡訂單  ==>  OrderCreate4Card()
	(2) 建立虛擬帳號訂單  ==>  OrderCreate4ATM()
	(3) 建立行動支付訂單  ==>  OrderCreate4Mobile()
	(4) 信用卡 - 待請款訂單查詢  ==>  OrderUnCapturedQuery()
	(5) 信用卡 – 訂單維護服務  ==>  OrderMaintain()
	(6) 訂單交易查詢  ==>  OrderQuery()
	(7) 訊息查詢服務  ==>  OrderPayQuery()
	(8) 交易對帳檔查詢服務  ==>  BillQuery()
	(9) 撥款檔查詢服務  ==>  AllotQuery()
	(10) 信用卡價金 撥款檔查詢服務  ==>  AllotTrustQuery()
	(11) 信用卡綁卡授權交易  ==>  CardBindCreate()
	
	PS.$Service 各欄位填寫限制請以API串接手冊為主