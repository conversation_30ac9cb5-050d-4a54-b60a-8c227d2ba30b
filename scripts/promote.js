const fs = require('fs-extra')
const path = require('path')

const nextDir = path.join(__dirname, '../.next')
const serveDir = path.join(__dirname, '../serve')
const serveNextDir = path.join(serveDir, '.next')

async function promote() {
  try {
    // Check if .next directory exists
    const nextDirExists = await fs.pathExists(nextDir)
    if (!nextDirExists) {
      console.error(
        'Error: .next directory does not exist. So Im not deleting the current .next directory in the serve folder.'
      )
      process.exit(1)
    }

    // Ensure serve directory exists
    await fs.ensureDir(serveDir)

    // Remove existing .next in serve directory if it exists
    const serveNextDirExists = await fs.pathExists(serveNextDir)
    if (serveNextDirExists) {
      await fs.remove(serveNextDir)
    }

    // Move .next to serve directory
    await fs.move(nextDir, serveNextDir)

    console.log('Build promoted successfully: .next moved to serve/.next')
  } catch (err) {
    console.error('Error promoting build:', err)
    process.exit(1)
  }
}

promote()

