const axios = require('axios');
const crypto = require('crypto');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

// Create readline interface for confirmations
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Confirmation prompt helper
const confirm = async (message) => {
  return new Promise((resolve) => {
    rl.question(`${message} [y/N]: `, (answer) => {
      resolve(answer.toLowerCase() === 'y');
    });
  });
};

// Format sensitive data - show only partial information
const maskSensitive = (value, showChars = 4) => {
  if (!value) return '[NOT SET]';
  if (typeof value !== 'string') return '[OBJECT]';
  if (value.length <= showChars) return '****';
  return value.substring(0, showChars) + '****';
};

// Safe encryption method
function encryptAES(text, key, iv, algorithm = 'aes-128-cbc') {
  try {
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  } catch (error) {
    console.error('Encryption error:', error.message);
    throw error;
  }
}

// Add verbose logging function
const logVerbose = (message, data = null) => {
  const timestamp = new Date().toISOString();
  console.log(`\n[DEBUG ${timestamp}] ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
};

// Set NODE_ENV explicitly for testing different encryption methods
process.env.NODE_ENV = 'production'; // Force production mode
process.env.IF_USE_PASSWORD_KEY = 'true'; // Force password-based encryption

// Add command line argument parsing for encryption mode
const args = process.argv.slice(2);
const usePasswordKey = !args.includes('--use-config-key');
process.env.IF_USE_PASSWORD_KEY = usePasswordKey ? 'true' : 'false';

// Main function
async function productionTopup() {
  try {
    console.log('\n🚨 IF PRODUCTION TOPUP SCRIPT 🚨');
    console.log('==============================');
    console.log('⚠️  WARNING: This script will use REAL MONEY to recharge a phone number ⚠️');
    console.log('==============================\n');

    console.log('Encryption mode:', usePasswordKey ? 'Using password as key' : 'Using config key');
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('Hostname:', require('os').hostname());
    console.log('Node version:', process.version);

    // Load configuration
    console.log('Attempting to load configuration...');
    let config;
    
    try {
      const configPath = path.join(__dirname, '../components/taiwan/operators/if/config.json');
      console.log('Looking for config at:', configPath);
      config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      console.log('✅ Configuration loaded successfully from:', configPath);
    } catch (error) {
      console.error('❌ Failed to load configuration:', error.message);
      
      // Prompt for manual configuration path
      const manualPath = await new Promise((resolve) => {
        rl.question('\nEnter path to config file (or leave empty to abort): ', (path) => {
          resolve(path.trim());
        });
      });
      
      if (!manualPath) {
        console.log('Operation cancelled: No configuration available.');
        return;
      }
      
      try {
        config = JSON.parse(fs.readFileSync(manualPath, 'utf8'));
        console.log('✅ Configuration loaded successfully from manual path');
      } catch (error) {
        console.error('❌ Failed to load configuration from manual path:', error.message);
        return;
      }
    }

    // Print all configuration details for verification
    console.log('\n=== CONFIGURATION VERIFICATION ===');
    console.log(`Merchant ID: ${maskSensitive(config.merchantID)}`);
    console.log(`Merchant Password: ${maskSensitive(config.merchantPassword)}`);
    console.log(`API Key: ${maskSensitive(config.apiKey)}`);
    console.log(`Environment: PRODUCTION`);
    console.log(`API URL: ${config.liveUrl || '[NOT SET]'}`);
    console.log(`Encryption Algorithm: ${config.encryption?.algorithm || '[NOT SET]'}`);
    console.log(`Encryption Key Set: ${config.encryption?.key ? 'YES' : 'NO'}`);
    console.log(`Encryption IV Set: ${config.encryption?.iv ? 'YES' : 'NO'}`);
    
    // Show available products
    console.log('\n=== AVAILABLE PRODUCTS ===');
    if (config.productCodes && config.productCodes.length > 0) {
      config.productCodes.forEach((product, index) => {
        console.log(`[${index + 1}] ${product.id}: ${product.name} - NT$${product.price}`);
      });
    } else {
      console.log('No products found in configuration');
    }

    // Confirm configuration
    const configOk = await confirm('\nIs the configuration correct?');
    if (!configOk) {
      console.log('Operation cancelled by user: Configuration not confirmed.');
      return;
    }

    // Confirm production mode
    const useProduction = await confirm('\n⚠️ THIS WILL USE THE PRODUCTION API. Continue?');
    if (!useProduction) {
      console.log('Operation cancelled by user.');
      return;
    }

    // When running on server, use the same host for API
    // This will work whether script runs on development or production server
    const API_ENDPOINT = '/api/payment/if/activate';
    console.log('\nUsing API endpoint on current host: ' + API_ENDPOINT);

    // Get phone number
    const phoneNumber = await new Promise((resolve) => {
      rl.question('\nEnter 10-digit phone number to recharge: ', (number) => {
        resolve(number.trim());
      });
    });

    // Validate phone number
    if (!/^\d{10}$/.test(phoneNumber)) {
      console.error('❌ Invalid phone number. Must be exactly 10 digits.');
      return;
    }

    // Confirm phone number
    const confirmPhoneNumber = await confirm(`\n⚠️ Confirm recharge for phone number ${phoneNumber}?`);
    if (!confirmPhoneNumber) {
      console.log('Operation cancelled by user.');
      return;
    }

    // Select product
    const products = config.productCodes || [];
    
    if (products.length === 0) {
      console.error('❌ No products available. Cannot proceed.');
      return;
    }

    // Group products by type
    const topupProducts = products.filter(p => p.name.toLowerCase().includes('recharge'));
    const wirelessProducts = products.filter(p => !p.name.toLowerCase().includes('recharge'));

    console.log('\n=== SELECT A PRODUCT ===');
    
    if (topupProducts.length > 0) {
      console.log('\n📱 TOPUP PLANS:');
      topupProducts.forEach((product, index) => {
        console.log(`[${index + 1}] ${product.name} - NT$${product.price}`);
      });
    }

    if (wirelessProducts.length > 0) {
      console.log('\n📶 WIRELESS PLANS:');
      wirelessProducts.forEach((product, index) => {
        console.log(`[${index + topupProducts.length + 1}] ${product.name} - NT$${product.price}`);
      });
    }
    
    const productIndex = await new Promise((resolve) => {
      rl.question('\nSelect product by number: ', (index) => {
        resolve(parseInt(index.trim()) - 1);
      });
    });
    
    if (isNaN(productIndex) || productIndex < 0 || productIndex >= products.length) {
      console.error('❌ Invalid selection. Operation cancelled.');
      return;
    }
    
    const selectedProduct = products[productIndex];

    console.log('\nSelected product:');
    console.log(`- Name: ${selectedProduct.name}`);
    console.log(`- ID: ${selectedProduct.id}`);
    console.log(`- Price: NT$${selectedProduct.price}`);

    // Final confirmation with amount
    const finalConfirm = await confirm(`\n⚠️ FINAL WARNING: You are about to spend NT$${selectedProduct.price} to recharge ${phoneNumber}. Proceed?`);
    if (!finalConfirm) {
      console.log('Operation cancelled by user.');
      return;
    }

    console.log('\nPreparing payload...');
    
    // Prepare Next.js API payload (much simpler)
    const payload = {
      phoneNumber,
      productId: selectedProduct.id,
      orderId: `order_${Date.now()}` // Generate an order ID
    };

    console.log('✅ Payload prepared');
    console.log('\nPayload summary:');
    console.log(`- Phone Number: ${phoneNumber}`);
    console.log(`- Product ID: ${selectedProduct.id}`);
    console.log(`- Product Name: ${selectedProduct.name}`);
    console.log(`- Price: NT$${selectedProduct.price}`);
    console.log(`- Order ID: ${payload.orderId}`);

    // Last chance to cancel
    console.log('\n🔴 POINT OF NO RETURN 🔴');
    const executeConfirm = await confirm('Are you absolutely sure you want to execute this transaction?');
    if (!executeConfirm) {
      console.log('Operation cancelled by user.');
      return;
    }

    console.log('\nExecuting transaction...');
    
    try {
      // For a Node.js script running directly, we need a host
      const host = 'https://sim.abnasia.org';
      const fullUrl = new URL(API_ENDPOINT, host).toString();
      console.log(`Full URL: ${fullUrl}`);
      
      // Make API request to Next.js endpoint
      console.log(`Calling API endpoint...`);
      
      // Add request tracing
      const requestStartTime = Date.now();
      
      const response = await axios.post(fullUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Debug-Mode': 'true',
          'X-Request-ID': `script-${Date.now()}`
        },
        timeout: 30000 // 30 second timeout
      });

      const requestDuration = Date.now() - requestStartTime;
      logVerbose(`Request completed in ${requestDuration}ms`);
      
      console.log('\nAPI Response received:');
      console.log('Status:', response.status);
      console.log('Response headers:', response.headers);
      console.log('Response time:', requestDuration + 'ms');
      console.log('Response:', JSON.stringify(response.data, null, 2));

      if (response.data && response.data.success) {
        console.log('\n✅ SUCCESS! Card recharged successfully');
        if (response.data.data) {
          console.log(`Transaction ID: ${response.data.data.transactionId}`);
          console.log(`Message: ${response.data.data.message}`);
          console.log(`Activation Date: ${response.data.data.activationDate}`);
          console.log(`Product: ${response.data.data.productName}`);
          console.log(`Amount: NT$${response.data.data.cardValue}`);
          
          // Show which encryption method worked
          if (response.data.data.encryptionMethod) {
            console.log(`\n🔑 WORKING ENCRYPTION METHOD: ${response.data.data.encryptionMethod}`);
            console.log('This is the encryption method that successfully authenticated with the VRC API.');
          }
        }
      } else {
        console.log('\n❌ Transaction failed');
        console.log(`Error: ${response.data?.error || 'Unknown error'}`);
        console.log(`Result Code: ${response.data?.resultCode || 'unknown'}`);
        
        // Show encryption methods tried
        if (response.data?.testedMethods) {
          console.log('\n🔑 ENCRYPTION METHODS TRIED:');
          response.data.testedMethods.forEach(method => {
            console.log(`- ${method}`);
          });
        }
        
        if (response.data?.resultCode === '0004') {
          console.log('\n💡 TROUBLESHOOTING TIP:');
          console.log('Error 0004 typically indicates an authentication issue with the VRC API.');
          console.log('All standard encryption methods were tried but none succeeded.');
        }
      }
    } catch (error) {
      console.error('\n❌ Error occurred:', error.message);
      
      // Enhanced error logging
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        
        // If we have an error code 0004, provide specific troubleshooting guidance
        if (error.response.data?.resultCode === '0004') {
          console.error('\n🔍 TROUBLESHOOTING ERROR 0004:');
          console.error('This error code typically indicates an authentication issue:');
          console.error('1. The merchant account credentials might be incorrect');
          console.error('2. The encryption key method might be incorrect (try --use-config-key flag)');
          console.error('3. The IP address may not be whitelisted for API access');
          console.error('4. The API key might be incorrect or expired');
          
          // Suggest trying both encryption methods
          if (usePasswordKey) {
            console.error('\nTry running the script with --use-config-key flag to use the fixed key');
          } else {
            console.error('\nTry running the script without --use-config-key to use the password as the key');
          }
        }
      } else if (error.request) {
        console.error('No response received. Server may be down or network issue.');
        console.error('Request details:', error.request);
      } else {
        console.error('Error details:', error);
      }
    }
  } catch (error) {
    console.error('\n❌ Script error:', error.message);
  } finally {
    // Close readline interface
    rl.close();
  }
}

// Run the topup function
productionTopup(); 
