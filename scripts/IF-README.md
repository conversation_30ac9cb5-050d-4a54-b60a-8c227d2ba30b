# <PERSON><PERSON> VRC System Testing Guide

This guide provides instructions for testing the Chuan <PERSON> VRC (Value Recharge Card) system integration.

## Test Scripts

Two test scripts are provided:

1. `test-if-connection.js` - Main test script for the VRC API
2. `if-test-numbers.js` - Helper script to generate test phone numbers

## Prerequisites

- Node.js 14+ installed
- Access to a whitelisted server for production testing

## Configuration

The test scripts use the same configuration file as the main application:

```
components/taiwan/operators/if/config.json
```

Make sure this file is properly configured with your credentials:

```json
{
  "merchantID": "01PE0016C",
  "merchantPassword": "!Aa12345",
  "apiKey": "pd2es73anm16zhe",
  "encryption": {
    "key": "ptWadzG6WfLMs7fi",
    "iv": "ptWadzG6WfLMs7fi",
    "algorithm": "aes-128-cbc"
  },
  "testUrl": "https://*************/vrc/VrcService/StoredValue",
  "liveUrl": "https://vrc.arcoa.com.tw/vrc/VrcService/StoredValue",
  "notifyURL": "https://your-website.com/api/payment/if/callback",
  "productCodes": [
    // Product codes here...
  ]
}
```

## Main Test Script Usage

### Local Testing

For local testing (without actually connecting to the VRC API):

```bash
node scripts/test-if-connection.js --mock --verbose
```

This will run all tests but mock the API calls.

### Server Testing

When running on a whitelisted server:

```bash
# Test environment
node scripts/test-if-connection.js --env=test

# Production environment
node scripts/test-if-connection.js --env=production
```

### Command Line Options

- `--env=test|production|development` - Select the environment (default: test)
- `--verbose` - Enable verbose logging
- `--mock` - Mock API calls (for local testing)
- `--ignore-ssl` - Ignore SSL certificate validation (for development only)

## Test Phone Numbers

Generate test phone numbers:

```bash
node scripts/if-test-numbers.js [count] [prefix]
```

Example:
```bash
node scripts/if-test-numbers.js 5 0912
```

This will generate 5 random phone numbers starting with 0912.

## Collecting Test Logs

The test script automatically logs to files in the `logs/` directory. When running tests on the server, you'll find the log file at:

```
logs/if-test-[timestamp].log
```

Copy the contents of this file for analysis.

## Testing Process

1. **Local Development**
   - Run with `--mock` to test functionality without making real API calls
   - Verify encryption and validation logic works correctly

2. **Server Testing**
   - Upload the scripts to your whitelisted server
   - Run without the `--mock` flag to test real API connections
   - Collect logs for analysis

3. **Analysis**
   - Review logs to identify issues
   - Check for successful API connections and responses
   - Verify encryption matches expected format

## Troubleshooting

### Connection Issues

If you see errors like `ENOTFOUND` or `ECONNREFUSED`:
- Make sure you're running on a whitelisted server
- Check that firewall rules allow outbound connections
- Verify DNS resolution works correctly

### Authentication Errors

If the API rejects your credentials:
- Verify `merchantID` and `merchantPassword` are correct
- Check that the `apiKey` is valid and active
- Ensure encryption parameters match the documentation

### SSL/TLS Issues

If you see SSL errors:
- Make sure your server has updated CA certificates
- Node.js may require special options for TLS connections
- For development or testing purposes, you can use the `--ignore-ssl` flag:
  ```
  node scripts/test-if-connection.js --env=development --ignore-ssl
  ```
- When connecting directly via IP address instead of hostname, you may need to use the `--ignore-ssl` option
- **Important**: Do not use `--ignore-ssl` in production environments

## Error Codes

Common error codes from the VRC API:

| Error Code | Description                  |
|------------|------------------------------|
| 0000       | Success                      |
| 0004       | Error (see message)          |
| 1000       | Pending                      |

## Support

For additional assistance, contact:
- Chuan Hung VRC System Support
- Email: <EMAIL>
- Phone: +886-xxx-xxx-xxx 