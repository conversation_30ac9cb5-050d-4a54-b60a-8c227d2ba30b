// Test hello endpoint
const axios = require('axios');

const API_KEY = 'uX6HsVoPhmapndxrUhDn';
const BASE_URL = 'http://localhost:3000';

async function testHello() {
  console.log('Testing hello endpoint');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/hello`, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    console.log('Status:', response.status);
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Message:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

testHello().catch(console.error);