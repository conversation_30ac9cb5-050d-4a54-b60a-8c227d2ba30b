#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/*    } catch (error) {
        if (error instanceof SyntaxError) {
            console.error(`✗ Invalid JSON in ${sourceFile}: ${error.message}`);
        } else {
            console.error(`✗ Error backing up ${sourceFile}: ${error.message}`);
        }ackup Script for .gitignore JSON Files
 * 
 * This script backs up JSON files that are listed in .gitignore to prevent
 * them from being lost on the server while avoiding git conflicts during development.
 */

// Configuration
const BACKUP_DIR = 'data/backups/gitignore-json';
const PROJECT_ROOT = path.resolve(__dirname, '../..');
const GITIGNORE_PATH = path.join(PROJECT_ROOT, '.gitignore');

/**
 * Parse .gitignore file and extract JSON files that need backup
 */
function getJsonFilesFromGitignore() {
    if (!fs.existsSync(GITIGNORE_PATH)) {
        console.error(`✗ .gitignore file not found at: ${GITIGNORE_PATH}`);
        return [];
    }

    try {
        const gitignoreContent = fs.readFileSync(GITIGNORE_PATH, 'utf8');
        const lines = gitignoreContent.split('\n');
        
        const jsonFiles = lines
            .map(line => line.trim())
            .filter(line => 
                line.length > 0 && 
                !line.startsWith('#') && 
                line.endsWith('.json') &&
                !line.startsWith('!') &&
                !line.includes('*') && // Skip glob patterns like *.json or **/file.json
                !line.includes('?') && // Skip single-char wildcards
                line.includes('/') && // Only include files with specific paths (not just filenames)
                line.split('/').length >= 2 // Ensure it's a proper path with at least one directory
            )
            .map(line => line.replace(/^\//, '')) // Remove leading slash if present
            .filter((file, index, array) => array.indexOf(file) === index); // Remove duplicates
        
        return jsonFiles;
    } catch (error) {
        console.error(`✗ Error reading .gitignore file: ${error.message}`);
        return [];
    }
}

/**
 * List available backup files
 */
function listBackupFiles() {
    const baseBackupDir = path.join(PROJECT_ROOT, BACKUP_DIR);
    
    if (!fs.existsSync(baseBackupDir)) {
        return [];
    }

    // Get all JSON files in the backup directory
    const findJsonFiles = (dir, baseDir = dir) => {
        let jsonFiles = [];
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                jsonFiles = jsonFiles.concat(findJsonFiles(fullPath, baseDir));
            } else if (entry.name.endsWith('.json')) {
                const relativePath = path.relative(baseDir, fullPath);
                const stats = fs.statSync(fullPath);
                jsonFiles.push({
                    path: relativePath,
                    fullPath: fullPath,
                    mtime: stats.mtime,
                    size: stats.size
                });
            }
        }
        return jsonFiles;
    };
    
    return findJsonFiles(baseBackupDir).sort((a, b) => b.mtime - a.mtime);
}

/**
 * Ask for user confirmation with a specific prompt
 */
function askConfirmation(prompt) {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    return new global.Promise((resolve) => {
        rl.question(prompt, (answer) => {
            rl.close();
            resolve(answer.toLowerCase().trim());
        });
    });
}

/**
 * Restore files from backup with multiple confirmations
 */
async function restoreFromBackup(filesToRestore = null, requireConfirmation = false) {
    const baseBackupDir = path.join(PROJECT_ROOT, BACKUP_DIR);
    
    if (!fs.existsSync(baseBackupDir)) {
        console.error(`✗ Backup directory not found: ${baseBackupDir}`);
        return false;
    }

    // Multiple confirmation prompts for safety
    if (requireConfirmation) {
        console.log('\n⚠️  WARNING: This will restore backup files and overwrite existing data!');
        console.log('⚠️  This operation should ONLY be run on LOCAL development environment!');
        console.log('⚠️  NEVER run this on production or server environments!\n');
        
        const confirmations = [
            'Are you sure you want to restore backup files? This will OVERWRITE existing data! (yes/no): ',
            'Are you running this on your LOCAL development machine? (yes/no): ',
            'Are you absolutely certain this is NOT a production server? (yes/no): ',
            'Do you understand this will replace current JSON files with backup versions? (yes/no): ',
            'Final confirmation - proceed with restore operation? (yes/no): '
        ];
        
        for (let i = 0; i < confirmations.length; i++) {
            const answer = await askConfirmation(confirmations[i]);
            if (answer !== 'yes') {
                console.log('\n❌ Restore operation cancelled by user.');
                return false;
            }
        }
        
        console.log('\n✅ All confirmations received. Proceeding with restore...\n');
    }

    console.log(`🔄 Restoring from backup...\n`);
    
    // Get all JSON files in the backup directory
    const findJsonFiles = (dir, baseDir = dir) => {
        let jsonFiles = [];
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                jsonFiles = jsonFiles.concat(findJsonFiles(fullPath, baseDir));
            } else if (entry.name.endsWith('.json')) {
                const relativePath = path.relative(baseDir, fullPath);
                jsonFiles.push(relativePath);
            }
        }
        return jsonFiles;
    };
    
    const availableFiles = findJsonFiles(baseBackupDir);
    const filesToProcess = filesToRestore || availableFiles;
    
    let successCount = 0;
    let totalCount = 0;
    
    console.log('📁 Restoring files...');
    
    for (const file of filesToProcess) {
        totalCount++;
        
        const backupFilePath = path.join(baseBackupDir, file);
        const targetFilePath = path.join(PROJECT_ROOT, file);
        
        if (!fs.existsSync(backupFilePath)) {
            console.log(`⚠ Backup file not found: ${file}`);
            continue;
        }
        
        try {
            // Ensure target directory exists
            const targetDir = path.dirname(targetFilePath);
            ensureDirectoryExists(targetDir);
            
            // Copy file
            const backupContent = fs.readFileSync(backupFilePath, 'utf8');
            
            // Validate JSON
            JSON.parse(backupContent);
            
            // Write to target location
            fs.writeFileSync(targetFilePath, backupContent);
            
            // Get file stats for reporting
            const stats = fs.statSync(targetFilePath);
            const fileSizeKB = (stats.size / 1024).toFixed(2);
            
            console.log(`✓ Restored ${file} (${fileSizeKB} KB)`);
            successCount++;
            
        } catch (error) {
            if (error instanceof SyntaxError) {
                console.error(`✗ Invalid JSON in backup ${file}: ${error.message}`);
            } else {
                console.error(`✗ Error restoring ${file}: ${error.message}`);
            }
        }
    }
    
    // Summary
    console.log('\n📊 Restore Summary:');
    console.log(`  • Files processed: ${totalCount}`);
    console.log(`  • Successful restores: ${successCount}`);
    console.log(`  • Failed restores: ${totalCount - successCount}`);
    
    return successCount === totalCount;
}

/**
 * Ensure directory exists, create if it doesn't
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`✓ Created directory: ${dirPath}`);
    }
}



/**
 * Copy file to backup location, preserving directory structure
 */
function backupFile(sourceFile, backupDir) {
    const sourcePath = path.join(PROJECT_ROOT, sourceFile);
    
    // Check if source file exists
    if (!fs.existsSync(sourcePath)) {
        console.log(`⚠ Skipping ${sourceFile} - file does not exist`);
        return false;
    }

    try {
        // Read source file
        const fileContent = fs.readFileSync(sourcePath, 'utf8');
        
        // Validate JSON
        JSON.parse(fileContent);
        
        // Preserve directory structure in backup
        const relativePath = sourceFile;
        const backupFilePath = path.join(backupDir, relativePath);
        const backupFileDir = path.dirname(backupFilePath);
        
        // Ensure the directory structure exists in backup location
        ensureDirectoryExists(backupFileDir);
        
        // Write backup file (preserving original filename and structure)
        fs.writeFileSync(backupFilePath, fileContent);
        
        // Get file stats for reporting
        const stats = fs.statSync(sourcePath);
        const fileSizeKB = (stats.size / 1024).toFixed(2);
        
        console.log(`✓ Backed up ${sourceFile} (${fileSizeKB} KB)`);
        return true;
        
    } catch (error) {
        if (error instanceof SyntaxError) {
            console.error(`✗ Invalid JSON in ${sourceFile}: ${error.message}`);
        } else {
            console.error(`✗ Error backing up ${sourceFile}: ${error.message}`);
        }
        return false;
    }
}





/**
 * Main backup function with server-side confirmations
 */
async function runBackup() {
    console.log('🔄 Starting backup of .gitignore JSON files...\n');
    
    // Multiple confirmation prompts for server-side backup safety
    console.log('⚠️  WARNING: This backup operation should ONLY be run on SERVER environments!');
    console.log('⚠️  NEVER run this backup operation on LOCAL development machines!');
    console.log('⚠️  This will create server-side backups of production data!\n');
    
    const confirmations = [
        'Are you running this backup on a SERVER environment? (yes/no): ',
        'Are you absolutely certain this is NOT a local development machine? (yes/no): ',
        'Do you understand this will backup production data on the server? (yes/no): ',
        'Are you authorized to perform server backups? (yes/no): ',
        'Final confirmation - proceed with SERVER backup operation? (yes/no): '
    ];
    
    for (let i = 0; i < confirmations.length; i++) {
        const answer = await askConfirmation(confirmations[i]);
        if (answer !== 'yes') {
            console.log('\n❌ Backup operation cancelled by user.');
            process.exit(1);
        }
    }
    
    console.log('\n✅ All confirmations received. Proceeding with server backup...\n');
    
    const timestamp = new Date().toISOString();
    console.log(`Backup started at: ${timestamp}`);
    console.log(`Project root: ${PROJECT_ROOT}\n`);
    
    // Get JSON files from .gitignore
    const jsonFilesToBackup = getJsonFilesFromGitignore();
    
    if (jsonFilesToBackup.length === 0) {
        console.log('⚠ No JSON files found in .gitignore to backup.');
        process.exit(0);
    }
    
    console.log(`Found ${jsonFilesToBackup.length} JSON files in .gitignore:`);
    jsonFilesToBackup.forEach(file => console.log(`  • ${file}`));
    console.log('');
    
    // Create base backup directory
    const baseBackupDir = path.join(PROJECT_ROOT, BACKUP_DIR);
    ensureDirectoryExists(baseBackupDir);
    
    console.log(`Backup directory: ${path.relative(PROJECT_ROOT, baseBackupDir)}\n`);
    
    // Backup each file
    let successCount = 0;
    let totalCount = 0;
    
    console.log('📁 Backing up files...');
    jsonFilesToBackup.forEach(file => {
        totalCount++;
        if (backupFile(file, baseBackupDir)) {
            successCount++;
        }
    });
    
    // Summary
    console.log('\n📊 Backup Summary:');
    console.log(`  • Files processed: ${totalCount}`);
    console.log(`  • Successful backups: ${successCount}`);
    console.log(`  • Failed backups: ${totalCount - successCount}`);
    console.log(`  • Backup location: ${path.relative(PROJECT_ROOT, baseBackupDir)}`);
    
    if (successCount === totalCount) {
        console.log('\n✅ All files backed up successfully!');
        process.exit(0);
    } else {
        console.log('\n⚠ Some files could not be backed up. Check the log above for details.');
        process.exit(1);
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    const jsonFiles = getJsonFilesFromGitignore();
    console.log(`
🔧 Backup & Restore Script for .gitignore JSON Files

Usage: node backup.gitignore-data.cjs [options]

Options:
  --help, -h           Show this help message
  --list, -l           List files that would be backed up (dry run)
  --restore            Restore files from backup (simple restore)
  --restore --confirm  Restore files with 5 confirmation prompts (for yarn json:restore)
  --list-backups       List available backup files

⚠️  IMPORTANT USAGE GUIDELINES:

🖥️  BACKUP OPERATION (default):
   • Should ONLY be run on SERVER environments
   • NEVER run on local development machines
   • Requires 5 confirmations to ensure server-side execution
   • Use: yarn json:backup:server

💻 RESTORE OPERATION (--restore --confirm):
   • Should ONLY be run on LOCAL development environments
   • NEVER run on production or server environments
   • Requires 5 confirmations to prevent accidental server execution
   • Use: yarn json:restore

This script backs up JSON files that are in .gitignore to prevent data loss
while avoiding git conflicts during development. Files are backed up directly
to the backup folder without date-based subfolders since git handles versioning.

Files found in .gitignore:
${jsonFiles.length > 0 ? jsonFiles.map(f => `  • ${f}`).join('\n') : '  • No JSON files found in .gitignore'}

Backups are stored in: ${BACKUP_DIR}
    `);
    process.exit(0);
}

if (args.includes('--list') || args.includes('-l')) {
    const jsonFiles = getJsonFilesFromGitignore();
    console.log('📋 Files that would be backed up:\n');
    
    if (jsonFiles.length === 0) {
        console.log('  ⚠ No JSON files found in .gitignore');
    } else {
        jsonFiles.forEach(file => {
            const filePath = path.join(PROJECT_ROOT, file);
            const exists = fs.existsSync(filePath);
            const status = exists ? '✓' : '✗';
            const size = exists ? `(${(fs.statSync(filePath).size / 1024).toFixed(2)} KB)` : '(not found)';
            console.log(`  ${status} ${file} ${size}`);
        });
    }
    
    console.log(`\nBackup location: ${BACKUP_DIR}`);
    process.exit(0);
}

if (args.includes('--list-backups')) {
    const backupFiles = listBackupFiles();
    console.log('📋 Available backup files:\n');
    
    if (backupFiles.length === 0) {
        console.log('  • No backup files found');
    } else {
        backupFiles.forEach((backup, index) => {
            const relativeTime = new Date(backup.mtime).toLocaleString();
            const fileSizeKB = (backup.size / 1024).toFixed(2);
            console.log(`  ${index + 1}. ${backup.path} (${fileSizeKB} KB, ${relativeTime})`);
        });
    }
    
    console.log(`\nBackup location: ${BACKUP_DIR}`);
    process.exit(0);
}

if (args.includes('--restore')) {
    const backupFiles = listBackupFiles();
    
    if (backupFiles.length === 0) {
        console.log('❌ No backup files found. Run a backup first.');
        process.exit(1);
    }
    
    console.log('📋 Available backup files:\n');
    backupFiles.forEach((backup, index) => {
        const relativeTime = new Date(backup.mtime).toLocaleString();
        const fileSizeKB = (backup.size / 1024).toFixed(2);
        console.log(`  ${index + 1}. ${backup.path} (${fileSizeKB} KB, ${relativeTime})`);
    });
    
    console.log(`\nRestoring all backup files...`);
    
    const requireConfirmation = args.includes('--confirm');
    
    (async () => {
        try {
            const success = await restoreFromBackup(null, requireConfirmation);
            if (success) {
                console.log('\n✅ Files restored successfully!');
                process.exit(0);
            } else {
                console.log('\n❌ Restore failed!');
                process.exit(1);
            }
        } catch (error) {
            console.error('\n❌ Error during restore:', error.message);
            process.exit(1);
        }
    })();
} else {
    // Run the backup
    (async () => {
        try {
            await runBackup();
        } catch (error) {
            console.error('\n❌ Error during backup:', error.message);
            process.exit(1);
        }
    })();
}