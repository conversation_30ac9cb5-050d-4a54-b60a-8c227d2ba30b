const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Path to orders.json
const ordersFilePath = path.join(process.cwd(), 'data', 'orders.json');

// Test order data
const testOrder = {
  customerId: "TEST_CUSTOMER_001",
  items: [
    {
      name: "Test Product 1",
      price: 29.99,
      quantity: 2,
      sku: "TEST-SKU-001"
    }
  ],
  billingAddress: {
    street: "123 Test St",
    city: "Test City",
    state: "TS",
    zip: "12345",
    country: "Test Country"
  },
  shippingAddress: {
    street: "123 Test St",
    city: "Test City",
    state: "TS",
    zip: "12345",
    country: "Test Country"
  },
  paymentMethod: "convenience_store",
  paymentSubMethod: "family_mart",
  currency: "NT$",
  subtotal: 59.98,
  tax: 4.8,
  shipping: 5.99,
  total: 70.77,
  status: "test",
  notes: "This is a test order"
};

// Function to read orders from file
function readOrders() {
  try {
    if (fs.existsSync(ordersFilePath)) {
      const data = fs.readFileSync(ordersFilePath, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading orders:', error);
    return [];
  }
}

// Function to verify order was saved
function verifyOrderSaved(orderId) {
  const orders = readOrders();
  const savedOrder = orders.find(order => order.id === orderId);
  
  if (savedOrder) {
    console.log('✅ Order successfully saved to orders.json');
    console.log('Order details:', JSON.stringify(savedOrder, null, 2));
    return true;
  } else {
    console.error('❌ Order not found in orders.json');
    return false;
  }
}

// Main test function
async function runTest() {
  console.log('🧪 Starting order storage test...');
  
  try {
    // 1. Submit the test order
    console.log('Submitting test order...');
    const response = await axios.post('http://localhost:3000/api/orders', testOrder);
    
    if (response.status === 201) {
      console.log('✅ Order submitted successfully');
      const orderId = response.data.id;
      
      // 2. Verify the order was saved
      console.log('Verifying order storage...');
      const isSaved = verifyOrderSaved(orderId);
      
      if (isSaved) {
        console.log('✅ Test completed successfully');
      } else {
        console.error('❌ Test failed: Order not found in storage');
      }
    } else {
      console.error('❌ Failed to submit order:', response.status);
    }
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
runTest(); 