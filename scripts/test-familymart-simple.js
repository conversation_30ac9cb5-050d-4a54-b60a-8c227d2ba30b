/**
 * Simple FamilyMart Payment Notification Test Script
 * 
 * This script sends a single test payment notification to your FamilyMart callback endpoint.
 * It uses the configured credentials to test authentication.
 * 
 * Run with: node scripts/test-familymart-simple.js [status_code] [url]
 * 
 * Where:
 * - status_code: Payment status (0=success, 1=pending, 2=expired, 3=cancelled) (default: 0)
 * - url: The callback URL (default: http://localhost:3000/api/payment/familymart-callback)
 * 
 * Example: node scripts/test-familymart-simple.js 0 https://your-domain.com/api/payment/familymart-callback
 */

const axios = require('axios');
const crypto = require('crypto');

// Parse command line arguments
const statusCode = process.argv[2] || '0';
const callbackUrl = process.argv[3] || 'http://localhost:3000/api/payment/familymart-callback';

// Merchant credentials - these should match what's in your callback endpoint
const MERCHANT_ID = 'MAGSHOP_TEST';
const MERCHANT_SECRET = 'Mw9p7QxR6aT2sZ8e';

// Generate a test order
const testOrder = {
  orderNo: `TEST-FM-${Date.now()}`,
  pinCode: `FM${Math.floor(10000000 + Math.random() * 90000000)}`,
  amount: '1500',
  storeId: 'FAM1234'
};

// Generate security token using the same algorithm as in the callback endpoint
function generateSecurityToken() {
  return crypto
    .createHash('md5')
    .update(`${MERCHANT_ID}|${MERCHANT_SECRET}`)
    .digest('hex')
    .toUpperCase();
}

// Send a test notification
async function sendTestNotification() {
  // Current date and time
  const now = new Date();
  const orderDate = now.toISOString().replace(/T/, ' ').replace(/\..+/, '');
  const paymentDate = new Date(now.getTime() + 1000 * 60 * 5) // 5 minutes later
    .toISOString().replace(/T/, ' ').replace(/\..+/, '');
  
  // Calculate security token
  const securityToken = generateSecurityToken();
  
  // Create payload
  const payload = {
    EC_ID: MERCHANT_ID,
    PIN_CODE: testOrder.pinCode,
    ORDER_NO: testOrder.orderNo,
    ORDER_DATE: orderDate,
    AMOUNT: testOrder.amount,
    STATUS_CODE: statusCode,
    PAYMENT_NO: `PAY${Date.now()}`,
    PAYMENT_DATE: paymentDate,
    STORE_ID: testOrder.storeId,
    BARCODE1: `B1${Math.floor(Math.random() * 1000000000000)}`,
    BARCODE2: `B2${Math.floor(Math.random() * 1000000000000)}`,
    BARCODE3: `B3${Math.floor(Math.random() * 1000000000000)}`,
    SECURITY_TOKEN: securityToken
  };
  
  console.log('\n📤 Sending FamilyMart payment notification:');
  console.log('✅ URL:', callbackUrl);
  console.log('✅ Status Code:', getStatusLabel(statusCode));
  console.log('✅ Merchant ID:', MERCHANT_ID);
  console.log('✅ Order No:', testOrder.orderNo);
  console.log('✅ PIN Code:', testOrder.pinCode);
  
  try {
    // Make the POST request
    const response = await axios.post(callbackUrl, payload, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    // Display response
    console.log('\n✅ Response received:');
    console.log(`Status: ${response.status}`);
    console.log('Headers:', response.headers);
    console.log('Body:', response.data);
    
    console.log('\n🎉 Test completed successfully!');
  } catch (error) {
    console.error('\n❌ Error sending notification:');
    
    if (error.response) {
      // The request was made and the server responded with a status code outside 2xx
      console.error(`Status: ${error.response.status}`);
      console.error('Headers:', error.response.headers);
      console.error('Body:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received. Is your server running?');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }
    
    console.log('\n❌ Test failed.');
    process.exit(1);
  }
}

// Get a label for the status code
function getStatusLabel(code) {
  const statuses = {
    '0': 'Payment Success',
    '1': 'Payment Pending',
    '2': 'Payment Expired',
    '3': 'Payment Cancelled'
  };
  
  return statuses[code] || `Unknown Status (${code})`;
}

// Run the test
console.log('🚀 Starting FamilyMart Payment Notification Test...');
sendTestNotification(); 