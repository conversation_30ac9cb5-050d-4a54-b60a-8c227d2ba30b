const axios = require('axios');
const crypto = require('crypto');
const config = require('../../components/taiwan/payment/methods/711/config.json');

// Maximum number of retry attempts
const MAX_RETRIES = 5;
// Delay between retries in milliseconds
const RETRY_DELAY = 3000;

// Helper for logging
const log = (message, data = null) => {
  console.log(`[TEST] ${message}`, data ? JSON.stringify(data) : '');
};

// Sleep function for delays
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Get authentication token
async function getAuthToken(merchantID, apiUrl) {
  // Use the platform login password from documentation
  const CARD_PASSWORD = "@a827315300001";
  
  log('Requesting authentication token with platform login password', { 
    merchantID,
    tokenEndpoint: `${apiUrl}/Token`
  });
  
  try {
    // Use the CARD_PASSWORD for authentication directly
    const tokenResponse = await axios.post(
      `${apiUrl}/Token`,
      `grant_type=password&username=${merchantID}&password=${CARD_PASSWORD}`,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    if (!tokenResponse.data || !tokenResponse.data.access_token) {
      log('Failed to obtain authentication token', tokenResponse.data);
      throw new Error('Failed to obtain authentication token');
    }
    
    // Log success (but mask most of the token)
    const token = tokenResponse.data.access_token;
    log('Card authentication token received', {
      tokenLength: token.length,
      tokenStart: token.substring(0, 10) + '...'
    });
    
    return token;
  } catch (error) {
    // Log and re-throw the error
    log('Card authentication failed', {
      error: error.message,
      response: error.response?.data
    });
    throw error;
  }
}

// Make a card payment request
async function makeCardPaymentRequest(retryCount = 0, endpointVersion = 0) {
  try {
    // Define endpoint versions to try
    const endpointVersions = [
      '/api/Collect',      // Version 0: Original endpoint
      '/Collect',          // Version 1: No /api prefix
      '',                  // Version 2: No path (just base URL)
      '/api',              // Version 3: Just /api
      '/api/test',         // Version 4: /api/test
      '/test/api/Collect'  // Version 5: Another variation
    ];
    
    // Get current endpoint to try
    const currentEndpoint = endpointVersions[endpointVersion];
    
    log(`Attempt ${retryCount + 1} of ${MAX_RETRIES} (using endpoint: ${currentEndpoint || 'base URL only'})`);
    
    const { tw: { tw711: { card } } } = config;

    // Card payment specific configuration
    const CARD_MERCHANT_ID = card.merchantID;
    
    // Override with the exact values from documentation
    const CARD_PASSWORD = "W7529992P$"; // API password from documentation
    const CARD_LINK_ID = "izmIFZckOoHe"; // From documentation
    const CARD_HASH_BASE = "l9TH7sAMJsAZSJXg"; // From documentation
    
    const CARD_TEST_URL = card.testUrl;
    const CARD_RETURN_URL = card.returnURL;
    const CARD_NOTIFY_URL = card.notifyURL;
    const HASH_ALGORITHM = card.security.hashAlgorithm;
    const HASH_FORMAT = card.security.hashFormat;

    // Log the values we're using
    log('Using configuration values', {
      merchantID: CARD_MERCHANT_ID,
      linkID: CARD_LINK_ID,
      hashBase: CARD_HASH_BASE.substring(0, 3) + '...' // Only show first few chars for security
    });

    // Set the API URL to test URL
    const API_URL = CARD_TEST_URL;
    
    log('Using API endpoint', { 
      url: API_URL,
      environment: 'test'
    });

    // Get authentication token first
    const authToken = await getAuthToken(CARD_MERCHANT_ID, API_URL);

    // Create test order data
    const MerchantTradeNo = `TEST_${Date.now()}`;
    const TotalAmount = 100; // Small amount for testing
    const CustomerName = "Test User";
    const CustomerEmail = "<EMAIL>";
    const CustomerPhone = "0912345678";
    const TradeDesc = "Test payment";
    const ItemName = "Test item";

    // Calculate expiry date (7 days from now)
    const now = new Date();
    const expiryDate = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days expiry
    const expireDateString = expiryDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    
    // Generate timestamp in the required format
    const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14);
    
    // Create the data string for hash generation
    const dataString = `${MerchantTradeNo}|${TotalAmount}|${timestamp}|${CustomerName}|${CustomerEmail}`;
    
    // Generate hash using merchant-specific parameters
    const hashString = HASH_FORMAT
      .replace('${cust_id}', CARD_MERCHANT_ID)
      .replace('${data}', dataString)
      .replace('${hash_base}', CARD_HASH_BASE);
    
    const hash = crypto.createHash(HASH_ALGORITHM).update(hashString).digest('hex');
    
    // Prepare the API request payload
    const payload = {
      cmd: "CvsOrderAppend2",  // Required command name for the API
      cust_id: CARD_MERCHANT_ID,
      cust_order_no: MerchantTradeNo,
      order_amount: TotalAmount,
      expire_date: expireDateString,  // Required payment expiry date
      timestamp: timestamp,
      link_id: CARD_LINK_ID,
      password: CARD_PASSWORD,
      payer_name: CustomerName,
      payer_email: CustomerEmail,
      payer_mobile: CustomerPhone,
      customer_address: '',
      order_detail: ItemName || TradeDesc || 'Test item',
      test_mode: '1', // Always use test mode
      return_url: CARD_RETURN_URL,
      notify_url: CARD_NOTIFY_URL,
      payment_type: '0',
      hash: hash
    };

    log(`Processing payment for test order ${MerchantTradeNo}`, {
      timestamp,
      testMode: payload.test_mode,
      returnUrl: CARD_RETURN_URL,
      notifyUrl: CARD_NOTIFY_URL
    });
    
    // Make the API request to the 7-11 card payment gateway
    const apiPath = currentEndpoint;
    const fullEndpoint = `${API_URL}${apiPath}`;
    
    log('Making API request', { 
      endpoint: fullEndpoint,
      endpointVersion,
      hasAuthToken: !!authToken
    });
    
    const paymentResponse = await axios.post(
      fullEndpoint, 
      payload, 
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        timeout: 30000 // 30 second timeout
      }
    );
    
    log('Raw response received', {
      status: paymentResponse.status,
      statusText: paymentResponse.statusText,
      data: paymentResponse.data
    });
    
    if (paymentResponse.data && paymentResponse.data.resultCode === '0000') {
      // Successful response
      log('SUCCESS! Payment URL generated successfully', {
        paymentUrl: paymentResponse.data.paymentUrl,
        shortUrl: paymentResponse.data.shortUrl || 'N/A'
      });
      
      console.log("\n=== PAYMENT URL OBTAINED SUCCESSFULLY ===");
      console.log(`Order Number: ${MerchantTradeNo}`);
      console.log(`Payment URL: ${paymentResponse.data.paymentUrl}`);
      if (paymentResponse.data.shortUrl) {
        console.log(`Short URL: ${paymentResponse.data.shortUrl}`);
      }
      console.log("====================================\n");
      
      return true;
    } else {
      // API returned an error
      const errorMsg = paymentResponse.data?.message || 'Payment provider returned an error';
      const resultCode = paymentResponse.data?.resultCode || 'unknown';
      
      log('Error processing payment', { 
        resultCode,
        message: errorMsg,
        data: paymentResponse.data
      });
      
      // Check if this is a data exchange error that we should retry
      let shouldRetry = false;
      let message = errorMsg;
      
      // Extract error message from response if available
      if (paymentResponse.data && paymentResponse.data.msg) {
        message = paymentResponse.data.msg;

        // Check for data exchange errors that we should retry
        if (paymentResponse.data.msg.includes('T-ibon資料交換異常') || 
            paymentResponse.data.msg.includes('T-ibon data exchange exception') ||
            paymentResponse.data.msg.includes('data exchange')) {
          log('Data exchange error detected, will retry', { 
            originalMessage: paymentResponse.data.msg
          });
          shouldRetry = true;
        }
      }
      
      // Try next endpoint version if available
      if (endpointVersion < endpointVersions.length - 1) {
        log(`Trying next endpoint format (${endpointVersions[endpointVersion + 1]})...`);
        await sleep(1000);
        return makeCardPaymentRequest(retryCount, endpointVersion + 1);
      }
      
      if (shouldRetry && retryCount < MAX_RETRIES - 1) {
        log(`Retrying in ${RETRY_DELAY / 1000} seconds...`);
        await sleep(RETRY_DELAY);
        return makeCardPaymentRequest(retryCount + 1, 0); // Reset to first endpoint
      } else if (retryCount >= MAX_RETRIES - 1) {
        log('Maximum retry attempts reached');
        console.error(`Failed to get payment URL after ${MAX_RETRIES} attempts.`);
        return false;
      } else {
        log('Non-retryable error');
        console.error(`Error: ${message}`);
        return false;
      }
    }
  } catch (error) {
    log('Request error', {
      message: error.message,
      response: error.response?.data
    });
    
    // Try next endpoint version if available
    const endpointVersions = [
      '/api/Collect',      // Version 0: Original endpoint
      '/Collect',          // Version 1: No /api prefix
      '',                  // Version 2: No path (just base URL)
      '/api',              // Version 3: Just /api
      '/api/test',         // Version 4: /api/test
      '/test/api/Collect'  // Version 5: Another variation
    ];
    
    if (endpointVersion < endpointVersions.length - 1) {
      log(`Trying next endpoint format (${endpointVersions[endpointVersion + 1]}) due to error...`);
      await sleep(1000);
      return makeCardPaymentRequest(retryCount, endpointVersion + 1);
    }
    
    if (retryCount < MAX_RETRIES - 1) {
      log(`Retrying in ${RETRY_DELAY / 1000} seconds...`);
      await sleep(RETRY_DELAY);
      return makeCardPaymentRequest(retryCount + 1, 0); // Reset to first endpoint
    } else {
      log('Maximum retry attempts reached');
      console.error(`Failed to get payment URL after ${MAX_RETRIES} attempts.`);
      return false;
    }
  }
}

// Main function to execute the test
async function main() {
  console.log("=== 7-11 CARD PAYMENT TEST ===");
  console.log("Making test payment requests until successful...\n");
  
  try {
    await makeCardPaymentRequest();
  } catch (error) {
    console.error('Fatal error:', error.message);
  }
}

// Run the main function
main(); 