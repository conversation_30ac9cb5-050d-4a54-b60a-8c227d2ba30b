// A script to test the cart API routes
const fs = require('fs');
const path = require('path');
const http = require('http');

console.log('Starting cart API check script...');

// Paths
const dataDir = path.join(process.cwd(), 'data', 'carts');
const cartsFilePath = path.join(dataDir, 'carts.json');

// Check if data directory exists
console.log('Checking if data directory exists...');
if (!fs.existsSync(dataDir)) {
  console.log('Creating data directory:', dataDir);
  fs.mkdirSync(dataDir, { recursive: true });
} else {
  console.log('Data directory exists:', dataDir);
}

// Check if carts.json file exists
console.log('Checking if carts.json file exists...');
if (!fs.existsSync(cartsFilePath)) {
  console.log('Creating empty carts.json file...');
  fs.writeFileSync(cartsFilePath, JSON.stringify({ carts: {} }, null, 2));
  console.log('Created empty carts.json file.');
} else {
  console.log('carts.json file exists.');
  try {
    const fileContents = fs.readFileSync(cartsFilePath, 'utf8');
    const parsedData = JSON.parse(fileContents);
    console.log('Successfully parsed carts.json:', {
      cartCount: Object.keys(parsedData.carts || {}).length
    });
  } catch (error) {
    console.error('Error reading or parsing carts.json:', error);
  }
}

// Test creating a test cart via direct file write
console.log('Attempting to write test cart directly to file...');
try {
  const testCartId = 'test_user_test_store';
  let cartsData;
  
  if (fs.existsSync(cartsFilePath)) {
    const fileData = fs.readFileSync(cartsFilePath, 'utf8');
    cartsData = JSON.parse(fileData);
  } else {
    cartsData = { carts: {} };
  }
  
  cartsData.carts[testCartId] = {
    userId: 'test_user',
    storeId: 'test_store',
    cart: [
      {
        id: 'test_item',
        name: 'Test Item',
        price: 1000,
        currency: 'USD',
        quantity: 1,
        sku: 'TEST001',
        store: 'test_store'
      }
    ],
    numberOfItemsInCart: 1,
    total: 1000,
    lastUpdated: new Date().toISOString()
  };
  
  fs.writeFileSync(cartsFilePath, JSON.stringify(cartsData, null, 2));
  console.log('Successfully wrote test cart to file.');
} catch (error) {
  console.error('Error writing test cart to file:', error);
}

console.log('Cart API check completed.');

// Function to simulate an API call to our local endpoint
function makeLocalApiCall(method, path, body = null) {
  return new Promise((resolve, reject) => {
    console.log(`Making ${method} request to ${path}...`);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`Response status: ${res.statusCode}`);
        try {
          const parsedData = JSON.parse(data);
          console.log('Response data:', parsedData);
          resolve({ statusCode: res.statusCode, data: parsedData });
        } catch (error) {
          console.log('Response (raw):', data);
          resolve({ statusCode: res.statusCode, data });
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('Error making request:', error);
      reject(error);
    });
    
    if (body) {
      req.write(JSON.stringify(body));
    }
    
    req.end();
  });
}

// Test API endpoints if server is running
console.log('\nNOTE: To test the API endpoints, you need to have your Next.js server running.');
console.log('If it\'s running, you can remove the return statement below and run this script again.');

// Remove the line below to actually make API calls to your running server
return;

async function testApiEndpoints() {
  try {
    // Test the sync endpoint
    const syncResponse = await makeLocalApiCall('POST', '/api/cart/sync', {
      userId: 'api_test_user',
      storeId: 'api_test_store',
      cart: [
        {
          id: 'api_test_item',
          name: 'API Test Item',
          price: 2000,
          currency: 'USD',
          quantity: 2,
          sku: 'API_TEST001',
          store: 'api_test_store'
        }
      ],
      numberOfItemsInCart: 1,
      total: 4000
    });
    
    // Test the get endpoint
    const getResponse = await makeLocalApiCall('GET', '/api/cart/get?userId=api_test_user&storeId=api_test_store');
    
    console.log('API tests completed.');
  } catch (error) {
    console.error('Error during API tests:', error);
  }
}

testApiEndpoints(); 