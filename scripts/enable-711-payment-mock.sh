#!/bin/bash

# Enable mock mode for 7-11 card payment testing
# This script sets the environment variable to use mock mode
# which returns simulated successful responses without making 
# actual API calls to the payment gateway.

echo "========================================================"
echo "      7-ELEVEN CARD PAYMENT MOCK MODE ENABLER"
echo "========================================================"
echo ""
echo "This script will:"
echo "  1. Create a .env.local file with mock mode enabled"
echo "  2. Restart the Next.js server with mock mode"
echo ""
echo "Press Enter to continue or Ctrl+C to cancel..."
read

# Create or update .env.local file
echo "Creating .env.local file with mock mode enabled..."
if [ -f .env.local ]; then
  # Check if the variable is already set
  if grep -q "PAYMENT_MOCK_MODE" .env.local; then
    # Update existing variable
    sed -i '' 's/PAYMENT_MOCK_MODE=.*/PAYMENT_MOCK_MODE=true/' .env.local
  else
    # Add variable to existing file
    echo "PAYMENT_MOCK_MODE=true" >> .env.local
  fi
else
  # Create new file
  echo "PAYMENT_MOCK_MODE=true" > .env.local
fi

echo "✅ Mock mode environment variable set"

# Check if the server is running
if pgrep -f "next dev" > /dev/null; then
  echo "Stopping existing Next.js server..."
  pkill -f "next dev"
  sleep 2
fi

echo ""
echo "========================================================"
echo "  Mock mode is now enabled for 7-11 card payments"
echo "  To start the server with mock mode, run:"
echo "  npm run dev"
echo ""
echo "  To test a payment, proceed to checkout and select"
echo "  the 7-11 card payment option."
echo "========================================================"
echo ""
echo "For testing, you can use these card numbers:"
echo "  ✓ Visa: **************** or ****************"
echo "  ✓ JCB: 3560512000000001"
echo "  ✓ UnionPay: 6200000000000001"
echo ""
echo "Use any future expiry date and any 3-digit CVV"
echo "========================================================" 