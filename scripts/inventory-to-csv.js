const fs = require('fs');
const path = require('path');

// Read the inventory JSON file
const inventory = require('../utils/inventory.json');

// Define the columns you want in your CSV
const columns = [
  'sku',
  'name',
  'categories',
  'price',
  'currency',
  'description',
  'provider',
  'activestatus',
  'currentInventory',
  'image',
  'validity',
  'territory',
  'unit',
  'buyfrom',
  'supplier_price'
];

// Create CSV header row
let csvContent = columns.join(',') + '\n';

// Process each inventory item
inventory.forEach(item => {
  const row = columns.map(column => {
    let value = item[column];
    
    // Handle arrays (like categories and image)
    if (Array.isArray(value)) {
      value = value.join('|');
    }
    
    // Handle null or undefined values
    if (value === null || value === undefined) {
      return '';
    }

    // Handle strings that might contain commas or quotes
    if (typeof value === 'string') {
      // Escape quotes and wrap in quotes if contains comma or quote
      if (value.includes(',') || value.includes('"')) {
        value = '"' + value.replace(/"/g, '""') + '"';
      }
      
      // Clean up any newlines or tabs that might break the CSV
      value = value.replace(/\n/g, ' ').replace(/\r/g, ' ').replace(/\t/g, ' ');
    }
    
    return value;
  });
  
  // Add the row to CSV content
  csvContent += row.join(',') + '\n';
});

// Write to a CSV file
const outputPath = path.join(__dirname, 'inventory-export.csv');
fs.writeFileSync(outputPath, csvContent);

console.log(`CSV file has been created at: ${outputPath}`); 