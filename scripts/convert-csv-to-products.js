const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Path to your CSV file
const csvFilePath = path.join(__dirname, 'SIMTHE.csv');
// Output JSON files
const outputFilePath = path.join(__dirname, 'converted-products.json');
const skuListFilePath = path.join(__dirname, 'converted-products-skus.json');

const results = [];

// Helper function to convert category string to array
function parseCategory(categoryStr) {
  // Extract main categories from the dot-separated format
  const parts = categoryStr.split('.');
  // Create an array with the main category types
  const categories = [];
  
  // Add territory if present (taiwan, vietnam, etc.)
  if (parts[0] && !['prepaid', 'postpaid'].includes(parts[0])) {
    categories.push(parts[0].charAt(0).toUpperCase() + parts[0].slice(1));
  }
  
  // Add service type if present
  if (parts[1]) {
    categories.push(parts[1].charAt(0).toUpperCase() + parts[1].slice(1));
  }
  
  // If no categories were extracted, use the original string
  if (categories.length === 0) {
    categories.push('Other');
  }
  
  return categories;
}

// Helper function to parse prices with dot separators (e.g., 1.033.000 -> 1033000)
function parsePrice(priceStr) {
  if (!priceStr) return 0;
  
  // Handle empty string or non-string values
  const price = priceStr.toString().trim();
  if (price === '') return 0;
  
  // If the price contains dots as thousand separators
  if (price.includes('.') && /^\d+(\.\d{3})+$/.test(price)) {
    // Remove all dots and convert to number
    return parseFloat(price.replace(/\./g, ''));
  }
  
  // For comma as decimal separator (e.g., 1,5 -> 1.5)
  if (price.includes(',') && !price.includes('.')) {
    return parseFloat(price.replace(',', '.'));
  }
  
  // Default parsing
  return parseFloat(price);
}

fs.createReadStream(csvFilePath)
  .pipe(csv())
  .on('data', (data) => {
    // Get the base categories from the category string
    const baseCategories = parseCategory(data.category);
    
    // Create full categories array including provider and territory
    const categories = [...baseCategories];
    
    // Add provider as a category if it exists
    if (data.provider && !categories.includes(data.provider)) {
      categories.push(data.provider);
    }
    
    // Add territory as a category if it exists and isn't already included
    if (data.territory && data.territory.trim() !== '' && !categories.includes(data.territory)) {
      categories.push(data.territory);
    }
    
    // Parse the price with our custom function
    const parsedPrice = parsePrice(data.price);
    
    // Transform CSV row to product object
    const product = {
      buyfrom: data.provider || 'MAG',
      name: data.name || '',
      categories: categories,
      unit: 'Cái',
      description: data.description || '',
      currency: data.currency || 'NT',
      supplier_price: 0, // Assuming supplier price is 85% of retail
      competitor_worst_price: 0,
      competitor_best_price: 0,
      competitor_best_name: '',
      price: parsedPrice, // Use our custom parsed price
      skuproxy: '',
      sku: data.sku || '',
      activestatus: '1',
      currentInventory: 100, // Default inventory
      image: data.image ? [data.image] : [
        'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.1.png'
      ],
      provider: data.provider || 'MAG',
      discount_supplierdiscount: 0,
      discount_affiliatemax: '',
      discount_discountmax: '',
      discount_period: '',
      discountspecial_supplierdiscount: '',
      discountspecial_affiliatemax: '',
      discountspecial_discountmax: '',
      discountspecial_period: ''
    };
    
    // Add configurations if needed based on product type
    if (data.category.includes('prepaid') || data.category.includes('topup')) {
      product.configurations = {
        mobiletopup: {
          purpose: 'input',
          required: true,
          datatype: 'string',
          label: 'Số di động',
          run: {
            function: data.category.includes('taiwan') ? 'taiwanmobile' : 'checkmobile',
            label: 'KIỂM TRA'
          }
        },
        note: {
          purpose: 'display',
          content: 'Cung cấp đúng số để tránh nhầm lẫn trong quá trình nạp tiền'
        }
      };
    }
    
    // Add validity if present
    if (data.validity) {
      product.validity = data.validity;
    }
    
    // Add territory if present
    if (data.territory) {
      product.territory = data.territory;
    }
    
    // Add notes if present
    if (data.notes) {
      product.notes = data.notes;
    }
    
    results.push(product);
  })
  .on('end', () => {
    // Write the full product data to a JSON file
    fs.writeFileSync(outputFilePath, JSON.stringify(results, null, 2));
    console.log(`Conversion complete! ${results.length} products saved to ${outputFilePath}`);
    
    // Create SKU list in the format for multistores.json
    const skuList = results.map(product => ({ sku: product.sku }));
    fs.writeFileSync(skuListFilePath, JSON.stringify(skuList, null, 2));
    console.log(`SKU list with ${skuList.length} items saved to ${skuListFilePath}`);
  })
  .on('error', (error) => {
    console.error('Error processing CSV:', error);
  }); 