const fs = require('fs');
const path = require('path');

// Read the current inventory.json
const inventoryPath = path.join(__dirname, '..', 'utils', 'inventory.json');
const inventory = JSON.parse(fs.readFileSync(inventoryPath, 'utf8'));

// Function to determine inventory type based on product categories
function getInventoryType(product) {
  const categories = product.categories || [];
  const categoryString = categories.join(' ').toLowerCase();
  
  // Products that should use 'separate' type (SIM cards, top-up codes, etc.)
  const separateKeywords = ['card', 'sim', 'topup', 'prepaid', 'voucher', 'code'];
  
  for (const keyword of separateKeywords) {
    if (categoryString.includes(keyword)) {
      return 'separate';
    }
  }
  
  // Default to 'same' type for other products
  return 'same';
}

// Update each product in the inventory
const updatedInventory = inventory.map(product => {
  // Skip if already updated
  if (product.inventory && typeof product.inventory === 'object') {
    return product;
  }
  
  const inventoryType = getInventoryType(product);
  const currentInventory = product.currentInventory || 0;
  
  // Create new inventory structure
  const newInventory = {
    type: inventoryType,
    count: currentInventory
  };
  
  // For 'separate' type, add empty items array
  if (inventoryType === 'separate') {
    newInventory.items = [];
  }
  
  // Remove old currentInventory field and add new inventory structure
  const { currentInventory: _, ...productWithoutOldInventory } = product;
  
  return {
    ...productWithoutOldInventory,
    inventory: newInventory
  };
});

// Write the updated inventory back to the file
fs.writeFileSync(inventoryPath, JSON.stringify(updatedInventory, null, 2));

console.log('Inventory structure updated successfully!');
console.log(`Updated ${updatedInventory.length} products`);

// Show summary of inventory types
const typeCounts = updatedInventory.reduce((acc, product) => {
  const type = product.inventory?.type || 'unknown';
  acc[type] = (acc[type] || 0) + 1;
  return acc;
}, {});

console.log('\nInventory type distribution:');
Object.entries(typeCounts).forEach(([type, count]) => {
  console.log(`  ${type}: ${count} products`);
});

// Show some examples
console.log('\nExamples of updated products:');
const separateExample = updatedInventory.find(p => p.inventory?.type === 'separate');
const sameExample = updatedInventory.find(p => p.inventory?.type === 'same');

if (separateExample) {
  console.log('\nSeparate type example:');
  console.log(`  SKU: ${separateExample.sku}`);
  console.log(`  Name: ${separateExample.name}`);
  console.log(`  Categories: ${separateExample.categories?.join(', ')}`);
  console.log(`  Inventory: ${JSON.stringify(separateExample.inventory, null, 4)}`);
}

if (sameExample) {
  console.log('\nSame type example:');
  console.log(`  SKU: ${sameExample.sku}`);
  console.log(`  Name: ${sameExample.name}`);
  console.log(`  Categories: ${sameExample.categories?.join(', ')}`);
  console.log(`  Inventory: ${JSON.stringify(sameExample.inventory, null, 4)}`);
}