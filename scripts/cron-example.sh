#!/bin/bash

# MAG Shop Database Maintenance Cron Job Example
# This script can be run via cron for automated database maintenance

# Set the working directory to your shop root
SHOP_DIR="/Users/<USER>/Workspace/mag.group.shop"
cd "$SHOP_DIR" || exit 1

# Log file for cron job output
LOG_FILE="$SHOP_DIR/logs/db-maintenance-$(date +%Y%m%d).log"

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Start maintenance
log "Starting automated database maintenance"

# 1. Create daily backup (only keep last 7 days)
log "Creating daily backup..."
if node "$SHOP_DIR/scripts/db-management.js" > /tmp/db-backup.tmp 2>&1 <<EOF
11
0
EOF
then
    log "✅ Daily backup completed successfully"
    cat /tmp/db-backup.tmp >> "$LOG_FILE"
else
    log "❌ Daily backup failed"
    cat /tmp/db-backup.tmp >> "$LOG_FILE"
fi

# 2. Cleanup old logs (runs without dry run)
log "Cleaning old logs..."
if node "$SHOP_DIR/scripts/db-management.js" > /tmp/db-cleanup-logs.tmp 2>&1 <<EOF
8
n
0
EOF
then
    log "✅ Log cleanup completed successfully"
    cat /tmp/db-cleanup-logs.tmp >> "$LOG_FILE"
else
    log "❌ Log cleanup failed"
    cat /tmp/db-cleanup-logs.tmp >> "$LOG_FILE"
fi

# 3. Cleanup old carts (runs without dry run)
log "Cleaning old carts..."
if node "$SHOP_DIR/scripts/db-management.js" > /tmp/db-cleanup-carts.tmp 2>&1 <<EOF
7
n
0
EOF
then
    log "✅ Cart cleanup completed successfully"
    cat /tmp/db-cleanup-carts.tmp >> "$LOG_FILE"
else
    log "❌ Cart cleanup failed"
    cat /tmp/db-cleanup-carts.tmp >> "$LOG_FILE"
fi

# 4. Weekly: Cleanup expired orders (only on Sundays)
if [ "$(date +%u)" -eq 7 ]; then
    log "Running weekly expired orders cleanup..."
    if node "$SHOP_DIR/scripts/db-management.js" > /tmp/db-cleanup-expired.tmp 2>&1 <<EOF
4
n
0
EOF
    then
        log "✅ Expired orders cleanup completed successfully"
        cat /tmp/db-cleanup-expired.tmp >> "$LOG_FILE"
    else
        log "❌ Expired orders cleanup failed"
        cat /tmp/db-cleanup-expired.tmp >> "$LOG_FILE"
    fi
fi

# 5. Monthly: Cleanup orphaned customers (only on 1st of month)
if [ "$(date +%d)" -eq 1 ]; then
    log "Running monthly orphaned customers cleanup..."
    if node "$SHOP_DIR/scripts/db-management.js" > /tmp/db-cleanup-orphaned.tmp 2>&1 <<EOF
6
n
0
EOF
    then
        log "✅ Orphaned customers cleanup completed successfully"
        cat /tmp/db-cleanup-orphaned.tmp >> "$LOG_FILE"
    else
        log "❌ Orphaned customers cleanup failed"
        cat /tmp/db-cleanup-orphaned.tmp >> "$LOG_FILE"
    fi
    
    # Create full backup on monthly maintenance
    log "Creating monthly full backup..."
    if node "$SHOP_DIR/scripts/db-management.js" > /tmp/db-backup-full.tmp 2>&1 <<EOF
10
0
EOF
    then
        log "✅ Monthly full backup completed successfully"
        cat /tmp/db-backup-full.tmp >> "$LOG_FILE"
    else
        log "❌ Monthly full backup failed"
        cat /tmp/db-backup-full.tmp >> "$LOG_FILE"
    fi
fi

# 6. Cleanup old backups (keep only last 30 days)
log "Cleaning old backups..."
find "$SHOP_DIR/backups" -type d -name "backup-*" -mtime +30 -exec rm -rf {} \; 2>/dev/null
if [ $? -eq 0 ]; then
    log "✅ Old backups cleanup completed"
else
    log "⚠️ Old backups cleanup had issues (may be normal if no old backups exist)"
fi

# 7. Cleanup old maintenance logs (keep only last 30 days)
find "$SHOP_DIR/logs" -name "db-maintenance-*.log" -mtime +30 -delete 2>/dev/null

# 8. Check disk space
DISK_USAGE=$(df "$SHOP_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
log "Current disk usage: ${DISK_USAGE}%"

if [ "$DISK_USAGE" -gt 85 ]; then
    log "⚠️ WARNING: Disk usage is above 85% - consider manual cleanup"
fi

# Cleanup temp files
rm -f /tmp/db-*.tmp

log "Automated database maintenance completed"

# Optional: Send email notification on errors (uncomment if needed)
# if grep -q "❌" "$LOG_FILE"; then
#     echo "Database maintenance errors detected. Check log: $LOG_FILE" | mail -s "MAG Shop DB Maintenance Errors" <EMAIL>
# fi 