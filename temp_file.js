import axios from 'axios';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../components/taiwan/payment/methods/711/config.json';

// Helper for consistent logging
const log711CardAPI = (message, data = null) => {
  console.log(`[7-11 CARD API] ${message}`, data ? JSON.stringify(data) : '');
};

// Helper function to get authentication token
async function getAuthToken(merchantID, apiUrl) {
