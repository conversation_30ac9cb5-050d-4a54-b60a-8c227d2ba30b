/**
 * APN Endpoint Test
 * 
 * This script tests the actual APN endpoint by sending payment notification payloads
 * that simulate real 7-Eleven payment callbacks.
 */

const crypto = require('crypto');
const axios = require('axios');

// Configuration
const CONFIG = {
  // Your actual APN endpoint (adjust as needed)
  apnUrl: 'http://localhost:3000/apn-callback',
  
  // Production URL (uncomment to test production)
  // apnUrl: 'https://mag.group.shop/api/payment/apn-callback',
  
  // API credentials (should match what's configured in your application)
  apiId: '827315300001',
  
  // Set to true to show detailed request/response information
  debug: true
};

// Generate MD5 checksum for APN verification
function generateChecksum(api_id, trans_id, amount, status, nonce) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  return crypto.createHash('md5').update(message).digest('hex');
}

// Generate random transaction ID
function generateTransactionId() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Generate random order number
function generateOrderNumber() {
  return `PO${Math.floor(1000000 + Math.random() * 9000000)}`;
}

// Generate timestamp in required format
function generateTimestamp() {
  return new Date().toISOString().replace('T', ' ').substring(0, 19);
}

// Generate random nonce
function generateNonce() {
  const now = new Date();
  const timeComponent = String(now.getHours()).padStart(2, '0') +
                         String(now.getMinutes()).padStart(2, '0') +
                         String(now.getSeconds()).padStart(2, '0');
  const randomComponent = String(Math.floor(1000 + Math.random() * 9000));
  return `${timeComponent}${randomComponent}`;
}

// Test cases for different payment scenarios
const TEST_CASES = [
  {
    name: 'Convenience Store Payment Success',
    request: () => {
      const api_id = CONFIG.apiId;
      const trans_id = generateTransactionId();
      const order_no = generateOrderNumber();
      const amount = 1250;
      const status = 'B'; // Paid by payer
      const nonce = generateNonce();
      
      const payload = {
        api_id,
        trans_id,
        order_no,
        amount,
        status,
        payment_code: 2, // CVS payment
        payment_detail: {
          pay_route: 'IbonPay',
          storeId: '123456',
          ibon_code: '405300000960'
        },
        create_time: generateTimestamp(),
        modify_time: generateTimestamp(),
        expire_time: generateTimestamp(),
        nonce,
        pay_date: generateTimestamp(),
        pay_amount: amount
      };
      
      // Generate and add checksum
      payload.checksum = generateChecksum(api_id, trans_id, amount, status, nonce);
      
      return payload;
    }
  },
  {
    name: 'Credit Card Payment Success',
    request: () => {
      const api_id = CONFIG.apiId;
      const trans_id = generateTransactionId();
      const order_no = generateOrderNumber();
      const amount = 2500;
      const status = 'B'; // Paid by payer
      const nonce = generateNonce();
      
      const payload = {
        api_id,
        trans_id,
        order_no,
        amount,
        status,
        payment_code: 1, // COCS payment
        payment_detail: {
          auth_card_no: '123456******1234',
          auth_code: '123456'
        },
        create_time: generateTimestamp(),
        modify_time: generateTimestamp(),
        expire_time: generateTimestamp(),
        nonce,
        pay_date: generateTimestamp(),
        pay_amount: amount
      };
      
      // Generate and add checksum
      payload.checksum = generateChecksum(api_id, trans_id, amount, status, nonce);
      
      return payload;
    }
  },
  {
    name: 'Payment Pending',
    request: () => {
      const api_id = CONFIG.apiId;
      const trans_id = generateTransactionId();
      const order_no = generateOrderNumber();
      const amount = 3500;
      const status = 'A'; // Waiting for payment
      const nonce = generateNonce();
      
      const payload = {
        api_id,
        trans_id,
        order_no,
        amount,
        status,
        payment_code: 2,
        payment_detail: {
          pay_route: 'IbonPay',
          ibon_code: '405300000961'
        },
        create_time: generateTimestamp(),
        modify_time: generateTimestamp(),
        expire_time: generateTimestamp(),
        nonce
      };
      
      // Generate and add checksum
      payload.checksum = generateChecksum(api_id, trans_id, amount, status, nonce);
      
      return payload;
    }
  },
  {
    name: 'Payment Expired',
    request: () => {
      const api_id = CONFIG.apiId;
      const trans_id = generateTransactionId();
      const order_no = generateOrderNumber();
      const amount = 1800;
      const status = 'D'; // Overdue payment
      const nonce = generateNonce();
      
      const payload = {
        api_id,
        trans_id,
        order_no,
        amount,
        status,
        payment_code: 2,
        payment_detail: {},
        create_time: generateTimestamp(),
        modify_time: generateTimestamp(),
        expire_time: generateTimestamp(),
        nonce
      };
      
      // Generate and add checksum
      payload.checksum = generateChecksum(api_id, trans_id, amount, status, nonce);
      
      return payload;
    }
  }
];

// Run a single test
async function runTest(testCase) {
  console.log(`\n========== Running Test: ${testCase.name} ==========`);
  
  const requestData = testCase.request();
  
  if (CONFIG.debug) {
    console.log('Request payload:');
    console.log(JSON.stringify(requestData, null, 2));
  }
  
  try {
    console.log(`Sending request to: ${CONFIG.apnUrl}`);
    const response = await axios.post(CONFIG.apnUrl, requestData);
    
    console.log(`Status Code: ${response.status}`);
    console.log(`Response: ${typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2)}`);
    
    return {
      name: testCase.name,
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    console.error(`Error: ${error.message}`);
    
    if (error.response) {
      console.log(`Status Code: ${error.response.status}`);
      console.log(`Response: ${typeof error.response.data === 'string' ? error.response.data : JSON.stringify(error.response.data, null, 2)}`);
    }
    
    return {
      name: testCase.name,
      success: false,
      error: error.message,
      response: error.response
    };
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting APN Endpoint Tests...');
  console.log(`Target URL: ${CONFIG.apnUrl}`);
  
  const results = [];
  
  for (const testCase of TEST_CASES) {
    try {
      const result = await runTest(testCase);
      results.push(result);
    } catch (error) {
      console.error(`Failed to run test "${testCase.name}": ${error.message}`);
      results.push({
        name: testCase.name,
        success: false,
        error: error.message
      });
    }
  }
  
  console.log('\n========== Test Results Summary ==========');
  
  let passCount = 0;
  for (const result of results) {
    const status = result.success ? '✅ PASSED' : '❌ FAILED';
    if (result.success) passCount++;
    console.log(`${status} - ${result.name}`);
  }
  
  console.log(`\nTotal: ${results.length}, Passed: ${passCount}, Failed: ${results.length - passCount}`);
  console.log('Tests completed.');
}

// Run the tests
runAllTests().catch(error => {
  console.error('Test runner error:', error);
}); 