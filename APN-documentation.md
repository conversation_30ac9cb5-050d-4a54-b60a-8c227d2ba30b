# 7-11 APN Implementation Details

## Overview
APN (Active Notification Service) is a callback mechanism used by the 7-Eleven payment system to notify our application about payment status changes. This document outlines the implementation details of the APN callback API.

## Configuration

- **APN URL**: `https://mag.group.shop/api/payment/apn-callback`
- **API Version**: 1.24.1/1.25.1 
- **Environment File Settings**: 
  ```
  NEXT_PUBLIC_APN_CALLBACK_URL="https://mag.group.shop/api/payment/apn-callback"
  ```
- **Configuration File Settings** (in `components/taiwan/payment/methods/711/config.json`): 
  ```json
  "apnURL": "https://mag.group.shop/api/payment/apn-callback"
  ```

## Expected Parameters from 7-11 APN Callback

- `api_id`: Customer code (827315300001)
- `trans_id`: Unique transaction ID
- `order_no`: Customer order number
- `amount`: Payment amount
- `status`: Status code (see status codes below)
- `payment_code`: Payment service type (1=COCS/Credit Card, 2=CVS/Convenience Store)
- `payment_detail`: Contains detailed payment information (varies by payment type)
- `create_time`: Order creation time
- `modify_time`: Status update time
- `expire_time`: Payment expiry time
- `nonce`: Random number for security
- `checksum`: Verification hash
- `pay_date`: Actual payment date
- `pay_amount`: Actual amount paid

## Security Verification

Authentication between our system and 7-Eleven's payment system relies on checksum verification. There is no traditional shared secret key. Instead, authentication depends on:

1. The `api_id` parameter (customer code "827315300001") which serves as an implicit identifier
2. A checksum calculation using the following formula:
```
checksum = MD5(api_id + ":" + trans_id + ":" + amount + ":" + status + ":" + nonce)
```

When our system receives an APN callback:
- It extracts all required parameters from the request
- Recalculates the checksum using the same formula
- Compares the calculated checksum with the one provided in the request
- Accepts the request only if checksums match

This approach provides basic security but relies on keeping the `api_id` private between our system and 7-Eleven. While it's not as strong as dedicated token-based authentication, it does ensure that only systems with knowledge of both the correct `api_id` and the checksum algorithm can send valid notifications.

SECURITY NOTE: This mechanism assumes that the `api_id` is kept confidential between both systems.

## Status Codes

| Code | Description | Internal Mapping |
|------|-------------|------------------|
| A | Waiting for payment | pending |
| B | Paid by payer | paid |
| C | Customer deregistered | failed |
| D | Overdue payment | expired |
| E | Funds reserved for contractor | paid |
| F | Authorization failed | failed |
| I | Invoice Notification | processing |
| J | Invoice Discount Number Notice | processing |
| M | Cancel transaction completed | cancelled |
| N | Cancel transaction failed | failed |
| O | Payment request in progress | processing |
| P | Payment request failed | failed |
| Q | Deauthorization completed | cancelled |
| R | Cancel authorization failed | failed |

## Response Format

Return `OK` (plain text) to stop repeated notifications. The APN transmitter sends notifications:
- Immediately after payment completion
- Every 15 minutes for up to 3 times if no "OK" response is received

## Payment Details

### Credit Card Payment (payment_code='1')
- `auth_card_no`: First 6 and last 4 digits of the credit card
- `auth_code`: Credit card authorization code
- `pay_date`: Payment date
- `pay_amount`: Actual payment amount

### Convenience Store Payment (payment_code='2')
- `pay_route`: Payment route (e.g., "IbonPay")
- `storeId`: Store number where payment was made
- `ibon_code`: ibon payment code
- `ibon_barcode1`, `ibon_barcode2`, `ibon_barcode3`: Three-segment ibon barcodes

## Implementation Notes

- All payment statuses are logged with relevant details
- The system updates the order status with payment details
- For successful payments, a fulfillment process can be triggered
- Error handling includes proper response codes and logging

## Sample Request

```json
{
  "api_id": "CV0000000000",
  "trans_id": "550e8400e29b41d4a716446655440000",
  "order_no": "*********",
  "amount": 1250,
  "status": "B",
  "payment_code": 2,
  "payment_detail": {
    "pay_route": "IbonPay",
    "storeId": "123456",
    "ibon_code": "405300000960",
    "ibon_barcode1": "0810025G5",
    "ibon_barcode2": "091002QP13137602",
    "ibon_barcode3": "171338770000671"
  },
  "create_time": "2016-04-03 08:00:00",
  "modify_time": "2016-04-08 08:30:00",
  "expire_time": "2016-04-10 08:00:00",
  "nonce": "1234569999",
  "checksum": "3579609ba3914a49441e98cb7e8a55de",
  "pay_date": "2016-04-08 08:35:00",
  "pay_amount": 1250
}
```

## Sample Response

```
OK
``` 