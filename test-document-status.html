<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Document Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Document Status Component Test</h1>
    
    <div class="test-section">
        <h2>1. Test Customer Login</h2>
        <p>Testing login with customer ID: 0945984877</p>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Document Requirements API</h2>
        <p>Testing document requirements configuration</p>
        <button onclick="testDocumentRequirements()">Test Document Requirements</button>
        <div id="requirementsResult"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Customer Documents API</h2>
        <p>Testing customer documents retrieval</p>
        <button onclick="testCustomerDocuments()">Test Customer Documents</button>
        <div id="documentsResult"></div>
    </div>

    <div class="test-section">
        <h2>4. Navigate to Customer Dashboard</h2>
        <p>Open the customer dashboard to see the document status component</p>
        <button onclick="openDashboard()">Open Dashboard</button>
    </div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<p>Testing login...</p>';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customerId: '0945984877',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Login Successful</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    
                    // Store customer data for other tests
                    localStorage.setItem('customerData', JSON.stringify(data.customer));
                    localStorage.setItem('SHOPME_CUSTOMER_ID', data.customer.id);
                    localStorage.setItem('SHOPME_CUSTOMER_INFO', JSON.stringify(data.customer));
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Login Failed</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testDocumentRequirements() {
            const resultDiv = document.getElementById('requirementsResult');
            resultDiv.innerHTML = '<p>Testing document requirements...</p>';
            
            try {
                // Test if we can import the document requirements
                const response = await fetch('/config/documentRequirements.js');
                const text = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Document Requirements Config Found</h3>
                            <p>Configuration file exists and is accessible</p>
                            <details>
                                <summary>View Config (first 500 chars)</summary>
                                <pre>${text.substring(0, 500)}...</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Config Not Found</h3>
                            <p>Status: ${response.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testCustomerDocuments() {
            const resultDiv = document.getElementById('documentsResult');
            resultDiv.innerHTML = '<p>Testing customer documents API...</p>';
            
            try {
                const response = await fetch('/api/customer/documents?customerId=0945984877');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Customer Documents Retrieved</h3>
                            <p>Found ${data.documents ? data.documents.length : 0} documents</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Failed to Get Documents</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function openDashboard() {
            // First ensure we're logged in
            const customerData = localStorage.getItem('customerData');
            if (!customerData) {
                alert('Please test login first!');
                return;
            }
            
            // Open the dashboard in a new tab
            window.open('/magshop/customer/dashboard', '_blank');
        }
    </script>
</body>
</html>
