// test-customer-api.js
const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// API Key from environment
const API_KEY = process.env.API_KEY || 'uX6HsVoPhmapndxrUhDn';

// Base URL for API
const BASE_URL = 'http://localhost:3000';

// Create axios instance with API key
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': API_KEY
  }
});

// Test customer creation
const testCustomerCreation = async () => {
  console.log('Testing customer API with key:', API_KEY);
  
  try {
    // Create a test customer
    const customerData = {
      name: 'Simple Test Customer',
      email: `test${Date.now()}@example.com`,
      password: 'password123',
      phone: '************'
    };
    
    console.log('Sending customer data:', customerData);
    
    const response = await api.post('/api/customers', customerData);
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    console.log('Response data type:', typeof response.data);
    
    const responseObj = response.data;
    console.log('Response object keys:', Object.keys(responseObj));
    
    // Access the customerId directly and check its value
    console.log('Direct customerId value:', responseObj.customerId);
    console.log('Has customerId property:', responseObj.hasOwnProperty('customerId'));
    
    // Get the customer ID from the response
    const customerId = responseObj.customerId;
    
    console.log('Customer ID from response:', customerId);
    
    if (customerId) {
      console.log('Successfully created customer with ID:', customerId);
      
      // Try to get the customer with the ID
      try {
        console.log(`Fetching customer with ID: ${customerId}`);
        const getResponse = await api.get(`/api/customers/${customerId}`);
        console.log('GET response:', JSON.stringify(getResponse.data, null, 2));
      } catch (getError) {
        console.error('Error fetching customer:');
        if (getError.response) {
          console.error(`Status: ${getError.response.status}`);
          console.error('Response data:', getError.response.data);
        } else {
          console.error(getError.message);
        }
      }
    } else {
      console.error('Could not find customer ID in response');
      console.log('Full response:', response.data);
    }
    
  } catch (error) {
    console.error('Error during test:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response data:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
};

// Run the test
testCustomerCreation(); 