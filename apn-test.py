#!/usr/bin/env python3
"""
7-Eleven APN Test Script
Tests the APN callback API with an existing order from the database
"""

import requests
import json
import hashlib
import time
import random
from datetime import datetime

# Order details - these match with the orders.json data
ORDER_NO = "711-TEST-12345"
TRANS_ID = "711apntest20250416001"
AMOUNT = "1225"

# APN configuration
APN_URL = "https://sim.dailoanshop.net/api/payment/apn-callback"
API_ID = "827315300001"

def generate_checksum(api_id, trans_id, amount, status, nonce):
    """Generate MD5 checksum for APN validation"""
    message = f"{api_id}:{trans_id}:{amount}:{status}:{nonce}"
    print(f"Generating checksum for: {message}")
    return hashlib.md5(message.encode()).hexdigest()

def send_apn_notification(status, status_name):
    """Send APN notification to the server"""
    # Generate unique nonce
    nonce = str(int(time.time() * 1000))
    
    # Generate checksum
    checksum = generate_checksum(API_ID, TRANS_ID, AMOUNT, status, nonce)
    
    # Random payment details
    payment_code = f"PC{random.randint(1000000, 9999999)}"
    store_id = f"ST{random.randint(100, 999)}"
    receipt_no = f"R{random.randint(100000, 999999)}"
    payment_time = datetime.now().isoformat()
    
    # Prepare request payload
    payload = {
        "api_id": API_ID,
        "trans_id": TRANS_ID,
        "order_no": ORDER_NO,
        "amount": AMOUNT,
        "status": status,
        "nonce": nonce,
        "checksum": checksum,
        "payment_code": payment_code,
        "payment_detail": {
            "store_id": store_id,
            "payment_time": payment_time,
            "receipt_no": receipt_no
        }
    }
    
    print(f"\n=== SENDING {status_name} NOTIFICATION ===")
    print(f"Order Number: {ORDER_NO}")
    print(f"Transaction ID: {TRANS_ID}")
    print(f"Amount: {AMOUNT}")
    print(f"Status: {status}")
    print(f"Nonce: {nonce}")
    print(f"Checksum: {checksum}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    # Make the API call
    response = requests.post(APN_URL, json=payload)
    
    print(f"\nResponse (HTTP {response.status_code}):")
    print(response.text)
    
    if response.status_code >= 200 and response.status_code < 300:
        print("\n✓ APN notification successfully sent!")
    else:
        print("\n✗ APN notification failed!")
    
    # Sleep for 2 seconds before the next request
    time.sleep(2)

# Main execution
print("=" * 52)
print("     7-ELEVEN APN TEST SCRIPT                      ")
print("=" * 52)
print(f"Testing with order {ORDER_NO} on {APN_URL}")

# Scenario 1: Payment Waiting
send_apn_notification("A", "PAYMENT WAITING")

# Scenario 2: Payment Completed
send_apn_notification("B", "PAYMENT COMPLETED")

# Scenario 3: Payment Expired
send_apn_notification("D", "PAYMENT EXPIRED")

print("\nTest completed! All notifications sent.") 