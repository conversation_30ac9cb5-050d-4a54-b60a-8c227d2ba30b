/**
 * Immediate fix for cart linking issues
 * Run this script directly in your browser console
 * 
 * This script will:
 * 1. Check if you're logged in as a customer
 * 2. Set your USER_ID to match your CUSTOMER_ID
 * 3. Trigger cart merging if needed
 * 4. Force refresh of cart data
 */

(function() {
  // Check if we're in a browser
  if (typeof window === 'undefined') {
    console.error('This script must be run in a browser console');
    return;
  }
  
  console.log('🔍 Checking cart linking status...');
  
  // Get current IDs
  const customerId = localStorage.getItem('SHOPME_CUSTOMER_ID');
  const userId = localStorage.getItem('SHOPME_USER_ID');
  
  console.log('Current state:');
  console.log('- Customer ID:', customerId || 'Not set');
  console.log('- User ID:', userId || 'Not set');
  
  // If no customer ID, prompt for one
  if (!customerId) {
    console.log('❗ You are not logged in as a customer');
    
    // Ask for customer ID
    const newCustomerId = prompt('Please enter your customer ID to link your cart:');
    if (!newCustomerId || !newCustomerId.trim()) {
      console.log('❌ Operation cancelled - no customer ID provided');
      return;
    }
    
    // Set customer ID
    localStorage.setItem('SHOPME_CUSTOMER_ID', newCustomerId);
    console.log('✅ Set customer ID to:', newCustomerId);
    
    // Create customer info
    const customerInfo = {
      name: 'Customer',
      email: `${newCustomerId}@example.com`,
      setAt: new Date().toISOString()
    };
    localStorage.setItem('SHOPME_CUSTOMER_INFO', JSON.stringify(customerInfo));
    console.log('✅ Created basic customer info');
    
    // Store the previous userId for merging
    const previousId = userId;
    
    // Set USER_ID to match customer ID
    localStorage.setItem('SHOPME_USER_ID', newCustomerId);
    console.log('✅ Updated USER_ID to match customer ID');
    
    // Trigger cart merge
    if (previousId && previousId !== newCustomerId) {
      console.log('🔄 Triggering cart merge');
      try {
        window.dispatchEvent(new CustomEvent('customer-identified', { 
          detail: { customerId: newCustomerId, previousId }
        }));
        console.log('✅ Cart merge event dispatched');
      } catch (e) {
        console.error('❌ Error dispatching merge event:', e);
      }
    }
  } else {
    // Customer ID exists, just make sure USER_ID matches
    if (userId !== customerId) {
      console.log('⚠️ USER_ID does not match CUSTOMER_ID - fixing...');
      
      // Store the previous userId for merging
      const previousId = userId;
      
      // Set USER_ID to match customer ID
      localStorage.setItem('SHOPME_USER_ID', customerId);
      console.log('✅ Updated USER_ID to match customer ID');
      
      // Trigger cart merge
      console.log('🔄 Triggering cart merge');
      try {
        window.dispatchEvent(new CustomEvent('customer-identified', { 
          detail: { customerId, previousId }
        }));
        console.log('✅ Cart merge event dispatched');
      } catch (e) {
        console.error('❌ Error dispatching merge event:', e);
      }
    } else {
      console.log('✅ Cart is already correctly linked to customer ID');
    }
  }
  
  // Try to reload cart data from server if available
  console.log('🔄 Attempting to reload cart data from server...');
  try {
    // If SiteContext is accessible
    if (window.document.currentSiteContext && 
        window.document.currentSiteContext.loadCartFromServer) {
      window.document.currentSiteContext.loadCartFromServer();
      console.log('✅ Reloaded cart data from server');
    } else {
      console.log('⚠️ Cannot directly access cart context, will reload page instead');
      
      // Ask user if they want to reload
      if (confirm('Cart linking fixed! Reload page to see changes?')) {
        window.location.reload();
      }
    }
  } catch (e) {
    console.error('❌ Error reloading cart data:', e);
    
    // Ask user if they want to reload
    if (confirm('Cart linking process completed. Reload page to see changes?')) {
      window.location.reload();
    }
  }
  
  console.log('✅ Cart linking operation completed');
  console.log('✅ Your cart should now be linked to your customer ID:', customerId || localStorage.getItem('SHOPME_CUSTOMER_ID'));
  console.log('ℹ️ If you don\'t see changes, try refreshing the page');
})(); 