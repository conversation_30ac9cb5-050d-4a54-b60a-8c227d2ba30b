#!/bin/bash

# Simple 7-Eleven APN Test Script
# This script tests updating order status via APN callbacks
# It uses the existing order 711TEST-001 from the database

# Color codes for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Order details - these match with the orders.json data
ORDER_NO="711-TEST-12345"
TRANS_ID="711apntest20250416001"
AMOUNT="1225"

# APN configuration
APN_URL="https://sim.dailoanshop.net/api/payment/apn-callback"
API_ID="827315300001"

# Function to generate MD5 checksum 
generate_checksum() {
    local api_id=$1
    local trans_id=$2
    local amount=$3
    local status=$4
    local nonce=$5
    local message="${api_id}:${trans_id}:${amount}:${status}:${nonce}"
    
    echo -e "${YELLOW}Generating checksum for: ${message}${NC}"
    echo -n "$message" | md5sum | awk '{print $1}'
}

# Function to send APN notification
send_apn_notification() {
    local status=$1
    local status_name=$2
    
    # Generate unique nonce
    local nonce=$(date +%s%N)
    
    # Generate checksum
    local checksum=$(generate_checksum "$API_ID" "$TRANS_ID" "$AMOUNT" "$status" "$nonce")
    
    # Random payment details
    local payment_code="PC$RANDOM$RANDOM"
    local store_id="ST$RANDOM"
    local receipt_no="R$RANDOM"
    local payment_time=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
    
    echo -e "\n${BLUE}=== SENDING ${status_name} NOTIFICATION ===${NC}"
    echo -e "Order Number: ${ORDER_NO}"
    echo -e "Transaction ID: ${TRANS_ID}"
    echo -e "Amount: ${AMOUNT}"
    echo -e "Status: ${status}"
    echo -e "Nonce: ${nonce}"
    echo -e "Checksum: ${checksum}"
    
    # Make the API call
    local response=$(curl -s -w "\n%{http_code}" -X POST "$APN_URL" \
        -H "Content-Type: application/json" \
        -d '{
            "api_id": "'"$API_ID"'",
            "trans_id": "'"$TRANS_ID"'",
            "order_no": "'"$ORDER_NO"'",
            "amount": "'"$AMOUNT"'",
            "status": "'"$status"'",
            "nonce": "'"$nonce"'",
            "checksum": "'"$checksum"'",
            "payment_code": "'"$payment_code"'",
            "payment_detail": {
                "store_id": "'"$store_id"'",
                "payment_time": "'"$payment_time"'",
                "receipt_no": "'"$receipt_no"'"
            }
        }')
    
    # Extract response body and status code
    local status_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | sed '$d')
    
    echo -e "\n${YELLOW}Response (HTTP $status_code):${NC}"
    echo "$body"
    
    if [[ $status_code -ge 200 && $status_code -lt 300 ]]; then
        echo -e "\n${GREEN}✓ APN notification successfully sent!${NC}"
    else
        echo -e "\n${RED}✗ APN notification failed!${NC}"
    fi
    
    # Sleep for 2 seconds before the next request
    sleep 2
}

# Main execution
echo -e "${BLUE}====================================================${NC}"
echo -e "${BLUE}     7-ELEVEN APN TEST SCRIPT                      ${NC}"
echo -e "${BLUE}====================================================${NC}"
echo -e "${YELLOW}Testing with order $ORDER_NO on $APN_URL${NC}"

# Scenario 1: Payment Waiting
send_apn_notification "A" "PAYMENT WAITING"

# Scenario 2: Payment Completed
send_apn_notification "B" "PAYMENT COMPLETED"

# Scenario 3: Payment Expired
send_apn_notification "D" "PAYMENT EXPIRED"

echo -e "\n${GREEN}Test completed! All notifications sent successfully.${NC}" 