const axios = require('axios');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load API key directly from .env file
dotenv.config();
const API_KEY = process.env.NEXT_PUBLIC_API_KEY || process.env.ADMIN_API_KEY || 'uX6HsVoPhmapndxrUhDn';

// Base URL - change this to your server's URL
const BASE_URL = 'http://localhost:3000';

// Create axios instance with API key in headers
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': API_KEY
  }
});

// Helper to log responses
const logResponse = (title, response) => {
  console.log('\n==========================');
  console.log(`✅ ${title} - Status: ${response.status}`);
  console.log('Response:');
  console.log(JSON.stringify(response.data, null, 2));
  console.log('==========================\n');
  return response.data;
};

// Helper to log errors
const logError = (title, error) => {
  console.error('\n==========================');
  console.error(`❌ ${title} - Error`);
  if (error.response) {
    console.error(`Status: ${error.response.status}`);
    console.error('Response:');
    console.error(JSON.stringify(error.response.data, null, 2));
  } else {
    console.error(error.message);
  }
  console.error('==========================\n');
};

// Test all API endpoints
const testApi = async () => {
  console.log('🔍 Starting API tests...');
  console.log(`Using API key: ${API_KEY}`);
  
  // Test variables to store created IDs
  let customerId;
  let orderId;
  
  try {
    // 1. Test customer creation
    console.log('Testing customer creation...');
    const customerData = {
      name: 'Test Customer',
      email: `test${Date.now()}@example.com`,
      password: 'testPassword123',
      phone: '************',
      address: '123 Test Street, Test City, 12345'
    };
    
    try {
      const createCustomerResponse = await api.post('/api/customers', customerData);
      const customerResult = logResponse('Create Customer', createCustomerResponse);
      customerId = customerResult.customer.id;
    } catch (error) {
      logError('Create Customer', error);
    }
    
    // 2. Test get all customers
    console.log('Testing get all customers...');
    try {
      const getAllCustomersResponse = await api.get('/api/customers');
      logResponse('Get All Customers', getAllCustomersResponse);
    } catch (error) {
      logError('Get All Customers', error);
    }
    
    // 3. Test get customer by ID
    if (customerId) {
      console.log(`Testing get customer by ID (${customerId})...`);
      try {
        const getCustomerResponse = await api.get(`/api/customers/${customerId}`);
        logResponse('Get Customer by ID', getCustomerResponse);
      } catch (error) {
        logError('Get Customer by ID', error);
      }
    }
    
    // 4. Test update customer
    if (customerId) {
      console.log(`Testing update customer (${customerId})...`);
      const updateData = {
        name: 'Updated Test Customer',
        phone: '************'
      };
      
      try {
        const updateCustomerResponse = await api.put(`/api/customers/${customerId}`, updateData);
        logResponse('Update Customer', updateCustomerResponse);
      } catch (error) {
        logError('Update Customer', error);
      }
    }
    
    // 5. Test create order
    if (customerId) {
      console.log('Testing order creation...');
      const orderData = {
        customerId,
        items: [
          {
            sku: 'TEST-001',
            name: 'Test Product 1',
            price: 19.99,
            quantity: 2
          },
          {
            sku: 'TEST-002',
            name: 'Test Product 2',
            price: 29.99,
            quantity: 1
          }
        ],
        billingAddress: '123 Test Street, Test City, 12345',
        paymentMethod: 'credit_card',
        currency: 'USD'
      };
      
      try {
        const createOrderResponse = await api.post('/api/orders', orderData);
        const orderResult = logResponse('Create Order', createOrderResponse);
        orderId = orderResult.order.id;
      } catch (error) {
        logError('Create Order', error);
      }
    }
    
    // 6. Test get all orders
    console.log('Testing get all orders...');
    try {
      const getAllOrdersResponse = await api.get('/api/orders');
      logResponse('Get All Orders', getAllOrdersResponse);
    } catch (error) {
      logError('Get All Orders', error);
    }
    
    // 7. Test get order by ID
    if (orderId) {
      console.log(`Testing get order by ID (${orderId})...`);
      try {
        const getOrderResponse = await api.get(`/api/orders/${orderId}`);
        logResponse('Get Order by ID', getOrderResponse);
      } catch (error) {
        logError('Get Order by ID', error);
      }
    }
    
    // 8. Test get orders by customer ID
    if (customerId) {
      console.log(`Testing get orders by customer ID (${customerId})...`);
      try {
        const getCustomerOrdersResponse = await api.get(`/api/orders?customerId=${customerId}`);
        logResponse('Get Orders by Customer ID', getCustomerOrdersResponse);
      } catch (error) {
        logError('Get Orders by Customer ID', error);
      }
    }
    
    // 9. Test update order
    if (orderId) {
      console.log(`Testing update order (${orderId})...`);
      const updateOrderData = {
        status: 'processing',
        notes: 'Customer called to confirm order details'
      };
      
      try {
        const updateOrderResponse = await api.put(`/api/orders/${orderId}`, updateOrderData);
        logResponse('Update Order', updateOrderResponse);
      } catch (error) {
        logError('Update Order', error);
      }
    }
    
    // 10. Test update order items
    if (orderId) {
      console.log(`Testing update order items (${orderId})...`);
      const updateItemsData = {
        items: [
          {
            sku: 'TEST-001',
            name: 'Test Product 1',
            price: 19.99,
            quantity: 3 // Increased quantity
          },
          {
            sku: 'TEST-002',
            name: 'Test Product 2',
            price: 29.99,
            quantity: 1
          }
        ]
      };
      
      try {
        const updateItemsResponse = await api.put(`/api/orders/${orderId}`, updateItemsData);
        logResponse('Update Order Items', updateItemsResponse);
      } catch (error) {
        logError('Update Order Items', error);
      }
    }
    
    // 11. Test delete order (optional - comment out if you want to keep the test order)
    if (orderId) {
      console.log(`Testing delete order (${orderId})...`);
      try {
        const deleteOrderResponse = await api.delete(`/api/orders/${orderId}`);
        logResponse('Delete Order', deleteOrderResponse);
        orderId = null;
      } catch (error) {
        logError('Delete Order', error);
      }
    }
    
    // 12. Test delete customer (optional - comment out if you want to keep the test customer)
    if (customerId) {
      console.log(`Testing delete customer (${customerId})...`);
      try {
        const deleteCustomerResponse = await api.delete(`/api/customers/${customerId}`);
        logResponse('Delete Customer', deleteCustomerResponse);
        customerId = null;
      } catch (error) {
        logError('Delete Customer', error);
      }
    }
    
    console.log('🎉 API testing completed!');
    
  } catch (error) {
    console.error('❌ Fatal error during API testing:');
    console.error(error);
  }
};

// Run the tests
testApi(); 