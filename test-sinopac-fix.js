const axios = require('axios');

// Test the updated SinoPAC API
async function testSinoPacAPI() {
  try {
    const testPayload = {
      OrderNo: 'MAG-TEST-' + Date.now(),
      Amount: 100,
      PrdtName: 'Test Product',
      CustomerName: 'Test Customer',
      CustomerEmail: '<EMAIL>',
      CustomerPhone: '0912345678',
      CustomerAddress: 'Test Address'
    };

    console.log('Testing SinoPAC API with payload:', testPayload);

    const response = await axios.post('http://localhost:3000/api/payment/sinopac/order', testPayload, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('Success! API Response:', response.data);
  } catch (error) {
    console.error('Test failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
}

testSinoPacAPI(); 