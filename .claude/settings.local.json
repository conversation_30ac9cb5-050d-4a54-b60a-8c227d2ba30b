{"permissions": {"allow": ["Bash(find:*)", "Bash(# Create a list of all component file basenames\nfind /Users/<USER>/Workspace/mag.group.shop/components -name \"\"*.js\"\" -o -name \"\"*.jsx\"\" -o -name \"\"*.ts\"\" -o -name \"\"*.tsx\"\" | while read file; do\n  basename=$(basename \"\"$file\"\" | sed ''s/\\.[^.]*$//'')\n  # Check if this component is imported anywhere (excluding the file itself)\n  if ! grep -r \"\"import.*$basename\"\" /Users/<USER>/Workspace/mag.group.shop --exclude-dir=node_modules --exclude-dir=.next --exclude=\"\"$file\"\" >/dev/null 2>&1; then\n    echo \"\"POTENTIALLY UNUSED: $file\"\"\n  fi\ndone | head -20)", "Bash(npm uninstall:*)", "Bash(rm:*)", "Bash(yarn build)"], "deny": []}}