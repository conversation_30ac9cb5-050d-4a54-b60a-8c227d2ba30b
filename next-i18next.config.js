const { i18n } = require('./i18n');

/** @type {import('next').NextConfig} */
const config = {
  //i18n,
  i18n: {
    defaultLocale: 'vi',
    locales: ['en', 'vi'],
    localeDetection: true,
  },
  fallbackLng: {
    default: ['vi'],
  },
  output: 'export',
  images: {
    unoptimized: true,
  },
  headers: async () => {
    return [
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
        ],
      },
    ]
  },
  pwa: {
    dest: 'public',
    disable: process.env.NODE_ENV === 'development',
    register: true,
    scope: '/',
    sw: 'sw.js',
  },
};

module.exports = config;
