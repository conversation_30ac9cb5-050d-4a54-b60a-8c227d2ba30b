# Testing the 7-11 APN Callback

This guide explains how to use the test script to verify that the APN (Active Notification Service) callback API is working correctly.

## Prerequisites

Before running the test script, make sure you have:

1. Node.js installed (version 14 or higher)
2. The required dependencies:
   ```
   npm install axios crypto
   ```

## Test Script Overview

The `test-apn-callback.js` script simulates payment notifications from the 7-Eleven payment system. It includes multiple test cases covering different payment scenarios:

1. Convenience Store Payment Success - Simulates a successful ibon payment
2. Credit Card Payment Success - Simulates a successful credit card payment
3. Payment Pending - Simulates a payment that is waiting to be processed
4. Payment Expired - Simulates an expired payment
5. Invalid Checksum - Simulates a request with an invalid checksum (should be rejected)

Each test case generates a properly formatted request payload, including the required MD5 checksum verification.

## Configuration

Before running the tests, you may need to adjust the configuration in the script:

```javascript
// Configuration - adjust these values as needed
const CONFIG = {
  // APN callback URL
  apnUrl: 'https://mag.group.shop/api/payment/apn-callback',
  
  // Local development URL (uncomment to test locally)
  // apnUrl: 'http://localhost:3000/api/payment/apn-callback',
  
  // API credentials
  apiId: '827315300001',
  
  // Set to true to log detailed request/response information
  debug: true
};
```

- For local testing, uncomment the local URL and comment out the production URL
- Set `debug` to `true` to see detailed request and response payloads
- Make sure the `apiId` matches the one configured in your API

## Running the Tests

To run the tests, simply execute the script:

```bash
node test-apn-callback.js
```

The script will run each test case sequentially and display the results:

```
Starting APN Callback Tests...

========== Running Test: Convenience Store Payment Success ==========
Request payload:
{
  "api_id": "827315300001",
  "trans_id": "550e8400e29b41d4a716446655440000",
  ...
}
Status Code: 200
Response: OK
✅ Test PASSED

...

All tests completed.
```

## Expected Results

For each test, the expected response is:

- A HTTP status code of 200
- A plain text response of "OK" 

The test is considered passed if both conditions are met.

## Troubleshooting

If a test fails, check the following:

1. Make sure the APN callback API is running and accessible from your machine
2. Verify that the API credentials in the test script match those configured in the API
3. Check that the request format matches what the API expects (according to the 7-11 API specification)
4. Look for any error messages in the API logs

## Adding Custom Test Cases

You can add your own test cases by adding to the `TEST_CASES` array:

```javascript
const TEST_CASES = [
  // Existing test cases...
  
  {
    name: 'Your Custom Test',
    request: () => {
      // Generate and return a custom request payload
      // ...
    }
  }
];
```

Make sure your custom test cases include all required fields and a valid checksum (unless you're specifically testing invalid requests).

## Security Note

This script is intended for testing purposes only. It generates simulated payment notifications that look like they come from the 7-Eleven payment system. In a production environment, always verify that incoming requests are authentic by checking the credentials and validating the checksum. 