#!/bin/bash

# 7-Eleven APN Curl Test Script
# A simpler way to test the APN endpoint using curl

# Color codes for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}====================================================${NC}"
echo -e "${BLUE}     7-ELEVEN APN CURL TEST TOOL                   ${NC}"
echo -e "${BLUE}====================================================${NC}"

# Default values (can be overridden with command line arguments)
API_ID="827315300001"
TRANS_ID="TEST_TRANS_$(date +%s)"
ORDER_NO="ORD-TEST-$(date +%s | cut -c6-13)"
AMOUNT="299"
STATUS="A"
APN_URL="https://sim.dailoanshop.net/api/payment/apn-callback"

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --api-id) API_ID="$2"; shift ;;
        --trans-id) TRANS_ID="$2"; shift ;;
        --order-no) ORDER_NO="$2"; shift ;;
        --amount) AMOUNT="$2"; shift ;;
        --status) STATUS="$2"; shift ;;
        --url) APN_URL="$2"; shift ;;
        *) echo "Unknown parameter: $1"; exit 1 ;;
    esac
    shift
done

# Generate timestamp for nonce
NONCE=$(date +%s)

# Generate MD5 checksum
generate_checksum() {
  local message="$1:$2:$3:$4:$5"
  echo -n "$message" | md5sum | awk '{print $1}'
}

# Generate checksum for this request
CHECKSUM=$(generate_checksum "$API_ID" "$TRANS_ID" "$AMOUNT" "$STATUS" "$NONCE")

# Prepare status description
get_status_name() {
  case "$1" in
    "A") echo "Waiting for payment" ;;
    "B") echo "Payment completed" ;;
    "D") echo "Payment expired" ;;
    *) echo "Unknown status" ;;
  esac
}

STATUS_NAME=$(get_status_name "$STATUS")

# Prepare random payment details
PAYMENT_CODE="PC$(( RANDOM % 10000000 ))"
STORE_ID="ST$(( RANDOM % 1000 ))"
RECEIPT_NO="R$(( RANDOM % 1000000 ))"
PAYMENT_TIME=$(date -Iseconds)
PAYMENT_DETAIL="{\"store_id\":\"$STORE_ID\",\"payment_time\":\"$PAYMENT_TIME\",\"receipt_no\":\"$RECEIPT_NO\"}"

# Display request information
echo -e "\n${YELLOW}APN Request Details:${NC}"
echo -e "  Target URL: ${BLUE}$APN_URL${NC}"
echo -e "  API ID: $API_ID"
echo -e "  Transaction ID: $TRANS_ID"
echo -e "  Order Number: $ORDER_NO"
echo -e "  Amount: $AMOUNT"
echo -e "  Status: $STATUS ($STATUS_NAME)"
echo -e "  Nonce: $NONCE"
echo -e "  Checksum: $CHECKSUM"
echo -e "  Payment Code: $PAYMENT_CODE"
echo -e "  Payment Details: $PAYMENT_DETAIL"

# Confirmation prompt
echo -e "\n${YELLOW}Ready to send this request to the APN endpoint.${NC}"
read -p "Press Enter to continue or Ctrl+C to cancel... "

# Send the request with curl
echo -e "\n${YELLOW}Sending APN request...${NC}"

RESPONSE=$(curl -s -w "\n%{http_code}" -X POST "$APN_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "api_id": "'"$API_ID"'",
    "trans_id": "'"$TRANS_ID"'",
    "order_no": "'"$ORDER_NO"'",
    "amount": "'"$AMOUNT"'",
    "status": "'"$STATUS"'",
    "nonce": "'"$NONCE"'",
    "checksum": "'"$CHECKSUM"'",
    "payment_code": "'"$PAYMENT_CODE"'",
    "payment_detail": "'"$PAYMENT_DETAIL"'"
  }')

# Extract the response body and status code
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | sed '$d')

# Display the response
echo -e "\n${YELLOW}Response:${NC}"
echo -e "  HTTP Status: ${BLUE}$HTTP_CODE${NC}"
echo -e "  Body: $BODY"

# Check if the request was successful
if [[ $HTTP_CODE -ge 200 && $HTTP_CODE -lt 300 ]]; then
  echo -e "\n${GREEN}✓ APN notification successfully sent!${NC}"
  echo -e "The server has acknowledged receipt of the payment notification."
else
  echo -e "\n${RED}✗ APN notification failed!${NC}"
  echo -e "The server returned an error response. Check the server logs for more details."
fi

echo -e "\n${BLUE}====================================================${NC}"
echo -e "${GREEN}Test completed!${NC}"
echo -e "${BLUE}====================================================${NC}"

# Save to log file
LOG_DIR="./apn-logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/apn-curl-test-$(date +%Y%m%d-%H%M%S).log"

{
  echo "=== 7-ELEVEN APN CURL TEST LOG ==="
  echo "Date: $(date)"
  echo "Target URL: $APN_URL"
  echo ""
  echo "Request:"
  echo "  API ID: $API_ID"
  echo "  Transaction ID: $TRANS_ID"
  echo "  Order Number: $ORDER_NO"
  echo "  Amount: $AMOUNT"
  echo "  Status: $STATUS ($STATUS_NAME)"
  echo "  Nonce: $NONCE"
  echo "  Checksum: $CHECKSUM"
  echo "  Payment Code: $PAYMENT_CODE"
  echo "  Payment Details: $PAYMENT_DETAIL"
  echo ""
  echo "Response:"
  echo "  HTTP Status: $HTTP_CODE"
  echo "  Body: $BODY"
  echo ""
  echo "=== END OF LOG ==="
} > "$LOG_FILE"

echo -e "Log saved to: $LOG_FILE" 