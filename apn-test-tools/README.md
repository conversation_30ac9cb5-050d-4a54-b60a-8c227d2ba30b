# 7-Eleven APN Testing Tools

This directory contains tools for testing the 7-Eleven APN (Active Payment Notification) integration with the system at `https://sim.dailoanshop.net/api/payment/apn-callback`.

## Available Tools

### 1. Main Test Script (`../run-apn-test.sh`)

The main test script runs a complete Node.js-based test suite for the APN integration.

```bash
# Run the main test
./run-apn-test.sh
```

This script will:
- Create a test environment in the `apn-test` directory
- Install necessary dependencies
- Run a series of test scenarios
- Generate detailed logs

### 2. Curl Test Script (`apn-curl-test.sh`)

A simpler way to test the APN endpoint using curl. This is useful for quick tests without setting up the full Node.js environment.

```bash
# Basic usage
./apn-curl-test.sh

# With custom parameters
./apn-curl-test.sh --status B --order-no ORD-12345 --amount 599
```

#### Available Parameters:

- `--api-id`: The merchant API ID (default: 827315300001)
- `--trans-id`: Transaction ID
- `--order-no`: Order number
- `--amount`: Payment amount
- `--status`: Payment status (A=waiting, B=paid, D=expired)
- `--url`: APN endpoint URL

## Understanding Payment Status Codes

- **Status A**: Waiting for payment
- **Status B**: Payment completed
- **Status D**: Payment expired

## Test Scenarios

The main test script simulates these scenarios:

1. **Payment Waiting**: Sends status "A" for a new order
2. **Payment Completed**: Updates the order with status "B"
3. **Payment Expired**: Creates a new order with status "A", then changes it to "D"

## Troubleshooting

If you encounter issues with the APN testing:

1. Check server logs for error messages
2. Ensure the server is accessible from the internet
3. Verify that the checksum calculation matches on both client and server
4. Confirm that the API credentials are correct
5. Check that the orders being tested exist in your system

## Example APN Payload

```json
{
  "api_id": "827315300001",
  "trans_id": "TEST_TRANS_1234567890",
  "order_no": "ORD-TEST-12345",
  "amount": "299",
  "status": "B",
  "nonce": "1634567890",
  "checksum": "abcdef1234567890abcdef1234567890",
  "payment_code": "PC12345678",
  "payment_detail": "{\"store_id\":\"12345\",\"payment_time\":\"2023-09-15T12:34:56Z\"}"
}
```

## Security Considerations

- The APN endpoint should only accept requests with valid checksums
- All test credentials should be kept secure
- In production, use proper TLS/HTTPS for all communication
- Consider rate limiting the APN endpoint to prevent abuse 