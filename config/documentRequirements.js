// Document Requirements Configuration
// Defines required documents for different customer types and scenarios

export const DOCUMENT_CATEGORIES = {
  identity: {
    name: '<PERSON><PERSON><PERSON><PERSON> tờ tùy thân',
    description: '<PERSON><PERSON><PERSON> giấy tờ chứng minh danh tính',
    priority: 'critical',
    color: '#dc2626'
  },
  address: {
    name: '<PERSON><PERSON><PERSON><PERSON> tờ cư trú',
    description: '<PERSON><PERSON><PERSON> giấy tờ chứng minh nơi cư trú',
    priority: 'high',
    color: '#ea580c'
  },
  financial: {
    name: '<PERSON><PERSON><PERSON> chính',
    description: '<PERSON><PERSON><PERSON> giấy tờ liên quan đến tài chính',
    priority: 'high',
    color: '#059669'
  },
  business: {
    name: '<PERSON><PERSON> doan<PERSON>',
    description: '<PERSON><PERSON><PERSON> giấy tờ liên quan đến kinh doanh',
    priority: 'medium',
    color: '#7c3aed'
  },
  personal: {
    name: '<PERSON><PERSON> nhân',
    description: '<PERSON><PERSON><PERSON> giấy tờ cá nhân khác',
    priority: 'medium',
    color: '#0891b2'
  },
  legal: {
    name: '<PERSON><PERSON><PERSON> lý',
    description: '<PERSON><PERSON><PERSON> giấy tờ pháp lý',
    priority: 'low',
    color: '#6b7280'
  }
};

export const DOCUMENT_REQUIREMENTS = {
  // Identity Documents (Critical Priority)
  idCard: {
    category: 'identity',
    label: 'CMND/CCCD',
    description: 'Chứng minh nhân dân hoặc căn cước công dân',
    required: true,
    priority: 'critical',
    expirationTracking: true,
    validityPeriod: null, // Permanent document
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5, // MB
    validationRules: {
      minAge: 16, // Minimum age to have ID card
      mustBeValid: true,
      requiresBothSides: true
    }
  },
  passport: {
    category: 'identity',
    label: 'Hộ chiếu',
    description: 'Hộ chiếu quốc tế',
    required: false,
    priority: 'high',
    expirationTracking: true,
    validityPeriod: 10 * 365, // 10 years in days
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeValid: true,
      requiresInfoPage: true
    }
  },
  
  // Address Documents (High Priority)
  address: {
    category: 'address',
    label: 'Giấy tờ cư trú',
    description: 'Sổ hộ khẩu, tạm trú, KT3, xác nhận cư trú',
    required: true,
    priority: 'high',
    expirationTracking: false,
    validityPeriod: null,
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustMatchIdAddress: false, // Can be different from ID address
      requiresCurrentAddress: true
    }
  },
  utilityBill: {
    category: 'address',
    label: 'Hóa đơn tiện ích',
    description: 'Hóa đơn điện, nước, internet (3 tháng gần nhất)',
    required: false,
    priority: 'medium',
    expirationTracking: true,
    validityPeriod: 90, // 3 months
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 3,
    validationRules: {
      maxAge: 90, // Must be within 3 months
      mustShowAddress: true
    }
  },
  
  // Financial Documents (High Priority)
  bankStatement: {
    category: 'financial',
    label: 'Sao kê ngân hàng',
    description: 'Sao kê tài khoản ngân hàng (6 tháng gần nhất)',
    required: false,
    priority: 'high',
    expirationTracking: true,
    validityPeriod: 30, // 1 month
    acceptedFormats: ['pdf', 'jpg', 'jpeg', 'png'],
    maxFileSize: 10,
    validationRules: {
      maxAge: 30,
      mustShowTransactions: true,
      minimumPeriod: 180 // 6 months of history
    }
  },
  
  // Business Documents (Medium Priority)
  businessRegistration: {
    category: 'business',
    label: 'Đăng ký kinh doanh',
    description: 'Giấy phép đăng ký kinh doanh',
    required: false,
    priority: 'medium',
    expirationTracking: true,
    validityPeriod: null, // Check individual document
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeValid: true,
      requiresBusinessOwner: true
    },
    conditionalRequired: {
      condition: 'customerType',
      value: 'business'
    }
  },
  taxRegistration: {
    category: 'business',
    label: 'Đăng ký thuế',
    description: 'Giấy đăng ký mã số thuế',
    required: false,
    priority: 'medium',
    expirationTracking: false,
    validityPeriod: null,
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeValid: true
    },
    conditionalRequired: {
      condition: 'customerType',
      value: 'business'
    }
  },
  
  // Personal Documents (Medium Priority)
  photo: {
    category: 'personal',
    label: 'Ảnh chân dung',
    description: 'Ảnh chân dung chính chủ (4x6cm)',
    required: true,
    priority: 'medium',
    expirationTracking: false,
    validityPeriod: 365, // 1 year for freshness
    acceptedFormats: ['jpg', 'jpeg', 'png'],
    maxFileSize: 2,
    validationRules: {
      mustBeClearFace: true,
      noSunglasses: true,
      frontFacing: true
    }
  },
  driverLicense: {
    category: 'personal',
    label: 'Bằng lái xe',
    description: 'Giấy phép lái xe',
    required: false,
    priority: 'low',
    expirationTracking: true,
    validityPeriod: null, // Check individual document
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeValid: true,
      requiresBothSides: true
    }
  },
  
  // Legal Documents (Low Priority)
  birthCertificate: {
    category: 'legal',
    label: 'Giấy khai sinh',
    description: 'Giấy khai sinh',
    required: false,
    priority: 'low',
    expirationTracking: false,
    validityPeriod: null,
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeOriginalOrCertified: true
    }
  },
  marriageCertificate: {
    category: 'legal',
    label: 'Giấy kết hôn',
    description: 'Giấy chứng nhận kết hôn',
    required: false,
    priority: 'low',
    expirationTracking: false,
    validityPeriod: null,
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeOriginalOrCertified: true
    },
    conditionalRequired: {
      condition: 'maritalStatus',
      value: 'married'
    }
  },
  insuranceCard: {
    category: 'personal',
    label: 'Thẻ bảo hiểm',
    description: 'Thẻ bảo hiểm y tế/xã hội',
    required: false,
    priority: 'low',
    expirationTracking: true,
    validityPeriod: null, // Check individual document
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 3,
    validationRules: {
      mustBeValid: true,
      requiresBothSides: true
    }
  },
  workPermit: {
    category: 'legal',
    label: 'Giấy phép lao động',
    description: 'Giấy phép lao động cho người nước ngoài',
    required: false,
    priority: 'high',
    expirationTracking: true,
    validityPeriod: null, // Check individual document
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeValid: true
    },
    conditionalRequired: {
      condition: 'nationality',
      value: '!Vietnamese' // Not Vietnamese
    }
  },
  academicDegree: {
    category: 'personal',
    label: 'Bằng cấp học thuật',
    description: 'Bằng đại học, cao đẳng, chứng chỉ',
    required: false,
    priority: 'low',
    expirationTracking: false,
    validityPeriod: null,
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 5,
    validationRules: {
      mustBeOriginalOrCertified: true
    }
  },
  propertyOwnership: {
    category: 'legal',
    label: 'Giấy tờ sở hữu',
    description: 'Giấy chứng nhận quyền sử dụng đất, sở hữu nhà',
    required: false,
    priority: 'low',
    expirationTracking: false,
    validityPeriod: null,
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 10,
    validationRules: {
      mustBeOriginalOrCertified: true
    }
  },
  other: {
    category: 'personal',
    label: 'Khác',
    description: 'Các giấy tờ khác',
    required: false,
    priority: 'low',
    expirationTracking: false,
    validityPeriod: null,
    acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
    maxFileSize: 10,
    validationRules: {}
  }
};

// Customer type specific requirements
export const CUSTOMER_TYPE_REQUIREMENTS = {
  individual: {
    required: ['idCard', 'address', 'photo'],
    recommended: ['bankStatement', 'utilityBill'],
    optional: ['passport', 'driverLicense', 'insuranceCard']
  },
  business: {
    required: ['idCard', 'address', 'photo', 'businessRegistration', 'taxRegistration'],
    recommended: ['bankStatement', 'utilityBill'],
    optional: ['passport', 'driverLicense']
  },
  foreign: {
    required: ['passport', 'workPermit', 'address', 'photo'],
    recommended: ['bankStatement', 'utilityBill'],
    optional: ['driverLicense', 'academicDegree']
  }
};

// Document status definitions
export const DOCUMENT_STATUS = {
  pending: {
    label: 'Chờ duyệt',
    color: '#f59e0b',
    icon: 'FaClock',
    description: 'Tài liệu đang chờ được xem xét'
  },
  approved: {
    label: 'Đã duyệt',
    color: '#10b981',
    icon: 'FaCheck',
    description: 'Tài liệu đã được phê duyệt'
  },
  rejected: {
    label: 'Từ chối',
    color: '#ef4444',
    icon: 'FaTimes',
    description: 'Tài liệu bị từ chối, cần tải lên lại'
  },
  expired: {
    label: 'Hết hạn',
    color: '#6b7280',
    icon: 'FaExclamationTriangle',
    description: 'Tài liệu đã hết hạn, cần cập nhật'
  },
  missing: {
    label: 'Thiếu',
    color: '#dc2626',
    icon: 'FaExclamationTriangle',
    description: 'Tài liệu bắt buộc chưa được tải lên'
  }
};

// Helper functions
export const getRequiredDocumentsForCustomer = (customer) => {
  const customerType = determineCustomerType(customer);
  const requirements = CUSTOMER_TYPE_REQUIREMENTS[customerType] || CUSTOMER_TYPE_REQUIREMENTS.individual;
  
  return {
    required: requirements.required.map(docType => ({
      type: docType,
      ...DOCUMENT_REQUIREMENTS[docType]
    })),
    recommended: requirements.recommended.map(docType => ({
      type: docType,
      ...DOCUMENT_REQUIREMENTS[docType]
    })),
    optional: requirements.optional.map(docType => ({
      type: docType,
      ...DOCUMENT_REQUIREMENTS[docType]
    }))
  };
};

export const determineCustomerType = (customer) => {
  // Check if customer has business registration
  if (customer.personalDetails?.occupation?.includes('business') || 
      customer.personalDetails?.employer?.includes('Self')) {
    return 'business';
  }
  
  // Check if customer is foreign
  if (customer.citizenship?.nationality !== 'Vietnamese') {
    return 'foreign';
  }
  
  return 'individual';
};

export const getDocumentComplianceStatus = (customer) => {
  const requirements = getRequiredDocumentsForCustomer(customer);
  const customerDocs = customer.documents || [];
  
  const compliance = {
    total: requirements.required.length,
    completed: 0,
    pending: 0,
    missing: 0,
    expired: 0,
    percentage: 0
  };
  
  // If no required documents, return 100% compliance
  if (compliance.total === 0) {
    compliance.percentage = 100;
    return compliance;
  }
  
  requirements.required.forEach(reqDoc => {
    const customerDoc = customerDocs.find(doc => doc.documentType === reqDoc.type);
    
    if (!customerDoc) {
      compliance.missing++;
    } else if (customerDoc.status === 'approved') {
      // Check if document is expired
      if (reqDoc.expirationTracking && reqDoc.validityPeriod) {
        const uploadDate = new Date(customerDoc.uploadDate);
        const expirationDate = new Date(uploadDate.getTime() + (reqDoc.validityPeriod * 24 * 60 * 60 * 1000));
        
        if (expirationDate < new Date()) {
          compliance.expired++;
        } else {
          compliance.completed++;
        }
      } else {
        compliance.completed++;
      }
    } else {
      compliance.pending++;
    }
  });
  
  // Avoid division by zero
  compliance.percentage = compliance.total > 0 ? Math.round((compliance.completed / compliance.total) * 100) : 0;
  
  return compliance;
};

export const getDocumentHistory = (customer, documentType) => {
  const customerDocs = customer.documents || [];
  return customerDocs
    .filter(doc => doc.documentType === documentType)
    .sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate))
    .map(doc => ({
      ...doc,
      statusHistory: doc.statusHistory || [
        {
          status: doc.status,
          timestamp: doc.statusUpdatedAt || doc.uploadDate,
          updatedBy: doc.statusUpdatedBy || 'system',
          comment: doc.rejectionReason || ''
        }
      ]
    }));
};