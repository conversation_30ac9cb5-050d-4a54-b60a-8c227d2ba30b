/**
 * Test script to verify the payment channels API is working with real data
 */

const axios = require('axios');

async function testPaymentChannelsAPI() {
  try {
    console.log('🧪 Testing Payment Channels API with Real Data...\n');
    
    // Test the payment stats API
    const response = await axios.get('http://localhost:3000/api/admin/payment-stats');
    
    if (response.status !== 200) {
      throw new Error(`API returned status ${response.status}`);
    }
    
    const data = response.data;
    
    if (!data.success) {
      throw new Error(`API returned error: ${data.error}`);
    }
    
    console.log('✅ API Response Status: SUCCESS\n');
    
    // Display summary statistics
    const { summary, channels } = data.data;
    console.log('📊 Summary Statistics:');
    console.log(`  • Total Transactions: ${summary.totalTransactions.toLocaleString()}`);
    console.log(`  • Total Volume: ${summary.totalVolume.toLocaleString()}đ`);
    console.log(`  • Success Rate: ${summary.successRate.toFixed(1)}%`);
    console.log(`  • Total Channels: ${summary.totalChannels}\n`);
    
    // Display channel details
    console.log('🏪 Payment Channels:');
    channels.forEach((channel, index) => {
      console.log(`  ${index + 1}. ${channel.name} (${channel.id})`);
      console.log(`     • Transactions: ${channel.transactions.toLocaleString()}`);
      console.log(`     • Volume: ${channel.volume.toLocaleString()}đ`);
      console.log(`     • Success Rate: ${channel.successRate.toFixed(1)}%`);
      console.log(`     • Status: ${channel.status}`);
      console.log(`     • Currencies: ${channel.currencies.join(', ') || 'None'}`);
      console.log(`     • Last Transaction: ${new Date(channel.lastTransaction).toLocaleString()}`);
      console.log('');
    });
    
    console.log('🎉 All tests passed! Real data is being used instead of mock data.');
    
    // Verify no Hi-Life entries
    const hiLifeEntries = channels.filter(channel => 
      channel.name.toLowerCase().includes('hi-life') || 
      channel.id.toLowerCase().includes('hi-life')
    );
    
    if (hiLifeEntries.length > 0) {
      console.log('⚠️  WARNING: Hi-Life entries still found:');
      hiLifeEntries.forEach(entry => {
        console.log(`   - ${entry.name} (${entry.id})`);
      });
    } else {
      console.log('✅ Confirmed: No Hi-Life entries found (issue resolved)');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    
    process.exit(1);
  }
}

// Run the test
testPaymentChannelsAPI(); 