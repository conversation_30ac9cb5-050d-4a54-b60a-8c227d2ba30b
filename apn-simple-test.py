#!/usr/bin/env python3
"""
Simple 7-Eleven APN Test Script
Tests the APN callback API with an existing order from the database
"""

import urllib.request
import urllib.parse
import json
import hashlib
import time
import random
import ssl
from datetime import datetime

# Order details - these match with the orders.json data
ORDER_NO = "711-TEST-12345"
TRANS_ID = "711apntest20250416001"
AMOUNT = "1225"

# APN configuration
APN_URL = "https://sim.dailoanshop.net/api/payment/apn-callback"
API_ID = "827315300001"

def generate_checksum(api_id, trans_id, amount, status, nonce):
    """Generate MD5 checksum for APN validation"""
    message = f"{api_id}:{trans_id}:{amount}:{status}:{nonce}"
    print(f"Generating checksum for: {message}")
    return hashlib.md5(message.encode()).hexdigest()

def send_apn_notification(status, status_name):
    """Send APN notification to the server"""
    # Generate unique nonce
    nonce = str(int(time.time() * 1000))
    
    # Generate checksum
    checksum = generate_checksum(API_ID, TRANS_ID, AMOUNT, status, nonce)
    
    # Random payment details
    payment_code = f"PC{random.randint(1000000, 9999999)}"
    store_id = f"ST{random.randint(100, 999)}"
    receipt_no = f"R{random.randint(100000, 999999)}"
    payment_time = datetime.now().isoformat()
    
    # Prepare request payload
    payload = {
        "api_id": API_ID,
        "trans_id": TRANS_ID,
        "order_no": ORDER_NO,
        "amount": AMOUNT,
        "status": status,
        "nonce": nonce,
        "checksum": checksum,
        "payment_code": payment_code,
        "payment_detail": json.dumps({
            "store_id": store_id,
            "payment_time": payment_time,
            "receipt_no": receipt_no
        })
    }
    
    print(f"\n=== SENDING {status_name} NOTIFICATION ===")
    print(f"Order Number: {ORDER_NO}")
    print(f"Transaction ID: {TRANS_ID}")
    print(f"Amount: {AMOUNT}")
    print(f"Status: {status}")
    print(f"Nonce: {nonce}")
    print(f"Checksum: {checksum}")
    
    # Convert payload to JSON and prepare request
    data = json.dumps(payload).encode('utf-8')
    
    # Create request
    req = urllib.request.Request(APN_URL, data=data)
    req.add_header('Content-Type', 'application/json')
    req.add_header('Content-Length', len(data))
    
    # Send request
    try:
        # Ignore SSL certificate validation for testing
        context = ssl._create_unverified_context()
        response = urllib.request.urlopen(req, context=context)
        
        # Get response
        status_code = response.getcode()
        response_text = response.read().decode('utf-8')
        
        print(f"\nResponse (HTTP {status_code}):")
        print(response_text)
        
        print("\n✓ APN notification successfully sent!")
        
    except urllib.error.HTTPError as e:
        print(f"\nResponse (HTTP {e.code}):")
        print(e.read().decode('utf-8'))
        print("\n✗ APN notification failed!")
        
    except Exception as e:
        print(f"\nError: {e}")
        print("\n✗ APN notification failed!")
    
    # Sleep for 2 seconds before the next request
    time.sleep(2)

# Main execution
print("=" * 52)
print("     7-ELEVEN APN TEST SCRIPT                      ")
print("=" * 52)
print(f"Testing with order {ORDER_NO} on {APN_URL}")

# Scenario 1: Payment Waiting
send_apn_notification("A", "PAYMENT WAITING")

# Scenario 2: Payment Completed
send_apn_notification("B", "PAYMENT COMPLETED")

# Scenario 3: Payment Expired
send_apn_notification("D", "PAYMENT EXPIRED")

print("\nTest completed! All notifications sent.") 