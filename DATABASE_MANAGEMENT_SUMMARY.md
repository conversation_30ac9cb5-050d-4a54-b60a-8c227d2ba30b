# MAG Shop Database Management - Quick Reference

## 🚀 Quick Start Commands

```bash
# Check current status
./db-manage.sh status

# Run full analysis  
./db-manage.sh analyze

# Create backup before changes
./db-manage.sh backup full

# Interactive management tool
./db-manage.sh
```

## 📊 Current Database Status

Your database currently contains:
- **0 orders** (recently wiped)
- **1 customer** 
- **3 log files**
- **52 APN callback logs** (15 test logs, 37 production)
- **1 backup** available

## 🛠️ Most Common Operations

### Safety First - Always Backup!
```bash
./db-manage.sh backup full
```

### Clean Test Data (Recommended)
```bash
# Run interactive mode and select option 5
./db-manage.sh
# Then: 5 -> y (dry run first) -> y (confirm)
```

### Clean Old Logs to Save Space
```bash
# Run interactive mode and select option 8  
./db-manage.sh
# Then: 8 -> y (dry run first) -> y (confirm)
```

### Clean APN Callback Logs
```bash
# Clean test APN logs (recommended)
./db-manage.sh
# Then: 10 -> y (dry run first) -> y (confirm)

# Clean old APN logs (>90 days)
./db-manage.sh
# Then: 9 -> y (dry run first) -> y (confirm)
```

### Clean Expired Orders
```bash
# Run interactive mode and select option 4
./db-manage.sh  
# Then: 4 -> y (dry run first) -> y (confirm)
```

## 📁 What Gets Created

The tools will create:
- `backups/` directory - Your database backups
- `logs/db-maintenance-*.log` - Automated maintenance logs
- `scripts/db-management.js` - Main management script
- `db-manage.sh` - Quick access wrapper

## 🔍 Enhanced File Visibility

The system now provides detailed file information:

### During Backup Creation
- Shows each file being backed up with sizes
- Lists directory contents and file counts
- Displays backup summary with all included files

### When Listing Backups
- **Content Statistics**: Record counts for orders, customers, staff, APN logs
- **Detailed File Lists**: Shows actual files in each backup
- **Smart Truncation**: Shows first few files if many, with "+X more" indicators

### During Cleanup Operations
- **Dry Run Details**: Shows exactly which files would be cleaned
- **File Ages**: Displays how old files are (e.g., "45 days ago")
- **Progress Indicators**: Real-time feedback during operations

## ⚡ NPM Scripts (Alternative)

```bash
npm run db:status    # Quick status
npm run db:analyze   # Full analysis  
npm run db:backup    # Create backup
npm run db:cleanup   # Run cleanup
npm run db:manage    # Interactive mode
```

## 🔄 Recommended Workflow

1. **First Time Setup**:
   ```bash
   ./db-manage.sh backup full    # Create initial backup
   ./db-manage.sh analyze        # Understand your data
   ```

2. **Regular Maintenance**:
   ```bash
   ./db-manage.sh status         # Daily check
   ./db-manage.sh cleanup test   # Clean test data weekly
   ./db-manage.sh backup full    # Backup before major changes
   ```

3. **Emergency Recovery**:
   ```bash
   ./db-manage.sh                # Interactive mode
   # Select option 13 (Restore from backup)
   ```

## ⚠️ Important Safety Notes

- **Always run dry runs first** - All cleanup operations ask if you want a dry run
- **Always backup before major operations** 
- **Test restore process** occasionally
- **Keep multiple backup generations**
- **Wipe operations require typing 'CONFIRM'** - they cannot be undone

## 🚨 Emergency Commands

```bash
# If something goes wrong, restore from backup:
./db-manage.sh
# Then select: 13 (Restore from backup)

# If you need help:
./db-manage.sh help
```

## 📖 Full Documentation

For complete documentation, see: `DATABASE_MANAGEMENT.md`

For automated maintenance setup: `scripts/cron-example.sh` 