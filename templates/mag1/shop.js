import Head from "next/head"
import HotProducts from "../../components/HotProducts"
import SocialShare from "../../components/SocialShare"
import ListItemProduct from "../../components/ListItemProduct"
import { slugify } from "../../utils/helpers"
import { useState, useEffect, useRef } from "react"
import ProductList from '../../components/ProductList'
import { lightTheme, darkTheme } from './styles/theme'
import RecentlyViewed from "../../components/RecentlyViewed"

const ITEMS_PER_PAGE = 12

const Mag1ShopLayout = ({ 
  currentstore,
  hotProducts,
  store,
  currentUrl,
  hotProductSKUs,
  hotProductsBySKU,
  products,
  selectedCategory,
  setSelectedCategory,
  categories
}) => {
  const { shopLinkQRCode, qr_line, slogan, bannermessage } = currentstore
  const [isCompact, setIsCompact] = useState(false)
  const [currentTheme, setCurrentTheme] = useState(lightTheme)
  const [activeCategory, setActiveCategory] = useState('all')
  const [displayedProducts, setDisplayedProducts] = useState([])
  const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const observerTarget = useRef(null)

  // Get theme classes
  const { bgClasses, textClasses } = currentTheme;

  // Group products by category
  const productsByCategory = {}
  products.forEach(product => {
    if (Array.isArray(product.categories)) {
      product.categories.forEach(category => {
        if (!productsByCategory[category]) {
          productsByCategory[category] = []
        }
        productsByCategory[category].push(product)
      })
    }
  })

  // Get unique categories
  const uniqueCategories = Object.keys(productsByCategory)

  // Filter products based on selected category
  const filteredProducts = activeCategory === 'all' 
    ? products 
    : productsByCategory[activeCategory] || []

  // Load more products
  const loadMoreProducts = () => {
    if (loading || !hasMore) return
    
    setLoading(true)
    const start = (page - 1) * ITEMS_PER_PAGE
    const end = start + ITEMS_PER_PAGE
    const newProducts = filteredProducts.slice(start, end)
    
    setDisplayedProducts(prev => [...prev, ...newProducts])
    setPage(prev => prev + 1)
    setHasMore(end < filteredProducts.length)
    setLoading(false)
  }

  // Reset pagination when category changes
  useEffect(() => {
    setDisplayedProducts([])
    setPage(1)
    setHasMore(true)
    loadMoreProducts()
  }, [activeCategory])

  // Setup intersection observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          loadMoreProducts()
        }
      },
      { threshold: 0.1 }
    )

    if (observerTarget.current) {
      observer.observe(observerTarget.current)
    }

    return () => observer.disconnect()
  }, [page, hasMore, loading])

  // Initial load
  useEffect(() => {
    loadMoreProducts()
  }, [])

  return (
    <div className={`min-h-screen ${bgClasses.secondary}`}>
      <Head>
        <title>{currentstore.name}</title>
        <meta name="description" content={currentstore.slogan} />
        <meta property="og:title" content={currentstore.name} key="title" />
      </Head>

      {/* Banner Message */}
      {bannermessage && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-orange-50 border-l-4 border-orange-500 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-orange-800">{bannermessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content with Sidebar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar Filter */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className={`${bgClasses.primary} rounded-lg shadow-sm p-4`}>
              <h2 className={`text-lg font-semibold ${textClasses.primary} mb-4`}>Danh mục</h2>
              <div className="space-y-2">
                <button
                  onClick={() => setActiveCategory('all')}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                    activeCategory === 'all'
                      ? 'bg-orange-100 text-orange-700 font-medium'
                      : `${textClasses.secondary} hover:bg-gray-50`
                  }`}
                >
                  Tất cả sản phẩm
                </button>
                {uniqueCategories.map(category => (
                  <button
                    key={category}
                    onClick={() => setActiveCategory(category)}
                    className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                      activeCategory === category
                        ? 'bg-orange-100 text-orange-700 font-medium'
                        : `${textClasses.secondary} hover:bg-gray-50`
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* View Toggle */}
            <div className="flex justify-end mb-6">
              <button
                onClick={() => setIsCompact(!isCompact)}
                className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium ${textClasses.secondary} ${bgClasses.primary} hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500`}
              >
                {isCompact ? 'Xem chi tiết' : 'Xem gọn'}
              </button>
            </div>

            {/* Hot Products */}
            <section className="mb-12">
              <HotProducts 
                products={hotProducts}
                store={store}
              />
            </section>

            {/* Recently Viewed Products */}
            <section className="mb-12">
              <RecentlyViewed 
                store={store}
                currentProduct={null}
                products={products}
              />
            </section>

            {/* Filtered Products Grid */}
            <section className="mb-12">
              {isCompact ? (
                // Compact view - responsive flex layout
                <div className={`${bgClasses.primary} rounded-lg shadow-sm`}>
                  <div className="flex flex-wrap">
                    {displayedProducts.map(product => (
                      <a 
                        key={product.id} 
                        href={`/${store}/product/${slugify(product.sku)}`}
                        className={`flex items-center gap-2 p-3 hover:bg-gray-50 transition-colors border-b border-r w-full sm:w-1/2 lg:w-1/3`}
                      >
                        <div className="w-12 h-12 flex-shrink-0 rounded overflow-hidden bg-gray-50">
                          {product.image?.[0] && (
                            <img
                              src={product.image[0]}
                              alt={product.name}
                              className="h-full w-full object-cover"
                            />
                          )}
                        </div>
                        <div className="min-w-0 flex-1">
                          <h3 className={`text-sm ${textClasses.primary} truncate`}>{product.name}</h3>
                          {product.price && (
                            <p className={`text-sm ${textClasses.accent}`}>
                              {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                            </p>
                          )}
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              ) : (
                // Grid view - original detailed layout
                <div className="grid grid-cols-3 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {displayedProducts.map(product => (
                    <a 
                      key={product.id} 
                      href={`/${store}/product/${slugify(product.sku)}`}
                      className={`${bgClasses.primary} border rounded-lg p-4 block hover:shadow-md transition-shadow`}
                    >
                      <div className="aspect-square overflow-hidden mb-4">
                        {product.image?.[0] && (
                          <img
                            src={product.image[0]}
                            alt={product.name}
                            className="h-full w-full object-cover"
                          />
                        )}
                      </div>
                      <h3 className={`font-medium text-sm mb-2 line-clamp-2 ${textClasses.primary}`}>{product.name}</h3>
                      {product.price && (
                        <p className={`${textClasses.accent} font-medium`}>
                          {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                        </p>
                      )}
                    </a>
                  ))}
                </div>
              )}
              
              {/* Loading indicator */}
              <div ref={observerTarget} className="w-full py-4 text-center">
                {loading && (
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                  </div>
                )}
                {!hasMore && displayedProducts.length > 0 && (
                  <p className={`${textClasses.secondary} text-sm`}>Không còn sản phẩm nào</p>
                )}
              </div>
            </section>

            {/* QR Code */}
            {shopLinkQRCode && (
              <section className={`${bgClasses.primary} rounded-xl shadow-sm p-6 mb-8`}>
                <div className="flex justify-center">
                  <img 
                    src={shopLinkQRCode} 
                    alt="Store QR Code"
                    className="h-48"
                  />
                </div>
              </section>
            )}

            {/* Social Share */}
            <section className={`${bgClasses.primary} rounded-xl shadow-sm p-6`}>
              <SocialShare currentUrl={currentUrl} />
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Mag1ShopLayout