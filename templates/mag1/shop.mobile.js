import Head from "next/head"
import HotProducts from "../../components/HotProducts"
import SocialShare from "../../components/SocialShare"
import ListItemProduct from "../../components/ListItemProduct"
import { slugify } from "../../utils/helpers"
import { useState, useEffect } from "react"
import ProductList from '../../components/ProductList'
import { lightTheme, darkTheme } from './styles/theme'

const ITEMS_PER_PAGE = 8 // Smaller number for mobile to improve performance

const Mag1ShopLayoutMobile = ({ 
  currentstore,
  hotProducts,
  store,
  currentUrl,
  hotProductSKUs,
  hotProductsBySKU,
  products,
  selectedCategory,
  setSelectedCategory,
  categories
}) => {
  const { shopLinkQRCode, qr_line, slogan, bannermessage } = currentstore
  const [isCompact, setIsCompact] = useState(false)
  const [currentTheme, setCurrentTheme] = useState(lightTheme)
  const [displayedProducts, setDisplayedProducts] = useState({})
  const [loadingStates, setLoadingStates] = useState({})
  const [hasMoreStates, setHasMoreStates] = useState({})
  const [expandedCategories, setExpandedCategories] = useState({})

  // Use default values if theme files are missing
  const safeTheme = {
    colors: currentTheme.colors || { primary: '#ffffff', text: '#333333' },
    fontFamily: currentTheme.fontFamily || 'Arial, sans-serif'
  };

  // Use theme values in your styles
  const themeStyles = {
    backgroundColor: currentTheme.colors.primary,
    color: currentTheme.colors.text,
    fontFamily: currentTheme.fontFamily
  };

  // Group products by category
  const productsByCategory = {}
  products.forEach(product => {
    if (Array.isArray(product.categories)) {
      product.categories.forEach(category => {
        if (!productsByCategory[category]) {
          productsByCategory[category] = []
        }
        productsByCategory[category].push(product)
      })
    }
  })

  // Toggle category expansion
  const toggleCategory = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }))
  }

  // Load more products for a specific category
  const loadMoreProducts = (category) => {
    if (loadingStates[category] || !hasMoreStates[category]) return
    
    setLoadingStates(prev => ({ ...prev, [category]: true }))
    const categoryProducts = productsByCategory[category]
    const currentLength = displayedProducts[category]?.length || 0
    const start = currentLength
    const end = start + ITEMS_PER_PAGE
    const newProducts = categoryProducts.slice(start, end)
    
    setDisplayedProducts(prev => ({
      ...prev,
      [category]: [...(prev[category] || []), ...newProducts]
    }))
    setHasMoreStates(prev => ({
      ...prev,
      [category]: end < categoryProducts.length
    }))
    setLoadingStates(prev => ({ ...prev, [category]: false }))
  }

  // Initialize displayed products and expanded categories
  useEffect(() => {
    const initialProducts = {}
    const initialHasMore = {}
    const initialExpanded = {}
    const categories = Object.keys(productsByCategory)
    
    categories.forEach((category, index) => {
      initialProducts[category] = productsByCategory[category].slice(0, ITEMS_PER_PAGE)
      initialHasMore[category] = productsByCategory[category].length > ITEMS_PER_PAGE
      // Set first category as expanded by default
      initialExpanded[category] = index === 0
    })
    
    setDisplayedProducts(initialProducts)
    setHasMoreStates(initialHasMore)
    setExpandedCategories(initialExpanded)
  }, [])

  return (
    <div className="min-h-screen" style={themeStyles}>
      <Head>
        <title>{currentstore.name}</title>
        <meta name="description" content={currentstore.slogan} />
        <meta property="og:title" content={currentstore.name} key="title" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
      </Head>

      {/* Banner Message */}
      {bannermessage && (
        <div className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 py-2 sm:py-4">
          <div className="bg-orange-50 border-l-4 border-orange-500 p-3 sm:p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-xs sm:text-sm text-orange-800">{bannermessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content - mobile optimized */}
      <main className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 py-4 sm:py-8">
        {/* View Toggle - mobile optimized */}
        <div className="flex justify-end mb-4">
          <button
            onClick={() => setIsCompact(!isCompact)}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            {isCompact ? 'Xem chi tiết' : 'Xem gọn'}
          </button>
        </div>

        {/* Hot Products */}
        <section className="mb-6">
          <HotProducts 
            products={hotProducts}
            store={store}
          />
        </section>

        {/* Products by Category - mobile optimized */}
        <section className="space-y-4">
          {Object.entries(productsByCategory).map(([category, categoryProducts]) => (
            <div key={category} className="border rounded-lg overflow-hidden">
              <button
                onClick={() => toggleCategory(category)}
                className="w-full bg-gray-100 p-3 flex justify-between items-center"
              >
                <span className="font-medium text-gray-700">{category}</span>
                <svg
                  className={`w-5 h-5 transform transition-transform ${expandedCategories[category] ? 'rotate-180' : ''}`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {expandedCategories[category] && (
                <div className="p-3">
                  <div className="space-y-3">
                    {displayedProducts[category]?.map(product => (
                      <a 
                        key={product.id} 
                        href={`/${store}/product/${slugify(product.sku)}`}
                        className="flex items-start gap-3 p-2 border rounded-lg hover:shadow-sm transition-shadow"
                      >
                        <div className="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden bg-gray-50">
                          {product.image?.[0] && (
                            <img
                              src={product.image[0]}
                              alt={product.name}
                              className="h-full w-full object-cover"
                              loading="lazy"
                            />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-sm text-gray-900 line-clamp-2">{product.name}</h3>
                          {product.price && (
                            <p className="text-orange-600 text-sm mt-1">
                              {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                            </p>
                          )}
                        </div>
                      </a>
                    ))}
                  </div>

                  {/* Load More button for each category */}
                  {hasMoreStates[category] && (
                    <div className="mt-4 flex justify-center">
                      <button
                        onClick={() => loadMoreProducts(category)}
                        disabled={loadingStates[category]}
                        className="inline-flex items-center px-4 py-2 border border-orange-500 text-sm font-medium rounded-md text-orange-700 bg-white hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loadingStates[category] ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500 mr-2"></div>
                            Đang tải...
                          </>
                        ) : (
                          'Xem thêm sản phẩm'
                        )}
                      </button>
                    </div>
                  )}
                  {!hasMoreStates[category] && displayedProducts[category]?.length > 0 && (
                    <p className="text-center text-gray-500 text-xs mt-4">Không còn sản phẩm nào</p>
                  )}
                </div>
              )}
            </div>
          ))}
        </section>

        {/* QR Code - mobile optimized */}
        {shopLinkQRCode && (
          <section className="bg-white rounded-xl shadow-sm p-4 mt-6">
            <div className="flex justify-center">
              <img 
                src={shopLinkQRCode} 
                alt="Store QR Code"
                className="h-32"
                loading="lazy"
              />
            </div>
          </section>
        )}

        {/* Social Share - mobile optimized */}
        <section className="bg-white rounded-xl shadow-sm p-4 mt-4">
          <SocialShare currentUrl={currentUrl} />
        </section>
      </main>
    </div>
  )
}

export default Mag1ShopLayoutMobile