/* ./styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --ion-safe-area-top: env(safe-area-inset-top);
  --ion-safe-area-bottom: env(safe-area-inset-bottom);
}

body {
  padding-top: var(--ion-safe-area-top);
  padding-bottom: var(--ion-safe-area-bottom);
  /* font-family: "ABN", sans-serif; */
  font-family: "ABN";
}
/* 
@font-face {
  font-family: "ABN";
  src: url("/fonts/Montserrat-Regular.ttf");
}


@font-face {
  font-family: "ABN Light";
  src: url("/fonts/Montserrat-Light.ttf");
}

@font-face {
  font-family: "ABN SemiBold";
  src: url("/fonts/Montserrat-SemiBold.ttf");
}
 */

 @font-face {
  font-family: "ABN";
  src: url("/fonts/ABChanelCorpoVietnam-Light.woff2");
}


@font-face {
  font-family: "ABN Light";
  src: url("/fonts/ABChanelCorpoVietnam-Light.woff2");
}

@font-face {
  font-family: "ABN SemiBold";
  src: url("/fonts/ABChanelCorpoVietnam-SemiBold.woff2");
}

@layer base {
  p, h1, h2, h3, h4, h5 {
    color: rgba(0, 0, 0, .8);
    font-family: ABN;
  }
}

.Toastify__progress-bar--default {
  background: white !important;
}

.slides {
  display: flex;
  overflow-x: hidden; /* Ensure that images outside the viewport are hidden */
}

.slide {
  flex: 0 0 100%; /* Ensure each image occupies the entire width */
}

.carousel-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: auto;
  overflow: hidden; /* Ensure that images outside the container are hidden */
}


.active {
  opacity: 1;
}

.prev,
.next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 20px;
  color: red;
  z-index: 100;
}

.prev {
  left: 10px; /* Adjust the left position as needed */
}

.next {
  right: 10px; /* Adjust the right position as needed */
}

.boxstyle_1 {
  border: 1px solid #d1d5db; 
  /* border-radius: 0 45px 45px 45px; */
  padding: 0 10px;
}

.boxstyle_1:hover {
  border-color: #9ca3af; /* Change border color on hover to gray-400 */
}

.boxstyle_2 {
  border: 1px solid #d1d5db; 
  border-radius: 0 45px 45px 45px;
  padding: 0 10px;
}

.boxstyle_2:hover {
  border-color: #9ca3af; /* Change border color on hover to gray-400 */
}

.boxstyle_3 {
  border: 1px solid #d1d5db; 
  border-radius: 0 45px 45px 0;
  padding: 0 10px;
}

.boxstyle_:hover {
  border-color: #9ca3af; /* Change border color on hover to gray-400 */
}

.boxstyle_4 {
  border: 1px solid #d1d5db; 
  border-radius: 45px 0 45px 45px;
  padding: 0 10px;
}

.boxstyle_4:hover {
  border-color: #9ca3af; /* Change border color on hover to gray-400 */
}

.boxstyle_5 {
  border: 1px solid #d1d5db; 
  border-radius: 0 45px 0 0;
  padding: 0 10px;
}

.boxstyle_5:hover {
  border-color: #9ca3af; /* Change border color on hover to gray-400 */
}

.boxstyle_6 {
  border-left: 3px solid #d1d5db ; 
  padding: 0 10px;
}

.boxstyle_6:hover {
  border-color: #9ca3af; /* Change border color on hover to gray-400 */
}

.boxstyle_7 {
  border: 1px solid #d1d5db; 
  border-radius: 45px 45px 0 0;
  padding: 0 10px;
}

.boxstyle_7:hover {
  border-color: #9ca3af; /* Change border color on hover to gray-400 */
}

@media screen and (min-width: 768px) {
  .grid-cols-cartItem {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr; /* Adjust column widths as needed */
    grid-gap: 10px; /* Adjust gap between grid items as needed */
  }
}

@media screen and (max-width: 768px) {
  .grid-cols-cartItem {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr; 
    grid-gap: 10px; 
  }  
}

.grid-cols-cartItem > * {
  justify-self: end; /* Align content to the end (right) of each column */
}

@media screen and (min-width: 768px) {
  .grid-cols-checkoutItem {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr; /* Adjust column widths as needed */
    grid-gap: 10px; /* Adjust gap between grid items as needed */
  }
}
.grid-cols-checkoutItem > * {
  justify-self: end; /* Align content to the end (right) of each column */
}

@media screen and (max-width: 768px) {
  .grid-cols-checkoutItem {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr; 
    grid-gap: 10px; 
  }
}

.userLocationContainer {
  display: flex;
  flex-direction: row;
}

.buttonContainer {
  margin-right: 20px; /* Adjust spacing between button and text */
}

.category-panel {
  cursor: pointer;
}

.category-content {
  border: 1px solid #ccc;
  display: none;
  opacity: 0;
  column-gap: 20px;
  margin-left: 30px;
}

/* Adjust column count for laptops */
@media only screen and (min-width: 768px) {
  .category-content {
    column-count: 4;
  }
}

/* Adjust column count for mobile devices */
@media only screen and (max-width: 767px) {
  .category-content {
    column-count: 2;
  }
}

.category-panel.active .category-content {
  display: block;
  opacity: 1;
}

.category-toggle {
  transition: opacity 0.1s ease;
}

.category-panel:hover .category-toggle {
  /* opacity: 0; */
  opacity: 1;
}

.blinking {
  animation: blink-animation 3s steps(5, start) infinite;
}

@keyframes blink-animation {
  to {
    visibility: hidden;
  }
}



/* table.module.css */

.table {
  border-collapse: collapse;
  width: 100%;
}

.table th,
.table td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

.table th {
  background-color: #f2f2f2;
}

.table tr:nth-child(even) {
  background-color: #f2f2f2;
}

.table tr:hover {
  background-color: #ddd;
}

.custom-checkbox {
  width: 24px; /* Adjust the width as desired */
  height: 24px; /* Adjust the height as desired */
}

.select-dropdown {
  width: 50px; /* Adjust the width as needed */
}


.shopme-logo-separator {
  border-radius: 0 15% 15% 15%;
  width: 10%;
}

@media screen and (max-width: 768px) {
  .shopme-logo-separator {
    width: 40%;
  }
}

/* Add viewport height fix for mobile browsers */
.min-h-screen {
  min-height: -webkit-fill-available;
}