// themes.js
export const lightTheme = {
  colors: {
    primary: '#ffffff',
    secondary: '#f0f0f0',
    text: '#333333',
    accent: '#ff6b00', // Orange accent for store theme
    highlight: '#ffefd5', // Peach highlight color
  },
  fontFamily: 'Arial, sans-serif',
  // Adding Tailwind compatible classes
  bgClasses: {
    primary: 'bg-white',
    secondary: 'bg-gray-100',
    accent: 'bg-orange-500',
  },
  textClasses: {
    primary: 'text-gray-900',
    secondary: 'text-gray-600',
    accent: 'text-orange-600',
  }
};

export const darkTheme = {
  colors: {
    primary: '#333333',
    secondary: '#444444',
    text: '#ffffff',
    accent: '#ff8c38', // Lighter orange for dark theme
    highlight: '#5e4a3b', // Dark peach for dark theme
  },
  fontFamily: 'Roboto, sans-serif',
  // Adding Tailwind compatible classes
  bgClasses: {
    primary: 'bg-gray-900',
    secondary: 'bg-gray-800',
    accent: 'bg-orange-500',
  },
  textClasses: {
    primary: 'text-white',
    secondary: 'text-gray-300',
    accent: 'text-orange-400',
  }
};
