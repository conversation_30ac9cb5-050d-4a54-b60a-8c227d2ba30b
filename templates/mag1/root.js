import Head from "next/head"
import ListItemShop from "../../components/ListItemShop"
import { titleIfy } from "../../utils/helpers"

export default function Mag1RootLayout({ allshops, currentUrl }) {
  return (
    <>
      <div className="w-full bg-gray-100 min-h-screen">
        <Head>
          <title>ShopMe: MAG GROUP</title>
          <meta name="description" content="All Products" />
          <meta property="og:title" content="All Products" key="title" />
        </Head>
        
        {/* Search Bar */}
        <div className="bg-orange-500 p-4">
          <div className="max-w-6xl mx-auto">
            <input 
              type="search"
              placeholder="Tìm kiếm..."
              className="w-full p-2 rounded-sm"
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-medium">
              {titleIfy("MAG GROUP")}
            </h3>
            <div className="flex gap-4">
              <select className="border p-1 rounded">
                <option>Phổ biến</option>
                <option>Mới nhất</option>
                <option>Bán chạy</option>
              </select>
            </div>
          </div>

          {/* Shop Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {allshops.map((store, index) => (
              <ListItemShop
                key={index}
                link={`/${store.storeId}`}
                title={store.name} 
                imageSrc={store.logo}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  )
} 