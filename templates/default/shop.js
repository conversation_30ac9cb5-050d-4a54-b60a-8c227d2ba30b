import Head from "next/head"
import CartLink from "../../components/CartLink"
import { Tag, Center, Showcase } from "../../components"
import HotProducts from "../../components/HotProducts"
import SocialShare from "../../components/SocialShare"
import { slugify } from "../../utils/helpers"

export default function DefaultShopLayout({ 
  currentstore,
  hotProducts,
  store,
  currentUrl,
  hotProductSKUs,
  hotProductsBySKU
}) {
  return (
    <>
      <CartLink />
      <div className="w-full">
        <Head>
          <title>{currentstore.name}</title>
          <meta name="description" content={currentstore.slogan} />
          <meta property="og:title" content={currentstore.name} key="title" />
        </Head>
        <div className="bg-transparent border border-gray-300 hover:border-gray-400 p-6 pb-10 smpb-6 flex lg:flex-row flex-col">
          <div className="pt-4 pl-2 sm:pt-12 sm:pl-12 flex flex-col">
            {currentstore.bannermessage && (
              <div className="blinking">
                <p className="text-gray-600">{currentstore.bannermessage}</p>
                <br />
              </div>
            )}
            <Tag year="2025" category="SẢN PHẨM BÁN CHẠY" />
            <Center
              price={hotProductsBySKU[hotProductSKUs[0]]?.price || 0}
              title={hotProductsBySKU[hotProductSKUs[0]]?.name || currentstore.name}
              link={hotProductsBySKU[hotProductSKUs[0]] ? 
                `${store}/product/${slugify(hotProductsBySKU[hotProductSKUs[0]].sku)}` : 
                "#"
              }
            />
          </div>
          <div className="flex flex-1 justify-center items-center relative">
            <Showcase
              imageSrc={hotProductsBySKU[hotProductSKUs[0]]?.image[0] || currentstore.logo}
            />
          </div>
        </div>
        <SocialShare currentUrl={currentUrl} />
        <HotProducts 
          products={hotProducts}
          store={store}
        />
      </div>
    </>
  )
} 