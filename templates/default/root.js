import Head from "next/head"
import ListItemShop from "../../components/ListItemShop"
import { titleIfy } from "../../utils/helpers"

export default function DefaultRootLayout({ allshops, currentUrl }) {
  return (
    <div className="w-full">
      <Head>
        <title>ShopMe: MAG GROUP</title>
        <meta name="description" content="All Products" />
        <meta property="og:title" content="All Products" key="title" />
      </Head>
      <div className="flex flex-col items-center">
        <div className="max-w-fw flex flex-col w-full">
          <div className="pt-4 sm:pt-10 pb-8">
            <h3 className="text-xl font-light text-right">
              {titleIfy("MAG GROUP")}
            </h3>
          </div>
          <div>
            <div className="flex flex-1 flex-wrap flex-row justify-center">
              {allshops.map((store, index) => (
                <ListItemShop
                  key={index}
                  link={`/${store.storeId}`}
                  title={store.name}
                  imageSrc={store.logo}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 