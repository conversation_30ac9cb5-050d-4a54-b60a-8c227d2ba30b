import Head from "next/head"
import HotProducts from "../../components/HotProducts"
import SocialShare from "../../components/SocialShare"
import ListItemProduct from "../../components/ListItemProduct"
import { slugify } from "../../utils/helpers"
import { useState } from "react"
import ProductList from '../../components/ProductList'

const ShopeeShopLayout = ({ 
  currentstore,
  hotProducts,
  store,
  currentUrl,
  hotProductSKUs,
  hotProductsBySKU,
  products,
  selectedCategory,
  setSelectedCategory,
  categories
}) => {
  const { shopLinkQRCode, qr_line, slogan, bannermessage } = currentstore
  const [isCompact, setIsCompact] = useState(false)

  // Group products by category
  const productsByCategory = {}
  products.forEach(product => {
    if (Array.isArray(product.categories)) {
      product.categories.forEach(category => {
        if (!productsByCategory[category]) {
          productsByCategory[category] = []
        }
        productsByCategory[category].push(product)
      })
    }
  })

  console.log("products:", products);
  console.log("productsByCategory:", productsByCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>{currentstore.name}</title>
        <meta name="description" content={currentstore.slogan} />
        <meta property="og:title" content={currentstore.name} key="title" />
      </Head>

      {/* Header */}
      {/* <div className="sticky top-0 z-50 bg-white shadow-sm">
        <div className="bg-orange-500">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="relative">
              <input 
                type="search"
                placeholder="Tìm kiếm sản phẩm..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none focus:ring-2 focus:ring-orange-300"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-4">
            <img 
              src={currentstore.logo} 
              alt={currentstore.name}
              className="w-16 h-16 rounded-full object-cover ring-2 ring-orange-500"
            />
            <div>
              <h1 className="text-xl font-bold text-gray-900">{currentstore.name}</h1>
              {slogan && <p className="text-sm text-gray-600 mt-0.5">{slogan}</p>}
            </div>
          </div>
        </div>
      </div> */}

      {/* Banner Message */}
      {bannermessage && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-orange-50 border-l-4 border-orange-500 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-orange-800">{bannermessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* View Toggle */}
        <div className="flex justify-end mb-6">
          <button
            onClick={() => setIsCompact(!isCompact)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            {isCompact ? 'Xem chi tiết' : 'Xem gọn'}
          </button>
        </div>

        {/* Hot Products */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Sản phẩm nổi bật</h2>
          <HotProducts 
            products={hotProducts}
            store={store}
          />
        </section>
        
        {/* All Products */}
        {/* <ProductList
          key={category}
          products={products}
          store={store}
          title={category}
          expandable={true}
          compact={isCompact}
        /> */}

        {/* Products by Category */}
        {Object.entries(productsByCategory).map(([category, products]) => (
          <ProductList
            key={category}
            products={products}
            store={store}
            title={category}
            expandable={true}
            compact={isCompact}
          />
        ))}

        {/* QR Code */}
        {shopLinkQRCode && (
          <section className="bg-white rounded-xl shadow-sm p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Quét mã QR để truy cập cửa hàng</h2>
            <div className="flex justify-center">
              <img 
                src={shopLinkQRCode} 
                alt="Store QR Code"
                className="h-48"
              />
            </div>
          </section>
        )}

        {/* Social Share */}
        <section className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Chia sẻ cửa hàng</h2>
          <SocialShare currentUrl={currentUrl} />
        </section>
      </main>
    </div>
  )
}

export default ShopeeShopLayout 