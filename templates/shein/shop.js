import Head from "next/head"
import HotProducts from "../../components/HotProducts"
import SocialShare from "../../components/SocialShare"
import ListItemProduct from "../../components/ListItemProduct"
import { slugify } from "../../utils/helpers"
import { useState, useEffect } from "react"
import ProductList from '../../components/ProductList'
import { lightTheme, darkTheme } from './styles/theme'
import Link from 'next/link'
import DesktopHeader from './components/DesktopHeader'
import AllCategories from './components/AllCategories'
import FocusedCategories from './components/FocusedCategories'
import DesktopCategoriesPanel from './components/DesktopCategoriesPanel'
import categoryDisplayNames from '../../utils/categoryDisplayNames.json'

const ITEMS_PER_PAGE = 8 // Smaller number for mobile to improve performance

const SheinShopLayout = ({
  currentstore,
  hotProducts,
  store,
  currentUrl,
  hotProductSKUs,
  hotProductsBySKU,
  products,
  selectedCategory,
  setSelectedCategory,
  categories
}) => {
  const { shopLinkQRCode, qr_line, slogan, bannermessage, focusedCategories } = currentstore
  const [isCompact, setIsCompact] = useState(false)
  const [currentTheme, setCurrentTheme] = useState(lightTheme)
  const [displayedProducts, setDisplayedProducts] = useState({})
  const [loadingStates, setLoadingStates] = useState({})
  const [hasMoreStates, setHasMoreStates] = useState({})
  const [expandedCategories, setExpandedCategories] = useState({})
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [searchResults, setSearchResults] = useState([])
  const [selectedPanelCategory, setSelectedPanelCategory] = useState(null)
  const [showingProducts, setShowingProducts] = useState(false)

  // Default promotional banners
  const defaultBanners = [
    {
      title: 'Quà tặng',
      message: 'Tích điểm để giảm giá',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-900',
      messageColor: 'text-orange-800',
      iconColor: 'text-orange-500',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      )
    },
    {
      title: 'Tin tức',
      message: 'Xem thêm',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-900',
      messageColor: 'text-yellow-800',
      iconColor: 'text-yellow-500',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    }
  ]

  // Parse custom banners from store settings if available
  const customBanners = currentstore.promotionalBanners || defaultBanners

  // Use default values if theme files are missing
  const safeTheme = {
    colors: currentTheme.colors || { primary: '#ffffff', text: '#333333' },
    fontFamily: currentTheme.fontFamily || 'Arial, sans-serif'
  };

  // Use theme values in your styles
  const themeStyles = {
    backgroundColor: currentTheme.colors.primary,
    color: currentTheme.colors.text,
    fontFamily: currentTheme.fontFamily
  };

  // Group products by category
  const productsByCategory = {}
  products.forEach(product => {
    if (Array.isArray(product.categories)) {
      product.categories.forEach(category => {
        if (!productsByCategory[category]) {
          productsByCategory[category] = []
        }
        productsByCategory[category].push(product)
      })
    }
  })

  // Get category images
  const categoryImages = {}
  Object.entries(productsByCategory).forEach(([category, products]) => {
    const firstProduct = products[0]
    if (firstProduct && Array.isArray(firstProduct.image) && firstProduct.image.length > 0) {
      categoryImages[category] = firstProduct.image[0]
    }
  })

  // Toggle category expansion - modified to allow only one active category
  const toggleCategory = (category) => {
    // Create a new object with all categories set to false
    const newExpandedState = {}
    Object.keys(productsByCategory).forEach(cat => {
      newExpandedState[cat] = false
    })
    
    // Toggle the selected category (if it's already true, it becomes false; otherwise true)
    newExpandedState[category] = !expandedCategories[category]
    
    setExpandedCategories(newExpandedState)
  }

  // Load more products for a specific category
  const loadMoreProducts = (category) => {
    if (loadingStates[category] || !hasMoreStates[category]) return

    setLoadingStates(prev => ({ ...prev, [category]: true }))
    const categoryProducts = productsByCategory[category]
    const currentLength = displayedProducts[category]?.length || 0
    const start = currentLength
    const end = start + ITEMS_PER_PAGE
    const newProducts = categoryProducts.slice(start, end)

    setDisplayedProducts(prev => ({
      ...prev,
      [category]: [...(prev[category] || []), ...newProducts]
    }))
    setHasMoreStates(prev => ({
      ...prev,
      [category]: end < categoryProducts.length
    }))
    setLoadingStates(prev => ({ ...prev, [category]: false }))
  }

  // Initialize displayed products and expanded categories
  useEffect(() => {
    const initialProducts = {}
    const initialHasMore = {}
    const initialExpanded = {}
    const categories = Object.keys(productsByCategory)

    // Initialize products and hasMore states for all categories
    categories.forEach((category) => {
      initialProducts[category] = productsByCategory[category].slice(0, ITEMS_PER_PAGE)
      initialHasMore[category] = productsByCategory[category].length > ITEMS_PER_PAGE
      // Default all to not expanded
      initialExpanded[category] = false
    })

    // If we have focused categories, expand the first one that has products
    const focusedCategoriesWithProducts = (currentstore.focusedCategories || [])
      .filter(category => productsByCategory[category]?.length > 0)
    
    if (focusedCategoriesWithProducts.length > 0) {
      initialExpanded[focusedCategoriesWithProducts[0]] = true
    } else if (categories.length > 0) {
      // If no focused categories with products, expand the first category
      initialExpanded[categories[0]] = true
    }

    setDisplayedProducts(initialProducts)
    setHasMoreStates(initialHasMore)
    setExpandedCategories(initialExpanded)
  }, [])

  // Search function
  const handleSearch = (query) => {
    setSearchQuery(query)
    if (query.trim() === '') {
      setShowSearchResults(false)
      setSearchResults([])
      return
    }

    const results = products.filter(product => 
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.sku.toLowerCase().includes(query.toLowerCase())
    )
    setSearchResults(results)
    setShowSearchResults(true)
  }

  // Handle category click in panel
  const handlePanelCategoryClick = (category) => {
    setSelectedPanelCategory(category)
    setShowingProducts(true)
    
    // Also toggle the category in the main view
    toggleCategory(category)
  }

  // Handle back to categories
  const handleBackToCategories = () => {
    setSelectedPanelCategory(null)
    setShowingProducts(false)
  }

  // Get focused categories that exist in productsByCategory
  const availableFocusedCategories = (currentstore.focusedCategories || [])
    .filter(category => productsByCategory[category]?.length > 0)

  return (
    <div className="min-h-screen" style={themeStyles}>
      <Head>
        <title>{currentstore.name}</title>
        <meta name="description" content={currentstore.slogan} />
        <meta property="og:title" content={currentstore.name} key="title" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <DesktopHeader 
        store={store}
        currentstore={currentstore}
        onSearch={handleSearch}
        onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />

      {/* Search Results Panel */}
      {showSearchResults && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setShowSearchResults(false)}>
          <div 
            className="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl p-4 max-h-[80vh] overflow-y-auto"
            onClick={e => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Kết quả tìm kiếm</h2>
              <button
                onClick={() => setShowSearchResults(false)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-3">
              {searchResults.length > 0 ? (
                searchResults.map(product => (
                  <a
                    key={product.id}
                    href={`/${store}/product/${slugify(product.sku)}`}
                    className="flex items-start gap-3 p-2 border rounded-lg hover:shadow-sm transition-shadow"
                    onClick={() => setShowSearchResults(false)}
                  >
                    <div className="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden bg-gray-50">
                      {product.image?.[0] && (
                        <img
                          src={product.image[0]}
                          alt={product.name}
                          className="h-full w-full object-cover"
                          loading="lazy"
                        />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm text-gray-900 line-clamp-2">{product.name}</h3>
                      {product.price && (
                        <p className="text-orange-600 text-sm mt-1">
                          {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                        </p>
                      )}
                    </div>
                  </a>
                ))
              ) : (
                <p className="text-center text-gray-500 py-4">Không tìm thấy sản phẩm nào</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Categories Panel */}
      <DesktopCategoriesPanel 
        showCategoriesPanel={showCategoriesPanel}
        setShowCategoriesPanel={setShowCategoriesPanel}
        showingProducts={showingProducts}
        selectedPanelCategory={selectedPanelCategory}
        categoryDisplayNames={categoryDisplayNames}
        handleBackToCategories={handleBackToCategories}
        availableFocusedCategories={availableFocusedCategories}
        categoryImages={categoryImages}
        handlePanelCategoryClick={handlePanelCategoryClick}
        productsByCategory={productsByCategory}
        store={store}
      />

      {/* Main Content - mobile optimized */}
      <main className="max-w-7xl mx-auto px-2 sm:px-6 lg:px-8">
        {/* Hot Products */}
        {hotProducts && hotProducts.length > 0 && (
          <section className="mb-6">
            <HotProducts
              products={hotProducts}
              store={store}
            />
          </section>
        )}

        {/* Products by Category - mobile optimized */}
        <section className="space-y-4">
          {/* Promotional Banners */}
          <div className="grid grid-cols-2 gap-2">
            {customBanners.map((banner, index) => (
              <div 
                key={index}
                className={`flex items-center justify-between p-3 rounded-lg ${banner.bgColor}`}
              >
                <div>
                  <h3 className={`font-medium text-base ${banner.textColor}`}>{banner.title}</h3>
                  <p className={`text-sm ${banner.messageColor}`}>{banner.message}</p>
                </div>
                <div className={banner.iconColor}>
                  {banner.icon}
                </div>
              </div>
            ))}
          </div>

          {/* Banner Message */}
          {bannermessage && (
            <div className="px-4 py-2 bg-orange-100 text-orange-800 text-sm text-center rounded-lg">
              {bannermessage}
            </div>
          )}

          {/* Focused Categories Section */}
          <FocusedCategories 
            availableFocusedCategories={availableFocusedCategories}
            categoryImages={categoryImages}
            expandedCategories={expandedCategories}
            toggleCategory={toggleCategory}
          />

          {/* All Categories */}
          {/* <AllCategories
            productsByCategory={productsByCategory}
            categoryImages={categoryImages}
            expandedCategories={expandedCategories}
            toggleCategory={toggleCategory}
          /> */}

          {/* Main Product List */}
          {Object.entries(productsByCategory).map(([category, categoryProducts]) => (
            expandedCategories[category] && (
              <div key={category} className="border rounded-lg overflow-hidden">
                <div className="p-3">
                  <div className="text-center mb-3">
                    <h3 className="text-lg font-medium text-gray-800">
                      {categoryDisplayNames[category] || category}
                    </h3>
                  </div>
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                    {displayedProducts[category]?.map(product => (
                      <a
                        key={product.id}
                        href={`/${store}/product/${slugify(product.sku)}`}
                        className="flex flex-col items-center"
                      >
                        <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-100 mb-1">
                          {product.image?.[0] && (
                            <img
                              src={product.image[0]}
                              alt={product.name}
                              className="w-full h-full object-cover"
                              loading="lazy"
                            />
                          )}
                        </div>
                        <div className="text-center px-1">
                          <h3 className="text-xs text-gray-900 line-clamp-2">{product.name}</h3>
                          {product.price && (
                            <p className="text-orange-600 text-xs mt-0.5">
                              {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                            </p>
                          )}
                          {/* {product.validity && (
                            <p className="text-gray-600 text-xs mt-0.5">
                              Hiệu lực: {product.validity}
                            </p>
                          )} */}
                          {product.territory && (
                            <p className="text-gray-600 text-xs mt-0.5">
                              {product.territory}
                            </p>
                          )}
                        </div>
                      </a>
                    ))}
                  </div>

                  {/* Load More button for each category */}
                  {hasMoreStates[category] && (
                    <div className="mt-4 flex justify-center">
                      <button
                        onClick={() => loadMoreProducts(category)}
                        disabled={loadingStates[category]}
                        className="inline-flex items-center px-4 py-2 border border-orange-500 text-sm font-medium rounded-md text-orange-700 bg-white hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loadingStates[category] ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500 mr-2"></div>
                            Đang tải...
                          </>
                        ) : (
                          'Xem thêm sản phẩm'
                        )}
                      </button>
                    </div>
                  )}
                  {!hasMoreStates[category] && displayedProducts[category]?.length > 0 && displayedProducts[category]?.length < productsByCategory[category]?.length && (
                    <p className="text-center text-gray-500 text-xs mt-4">Không còn sản phẩm nào</p>
                  )}
                </div>
              </div>
            )
          ))}
        </section>
        {/* Social Share - mobile optimized */}
        <section className="bg-white rounded-xl shadow-sm p-4 mt-4">
          <SocialShare currentUrl={currentUrl} />
        </section>

        {/* QR Code - mobile optimized */}
        {/* {shopLinkQRCode && (
          <section className="bg-white rounded-xl shadow-sm p-4 mt-6">
            <div className="flex justify-center">
              <img
                src={shopLinkQRCode}
                alt="Store QR Code"
                className="h-32"
                loading="lazy"
              />
            </div>
          </section>
        )} */}

        <section className="bg-white rounded-xl shadow-sm p-4 mt-6">
          <div className="flex justify-center">
            <img
              src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.sim.more.qr.png"
              alt="SIM & More"
              className="h-32"
              loading="lazy"
            />
          </div>
        </section>

      </main>
    </div>
  )
}

export default SheinShopLayout