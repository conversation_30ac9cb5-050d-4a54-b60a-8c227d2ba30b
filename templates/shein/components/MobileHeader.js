'use client';

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { slugify } from '../../../utils/helpers'
import NavigationMenu from '../../../components/NavigationMenu'

const MobileHeader = ({ 
  store, 
  currentstore, 
  onSearch, 
  onToggleCategories,
  searchQuery,
  setSearchQuery,
  inventory
}) => {
  const router = useRouter()
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false)
  const [productsByCategory, setProductsByCategory] = useState({})
  const [categoryImages, setCategoryImages] = useState({})
  const [showingProducts, setShowingProducts] = useState(false)
  const [selectedPanelCategory, setSelectedPanelCategory] = useState(null)
  const [isSearchMode, setIsSearchMode] = useState(false)

  // Group products by category
  useEffect(() => {
    if (!inventory) return;

    const groupedProducts = {}
    inventory.forEach(product => {
      if (Array.isArray(product.categories)) {
        product.categories.forEach(category => {
          if (!groupedProducts[category]) {
            groupedProducts[category] = []
          }
          groupedProducts[category].push(product)
        })
      }
    })
    setProductsByCategory(groupedProducts)

    // Get category images
    const images = {}
    Object.entries(groupedProducts).forEach(([category, products]) => {
      const firstProduct = products[0]
      if (firstProduct && Array.isArray(firstProduct.image) && firstProduct.image.length > 0) {
        images[category] = firstProduct.image[0]
      }
    })
    setCategoryImages(images)
  }, [inventory])

  const handleToggleCategories = () => {
    setShowCategoriesPanel(!showCategoriesPanel)
    if (onToggleCategories) {
      onToggleCategories(!showCategoriesPanel)
    }
  }

  const handlePanelCategoryClick = (category) => {
    setSelectedPanelCategory(category)
    setShowingProducts(true)
  }

  const handleBackToCategories = () => {
    setSelectedPanelCategory(null)
    setShowingProducts(false)
  }

  const handleSearchToggle = () => {
    setIsSearchMode(!isSearchMode)
    if (!isSearchMode) {
      setSearchQuery('')
    }
  }

  return (
    <>
      <header className="sticky top-0 z-50 bg-white shadow-sm">
        {/* Top Header with Logo and Search */}
        <div className="flex items-center justify-between px-4 py-2 border-b">
          <div className="flex items-center flex-1">
            {/* Logo */}
            <Link href={`/${store}`} className="flex-shrink-0 mr-4 animate-fade-in">
              <img
                src={currentstore.logo || "/default-logo.png"}
                alt={currentstore.name}
                className="h-12 hover:scale-105 transition-transform duration-300 hover:shadow-md rounded"
              />
            </Link>

            {/* Shop Name or Search Bar */}
            <div className="flex-1 max-w-md">
              {isSearchMode ? (
                <div className="relative">
                  <input
                    type="search"
                    placeholder="Tìm kiếm..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      onSearch(e.target.value);
                    }}
                    className="w-full pl-4 pr-10 py-1.5 text-sm border rounded-full bg-gray-50 focus:outline-none focus:ring-1 focus:ring-black focus:border-black"
                    autoFocus
                  />
                  <button 
                    onClick={handleSearchToggle}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  >
                    <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ) : (
                <Link href={`/${store}`} className="flex-shrink-0 mr-4 animate-fade-in">
                  <h1 className="hidden md:block text-sm md:text-lg font-semibold truncate bg-gradient-to-r from-black to-gray-600 bg-clip-text text-transparent animate-fade-in hover:scale-105 transition-transform">{currentstore.name}</h1>
                </Link>
              )}
            </div>

            {/* Icons */}
            <div className="flex items-center ml-4 space-x-4">
              {!isSearchMode && (
                <button 
                  onClick={handleSearchToggle}
                  className="p-1"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              )}
              <Link href={`/${store}/customer/dashboard`}>
                <button className="p-1">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </button>
              </Link>
              <Link href={`/${store}/cart`}>
                <button className="p-1">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="overflow-x-auto scrollbar-hide">
          <div className="flex px-4 py-2 space-x-6 whitespace-nowrap">
            <button
              onClick={handleToggleCategories}
              className="text-sm font-medium hover:text-orange-500 transition-colors"
            >
              Sản phẩm
            </button>

            <NavigationMenu store={store} />
          </div>
        </nav>
      </header>

      {/* Categories Panel */}
      {showCategoriesPanel && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
          <div 
            className="fixed inset-y-0 left-0 w-4/5 bg-white shadow-lg transform transition-transform duration-300 ease-in-out"
            onClick={e => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 z-10 bg-white border-b">
              <div className="flex items-center justify-between px-4 py-3">
                {showingProducts ? (
                  <>
                    <button
                      onClick={handleBackToCategories}
                      className="p-2 -ml-2 hover:bg-gray-100 rounded-full"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <h2 className="text-lg font-medium flex-1 text-center">{selectedPanelCategory}</h2>
                  </>
                ) : (
                  <h2 className="text-lg font-medium">Categories</h2>
                )}
                <button
                  onClick={() => setShowCategoriesPanel(false)}
                  className="p-2 hover:bg-gray-100 rounded-full"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="overflow-y-auto h-[calc(100vh-56px)]">
              {!showingProducts ? (
                // Categories List
                Object.keys(productsByCategory).map((category) => (
                  <button
                    key={category}
                    onClick={() => handlePanelCategoryClick(category)}
                    className="flex items-center w-full px-4 py-3 hover:bg-gray-50 active:bg-gray-100"
                  >
                    <div className="flex items-center flex-1">
                      {categoryImages[category] ? (
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 mr-4 flex-shrink-0">
                          <img
                            src={categoryImages[category]}
                            alt={category}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gray-100 mr-4 flex-shrink-0 flex items-center justify-center">
                          <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                          </svg>
                        </div>
                      )}
                      <span className="text-base">{category}</span>
                    </div>
                    <svg className="w-5 h-5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                ))
              ) : (
                // Products List in Panel
                <div className="grid grid-cols-4 gap-2 p-3">
                  {productsByCategory[selectedPanelCategory]?.map((product) => (
                    <a
                      key={product.id}
                      href={`/${store}/product/${slugify(product.sku)}`}
                      className="flex flex-col items-center"
                      onClick={() => setShowCategoriesPanel(false)}
                    >
                      <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-100 mb-1.5">
                        {product.image?.[0] && (
                          <img
                            src={product.image[0]}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>
                      <h3 className="text-xs text-center line-clamp-2 text-gray-700 px-1">{product.name}</h3>
                      {product.price && (
                        <p className="text-orange-600 text-xs mt-0.5">
                          {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                        </p>
                      )}
                    </a>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div 
            className="fixed inset-0 z-[-1]" 
            onClick={() => setShowCategoriesPanel(false)}
          />
        </div>
      )}
    </>
  )
}

export default MobileHeader