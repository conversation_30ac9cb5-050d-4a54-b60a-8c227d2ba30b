import React from 'react';
import { slugify } from "../../../utils/helpers";

const DesktopCategoriesPanel = ({
  showCategoriesPanel,
  setShowCategoriesPanel,
  showingProducts,
  selectedPanelCategory,
  categoryDisplayNames,
  handleBackToCategories,
  availableFocusedCategories,
  categoryImages,
  handlePanelCategoryClick,
  productsByCategory,
  store
}) => {
  if (!showCategoriesPanel) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setShowCategoriesPanel(false)}>
      <div 
        className="fixed inset-y-0 left-0 w-4/5 bg-white shadow-xl transform transition-transform duration-300 ease-in-out rounded-r-xl overflow-hidden"
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        <div className="sticky top-0 z-10 bg-gradient-to-r from-orange-50 to-white border-b shadow-sm">
          <div className="flex items-center justify-between px-4 py-4">
            {showingProducts ? (
              <>
                <button
                  onClick={handleBackToCategories}
                  className="p-2 -ml-2 hover:bg-white/80 active:bg-orange-100 rounded-full transition-all duration-200"
                >
                  <svg className="w-6 h-6 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <h2 className="text-lg font-medium flex-1 text-center text-gray-800">
                  {categoryDisplayNames[selectedPanelCategory] || selectedPanelCategory}
                </h2>
              </>
            ) : (
              <h2 className="text-lg font-semibold text-gray-800">Categories</h2>
            )}
            <button
              onClick={() => setShowCategoriesPanel(false)}
              className="p-2.5 bg-orange-100 hover:bg-orange-200 active:bg-orange-300 text-orange-700 rounded-full transition-all duration-200 transform hover:rotate-90 hover:scale-105"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto h-[calc(100vh-56px)] bg-gradient-to-b from-white to-orange-50/30">
          {!showingProducts ? (
            // Categories List
            <>
              {/* Focused Categories */}
              {availableFocusedCategories.length > 0 && (
                <div className="mb-4 px-3 pt-3">
                  <h3 className="text-sm uppercase text-gray-500 font-medium mb-2 px-1">Featured Categories</h3>
                  {availableFocusedCategories.map((category) => (
                    <button
                      key={`focused-${category}`}
                      onClick={() => handlePanelCategoryClick(category)}
                      className="flex items-center w-full px-4 py-3 bg-gradient-to-r from-yellow-50 to-orange-50 hover:from-yellow-100 hover:to-orange-100 active:from-yellow-200 active:to-orange-200 mb-2 rounded-lg shadow-sm transition-all duration-200"
                    >
                      <div className="flex items-center flex-1">
                        {categoryImages[category] ? (
                          <div className="w-12 h-12 rounded-full overflow-hidden bg-white mr-4 flex-shrink-0 border-2 border-yellow-200 shadow-sm">
                            <img
                              src={categoryImages[category]}
                              alt={categoryDisplayNames[category] || category}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-100 to-orange-100 mr-4 flex-shrink-0 flex items-center justify-center border-2 border-yellow-200 shadow-sm">
                            <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                          </div>
                        )}
                        <span className="text-base font-medium text-gray-800">{categoryDisplayNames[category] || category}</span>
                      </div>
                      <svg className="w-5 h-5 text-orange-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  ))}
                </div>
              )}

              {/* All Categories */}
              <div className="px-3">
                <h3 className="text-sm uppercase text-gray-500 font-medium mb-2 px-1">All Categories</h3>
                {Object.keys(productsByCategory).map((category) => (
                  <button
                    key={category}
                    onClick={() => handlePanelCategoryClick(category)}
                    className="flex items-center w-full px-4 py-3 hover:bg-orange-50 active:bg-orange-100 mb-1 rounded-lg transition-all duration-200"
                  >
                    <div className="flex items-center flex-1">
                      {categoryImages[category] ? (
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 mr-4 flex-shrink-0 border border-gray-200 shadow-sm">
                          <img
                            src={categoryImages[category]}
                            alt={categoryDisplayNames[category] || category}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gray-100 mr-4 flex-shrink-0 flex items-center justify-center border border-gray-200 shadow-sm">
                          <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                          </svg>
                        </div>
                      )}
                      <span className="text-base text-gray-700">{categoryDisplayNames[category] || category}</span>
                    </div>
                    <svg className="w-5 h-5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                ))}
              </div>
            </>
          ) : (
            // Products List in Panel
            <div className="p-4">
              <div className="grid grid-cols-2 gap-3 sm:grid-cols-4">
                {productsByCategory[selectedPanelCategory]?.map((product) => (
                  <a
                    key={product.id}
                    href={`/${store}/product/${slugify(product.sku)}`}
                    className="flex flex-col items-center bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                    onClick={() => setShowCategoriesPanel(false)}
                  >
                    <div className="w-20 h-20 rounded-full overflow-hidden bg-orange-50 mb-2 border border-orange-100 shadow-sm">
                      {product.image?.[0] && (
                        <img
                          src={product.image[0]}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      )}
                    </div>
                    <h3 className="text-xs text-center line-clamp-2 text-gray-800 font-medium px-1">{product.name}</h3>
                    {product.price && (
                      <p className="text-orange-600 text-xs font-semibold mt-1">
                        {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                      </p>
                    )}
                    {product.validity && (
                      <p className="text-gray-600 text-xs mt-0.5">
                        Validity: {product.validity}
                      </p>
                    )}
                    {product.territory && (
                      <p className="text-gray-600 text-xs mt-0.5">
                        Territory: {product.territory}
                      </p>
                    )}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DesktopCategoriesPanel;