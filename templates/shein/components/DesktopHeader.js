'use client';

import Link from 'next/link'
import { useState, useContext } from 'react'
import { useRouter } from 'next/router'
import NavigationMenu from '../../../components/NavigationMenu'
import { SiteContext } from '../../../context/mainContext'

const DesktopHeader = ({
  store,
  currentstore,
  onSearch,
  onToggleCategories,
  searchQuery,
  setSearchQuery
}) => {
  const router = useRouter()
  const context = useContext(SiteContext)

  const [isSearchMode, setIsSearchMode] = useState(false)



  const handleToggleCategories = () => {
    if (onToggleCategories) {
      onToggleCategories()
    }
  }

  const handleSearchToggle = () => {
    setIsSearchMode(!isSearchMode)
    if (!isSearchMode) {
      setSearchQuery('')
    }
  }

  return (
    <>
      <header className="sticky top-0 z-50 bg-white shadow-sm">
        {/* Top Header with Logo and Search */}
        <div className="flex items-center justify-between px-4 py-2 border-b">
          <div className="flex items-center flex-1">
            {/* Logo */}
            <Link href={`/${store}`} className="flex-shrink-0 mr-4 animate-fade-in">
              <img
                src={currentstore.logo || "/default-logo.png"}
                alt={currentstore.name}
                className="h-12 hover:scale-105 transition-transform duration-300 hover:shadow-md rounded" 
              />
            </Link>

            {/* Shop Name or Search Bar */}
            <div className="flex-1 max-w-md">
              {isSearchMode ? (
                <div className="relative">
                  <input
                    type="search"
                    placeholder="Tìm kiếm..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      onSearch(e.target.value);
                    }}
                    className="w-full pl-4 pr-10 py-1.5 text-sm border rounded-full bg-gray-50 focus:outline-none focus:ring-1 focus:ring-black focus:border-black"
                    autoFocus
                  />
                  <button
                    onClick={handleSearchToggle}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  >
                    <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ) : (
                <Link href={`/${store}`} className="flex-shrink-0 mr-4 animate-fade-in">
                  <h1 className="text-lg font-semibold truncate bg-gradient-to-r from-black to-gray-600 bg-clip-text text-transparent animate-fade-in hover:scale-105 transition-transform">{currentstore.name}</h1>
                </Link>
              )}
            </div>


            <nav className="overflow-x-auto scrollbar-hide">
              <div className="flex px-4 py-2 space-x-6 whitespace-nowrap">
                <button
                  onClick={handleToggleCategories}
                  className="text-sm font-medium hover:text-orange-500 transition-colors"
                >
                  Sản phẩm
                </button>

                <NavigationMenu store={store} />
              </div>
            </nav>

            {/* Icons */}
            <div className="flex items-center ml-4 space-x-4">
              {!isSearchMode && (
                <button
                  onClick={handleSearchToggle}
                  className="p-1"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              )}
              <Link href={`/${store}/customer/dashboard`}>
                <button className="p-1">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </button>
              </Link>
              <Link href={`/${store}/cart`}>
                <button className="p-1 relative">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  {context && context.numberOfItemsInCart > 0 && (
                    <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {context.numberOfItemsInCart}
                    </div>
                  )}
                </button>
              </Link>
            </div>
          </div>
        </div>
      </header>


    </>
  )
}

export default DesktopHeader