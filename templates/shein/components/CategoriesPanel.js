import React from 'react';
import { slugify } from "../../../utils/helpers";

const CategoriesPanel = ({
  showCategoriesPanel,
  setShowCategoriesPanel,
  showingProducts,
  selectedPanelCategory,
  categoryDisplayNames,
  handleBackToCategories,
  availableFocusedCategories,
  categoryImages,
  handlePanelCategoryClick,
  productsByCategory,
  store
}) => {
  if (!showCategoriesPanel) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
      <div 
        className="fixed inset-y-0 left-0 w-4/5 bg-white shadow-lg transform transition-transform duration-300 ease-in-out"
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white border-b">
          <div className="flex items-center justify-between px-4 py-3">
            {showingProducts ? (
              <>
                <button
                  onClick={handleBackToCategories}
                  className="p-2 -ml-2 hover:bg-gray-100 rounded-full"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <h2 className="text-lg font-medium flex-1 text-center">
                  {categoryDisplayNames[selectedPanelCategory] || selectedPanelCategory}
                </h2>
              </>
            ) : (
              <h2 className="text-lg font-medium">Categories</h2>
            )}
            <button
              onClick={() => setShowCategoriesPanel(false)}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto h-[calc(100vh-56px)]">
          {!showingProducts ? (
            // Categories List
            <>
              {/* Focused Categories */}
              {availableFocusedCategories.length > 0 && (
                <div className="mb-3">
                  {availableFocusedCategories.map((category) => (
                    <button
                      key={`focused-${category}`}
                      onClick={() => handlePanelCategoryClick(category)}
                      className="flex items-center w-full px-4 py-3 bg-yellow-50 hover:bg-yellow-100 active:bg-yellow-200 mb-1"
                    >
                      <div className="flex items-center flex-1">
                        {categoryImages[category] ? (
                          <div className="w-12 h-12 rounded-full overflow-hidden bg-yellow-100 mr-4 flex-shrink-0">
                            <img
                              src={categoryImages[category]}
                              alt={categoryDisplayNames[category] || category}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-12 h-12 rounded-full bg-yellow-100 mr-4 flex-shrink-0 flex items-center justify-center">
                            <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                          </div>
                        )}
                        <span className="text-base font-medium">{categoryDisplayNames[category] || category}</span>
                      </div>
                      <svg className="w-5 h-5 text-yellow-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  ))}
                </div>
              )}

              {/* All Categories */}
              {Object.keys(productsByCategory).map((category) => (
                <button
                  key={category}
                  onClick={() => handlePanelCategoryClick(category)}
                  className="flex items-center w-full px-4 py-3 hover:bg-gray-50 active:bg-gray-100"
                >
                  <div className="flex items-center flex-1">
                    {categoryImages[category] ? (
                      <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 mr-4 flex-shrink-0">
                        <img
                          src={categoryImages[category]}
                          alt={categoryDisplayNames[category] || category}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-100 mr-4 flex-shrink-0 flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                      </div>
                    )}
                    <span className="text-base">{categoryDisplayNames[category] || category}</span>
                  </div>
                  <svg className="w-5 h-5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              ))}
            </>
          ) : (
            // Products List in Panel
            <div className="grid grid-cols-4 gap-2 p-3">
              {productsByCategory[selectedPanelCategory]?.map((product) => (
                <a
                  key={product.id}
                  href={`/${store}/product/${slugify(product.sku)}`}
                  className="flex flex-col items-center"
                  onClick={() => setShowCategoriesPanel(false)}
                >
                  <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-100 mb-1.5">
                    {product.image?.[0] && (
                      <img
                        src={product.image[0]}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div className="text-center px-1">
                    <h3 className="text-xs text-gray-900 line-clamp-2">{product.name}</h3>
                    {product.price && (
                      <p className="text-orange-600 text-xs mt-0.5">
                        {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                      </p>
                    )}
                    {product.validity && (
                      <p className="text-gray-600 text-xs mt-0.5">
                        Validity: {product.validity}
                      </p>
                    )}
                    {product.territory && (
                      <p className="text-gray-600 text-xs mt-0.5">
                        Territory: {product.territory}
                      </p>
                    )}
                  </div>
                </a>
              ))}
            </div>
          )}
        </div>
      </div>
      <div 
        className="fixed inset-0 z-[-1]" 
        onClick={() => setShowCategoriesPanel(false)}
      />
    </div>
  );
};

export default CategoriesPanel; 