import React from 'react'
import categoryDisplayNames from '../../../utils/categoryDisplayNames.json'

const FocusedCategories = ({
  availableFocusedCategories,
  categoryImages,
  expandedCategories,
  toggleCategory
}) => {
  if (availableFocusedCategories.length === 0) {
    return null
  }
  
  // Get display name for a category, fallback to original name if mapping doesn't exist
  const getDisplayName = (category) => {
    return categoryDisplayNames[category] || category
  }
  
  return (
    <div className="mb-3">
      {/* <h3 className="text-sm font-semibold mb-2 text-gray-700 px-1">Featured Categories</h3> */}
      <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
        {availableFocusedCategories.map((category) => (
          <button
            key={`focused-${category}`}
            onClick={() => toggleCategory(category)}
            className={`flex flex-col items-center justify-center p-2 rounded-full aspect-square transition-all ${
              expandedCategories[category]
                ? 'bg-orange-500 text-white'
                : 'bg-yellow-100 hover:bg-yellow-200 text-gray-700'
            }`}
          >
            {categoryImages[category] ? (
              <div className="w-12 h-12 mb-1 rounded-full overflow-hidden">
                <img
                  src={categoryImages[category]}
                  alt={getDisplayName(category)}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-12 h-12 mb-1 rounded-full bg-yellow-200 flex items-center justify-center">
                <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </div>
            )}
            <span className="text-xs text-center font-medium line-clamp-2">{getDisplayName(category)}</span>
          </button>
        ))}
      </div>
    </div>
  )
}

export default FocusedCategories 