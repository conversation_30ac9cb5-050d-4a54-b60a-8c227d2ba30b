import Head from "next/head"
import ListItemShop from "../../components/ListItemShop"

export default function MarketplaceRootLayout({ allshops, currentUrl }) {
  return (
    <div className="w-full">
      <Head>
        <title>Marketplace</title>
        <meta name="description" content="Marketplace" />
        <meta property="og:title" content="Marketplace" key="title" />
      </Head>
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Welcome to Marketplace</h1>
          <p className="text-xl">Discover amazing shops all in one place</p>
        </div>
      </div>
      <div className="bg-gray-100 py-16">
        <div className="max-w-7xl mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-12">Featured Shops</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {allshops.map((store, index) => (
              <ListItemShop
                key={index}
                link={`/${store.storeId}`}
                title={store.name}
                imageSrc={store.logo}
                className="transform hover:scale-105 transition-transform duration-200"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 