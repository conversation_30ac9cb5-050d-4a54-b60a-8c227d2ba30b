import Head from "next/head"
import HotProducts from "../../components/HotProducts"
import SocialShare from "../../components/SocialShare"

export default function MarketplaceShopLayout({ 
  currentstore,
  hotProducts,
  store,
  currentUrl 
}) {
  return (
    <div className="w-full">
      <Head>
        <title>{currentstore.name}</title>
        <meta name="description" content={currentstore.slogan} />
        <meta property="og:title" content={currentstore.name} key="title" />
      </Head>
      
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">{currentstore.name}</h1>
          <p className="text-xl">{currentstore.slogan}</p>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 py-12">
        <HotProducts 
          products={hotProducts}
          store={store}
        />
        <SocialShare currentUrl={currentUrl} />
      </div>
    </div>
  )
} 