import { useRouter } from 'next/router';
import Link from 'next/link';
import { slugify } from '../utils/helpers';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';
import { navItemLength } from '../ecommerce.config';
import Head from 'next/head';
import { useState, useEffect } from 'react';
import Button from '../components/Button';
import LanguageSwitcher from "../components/LanguageSwitcher";
import SiteNavigation from '../components/Navigation';


export default function Layout({ children, categoriesarrayA = {}, categories = [], allStores = [], hideMainNavigation = false }) {
  const router = useRouter();
  const { store } = router.query;
  const [isMounted, setIsMounted] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  // Add defensive checks
  if (!Array.isArray(categories)) categories = [];
  if (categories.length > navItemLength) {
    categories = categories.slice(0, navItemLength);
  }

  let categoriesCurrent = [];
  let storeobject = {};
  
  if (store && categoriesarrayA) {
    //inside a store
    categoriesCurrent = categoriesarrayA[store] || [];
    storeobject = allStores.find(storeitem => storeitem?.storeId === store) || {};
  } else if (categoriesarrayA) {
    //no store, but the root
    categoriesCurrent = categoriesarrayA.all || [];
    storeobject = {};
  }

  // Define paths where you want to disable menus
  const disabledPaths = ['/', '/store', '/our-story', '/ctv'];

  // Function to check if the current path is disabled
  const isDisabledPath = () => {
    return disabledPaths.includes(router.pathname) || hideMainNavigation;
  };

  // JavaScript to toggle the active class on click
  useEffect(() => {
    const categoryPanel = document.querySelector('.category-panel');
    if (categoryPanel) {
      categoryPanel.addEventListener('click', function() {
      /* categoryPanel.addEventListener('mouseover', function() { */
        categoryPanel.classList.toggle('active');
      });
    }
  }, []);

  // Handle client-side rendering and responsive checks
  useEffect(() => {
    setIsMounted(true);
    
    // Check if we're on desktop
    const checkIfDesktop = () => {
      setIsDesktop(window.innerWidth > 768);
    };
    
    // Initial check
    checkIfDesktop();
    
    // Add resize listener
    window.addEventListener('resize', checkIfDesktop);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfDesktop);
  }, []);


  const downloadImage = (imageLink) => {
    // Create a new anchor element
    const anchor = document.createElement('a');
  
    // Set the href attribute to the image URL
    anchor.href = imageLink;
  
    // Generate a filename based on the image link
    let filename;
    if (imageLink.startsWith('data:image')) {
      // If the image link is a data URL, generate a unique filename
      const timestamp = new Date().getTime();
      filename = `image_${timestamp}.jpg`;
    } else {
      // Extract the filename from the image link
      filename = imageLink.substring(imageLink.lastIndexOf('/') + 1);
    }
  
    // Set the download attribute to force download with the filename
    anchor.download = filename;
  
    // Simulate a click on the anchor element to trigger the download
    anchor.click();
  }

  // Add more defensive checks when accessing storeobject properties

  // Fixed logo source handling for both array and string formats
  let logoSrc = '/logo.png'; // Default fallback
  
  if (store && storeobject?.logo) {
    // Handle logo as array
    if (Array.isArray(storeobject.logo) && storeobject.logo.length > 0) {
      logoSrc = storeobject.logo[0];
    } 
    // Handle logo as string
    else if (typeof storeobject.logo === 'string' && storeobject.logo.trim() !== '') {
      logoSrc = storeobject.logo;
    }
  }

  return (
    <div>
      <Head>
        <title>SIM Store - MAG Group</title>
        <meta name="description" content={`Check out`} />
        <meta property="og:title" content="ShopMe" key="title" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Only render the navigation component once mounted on client-side and if it's desktop */}
      {/* {isMounted && isDesktop && (
        <SiteNavigation 
          store={store}
          storeobject={storeobject}
          categoriesCurrent={categoriesCurrent}
          isDisabledPath={isDisabledPath}
          logoSrc={logoSrc}
          slugify={slugify}
        />
      )} */}

      <div className="px-4 pb-10 flex justify-center">
        <main className="w-full max-w-7xl">{children}</main>
      </div>
      <footer className="flex justify-center">
        <div className="
        sm:flex-row sm:items-center
        flex-col
        flex w-full max-w-7xl px-4 py-8
        border-solid
        border-t border-gray-300">
          {/* <span className="block text-gray-700 text-xs  ml-3">© 2025 MAG GROUP - TIỂU THƯƠNG ONLINE</span> */}
          <span className="block text-gray-700 text-xs ml-3 whitespace-pre-line">[© 2025 SIM ONLINE STORE] {storeobject.shopfooter ? ' ' + storeobject.shopfooter : ''}</span>
          <div className="
            sm:justify-end sm:m-0
            flex flex-1 mt-4
          ">
            {/* App Policy Link */}
            <Link href={store ? `/${store}/apppolicy` : '/apppolicy'} className="text-gray-600 hover:text-gray-800 text-xs transition-colors duration-200">
              Chính sách ứng dụng | App Policy
            </Link>
          </div>
        </div>
      </footer>
      <ToastContainer 
        autoClose={3000} 
        position="top-right" 
        hideProgressBar={false} 
        newestOnTop={false} 
        closeOnClick={true} 
        rtl={false} 
        pauseOnFocusLoss={true} 
        draggable={true} 
        pauseOnHover={true} 
      />
    </div>
  );
}
