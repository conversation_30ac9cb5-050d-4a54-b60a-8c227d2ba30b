// Comprehensive E3501 Fix Verification Script
// Run this after implementing all fixes: node test-e3501-comprehensive.js

const axios = require('axios');

console.log('🚀 E3501 COMPREHENSIVE FIX VERIFICATION');
console.log('==========================================');

// Test cases covering all the fixes from SINOPAC_E3501_FIXES.md
const testCases = [
  {
    name: '1. Original Vietnamese product name (should now work)',
    payload: {
      OrderNo: 'MAG-13677',
      Amount: '3750',
      PrdtName: 'CHUNG HOA 1 NĂM SPECIAL'
    },
    expectedFix: 'Vietnamese character cleaning'
  },
  {
    name: '2. Product name with many special characters',
    payload: {
      OrderNo: 'TEST-001',
      Amount: '1000',
      PrdtName: 'CHÚNG TÔI LÀ SỐ 1! @#$%^&*()'
    },
    expectedFix: 'Special character removal'
  },
  {
    name: '3. Very long product name (should be truncated)',
    payload: {
      OrderNo: 'LONG-001',
      Amount: '500',
      PrdtName: 'THIS IS A VERY LONG PRODUCT NAME THAT EXCEEDS THE FIFTY CHARACTER LIMIT AND SHOULD BE TRUNCATED'
    },
    expectedFix: 'Length truncation'
  },
  {
    name: '4. Amount with extra characters',
    payload: {
      OrderNo: 'AMT-001',
      Amount: 'NT$3,750.00',
      PrdtName: 'TEST PRODUCT'
    },
    expectedFix: 'Amount cleaning'
  },
  {
    name: '5. OrderNo with special characters',
    payload: {
      OrderNo: 'ORDER#123@TEST!',
      Amount: '2000',
      PrdtName: 'SIMPLE PRODUCT'
    },
    expectedFix: 'OrderNo sanitization'
  },
  {
    name: '6. Mixed encoding issues',
    payload: {
      OrderNo: 'MIX-001',
      Amount: '1500',
      PrdtName: 'Sản phẩm đặc biệt với 123!@#'
    },
    expectedFix: 'Full encoding cleanup'
  }
];

async function testAPI(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log(`📋 Expected fix: ${testCase.expectedFix}`);
  console.log(`📤 Sending:`, JSON.stringify(testCase.payload, null, 2));
  
  try {
    const startTime = Date.now();
    const response = await axios.post('http://localhost:3000/api/payment/sinopac/qrcode', testCase.payload, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });
    
    const duration = Date.now() - startTime;
    
    console.log(`✅ SUCCESS (${duration}ms)`);
    
    // Check if it's using the fallback system or real API
    if (response.data.Result && response.data.Result.PaymentSystem === 'Functional_Fallback') {
      console.log(`📋 Result: Fallback QR system activated`);
      console.log(`🔧 Data cleaning applied:`, response.data.Result.DataCleaning);
      console.log(`⚠️  Original error was:`, response.data.Result.OriginalError);
    } else {
      console.log(`🎉 REAL API SUCCESS! SinoPAC accepted the cleaned data`);
      console.log(`📋 QR Code:`, response.data.Result?.QRCode?.substring(0, 50) + '...');
    }
    
    return { success: true, usedFallback: !!response.data.Result?.PaymentSystem };
    
  } catch (error) {
    console.log(`❌ FAILED:`, error.response?.data?.message || error.message);
    
    if (error.response?.data?.Result?.OriginalError) {
      console.log(`📋 API Error:`, error.response.data.Result.OriginalError);
    }
    
    return { success: false, error: error.message };
  }
}

async function runComprehensiveTest() {
  console.log(`\n⚠️  IMPORTANT: Make sure to update the callback URL in the code!`);
  console.log(`   Run: ngrok http 3000`);
  console.log(`   Then update getValidCallbackUrl() with your ngrok URL\n`);
  
  const results = [];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    const result = await testAPI(testCase);
    results.push({ ...testCase, ...result });
    
    // Wait between tests
    if (i < testCases.length - 1) {
      console.log(`⏳ Waiting 3 seconds before next test...`);
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // Summary
  console.log(`\n📊 TEST SUMMARY`);
  console.log(`===============`);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  const usedFallback = results.filter(r => r.success && r.usedFallback);
  const usedRealAPI = results.filter(r => r.success && !r.usedFallback);
  
  console.log(`✅ Successful tests: ${successful.length}/${results.length}`);
  console.log(`🔄 Used fallback system: ${usedFallback.length}`);
  console.log(`🎉 Used real SinoPAC API: ${usedRealAPI.length}`);
  console.log(`❌ Failed tests: ${failed.length}`);
  
  if (failed.length > 0) {
    console.log(`\n🚨 FAILED TESTS:`);
    failed.forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
  }
  
  console.log(`\n💡 INTERPRETATION:`);
  if (usedRealAPI.length > 0) {
    console.log(`🎉 EXCELLENT! SinoPAC API is accepting your cleaned data!`);
    console.log(`   The E3501 fixes are working for ${usedRealAPI.length} test cases.`);
  } else if (usedFallback.length > 0) {
    console.log(`🔧 GOOD! Data cleaning is working, but SinoPAC API still has issues.`);
    console.log(`   Check the callback URL and API credentials.`);
    console.log(`   The fallback system ensures payments still work.`);
  } else {
    console.log(`⚠️  NEEDS ATTENTION: Most tests failed.`);
    console.log(`   Check server logs for detailed error messages.`);
  }
  
  console.log(`\n🔧 NEXT STEPS:`);
  console.log(`1. Update callback URL with your ngrok URL`);
  console.log(`2. Check server logs for detailed cleaning results`);
  console.log(`3. Verify SinoPAC credentials are still valid`);
  console.log(`4. Test with real Vietnamese product names`);
}

// Run the comprehensive test
if (require.main === module) {
  runComprehensiveTest().catch(console.error);
}

module.exports = { testAPI, runComprehensiveTest }; 