# Top-Up Storefront Specification
Create a parallel storefront for the shop that focuses on the topup business:
## 1. Overview
The Top-Up storefront is a digital voucher and prepaid service platform that allows users to purchase, manage, and track various digital products including mobile top-ups, game credits, and utility bill payments across multiple countries.

## 2. Main Navigation Structure

### 2.1 Primary Navigation Boxes
The main interface consists of four primary navigation sections:

1. **Buy Voucher** 
   - Primary function for purchasing new vouchers
   - Icon: Shopping cart with voucher
   - Color: Green primary button

2. **My Voucher**
   - View and manage purchased vouchers
   - Icon: Voucher/ticket icon
   - Color: Light green secondary button

3. **Purchase Receipt**
   - Access transaction receipts and proof of purchase
   - Icon: Receipt/document icon
   - Color: Light green secondary button

4. **Order History**
   - View complete transaction history
   - Icon: Clock/history icon
   - Color: Light green secondary button

## 3. Buy Voucher Flow

### 3.1 Country Selection Page
**Page Title:** "Select Category" or "Choose Country"

**Layout:**
- Header with back navigation arrow
- Title: "Select product category to explore:"
- Scrollable list of countries with icons

**Countries List (Minimum Required):**
- 🇮🇩 Indonesia Mobile Top-up and Bill
- 🇹🇼 Taiwan Prepaid Voucher
- 🇲🇾 Malaysia Prepaid Top-up
- 🇻🇳 Vietnam Prepaid Top-up
- 🇵🇭 Philippines Prepaid Top-up
- 🇸🇬 Singapore Prepaid Top-up
- 🎮 Games (Global)
- 🏪 E-wallet Services (GoPay, OVO, Dana, LinkAja, ShopeePay)
- 🛒 Retail Vouchers (Indomaret, Alfamart, Carrefour)
- ⚡ PLN and BPJS (Indonesia Utilities)

**Each Country Item:**
- Country flag icon
- Country name
- Service type description
- Right arrow indicator
- Tap gesture to navigate to provider selection

### 3.2 Provider Selection Page
**Page Title:** "[Country Name] Prepaid Voucher"

**Header:**
- Back arrow to country selection
- Country flag icon
- "Selected Product Category: [Country] Prepaid Voucher"

**Provider Grid Layout:**
- 3-column grid on desktop, 2-column on mobile
- Each provider displayed as a card with:
  - Provider logo/icon
  - Provider name
  - Tap gesture to navigate to products

**Example Providers by Country:**

**Taiwan:**
- IF (Taiwan Mobile prepaid)
- OK (prepaid service)
- Chung Hwa (Chunghwa Telecom)

**Indonesia:**
- Telkomsel
- XL Axiata
- Indosat Ooredoo
- Three (Tri)
- Smartfren

**Malaysia:**
- Maxis
- Celcom
- Digi
- U Mobile

### 3.3 Product Selection Page
**Page Title:** "[Provider Name]"

**Header:**
- Back arrow to provider selection
- Provider logo
- "Selected Provider: [Provider Name]"

**Product Grid:**
- 2-3 column responsive grid
- Product cards showing:
  - Product image/icon
  - Denomination (e.g., "IF 150 NT")
  - Price in local currency (e.g., "NT$ 146")
  - Product type indicator (Elektrik/PIN/Bundle)
  - Special offers or bulk pricing

**Product Categories:**
1. **Basic Top-Up**
   - Various denominations (50, 100, 150, 300, 500, etc.)
   - Both electronic and PIN delivery options

2. **Internet Packages**
   - Time-based (30 days, 7 days)
   - Data-based (1GB, 5GB, unlimited)
   - Combo packages

3. **Bulk Packages**
   - Multi-piece offers (5pcs, 10pcs)
   - Discounted per-unit pricing

**Example Product Structure (Taiwan IF):**
- IF 150 NT (Elektrik) - NT$ 146
- IF 150 NT (PIN) - NT$ 146
- IF 350 NT (Elektrik) - NT$ 284
- IF Internet 30 Days 599 (Elektrik) - NT$ 570
- IF Internet 30 Days 599 (PIN) - NT$ 575
- 5pcs IF 150 NT @145 - NT$ 725
- 5pcs IF Internet 30 Days 599 @567 - NT$ 2,835

## 4. Additional Features

### 4.1 My Voucher Page
- List of purchased vouchers
- Voucher status (active, used, expired)
- Voucher codes (for PIN type)
- Usage instructions
- Expiration dates

### 4.2 Purchase Receipt Page
- Transaction ID
- Purchase date and time
- Product details
- Payment method
- Total amount
- Download/print options

### 4.3 Order History Page
- Chronological list of all transactions
- Filter options (date range, product type, status)
- Search functionality
- Quick reorder option

## 5. Technical Requirements

### 5.1 User Interface
- Responsive design (mobile-first)
- Green color scheme (#4CAF50 primary)
- Clean, card-based layout
- Intuitive navigation with breadcrumbs
- Loading states for all async operations

### 5.2 Payment Integration
- Multiple payment methods support
- Secure payment processing
- Real-time payment status updates
- Payment confirmation flow

### 5.3 Delivery Methods
- **Elektrik (Electronic):** Instant delivery to mobile number
- **PIN:** Generated code sent via email/SMS
- **Auto-delivery:** Automatic application to specified account

### 5.4 Localization
- Multi-language support
- Local currency display
- Regional payment methods
- Country-specific products and providers

## 6. User Experience Flow

### 6.1 Complete Purchase Flow
1. User clicks "Buy Voucher"
2. User selects country/category
3. User selects provider
4. User selects specific product
5. User enters recipient details (phone number, etc.)
6. User selects delivery method (Elektrik/PIN)
7. User proceeds to payment
8. User completes payment
9. System processes order
10. User receives confirmation and voucher/receipt

### 6.2 Navigation Patterns
- Consistent back navigation throughout
- Breadcrumb trail for deep navigation
- Quick access to main sections from any page
- Search functionality on product pages

## 7. Error Handling
- Clear error messages for failed transactions
- Retry mechanisms for network issues
- Alternative provider suggestions if one is unavailable
- Customer support contact integration
