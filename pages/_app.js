import '../styles/globals.css'
import Layout from '../layouts/layout'
import fetchCategories from '../utils/categoryProvider'
import { fetchStore } from '../utils/storeProvider'
import Head from 'next/head'
import { useEffect, Component } from 'react'
import InstallPrompt from '../components/InstallPrompt'
import '../utils/i18n'
import { appWithTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { Analytics } from '@vercel/analytics/react'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import { ensureCartLinkedToCustomer } from '../utils/customerAuth'
import { AuthProvider } from '../context/authContext'
import { AdminAuthProvider } from '../context/adminAuthContext'

// Global error boundary
class ErrorBoundary extends Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo })
    console.error("Application error:", error, errorInfo)
    
    // Log to server
    try {
      fetch('/api/log-error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: error.toString(),
          errorInfo: JSON.stringify(errorInfo),
          location: window.location.href
        })
      });
    } catch (e) {
      console.error("Failed to log error:", e);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-8 max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
          <p className="mb-4">We're sorry, there was an error loading this page.</p>
          <div className="bg-gray-100 p-4 rounded mb-4 overflow-auto">
            <p className="font-medium">Error:</p>
            <pre className="text-red-500 text-sm">{this.state.error && this.state.error.toString()}</pre>
            
            {this.state.errorInfo && (
              <>
                <p className="font-medium mt-4">Component Stack:</p>
                <pre className="text-sm overflow-auto">
                  {this.state.errorInfo.componentStack}
                </pre>
              </>
            )}
          </div>
          <button 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            Reload Page
          </button>
        </div>
      )
    }

    return this.props.children
  }
}

function Ecommerce({ Component, pageProps, categoriesarrayA, categories, allStores }) {
  const router = useRouter()

  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      // Register service worker
      navigator.serviceWorker.register('/sw.js').then(
        function (registration) {
          console.log('Service Worker registered with scope:', registration.scope);
        },
        function (error) {
          console.log('Service Worker registration failed:', error);
        }
      );
    }
  }, []);

  useEffect(() => {
    // Add global error handler for unhandled promise rejections and other errors
    const handleError = (event) => {
      console.error('Unhandled error:', event.error || event.reason);
      
      fetch('/api/log-error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: (event.error || event.reason || 'Unknown error').toString(),
          location: window.location.href,
          type: event.type
        })
      }).catch(e => console.error('Failed to log error:', e));
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleError);
    
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleError);
    };
  }, []);

  useEffect(() => {
    // Ensure cart is linked to customer if logged in
    ensureCartLinkedToCustomer();
  }, []);

  // Wrap component rendering with try-catch to prevent blank pages
  const renderComponent = () => {
    try {
      return <Component {...pageProps} />
    } catch (error) {
      console.error("Error rendering component:", error)
      return (
        <div className="p-4 text-center">
          <h2 className="text-lg font-semibold text-red-600">Component Error</h2>
          <p>There was an error loading this component. Please try again later.</p>
        </div>
      )
    }
  }

  // Check if current route is admin page
  const isAdminPage = router.pathname.startsWith('/admin');

  return (
    <AuthProvider>
      <AdminAuthProvider>
        <ErrorBoundary>
          <Head>
            <meta name='application-name' content='ShopMe' />
            <meta name='apple-mobile-web-app-capable' content='yes' />
            <meta name='apple-mobile-web-app-status-bar-style' content='default' />
            <meta name='apple-mobile-web-app-title' content='ShopMe' />
            <meta name='format-detection' content='telephone=no' />
            <meta name='mobile-web-app-capable' content='yes' />
            <meta name='theme-color' content='#000000' />
            {/* CSP is now managed through next.config.js headers */}
            <link rel='manifest' href='/manifest.json' />
            <link rel='apple-touch-icon' href='/icon-192x192.png' />
          </Head>
          <InstallPrompt />
          {isAdminPage ? (
            // Admin pages don't use the main Layout
            renderComponent()
          ) : (
            // Regular pages use the main Layout
            <Layout categoriesarrayA={categoriesarrayA} categories={categories} allStores={allStores}>
              {renderComponent()}
            </Layout>
          )}
          <Analytics />
          <ToastContainer />
        </ErrorBoundary>
      </AdminAuthProvider>
    </AuthProvider>
  )
}

Ecommerce.getInitialProps = async () => {
  try {
    const categoriesarrayA = await fetchCategories();
    const categoriesarray = categoriesarrayA.all;
    const categories = categoriesarray.map(category => category.slug);
    const allStores = await fetchStore();
    return {
      categoriesarrayA, categories, allStores
    };
  } catch (error) {
    console.error("Error in getInitialProps:", error)
    // Return minimal data to prevent app from crashing
    return {
      categoriesarrayA: { all: [] },
      categories: [],
      allStores: []
    }
  }
};

export default appWithTranslation(Ecommerce)
