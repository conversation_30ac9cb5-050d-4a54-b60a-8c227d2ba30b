import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function CustomerIndex() {
  const router = useRouter();
  
  useEffect(() => {
    // Check if user is logged in
    const customerData = typeof window !== 'undefined' ? localStorage.getItem('customerData') : null;
    
    if (customerData) {
      // If logged in, redirect to dashboard
      router.push('/customer/dashboard');
    } else {
      // If not logged in, redirect to login
      router.push('/customer/login');
    }
  }, [router]);

  return (
    <div className="flex justify-center items-center min-h-screen">
      <Head>
        <title>Cổng Khách Hàng</title>
      </Head>
      <div className="text-center">
        <h1 className="text-2xl font-semibold mb-4"><PERSON>ang chuyển hướng...</h1>
        <p><PERSON>ui lòng đợi trong khi chúng tôi chuyển bạn đến cổng kh<PERSON>ch hàng.</p>
      </div>
    </div>
  );
} 