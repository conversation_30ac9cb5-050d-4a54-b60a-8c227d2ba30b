import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import Head from 'next/head';

// Import all components
import Profile from '../../components/customer/Profile';
interface MenuItem {
  id: string;
  title: string;
  component: React.FC;
}

const CustomerDashboard: React.FC = () => {
  const router = useRouter();
  const [activeSection, setActiveSection] = useState('profile');
  const [customerName, setCustomerName] = useState('');

  useEffect(() => {
    // Check authentication
    const customerData = localStorage.getItem('customerData');
    if (!customerData) {
      router.push('/customer/login');
    } else {
      try {
        const customer = JSON.parse(customerData);
        setCustomerName(customer.name || 'Khách hàng');
      } catch (e) {
        console.error('Error parsing customer data:', e);
      }
    }
  }, [router]);

  const menuItems: MenuItem[] = [
    {
      id: 'profile',
      title: '<PERSON><PERSON> lịch',
      component: Profile,
    },
  ];

  const ActiveComponent = menuItems.find(item => item.id === activeSection)?.component || Profile;

  const handleLogout = async () => {
    // Clear local storage
    localStorage.removeItem('customerData');
    
    // Clear the authentication cookie (client-side)
    Cookies.remove('customerToken');
    
    // Call logout API to clear cookie server-side
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Error during logout:', error);
    }
    
    // Redirect to login page
    router.push('/customer/login');
  };

  return (
    <>
      <Head>
        <title>Cổng Khách Hàng</title>
      </Head>
      <div className="min-h-screen bg-white">
        {/* Header */}
        <header className="border-b border-gray-200">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <h1 className="text-lg font-light tracking-wider text-gray-800 uppercase">Cổng Khách Hàng</h1>
            <button 
              onClick={handleLogout}
              className="text-xs uppercase font-light tracking-wider text-gray-500 hover:text-gray-700"
            >
              Đăng Xuất
            </button>
          </div>
        </header>
        {/* <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <p className="text-sm text-gray-500 font-light">Xin chào, {customerName}</p>
            <div className="w-16 h-0.5 bg-gray-200 mt-2"></div>
          </div>

          <div className="flex flex-col md:flex-row gap-8">
            <nav className="md:w-48 space-y-1">
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`w-full text-left py-2 px-2 text-sm tracking-wide ${
                    activeSection === item.id
                      ? 'border-l-2 border-gray-400 font-medium'
                      : 'border-l-2 border-transparent text-gray-500 hover:border-gray-200'
                  }`}
                >
                  {item.title}
                </button>
              ))}
            </nav>

            <div className="flex-1 border border-gray-100 p-6">
              <ActiveComponent />
            </div>
          </div>
        </div> */}
      </div>
    </>
  );
};

export default CustomerDashboard; 