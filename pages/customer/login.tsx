import React, { useState, useContext } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import Head from 'next/head';
import { SiteContext, ContextProviderComponent } from '../../context/mainContext';

// Modified component to accept context as a prop
const LoginComponent: React.FC<{ context: any }> = ({ context }) => {
  const router = useRouter();
  // Now use context directly from props instead of useContext
  const { forceCustomerCartSync } = context;
  const [formData, setFormData] = useState({
    customerId: '',
    password: '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        // Store customer data in localStorage
        localStorage.setItem('customerData', JSON.stringify(data.customer));
        localStorage.setItem('user', JSON.stringify(data.customer));
        
        // Set authentication cookie
        Cookies.set('customerToken', data.customer.id, { expires: 7 }); // Expires in 7 days
        
        // Force customer cart sync to ensure cart is tied to logged-in customer
        if (forceCustomerCartSync) {
          forceCustomerCartSync(data.customer.id, data.customer);
        } else {
          console.warn('forceCustomerCartSync is not available, cart may not be properly linked');
        }
        
        router.push('/customer/dashboard');
      } else {
        setError(data.message || 'Thông tin đăng nhập không hợp lệ');
      }
    } catch (err) {
      setError('Đã xảy ra lỗi. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Đăng Nhập Khách Hàng</title>
      </Head>
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-10">
            <h1 className="text-2xl font-light tracking-wider text-gray-800 uppercase mb-3">Cổng Khách Hàng</h1>
            <div className="w-16 h-1 mx-auto bg-gray-200 mb-6"></div>
          </div>
          
          {error && (
            <div className="mb-6 p-2 border border-gray-300 text-center text-sm text-gray-600">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="customerId" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                Mã Khách Hàng
              </label>
              <input
                id="customerId"
                name="customerId"
                type="text"
                required
                value={formData.customerId}
                onChange={(e) => setFormData({ ...formData, customerId: e.target.value })}
                className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                placeholder="Nhập mã khách hàng của bạn"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="password" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                Mật Khẩu
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                placeholder="Nhập mật khẩu của bạn"
              />
            </div>
            
            <div className="pt-4">
              <button
                type="submit"
                disabled={loading}
                className={`w-full py-3 border border-gray-300 text-sm uppercase tracking-wider font-light hover:bg-gray-50 ${
                  loading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {loading ? 'Đang đăng nhập...' : 'Đăng Nhập'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

// Wrap the component with the context provider
const CustomerLogin: React.FC = () => (
  <ContextProviderComponent>
    <SiteContext.Consumer>
      {context => <LoginComponent context={context} />}
    </SiteContext.Consumer>
  </ContextProviderComponent>
);

export default CustomerLogin; 