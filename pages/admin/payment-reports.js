import React, { useState, useEffect } from 'react';
import { FaChartLine, FaDownload, Fa<PERSON>ilt<PERSON>, FaSpinner, FaCreditCard, FaStore, FaExclamationTriangle } from 'react-icons/fa';

const PaymentReports = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);
  const [callbacks, setCallbacks] = useState([]);
  const [filters, setFilters] = useState({
    paymentMethod: 'all',
    dateFrom: '',
    dateTo: '',
    status: ''
  });

  // Set default date range (last 30 days)
  useEffect(() => {
    const now = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(now.getDate() - 30);
    
    setFilters(prev => ({
      ...prev,
      dateFrom: thirtyDaysAgo.toISOString().split('T')[0],
      dateTo: now.toISOString().split('T')[0]
    }));
  }, []);

  const fetchPaymentData = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/payment-callbacks?limit=100&includeStats=true');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch payment data');
      }

      setStats(result.data.stats);
      setCallbacks(result.data.callbacks);
    } catch (err) {
      console.error('Error fetching payment data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentData();
  }, []);

  const handleFilterChange = (name, value) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const exportToCSV = () => {
    if (callbacks.length === 0) {
      alert('No data to export');
      return;
    }

    const headers = [
      'Timestamp',
      'Payment Method',
      'Order ID',
      'Transaction ID',
      'Amount',
      'Status',
      'Response Status',
      'Processing Time (ms)',
      'Client IP',
      'Order Found'
    ];

    const csvData = callbacks.map(callback => [
      callback.timestamp,
      callback.paymentMethod,
                  callback.orderId,
      callback.requestPayload?.trans_id || callback.requestPayload?.MerchantTradeNo || '',
      callback.orderAmount || 0,
      callback.requestPayload?.status || '',
      callback.responseStatus,
      callback.processingTimeMs || 0,
      callback.clientIP || '',
      callback.orderFound ? 'Yes' : 'No'
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `payment-callbacks-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="text-lg">Loading payment reports...</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Payment Callback Reports</h1>
        <p className="mt-2 text-sm text-gray-600">
          Comprehensive payment callback analytics from centralized orders system
        </p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <span>{error}</span>
          <button 
            onClick={fetchPaymentData}
            className="mt-2 text-sm text-red-700 hover:text-red-900 underline"
          >
            Retry
          </button>
        </div>
      )}

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-sm font-medium text-gray-500">Total Callbacks</p>
            <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-sm font-medium text-gray-500">Success Rate</p>
            <p className="text-2xl font-semibold text-gray-900">
              {stats.total > 0 ? Math.round((stats.byResponseStatus['200'] || 0) / stats.total * 100) : 0}%
            </p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-sm font-medium text-gray-500">Avg Processing</p>
            <p className="text-2xl font-semibold text-gray-900">{stats.avgProcessingTime}ms</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-sm font-medium text-gray-500">Recent (24h)</p>
            <p className="text-2xl font-semibold text-gray-900">{stats.recentCallbacks}</p>
          </div>
        </div>
      )}

      {/* Recent Callbacks Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Payment Callbacks</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 p-4">
          {callbacks.slice(0, 20).map((callback, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <FaCreditCard className="w-4 h-4 text-gray-400 mr-2" />
                  <span className="text-sm font-medium text-gray-900">
                    {callback.orderId}
                  </span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  callback.responseStatus === 200 ? 'bg-green-100 text-green-800' :
                  callback.responseStatus >= 400 ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {callback.responseStatus}
                </span>
              </div>
              
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">Payment Method</p>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  callback.paymentMethod === '7-eleven' ? 'bg-orange-100 text-orange-800' :
                  callback.paymentMethod === 'familymart' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {callback.paymentMethod === '7-eleven' ? '7-Eleven' :
                   callback.paymentMethod === 'familymart' ? 'FamilyMart' :
                   callback.paymentMethod}
                </span>
              </div>
              
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">Status</p>
                <p className="text-sm text-gray-700">{callback.requestPayload?.status || 'N/A'}</p>
              </div>
              
              <div>
                <p className="text-xs text-gray-500 mb-1">Timestamp</p>
                <p className="text-sm text-gray-700">{new Date(callback.timestamp).toLocaleString()}</p>
              </div>
            </div>
          ))}
        </div>
        
        {callbacks.length === 0 && (
          <div className="text-center py-12">
            <p className="text-sm text-gray-500">No payment callbacks found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentReports;