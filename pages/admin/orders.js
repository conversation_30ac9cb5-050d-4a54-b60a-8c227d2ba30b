import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FiShoppingCart, FiUser, FiCalendar, FiDollarSign, FiPackage, FiTruck, FiCheckCircle, FiXCircle, FiClock, FiArrowLeft, FiSearch, FiFilter, FiEye, FiEdit, FiDownload, FiLink, FiMessageSquare, FiSend, FiMail, FiPhone, FiClipboard, FiAlertCircle, FiBox, FiMapPin, FiX } from 'react-icons/fi';

const OrdersManagement = () => {
  const router = useRouter();
  const { store } = router.query;
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const [showInventoryLinking, setShowInventoryLinking] = useState(false);
  const [showCommunication, setShowCommunication] = useState(false);
  const [availableInventory, setAvailableInventory] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [communicationHistory, setCommunicationHistory] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [selectedCurrency, setSelectedCurrency] = useState('all');

  // Currency formatting function - NO FALLBACKS
  const formatCurrency = (amount, currency) => {
    if (!currency) {
      return `${amount} (KHÔNG RÕ LOẠI TIỀN)`;
    }

    switch (currency) {
      case 'VND':
        return `${amount.toLocaleString('vi-VN')}đ`;
      case 'NT$':
      case 'NT':
      case 'NTD':
      case 'TWD':
        return `NT$ ${amount.toLocaleString()}`;
      case 'USD':
      case '$':
        return `$${amount.toFixed(2)}`;
      default:
        return `${currency} ${amount}`;
    }
  };

  // Group orders by currency
  const groupOrdersByCurrency = (orders) => {
    const grouped = {};
    orders.forEach(order => {
      const currency = order.currency || 'UNKNOWN';
      if (!grouped[currency]) {
        grouped[currency] = [];
      }
      grouped[currency].push(order);
    });
    return grouped;
  };

  // Get available currencies from orders
  const getAvailableCurrencies = (orders) => {
    const currencies = new Set();
    orders.forEach(order => {
      if (order.currency) {
        currencies.add(order.currency);
      } else {
        currencies.add('UNKNOWN');
      }
    });
    return Array.from(currencies).sort();
  };

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        // Use the admin orders endpoint to get consistent data with order detail page
        const response = await fetch('/api/admin/orders');

        if (!response.ok) {
          throw new Error('Failed to fetch orders');
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || 'Failed to fetch orders');
        }

        // Handle duplicate order IDs by prioritizing complete orders
        const orderMap = new Map();

        (data.orders || []).forEach(order => {
          const orderId = order.id || order.orderId;
          if (!orderId) return;

          const transformedOrder = {
            id: orderId,
            customerName: order.customerName,
            customerPhone: order.customerPhone,
            customerEmail: order.customerEmail,
            products: order.items || order.products || [],
            totalAmount: order.totalAmount || order.amount || 0,
            currency: order.currency, // Keep original currency, no fallback
            status: order.status || 'pending',
            paymentStatus: order.paymentStatus || 'not_paid',
            shippingAddress: order.shippingAddress || '',
            createdAt: order.createdAt,
            updatedAt: order.updatedAt,
            note: order.notes || order.note || ''
          };

          // Check if this order has complete data
          const hasCompleteData = transformedOrder.products.length > 0 &&
                                 transformedOrder.currency &&
                                 transformedOrder.totalAmount > 0;

          // If we don't have this order yet, or this one has more complete data, use it
          const existingOrder = orderMap.get(orderId);
          if (!existingOrder) {
            orderMap.set(orderId, transformedOrder);
          } else {
            const existingHasCompleteData = existingOrder.products.length > 0 &&
                                          existingOrder.currency &&
                                          existingOrder.totalAmount > 0;

            // Prioritize the order with complete data
            if (hasCompleteData && !existingHasCompleteData) {
              orderMap.set(orderId, transformedOrder);
            }
          }
        });

        const transformedOrders = Array.from(orderMap.values());

        console.log('Fetched orders with currencies:', transformedOrders.map(o => ({
          id: o.id,
          currency: o.currency,
          products: o.products.length,
          totalAmount: o.totalAmount
        })));
        setOrders(transformedOrders);
      } catch (error) {
        console.error('Error fetching orders:', error);
        // Fallback to empty array on error
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerPhone.includes(searchTerm);
    const matchesFilter = filterStatus === 'all' || order.status === filterStatus;
    const matchesCurrency = selectedCurrency === 'all' || order.currency === selectedCurrency;
    return matchesSearch && matchesFilter && matchesCurrency;
  });

  // Group filtered orders by currency
  const groupedOrders = groupOrdersByCurrency(filteredOrders);
  const availableCurrencies = getAvailableCurrencies(orders);

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed': return 'Hoàn thành';
      case 'processing': return 'Đang xử lý';
      case 'pending': return 'Chờ xử lý';
      case 'cancelled': return 'Đã hủy';
      default: return 'Không xác định';
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'refunded': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPaymentStatusText = (status) => {
    switch (status) {
      case 'paid': return 'Đã thanh toán';
      case 'pending': return 'Chờ thanh toán';
      case 'refunded': return 'Đã hoàn tiền';
      default: return 'Không xác định';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <FiCheckCircle className="w-4 h-4" />;
      case 'processing': return <FiTruck className="w-4 h-4" />;
      case 'pending': return <FiClock className="w-4 h-4" />;
      case 'cancelled': return <FiXCircle className="w-4 h-4" />;
      default: return null;
    }
  };

  const handleViewOrder = (order) => {
    // Navigate to dedicated order detail page
    router.push(`/admin/orders/${order.id}`);
  };

  const handleUpdateOrderStatus = (orderId, newStatus) => {
    setOrders(orders.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus, updatedAt: new Date().toLocaleString('vi-VN') }
        : order
    ));
  };

  const handleInventoryLinking = (product) => {
    setSelectedProduct(product);
    // Fetch available inventory for this product
    fetchAvailableInventory(product.sku);
    setShowInventoryLinking(true);
  };

  const fetchAvailableInventory = async (sku) => {
    try {
      // Mock data - in real implementation, fetch from inventory API
      const mockInventory = [
        { id: 'INV-001', serialNumber: 'SN123456789', status: 'available', condition: 'new' },
        { id: 'INV-002', serialNumber: 'SN987654321', status: 'available', condition: 'new' },
        { id: 'INV-003', serialNumber: 'SN456789123', status: 'available', condition: 'refurbished' }
      ];
      setAvailableInventory(mockInventory);
    } catch (error) {
      console.error('Error fetching inventory:', error);
    }
  };

  const handleLinkInventory = (inventoryId) => {
    // Update order with linked inventory
    setOrders(orders.map(order => {
      if (order.id === selectedOrder.id) {
        const updatedProducts = order.products.map(product => {
          if (product.sku === selectedProduct.sku) {
            return {
              ...product,
              linkedInventory: [inventoryId],
              fulfillmentStatus: 'linked'
            };
          }
          return product;
        });
        return { ...order, products: updatedProducts };
      }
      return order;
    }));
    setShowInventoryLinking(false);
  };

  const handleCommunication = (order) => {
    setSelectedOrder(order);
    // Fetch communication history
    fetchCommunicationHistory(order.id);
    setShowCommunication(true);
  };

  const fetchCommunicationHistory = async (orderId) => {
    // Mock communication history
    const mockHistory = [
      {
        id: 1,
        type: 'email',
        message: 'Đơn hàng của bạn đã được xác nhận',
        timestamp: '2024-01-15 10:35:00',
        direction: 'outbound',
        status: 'sent'
      },
      {
        id: 2,
        type: 'sms',
        message: 'Cảm ơn bạn đã đặt hàng',
        timestamp: '2024-01-15 10:30:00',
        direction: 'outbound',
        status: 'delivered'
      }
    ];
    setCommunicationHistory(mockHistory);
  };

  const handleSendMessage = async (type) => {
    if (!newMessage.trim()) return;
    
    const newCommunication = {
      id: Date.now(),
      type,
      message: newMessage,
      timestamp: new Date().toLocaleString('vi-VN'),
      direction: 'outbound',
      status: 'sending'
    };
    
    setCommunicationHistory([...communicationHistory, newCommunication]);
    setNewMessage('');
    
    // Simulate API call
    setTimeout(() => {
      setCommunicationHistory(prev => 
        prev.map(comm => 
          comm.id === newCommunication.id 
            ? { ...comm, status: 'sent' }
            : comm
        )
      );
    }, 1000);
  };

  const getFulfillmentStatusColor = (status) => {
    switch (status) {
      case 'linked': return 'text-green-600 bg-green-100';
      case 'ready': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getFulfillmentStatusText = (status) => {
    switch (status) {
      case 'linked': return 'Đã liên kết';
      case 'ready': return 'Sẵn sàng';
      case 'pending': return 'Chờ xử lý';
      case 'cancelled': return 'Đã hủy';
      default: return 'Không xác định';
    }
  };

  // Calculate revenue by currency
  const calculateRevenueByCurrency = (orders) => {
    const revenueByCurrency = {};
    orders.filter(order => order.status === 'completed').forEach(order => {
      const currency = order.currency || 'UNKNOWN';
      if (!revenueByCurrency[currency]) {
        revenueByCurrency[currency] = 0;
      }
      revenueByCurrency[currency] += order.totalAmount;
    });
    return revenueByCurrency;
  };

  const orderStats = {
    total: orders.length,
    completed: orders.filter(order => order.status === 'completed').length,
    processing: orders.filter(order => order.status === 'processing').length,
    pending: orders.filter(order => order.status === 'pending').length,
    cancelled: orders.filter(order => order.status === 'cancelled').length,
    revenueByCurrency: calculateRevenueByCurrency(orders)
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin`)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5 mr-2" />
            Quay lại trang chủ Admin
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FiShoppingCart className="w-8 h-8 mr-3 text-blue-600" />
                Quản lý đơn hàng
              </h1>
              <p className="text-gray-600 mt-2">Theo dõi và quản lý tất cả đơn hàng</p>
            </div>
            
            <div className="text-right">
              <div className="text-sm text-gray-500">Tổng đơn hàng</div>
              <div className="text-2xl font-bold text-blue-600">{orderStats.total}</div>
            </div>
          </div>
        </div>

        {/* Thống kê nhanh */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Hoàn thành</p>
                <p className="text-2xl font-bold text-green-600">{orderStats.completed}</p>
              </div>
              <FiCheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Đang xử lý</p>
                <p className="text-2xl font-bold text-blue-600">{orderStats.processing}</p>
              </div>
              <FiTruck className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Chờ xử lý</p>
                <p className="text-2xl font-bold text-yellow-600">{orderStats.pending}</p>
              </div>
              <FiClock className="w-8 h-8 text-yellow-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Đã hủy</p>
                <p className="text-2xl font-bold text-red-600">{orderStats.cancelled}</p>
              </div>
              <FiXCircle className="w-8 h-8 text-red-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Doanh thu</p>
                <div className="text-sm font-bold text-green-600">
                  {Object.entries(orderStats.revenueByCurrency).length === 0 ? (
                    <span className="text-gray-400">Chưa có doanh thu</span>
                  ) : (
                    Object.entries(orderStats.revenueByCurrency).map(([currency, amount]) => (
                      <div key={currency} className="mb-1">
                        {formatCurrency(amount, currency)}
                      </div>
                    ))
                  )}
                </div>
              </div>
              <FiDollarSign className="w-8 h-8 text-green-600" />
            </div>
          </div>
        </div>

        {/* Danh sách đơn hàng */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h2 className="text-xl font-semibold text-gray-900">Danh sách đơn hàng</h2>
              
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Tìm kiếm đơn hàng..."
                  />
                </div>
                
                <div className="relative">
                  <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                  >
                    <option value="all">Tất cả trạng thái</option>
                    <option value="pending">Chờ xử lý</option>
                    <option value="processing">Đang xử lý</option>
                    <option value="completed">Hoàn thành</option>
                    <option value="cancelled">Đã hủy</option>
                  </select>
                </div>

                <div className="relative">
                  <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <select
                    value={selectedCurrency}
                    onChange={(e) => setSelectedCurrency(e.target.value)}
                    className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                  >
                    <option value="all">Tất cả tiền tệ</option>
                    {availableCurrencies.map(currency => (
                      <option key={currency} value={currency}>
                        {currency === 'UNKNOWN' ? 'Không xác định' : currency}
                      </option>
                    ))}
                  </select>
                </div>
                
                <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                  <FiDownload className="w-4 h-4 mr-2" />
                  Xuất Excel
                </button>
              </div>
            </div>
          </div>
          
          <div>
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Đang tải dữ liệu...</p>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <FiShoppingCart className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Không có đơn hàng nào</p>
              </div>
            ) : selectedCurrency === 'all' ? (
              // Display orders grouped by currency
              <div className="p-4">
                {Object.entries(groupedOrders).map(([currency, currencyOrders]) => (
                  <div key={currency} className="mb-8">
                    <div className="flex items-center justify-between mb-4 pb-2 border-b border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                        <FiDollarSign className="w-5 h-5 mr-2 text-blue-600" />
                        {currency === 'UNKNOWN' ? 'Không xác định tiền tệ' : `Tiền tệ: ${currency}`}
                      </h3>
                      <span className="text-sm text-gray-500">
                        {currencyOrders.length} đơn hàng
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {currencyOrders.map((order) => (
                        <div key={order.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                          {/* Header with order ID and actions */}
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center">
                              <FiPackage className="w-5 h-5 text-blue-600 mr-2" />
                              <span className="font-semibold text-blue-600">#{order.id}</span>
                            </div>
                            <div className="flex space-x-1">
                              <button
                                onClick={() => handleViewOrder(order)}
                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                title="Xem chi tiết"
                              >
                                <FiEye className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => setSelectedProduct(order)}
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Liên kết kho"
                              >
                                <FiLink className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => {
                                  setSelectedProduct(order);
                                  setShowCommunication(true);
                                }}
                                className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
                                title="Liên lạc"
                              >
                                <FiMessageSquare className="w-4 h-4" />
                              </button>
                            </div>
                          </div>

                          {/* Customer info */}
                          <div className="mb-3">
                            <div className="flex items-center mb-1">
                              <FiUser className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
                              <span className="text-sm font-medium text-gray-900 truncate">{order.customerName}</span>
                            </div>
                            <div className="text-sm text-gray-500 ml-6">{order.customerPhone}</div>
                          </div>

                          {/* Products info */}
                          <div className="mb-3">
                            <div className="text-sm text-gray-900 font-medium">
                              {order.products.length} sản phẩm
                            </div>
                            <div className="text-sm text-gray-500 truncate">
                              {order.products[0]?.name}{order.products.length > 1 && ` +${order.products.length - 1} khác`}
                            </div>
                          </div>

                          {/* Status badges */}
                          <div className="flex flex-wrap gap-2 mb-3">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                              {getStatusIcon(order.status)}
                              <span className="ml-1">{getStatusText(order.status)}</span>
                            </span>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(order.paymentStatus)}`}>
                              {getPaymentStatusText(order.paymentStatus)}
                            </span>
                          </div>

                          {/* Amount and date */}
                          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                            <div className="text-center">
                              <div className="text-lg font-semibold text-green-600">
                                {formatCurrency(order.totalAmount, order.currency)}
                              </div>
                              <div className="text-xs text-gray-500">Tổng tiền</div>
                              {!order.currency && (
                                <div className="text-xs text-red-500">⚠️ Thiếu thông tin tiền tệ</div>
                              )}
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-gray-900">{order.createdAt}</div>
                              <div className="text-xs text-gray-500">Ngày tạo</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Display filtered orders for specific currency
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                {filteredOrders.map((order) => (
                  <div key={order.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    {/* Header with order ID and actions */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center">
                        <FiPackage className="w-5 h-5 text-blue-600 mr-2" />
                        <span className="text-sm font-medium text-blue-600">
                          {order.id}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleViewOrder(order)}
                          className="text-blue-600 hover:text-blue-900 transition-colors p-1"
                          title="Xem chi tiết"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleCommunication(order)}
                          className="text-green-600 hover:text-green-900 transition-colors p-1"
                          title="Liên hệ khách hàng"
                        >
                          <FiMessageSquare className="w-4 h-4" />
                        </button>
                        {order.products.some(p => p.inventoryType === 'separate' && !p.linkedInventory) && (
                          <button 
                            className="text-orange-600 hover:text-orange-900 transition-colors p-1"
                            title="Cần liên kết kho"
                          >
                            <FiAlertCircle className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Customer info */}
                    <div className="mb-3">
                      <div className="flex items-center mb-1">
                        <FiUser className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
                        <span className="text-sm font-medium text-gray-900 truncate">{order.customerName}</span>
                      </div>
                      <div className="text-sm text-gray-500 ml-6">{order.customerPhone}</div>
                    </div>

                    {/* Products info */}
                    <div className="mb-3">
                      <div className="text-sm text-gray-900 font-medium">
                        {order.products.length} sản phẩm
                      </div>
                      <div className="text-sm text-gray-500 truncate">
                        {order.products[0]?.name}{order.products.length > 1 && ` +${order.products.length - 1} khác`}
                      </div>
                    </div>

                    {/* Status badges */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1">{getStatusText(order.status)}</span>
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(order.paymentStatus)}`}>
                        {getPaymentStatusText(order.paymentStatus)}
                      </span>
                    </div>

                    {/* Amount and date */}
                    <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">
                          {formatCurrency(order.totalAmount, order.currency)}
                        </div>
                        <div className="text-xs text-gray-500">Tổng tiền</div>
                        {!order.currency && (
                          <div className="text-xs text-red-500">⚠️ Thiếu thông tin tiền tệ</div>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-900">{order.createdAt}</div>
                        <div className="text-xs text-gray-500">Ngày tạo</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Inventory Linking Modal */}
        {showInventoryLinking && selectedProduct && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900">Liên kết kho cho {selectedProduct.name}</h3>
                  <button
                     onClick={() => setShowInventoryLinking(false)}
                     className="text-gray-400 hover:text-gray-600 transition-colors"
                   >
                     <FiX className="w-6 h-6" />
                   </button>
                </div>
                
                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">SKU: {selectedProduct.sku}</p>
                  <p className="text-sm text-gray-600 mb-4">Cần liên kết: {selectedProduct.quantity} sản phẩm</p>
                </div>
                
                <div className="space-y-3 mb-6">
                  <h4 className="text-lg font-medium text-gray-900">Kho có sẵn:</h4>
                  {availableInventory.length > 0 ? (
                    availableInventory.map((item) => (
                      <div key={item.id} className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">ID: {item.id}</p>
                            <p className="text-sm text-gray-600">Serial: {item.serialNumber}</p>
                            <p className="text-sm text-gray-600">Trạng thái: {item.status}</p>
                            <p className="text-xs text-gray-500">Tình trạng: {item.condition}</p>
                          </div>
                          <button
                            onClick={() => handleLinkInventory(item.id)}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                          >
                            Chọn
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">Không có kho khả dụng</p>
                  )}
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setShowInventoryLinking(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Communication Modal */}
        {showCommunication && selectedOrder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[80vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900">Liên hệ khách hàng - {selectedOrder.customerName}</h3>
                  <button
                     onClick={() => setShowCommunication(false)}
                     className="text-gray-400 hover:text-gray-600 transition-colors"
                   >
                     <FiX className="w-6 h-6" />
                   </button>
                </div>
                
                {/* Customer contact info */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center">
                      <FiPhone className="w-4 h-4 text-blue-600 mr-2" />
                      <span className="text-sm text-gray-900">{selectedOrder.customerPhone}</span>
                    </div>
                    <div className="flex items-center">
                      <FiMail className="w-4 h-4 text-blue-600 mr-2" />
                      <span className="text-sm text-gray-900">{selectedOrder.customerEmail}</span>
                    </div>
                    <div className="flex items-center">
                      <FiMapPin className="w-4 h-4 text-blue-600 mr-2" />
                      <span className="text-sm text-gray-900 truncate">{selectedOrder.shippingAddress}</span>
                    </div>
                  </div>
                </div>
                
                {/* Communication history */}
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Lịch sử liên hệ</h4>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {communicationHistory.length > 0 ? (
                      communicationHistory.map((comm) => (
                        <div key={comm.id} className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              {comm.type === 'email' ? <FiMail className="w-4 h-4 text-blue-600 mr-2" /> : <FiMessageSquare className="w-4 h-4 text-green-600 mr-2" />}
                              <span className="text-sm font-medium text-gray-900">
                                {comm.type === 'email' ? 'Email' : 'SMS'}
                              </span>
                            </div>
                            <span className="text-xs text-gray-500">{comm.timestamp}</span>
                          </div>
                          <p className="text-sm text-gray-700">{comm.message}</p>
                          <div className="mt-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              comm.status === 'sent' ? 'bg-green-100 text-green-800' :
                              comm.status === 'delivered' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {comm.status === 'sent' ? 'Đã gửi' : comm.status === 'delivered' ? 'Đã nhận' : 'Đang gửi'}
                            </span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-center py-4">Chưa có lịch sử liên hệ</p>
                    )}
                  </div>
                </div>
                
                {/* Send new message */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Gửi tin nhắn mới</h4>
                  <div className="space-y-3">
                    <textarea
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Nhập tin nhắn..."
                    />
                    <div className="flex justify-between">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleSendMessage('email')}
                          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                          disabled={!newMessage.trim()}
                        >
                          <FiMail className="w-4 h-4 mr-2" />
                          Gửi Email
                        </button>
                        <button
                          onClick={() => handleSendMessage('sms')}
                          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                          disabled={!newMessage.trim()}
                        >
                          <FiMessageSquare className="w-4 h-4 mr-2" />
                          Gửi SMS
                        </button>
                      </div>
                      <button
                        onClick={() => setShowCommunication(false)}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                      >
                        Đóng
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}


      </div>
    </div>
  );
};

export default OrdersManagement;