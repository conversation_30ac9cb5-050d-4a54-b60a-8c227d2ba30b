import React, { useState, useEffect } from 'react';

const AuthStatus = () => {
  const [status, setStatus] = useState({
    localStorage: null,
    cookies: null,
    apiVerification: null
  });

  useEffect(() => {
    const checkStatus = async () => {
      // Check localStorage
      const localToken = localStorage.getItem('adminToken');
      const localUser = localStorage.getItem('adminUser');

      // Check cookies (client-side)
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {});

      // Check API verification
      let apiResult = null;
      if (localToken) {
        try {
          const response = await fetch('/api/admin/verify', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localToken}`,
              'Content-Type': 'application/json',
            },
          });
          apiResult = {
            status: response.status,
            ok: response.ok,
            data: response.ok ? await response.json() : await response.text()
          };
        } catch (err) {
          apiResult = { error: err.message };
        }
      }

      setStatus({
        localStorage: {
          token: localToken ? 'Present' : 'Missing',
          tokenLength: localToken ? localToken.length : 0,
          user: localUser ? JSON.parse(localUser) : null
        },
        cookies: cookies,
        apiVerification: apiResult
      });
    };

    checkStatus();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Trạng Thái Xác Thực Quản Trị</h1>
        
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Bộ Nhớ Cục Bộ</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(status.localStorage, null, 2)}
            </pre>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Cookies</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(status.cookies, null, 2)}
            </pre>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Xác Thực API</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(status.apiVerification, null, 2)}
            </pre>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Hành Động</h2>
            <div className="space-x-4">
              <button
                onClick={() => {
                  localStorage.removeItem('adminToken');
                  localStorage.removeItem('adminUser');
                  window.location.reload();
                }}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Xóa Bộ Nhớ Cục Bộ
              </button>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Làm Mới
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthStatus;
