import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { 
  FaArrowLeft, 
  FaCheckCircle, 
  FaExclamationTriangle, 
  FaTimesCircle,
  FaEye,
  FaEyeSlash,
  FaSync,
  FaCopy,
  FaExternalLinkAlt
} from 'react-icons/fa'
import config711 from '../../components/taiwan/payment/methods/711/config.json'
import configFamilyMart from '../../components/taiwan/payment/methods/FamilyMart/config.json'

const ProviderManagement = () => {
  const router = useRouter()
  const { store } = router.query
  const [showPasswords, setShowPasswords] = useState({})
  const [connectionStatus, setConnectionStatus] = useState({})
  const [pendingOrders, setPendingOrders] = useState({})
  const [statistics, setStatistics] = useState({})
  const [apnCalls, setApnCalls] = useState([])
  const [apnCallbacks, setApnCallbacks] = useState([])
  const [showApnDetails, setShowApnDetails] = useState(false)
  const [apnStats, setApnStats] = useState({})
  const [selectedProvider, setSelectedProvider] = useState(null)

  // Provider configurations read directly from source config files
  const providers = [
    {
      id: '7eleven_ibon',
      name: '7-Eleven iBon',
      type: 'Convenience Store Payment',
      status: 'active',
      config: {
        merchantID: config711.tw.tw711.ibon.merchantID,
        password: config711.tw.tw711.ibon.password,
        apiPassword: config711.tw.tw711.ibon.apiPassword,
        testUrl: config711.tw.tw711.ibon.testUrl,
        returnURL: config711.tw.tw711.ibon.returnURL,
        clientBackURL: config711.tw.tw711.ibon.clientBackURL,
        paymentInfoURL: config711.tw.tw711.ibon.paymentInfoURL,
        clientRedirectURL: config711.tw.tw711.ibon.clientRedirectURL,
        notifyURL: config711.tw.tw711.ibon.notifyURL,
        customerURL: config711.tw.tw711.ibon.customerURL,
        apnURL: config711.tw.tw711.ibon.apnURL,
        language: config711.tw.tw711.ibon.language,
        version: config711.tw.tw711.ibon.version,
        respondType: config711.tw.tw711.ibon.respondType
      },
      limits: {
        minAmount: 30,
        maxAmount: 30000,
        expiryHours: 24
      },
      fee: {
        amount: 25,
        currency: 'NT$'
      }
    },
    {
      id: '7eleven_card',
      name: '7-Eleven Credit Card',
      type: 'Credit Card Payment',
      status: 'active',
      config: {
        merchantID: config711.tw.tw711.card.merchantID,
        merchantPassword: config711.tw.tw711.card.merchantPassword,
        linkID: config711.tw.tw711.card.linkID,
        hashBase: config711.tw.tw711.card.hashBase,
        testUrl: config711.tw.tw711.card.testUrl,
        liveUrl: config711.tw.tw711.card.liveUrl,
        returnURL: config711.tw.tw711.card.returnURL,
        notifyURL: config711.tw.tw711.card.notifyURL,
        version: config711.tw.tw711.card.apiSpecifications.version
      },
      limits: {
        minAmount: config711.tw.tw711.card.limits.minAmount,
        maxAmount: config711.tw.tw711.card.limits.maxAmount,
        expiryHours: config711.tw.tw711.card.limits.expiryHours
      },
      fee: {
        amount: config711.tw.tw711.card.fee.amount,
        currency: config711.tw.tw711.card.fee.currency
      }
    },
    {
      id: 'familymart',
      name: 'FamilyMart FamiPort',
      type: 'Convenience Store Payment',
      status: 'test',
      config: {
        merchant_id: configFamilyMart.api.credentials.merchant_id,
        api_key: configFamilyMart.api.credentials.api_key,
        api_secret: configFamilyMart.api.credentials.api_secret,
        api_endpoint: configFamilyMart.api.credentials.api_endpoint,
        test_mode: configFamilyMart.api.credentials.test_mode,
        version: configFamilyMart.api_specifications.version
      },
      limits: {
        minAmount: configFamilyMart.payment.limits.min_amount,
        maxAmount: configFamilyMart.payment.limits.max_amount,
        expiryHours: configFamilyMart.payment.limits.expiry_hours
      },
      fee: {
        amount: configFamilyMart.payment.fee.amount,
        currency: configFamilyMart.payment.fee.currency
      }
    },
    {
      id: 'sinopac_bank',
      name: 'Sinopac Bank',
      type: 'Bank Transfer',
      status: 'inactive',
      config: {
        bankCode: 'NOT AVAILABLE',
        bankName: 'Sinopac Bank',
        accountNumber: 'NOT AVAILABLE',
        apiEndpoint: 'NOT AVAILABLE',
        version: 'NOT AVAILABLE'
      },
      limits: {
        minAmount: 'NOT AVAILABLE',
        maxAmount: 'NOT AVAILABLE',
        expiryHours: 'NOT AVAILABLE'
      },
      fee: {
        amount: 'NOT AVAILABLE',
        currency: 'NOT AVAILABLE'
      }
    },
    {
      id: 'if_topup',
      name: 'IF Mobile Topup',
      type: 'Mobile Topup',
      status: 'inactive',
      config: {
        apiEndpoint: 'NOT AVAILABLE',
        version: 'NOT AVAILABLE'
      },
      limits: {
        minAmount: 'NOT AVAILABLE',
        maxAmount: 'NOT AVAILABLE',
        expiryHours: 'NOT AVAILABLE'
      },
      fee: {
        amount: 'NOT AVAILABLE',
        currency: 'NOT AVAILABLE'
      }
    },
    {
      id: 'ok_topup',
      name: 'OK Mobile Topup',
      type: 'Mobile Topup',
      status: 'inactive',
      config: {
        apiEndpoint: 'NOT AVAILABLE',
        version: 'NOT AVAILABLE'
      },
      limits: {
        minAmount: 'NOT AVAILABLE',
        maxAmount: 'NOT AVAILABLE',
        expiryHours: 'NOT AVAILABLE'
      },
      fee: {
        amount: 'NOT AVAILABLE',
        currency: 'NOT AVAILABLE'
      }
    }
  ]

  useEffect(() => {

    // Load real provider data from API
    const loadProviderData = async () => {
      try {
        const response = await fetch('/api/admin/provider-stats')
        const data = await response.json()
        
        if (data.success) {
          const statusData = {}
          const ordersData = {}
          const statsData = {}

          Object.entries(data.providers).forEach(([providerId, providerData]) => {
            statusData[providerId] = providerData.connection.status
            ordersData[providerId] = providerData.statistics.pendingOrders
            statsData[providerId] = {
              totalTransactions: providerData.statistics.totalTransactions,
              successRate: providerData.statistics.successRate,
              avgResponseTime: providerData.statistics.avgResponseTime,
              lastTransaction: providerData.statistics.lastTransaction?.date || null,
              totalRevenue: providerData.statistics.totalRevenue
            }
          })

          setConnectionStatus(statusData)
          setPendingOrders(ordersData)
          setStatistics(statsData)
        }
      } catch (error) {
        console.error('Error loading provider data:', error)
        // Fallback to simulated data if API fails
        const statusData = {}
        const ordersData = {}
        const statsData = {}

        providers.forEach(provider => {
          statusData[provider.id] = 'error'
          ordersData[provider.id] = 0
          statsData[provider.id] = {
            totalTransactions: 0,
            successRate: '0.0',
            avgResponseTime: 0,
            lastTransaction: null,
            totalRevenue: '0.00'
          }
        })

        setConnectionStatus(statusData)
        setPendingOrders(ordersData)
        setStatistics(statsData)
      }
    }

    loadProviderData()
    loadApnData()
    
    // Refresh data every 30 seconds
    const interval = setInterval(() => {
      loadProviderData()
      loadApnData()
    }, 30000)
    return () => clearInterval(interval)
  }, [])

  // Load APN calls and callbacks data
  const loadApnData = async () => {
    try {
      // Load outgoing APN calls
      const callsResponse = await fetch('/api/admin/apn-calls?limit=50')
      const callsData = await callsResponse.json()
      
      if (callsData.success) {
        setApnCalls(callsData.calls)
        setApnStats(prev => ({ ...prev, calls: callsData.statistics }))
      }
      
      // Load incoming APN callbacks
      const callbacksResponse = await fetch('/api/admin/apn-callbacks')
      const callbacksData = await callbacksResponse.json()
      
      if (callbacksData.success) {
        setApnCallbacks(callbacksData.callbacks.slice(0, 50))
        setApnStats(prev => ({ ...prev, callbacks: callbacksData.summary }))
      }
    } catch (error) {
      console.error('Error loading APN data:', error)
    }
  }

  const getStatusIcon = (status, connectionStatus) => {
    if (connectionStatus === 'error') {
      return <FaTimesCircle className="text-red-500" />
    }
    
    switch (status) {
      case 'active':
        return <FaCheckCircle className="text-green-500" />
      case 'test':
        return <FaExclamationTriangle className="text-yellow-500" />
      default:
        return <FaTimesCircle className="text-red-500" />
    }
  }

  const getStatusText = (status, connectionStatus) => {
    if (connectionStatus === 'error') {
      return 'Connection Error'
    }
    
    switch (status) {
      case 'active':
        return 'Active'
      case 'test':
        return 'Test Mode'
      default:
        return 'Inactive'
    }
  }

  const togglePasswordVisibility = (providerId, field) => {
    setShowPasswords(prev => ({
      ...prev,
      [`${providerId}_${field}`]: !prev[`${providerId}_${field}`]
    }))
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  const testConnection = async (providerId) => {
    setConnectionStatus(prev => ({ ...prev, [providerId]: 'testing' }))
    
    try {
      const response = await fetch('/api/admin/provider-stats')
      const data = await response.json()
      
      if (data.success && data.providers[providerId]) {
        setConnectionStatus(prev => ({ 
          ...prev, 
          [providerId]: data.providers[providerId].connection.status 
        }))
        
        // Update statistics as well
        setStatistics(prev => ({
          ...prev,
          [providerId]: {
            ...prev[providerId],
            avgResponseTime: data.providers[providerId].statistics.avgResponseTime
          }
        }))
      }
    } catch (error) {
      console.error('Error testing connection:', error)
      setConnectionStatus(prev => ({ ...prev, [providerId]: 'error' }))
    }
  }

  const maskSensitiveData = (value, show) => {
    if (!value) return 'N/A'
    if (show) return value
    return value.replace(/./g, '*')
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => router.push(`/admin`)}
              className="mr-4 p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <FaArrowLeft className="text-xl" />
            </button>
            <h1 className="text-3xl font-bold text-gray-800">Ngân hàng, OK, IF</h1>
            <div className="ml-auto">
              <button
                onClick={() => setShowApnDetails(!showApnDetails)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  showApnDetails 
                    ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                    : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                }`}
              >
                {showApnDetails ? 'Ẩn APN Details' : 'Xem APN Details'}
              </button>
            </div>
          </div>
          <p className="text-gray-600">Quản lý kết nối với các nhà cung cấp dịch vụ thanh toán và nạp tiền</p>
        </div>

        {/* APN Management Section */}
        {showApnDetails && (
          <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">APN Call & Callback Management</h2>
            
            {/* APN Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-2xl font-bold text-blue-600">{apnStats.calls?.total || 0}</div>
                <div className="text-sm text-gray-600">Tổng cuộc gọi</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="text-2xl font-bold text-green-600">{apnStats.calls?.successful || 0}</div>
                <div className="text-sm text-gray-600">Thành công</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-red-200">
                <div className="text-2xl font-bold text-red-600">{apnStats.calls?.failed || 0}</div>
                <div className="text-sm text-gray-600">Thất bại</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-yellow-200">
                <div className="text-2xl font-bold text-yellow-600">{apnStats.calls?.avgProcessingTime || 0}ms</div>
                <div className="text-sm text-gray-600">Thời gian xử lý TB</div>
              </div>
            </div>

            {/* Recent APN Calls */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">Cuộc gọi gần đây (Outgoing)</h3>
                <div className="bg-white rounded-lg border max-h-64 overflow-y-auto">
                  {apnCalls.length > 0 ? (
                    apnCalls.slice(0, 10).map((call, index) => (
                      <div key={call.id} className={`p-3 border-b last:border-b-0 ${
                        call.success ? 'bg-green-50' : 'bg-red-50'
                      }`}>
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium text-sm">{call.provider}</div>
                            <div className="text-xs text-gray-600">Order: {call.orderId}</div>
                            <div className="text-xs text-gray-500">
                              {new Date(call.timestamp).toLocaleString('vi-VN')}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-xs px-2 py-1 rounded ${
                              call.success 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {call.success ? 'Thành công' : 'Thất bại'}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {call.processingTimeMs}ms
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500">Chưa có cuộc gọi nào</div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">Callback gần đây (Incoming)</h3>
                <div className="bg-white rounded-lg border max-h-64 overflow-y-auto">
                  {apnCallbacks.length > 0 ? (
                    apnCallbacks.slice(0, 10).map((callback, index) => (
                      <div key={callback.id} className={`p-3 border-b last:border-b-0 ${
                        callback.status === 'success' ? 'bg-green-50' : 
                        callback.status === 'failed' ? 'bg-red-50' : 'bg-yellow-50'
                      }`}>
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium text-sm">{callback.method}</div>
                            <div className="text-xs text-gray-600">Order: {callback.orderId}</div>
                            <div className="text-xs text-gray-500">
                              {new Date(callback.timestamp).toLocaleString('vi-VN')}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-xs px-2 py-1 rounded ${
                              callback.status === 'success' ? 'bg-green-100 text-green-800' :
                              callback.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {callback.status === 'success' ? 'Thành công' :
                               callback.status === 'failed' ? 'Thất bại' : 'Đang xử lý'}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {callback.amount} {callback.currency}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500">Chưa có callback nào</div>
                  )}
                </div>
              </div>
            </div>

            {/* Provider-specific APN Details */}
             <div className="mt-6">
               <div className="flex items-center justify-between mb-3">
                 <h3 className="text-lg font-semibold text-gray-800">Chi tiết theo nhà cung cấp</h3>
                 <button
                   onClick={() => router.push(`/${store || 'shopname'}/admin/apn-history`)}
                   className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2"
                 >
                   <span>📊</span>
                   <span>Xem lịch sử chi tiết</span>
                 </button>
               </div>
               <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                 {Object.entries(apnStats.calls?.byProvider || {}).map(([provider, stats]) => (
                   <div key={provider} className="bg-white rounded-lg border p-4">
                     <div className="font-medium text-gray-800 mb-2">{provider}</div>
                     <div className="space-y-1 text-sm">
                       <div className="flex justify-between">
                         <span>Tổng:</span>
                         <span className="font-medium">{stats.total}</span>
                       </div>
                       <div className="flex justify-between">
                         <span>Thành công:</span>
                         <span className="text-green-600 font-medium">{stats.successful}</span>
                       </div>
                       <div className="flex justify-between">
                         <span>Thất bại:</span>
                         <span className="text-red-600 font-medium">{stats.failed}</span>
                       </div>
                       <div className="flex justify-between">
                         <span>Tỷ lệ thành công:</span>
                         <span className="font-medium">
                           {stats.total > 0 ? Math.round((stats.successful / stats.total) * 100) : 0}%
                         </span>
                       </div>
                       <div className="flex justify-between">
                         <span>Thời gian TB:</span>
                         <span className="font-medium">{stats.avgProcessingTime}ms</span>
                       </div>
                     </div>
                   </div>
                 ))}
               </div>
             </div>
          </div>
        )}

        {/* Providers Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {providers.map((provider) => (
            <div key={provider.id} className="bg-white rounded-lg shadow-md p-6">
              {/* Provider Header */}
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800">{provider.name}</h3>
                  <p className="text-sm text-gray-600">{provider.type}</p>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(provider.status, connectionStatus[provider.id])}
                  <span className={`text-sm font-medium ${
                    connectionStatus[provider.id] === 'error' ? 'text-red-600' :
                    provider.status === 'active' ? 'text-green-600' :
                    provider.status === 'test' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {getStatusText(provider.status, connectionStatus[provider.id])}
                  </span>
                </div>
              </div>

              {/* Connection Details */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">Thông tin kết nối</h4>
                <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                  {Object.entries(provider.config).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-gray-800">
                          {key.toLowerCase().includes('password') || key.toLowerCase().includes('secret') || key.toLowerCase().includes('key') ?
                            maskSensitiveData(value, showPasswords[`${provider.id}_${key}`]) :
                            value
                          }
                        </span>
                        {(key.toLowerCase().includes('password') || key.toLowerCase().includes('secret') || key.toLowerCase().includes('key')) && (
                          <button
                            onClick={() => togglePasswordVisibility(provider.id, key)}
                            className="text-gray-500 hover:text-gray-700"
                          >
                            {showPasswords[`${provider.id}_${key}`] ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        )}
                        <button
                          onClick={() => copyToClipboard(value)}
                          className="text-gray-500 hover:text-gray-700"
                        >
                          <FaCopy />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Limits and Fees */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">Giới hạn</h4>
                  <div className="bg-blue-50 rounded-lg p-3 text-sm">
                    <div>Tối thiểu: {provider.limits.minAmount} {provider.fee.currency}</div>
                    <div>Tối đa: {provider.limits.maxAmount} {provider.fee.currency}</div>
                    <div>Hết hạn: {provider.limits.expiryHours}h</div>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">Phí dịch vụ</h4>
                  <div className="bg-green-50 rounded-lg p-3 text-sm">
                    <div className="font-semibold">{provider.fee.amount} {provider.fee.currency}</div>
                    <div className="text-gray-600">Mỗi giao dịch</div>
                  </div>
                </div>
              </div>

              {/* Statistics */}
              {statistics[provider.id] && (
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">Thống kê</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="bg-purple-50 rounded p-2">
                      <div className="font-semibold">{statistics[provider.id].totalTransactions}</div>
                      <div className="text-gray-600">Tổng giao dịch</div>
                    </div>
                    <div className="bg-green-50 rounded p-2">
                      <div className="font-semibold">{statistics[provider.id].successRate}%</div>
                      <div className="text-gray-600">Tỷ lệ thành công</div>
                    </div>
                    <div className="bg-blue-50 rounded p-2">
                      <div className="font-semibold">{statistics[provider.id].avgResponseTime}ms</div>
                      <div className="text-gray-600">Thời gian phản hồi</div>
                    </div>
                    <div className="bg-orange-50 rounded p-2">
                      <div className="font-semibold">{pendingOrders[provider.id] || 0}</div>
                      <div className="text-gray-600">Đơn hàng chờ</div>
                    </div>
                  </div>
                  {statistics[provider.id]?.totalRevenue && (
                    <div className="mt-2 bg-yellow-50 rounded p-2 text-sm">
                      <div className="font-semibold">Doanh thu: {statistics[provider.id].totalRevenue} NT$</div>
                      <div className="text-gray-600">
                        Giao dịch cuối: {statistics[provider.id].lastTransaction ? 
                          new Date(statistics[provider.id].lastTransaction).toLocaleDateString('vi-VN') : 
                          'Chưa có'
                        }
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Pending Orders Details */}
              {pendingOrders[provider.id] > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">Đơn hàng chờ xử lý</h4>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-orange-800 font-medium">
                        {pendingOrders[provider.id]} đơn hàng đang chờ xử lý
                      </span>
                      <button
                        onClick={() => router.push(`/admin/orders?provider=${provider.id}&status=pending`)}
                        className="text-orange-600 hover:text-orange-800 text-sm underline"
                      >
                        Xem chi tiết
                      </button>
                    </div>
                    {pendingOrders[provider.id] > 10 && (
                      <div className="mt-2 text-sm text-orange-700">
                        ⚠️ Số lượng đơn hàng chờ cao, cần kiểm tra kết nối
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => testConnection(provider.id)}
                  disabled={connectionStatus[provider.id] === 'testing'}
                  className="flex items-center space-x-1 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 text-sm"
                >
                  <FaSync className={connectionStatus[provider.id] === 'testing' ? 'animate-spin' : ''} />
                  <span>{connectionStatus[provider.id] === 'testing' ? 'Đang kiểm tra...' : 'Kiểm tra kết nối'}</span>
                </button>
                <button
                  onClick={() => router.push(`/admin/provider-details/${provider.id}`)}
                  className="flex items-center space-x-1 px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 text-sm"
                >
                  <FaExternalLinkAlt />
                  <span>Chi tiết</span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Summary Stats */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">Tổng quan hệ thống</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-green-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {providers.filter(p => connectionStatus[p.id] === 'connected' || p.status === 'active').length}
              </div>
              <div className="text-sm text-gray-600">Nhà cung cấp hoạt động</div>
            </div>
            <div className="bg-red-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-600">
                {providers.filter(p => connectionStatus[p.id] === 'error').length}
              </div>
              <div className="text-sm text-gray-600">Lỗi kết nối</div>
            </div>
            <div className="bg-blue-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {Object.values(pendingOrders).reduce((sum, count) => sum + count, 0)}
              </div>
              <div className="text-sm text-gray-600">Tổng đơn hàng chờ</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Object.values(statistics).reduce((sum, stat) => sum + stat.totalTransactions, 0)}
              </div>
              <div className="text-sm text-gray-600">Tổng giao dịch</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProviderManagement