import React from 'react';
import { useAdminAuth } from '../../context/adminAuthContext';
import AdminLayout from '../../components/admin/AdminLayout';

const AdminTestContent = () => {
  const { user, loading, isAuthenticated } = useAdminAuth();

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Admin Test Page</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'None'}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Local Storage</h2>
          <div className="space-y-2">
            <p><strong>Admin Token:</strong> {typeof window !== 'undefined' ? localStorage.getItem('adminToken') || 'None' : 'Server Side'}</p>
            <p><strong>Admin User:</strong> {typeof window !== 'undefined' ? localStorage.getItem('adminUser') || 'None' : 'Server Side'}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const AdminTest = () => {
  return (
    <AdminLayout>
      <AdminTestContent />
    </AdminLayout>
  );
};

export default AdminTest;
