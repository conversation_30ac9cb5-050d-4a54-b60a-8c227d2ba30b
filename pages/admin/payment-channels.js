import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FiCreditCard, FiDollarSign, FiArrowLeft, FiSearch, FiFilter, FiEye, FiEdit, FiPlus, FiToggleLeft, FiToggleRight, FiTrendingUp, FiTrendingDown, FiActivity, FiSettings, FiCheck, FiX, FiAlertCircle } from 'react-icons/fi';

const PaymentChannelsManagement = () => {
  const router = useRouter();
  const { store } = router.query;
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [showChannelDetail, setShowChannelDetail] = useState(false);
  const [showAddChannel, setShowAddChannel] = useState(false);
  const [newChannel, setNewChannel] = useState({
    name: '',
    type: 'bank',
    accountNumber: '',
    accountName: '',
    bankName: '',
    description: '',
    feeRate: 0,
    minAmount: 0,
    maxAmount: 0
  });

  useEffect(() => {
    // Giả lập dữ liệu kênh thanh toán
    const mockChannels = [
      {
        id: 'PAY001',
        name: 'Vietcombank - Tài khoản chính',
        type: 'bank',
        accountNumber: '**********',
        accountName: 'CONG TY TNHH MAG GROUP',
        bankName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
        status: 'active',
        feeRate: 0.5,
        minAmount: 10000,
        maxAmount: ********,
        totalTransactions: 1250,
        totalAmount: *********,
        lastTransaction: '2024-01-15 14:30',
        description: 'Tài khoản chính cho thanh toán đơn hàng',
        createdDate: '2023-01-15'
      },
      {
        id: 'PAY002',
        name: 'MoMo Business',
        type: 'ewallet',
        accountNumber: '**********',
        accountName: 'MAG GROUP SHOP',
        bankName: 'MoMo',
        status: 'active',
        feeRate: 1.0,
        minAmount: 5000,
        maxAmount: ********,
        totalTransactions: 850,
        totalAmount: ********,
        lastTransaction: '2024-01-15 16:45',
        description: 'Ví điện tử MoMo cho thanh toán nhanh',
        createdDate: '2023-03-20'
      },
      {
        id: 'PAY003',
        name: 'ZaloPay Business',
        type: 'ewallet',
        accountNumber: '**********',
        accountName: 'MAG GROUP',
        bankName: 'ZaloPay',
        status: 'active',
        feeRate: 0.8,
        minAmount: 5000,
        maxAmount: ********,
        totalTransactions: 620,
        totalAmount: ********,
        lastTransaction: '2024-01-14 20:15',
        description: 'Ví điện tử ZaloPay',
        createdDate: '2023-05-10'
      },
      {
        id: 'PAY004',
        name: 'Techcombank - Tài khoản phụ',
        type: 'bank',
        accountNumber: '**********',
        accountName: 'CONG TY TNHH MAG GROUP',
        bankName: 'Ngân hàng TMCP Kỹ thương Việt Nam',
        status: 'inactive',
        feeRate: 0.3,
        minAmount: 10000,
        maxAmount: ********,
        totalTransactions: 320,
        totalAmount: ********,
        lastTransaction: '2023-12-20 10:30',
        description: 'Tài khoản phụ tạm ngưng sử dụng',
        createdDate: '2023-02-28'
      },
      {
        id: 'PAY005',
        name: 'VNPay Gateway',
        type: 'gateway',
        accountNumber: 'VNPAY_MERCHANT_001',
        accountName: 'MAG GROUP SHOP',
        bankName: 'VNPay',
        status: 'active',
        feeRate: 2.0,
        minAmount: 1000,
        maxAmount: *********,
        totalTransactions: 2100,
        totalAmount: *********,
        lastTransaction: '2024-01-15 18:20',
        description: 'Cổng thanh toán VNPay tích hợp',
        createdDate: '2023-04-15'
      }
    ];
    
    setTimeout(() => {
      setChannels(mockChannels);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredChannels = channels.filter(channel => {
    const matchesSearch = channel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         channel.accountNumber.includes(searchTerm) ||
                         channel.bankName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         channel.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || channel.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const getChannelTypeColor = (type) => {
    switch (type) {
      case 'bank': return 'text-blue-600 bg-blue-100';
      case 'ewallet': return 'text-green-600 bg-green-100';
      case 'gateway': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getChannelTypeText = (type) => {
    switch (type) {
      case 'bank': return 'Ngân hàng';
      case 'ewallet': return 'Ví điện tử';
      case 'gateway': return 'Cổng thanh toán';
      default: return 'Khác';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-red-600 bg-red-100';
      case 'maintenance': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Hoạt động';
      case 'inactive': return 'Tạm ngưng';
      case 'maintenance': return 'Bảo trì';
      default: return 'Không xác định';
    }
  };

  const toggleChannelStatus = (channelId) => {
    setChannels(channels.map(channel => {
      if (channel.id === channelId) {
        return {
          ...channel,
          status: channel.status === 'active' ? 'inactive' : 'active'
        };
      }
      return channel;
    }));
  };

  const handleViewChannel = (channel) => {
    setSelectedChannel(channel);
    setShowChannelDetail(true);
  };

  const handleAddChannel = (e) => {
    e.preventDefault();
    const channel = {
      id: `PAY${String(channels.length + 1).padStart(3, '0')}`,
      ...newChannel,
      status: 'active',
      totalTransactions: 0,
      totalAmount: 0,
      lastTransaction: null,
      createdDate: new Date().toISOString().split('T')[0]
    };
    
    setChannels([channel, ...channels]);
    setNewChannel({
      name: '',
      type: 'bank',
      accountNumber: '',
      accountName: '',
      bankName: '',
      description: '',
      feeRate: 0,
      minAmount: 0,
      maxAmount: 0
    });
    setShowAddChannel(false);
  };

  const channelStats = {
    total: channels.length,
    active: channels.filter(c => c.status === 'active').length,
    inactive: channels.filter(c => c.status === 'inactive').length,
    totalTransactions: channels.reduce((sum, c) => sum + c.totalTransactions, 0),
    totalAmount: channels.reduce((sum, c) => sum + c.totalAmount, 0),
    avgFeeRate: channels.length > 0 ? (channels.reduce((sum, c) => sum + c.feeRate, 0) / channels.length).toFixed(2) : 0
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin`)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5 mr-2" />
            Quay lại trang chủ Admin
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FiCreditCard className="w-8 h-8 mr-3 text-blue-600" />
                Quản lý kênh thanh toán
              </h1>
              <p className="text-gray-600 mt-2">Theo dõi và quản lý các phương thức thanh toán</p>
            </div>
            
            <div className="text-right">
              <div className="text-sm text-gray-500">Tổng kênh</div>
              <div className="text-2xl font-bold text-blue-600">{channelStats.total}</div>
            </div>
          </div>
        </div>

        {/* Thống kê nhanh */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Hoạt động</p>
                <p className="text-2xl font-bold text-green-600">{channelStats.active}</p>
              </div>
              <FiCheck className="w-8 h-8 text-green-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tạm ngưng</p>
                <p className="text-2xl font-bold text-red-600">{channelStats.inactive}</p>
              </div>
              <FiX className="w-8 h-8 text-red-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng giao dịch</p>
                <p className="text-2xl font-bold text-blue-600">{channelStats.totalTransactions.toLocaleString('vi-VN')}</p>
              </div>
              <FiActivity className="w-8 h-8 text-blue-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng tiền</p>
                <p className="text-2xl font-bold text-green-600">
                  {channelStats.totalAmount.toLocaleString('vi-VN')}đ
                </p>
              </div>
              <FiDollarSign className="w-8 h-8 text-green-600" />
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Phí TB</p>
                <p className="text-2xl font-bold text-purple-600">{channelStats.avgFeeRate}%</p>
              </div>
              <FiTrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Danh sách kênh thanh toán */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h2 className="text-xl font-semibold text-gray-900">Danh sách kênh thanh toán</h2>
              
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Tìm kiếm kênh..."
                  />
                </div>
                
                <div className="relative">
                  <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                  >
                    <option value="all">Tất cả trạng thái</option>
                    <option value="active">Hoạt động</option>
                    <option value="inactive">Tạm ngưng</option>
                    <option value="maintenance">Bảo trì</option>
                  </select>
                </div>
                
                <button
                  onClick={() => setShowAddChannel(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <FiPlus className="w-4 h-4 mr-2" />
                  Thêm kênh
                </button>
              </div>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Đang tải dữ liệu...</p>
              </div>
            ) : filteredChannels.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <FiCreditCard className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Không có kênh thanh toán nào</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-6 p-6">
                {filteredChannels.map((channel) => (
                  <div key={channel.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                          <FiCreditCard className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{channel.name}</h3>
                          <p className="text-sm text-blue-600 font-medium">{channel.id}</p>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(channel.status)}`}>
                        {getStatusText(channel.status)}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-gray-500 mb-1">Loại kênh</p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getChannelTypeColor(channel.type)}`}>
                          {getChannelTypeText(channel.type)}
                        </span>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 mb-1">Phí giao dịch</p>
                        <p className="text-sm font-semibold text-gray-900">{channel.feeRate}%</p>
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <p className="text-xs text-gray-500 mb-1">Ngân hàng/Nhà cung cấp</p>
                      <p className="text-sm font-medium text-gray-900">{channel.bankName}</p>
                    </div>
                    
                    <div className="mb-4">
                      <p className="text-xs text-gray-500 mb-1">Thông tin tài khoản</p>
                      <p className="text-sm text-gray-900">{channel.accountNumber}</p>
                      <p className="text-sm text-gray-500">{channel.accountName}</p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-gray-500 mb-1">Tổng giao dịch</p>
                        <p className="text-sm font-medium text-gray-900">{channel.totalTransactions.toLocaleString('vi-VN')}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 mb-1">Tổng tiền</p>
                        <p className="text-sm font-medium text-gray-900">{channel.totalAmount.toLocaleString('vi-VN')}đ</p>
                      </div>
                    </div>
                    
                    <div className="flex justify-end space-x-2 pt-4 border-t border-gray-200">
                      <button
                        onClick={() => handleViewChannel(channel)}
                        className="px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-sm flex items-center"
                      >
                        <FiEye className="w-4 h-4 mr-1" /> Chi tiết
                      </button>
                      <button className="px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm flex items-center">
                        <FiEdit className="w-4 h-4 mr-1" /> Sửa
                      </button>
                      <button
                        onClick={() => toggleChannelStatus(channel.id)}
                        className={`px-3 py-2 rounded-md text-sm flex items-center ${
                          channel.status === 'active' 
                            ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                            : 'bg-green-100 text-green-700 hover:bg-green-200'
                        }`}
                      >
                        {channel.status === 'active' ? (
                          <><FiToggleRight className="w-4 h-4 mr-1" /> Tắt</>
                        ) : (
                          <><FiToggleLeft className="w-4 h-4 mr-1" /> Bật</>
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Modal chi tiết kênh */}
        {showChannelDetail && selectedChannel && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Chi tiết kênh {selectedChannel.id}
                  </h3>
                  <button
                    onClick={() => setShowChannelDetail(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                {/* Thông tin cơ bản */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Thông tin cơ bản</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Tên kênh</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedChannel.name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Loại kênh</label>
                      <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getChannelTypeColor(selectedChannel.type)}`}>
                        {getChannelTypeText(selectedChannel.type)}
                      </span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Số tài khoản</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedChannel.accountNumber}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Tên tài khoản</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedChannel.accountName}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Ngân hàng/Nhà cung cấp</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedChannel.bankName}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Trạng thái</label>
                      <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedChannel.status)}`}>
                        {getStatusText(selectedChannel.status)}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Cấu hình phí và giới hạn */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Cấu hình phí và giới hạn</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiDollarSign className="w-8 h-8 text-blue-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Phí giao dịch</p>
                          <p className="text-2xl font-bold text-blue-600">{selectedChannel.feeRate}%</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiTrendingDown className="w-8 h-8 text-green-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Số tiền tối thiểu</p>
                          <p className="text-lg font-bold text-green-600">
                            {selectedChannel.minAmount.toLocaleString('vi-VN')}đ
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiTrendingUp className="w-8 h-8 text-purple-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Số tiền tối đa</p>
                          <p className="text-lg font-bold text-purple-600">
                            {selectedChannel.maxAmount.toLocaleString('vi-VN')}đ
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Thống kê giao dịch */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Thống kê giao dịch</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiActivity className="w-8 h-8 text-yellow-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Tổng giao dịch</p>
                          <p className="text-2xl font-bold text-yellow-600">
                            {selectedChannel.totalTransactions.toLocaleString('vi-VN')}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiDollarSign className="w-8 h-8 text-green-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Tổng số tiền</p>
                          <p className="text-lg font-bold text-green-600">
                            {selectedChannel.totalAmount.toLocaleString('vi-VN')}đ
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center">
                        <FiActivity className="w-8 h-8 text-blue-600 mr-3" />
                        <div>
                          <p className="text-sm text-gray-600">Giao dịch cuối</p>
                          <p className="text-sm font-bold text-blue-600">
                            {selectedChannel.lastTransaction || 'Chưa có'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Mô tả */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">Mô tả</h4>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {selectedChannel.description || 'Không có mô tả'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modal thêm kênh */}
        {showAddChannel && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900">Thêm kênh thanh toán mới</h3>
                  <button
                    onClick={() => setShowAddChannel(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>
              </div>
              
              <form onSubmit={handleAddChannel} className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tên kênh *
                    </label>
                    <input
                      type="text"
                      value={newChannel.name}
                      onChange={(e) => setNewChannel({...newChannel, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Loại kênh *
                    </label>
                    <select
                      value={newChannel.type}
                      onChange={(e) => setNewChannel({...newChannel, type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="bank">Ngân hàng</option>
                      <option value="ewallet">Ví điện tử</option>
                      <option value="gateway">Cổng thanh toán</option>
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số tài khoản *
                    </label>
                    <input
                      type="text"
                      value={newChannel.accountNumber}
                      onChange={(e) => setNewChannel({...newChannel, accountNumber: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tên tài khoản *
                    </label>
                    <input
                      type="text"
                      value={newChannel.accountName}
                      onChange={(e) => setNewChannel({...newChannel, accountName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ngân hàng/Nhà cung cấp *
                  </label>
                  <input
                    type="text"
                    value={newChannel.bankName}
                    onChange={(e) => setNewChannel({...newChannel, bankName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phí giao dịch (%)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      value={newChannel.feeRate}
                      onChange={(e) => setNewChannel({...newChannel, feeRate: parseFloat(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số tiền tối thiểu
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={newChannel.minAmount}
                      onChange={(e) => setNewChannel({...newChannel, minAmount: parseInt(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số tiền tối đa
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={newChannel.maxAmount}
                      onChange={(e) => setNewChannel({...newChannel, maxAmount: parseInt(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả
                  </label>
                  <textarea
                    value={newChannel.description}
                    onChange={(e) => setNewChannel({...newChannel, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                  />
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddChannel(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Thêm kênh
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentChannelsManagement;