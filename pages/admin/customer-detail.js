import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../components/admin/AdminLayout';
import ProfileApprovalTab from '../../components/admin/ProfileApprovalTab';
import {
  FiUser, FiPhone, FiMail, FiMapPin, FiCalendar, FiStar,
  FiGift, FiFileText, FiEye, FiDownload, FiCheck, FiX,
  FiTrash2, FiHistory, FiArrowLeft, FiEdit3
} from 'react-icons/fi';
import { FaChartPie } from 'react-icons/fa';

const CustomerDetail = () => {
  const router = useRouter();
  const { id, tab } = router.query;
  const [activeTab, setActiveTab] = useState('info');
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [customerDocuments, setCustomerDocuments] = useState({});
  const [documentLoading, setDocumentLoading] = useState(false);
  const [customerCompliance, setCustomerCompliance] = useState({});
  const [loyaltyData, setLoyaltyData] = useState({});
  const [showLoyaltyModal, setShowLoyaltyModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState('');
  const [loyaltyAdjustment, setLoyaltyAdjustment] = useState({ points: '', reason: '' });
  const [loyaltyHistory, setLoyaltyHistory] = useState([]);
  const [documentHistory, setDocumentHistory] = useState([]);

  // Document requirements configuration
  const documentRequirements = {
    'photo': { label: 'Ảnh chân dung', required: true },
    'idCard': { label: 'CMND/CCCD', required: true },
    'passport': { label: 'Hộ chiếu', required: false },
    'workPermit': { label: 'Giấy phép lao động', required: false },
    'residenceCard': { label: 'Thẻ cư trú', required: false },
    'bankStatement': { label: 'Sao kê ngân hàng', required: false },
    'utilityBill': { label: 'Hóa đơn tiện ích', required: false }
  };



  useEffect(() => {
    if (id) {
      fetchCustomerData();
      fetchCustomerDocuments();
      fetchLoyaltyData();
    }
    if (tab) {
      setActiveTab(tab);
    }
  }, [id, tab]);

  const fetchCustomerData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/customers/${id}`);
      if (response.ok) {
        const customerData = await response.json();
        
        // Transform data to match UI expectations
        const transformedCustomer = {
          id: customerData.id,
          name: customerData.name || customerData.personalDetails?.name || 'N/A',
          phone: customerData.phone || customerData.contactInfo?.primaryPhone || 'N/A',
          email: customerData.email || customerData.contactInfo?.email || 'N/A',
          address: customerData.address || 
                  (customerData.addresses && customerData.addresses[0] ? 
                   `${customerData.addresses[0].street}, ${customerData.addresses[0].city}` : 'N/A'),
          customerType: customerData.membershipInfo?.membershipLevel || 'regular',
          status: customerData.accessControl?.accountStatus || 'active',
          totalOrders: customerData.orders?.length || 0,
          totalSpent: customerData.purchaseHistory?.reduce((sum, purchase) => sum + (purchase.amount || 0), 0) || 0,
          joinDate: customerData.createdAt ? new Date(customerData.createdAt).toLocaleDateString('vi-VN') : 'N/A',
          lastOrderDate: customerData.accessControl?.lastLogin ? new Date(customerData.accessControl.lastLogin).toLocaleDateString('vi-VN') : null,
          note: customerData.notes || ''
        };
        
        setCustomer(transformedCustomer);
      } else {
        console.error('Customer not found');
        setCustomer(null);
      }
    } catch (error) {
      console.error('Error fetching customer:', error);
      setCustomer(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchCustomerDocuments = async () => {
    setDocumentLoading(true);
    try {
      const response = await fetch(`/api/customer/documents/${id}`);
      if (response.ok) {
        const documentsData = await response.json();
        
        // Transform documents data to match UI expectations
        const transformedDocuments = {};
        documentsData.forEach(doc => {
          if (!transformedDocuments[id]) {
            transformedDocuments[id] = {};
          }
          if (!transformedDocuments[id][doc.type]) {
            transformedDocuments[id][doc.type] = [];
          }
          transformedDocuments[id][doc.type].push({
            filename: doc.filename,
            uploadedAt: doc.uploadedAt || new Date().toISOString(),
            status: doc.status || 'pending'
          });
        });
        
        setCustomerDocuments(transformedDocuments);
        
        // Calculate compliance
        const totalRequirements = Object.keys(documentRequirements).length;
        const completedRequirements = Object.keys(transformedDocuments[id] || {}).length;
        const percentage = Math.round((completedRequirements / totalRequirements) * 100);
        
        setCustomerCompliance({
          [id]: { percentage }
        });
      } else {
        console.error('Failed to fetch documents');
        setCustomerDocuments({});
        setCustomerCompliance({ [id]: { percentage: 0 } });
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      setCustomerDocuments({});
      setCustomerCompliance({ [id]: { percentage: 0 } });
    } finally {
      setDocumentLoading(false);
    }
  };

  const fetchLoyaltyData = async () => {
    try {
      const response = await fetch(`/api/customers/${id}`);
      if (response.ok) {
        const customerData = await response.json();
        
        // Extract loyalty data from customer data
        const loyaltyInfo = {
          availablePoints: customerData.membershipInfo?.loyaltyPoints || 0,
          totalEarned: customerData.membershipInfo?.totalPointsEarned || 0,
          totalRedeemed: customerData.membershipInfo?.totalPointsRedeemed || 0,
          tier: customerData.membershipInfo?.membershipLevel || 'Đồng',
          nextTierPoints: customerData.membershipInfo?.pointsToNextLevel || 0
        };
        
        setLoyaltyData({ [id]: loyaltyInfo });
      } else {
        console.error('Failed to fetch loyalty data');
        setLoyaltyData({});
      }
    } catch (error) {
      console.error('Error fetching loyalty data:', error);
      setLoyaltyData({});
    }
  };

  const handleDocumentAction = async (doc, action) => {
    try {
      console.log(`Document ${action}:`, doc);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      fetchCustomerDocuments();
    } catch (error) {
      console.error(`Error ${action} document:`, error);
    }
  };

  const handleShowDocumentHistory = (customer, docType) => {
    setSelectedDocumentType(docType);
    setDocumentHistory([
      {
        action: 'uploaded',
        filename: 'passport_scan.pdf',
        date: '2024-11-15T10:30:00Z',
        status: 'approved',
        note: 'Tài liệu hợp lệ'
      }
    ]);
    setShowHistoryModal(true);
  };

  const handleLoyaltyAdjustment = async () => {
    try {
      console.log('Loyalty adjustment:', loyaltyAdjustment);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowLoyaltyModal(false);
      setLoyaltyAdjustment({ points: '', reason: '' });
      fetchLoyaltyData();
    } catch (error) {
      console.error('Error adjusting loyalty:', error);
    }
  };

  // Helper functions
  const getCustomerTypeColor = (type) => {
    switch (type) {
      case 'vip': return 'bg-yellow-100 text-yellow-800';
      case 'regular': return 'bg-blue-100 text-blue-800';
      case 'new': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCustomerTypeText = (type) => {
    switch (type) {
      case 'vip': return 'VIP';
      case 'regular': return 'Thường';
      case 'new': return 'Mới';
      default: return 'Không xác định';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Hoạt động';
      case 'inactive': return 'Không hoạt động';
      case 'pending': return 'Chờ xử lý';
      default: return 'Không xác định';
    }
  };

  const getTierColor = (tier) => {
    switch (tier) {
      case 'Vàng': return 'bg-yellow-100 text-yellow-800';
      case 'Bạc': return 'bg-gray-100 text-gray-800';
      case 'Đồng': return 'bg-orange-100 text-orange-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getDocumentStatusBadge = (status) => {
    const statusConfig = {
      'approved': { color: 'bg-green-100 text-green-800', text: 'Đã duyệt' },
      'pending_approval': { color: 'bg-yellow-100 text-yellow-800', text: 'Chờ duyệt' },
      'rejected': { color: 'bg-red-100 text-red-800', text: 'Từ chối' },
      'missing': { color: 'bg-gray-100 text-gray-800', text: 'Thiếu' }
    };
    
    const config = statusConfig[status] || statusConfig['missing'];
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getDocumentTypeLabel = (docType) => {
    return documentRequirements[docType]?.label || docType;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getComplianceColor = (percentage) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    if (percentage >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  const getComplianceBadgeColor = (percentage) => {
    if (percentage >= 90) return 'bg-green-100 text-green-800';
    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800';
    if (percentage >= 50) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Đang tải dữ liệu...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!customer) {
    return (
      <AdminLayout>
        <div className="p-8 text-center">
          <p className="text-gray-500">Không tìm thấy khách hàng</p>
          <button
            onClick={() => router.push('/admin/customers')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Quay lại danh sách
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/admin/customers')}
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <FiArrowLeft className="w-5 h-5 mr-2" />
              Quay lại
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Chi tiết khách hàng</h1>
              <p className="text-gray-600">{customer.name} - {customer.id}</p>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('info')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'info'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Thông tin cơ bản
            </button>
            <button
              onClick={() => setActiveTab('documents')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'documents'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Yêu cầu tài liệu
            </button>
            <button
              onClick={() => setActiveTab('loyalty')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'loyalty'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Điểm loyalty
            </button>
            <button
              onClick={() => setActiveTab('approval')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'approval'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Duyệt hồ sơ
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'info' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Thông tin cơ bản</h3>
                
                <div className="flex items-center space-x-4">
                  <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                    <FiUser className="w-8 h-8 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">{customer.name}</h4>
                    <p className="text-blue-600 font-medium">{customer.id}</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <FiPhone className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.phone}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FiMail className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.email}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FiMapPin className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">{customer.address}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FiCalendar className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-900">Tham gia: {customer.joinDate}</span>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getCustomerTypeColor(customer.customerType)}`}>
                    {customer.customerType === 'vip' && <FiStar className="w-4 h-4 mr-1" />}
                    {getCustomerTypeText(customer.customerType)}
                  </span>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(customer.status)}`}>
                    {getStatusText(customer.status)}
                  </span>
                </div>
              </div>

              {/* Stats */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Thống kê</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">{customer.totalOrders}</div>
                    <div className="text-sm text-gray-500">Tổng đơn hàng</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {customer.totalSpent.toLocaleString('vi-VN')}đ
                    </div>
                    <div className="text-sm text-gray-500">Tổng chi tiêu</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {loyaltyData?.[id]?.availablePoints?.toLocaleString() || 0}
                    </div>
                    <div className="text-sm text-gray-500">Điểm hiện tại</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className={`text-2xl font-bold ${getComplianceColor(customerCompliance[customer.id]?.percentage || 0)}`}>
                      {customerCompliance[customer.id]?.percentage || 0}%
                    </div>
                    <div className="text-sm text-gray-500">Tuân thủ tài liệu</div>
                  </div>
                </div>

                {customer.lastOrderDate && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-600">Đơn hàng cuối cùng</div>
                    <div className="text-lg font-semibold text-blue-600">{customer.lastOrderDate}</div>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Ghi chú</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-900">{customer.note || 'Không có ghi chú'}</p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'approval' && (
          <ProfileApprovalTab customerId={id} customerData={customer} />
        )}

        {activeTab === 'documents' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Yêu cầu tài liệu</h3>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  Tuân thủ: <span className={`font-semibold ${getComplianceColor(customerCompliance[customer.id]?.percentage || 0)}`}>
                    {customerCompliance[customer.id]?.percentage || 0}%
                  </span>
                </span>
                {getDocumentStatusBadge(
                  (customerCompliance[customer.id]?.percentage || 0) >= 90 ? 'approved' :
                  (customerCompliance[customer.id]?.percentage || 0) >= 70 ? 'pending_approval' : 'missing'
                )}
              </div>
            </div>

            {/* Document Requirements */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-900 flex items-center">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                Tài liệu bắt buộc
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(documentRequirements)
                  .filter(([_, req]) => req.required)
                  .map(([docType, requirement]) => {
                    const hasDocument = customerDocuments[customer.id]?.[docType];
                    const documents = hasDocument || [];
                    
                    return (
                      <div key={docType} className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="text-sm font-semibold text-gray-900">
                            {requirement.label}
                          </h5>
                          <div className="flex items-center space-x-2">
                            {getDocumentStatusBadge(hasDocument ? 'approved' : 'missing')}
                            {hasDocument && (
                              <button
                                onClick={() => handleShowDocumentHistory(customer, docType)}
                                className="text-gray-600 hover:text-gray-900 text-xs flex items-center"
                              >
                                <FiHistory className="mr-1" />
                                Lịch sử
                              </button>
                            )}
                          </div>
                        </div>
                        
                        {hasDocument ? (
                          <div className="space-y-2">
                            {documents.map((doc, index) => (
                              <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
                                <div className="flex items-center space-x-3">
                                  <div className="h-8 w-8 bg-blue-100 rounded flex items-center justify-center">
                                    <FiFileText className="w-4 h-4 text-blue-600" />
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">
                                      {doc.filename || 'Tài liệu'}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      Tải lên: {formatDate(doc.uploadedAt)}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getDocumentStatusBadge(doc.status)}
                                  <div className="flex items-center space-x-1">
                                    <button
                                      onClick={() => window.open(`/api/documents/view/${docType}/${doc.filename}?customerId=${customer.id}`, '_blank')}
                                      className="text-blue-600 hover:text-blue-800 p-1"
                                      title="Xem tài liệu"
                                    >
                                      <FiEye className="w-4 h-4" />
                                    </button>
                                    <button
                                      onClick={() => {
                                        const link = document.createElement('a');
                                        link.href = `/api/documents/view/${docType}/${doc.filename}?customerId=${customer.id}`;
                                        link.download = doc.filename;
                                        link.click();
                                      }}
                                      className="text-green-600 hover:text-green-800 p-1"
                                      title="Tải xuống"
                                    >
                                      <FiDownload className="w-4 h-4" />
                                    </button>
                                    {doc.status === 'pending_approval' && (
                                      <>
                                        <button
                                          onClick={() => handleDocumentAction(doc, 'approve')}
                                          className="text-green-600 hover:text-green-800 p-1"
                                          title="Duyệt tài liệu"
                                        >
                                          <FiCheck className="w-4 h-4" />
                                        </button>
                                        <button
                                          onClick={() => handleDocumentAction(doc, 'reject')}
                                          className="text-red-600 hover:text-red-800 p-1"
                                          title="Từ chối tài liệu"
                                        >
                                          <FiX className="w-4 h-4" />
                                        </button>
                                      </>
                                    )}
                                    <button
                                      onClick={() => {
                                        if (confirm('Bạn có chắc chắn muốn xóa tài liệu này?')) {
                                          handleDocumentAction(doc, 'delete');
                                        }
                                      }}
                                      className="text-red-600 hover:text-red-800 p-1"
                                      title="Xóa tài liệu"
                                    >
                                      <FiTrash2 className="w-4 h-4" />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            <FiFileText className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                            <p className="text-sm">Chưa có tài liệu</p>
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>

              <h4 className="text-md font-medium text-gray-900 flex items-center mt-6">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                Tài liệu khuyến nghị
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(documentRequirements)
                  .filter(([_, req]) => !req.required)
                  .map(([docType, requirement]) => {
                    const hasDocument = customerDocuments[customer.id]?.[docType];
                    const documents = hasDocument || [];
                    
                    return (
                      <div key={docType} className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="text-sm font-semibold text-gray-900">
                            {requirement.label}
                          </h5>
                          <div className="flex items-center space-x-2">
                            {getDocumentStatusBadge(hasDocument ? 'approved' : 'missing')}
                            {hasDocument && (
                              <button
                                onClick={() => handleShowDocumentHistory(customer, docType)}
                                className="text-gray-600 hover:text-gray-900 text-xs flex items-center"
                              >
                                <FiHistory className="mr-1" />
                                Lịch sử
                              </button>
                            )}
                          </div>
                        </div>
                        
                        {!hasDocument && (
                          <div className="text-center py-4 text-gray-500">
                            <FiFileText className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                            <p className="text-sm">Khuyến nghị</p>
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'loyalty' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Điểm loyalty</h3>
              <button
                onClick={() => setShowLoyaltyModal(true)}
                className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                <FiEdit3 className="w-4 h-4 mr-2" />
                Điều chỉnh điểm
              </button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Điểm hiện tại</p>
                <p className="text-2xl font-bold text-blue-600">
                  {loyaltyData?.[id]?.availablePoints || 0}
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Tổng điểm tích lũy</p>
                <p className="text-2xl font-bold text-green-600">
                  {loyaltyData?.[id]?.totalEarned || 0}
                </p>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Điểm đã sử dụng</p>
                <p className="text-2xl font-bold text-orange-600">
                  {loyaltyData?.[id]?.totalRedeemed || 0}
                </p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Hạng thành viên</p>
                <p className="text-2xl font-bold text-purple-600">
                  {loyaltyData?.[id]?.tier || 'Đồng'}
                </p>
              </div>
            </div>

            <div className="mt-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-medium text-gray-900">Hạng thành viên</h4>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getTierColor(loyaltyData?.[id]?.tier)}`}>
                  <FiGift className="w-4 h-4 mr-1" />
                  {loyaltyData?.[id]?.tier || 'Đồng'}
                </span>
              </div>
              
              {loyaltyData?.[id]?.nextTierPoints && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">
                    Cần thêm <span className="font-semibold text-purple-600">
                      {loyaltyData?.[id].nextTierPoints.toLocaleString()}
                    </span> điểm để lên hạng tiếp theo
                  </p>
                  <div className="mt-3">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Tiến độ lên hạng tiếp theo</span>
                      <span className="text-sm text-gray-600">
                        {loyaltyData?.[id]?.nextTierPoints || 0} điểm nữa
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ 
                          width: `${Math.min(100, ((loyaltyData?.[id]?.availablePoints || 0) / 
                            ((loyaltyData?.[id]?.availablePoints || 0) + (loyaltyData?.[id]?.nextTierPoints || 1))) * 100)}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Loyalty Adjustment Modal */}
        {showLoyaltyModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Điều chỉnh điểm loyalty</h3>
              
              <p className="text-sm text-gray-600 mb-4">
                Điểm hiện tại: <span className="font-semibold">{loyaltyData?.[id]?.availablePoints || 0}</span>
              </p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Số điểm (+ để cộng, - để trừ)
                  </label>
                  <input
                    type="number"
                    value={loyaltyAdjustment.points}
                    onChange={(e) => setLoyaltyAdjustment({...loyaltyAdjustment, points: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Nhập số điểm"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Lý do điều chỉnh
                  </label>
                  <textarea
                    value={loyaltyAdjustment.reason}
                    onChange={(e) => setLoyaltyAdjustment({...loyaltyAdjustment, reason: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    rows={3}
                    placeholder="Nhập lý do điều chỉnh"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowLoyaltyModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Hủy
                </button>
                <button
                  onClick={handleLoyaltyAdjustment}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                  disabled={!loyaltyAdjustment.points || !loyaltyAdjustment.reason}
                >
                  Xác nhận
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Document History Modal */}
        {showHistoryModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Lịch sử tài liệu: {getDocumentTypeLabel(selectedDocumentType)}
                </h3>
                <button
                  onClick={() => setShowHistoryModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="w-6 h-6" />
                </button>
              </div>
              
              <div className="space-y-4">
                {documentHistory.map((entry, index) => (
                  <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <FiFileText className="w-4 h-4 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900">{entry.filename}</p>
                        {getDocumentStatusBadge(entry.status)}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDate(entry.date)}
                      </p>
                      {entry.note && (
                        <p className="text-sm text-gray-600 mt-2">{entry.note}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default CustomerDetail;