import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { FaArrowLeft, FaSearch, FaFilter, FaDownload, FaEye, FaSyncAlt } from 'react-icons/fa'

export default function APNHistory() {
  const router = useRouter()
  const { store } = router.query
  
  const [apnCalls, setApnCalls] = useState([])
  const [apnCallbacks, setApnCallbacks] = useState([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('calls')
  const [filters, setFilters] = useState({
    provider: '',
    status: '',
    dateFrom: '',
    dateTo: '',
    orderId: '',
    transactionId: ''
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  })
  const [selectedItem, setSelectedItem] = useState(null)
  const [showDetails, setShowDetails] = useState(false)
  const [statistics, setStatistics] = useState({})

  useEffect(() => {
    loadData()
  }, [activeTab, filters, pagination.page])

  const loadData = async () => {
    setLoading(true)
    try {
      if (activeTab === 'calls') {
        await loadAPNCalls()
      } else {
        await loadAPNCallbacks()
      }
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadAPNCalls = async () => {
    const params = new URLSearchParams({
      page: pagination.page,
      limit: pagination.limit,
      ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
    })
    
    const response = await fetch(`/api/admin/apn-calls?${params}`)
    const data = await response.json()
    
    if (data.success) {
      setApnCalls(data.calls)
      setPagination(prev => ({ ...prev, total: data.total }))
      setStatistics(data.statistics)
    }
  }

  const loadAPNCallbacks = async () => {
    const params = new URLSearchParams({
      page: pagination.page,
      limit: pagination.limit,
      ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
    })
    
    const response = await fetch(`/api/admin/apn-callbacks?${params}`)
    const data = await response.json()
    
    if (data.success) {
      setApnCallbacks(data.callbacks)
      setPagination(prev => ({ ...prev, total: data.total }))
      setStatistics(data.summary)
    }
  }

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const clearFilters = () => {
    setFilters({
      provider: '',
      status: '',
      dateFrom: '',
      dateTo: '',
      orderId: '',
      transactionId: ''
    })
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const exportData = async () => {
    try {
      const endpoint = activeTab === 'calls' ? '/api/admin/apn-calls' : '/api/admin/apn-callbacks'
      const params = new URLSearchParams({
        export: 'true',
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      })
      
      const response = await fetch(`${endpoint}?${params}`)
      const blob = await response.blob()
      
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `apn-${activeTab}-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const getStatusBadge = (status, success) => {
    if (activeTab === 'calls') {
      return (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {success ? 'Thành công' : 'Thất bại'}
        </span>
      )
    } else {
      const statusColors = {
        success: 'bg-green-100 text-green-800',
        failed: 'bg-red-100 text-red-800',
        pending: 'bg-yellow-100 text-yellow-800',
        processing: 'bg-blue-100 text-blue-800'
      }
      
      return (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status] || 'bg-gray-100 text-gray-800'
        }`}>
          {status === 'success' ? 'Thành công' :
           status === 'failed' ? 'Thất bại' :
           status === 'pending' ? 'Chờ xử lý' :
           status === 'processing' ? 'Đang xử lý' : status}
        </span>
      )
    }
  }

  const renderCallsTable = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 p-4">
      {apnCalls.map((call) => (
        <div key={call.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-900">
                {call.orderId}
              </span>
            </div>
            {getStatusBadge(null, call.success)}
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-3">
            <div>
              <p className="text-xs text-gray-500 mb-1">Nhà cung cấp</p>
              <p className="text-sm font-medium text-gray-900">{call.provider}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 mb-1">Thời gian xử lý</p>
              <p className="text-sm text-gray-900">{call.processingTimeMs}ms</p>
            </div>
          </div>
          
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-1">Endpoint</p>
            <p className="text-sm text-gray-700 truncate">{call.endpoint}</p>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-500 mb-1">Thời gian</p>
              <p className="text-sm text-gray-700">{formatTimestamp(call.timestamp)}</p>
            </div>
            <button
              onClick={() => {
                setSelectedItem(call)
                setShowDetails(true)
              }}
              className="px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-sm flex items-center"
            >
              <FaEye className="mr-1" /> Chi tiết
            </button>
          </div>
        </div>
      ))}
    </div>
  )

  const renderCallbacksTable = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 p-4">
      {apnCallbacks.map((callback) => (
        <div key={callback.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-900">
                {callback.orderId}
              </span>
            </div>
            {getStatusBadge(callback.status)}
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-3">
            <div>
              <p className="text-xs text-gray-500 mb-1">Phương thức</p>
              <p className="text-sm font-medium text-gray-900">{callback.method}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 mb-1">Số tiền</p>
              <p className="text-sm text-gray-900">{callback.amount} {callback.currency}</p>
            </div>
          </div>
          
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-1">Transaction ID</p>
            <p className="text-sm text-gray-700">{callback.transactionId || 'N/A'}</p>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-500 mb-1">Thời gian</p>
              <p className="text-sm text-gray-700">{formatTimestamp(callback.timestamp)}</p>
            </div>
            <button
              onClick={() => {
                setSelectedItem(callback)
                setShowDetails(true)
              }}
              className="px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-sm flex items-center"
            >
              <FaEye className="mr-1" /> Chi tiết
            </button>
          </div>
        </div>
      ))}
    </div>
  )

  const renderDetailsModal = () => {
    if (!showDetails || !selectedItem) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">
              Chi tiết {activeTab === 'calls' ? 'Cuộc gọi APN' : 'Callback APN'}
            </h3>
            <button
              onClick={() => setShowDetails(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">ID</label>
                <div className="mt-1 text-sm text-gray-900">{selectedItem.id}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Thời gian</label>
                <div className="mt-1 text-sm text-gray-900">{formatTimestamp(selectedItem.timestamp)}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {activeTab === 'calls' ? 'Nhà cung cấp' : 'Phương thức'}
                </label>
                <div className="mt-1 text-sm text-gray-900">
                  {activeTab === 'calls' ? selectedItem.provider : selectedItem.method}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Order ID</label>
                <div className="mt-1 text-sm text-gray-900">{selectedItem.orderId}</div>
              </div>
            </div>
            
            {activeTab === 'calls' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Endpoint</label>
                  <div className="mt-1 text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded">
                    {selectedItem.endpoint}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Request Payload</label>
                  <pre className="mt-1 text-sm text-gray-900 bg-gray-100 p-3 rounded overflow-x-auto">
                    {JSON.stringify(selectedItem.requestPayload, null, 2)}
                  </pre>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Response</label>
                  <pre className="mt-1 text-sm text-gray-900 bg-gray-100 p-3 rounded overflow-x-auto">
                    {JSON.stringify(selectedItem.response, null, 2)}
                  </pre>
                </div>
                {selectedItem.error && (
                  <div>
                    <label className="block text-sm font-medium text-red-700">Error</label>
                    <div className="mt-1 text-sm text-red-900 bg-red-50 p-3 rounded">
                      {selectedItem.error}
                    </div>
                  </div>
                )}
              </>
            )}
            
            {activeTab === 'callbacks' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Transaction ID</label>
                    <div className="mt-1 text-sm text-gray-900">{selectedItem.transactionId || 'N/A'}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Số tiền</label>
                    <div className="mt-1 text-sm text-gray-900">{selectedItem.amount} {selectedItem.currency}</div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Callback Payload</label>
                  <pre className="mt-1 text-sm text-gray-900 bg-gray-100 p-3 rounded overflow-x-auto">
                    {JSON.stringify(selectedItem.payload, null, 2)}
                  </pre>
                </div>
                {selectedItem.headers && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Headers</label>
                    <pre className="mt-1 text-sm text-gray-900 bg-gray-100 p-3 rounded overflow-x-auto">
                      {JSON.stringify(selectedItem.headers, null, 2)}
                    </pre>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderPagination = () => {
    const totalPages = Math.ceil(pagination.total / pagination.limit)
    
    return (
      <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
            disabled={pagination.page === 1}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Trước
          </button>
          <button
            onClick={() => setPagination(prev => ({ ...prev, page: Math.min(totalPages, prev.page + 1) }))}
            disabled={pagination.page === totalPages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Sau
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Hiển thị <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> đến{' '}
              <span className="font-medium">
                {Math.min(pagination.page * pagination.limit, pagination.total)}
              </span>{' '}
              trong <span className="font-medium">{pagination.total}</span> kết quả
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                disabled={pagination.page === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                Trước
              </button>
              {[...Array(Math.min(5, totalPages))].map((_, i) => {
                const pageNum = i + 1
                return (
                  <button
                    key={pageNum}
                    onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      pagination.page === pageNum
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                )
              })}
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: Math.min(totalPages, prev.page + 1) }))}
                disabled={pagination.page === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                Sau
              </button>
            </nav>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push(`/${store || 'shopname'}/admin/provider-management`)}
                className="text-gray-600 hover:text-gray-800"
              >
                <FaArrowLeft className="text-xl" />
              </button>
              <h1 className="text-2xl font-bold text-gray-900">Lịch sử APN Call & Callback</h1>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={loadData}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
              >
                <FaSyncAlt /> <span>Làm mới</span>
              </button>
              <button
                onClick={exportData}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
              >
                <FaDownload /> <span>Xuất dữ liệu</span>
              </button>
            </div>
          </div>
          
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{statistics.total || 0}</div>
              <div className="text-sm text-gray-600">Tổng số {activeTab === 'calls' ? 'cuộc gọi' : 'callback'}</div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{statistics.successful || 0}</div>
              <div className="text-sm text-gray-600">Thành công</div>
            </div>
            <div className="bg-red-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-red-600">{statistics.failed || 0}</div>
              <div className="text-sm text-gray-600">Thất bại</div>
            </div>
            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-yellow-600">
                {statistics.total > 0 ? Math.round(((statistics.successful || 0) / statistics.total) * 100) : 0}%
              </div>
              <div className="text-sm text-gray-600">Tỷ lệ thành công</div>
            </div>
          </div>
          
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('calls')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'calls'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Cuộc gọi APN (Outgoing)
              </button>
              <button
                onClick={() => setActiveTab('callbacks')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'callbacks'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Callback APN (Incoming)
              </button>
            </nav>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FaFilter className="mr-2" /> Bộ lọc
            </h2>
            <button
              onClick={clearFilters}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Xóa bộ lọc
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {activeTab === 'calls' ? 'Nhà cung cấp' : 'Phương thức'}
              </label>
              <select
                value={filters.provider}
                onChange={(e) => handleFilterChange('provider', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="">Tất cả</option>
                <option value="7-eleven">7-Eleven</option>
                <option value="familymart">FamilyMart</option>
                <option value="sinopac">Sinopac Bank</option>
                <option value="if-card">IF Card</option>
                <option value="ok-card">OK Card</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="">Tất cả</option>
                {activeTab === 'calls' ? (
                  <>
                    <option value="success">Thành công</option>
                    <option value="failed">Thất bại</option>
                  </>
                ) : (
                  <>
                    <option value="success">Thành công</option>
                    <option value="failed">Thất bại</option>
                    <option value="pending">Chờ xử lý</option>
                    <option value="processing">Đang xử lý</option>
                  </>
                )}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Từ ngày</label>
              <input
                type="datetime-local"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Đến ngày</label>
              <input
                type="datetime-local"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
              <input
                type="text"
                value={filters.orderId}
                onChange={(e) => handleFilterChange('orderId', e.target.value)}
                placeholder="Nhập Order ID"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Transaction ID</label>
              <input
                type="text"
                value={filters.transactionId}
                onChange={(e) => handleFilterChange('transactionId', e.target.value)}
                placeholder="Nhập Transaction ID"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
          </div>
        </div>

        {/* Data Table */}
        <div className="bg-white rounded-lg shadow">
          {loading ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <div className="mt-2 text-gray-600">Đang tải dữ liệu...</div>
            </div>
          ) : (
            <>
              {activeTab === 'calls' ? renderCallsTable() : renderCallbacksTable()}
              {renderPagination()}
            </>
          )}
        </div>
      </div>
      
      {renderDetailsModal()}
    </div>
  )
}