import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

const AnalyticsPage = () => {
  const router = useRouter();
  const { store } = router.query;
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('7days');
  const [analyticsData, setAnalyticsData] = useState(null);
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  useEffect(() => {
    const fetchRealAnalyticsData = async () => {
      try {
        setLoading(true);
        
        // Fetch real orders data
        const ordersResponse = await fetch('/api/admin/orders');
        const ordersData = await ordersResponse.json();
        
        // Fetch real customers data
        const customersResponse = await fetch('/api/customers');
        const customersData = await customersResponse.json();
        
        // Fetch payment statistics
        const paymentStatsResponse = await fetch('/api/admin/payment-stats');
        const paymentStatsData = await paymentStatsResponse.json();
        
        if (ordersData.success && customersData.success) {
          const orders = ordersData.orders || [];
          const customers = customersData.customers || [];
          const paymentStats = paymentStatsData.success ? paymentStatsData.stats : null;
          
          // Calculate analytics from real data
          const analyticsData = calculateAnalyticsFromRealData(orders, customers, paymentStats);
          setAnalyticsData(analyticsData);
        } else {
          console.error('Failed to fetch data:', { ordersData, customersData });
          // Fallback to empty data structure
          setAnalyticsData(getEmptyAnalyticsData());
        }
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        // Fallback to empty data structure
        setAnalyticsData(getEmptyAnalyticsData());
      } finally {
        setLoading(false);
      }
    };
    
    fetchRealAnalyticsData();
  }, [dateRange]);

  // Helper function to calculate analytics from real data
  const calculateAnalyticsFromRealData = (orders, customers, paymentStats) => {
    const now = new Date();
    const dateRangeMs = {
      '7days': 7 * 24 * 60 * 60 * 1000,
      '30days': 30 * 24 * 60 * 60 * 1000,
      '90days': 90 * 24 * 60 * 60 * 1000
    };
    
    const rangeMs = dateRangeMs[dateRange] || dateRangeMs['7days'];
    const startDate = new Date(now.getTime() - rangeMs);
    
    // Filter orders by date range
    const filteredOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= startDate;
    });
    
    // Calculate overview metrics
    const paidOrders = filteredOrders.filter(order => order.paymentStatus === 'paid');
    const totalRevenue = paidOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
    const totalOrders = filteredOrders.length;
    const totalCustomers = new Set(filteredOrders.map(order => order.customerId)).size;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Calculate daily revenue chart
    const revenueChart = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const dayOrders = paidOrders.filter(order => {
        const orderDate = new Date(order.createdAt).toISOString().split('T')[0];
        return orderDate === dateStr;
      });
      
      revenueChart.push({
        date: dateStr,
        revenue: dayOrders.reduce((sum, order) => sum + (order.totalAmount || 0), 0),
        orders: dayOrders.length
      });
    }
    
    // Calculate top products
    const productSales = {};
    paidOrders.forEach(order => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach(item => {
          const key = item.name || item.sku || 'Unknown Product';
          if (!productSales[key]) {
            productSales[key] = { name: key, sales: 0, revenue: 0 };
          }
          productSales[key].sales += item.quantity || 1;
          productSales[key].revenue += (item.price || 0) * (item.quantity || 1);
        });
      }
    });
    
    const topProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
    
    // Calculate payment methods from paymentStats or orders
    let paymentMethods = [];
    if (paymentStats && paymentStats.channels) {
      const totalAmount = paymentStats.channels.reduce((sum, channel) => sum + channel.totalAmount, 0);
      paymentMethods = paymentStats.channels.map(channel => ({
        method: channel.displayName,
        percentage: totalAmount > 0 ? Math.round((channel.totalAmount / totalAmount) * 100) : 0,
        amount: channel.totalAmount
      }));
    } else {
      // Fallback: calculate from orders
      const methodStats = {};
      paidOrders.forEach(order => {
        const method = order.paymentMethod || 'Unknown';
        if (!methodStats[method]) {
          methodStats[method] = { amount: 0, count: 0 };
        }
        methodStats[method].amount += order.totalAmount || 0;
        methodStats[method].count += 1;
      });
      
      const totalAmount = Object.values(methodStats).reduce((sum, stat) => sum + stat.amount, 0);
      paymentMethods = Object.entries(methodStats).map(([method, stat]) => ({
        method,
        percentage: totalAmount > 0 ? Math.round((stat.amount / totalAmount) * 100) : 0,
        amount: stat.amount
      }));
    }
    
    // Calculate customer segments
    const customerOrderCounts = {};
    filteredOrders.forEach(order => {
      const customerId = order.customerId;
      if (!customerOrderCounts[customerId]) {
        customerOrderCounts[customerId] = { count: 0, revenue: 0 };
      }
      customerOrderCounts[customerId].count += 1;
      if (order.paymentStatus === 'paid') {
        customerOrderCounts[customerId].revenue += order.totalAmount || 0;
      }
    });
    
    const customerSegments = [
      { segment: 'VIP', count: 0, percentage: 0, revenue: 0 },
      { segment: 'Thường xuyên', count: 0, percentage: 0, revenue: 0 },
      { segment: 'Mới', count: 0, percentage: 0, revenue: 0 }
    ];
    
    Object.values(customerOrderCounts).forEach(customer => {
      if (customer.count >= 10) {
        customerSegments[0].count += 1;
        customerSegments[0].revenue += customer.revenue;
      } else if (customer.count >= 3) {
        customerSegments[1].count += 1;
        customerSegments[1].revenue += customer.revenue;
      } else {
        customerSegments[2].count += 1;
        customerSegments[2].revenue += customer.revenue;
      }
    });
    
    const totalCustomersInSegments = customerSegments.reduce((sum, seg) => sum + seg.count, 0);
    customerSegments.forEach(segment => {
      segment.percentage = totalCustomersInSegments > 0 ? Math.round((segment.count / totalCustomersInSegments) * 100) : 0;
    });
    
    // Calculate hourly activity
    const hourlyActivity = Array.from({ length: 24 }, (_, hour) => {
      const hourStr = hour.toString().padStart(2, '0') + ':00';
      const hourOrders = filteredOrders.filter(order => {
        const orderHour = new Date(order.createdAt).getHours();
        return orderHour === hour;
      });
      return { hour: hourStr, orders: hourOrders.length };
    });
    
    return {
      overview: {
        totalRevenue,
        revenueGrowth: 0, // Would need historical data to calculate
        totalOrders,
        ordersGrowth: 0, // Would need historical data to calculate
        totalCustomers,
        customersGrowth: 0, // Would need historical data to calculate
        avgOrderValue,
        avgOrderGrowth: 0 // Would need historical data to calculate
      },
      revenueChart,
      topProducts,
      paymentMethods,
      customerSegments,
      hourlyActivity
    };
  };
  
  // Helper function to provide empty data structure
  const getEmptyAnalyticsData = () => {
    return {
      overview: {
        totalRevenue: 0,
        revenueGrowth: 0,
        totalOrders: 0,
        ordersGrowth: 0,
        totalCustomers: 0,
        customersGrowth: 0,
        avgOrderValue: 0,
        avgOrderGrowth: 0
      },
      revenueChart: [],
      topProducts: [],
      paymentMethods: [],
      customerSegments: [],
      hourlyActivity: []
    };
  };

  const formatCurrency = (amount) => {
    return amount.toLocaleString('vi-VN') + 'đ';
  };

  const exportReport = () => {
    alert('Đang xuất báo cáo...');
  };

  const refreshData = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  if (loading || !analyticsData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Đang tải dữ liệu phân tích...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin`)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            ← Quay lại trang chủ Admin
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                📊 Phân tích & Báo cáo
              </h1>
              <p className="text-gray-600 mt-2">Theo dõi hiệu suất kinh doanh và xu hướng từ dữ liệu thực</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="relative">
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="pl-4 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="7days">7 ngày qua</option>
                  <option value="30days">30 ngày qua</option>
                  <option value="90days">90 ngày qua</option>
                </select>
              </div>
              
              <button
                onClick={refreshData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                🔄 Làm mới
              </button>
              
              <button
                onClick={exportReport}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                📊 Xuất báo cáo
              </button>
            </div>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng doanh thu</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.overview.totalRevenue)}</p>
                <p className="text-xs text-green-600 mt-1">+{analyticsData.overview.revenueGrowth}% so với kỳ trước</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                💰
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng đơn hàng</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalOrders.toLocaleString()}</p>
                <p className="text-xs text-green-600 mt-1">+{analyticsData.overview.ordersGrowth}% so với kỳ trước</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                📦
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Khách hàng</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.overview.totalCustomers.toLocaleString()}</p>
                <p className="text-xs text-green-600 mt-1">+{analyticsData.overview.customersGrowth}% so với kỳ trước</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                👥
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Giá trị đơn hàng TB</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.overview.avgOrderValue)}</p>
                <p className="text-xs text-green-600 mt-1">+{analyticsData.overview.avgOrderGrowth}% so với kỳ trước</p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                📈
              </div>
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Revenue Chart */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Doanh thu theo ngày</h3>
            <div className="space-y-3">
              {analyticsData.revenueChart.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{new Date(item.date).toLocaleDateString('vi-VN')}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${Math.min(100, (item.revenue / Math.max(...analyticsData.revenueChart.map(d => d.revenue))) * 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-20 text-right">{formatCurrency(item.revenue)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Hourly Activity */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Hoạt động theo giờ</h3>
            <div className="grid grid-cols-6 gap-2">
              {analyticsData.hourlyActivity.map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-xs text-gray-500 mb-1">{item.hour}</div>
                  <div 
                    className="bg-blue-100 rounded" 
                    style={{ height: `${Math.max(4, (item.orders / Math.max(...analyticsData.hourlyActivity.map(h => h.orders))) * 40)}px` }}
                  ></div>
                  <div className="text-xs text-gray-700 mt-1">{item.orders}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Detailed Reports */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Top Products */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Sản phẩm bán chạy</h3>
            <div className="space-y-3">
              {analyticsData.topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{product.name}</p>
                    <p className="text-xs text-gray-500">{product.sales} đã bán</p>
                  </div>
                  <p className="text-sm font-semibold text-gray-900">{formatCurrency(product.revenue)}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Payment Methods */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Phương thức thanh toán</h3>
            <div className="space-y-3">
              {analyticsData.paymentMethods.map((method, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{method.method}</p>
                    <p className="text-xs text-gray-500">{method.percentage}%</p>
                  </div>
                  <p className="text-sm font-semibold text-gray-900">{formatCurrency(method.amount)}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Customer Segments */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Phân khúc khách hàng</h3>
            <div className="space-y-3">
              {analyticsData.customerSegments.map((segment, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{segment.segment}</p>
                    <p className="text-xs text-gray-500">{segment.count} khách hàng ({segment.percentage}%)</p>
                  </div>
                  <p className="text-sm font-semibold text-gray-900">{formatCurrency(segment.revenue)}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;