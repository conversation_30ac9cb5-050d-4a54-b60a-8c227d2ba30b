import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
import {
  FiHome,
  FiUsers,
  FiShoppingCart,
  FiCreditCard,
  FiBarChart,
  FiFileText,
  FiSettings,
  FiDollarSign,
  FiTrendingUp,
  FiActivity,
  FiPackage,
  FiHeart,
  FiPhone,
  FiWifi,
  FiClock,
  FiEye,
  FiLogOut
} from 'react-icons/fi';
import { useAdminAuth } from '../../context/adminAuthContext';
import AdminLayout from '../../components/admin/AdminLayout';

const AdminDashboardContent = () => {
  const router = useRouter();
  const { user, logout } = useAdminAuth();
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalRevenueByCurrency: {},
    totalCustomers: 0,
    pendingOrders: 0,
    recentOrders: [],
    topProducts: [],
    paymentChannels: 0
  });
  const [loading, setLoading] = useState(true);

  const handleLogout = async () => {
    await logout();
  };

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      
      // Fetch orders
      const ordersResponse = await fetch('/api/admin/orders');
      const ordersData = await ordersResponse.json();
      
      // Fetch customers
      const customersResponse = await fetch('/api/customers');
      const customersData = await customersResponse.json();
      
      if (ordersData.success) {
        const orders = ordersData.orders || [];
        const customers = customersData.customers || [];

        // Calculate revenue by currency - NO FALLBACKS
        const totalRevenueByCurrency = {};
        let paidOrdersCount = 0;
        let validRevenueOrdersCount = 0;

        orders.forEach(order => {
          // Only count paid orders
          if (order.paymentStatus === 'paid' || order.status === 'completed') {
            paidOrdersCount++;
            const amount = order.totalAmount || order.amount || 0;
            const currency = order.currency;

            console.log(`Processing paid order ${order.id}: currency=${currency}, amount=${amount}`);

            if (currency && amount > 0) {
              validRevenueOrdersCount++;
              if (!totalRevenueByCurrency[currency]) {
                totalRevenueByCurrency[currency] = 0;
              }
              totalRevenueByCurrency[currency] += amount;
            }
          }
        });

        console.log(`Dashboard stats: ${paidOrdersCount} paid orders, ${validRevenueOrdersCount} with valid revenue data`);
        console.log('Revenue by currency:', totalRevenueByCurrency);

        const pendingOrders = orders.filter(order => order.status === 'pending' || order.status === 'not_yet_paid').length;
        const recentOrders = orders.slice(0, 5);

        console.log('Dashboard revenue by currency:', totalRevenueByCurrency);

        setStats(prevStats => ({
          ...prevStats,
          totalOrders: orders.length,
          totalRevenueByCurrency: totalRevenueByCurrency || {},
          totalCustomers: customers.length,
          pendingOrders,
          recentOrders,
          topProducts: [],
          paymentChannels: 8 // Mock data
        }));
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Multi-currency formatting function - NO FALLBACKS
  const formatCurrency = (amount, currency) => {
    if (!currency) {
      return `${amount} (KHÔNG RÕ LOẠI TIỀN)`;
    }

    switch (currency) {
      case 'VND':
        return `${amount.toLocaleString('vi-VN')}đ`;
      case 'NT$':
      case 'NT':
      case 'NTD':
      case 'TWD':
        return `NT$ ${amount.toLocaleString()}`;
      case 'USD':
      case '$':
        return `$${amount.toFixed(2)}`;
      default:
        return `${currency} ${amount}`;
    }
  };

  // Format revenue display for multiple currencies
  const formatRevenueDisplay = (revenueByCurrency) => {
    if (!revenueByCurrency || typeof revenueByCurrency !== 'object') {
      return 'Chưa có doanh thu';
    }

    const currencies = Object.keys(revenueByCurrency);
    if (currencies.length === 0) {
      return 'Chưa có doanh thu';
    }

    if (currencies.length === 1) {
      const currency = currencies[0];
      return formatCurrency(revenueByCurrency[currency], currency);
    }

    // Multiple currencies - show all
    return currencies.map(currency =>
      formatCurrency(revenueByCurrency[currency], currency)
    ).join(' + ');
  };

  const adminModules = [
    {
      title: 'Quản Lý Đơn Hàng',
      description: 'Xem và quản lý tất cả đơn hàng',
      icon: <FiShoppingCart className="text-2xl" />,
      href: '/admin/orders',
      color: 'bg-green-500',
      stats: `${stats.totalOrders} đơn hàng`
    },
    {
      title: 'Quản Lý Khách Hàng',
      description: 'Quản lý tài khoản và dữ liệu khách hàng',
      icon: <FiUsers className="text-2xl" />,
      href: '/admin/customers',
      color: 'bg-purple-500',
      stats: `${stats.totalCustomers} khách hàng`
    },
    {
      title: 'Nạp thẻ thủ công',
      description: 'Xuất kho mã thẻ thủ công cho khách hàng',
      icon: <FiFileText className="text-2xl" />,
      href: '/admin/google-sheets',
      color: 'bg-emerald-600',
      stats: 'Sheets'
    },
    {
      title: 'Quản Lý Tài Liệu',
      description: 'Xem xét và phê duyệt tài liệu khách hàng',
      icon: <FiFileText className="text-2xl" />,
      href: '/admin/document-management',
      color: 'bg-red-500',
      stats: 'Tài liệu'
    },
    {
      title: 'Chính sách Điểm thưởng',
      description: 'Cấu hình quy tắc và phần thưởng khách hàng thân thiết',
      icon: <FiActivity className="text-2xl" />,
      href: '/admin/loyalty-management',
      color: 'bg-cyan-500',
      stats: 'Quy tắc'
    },
    {
      title: 'Phân Tích & Báo Cáo',
      description: 'Phân tích doanh thu, đơn hàng và khách hàng',
      icon: <FiBarChart className="text-2xl" />,
      href: '/admin/analytics',
      color: 'bg-blue-500',
      stats: formatRevenueDisplay(stats.totalRevenueByCurrency)
    },
    /* {
      title: 'Kênh Thanh Toán',
      description: 'Cấu hình phương thức thanh toán và giám sát',
      icon: <FiCreditCard className="text-2xl" />,
      href: '/admin/payment-channels',
      color: 'bg-indigo-500',
      stats: `${stats.paymentChannels} kênh`
    }, */

    /* {
      title: 'Quản Lý Nạp Tiền OK',
      description: 'Quản lý giao dịch nạp tiền di động OK',
      icon: <FiWifi className="text-2xl" />,
      href: '/admin/topup-ok',
      color: 'bg-teal-500',
      stats: 'Nhà cung cấp OK'
    }, */
    {
      title: 'API',
      description: 'Ngân hàng, OK, IF dịch vụ và tích hợp',
      icon: <FiPackage className="text-2xl" />,
      href: '/admin/provider-management',
      color: 'bg-yellow-500',
      stats: 'Nhà cung cấp'
    },
    /* {
      title: 'Báo Cáo Thanh Toán',
      description: 'Báo cáo chi tiết thanh toán và giao dịch',
      icon: <FiDollarSign className="text-2xl" />,
      href: '/admin/payment-reports',
      color: 'bg-emerald-500',
      stats: 'Báo cáo'
    }, */
    {
      title: 'APN',
      description: 'Xem nhật ký callback APN và lịch sử',
      icon: <FiClock className="text-2xl" />,
      href: '/admin/apn-history',
      color: 'bg-slate-500',
      stats: 'Callbacks'
    }
  ];

  const StatCard = ({ title, value, icon, color, change }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          {change && (
            <div className="flex items-center mt-2 text-sm text-green-600">
              <FiTrendingUp className="mr-1" />
              {change}
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <div className="text-white text-xl">
            {icon}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <Head>
        <title>Bảng Điều Khiển Quản Trị - MAG Shop</title>
        <meta name="description" content="Bảng điều khiển quản trị cho MAG Shop" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <FiHome className="text-2xl text-blue-600 mr-3" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Bảng Điều Khiển Quản Trị</h1>
                  <p className="text-sm text-gray-600">Quản lý nền tảng thương mại điện tử của bạn</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm text-gray-600">Tổng Doanh Thu</div>
                  <div className="text-lg font-bold text-blue-600">
                    {formatRevenueDisplay(stats.totalRevenueByCurrency)}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">Chào mừng, {user?.username}</div>
                    <div className="text-xs text-gray-500">Quản trị viên</div>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="flex items-center px-3 py-2 text-sm text-gray-700 hover:text-red-600 hover:bg-gray-100 rounded-md transition-colors"
                    title="Đăng xuất"
                  >
                    <FiLogOut className="h-4 w-4 mr-1" />
                    Đăng xuất
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Tổng Đơn Hàng"
            value={stats.totalOrders.toLocaleString()}
            icon={<FiShoppingCart />}
            color="bg-blue-500"
            /* change="+12.5%" */
          />
          {/* Custom Revenue Card with Multiple Currencies */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Tổng Doanh Thu</p>
                <div className="mt-2">
                  {Object.keys(stats.totalRevenueByCurrency || {}).length === 0 ? (
                    <p className="text-2xl font-bold text-gray-900">Chưa có doanh thu</p>
                  ) : (
                    Object.entries(stats.totalRevenueByCurrency || {}).map(([currency, amount]) => (
                      <p key={currency} className="text-lg font-bold text-gray-900 mb-1">
                        {formatCurrency(amount, currency)}
                      </p>
                    ))
                  )}
                </div>
                {/* Debug info */}
                {/* <div className="text-xs text-gray-500 mt-1">
                  Debug: {JSON.stringify(stats.totalRevenueByCurrency)}
                </div> */}
              </div>
              <div className={`h-12 w-12 bg-green-500 rounded-lg flex items-center justify-center text-white`}>
                <FiDollarSign />
              </div>
            </div>
          </div>
          <StatCard
            title="Tổng Khách Hàng"
            value={stats.totalCustomers.toLocaleString()}
            icon={<FiUsers />}
            color="bg-purple-500"
            /* change="+15.3%" */
          />
          <StatCard
            title="Đơn Hàng Chờ Xử Lý"
            value={stats.pendingOrders.toLocaleString()}
            icon={<FiClock />}
            color="bg-orange-500"
          />
        </div>

        {/* Admin Modules Grid */}
        <div className="mb-8">
          {/* <h2 className="text-2xl font-bold text-gray-900 mb-6">Admin Modules</h2> */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {adminModules.map((module, index) => (
              <Link key={index} href={module.href}>
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer group">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-full ${module.color} group-hover:scale-110 transition-transform`}>
                      <div className="text-white">
                        {module.icon}
                      </div>
                    </div>
                    <FiEye className="text-gray-400 group-hover:text-gray-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{module.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{module.description}</p>
                  <div className="text-sm font-medium text-blue-600">{module.stats}</div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Orders */}
        {stats.recentOrders.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Đơn Hàng Gần Đây</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã Đơn Hàng</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Khách Hàng</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số Tiền</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng Thái</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {stats.recentOrders.map((order, index) => (
                    <tr key={`order-${order.id || index}-${order.timestamp || Date.now()}`} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        <Link href={`/admin/orders/${order.id}`}>
                          {order.id}
                        </Link>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.customerName || order.customerId}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(order.totalAmount || order.amount || order.total || 0, order.currency)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          order.status === 'paid' ? 'bg-green-100 text-green-800' :
                          order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(order.createdAt || order.timestamp).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  );
};

// Main component with authentication wrapper
const AdminDashboard = () => {
  return (
    <AdminLayout>
      <AdminDashboardContent />
    </AdminLayout>
  );
};

export default AdminDashboard;
