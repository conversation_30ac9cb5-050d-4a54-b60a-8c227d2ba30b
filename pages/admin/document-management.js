import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaFileAlt, FaSearch, FaFilter, FaEye, FaDownload, FaTrash, FaUpload, FaCheck, FaTimes, FaSpinner, FaClock, FaExclamationTriangle, FaPlus, FaEdit, FaChartPie, FaHistory, FaCalendarAlt } from 'react-icons/fa';
import DocumentUploadModal from '../../components/admin/DocumentUploadModal';
import { DOCUMENT_TYPES } from '../../components/admin/DocumentApproval';
import { 
  DOCUMENT_REQUIREMENTS, 
  DOCUMENT_CATEGORIES, 
  DOCUMENT_STATUS,
  getRequiredDocumentsForCustomer,
  getDocumentComplianceStatus,
  getDocumentHistory
} from '../../config/documentRequirements';

const DocumentManagement = () => {
  const router = useRouter();
  const { store } = router.query;
  const [loading, setLoading] = useState(true);
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [customerDocuments, setCustomerDocuments] = useState({});
  const [documentLoading, setDocumentLoading] = useState(false);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [uploadCustomer, setUploadCustomer] = useState(null);
  const [documentToEdit, setDocumentToEdit] = useState(null);
  const [showRequirements, setShowRequirements] = useState(false);
  const [showDocumentHistory, setShowDocumentHistory] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState(null);
  const [customerCompliance, setCustomerCompliance] = useState({});

  useEffect(() => {
    fetchCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm, filterStatus]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/customers');
      const data = await response.json();
      
      if (data.success) {
        const customersWithStatus = data.customers.map(customer => {
          const compliance = getDocumentComplianceStatus(customer);
          return {
            ...customer,
            status: customer.accessControl?.accountStatus || 'active',
            documentCount: customer.documents?.length || 0,
            lastDocumentUpdate: customer.documents?.length > 0 
              ? Math.max(...customer.documents.map(doc => new Date(doc.uploadDate).getTime()))
              : null,
            compliance
          };
        });
        setCustomers(customersWithStatus);
        
        // Calculate compliance for all customers
        const complianceMap = {};
        customersWithStatus.forEach(customer => {
          complianceMap[customer.id] = customer.compliance;
        });
        setCustomerCompliance(complianceMap);
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(customer => {
        const name = customer.personalDetails?.name || '';
        const email = customer.contactInfo?.email || '';
        const phone = customer.contactInfo?.primaryPhone || customer.id || '';
        return (
          name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          phone.includes(searchTerm)
        );
      });
    }

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(customer => customer.status === filterStatus);
    }

    setFilteredCustomers(filtered);
  };

  const fetchCustomerDocuments = async (customerId) => {
    try {
      setDocumentLoading(true);
      const response = await fetch(`/api/customer/documents?customerId=${customerId}`);
      const data = await response.json();
      
      if (data.success) {
        setCustomerDocuments(prev => ({
          ...prev,
          [customerId]: data.documentsByType || {}
        }));
      }
    } catch (error) {
      console.error('Error fetching customer documents:', error);
    } finally {
      setDocumentLoading(false);
    }
  };

  const handleCustomerSelect = async (customer) => {
    setSelectedCustomer(customer);
    setShowCustomerModal(true);
    
    // Fetch documents for this customer if not already loaded
    if (!customerDocuments[customer.id]) {
      await fetchCustomerDocuments(customer.id);
    }
  };

  const handleUploadDocument = (customer, documentType = null) => {
    setUploadCustomer(customer);
    if (documentType) {
      setDocumentToEdit({ documentType });
    } else {
      setDocumentToEdit(null);
    }
    setIsUploadModalOpen(true);
  };

  const handleEditDocument = (customer, document) => {
    setUploadCustomer(customer);
    setDocumentToEdit(document);
    setIsUploadModalOpen(true);
  };

  const handleDocumentUploaded = async (newDocument) => {
    setIsUploadModalOpen(false);
    setUploadCustomer(null);
    setDocumentToEdit(null);
    
    // Refresh customer documents if viewing the same customer
    if (selectedCustomer && selectedCustomer.id === newDocument.customerId) {
      await handleCustomerSelect(selectedCustomer);
    }
    
    // Refresh customers list to update document counts
    await fetchCustomers();
  };

  const handleShowRequirements = (customer) => {
    setSelectedCustomer(customer);
    setShowRequirements(true);
  };
  
  const handleShowDocumentHistory = (customer, documentType) => {
    setSelectedCustomer(customer);
    setSelectedDocumentType(documentType);
    setShowDocumentHistory(true);
  };
  
  const getComplianceColor = (percentage) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    if (percentage >= 50) return 'text-orange-600';
    return 'text-red-600';
  };
  
  const getComplianceBadgeColor = (percentage) => {
    if (percentage >= 90) return 'bg-green-100 text-green-800';
    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800';
    if (percentage >= 50) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const handleDocumentAction = async (document, action) => {
    try {
      const response = await fetch('/api/documents/action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: document.id,
          customerId: document.customerId,
          action: action, // 'approve', 'reject', 'delete'
          documentType: document.documentType,
          filename: document.filename
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        // Refresh customer documents
         if (selectedCustomer) {
           await handleCustomerSelect(selectedCustomer);
         }
        // Refresh customers list
        await fetchCustomers();
      } else {
        alert(`Error: ${result.message}`);
      }
    } catch (error) {
      console.error('Error performing document action:', error);
      alert('An error occurred while processing the document action.');
    }
  };

  const getDocumentCount = (customerId) => {
    const docs = customerDocuments[customerId];
    if (!docs) return 0;
    
    return Object.values(docs).reduce((total, docArray) => {
      return total + (Array.isArray(docArray) ? docArray.length : 0);
    }, 0);
  };

  const getDocumentStatus = (customerId) => {
    const docs = customerDocuments[customerId];
    if (!docs) return 'no-documents';
    
    let hasApproved = false;
    let hasPending = false;
    let hasRejected = false;
    
    Object.values(docs).forEach(docArray => {
      if (Array.isArray(docArray)) {
        docArray.forEach(doc => {
          if (doc.status === 'approved') hasApproved = true;
          else if (doc.status === 'pending_approval') hasPending = true;
          else if (doc.status === 'rejected') hasRejected = true;
        });
      }
    });
    
    if (hasRejected) return 'rejected';
    if (hasPending) return 'pending';
    if (hasApproved) return 'approved';
    return 'no-documents';
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'active': { bg: 'bg-green-100', text: 'text-green-800', label: 'Hoạt động' },
      'inactive': { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Không hoạt động' },
      'suspended': { bg: 'bg-red-100', text: 'text-red-800', label: 'Tạm khóa' },
      'pending': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Chờ duyệt' }
    };
    
    const config = statusConfig[status] || statusConfig['active'];
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const getDocumentStatusBadge = (status) => {
    const statusConfig = {
      'approved': { bg: 'bg-green-100', text: 'text-green-800', label: 'Đã duyệt' },
      'pending': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Chờ duyệt' },
      'rejected': { bg: 'bg-red-100', text: 'text-red-800', label: 'Từ chối' },
      'no-documents': { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Chưa có tài liệu' }
    };
    
    const config = statusConfig[status] || statusConfig['no-documents'];
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getDocumentTypeLabel = (type) => {
    const labels = {
      'idCard': 'CMND/CCCD',
      'photo': 'Ảnh chân dung',
      'proofOfResidence': 'Giấy tờ cư trú',
      'taxRegistration': 'Giấy đăng ký thuế',
      'businessLicense': 'Giấy phép kinh doanh',
      'other': 'Khác'
    };
    return labels[type] || type;
  };

  const renderDocumentStatus = (doc) => {
    const status = doc.status || 'missing';
    const statusConfig = {
      'approved': { bg: 'bg-green-100', text: 'text-green-800', label: 'Đã duyệt', icon: FaCheck },
      'pending': { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Chờ duyệt', icon: FaClock },
      'rejected': { bg: 'bg-red-100', text: 'text-red-800', label: 'Từ chối', icon: FaTimes },
      'missing': { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Chưa có', icon: FaExclamationTriangle }
    };
    
    const config = statusConfig[status] || statusConfig['missing'];
    const IconComponent = config.icon;
    
    return (
      <div className="flex items-center space-x-2">
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.bg} ${config.text} flex items-center`}>
          <IconComponent className="mr-1" size={12} />
          {config.label}
        </span>
      </div>
    );
  };

  const renderDocumentActions = (doc, category) => {
    return (
      <>
        <button
          onClick={() => handleDocumentUpload(doc.type)}
          className="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 rounded border border-blue-200 hover:bg-blue-50 flex items-center"
        >
          <FaUpload className="mr-1" size={10} />
          Tải lên
        </button>
        <button
          onClick={() => handleDocumentHistory(doc.type)}
          className="text-gray-600 hover:text-gray-900 text-xs px-2 py-1 rounded border border-gray-200 hover:bg-gray-50 flex items-center"
        >
          <FaHistory className="mr-1" size={10} />
          Lịch sử
        </button>
      </>
    );
  };

  const handleDocumentUpload = (docType) => {
    // Handle document upload logic
    console.log('Upload document:', docType);
  };

  const handleDocumentHistory = (docType) => {
    // Handle document history logic
    console.log('View document history:', docType);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Đang tải dữ liệu khách hàng...</p>
        </div>
      </div>
    );
  }

  // Document Requirements Modal Component
  const DocumentRequirementsModal = ({ customer, isOpen, onClose, onUploadDocument, onShowHistory }) => {
    if (!isOpen) return null;
    
    const requirements = getRequiredDocumentsForCustomer(customer);
    const customerDocs = customer.documents || [];
    
    const getDocumentStatus = (docType) => {
      const doc = customerDocs.find(d => d.documentType === docType);
      if (!doc) return 'missing';
      
      const requirement = DOCUMENT_REQUIREMENTS[docType];
      if (requirement.expirationTracking && requirement.validityPeriod) {
        const uploadDate = new Date(doc.uploadDate);
        const expirationDate = new Date(uploadDate.getTime() + (requirement.validityPeriod * 24 * 60 * 60 * 1000));
        if (expirationDate < new Date()) return 'expired';
      }
      
      return doc.status;
    };
    
    const renderDocumentRow = (doc, category) => {
      const status = getDocumentStatus(doc.type);
      const statusInfo = DOCUMENT_STATUS[status];
      const hasDocument = customerDocs.find(d => d.documentType === doc.type);
      
      return (
        <tr key={doc.type} className="border-b border-gray-200">
          <td className="py-3 px-4">
            <div>
              <div className="font-medium text-gray-900">{doc.label}</div>
              <div className="text-sm text-gray-500">{doc.description}</div>
            </div>
          </td>
          <td className="py-3 px-4">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium`}
                  style={{ backgroundColor: statusInfo.color + '20', color: statusInfo.color }}>
              {statusInfo.label}
            </span>
          </td>
          <td className="py-3 px-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onUploadDocument(customer, doc.type)}
                className="text-blue-600 hover:text-blue-900 text-sm"
              >
                <FaUpload className="inline mr-1" />
                {hasDocument ? 'Thay thế' : 'Tải lên'}
              </button>
              {hasDocument && (
                <button
                  onClick={() => onShowHistory(customer, doc.type)}
                  className="text-gray-600 hover:text-gray-900 text-sm"
                >
                  <FaHistory className="inline mr-1" />
                  Lịch sử
                </button>
              )}
            </div>
          </td>
        </tr>
      );
    };
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
        <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col">
          <div className="flex justify-between items-center p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              Yêu cầu tài liệu - {customer.name}
            </h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <FaTimes size={20} />
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-6">
            {/* Compliance Summary */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">Tổng quan tuân thủ</h4>
                <span className={`text-lg font-bold ${getComplianceColor(customer.compliance?.percentage || 0)}`}>
                  {customer.compliance?.percentage || 0}%
                </span>
              </div>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{customer.compliance?.completed || 0}</div>
                  <div className="text-gray-600">Hoàn thành</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{customer.compliance?.pending || 0}</div>
                  <div className="text-gray-600">Chờ duyệt</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{customer.compliance?.missing || 0}</div>
                  <div className="text-gray-600">Thiếu</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{customer.compliance?.expired || 0}</div>
                  <div className="text-gray-600">Hết hạn</div>
                </div>
              </div>
            </div>
            
            {/* Required Documents */}
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                Tài liệu bắt buộc
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {requirements.required.map(doc => (
                  <div key={doc.type} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">{doc.name}</span>
                      <span className="text-xs px-2 py-1 bg-red-100 text-red-800 rounded-full">Bắt buộc</span>
                    </div>
                    <div className="mb-3">
                      {renderDocumentStatus(doc)}
                    </div>
                    <div className="flex items-center space-x-2">
                      {renderDocumentActions(doc, 'required')}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Recommended Documents */}
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                Tài liệu khuyến nghị
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {requirements.recommended.map(doc => (
                  <div key={doc.type} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">{doc.name}</span>
                      <span className="text-xs px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full">Khuyến nghị</span>
                    </div>
                    <div className="mb-3">
                      {renderDocumentStatus(doc)}
                    </div>
                    <div className="flex items-center space-x-2">
                      {renderDocumentActions(doc, 'recommended')}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  // Document History Modal Component
  const DocumentHistoryModal = ({ customer, documentType, isOpen, onClose }) => {
    if (!isOpen) return null;
    
    const history = getDocumentHistory(customer, documentType);
    const requirement = DOCUMENT_REQUIREMENTS[documentType];
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
        <div className="bg-white rounded-lg max-w-3xl w-full max-h-[90vh] flex flex-col">
          <div className="flex justify-between items-center p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              Lịch sử tài liệu: {requirement?.label} - {customer.name}
            </h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <FaTimes size={20} />
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-6">
            {history.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FaFileAlt size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Chưa có tài liệu nào được tải lên</p>
              </div>
            ) : (
              <div className="space-y-4">
                {history.map((doc, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900">{doc.originalFilename}</h4>
                        <p className="text-sm text-gray-500">Tải lên: {new Date(doc.uploadDate).toLocaleString('vi-VN')}</p>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium`}
                            style={{ 
                              backgroundColor: DOCUMENT_STATUS[doc.status]?.color + '20', 
                              color: DOCUMENT_STATUS[doc.status]?.color 
                            }}>
                        {DOCUMENT_STATUS[doc.status]?.label}
                      </span>
                    </div>
                    
                    {/* Status History */}
                    <div className="border-t pt-3">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Lịch sử trạng thái:</h5>
                      <div className="space-y-2">
                        {doc.statusHistory?.map((status, statusIndex) => (
                          <div key={statusIndex} className="flex items-center text-sm">
                            <span className={`w-2 h-2 rounded-full mr-2`}
                                  style={{ backgroundColor: DOCUMENT_STATUS[status.status]?.color }}></span>
                            <span className="text-gray-600">
                              {new Date(status.timestamp).toLocaleString('vi-VN')}
                            </span>
                            <span className="mx-2">-</span>
                            <span className="font-medium">{DOCUMENT_STATUS[status.status]?.label}</span>
                            {status.updatedBy && (
                              <span className="text-gray-500 ml-2">bởi {status.updatedBy}</span>
                            )}
                            {status.comment && (
                              <span className="text-gray-600 ml-2">({status.comment})</span>
                            )}
                          </div>
                        )) || (
                          <div className="flex items-center text-sm">
                            <span className={`w-2 h-2 rounded-full mr-2`}
                                  style={{ backgroundColor: DOCUMENT_STATUS[doc.status]?.color }}></span>
                            <span className="text-gray-600">
                              {new Date(doc.statusUpdatedAt || doc.uploadDate).toLocaleString('vi-VN')}
                            </span>
                            <span className="mx-2">-</span>
                            <span className="font-medium">{DOCUMENT_STATUS[doc.status]?.label}</span>
                            {doc.statusUpdatedBy && (
                              <span className="text-gray-500 ml-2">bởi {doc.statusUpdatedBy}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Document Info */}
                    <div className="border-t pt-3 mt-3">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Kích thước:</span>
                          <span className="ml-2">{(doc.size / 1024 / 1024).toFixed(2)} MB</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Định dạng:</span>
                          <span className="ml-2">{doc.filename.split('.').pop().toUpperCase()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin`)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            ← Quay lại trang chủ Admin
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                📋 Quản lý Hồ sơ Khách hàng
              </h1>
              <p className="text-gray-600 mt-2">Quản lý thông tin và tài liệu của khách hàng</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={fetchCustomers}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                🔄 Làm mới
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm khách hàng
              </label>
              <input
                type="text"
                placeholder="Tìm theo tên, email, số điện thoại..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lọc theo trạng thái
              </label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Tất cả</option>
                <option value="active">Hoạt động</option>
                <option value="inactive">Không hoạt động</option>
                <option value="suspended">Tạm khóa</option>
                <option value="pending">Chờ duyệt</option>
              </select>
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tổng khách hàng</p>
                <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                👥
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Hoạt động</p>
                <p className="text-2xl font-bold text-green-600">
                  {customers.filter(c => c.status === 'active').length}
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                ✅
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Chờ duyệt</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {customers.filter(c => c.status === 'pending').length}
                </p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                ⏳
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tạm khóa</p>
                <p className="text-2xl font-bold text-red-600">
                  {customers.filter(c => c.status === 'suspended').length}
                </p>
              </div>
              <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                🚫
              </div>
            </div>
          </div>
        </div>

        {/* Customer List */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Danh sách khách hàng ({filteredCustomers.length})
            </h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredCustomers.map((customer) => (
              <div key={customer.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                {/* Customer Header */}
                <div className="flex items-center mb-3">
                  <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">
                      {(customer.personalDetails?.name || customer.id).charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {customer.personalDetails?.name || 'N/A'}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: {customer.id}
                    </div>
                  </div>
                  <div className="ml-2">
                    {getStatusBadge(customer.status)}
                  </div>
                </div>

                {/* Contact Info */}
                <div className="mb-3 space-y-1">
                  <div className="text-sm text-gray-900">
                    📧 {customer.contactInfo?.email || 'N/A'}
                  </div>
                  <div className="text-sm text-gray-500">
                    📱 {customer.contactInfo?.primaryPhone || customer.id}
                  </div>
                </div>

                {/* Document Info */}
                <div className="mb-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Tài liệu</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-900">
                        {customer.documentCount || 0} tài liệu
                      </span>
                      {getDocumentStatusBadge(customer.documentCount > 0 ? 'approved' : 'no-documents')}
                    </div>
                  </div>
                  
                  {/* Compliance */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Tuân thủ</span>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium ${getComplianceColor(customer.compliance?.percentage || 0)}`}>
                        {customer.compliance?.percentage || 0}%
                      </span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getComplianceBadgeColor(customer.compliance?.percentage || 0)}`}>
                        {customer.compliance?.percentage >= 90 ? 'Hoàn thành' :
                         customer.compliance?.percentage >= 70 ? 'Tốt' :
                         customer.compliance?.percentage >= 50 ? 'Trung bình' : 'Cần cải thiện'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Date */}
                <div className="mb-3 text-xs text-gray-500">
                  📅 Ngày tạo: {formatDate(customer.createdAt)}
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
                  <button
                    onClick={() => handleCustomerSelect(customer)}
                    className="flex-1 text-blue-600 hover:text-blue-900 flex items-center justify-center py-2 px-3 text-sm border border-blue-200 rounded-md hover:bg-blue-50 transition-colors"
                    title="Xem chi tiết"
                  >
                    <FaEye className="mr-1" />
                    Chi tiết
                  </button>
                  <button
                    onClick={() => handleUploadDocument(customer)}
                    className="flex-1 text-green-600 hover:text-green-900 flex items-center justify-center py-2 px-3 text-sm border border-green-200 rounded-md hover:bg-green-50 transition-colors"
                    title="Tải lên tài liệu"
                  >
                    <FaUpload className="mr-1" />
                    Tải lên
                  </button>
                  <button
                    onClick={() => handleShowRequirements(customer)}
                    className="flex-1 text-purple-600 hover:text-purple-900 flex items-center justify-center py-2 px-3 text-sm border border-purple-200 rounded-md hover:bg-purple-50 transition-colors"
                    title="Xem yêu cầu tài liệu"
                  >
                    <FaChartPie className="mr-1" />
                    Yêu cầu
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Document Upload Modal */}
       {isUploadModalOpen && uploadCustomer && (
         <DocumentUploadModal
           isOpen={isUploadModalOpen}
           onClose={() => {
             setIsUploadModalOpen(false);
             setUploadCustomer(null);
             setDocumentToEdit(null);
           }}
           customerId={uploadCustomer.id}
           customerName={uploadCustomer.personalDetails?.name || uploadCustomer.id}
           onUpload={handleDocumentUploaded}
           allowTypeSelection={true}
           documentType={documentToEdit?.documentType}
         />
       )}

        {/* Document Requirements Modal */}
        {showRequirements && selectedCustomer && (
          <DocumentRequirementsModal
            customer={selectedCustomer}
            isOpen={showRequirements}
            onClose={() => {
              setShowRequirements(false);
              setSelectedCustomer(null);
            }}
            onUploadDocument={(customer, docType) => {
              setShowRequirements(false);
              handleUploadDocument(customer, docType);
            }}
            onShowHistory={(customer, docType) => {
              setShowRequirements(false);
              handleShowDocumentHistory(customer, docType);
            }}
          />
        )}
        
        {/* Document History Modal */}
        {showDocumentHistory && selectedCustomer && selectedDocumentType && (
          <DocumentHistoryModal
            customer={selectedCustomer}
            documentType={selectedDocumentType}
            isOpen={showDocumentHistory}
            onClose={() => {
              setShowDocumentHistory(false);
              setSelectedCustomer(null);
              setSelectedDocumentType(null);
            }}
          />
        )}

      {/* Customer Detail Modal */}
      {showCustomerModal && selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Hồ sơ khách hàng: {selectedCustomer.personalDetails?.name || selectedCustomer.id}
                </h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleShowRequirements(selectedCustomer)}
                    className="bg-purple-600 text-white px-3 py-1 rounded-md hover:bg-purple-700 flex items-center text-sm"
                  >
                    <FaChartPie className="mr-1" />
                    Yêu cầu tài liệu
                  </button>
                  <button
                    onClick={() => handleUploadDocument(selectedCustomer)}
                    className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 flex items-center text-sm"
                  >
                    <FaPlus className="mr-1" />
                    Tải lên tài liệu
                  </button>
                  <button
                    onClick={() => setShowCustomerModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>
              </div>
            </div>
            
            <div className="p-6">
              {/* Customer Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                {/* Personal Details */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-md font-semibold text-gray-900 mb-3">Thông tin cá nhân</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Họ tên:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedCustomer.personalDetails?.name || 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Ngày sinh:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatDate(selectedCustomer.personalDetails?.dateOfBirth)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Giới tính:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedCustomer.personalDetails?.gender === 'male' ? 'Nam' : 
                         selectedCustomer.personalDetails?.gender === 'female' ? 'Nữ' : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Nghề nghiệp:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedCustomer.personalDetails?.occupation || 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-md font-semibold text-gray-900 mb-3">Thông tin liên hệ</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Email:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedCustomer.contactInfo?.email || 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Điện thoại chính:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedCustomer.contactInfo?.primaryPhone || 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Điện thoại phụ:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedCustomer.contactInfo?.secondaryPhone || 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Addresses */}
              {selectedCustomer.addresses && selectedCustomer.addresses.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-md font-semibold text-gray-900 mb-3">Địa chỉ</h4>
                  <div className="space-y-3">
                    {selectedCustomer.addresses.map((address, index) => (
                      <div key={index} className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-900">
                            {address.type === 'home' ? 'Địa chỉ nhà' : 'Địa chỉ khác'}
                          </span>
                          {address.isDefault && (
                            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                              Mặc định
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          {address.street}, {address.ward}, {address.district}, {address.city}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Documents */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3">Tài liệu đã tải lên</h4>
                {documentLoading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-sm text-gray-500">Đang tải tài liệu...</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {customerDocuments[selectedCustomer.id] && 
                     Object.keys(customerDocuments[selectedCustomer.id]).length > 0 ? (
                      Object.entries(customerDocuments[selectedCustomer.id]).map(([docType, documents]) => (
                        <div key={docType} className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="text-sm font-semibold text-gray-900">
                              {getDocumentTypeLabel(docType)}
                            </h5>
                            <button
                              onClick={() => handleShowDocumentHistory(selectedCustomer, docType)}
                              className="text-gray-600 hover:text-gray-900 text-xs flex items-center"
                            >
                              <FaHistory className="mr-1" />
                              Lịch sử
                            </button>
                          </div>
                          <div className="space-y-2">
                            {Array.isArray(documents) && documents.map((doc, index) => (
                              <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
                                <div className="flex items-center space-x-3">
                                  <div className="h-8 w-8 bg-blue-100 rounded flex items-center justify-center">
                                    📄
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">
                                      {doc.filename || doc.originalName || 'Tài liệu'}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      Tải lên: {formatDate(doc.uploadedAt || doc.createdAt)}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {getDocumentStatusBadge(doc.status || 'pending')}
                                  
                                  {/* Document Actions */}
                                  <div className="flex items-center space-x-1">
                                    <button
                                      onClick={() => window.open(`/api/documents/view/${docType}/${doc.filename}?customerId=${selectedCustomer.id}`, '_blank')}
                                      className="text-blue-600 hover:text-blue-800 p-1"
                                      title="Xem tài liệu"
                                    >
                                      <FaEye />
                                    </button>
                                    <button
                                      onClick={() => {
                                        const link = document.createElement('a');
                                        link.href = `/api/documents/view/${docType}/${doc.filename}?customerId=${selectedCustomer.id}`;
                                        link.download = doc.filename;
                                        link.click();
                                      }}
                                      className="text-green-600 hover:text-green-800 p-1"
                                      title="Tải xuống"
                                    >
                                      <FaDownload />
                                    </button>
                                    <button
                                      onClick={() => handleEditDocument(selectedCustomer, doc)}
                                      className="text-orange-600 hover:text-orange-800 p-1"
                                      title="Thay thế tài liệu"
                                    >
                                      <FaEdit />
                                    </button>
                                    
                                    {/* Approval Actions */}
                                    {doc.status === 'pending_approval' && (
                                      <>
                                        <button
                                          onClick={() => handleDocumentAction(doc, 'approve')}
                                          className="text-green-600 hover:text-green-800 p-1"
                                          title="Duyệt tài liệu"
                                        >
                                          <FaCheck />
                                        </button>
                                        <button
                                          onClick={() => handleDocumentAction(doc, 'reject')}
                                          className="text-red-600 hover:text-red-800 p-1"
                                          title="Từ chối tài liệu"
                                        >
                                          <FaTimes />
                                        </button>
                                      </>
                                    )}
                                    
                                    {/* Delete Action */}
                                    <button
                                      onClick={() => {
                                        if (confirm('Bạn có chắc chắn muốn xóa tài liệu này?')) {
                                          handleDocumentAction(doc, 'delete');
                                        }
                                      }}
                                      className="text-red-600 hover:text-red-800 p-1"
                                      title="Xóa tài liệu"
                                    >
                                      <FaTrash />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 bg-gray-50 rounded-lg">
                        <p className="text-gray-500">Khách hàng chưa tải lên tài liệu nào</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentManagement;