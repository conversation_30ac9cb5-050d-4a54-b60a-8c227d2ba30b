import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { FaArrowLeft, FaSpinner, FaCheck, FaTimes, FaPrint, FaEnvelope } from 'react-icons/fa';
import { FiShoppingCart, FiUser, FiCalendar, FiDollarSign, FiPackage, FiTruck, FiCheckCircle, FiXCircle, FiClock, FiArrowLeft, FiSearch, FiFilter, FiEye, FiEdit, FiDownload, FiLink, FiMessageSquare, FiSend, FiMail, FiPhone, FiClipboard, FiAlertCircle, FiBox, FiMapPin, FiX, FiFileText, FiEdit2, FiSave } from 'react-icons/fi';

const ItemDetailsEditor = ({ item, itemIndex, details, onSave, onCancel }) => {
  const [topupNumber, setTopupNumber] = useState(details?.topupNumber || '');
  const [simNumber, setSimNumber] = useState(details?.simNumber || '');
  const [notes, setNotes] = useState(details?.notes || '');

  const handleSave = () => {
    const newDetails = {
      topupNumber: topupNumber.trim(),
      simNumber: simNumber.trim(),
      notes: notes.trim()
    };
    onSave(itemIndex, newDetails);
  };

  return (
    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <h4 className="text-sm font-medium text-blue-900 mb-3">Chi tiết sản phẩm</h4>
      <div className="space-y-3">
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Số topup/nạp tiền:</label>
          <input
            type="text"
            value={topupNumber}
            onChange={(e) => setTopupNumber(e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Nhập số topup hoặc mã nạp tiền"
          />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Số SIM:</label>
          <input
            type="text"
            value={simNumber}
            onChange={(e) => setSimNumber(e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Nhập số SIM"
          />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Ghi chú:</label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={2}
            placeholder="Ghi chú thêm về sản phẩm"
          />
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleSave}
            className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
          >
            <FaCheck className="w-3 h-3 mr-1 inline" />
            Lưu
          </button>
          <button
            onClick={onCancel}
            className="flex-1 px-3 py-2 bg-gray-500 text-white text-sm rounded-md hover:bg-gray-600 transition-colors"
          >
            <FaTimes className="w-3 h-3 mr-1 inline" />
            Hủy
          </button>
        </div>
      </div>
    </div>
  );
};

const OrderStatus = ({ status }) => {
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'delivered':
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'processing':
      case 'confirmed':
      case 'partially_paid':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
      case 'failed':
      case 'not_paid':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'Hoàn thành';
      case 'delivered':
        return 'Đã giao hàng';
      case 'paid':
        return 'Đã thanh toán';
      case 'processing':
        return 'Đang xử lý';
      case 'confirmed':
        return 'Đã xác nhận';
      case 'partially_paid':
        return 'Thanh toán một phần';
      case 'cancelled':
        return 'Đã hủy';
      case 'failed':
        return 'Thất bại';
      case 'not_paid':
        return 'Chưa thanh toán';
      case 'pending':
        return 'Chờ xác nhận';
      default:
        return status || 'Không xác định';
    }
  };

  return (
    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
      {getStatusText(status)}
    </span>
  );
};

const OrderDetailPage = () => {
  const router = useRouter();
  const { orderId } = router.query;
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [orderNotes, setOrderNotes] = useState('');
  const [itemDetails, setItemDetails] = useState({});
  const [editingItem, setEditingItem] = useState(null);
  const [showSheetsModal, setShowSheetsModal] = useState(false);
  const [currencyWarning, setCurrencyWarning] = useState('');
  const [sheetsItems, setSheetsItems] = useState([]);

  // Handle adding Google Sheets items to the order
  const handleAddSheetsItems = (items) => {
    setSheetsItems(prev => [...prev, ...items]);
  };

  useEffect(() => {
    if (!orderId) return;

    const fetchOrderDetails = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/orders/${orderId}`);
        
        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
          throw new Error(data.error);
        }
        
        // Handle both direct order data and wrapped response
        const orderData = data.order || data;
        setOrder(orderData);
        setOrderNotes(orderData.internalNotes || '');
        setItemDetails(orderData.itemDetails || {});
      } catch (error) {
        console.error('Error fetching order details:', error);
        setError(error.message || 'Failed to load order details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId]);

  // Currency validation and correction useEffect
  useEffect(() => {
    if (order && order.items) {
      const taiwanSkus = order.items.filter(item => 
        item.sku && item.sku.toLowerCase().includes('taiwan')
      );
      
      if (taiwanSkus.length > 0 && order.currency === 'VND') {
        setCurrencyWarning('Cảnh báo: Đơn hàng chứa sản phẩm Taiwan nhưng đang sử dụng tiền tệ VND. Đã tự động chuyển đổi hiển thị sang NT$.');
        // Auto-correct the currency for display purposes
        order.currency = 'NT$';
      } else {
        setCurrencyWarning('');
      }
    }
  }, [order]);

  const handleUpdateOrderStatus = async (newStatus) => {
    try {
      setUpdateLoading(true);
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          updatedAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update order status');
      }

      const updatedOrder = await response.json();
      setOrder(prev => ({ ...prev, status: newStatus, updatedAt: new Date().toLocaleString('vi-VN') }));
    } catch (error) {
      console.error('Error updating order status:', error);
      alert('Có lỗi xảy ra khi cập nhật trạng thái đơn hàng');
    } finally {
      setUpdateLoading(false);
    }
  };

  const handleSaveNotes = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          internalNotes: orderNotes,
          updatedAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save notes');
      }

      alert('Đã lưu ghi chú nội bộ!');
    } catch (error) {
      console.error('Error saving notes:', error);
      alert('Có lỗi xảy ra khi lưu ghi chú');
    }
  };

  const handleSaveItemDetails = async (itemIndex, details) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemDetails: {
            ...itemDetails,
            [itemIndex]: details
          },
          updatedAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save item details');
      }

      setItemDetails(prev => ({
        ...prev,
        [itemIndex]: details
      }));
      setEditingItem(null);
      alert('Đã lưu thông tin chi tiết sản phẩm!');
    } catch (error) {
      console.error('Error saving item details:', error);
      alert('Có lỗi xảy ra khi lưu thông tin sản phẩm');
    }
  };

  const isItemSeparateType = (item) => {
    // Check if item is of "separate" type based on SKU or name patterns
    const separateKeywords = ['sim', 'topup', 'recharge', 'card', 'mạng'];
    const itemName = (item.name || '').toLowerCase();
    const itemSku = (item.sku || '').toLowerCase();
    
    return separateKeywords.some(keyword => 
      itemName.includes(keyword) || itemSku.includes(keyword)
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleString('vi-VN');
    } catch {
      return dateString;
    }
  };

  // Currency formatting function - NO FALLBACKS
  const formatCurrency = (amount, currency) => {
    if (!amount && amount !== 0) {
      return currency ? `0 ${currency}` : '0 (KHÔNG RÕ LOẠI TIỀN)';
    }

    if (!currency) {
      return `${amount} (KHÔNG RÕ LOẠI TIỀN)`;
    }

    switch (currency) {
      case 'VND':
        return `${amount.toLocaleString('vi-VN')}đ`;
      case 'NT$':
      case 'NT':
      case 'NTD':
      case 'TWD':
        return `NT$ ${amount.toLocaleString()}`;
      case 'USD':
      case '$':
        return `$${amount.toFixed(2)}`;
      default:
        return `${currency} ${amount}`;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Đang tải thông tin đơn hàng...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaTimes className="text-4xl text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Có lỗi xảy ra</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/admin/orders')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Quay lại danh sách đơn hàng
          </button>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FaTimes className="text-4xl text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy đơn hàng</h2>
          <p className="text-gray-600 mb-4">Đơn hàng #{orderId} không tồn tại</p>
          <button
            onClick={() => router.push('/admin/orders')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Quay lại danh sách đơn hàng
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push('/admin/orders')}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5 mr-2" />
            Quay lại danh sách đơn hàng
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FiShoppingCart className="w-8 h-8 mr-3 text-blue-600" />
                Chi tiết đơn hàng #{orderId}
              </h1>
              <p className="text-gray-600 mt-2">Quản lý và theo dõi chi tiết đơn hàng</p>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => window.print()}
                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                <FaPrint className="w-4 h-4 mr-2" />
                In đơn hàng
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Thông tin đơn hàng</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Mã đơn hàng</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono">#{order.id || order.orderId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Trạng thái</label>
                  <div className="mt-1">
                    <OrderStatus status={order.status} />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Ngày tạo</label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(order.createdAt)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Cập nhật lần cuối</label>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(order.updatedAt)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Tiền tệ</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {order.currency ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {order.currency}
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        ⚠️ Không xác định
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Customer Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Thông tin khách hàng</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Tên khách hàng</label>
                  <p className="mt-1 text-sm text-gray-900">{order.customerName || 'Không có thông tin'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Số điện thoại</label>
                  <p className="mt-1 text-sm text-gray-900">{order.customerPhone || 'Không có thông tin'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{order.customerEmail || 'Không có thông tin'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Địa chỉ giao hàng</label>
                  <p className="mt-1 text-sm text-gray-900">{order.shippingAddress || 'Không có thông tin'}</p>
                </div>
              </div>
            </div>

            {/* Products */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Sản phẩm đặt hàng</h2>
              <div className="space-y-4">
                {order.products && order.products.length > 0 ? (
                  order.products.map((product, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="grid grid-cols-1 sm:grid-cols-4 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Sản phẩm</label>
                          <p className="text-sm font-medium text-gray-900">{product.name}</p>
                          {product.sku && <p className="text-xs text-gray-500">SKU: {product.sku}</p>}
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Số lượng</label>
                          <p className="text-sm text-gray-900">{product.quantity}</p>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Đơn giá</label>
                          <p className="text-sm text-gray-900">{formatCurrency(product.price, order.currency)}</p>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Thành tiền</label>
                          <p className="text-sm font-bold text-gray-900">
                            {formatCurrency(product.quantity * product.price, order.currency)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : order.items && order.items.length > 0 ? (
                  order.items.map((item, index) => {
                    const isSeparate = isItemSeparateType(item);
                    const details = itemDetails[index] || {};
                    const isEditing = editingItem === index;
                    
                    return (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="grid grid-cols-1 sm:grid-cols-4 gap-3">
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Sản phẩm</label>
                            <p className="text-sm font-medium text-gray-900">{item.name || item.title}</p>
                            {item.sku && <p className="text-xs text-gray-500">SKU: {item.sku}</p>}
                            {isSeparate && (
                              <span className="inline-block mt-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                Loại riêng biệt
                              </span>
                            )}
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Số lượng</label>
                            <p className="text-sm text-gray-900">{item.quantity}</p>
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Đơn giá</label>
                            <p className="text-sm text-gray-900">{formatCurrency(item.price, order.currency)}</p>
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase mb-1">Thành tiền</label>
                            <p className="text-sm font-bold text-gray-900">
                              {formatCurrency(item.quantity * item.price, order.currency)}
                            </p>
                          </div>
                        </div>
                        
                        {/* Item Details Section */}
                        {isSeparate && (
                          <div className="mt-4 border-t border-gray-200 pt-4">
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="text-sm font-medium text-gray-900">Chi tiết sản phẩm</h4>
                              {!isEditing && (
                                <button
                                  onClick={() => setEditingItem(index)}
                                  className="flex items-center px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                                >
                                  <FiEdit className="w-3 h-3 mr-1" />
                                  Chỉnh sửa
                                </button>
                              )}
                            </div>
                            
                            {isEditing ? (
                              <ItemDetailsEditor
                                item={item}
                                itemIndex={index}
                                details={details}
                                onSave={handleSaveItemDetails}
                                onCancel={() => setEditingItem(null)}
                              />
                            ) : (
                              <div className="space-y-2">
                                {details.topupNumber && (
                                  <div>
                                    <span className="text-xs font-medium text-gray-500">Số nạp tiền: </span>
                                    <span className="text-sm text-gray-900">{details.topupNumber}</span>
                                  </div>
                                )}
                                {details.simNumber && (
                                  <div>
                                    <span className="text-xs font-medium text-gray-500">Số SIM: </span>
                                    <span className="text-sm text-gray-900">{details.simNumber}</span>
                                  </div>
                                )}
                                {details.notes && (
                                  <div>
                                    <span className="text-xs font-medium text-gray-500">Ghi chú: </span>
                                    <span className="text-sm text-gray-900">{details.notes}</span>
                                  </div>
                                )}
                                {!details.topupNumber && !details.simNumber && !details.notes && (
                                  <p className="text-xs text-gray-500 italic">Chưa có thông tin chi tiết</p>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                        
                        {/* Google Sheets Product Details */}
                        {order.metadata && order.metadata.sheetsId && item.sku && (
                          <GoogleSheetsProductDetails
                            sku={item.sku}
                            sheetsId={order.metadata.sheetsId}
                            assignedColumns={order.metadata.assignedColumns}
                            assignedColumnValues={order.metadata.assignedColumnValues}
                          />
                        )}
                      </div>
                     );
                   })
) : (
                  <p className="text-gray-500 text-center py-4">Không có sản phẩm nào</p>
                )}
                
                {/* Google Sheets Items */}
                {sheetsItems.length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Sản phẩm từ Google Sheets:</h4>
                    <div className="space-y-2">
                      {sheetsItems.map((item, index) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">{item.name}</div>
                            <div className="text-sm text-gray-600">SKU: {item.sku} | Cột: {item.column}</div>
                            <div className="text-sm text-blue-600">Giá trị: {item.value}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-600">Số lượng: {item.quantity}</div>
                            <div className="font-medium text-gray-900">{formatCurrency(item.price, order.currency)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Total */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-900">Tổng cộng:</span>
                    <span className="text-lg font-bold text-blue-600">
                      {formatCurrency(order.totalAmount || order.amount || 0, order.currency)}
                    </span>
                    {!order.currency && (
                      <div className="text-xs text-red-500 ml-2">⚠️ Thiếu thông tin tiền tệ</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Google Sheets Integration */}
            {order.metadata?.sheetsId && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Google Sheets</h3>
                <div className="space-y-3">
                  <div className="text-sm text-gray-600">
                    <p>Sheets ID: {order.metadata.sheetsId}</p>
                    {order.metadata.assignedColumns && (
                      <p>Cột đã gán: {order.metadata.assignedColumns.join(', ')}</p>
                    )}
                  </div>
                  <button
                    onClick={() => setShowSheetsModal(true)}
                    className="w-full flex items-center justify-center px-4 py-2 bg-emerald-600 text-white text-sm rounded-md hover:bg-emerald-700 transition-colors"
                  >
                    <FiFileText className="w-4 h-4 mr-2" />
                    Thêm sản phẩm từ Sheets
                  </button>
                </div>
              </div>
            )}

            {/* Status Management */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quản lý trạng thái</h3>
              <div className="space-y-3">
                <button
                  onClick={() => handleUpdateOrderStatus('processing')}
                  disabled={updateLoading}
                  className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  <FiTruck className="w-4 h-4 mr-2" />
                  Đang xử lý
                </button>
                <button
                  onClick={() => handleUpdateOrderStatus('completed')}
                  disabled={updateLoading}
                  className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  <FiCheckCircle className="w-4 h-4 mr-2" />
                  Hoàn thành
                </button>
                <button
                  onClick={() => handleUpdateOrderStatus('cancelled')}
                  disabled={updateLoading}
                  className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                >
                  <FiXCircle className="w-4 h-4 mr-2" />
                  Hủy đơn
                </button>
              </div>
            </div>

            {/* Payment Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông tin thanh toán</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Phương thức:</span>
                  <span className="text-sm text-gray-900">
                    {order.paymentMethod === 'card' ? 'Thẻ tín dụng' : 
                     order.paymentMethod === 'cash' ? 'Tiền mặt' : 
                     order.paymentMethod === 'transfer' ? 'Chuyển khoản' : 
                     order.paymentMethod || 'Không xác định'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Trạng thái:</span>
                  <OrderStatus status={order.paymentStatus} />
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Tổng tiền:</span>
                  <span className="text-sm font-bold text-gray-900">
                    {formatCurrency(order.totalAmount || order.amount || 0, order.currency)}
                  </span>
                </div>
                {!order.currency && (
                  <div className="text-xs text-red-500 mt-1">⚠️ Đơn hàng này thiếu thông tin tiền tệ</div>
                )}
                {currencyWarning && (
                  <div className="text-xs text-orange-500 mt-1">{currencyWarning}</div>
                )}
              </div>
            </div>

            {/* Notes */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Ghi chú nội bộ</h3>
              <div className="space-y-3">
                {order.note && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Ghi chú từ khách hàng:</label>
                    <p className="text-gray-700 bg-gray-50 p-3 rounded-lg border text-sm">
                      {order.note}
                    </p>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Ghi chú nội bộ:</label>
                  <textarea
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    rows={4}
                    placeholder="Thêm ghi chú nội bộ cho đơn hàng này..."
                  />
                  <button
                    onClick={handleSaveNotes}
                    className="mt-2 w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <FiClipboard className="w-4 h-4 mr-2" />
                    Lưu ghi chú
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Google Sheets Modal */}
      {showSheetsModal && (
        <GoogleSheetsModal
          isOpen={showSheetsModal}
          onClose={() => setShowSheetsModal(false)}
          order={order}
          onAddItems={handleAddSheetsItems}
        />
      )}
    </div>
  );
};

// Google Sheets Product Details Component
const GoogleSheetsProductDetails = ({ sku, sheetsId, assignedColumns, assignedColumnValues }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [sheetsData, setSheetsData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [productDetails, setProductDetails] = useState(null);

  useEffect(() => {
    // Use the assignedColumnValues directly if available
    if (assignedColumnValues && assignedColumnValues.length > 0) {
      const details = {
        sku: sku,
        productName: 'Google Sheets Assignment',
        subItems: assignedColumnValues.map(colData => ({
          column: colData.columnLetter,
          value: colData.value || 'N/A'
        }))
      };
      setProductDetails(details);
      setLoading(false);
    } else {
      // Fallback to API fetch if no column values in metadata
      fetchSheetsDataFromAPI();
    }
  }, [sku, assignedColumns, assignedColumnValues]);

  const fetchSheetsDataFromAPI = async () => {
    if (!sheetsId || !sku) return;
    
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/admin/google-sheets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sheetsId }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch Google Sheets data');
      }
      
      const data = await response.json();
      setSheetsData(data);
      
      // Find the row with matching SKU
      const skuRow = data.rows.find(row => row[0] && row[0].toString().trim() === sku.trim());
      
      if (skuRow) {
        const details = {
          sku: skuRow[0] || '',
          productName: skuRow[1] || 'Google Sheets Assignment',
          subItems: []
        };
        
        // Extract sub-items from assigned columns
        if (assignedColumns && assignedColumns.length > 0) {
          assignedColumns.forEach(colIndex => {
            const columnLetter = String.fromCharCode(65 + colIndex); // Convert to A, B, C...
            const value = skuRow[colIndex] || '';
            if (value) {
              details.subItems.push({
                column: columnLetter,
                value: value
              });
            }
          });
        }
        
        setProductDetails(details);
      }
    } catch (err) {
      console.error('Error fetching sheets data:', err);
      setError('Không thể tải dữ liệu từ Google Sheets');
    } finally {
      setLoading(false);
    }
  };

  const toggleExpanded = () => {
    if (!isExpanded && !productDetails && !assignedColumnValues) {
      fetchSheetsDataFromAPI();
    }
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="mt-4 border-t border-gray-200 pt-4">
      <button
        onClick={toggleExpanded}
        className="flex items-center justify-between w-full text-left"
      >
        <h4 className="text-sm font-medium text-gray-900">Chi tiết Google Sheets</h4>
        <div className="flex items-center">
          {loading && (
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          )}
          <svg
            className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>
      
      {isExpanded && (
        <div className="mt-3 space-y-3">
          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}
          
          {productDetails && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="space-y-2">
                <div>
                  <span className="text-xs font-medium text-gray-500">SKU: </span>
                  <span className="text-sm text-gray-900 font-mono">{productDetails.sku}</span>
                </div>
                <div>
                  <span className="text-xs font-medium text-gray-500">Sản phẩm: </span>
                  <span className="text-sm text-gray-900">{productDetails.productName}</span>
                </div>
                
                {productDetails.subItems.length > 0 && (
                  <div>
                    <span className="text-xs font-medium text-gray-500 block mb-1">Các mục con:</span>
                    <div className="space-y-1">
                      {productDetails.subItems.map((subItem, index) => (
                        <div key={index} className="flex items-center">
                          <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded mr-2 font-mono">
                            Cột {subItem.column}
                          </span>
                          <span className="text-sm text-gray-900">{subItem.value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {productDetails.subItems.length === 0 && (
                  <p className="text-xs text-gray-500 italic">Không có dữ liệu từ các cột đã gán</p>
                )}
              </div>
            </div>
          )}
          
          {!productDetails && !loading && !error && (
            <p className="text-xs text-gray-500 italic">Không tìm thấy dữ liệu cho SKU này</p>
          )}
        </div>
      )}
    </div>
  );
};

// Google Sheets Modal Component
const GoogleSheetsModal = ({ isOpen, onClose, order, onAddItems }) => {
  const [sheetsData, setSheetsData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [availableSkus, setAvailableSkus] = useState([]);
  const [selectedSku, setSelectedSku] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && order.metadata?.sheetsId) {
      fetchSheetsData();
    }
  }, [isOpen, order.metadata?.sheetsId]);

  const fetchSheetsData = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch('/api/admin/google-sheets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sheetsId: order.metadata.sheetsId })
      });
      
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);
      
      setSheetsData(data);
      
      // Extract available SKUs
      const skus = data.rows
        .map(row => ({ sku: row[0], name: row[1] || '' }))
        .filter(item => item.sku && item.sku.trim() !== '')
        .filter((item, index, self) => self.findIndex(s => s.sku === item.sku) === index);
      setAvailableSkus(skus);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getAvailableColumns = () => {
    if (!sheetsData || !selectedSku) return [];
    
    const skuRow = sheetsData.rows.find(row => row[0] === selectedSku);
    if (!skuRow) return [];
    
    const columns = [];
    for (let i = 3; i < Math.min(26, sheetsData.headers?.length || 26); i++) {
      const cellValue = String(skuRow[i] || '').trim();
      if (cellValue && cellValue !== '') {
        columns.push({
          index: i,
          letter: String.fromCharCode(65 + i),
          header: sheetsData.headers?.[i] || `Cột ${String.fromCharCode(65 + i)}`,
          value: cellValue
        });
      }
    }
    return columns;
  };

  const handleColumnToggle = (columnIndex) => {
    setSelectedColumns(prev => {
      if (prev.includes(columnIndex)) {
        return prev.filter(col => col !== columnIndex);
      } else {
        return [...prev, columnIndex];
      }
    });
  };

  const handleAddItems = () => {
    if (!selectedSku || selectedColumns.length === 0) {
      setError('Vui lòng chọn SKU và ít nhất một cột');
      return;
    }

    const skuRow = sheetsData.rows.find(row => row[0] === selectedSku);
    const items = selectedColumns.map(colIndex => ({
      name: `${selectedSku} - ${String.fromCharCode(65 + colIndex)}`,
      sku: selectedSku,
      quantity: 1,
      price: 0,
      column: String.fromCharCode(65 + colIndex),
      value: skuRow[colIndex] || ''
    }));

    onAddItems(items);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Thêm sản phẩm từ Google Sheets</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <FiX className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {loading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Đang tải dữ liệu...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {sheetsData && (
            <div className="space-y-6">
              {/* SKU Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Chọn SKU:</label>
                <select
                  value={selectedSku}
                  onChange={(e) => {
                    setSelectedSku(e.target.value);
                    setSelectedColumns([]);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="">-- Chọn SKU --</option>
                  {availableSkus.map((item, index) => (
                    <option key={index} value={item.sku}>
                      {item.sku} - {item.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Column Selection */}
              {selectedSku && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Chọn các cột (D đến Z):</label>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 max-h-60 overflow-y-auto">
                    {getAvailableColumns().map((column) => (
                      <label key={column.index} className="flex items-start space-x-2 cursor-pointer p-2 border rounded hover:bg-gray-50">
                        <input
                          type="checkbox"
                          checked={selectedColumns.includes(column.index)}
                          onChange={() => handleColumnToggle(column.index)}
                          className="mt-1 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                        />
                        <div className="text-sm">
                          <div className="font-medium text-gray-700">{column.letter}</div>
                          <div className="text-gray-500 text-xs break-all">{column.value}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Hủy
          </button>
          <button
            onClick={handleAddItems}
            disabled={!selectedSku || selectedColumns.length === 0}
            className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            Thêm sản phẩm ({selectedColumns.length})
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailPage;