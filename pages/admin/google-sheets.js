import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { FiFileText, FiDownload, FiLoader, FiAlertCircle, FiSearch, FiFilter } from 'react-icons/fi';
import AdminLayout from '../../components/admin/AdminLayout';

const GoogleSheetsPage = () => {
  const [sheetsId, setSheetsId] = useState('');
  const [sheetsData, setSheetsData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [extractedArray, setExtractedArray] = useState([]);
  
  // New state for SKU selection feature
  const [selectedSku, setSelectedSku] = useState('');
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [skuData, setSkuData] = useState(null);
  const [availableSkus, setAvailableSkus] = useState([]);
  
  // State for customer selection
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [loadingCustomers, setLoadingCustomers] = useState(false);
  
  // State for sell functionality
  const [selling, setSelling] = useState(false);
  const [sellSuccess, setSellSuccess] = useState('');
  const [sellError, setSellError] = useState('');

  // Auto-fill Google Sheets ID from environment
  useEffect(() => {
    const fetchGoogleSheetsId = async () => {
      try {
        const response = await fetch('/api/admin/google-sheets-config');
        if (response.ok) {
          const data = await response.json();
          setSheetsId(data.googleSheetsId || '');
        }
      } catch (error) {
        console.error('Error fetching Google Sheets ID:', error);
      }
    };

    fetchGoogleSheetsId();
  }, []);

  // Fetch customers on component mount
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoadingCustomers(true);
        const response = await fetch('/api/customers');
        if (response.ok) {
          const data = await response.json();
          setCustomers(data.customers || []);
        } else {
          console.error('Failed to fetch customers');
        }
      } catch (error) {
        console.error('Error fetching customers:', error);
      } finally {
        setLoadingCustomers(false);
      }
    };

    fetchCustomers();
  }, []);

  // Extract available SKUs when sheets data is loaded
  useEffect(() => {
    if (sheetsData && sheetsData.rows && sheetsData.headers) {
      // Find the index of "DANH MỤC MÃ CHƯA NẠP" column
      const danhMucColumnIndex = sheetsData.headers.findIndex(header => 
        header && header.toLowerCase().includes('danh mục mã chưa nạp')
      );
      
      const skus = sheetsData.rows
        .map(row => ({
          sku: row[0], // Column A (SKU)
          name: row[1] || '', // Column B (NAME)
          danhMucValue: danhMucColumnIndex >= 0 ? row[danhMucColumnIndex] : ''
        }))
        .filter(item => item.sku && item.sku.trim() !== '')
        .filter(item => {
          // Only include SKUs that have non-empty "DANH MỤC MÃ CHƯA NẠP" value
          const danhMucValue = String(item.danhMucValue || '').trim();
          return danhMucValue && danhMucValue !== '';
        })
        .filter((item, index, self) => self.findIndex(s => s.sku === item.sku) === index); // Remove duplicates
      setAvailableSkus(skus);
    }
  }, [sheetsData]);

  const handleFetchSheets = async () => {
    if (!sheetsId.trim()) {
      setError('Vui lòng nhập ID Google Sheets');
      return;
    }

    setLoading(true);
    setError('');
    setSheetsData(null);

    try {
      const response = await fetch('/api/admin/google-sheets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sheetsId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Có lỗi xảy ra khi tải dữ liệu');
      }

      setSheetsData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const extractSheetsId = (input) => {
    // Extract ID from Google Sheets URL or return as-is if it's already an ID
    const urlMatch = input.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    return urlMatch ? urlMatch[1] : input;
  };

  const handleInputChange = (e) => {
    const input = e.target.value;
    const extractedId = extractSheetsId(input);
    setSheetsId(extractedId);
  };

  // Handle SKU selection and column extraction
  const handleSkuSelection = (sku) => {
    setSelectedSku(sku);
    setSelectedColumns([]); // Reset to empty array - no auto-selection
    setSkuData(null);
  };

  const handleColumnToggle = (columnIndex) => {
    setSelectedColumns(prev => {
      if (prev.includes(columnIndex)) {
        return prev.filter(col => col !== columnIndex);
      } else {
        return [...prev, columnIndex];
      }
    });
  };

  const extractSkuData = async () => {
    if (!selectedCustomer || !selectedSku || !sheetsData || selectedColumns.length === 0) {
      setError('Vui lòng chọn khách hàng, SKU và ít nhất một cột');
      return;
    }

    setSelling(true);
    setSellSuccess('');
    setSellError('');
    setError('');

    try {
      // Get selected customer info
      const selectedCustomerInfo = customers.find(c => c.id === selectedCustomer);

      // Find the row with the selected SKU
      const skuRow = sheetsData.rows.find(row => row[0] === selectedSku);
      if (!skuRow) {
        setError('Không tìm thấy SKU đã chọn');
        return;
      }

      // Extract data from selected columns (D to Z = index 3 to 25)
      const extractedItems = selectedColumns
        .map(colIndex => ({
          column: String.fromCharCode(65 + colIndex), // Convert index to column letter
          value: skuRow[colIndex] || ''
        }))
        .filter(item => {
          // Ensure value is a string before calling trim
          const stringValue = String(item.value || '');
          return stringValue && stringValue.trim() !== '';
        });

      // Display extracted data
      setSkuData({
        sku: selectedSku,
        customer: {
          id: selectedCustomerInfo?.id,
          name: selectedCustomerInfo?.name,
          email: selectedCustomerInfo?.email
        },
        items: extractedItems
      });

      // Call the sell API to remove items from Google Sheets
      const sellResponse = await fetch('/api/admin/google-sheets-sell', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sheetsId: sheetsId,
          sku: selectedSku,
          columns: selectedColumns,
          customerId: selectedCustomer
        }),
      });

      const sellData = await sellResponse.json();

      if (!sellResponse.ok) {
        throw new Error(sellData.error || 'Có lỗi xảy ra khi bán hàng');
      }

      setSellSuccess(sellData.message || `Đã xuất kho thành công ${sellData.clearedCells} sản phẩm cho SKU: ${selectedSku}`);
      
      // Refresh the sheets data to show updated information
      await handleFetchSheets();
      
    } catch (err) {
      setSellError(err.message);
    } finally {
      setSelling(false);
    }
  };

  // Get available columns D to Z (index 3 to 25) that have data for the selected SKU
  const getAvailableColumns = () => {
    if (!sheetsData || !sheetsData.headers || !selectedSku) return [];
    
    // Find the row with the selected SKU
    const skuRow = sheetsData.rows.find(row => row[0] === selectedSku);
    if (!skuRow) return [];
    
    const columns = [];
    for (let i = 3; i < Math.min(26, sheetsData.headers.length); i++) {
      // Only include columns that have non-empty data in the selected SKU row
      const cellValue = String(skuRow[i] || '').trim();
      if (cellValue && cellValue !== '') {
        columns.push({
          index: i,
          letter: String.fromCharCode(65 + i),
          header: sheetsData.headers[i] || `Cột ${String.fromCharCode(65 + i)}`,
          value: cellValue
        });
      }
    }
    return columns;
  };

  return (
    <>
      <Head>
        <title>Quản Lý Google Sheets - MAG Shop Admin</title>
        <meta name="description" content="Xem và quản lý dữ liệu từ Google Sheets" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <FiFileText className="text-2xl text-emerald-600 mr-3" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Quản Lý Google Sheets</h1>
                  <p className="text-sm text-gray-600">Xem và quản lý dữ liệu từ Google Sheets</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Input Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Nhập Google Sheets ID</h2>
            <div className="flex gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  value={sheetsId}
                  onChange={handleInputChange}
                  placeholder="Nhập Google Sheets ID hoặc URL đầy đủ"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  disabled={loading}
                />
                <p className="text-sm text-gray-500 mt-2">
                  Bạn có thể dán URL đầy đủ hoặc chỉ ID của Google Sheets
                </p>
              </div>
              <button
                onClick={handleFetchSheets}
                disabled={loading || !sheetsId.trim()}
                className="px-6 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <FiLoader className="animate-spin" />
                    Đang tải...
                  </>
                ) : (
                  <>
                    <FiDownload />
                    Tải dữ liệu
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <FiAlertCircle className="text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

{/* SKU Selection Feature */}
          {sheetsData && availableSkus.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center">
                  <FiSearch className="text-emerald-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Xuất kho
                  </h3>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Chọn khách hàng, và mã thẻ nạp trong kho để xuất cho khách.
                </p>
              </div>
              
              <div className="p-6">
                {/* Customer Selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Chọn khách hàng: *
                  </label>
                  <select
                    value={selectedCustomer}
                    onChange={(e) => {
                      setSelectedCustomer(e.target.value);
                      // Reset SKU selection when customer changes
                      setSelectedSku('');
                      setSelectedColumns([]);
                      setSkuData(null);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    disabled={loadingCustomers}
                  >
                    <option value="">-- Chọn khách hàng --</option>
                    {customers.map((customer) => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name} ({customer.email})
                      </option>
                    ))}
                  </select>
                  {loadingCustomers && (
                    <p className="text-sm text-gray-500 mt-1">Đang tải danh sách khách hàng...</p>
                  )}
                </div>

                {/* SKU Selection */}
                {selectedCustomer && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Chọn SKU:
                    </label>
                  <select
                    value={selectedSku}
                    onChange={(e) => handleSkuSelection(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">-- Chọn SKU --</option>
                    {availableSkus.map((item, index) => (
                      <option key={index} value={item.sku}>
                        {item.sku} - {item.name}
                      </option>
                    ))}
                  </select>
                </div>
                )}

                {/* Column Selection */}
                {selectedCustomer && selectedSku && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Chọn các cột (D đến Z):
                    </label>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                      {getAvailableColumns().map((column) => (
                        <label key={column.index} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedColumns.includes(column.index)}
                            onChange={() => handleColumnToggle(column.index)}
                            className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                          />
                          <span className="text-sm text-gray-700">
                            {column.letter} ({column.header}): {column.value}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                {/* Extract Button */}
                {selectedCustomer && selectedSku && selectedColumns.length > 0 && (
                  <div className="mb-6">
                    <button
                      onClick={extractSkuData}
                      disabled={selling}
                      className="px-6 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2"
                    >
                      {selling ? (
                        <>
                          <FiLoader className="animate-spin" />
                          Đang thực hiện xuất kho...
                        </>
                      ) : (
                        <>
                          <FiFilter />
                          Xuất kho cho khách này
                        </>
                      )}
                    </button>
                  </div>
                )}

                {/* Success Message */}
                {sellSuccess && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                      <FiDownload className="text-green-500 mr-2" />
                      <span className="text-green-700">{sellSuccess}</span>
                    </div>
                  </div>
                )}

                {/* Sell Error Message */}
                {sellError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                      <FiAlertCircle className="text-red-500 mr-2" />
                      <span className="text-red-700">{sellError}</span>
                    </div>
                  </div>
                )}

                {/* Extracted Data Display */}
                {skuData && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-semibold text-gray-900 mb-3">
                      Dữ liệu trích xuất cho SKU: {skuData.sku}
                    </h4>
                    {skuData.items.length > 0 ? (
                      <div className="space-y-2">
                        {skuData.items.map((item, index) => (
                          <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
                            <span className="font-medium text-gray-700">Cột {item.column}:</span>
                            <span className="text-gray-900">{item.value}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500">Không có dữ liệu trong các cột đã chọn</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
          {/* Data Table */}
          {sheetsData && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Dữ liệu Google Sheets: {sheetsData.title}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {sheetsData.rowCount} hàng × {sheetsData.columnCount} cột
                </p>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {sheetsData.headers && sheetsData.headers.map((header, index) => (
                        <th
                          key={index}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {header || `Cột ${index + 1}`}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sheetsData.rows && sheetsData.rows.map((row, rowIndex) => {
                      const rowSku = row[0];
                      const isSelected = selectedSku === rowSku;
                      const isClickable = rowSku && rowSku.trim() !== '';
                      
                      return (
                        <tr 
                          key={rowIndex} 
                          className={`${
                            rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                          } ${
                            isSelected ? 'ring-2 ring-emerald-500 bg-emerald-50' : ''
                          } ${
                            isClickable ? 'hover:bg-emerald-100 cursor-pointer' : ''
                          }`}
                          onClick={() => {
                            if (isClickable && selectedCustomer) {
                              handleSkuSelection(rowSku);
                            }
                          }}
                        >
                          {row.map((cell, cellIndex) => (
                            <td
                              key={cellIndex}
                              className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                            >
                              {cell || '-'}
                            </td>
                          ))}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {sheetsData.rows && sheetsData.rows.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">Không có dữ liệu trong sheet này</p>
                </div>
              )}
            </div>
          )}          
        </div>
      </div>
    </>
  );
};

const GoogleSheetsAdmin = () => {
  return (
    <AdminLayout>
      <GoogleSheetsPage />
    </AdminLayout>
  );
};

export default GoogleSheetsAdmin;