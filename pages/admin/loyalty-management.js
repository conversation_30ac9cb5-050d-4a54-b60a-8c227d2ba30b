import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FaEdit, FaSave, FaTimes, FaPlus, FaTrash, FaUsers, FaCog, FaGift, FaStar, FaCalendar } from 'react-icons/fa';

const LoyaltyManagement = () => {
  const router = useRouter();
  const { store } = router.query;
  const [loyaltyRules, setLoyaltyRules] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingSection, setEditingSection] = useState(null);
  const [tempData, setTempData] = useState({});
  const [activeTab, setActiveTab] = useState('settings');

  // Fetch loyalty rules on component mount
  useEffect(() => {
    fetchLoyaltyRules();
  }, []);

  const fetchLoyaltyRules = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/loyalty-rules');
      if (response.ok) {
        const data = await response.json();
        setLoyaltyRules(data);
      } else {
        throw new Error('Failed to fetch loyalty rules');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const updateLoyaltyRules = async (section, data) => {
    try {
      const response = await fetch('/api/admin/loyalty-rules', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: section,
          data: data
        })
      });

      if (response.ok) {
        const updatedRules = await response.json();
        setLoyaltyRules(updatedRules);
        setEditingSection(null);
        setTempData({});
      } else {
        throw new Error('Failed to update loyalty rules');
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const startEditing = (section, data) => {
    setEditingSection(section);
    setTempData(JSON.parse(JSON.stringify(data)));
  };

  const cancelEditing = () => {
    setEditingSection(null);
    setTempData({});
  };

  const saveChanges = () => {
    updateLoyaltyRules(editingSection, tempData);
  };

  const addNewItem = (section, newItem) => {
    const updatedData = [...tempData, newItem];
    setTempData(updatedData);
  };

  const removeItem = (section, index) => {
    const updatedData = tempData.filter((_, i) => i !== index);
    setTempData(updatedData);
  };

  const updateItem = (index, field, value) => {
    const updatedData = [...tempData];
    if (field.includes('.')) {
      const [parentField, childField] = field.split('.');
      updatedData[index][parentField][childField] = value;
    } else {
      updatedData[index][field] = value;
    }
    setTempData(updatedData);
  };

  const updateSettings = (field, value) => {
    setTempData({
      ...tempData,
      [field]: value
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải cấu hình loyalty...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Lỗi: {error}</p>
          <button
            onClick={fetchLoyaltyRules}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin`)}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
          >
            ← Quay lại trang chủ Admin
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FaGift className="mr-3 text-purple-600" />
                Quản lý Loyalty Program
              </h1>
              <p className="text-gray-600 mt-2">Cấu hình quy tắc và chương trình khách hàng thân thiết</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={fetchLoyaltyRules}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                🔄 Làm mới
              </button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'settings', label: 'Cài đặt chung', icon: <FaCog /> },
                { id: 'customerTiers', label: 'Hạng khách hàng', icon: <FaStar /> },
                { id: 'earningRules', label: 'Quy tắc tích điểm', icon: <FaPlus /> },
                { id: 'redemptionRules', label: 'Quy tắc đổi điểm', icon: <FaGift /> },
                { id: 'specialEvents', label: 'Sự kiện đặc biệt', icon: <FaCalendar /> }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          {activeTab === 'settings' && (
            <SettingsTab
              settings={loyaltyRules?.settings}
              isEditing={editingSection === 'settings'}
              tempData={tempData}
              onStartEdit={() => startEditing('settings', loyaltyRules.settings)}
              onSave={saveChanges}
              onCancel={cancelEditing}
              onUpdate={updateSettings}
            />
          )}

          {activeTab === 'customerTiers' && (
            <CustomerTiersTab
              tiers={loyaltyRules?.customerTiers}
              isEditing={editingSection === 'customerTiers'}
              tempData={tempData}
              onStartEdit={() => {
                const tiersArray = Object.entries(loyaltyRules.customerTiers).map(([key, value]) => ({ id: key, ...value }));
                startEditing('customerTiers', tiersArray);
              }}
              onSave={saveChanges}
              onCancel={cancelEditing}
              onUpdate={updateItem}
              onAdd={addNewItem}
              onRemove={removeItem}
            />
          )}

          {activeTab === 'earningRules' && (
            <EarningRulesTab
              rules={loyaltyRules?.earningRules}
              isEditing={editingSection === 'earningRules'}
              tempData={tempData}
              onStartEdit={() => {
                const allRules = [];
                Object.entries(loyaltyRules.earningRules).forEach(([category, categoryData]) => {
                  if (categoryData.rules && Array.isArray(categoryData.rules)) {
                    categoryData.rules.forEach(rule => {
                      allRules.push({ ...rule, category, enabled: categoryData.enabled });
                    });
                  }
                });
                startEditing('earningRules', allRules);
              }}
              onSave={saveChanges}
              onCancel={cancelEditing}
              onUpdate={updateItem}
              onAdd={addNewItem}
              onRemove={removeItem}
            />
          )}

          {activeTab === 'redemptionRules' && (
            <RedemptionRulesTab
              rules={loyaltyRules?.redemptionRules}
              isEditing={editingSection === 'redemptionRules'}
              tempData={tempData}
              onStartEdit={() => {
                const allRules = [];
                Object.entries(loyaltyRules.redemptionRules).forEach(([category, categoryData]) => {
                  if (categoryData.rules && Array.isArray(categoryData.rules)) {
                    categoryData.rules.forEach(rule => {
                      allRules.push({ ...rule, category, enabled: categoryData.enabled });
                    });
                  }
                });
                startEditing('redemptionRules', allRules);
              }}
              onSave={saveChanges}
              onCancel={cancelEditing}
              onUpdate={updateItem}
              onAdd={addNewItem}
              onRemove={removeItem}
            />
          )}

          {activeTab === 'specialEvents' && (
            <SpecialEventsTab
              events={loyaltyRules?.specialEvents}
              isEditing={editingSection === 'specialEvents'}
              tempData={tempData}
              onStartEdit={() => {
                const eventsArray = Array.isArray(loyaltyRules.specialEvents) ? loyaltyRules.specialEvents : 
                  (typeof loyaltyRules.specialEvents === 'object' ? Object.entries(loyaltyRules.specialEvents).map(([key, value]) => ({ id: key, ...value })) : []);
                startEditing('specialEvents', eventsArray);
              }}
              onSave={saveChanges}
              onCancel={cancelEditing}
              onUpdate={updateItem}
              onAdd={addNewItem}
              onRemove={removeItem}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Settings Tab Component
const SettingsTab = ({ settings, isEditing, tempData, onStartEdit, onSave, onCancel, onUpdate }) => {
  if (!settings) return <div>Không có dữ liệu cài đặt</div>;

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Cài đặt chung</h2>
        {!isEditing ? (
          <button
            onClick={onStartEdit}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <FaEdit className="mr-2" /> Chỉnh sửa
          </button>
        ) : (
          <div className="flex space-x-2">
            <button
              onClick={onSave}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <FaSave className="mr-2" /> Lưu
            </button>
            <button
              onClick={onCancel}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              <FaTimes className="mr-2" /> Hủy
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Điểm hết hạn sau (ngày)
          </label>
          {isEditing ? (
            <input
              type="number"
              value={tempData.pointsExpiryDays || ''}
              onChange={(e) => onUpdate('pointsExpiryDays', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          ) : (
            <p className="text-gray-900">{settings.pointsExpiryDays} ngày</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Điểm tối thiểu để đổi
          </label>
          {isEditing ? (
            <input
              type="number"
              value={tempData.minimumRedemptionPoints || ''}
              onChange={(e) => onUpdate('minimumRedemptionPoints', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          ) : (
            <p className="text-gray-900">{settings.minimumRedemptionPoints} điểm</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Điểm tối đa mỗi lần đổi
          </label>
          {isEditing ? (
            <input
              type="number"
              value={tempData.maximumRedemptionPoints || ''}
              onChange={(e) => onUpdate('maximumRedemptionPoints', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          ) : (
            <p className="text-gray-900">{settings.maximumRedemptionPoints} điểm</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tỷ lệ quy đổi (điểm/VND)
          </label>
          {isEditing ? (
            <input
              type="number"
              step="0.01"
              value={tempData.pointsToVndRate || ''}
              onChange={(e) => onUpdate('pointsToVndRate', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          ) : (
            <p className="text-gray-900">{settings.pointsToVndRate} điểm = 1 VND</p>
          )}
        </div>
      </div>
    </div>
  );
};

// Customer Tiers Tab Component
const CustomerTiersTab = ({ tiers, isEditing, tempData, onStartEdit, onSave, onCancel, onUpdate, onAdd, onRemove }) => {
  if (!tiers) return <div>Không có dữ liệu hạng khách hàng</div>;

  const addNewTier = () => {
    const newTier = {
      name: 'Hạng mới',
      minPoints: 0,
      maxPoints: 999,
      multiplier: 1.0,
      benefits: []
    };
    onAdd('customerTiers', newTier);
  };

  // Convert object to array for display
  const tiersArray = Object.entries(tiers).map(([key, value]) => ({ id: key, ...value }));
  const displayData = isEditing ? tempData : tiersArray;

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Hạng khách hàng</h2>
        <div className="flex space-x-2">
          {isEditing && (
            <button
              onClick={addNewTier}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <FaPlus className="mr-2" /> Thêm hạng
            </button>
          )}
          {!isEditing ? (
            <button
              onClick={onStartEdit}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <FaEdit className="mr-2" /> Chỉnh sửa
            </button>
          ) : (
            <>
              <button
                onClick={onSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                <FaSave className="mr-2" /> Lưu
              </button>
              <button
                onClick={onCancel}
                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                <FaTimes className="mr-2" /> Hủy
              </button>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {displayData.map((tier, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            {isEditing && (
              <div className="flex justify-end mb-2">
                <button
                  onClick={() => onRemove('customerTiers', index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <FaTrash />
                </button>
              </div>
            )}
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Tên hạng</label>
              {isEditing ? (
                <input
                  type="text"
                  value={tier.name}
                  onChange={(e) => onUpdate(index, 'name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <p className="font-semibold text-lg">{tier.name}</p>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Điểm tối thiểu</label>
              {isEditing ? (
                <input
                  type="number"
                  value={tier.minPoints || 0}
                  onChange={(e) => onUpdate(index, 'minPoints', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <p>{(tier.minPoints || 0).toLocaleString()} điểm</p>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Điểm tối đa</label>
              {isEditing ? (
                <input
                  type="number"
                  value={tier.maxPoints || 999}
                  onChange={(e) => onUpdate(index, 'maxPoints', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <p>{(tier.maxPoints || 999).toLocaleString()} điểm</p>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Hệ số điểm</label>
              {isEditing ? (
                <input
                  type="number"
                  step="0.1"
                  value={tier.multiplier || 1.0}
                  onChange={(e) => onUpdate(index, 'multiplier', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <p>x{tier.multiplier || 1.0}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Quyền lợi</label>
              <ul className="text-sm text-gray-600">
                {tier.benefits?.map((benefit, benefitIndex) => (
                  <li key={benefitIndex} className="mb-1">• {benefit}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Earning Rules Tab Component
const EarningRulesTab = ({ rules, isEditing, tempData, onStartEdit, onSave, onCancel, onUpdate, onAdd, onRemove }) => {
  if (!rules) return <div>Không có dữ liệu quy tắc tích điểm</div>;

  const addNewRule = () => {
    const newRule = {
      id: 'new_rule_' + Date.now(),
      name: 'Quy tắc mới',
      type: 'percentage',
      value: 1.0,
      description: 'Mô tả quy tắc',
      conditions: {
        minAmount: 0
      }
    };
    onAdd('earningRules', newRule);
  };

  // Flatten all rules from different categories
  const allRules = [];
  Object.entries(rules).forEach(([category, categoryData]) => {
    if (categoryData.rules && Array.isArray(categoryData.rules)) {
      categoryData.rules.forEach(rule => {
        allRules.push({ ...rule, category, enabled: categoryData.enabled });
      });
    }
  });
  
  const displayData = isEditing ? tempData : allRules;

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Quy tắc tích điểm</h2>
        <div className="flex space-x-2">
          {isEditing && (
            <button
              onClick={addNewRule}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <FaPlus className="mr-2" /> Thêm quy tắc
            </button>
          )}
          {!isEditing ? (
            <button
              onClick={onStartEdit}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <FaEdit className="mr-2" /> Chỉnh sửa
            </button>
          ) : (
            <>
              <button
                onClick={onSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                <FaSave className="mr-2" /> Lưu
              </button>
              <button
                onClick={onCancel}
                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                <FaTimes className="mr-2" /> Hủy
              </button>
            </>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {displayData.map((rule, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                {isEditing ? (
                  <input
                    type="text"
                    value={rule.name}
                    onChange={(e) => onUpdate(index, 'name', e.target.value)}
                    className="font-semibold text-lg border-b border-gray-300 focus:outline-none focus:border-blue-500"
                  />
                ) : (
                  <h3 className="font-semibold text-lg">{rule.name}</h3>
                )}
                <span className={`ml-3 px-2 py-1 rounded-full text-xs ${
                  rule.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {rule.active ? 'Hoạt động' : 'Tạm dừng'}
                </span>
              </div>
              {isEditing && (
                <button
                  onClick={() => onRemove('earningRules', index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <FaTrash />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Loại</label>
                {isEditing ? (
                  <select
                    value={rule.type}
                    onChange={(e) => onUpdate(index, 'type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="purchase">Mua hàng</option>
                    <option value="referral">Giới thiệu</option>
                    <option value="birthday">Sinh nhật</option>
                    <option value="review">Đánh giá</option>
                  </select>
                ) : (
                  <p>{rule.type}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Điểm thưởng</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={rule.points}
                    onChange={(e) => onUpdate(index, 'points', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <p>{rule.points} điểm</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                {isEditing ? (
                  <select
                    value={rule.active}
                    onChange={(e) => onUpdate(index, 'active', e.target.value === 'true')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="true">Hoạt động</option>
                    <option value="false">Tạm dừng</option>
                  </select>
                ) : (
                  <p>{rule.active ? 'Hoạt động' : 'Tạm dừng'}</p>
                )}
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
              {isEditing ? (
                <textarea
                  value={rule.description}
                  onChange={(e) => onUpdate(index, 'description', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <p className="text-gray-600">{rule.description}</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Redemption Rules Tab Component
const RedemptionRulesTab = ({ rules, isEditing, tempData, onStartEdit, onSave, onCancel, onUpdate, onAdd, onRemove }) => {
  if (!rules) return <div>Không có dữ liệu quy tắc đổi điểm</div>;

  const addNewRule = () => {
    const newRule = {
      id: 'new_redemption_' + Date.now(),
      name: 'Quy tắc đổi điểm mới',
      pointsCost: 100,
      discountType: 'percentage',
      discountValue: 5,
      description: 'Mô tả quy tắc đổi điểm',
      conditions: {
        minOrderAmount: 500,
        validDays: 30
      }
    };
    onAdd('redemptionRules', newRule);
  };

  // Flatten all redemption rules from different categories
  const allRules = [];
  Object.entries(rules).forEach(([category, categoryData]) => {
    if (categoryData.rules && Array.isArray(categoryData.rules)) {
      categoryData.rules.forEach(rule => {
        allRules.push({ ...rule, category, enabled: categoryData.enabled });
      });
    }
  });
  
  const displayData = isEditing ? tempData : allRules;

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Quy tắc đổi điểm</h2>
        <div className="flex space-x-2">
          {isEditing && (
            <button
              onClick={addNewRule}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <FaPlus className="mr-2" /> Thêm quy tắc
            </button>
          )}
          {!isEditing ? (
            <button
              onClick={onStartEdit}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <FaEdit className="mr-2" /> Chỉnh sửa
            </button>
          ) : (
            <>
              <button
                onClick={onSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                <FaSave className="mr-2" /> Lưu
              </button>
              <button
                onClick={onCancel}
                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                <FaTimes className="mr-2" /> Hủy
              </button>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {displayData.map((rule, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                {isEditing ? (
                  <input
                    type="text"
                    value={rule.name}
                    onChange={(e) => onUpdate(index, 'name', e.target.value)}
                    className="font-semibold text-lg border-b border-gray-300 focus:outline-none focus:border-blue-500"
                  />
                ) : (
                  <h3 className="font-semibold text-lg">{rule.name}</h3>
                )}
                <span className={`ml-3 px-2 py-1 rounded-full text-xs ${
                  rule.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {rule.active ? 'Hoạt động' : 'Tạm dừng'}
                </span>
              </div>
              {isEditing && (
                <button
                  onClick={() => onRemove('redemptionRules', index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <FaTrash />
                </button>
              )}
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Loại</label>
                {isEditing ? (
                  <select
                    value={rule.type}
                    onChange={(e) => onUpdate(index, 'type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="discount">Giảm giá</option>
                    <option value="gift">Quà tặng</option>
                    <option value="voucher">Voucher</option>
                  </select>
                ) : (
                  <p>{rule.type}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Chi phí điểm</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={rule.pointsCost}
                    onChange={(e) => onUpdate(index, 'pointsCost', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <p>{rule.pointsCost} điểm</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Giá trị</label>
                {isEditing ? (
                  <input
                    type="number"
                    value={rule.value}
                    onChange={(e) => onUpdate(index, 'value', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <p>{rule.value?.toLocaleString()} VND</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
                {isEditing ? (
                  <textarea
                    value={rule.description}
                    onChange={(e) => onUpdate(index, 'description', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <p className="text-gray-600">{rule.description}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Special Events Tab Component
const SpecialEventsTab = ({ events, isEditing, tempData, onStartEdit, onSave, onCancel, onUpdate, onAdd, onRemove }) => {
  if (!events) return <div>Không có dữ liệu sự kiện đặc biệt</div>;

  const addNewEvent = () => {
    const newEvent = {
      id: 'new_event_' + Date.now(),
      name: 'Sự kiện mới',
      description: 'Mô tả sự kiện',
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      multiplier: 2.0,
      conditions: {},
      active: true
    };
    onAdd('specialEvents', newEvent);
  };

  // Convert events object to array if it's an object
  const eventsArray = Array.isArray(events) ? events : 
    (typeof events === 'object' ? Object.entries(events).map(([key, value]) => ({ id: key, ...value })) : []);
  
  const displayData = isEditing ? tempData : eventsArray;

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Sự kiện đặc biệt</h2>
        <div className="flex space-x-2">
          {isEditing && (
            <button
              onClick={addNewEvent}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <FaPlus className="mr-2" /> Thêm sự kiện
            </button>
          )}
          {!isEditing ? (
            <button
              onClick={onStartEdit}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <FaEdit className="mr-2" /> Chỉnh sửa
            </button>
          ) : (
            <>
              <button
                onClick={onSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                <FaSave className="mr-2" /> Lưu
              </button>
              <button
                onClick={onCancel}
                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                <FaTimes className="mr-2" /> Hủy
              </button>
            </>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {displayData.map((event, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                {isEditing ? (
                  <input
                    type="text"
                    value={event.name}
                    onChange={(e) => onUpdate(index, 'name', e.target.value)}
                    className="font-semibold text-lg border-b border-gray-300 focus:outline-none focus:border-blue-500"
                  />
                ) : (
                  <h3 className="font-semibold text-lg">{event.name}</h3>
                )}
                <span className={`ml-3 px-2 py-1 rounded-full text-xs ${
                  event.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {event.active ? 'Hoạt động' : 'Tạm dừng'}
                </span>
              </div>
              {isEditing && (
                <button
                  onClick={() => onRemove('specialEvents', index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <FaTrash />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ngày bắt đầu</label>
                {isEditing ? (
                  <input
                    type="date"
                    value={event.startDate}
                    onChange={(e) => onUpdate(index, 'startDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <p>{new Date(event.startDate).toLocaleDateString('vi-VN')}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ngày kết thúc</label>
                {isEditing ? (
                  <input
                    type="date"
                    value={event.endDate}
                    onChange={(e) => onUpdate(index, 'endDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <p>{new Date(event.endDate).toLocaleDateString('vi-VN')}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hệ số nhân</label>
                {isEditing ? (
                  <input
                    type="number"
                    step="0.1"
                    value={event.multiplier}
                    onChange={(e) => onUpdate(index, 'multiplier', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                ) : (
                  <p>x{event.multiplier}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                {isEditing ? (
                  <select
                    value={event.active}
                    onChange={(e) => onUpdate(index, 'active', e.target.value === 'true')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="true">Hoạt động</option>
                    <option value="false">Tạm dừng</option>
                  </select>
                ) : (
                  <p>{event.active ? 'Hoạt động' : 'Tạm dừng'}</p>
                )}
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
              {isEditing ? (
                <textarea
                  value={event.description}
                  onChange={(e) => onUpdate(index, 'description', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <p className="text-gray-600">{event.description}</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LoyaltyManagement;