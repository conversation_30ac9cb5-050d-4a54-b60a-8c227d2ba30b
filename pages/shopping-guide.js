import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { fetchStore } from '../utils/storeProvider';

const ShoppingGuidePage = () => {
  const router = useRouter();
  
  useEffect(() => {
    async function redirect() {
      try {
        const allstores = await fetchStore();
        if (allstores && allstores.length > 0) {
          const store = allstores[0];
          const storeId = store.storeId;
          router.push(`/${storeId}/shopping-guide`);
        }
      } catch (error) {
        console.error('Error redirecting to store page:', error);
      }
    }
    
    redirect();
  }, [router]);
  
  return null;
};

export default ShoppingGuidePage; 