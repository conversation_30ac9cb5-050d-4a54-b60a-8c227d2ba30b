import React from 'react'
import MarketplaceLogin from '../components/formComponents/MarketplaceLogin'

class MarketAuth extends React.Component {
  state = { isAuthenticated: false }

  signIn = async (form) => {
    const { username, password } = form
    // Implement your sign in logic here
    // Update authentication state
  }

  signUp = async (form) => {
    const { username, email, password } = form
    // Implement your sign up logic here
    // Handle confirmation if needed
  }

  render() {
    return (
      <div className="flex flex-col">
        <div className="max-w-fw flex flex-col">
          <MarketplaceLogin
            signIn={this.signIn}
            signUp={this.signUp}
            facebookSignIn={this.handleFacebookSignIn}
            appleSignIn={this.handleAppleSignIn}
            googleSignIn={this.handleGoogleSignIn}
            twitterSignIn={this.handleTwitterSignIn}
          />
        </div>
      </div>
    )
  }
}

export default MarketAuth 