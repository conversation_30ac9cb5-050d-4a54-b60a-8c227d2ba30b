import React, { useEffect, useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { motion } from 'framer-motion'

function Error({ statusCode, hasError, errorMessage }) {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Initial animation for the container
  const containerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: "easeOut",
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };
  
  // Animation for child elements
  const itemVariants = {
    hidden: { opacity: 0, y: -5 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };
  
  // Choose appropriate illustration based on status code
  const getIllustration = () => {
    if (statusCode === 404) {
      return '/illustrations/404-illustration.svg';
    } else if (statusCode >= 500) {
      return '/illustrations/500-illustration.svg';
    }
    return '/illustrations/error-illustration.svg';
  };
  
  // Handle alternate illustration if the SVG is not available
  const handleImageError = (e) => {
    e.target.onerror = null;
    e.target.src = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp";
  };

  // Select header color based on error type
  const getHeaderColors = () => {
    if (statusCode === 404) {
      return 'from-blue-400 to-blue-500';
    } else if (statusCode >= 500) {
      return 'from-amber-400 to-amber-500';
    }
    return 'from-rose-400 to-rose-500';
  };

  // Select button color based on error type
  const getButtonColor = () => {
    if (statusCode === 404) {
      return 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-400';
    } else if (statusCode >= 500) {
      return 'bg-amber-500 hover:bg-amber-600 focus:ring-amber-400';
    }
    return 'bg-rose-500 hover:bg-rose-600 focus:ring-rose-400';
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-5 bg-gradient-to-b from-white to-gray-50">
      <Head>
        <title>{statusCode ? `Rất tiếc! Lỗi ${statusCode}` : 'Rất tiếc! Đã xảy ra lỗi'}</title>
        <meta name="description" content={`Lỗi ${statusCode || ''} - Chúng tôi xin lỗi vì sự bất tiện này`} />
      </Head>
      
      {mounted && (
        <motion.div 
          className="max-w-lg w-full"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <div className="bg-white rounded-2xl shadow-md overflow-hidden border border-gray-100">
            {/* Error Status Header */}
            <div className={`bg-gradient-to-r ${getHeaderColors()} px-6 py-6 text-center`}>
              <motion.h1 
                className="text-4xl font-bold text-white mb-1"
                variants={itemVariants}
              >
                {statusCode ? `${statusCode}` : 'Lỗi!'}
              </motion.h1>
              <motion.p 
                className="text-white/90 text-lg font-medium"
                variants={itemVariants}
              >
                {statusCode === 404 
                  ? "Không tìm thấy trang" 
                  : statusCode >= 500 
                    ? "Máy chủ đang bảo trì"
                    : "Đã xảy ra lỗi không mong muốn"}
              </motion.p>
            </div>
            
            {/* Illustration */}
            <motion.div 
              className="flex justify-center -mt-8 mb-4"
              variants={itemVariants}
            >
              <div className="w-28 h-28 bg-white rounded-full shadow-sm flex items-center justify-center p-2 border border-gray-100">
                <img 
                  src={getIllustration()} 
                  alt="Hình minh họa lỗi" 
                  className="w-20 h-20 object-contain"
                  onError={handleImageError}
                />
              </div>
            </motion.div>
            
            {/* Error Message Content */}
            <div className="px-6 pb-6">
              <motion.p 
                className="text-gray-600 text-center mb-5 leading-relaxed"
                variants={itemVariants}
              >
                {statusCode === 404
                  ? "Trang bạn đang tìm kiếm có thể đã bị di chuyển hoặc không tồn tại. Đừng lo lắng!"
                  : statusCode >= 500
                    ? "Máy chủ của chúng tôi đang gặp trục trặc nhỏ. Vui lòng quay lại sau."
                    : "Chúng tôi đã gặp một vấn đề nhỏ. Chúng tôi đang khắc phục."}
              </motion.p>
              
              {errorMessage && (
                <motion.div 
                  className="mb-6 p-3 bg-gray-50 rounded-lg border border-gray-100"
                  variants={itemVariants}
                >
                  <p className="text-gray-600 text-sm">{errorMessage}</p>
                </motion.div>
              )}
              
              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                <motion.div 
                  className="flex-1"
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link href="/" className="block w-full">
                    <button className={`w-full py-2.5 px-4 ${getButtonColor()} text-white font-medium rounded-lg transition-all duration-150 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-opacity-50`}>
                      Về Trang Chủ
                    </button>
                  </Link>
                </motion.div>
                
                <motion.div
                  className="flex-1"
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <button 
                    onClick={() => window.location.reload()} 
                    className="w-full py-2.5 px-4 bg-white hover:bg-gray-50 text-gray-600 font-medium rounded-lg transition-all duration-150 shadow-sm border border-gray-200 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-opacity-50"
                  >
                    Thử Lại
                  </button>
                </motion.div>
              </div>
            </div>
          </div>
          
          {/* Footer Message */}
          <motion.p 
            className="text-center text-gray-400 text-sm mt-5"
            variants={itemVariants}
          >
            Cần hỗ trợ? Đội ngũ hỗ trợ của chúng tôi luôn sẵn sàng giúp đỡ bạn.
          </motion.p>
        </motion.div>
      )}
    </div>
  )
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404
  const errorMessage = err ? (err.message || String(err)) : null
  
  // Log the error server-side
  if (err) {
    console.error('Error in _error.js:', err)
  }
  
  return { 
    statusCode,
    hasError: !!err,
    errorMessage
  }
}

export default Error
