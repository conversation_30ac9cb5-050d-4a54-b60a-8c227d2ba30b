import { useState, useEffect, useCallback } from 'react';
import Section from '../components/section';
import FAQSection from '../components/faq/FAQSection';

const FAQPage = () => {
  const [faqs, setFaqs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchFAQs = useCallback(async () => {
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${API_URL}/api/faqs`);
      if (!response.ok) throw new Error('Failed to fetch FAQs');
      const data = await response.json();
      setFaqs(data);
    } catch (err) {
      setError('Error loading FAQs');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchFAQs();
  }, [fetchFAQs]);

  return (
    <div className="space-y-12">
      <Section>
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 transform -skew-y-3 opacity-10 dark:opacity-20" />
          <div className="relative">
            <h2 className="text-2xl font-bold mb-8 text-gray-900 dark:text-white">
              Câu hỏi thường gặp
            </h2>
            <FAQSection faqs={faqs} loading={loading} error={error} />
          </div>
        </div>
      </Section>
    </div>
  );
};

export default FAQPage;
