import { useEffect, useState, useContext } from "react"
import { fetchStore } from "../utils/storeProvider"
import DefaultRootLayout from "../templates/default/root"
import MarketplaceRootLayout from "../templates/marketplace/root"
import ShopeeRootLayout from "../templates/shopee/root"
import Mag1RootLayout from "../templates/mag1/root"
import SheinRootLayout from "../templates/shein/root"
import { useRouter } from "next/router"

function AllShops({ allshops = [] }) {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  
  // Redirect to single shop if only one shop exists
  useEffect(() => {
    if (allshops.length === 1) {
      router.push(`/${allshops[0].storeId}`)
    } else {
      setLoading(false)
    }
  }, [allshops, router])

  const getLayoutComponent = (template) => {
    switch(template) {
      case 'marketplace':
        return MarketplaceRootLayout
      case 'shopee':
        return ShopeeRootLayout
      case 'mag1':
        return Mag1RootLayout
      case 'shein':
        return SheinRootLayout
      default:
        return DefaultRootLayout
    }
  }

  // Get template from first shop or use default
  //const template = 'default' // or get from configuration/props
  const template = 'default'
  const LayoutComponent = getLayoutComponent(template)
  const currentUrl = typeof window !== "undefined" ? window.location.href : ""

  // Show loading state or layout component with shops
  if (loading && allshops.length === 1) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
    </div>
  }

  return (
    <LayoutComponent 
      allshops={allshops}
      currentUrl={currentUrl}
    />
  )
}

export async function getStaticProps() {
  const allShops = await fetchStore()
  const filteredShops = allShops.filter(
    (store) => !store.privatestore || store.privatestore !== "1"
  )

  const shopsInfo = filteredShops.map((store) => ({
    storeId: store.storeId,
    name: store.name,
    logo: store.logo,
    layouttemplate: store.layouttemplate || 'default'
  }))

  return {
    props: {
      allshops: shopsInfo,
    },
  }
}

export default AllShops
