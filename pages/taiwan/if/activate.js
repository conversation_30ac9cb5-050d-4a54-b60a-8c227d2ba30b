import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useRouter } from 'next/router';
import Head from 'next/head';

const ActivateIFCard = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    phoneNumber: '',
    productId: '',
    orderId: ''
  });
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [activationResult, setActivationResult] = useState(null);

  useEffect(() => {
    // Fetch available products when component mounts
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await axios.get('/api/payment/if/products');
      if (response.data.success) {
        setProducts(response.data.data);
      } else {
        setError('Failed to load product list');
      }
    } catch (err) {
      setError('Error loading product list');
      console.error('Error fetching products:', err);
    } finally {
      setIsLoadingProducts(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    const requiredFields = ['phoneNumber', 'productId'];
    const missingFields = requiredFields.filter(field => !formData[field]);
    
    if (missingFields.length > 0) {
      setError(`Please fill out the following fields: ${missingFields.join(', ')}`);
      return false;
    }
    
    // Validate phone number format - Taiwanese numbers are 10 digits
    if (!/^\d{10}$/.test(formData.phoneNumber)) {
      setError('Phone number must be 10 digits');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Reset states
    setError(null);
    setSuccess(null);
    setActivationResult(null);
    
    // Validate form
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Make the API call
      const response = await axios.post('/api/payment/if/activate', formData);
      
      if (response.data.success) {
        setSuccess('Recharge successful!');
        setActivationResult(response.data.data);
      } else {
        setError(response.data.error || 'Failed to process recharge. Please try again.');
      }
    } catch (err) {
      console.error('Error activating card:', err);
      setError(err.response?.data?.error || 'An unexpected error occurred. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCheckStatus = async () => {
    if (!activationResult?.transactionId) {
      setError('No transaction ID available to check status');
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await axios.get(`/api/payment/if/status?transactionId=${activationResult.transactionId}`);
      
      if (response.data.success) {
        setSuccess(`Transaction status: ${response.data.data.status}`);
        setActivationResult(prev => ({
          ...prev,
          status: response.data.data.status,
          statusMessage: response.data.data.message,
          lastChecked: new Date().toISOString()
        }));
      } else {
        setError(response.data.error || 'Failed to check transaction status');
      }
    } catch (err) {
      setError(err.response?.data?.error || 'An error occurred while checking transaction status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setFormData({
      phoneNumber: '',
      productId: '',
      orderId: ''
    });
    setError(null);
    setSuccess(null);
    setActivationResult(null);
  };

  // Find selected product details
  const selectedProduct = products.find(p => p.id === formData.productId);

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <Head>
        <title>VRC Card Activation</title>
      </Head>
      
      <h1 className="text-3xl font-bold mb-6">Chuan Hung VRC System Recharge</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <strong>Success:</strong> {success}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-gray-700 font-semibold mb-2">
              Phone Number*
            </label>
            <input
              type="text"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded"
              placeholder="Enter 10-digit phone number"
              maxLength={10}
            />
            <p className="text-xs text-gray-500 mt-1">Format: 10 digits without spaces</p>
          </div>
          
          <div>
            <label className="block text-gray-700 font-semibold mb-2">
              Product*
            </label>
            <select
              name="productId"
              value={formData.productId}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded"
              disabled={isLoadingProducts}
            >
              <option value="">Select a product</option>
              {products.map(product => (
                <option key={product.id} value={product.id}>
                  {product.name} - NT${product.price}
                </option>
              ))}
            </select>
            {isLoadingProducts && <p className="text-xs text-gray-500 mt-1">Loading products...</p>}
          </div>
          
          <div>
            <label className="block text-gray-700 font-semibold mb-2">
              Order ID <span className="font-normal text-gray-500">(Optional)</span>
            </label>
            <input
              type="text"
              name="orderId"
              value={formData.orderId}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded"
              placeholder="Enter order ID (optional)"
            />
          </div>
        </div>

        {selectedProduct && (
          <div className="mt-4 p-3 bg-blue-50 rounded">
            <h3 className="font-semibold">Selected Product:</h3>
            <p>{selectedProduct.name}</p>
            <p className="font-bold">Price: NT${selectedProduct.price}</p>
          </div>
        )}
        
        <div className="mt-6 flex flex-wrap gap-3">
          <button
            type="submit"
            disabled={isLoading}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            {isLoading ? 'Processing...' : 'Process Recharge'}
          </button>
          
          <button
            type="button"
            onClick={handleReset}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
          >
            Reset Form
          </button>
        </div>
      </form>
      
      {activationResult && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-4">Recharge Result</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="font-semibold">Transaction ID:</p>
              <p className="text-gray-700">{activationResult.transactionId}</p>
            </div>
            
            <div>
              <p className="font-semibold">Result Code:</p>
              <p className="text-gray-700">{activationResult.resultCode}</p>
            </div>
            
            <div>
              <p className="font-semibold">Product:</p>
              <p className="text-gray-700">{activationResult.productName}</p>
            </div>
            
            <div>
              <p className="font-semibold">Amount:</p>
              <p className="text-gray-700">NT${activationResult.cardValue}</p>
            </div>
            
            <div>
              <p className="font-semibold">Date:</p>
              <p className="text-gray-700">
                {new Date(activationResult.activationDate).toLocaleString()}
              </p>
            </div>
            
            {activationResult.status && (
              <div>
                <p className="font-semibold">Status:</p>
                <p className="text-gray-700">{activationResult.status}</p>
              </div>
            )}
            
            {activationResult.message && (
              <div className="col-span-2">
                <p className="font-semibold">Message:</p>
                <p className="text-gray-700">{activationResult.message}</p>
              </div>
            )}
            
            {activationResult.lastChecked && (
              <div>
                <p className="font-semibold">Last Checked:</p>
                <p className="text-gray-700">
                  {new Date(activationResult.lastChecked).toLocaleString()}
                </p>
              </div>
            )}
          </div>
          
          <div className="mt-4">
            <button
              type="button"
              onClick={handleCheckStatus}
              disabled={isLoading}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              {isLoading ? 'Checking...' : 'Check Status'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ActivateIFCard; 