import { useState, useContext, useEffect } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { SiteContext } from '../context/mainContext'
import DENOMINATION from '../utils/currencyProvider'
import Image from '../components/Image'
import { titleIfy, slugify } from '../utils/helpers'
import CartLink from '../components/CartLink'
function PurchaseHistory() {
  const [orders, setOrders] = useState([])
  const { user } = useContext(SiteContext)

  useEffect(() => {
    // Fetch orders from localStorage
    const fetchOrders = () => {
      const storedOrders = localStorage.getItem('orders')
      if (storedOrders) {
        const parsedOrders = JSON.parse(storedOrders)
        // Sort orders by date, most recent first
        const sortedOrders = parsedOrders.sort((a, b) => b.timestamp - a.timestamp)
        setOrders(sortedOrders)
      }
    }

    fetchOrders()
  }, [])

  return (
    <>
      <CartLink />
      <div className="flex flex-col items-center pb-10">
        <Head>
          <title>ShopMe - Lịch sử mua hàng</title>
          <meta name="description" content="Purchase History" />
          <meta property="og:title" content="ShopMe - Purchase History" key="title" />
        </Head>

        <div className="flex flex-col w-full c_large:w-c_large">
          <div className="pt-10 pb-8">
            <h1 className="text-5xl font-light">LỊCH SỬ MUA HÀNG</h1>
          </div>

          {orders.length === 0 ? (
            <div className="text-center py-10">
              <h2 className="text-2xl">Chưa có đơn hàng nào</h2>
              <Link href="/" className="text-blue-500 hover:text-blue-700 mt-4 inline-block">
                Tiếp tục mua sắm
              </Link>
            </div>
          ) : (
            <div className="flex flex-col space-y-8">
              {orders.map((order, orderIndex) => (
                <div key={orderIndex} className="border rounded-lg p-6 shadow-sm">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <p className="text-gray-600">Đơn hàng #{order.orderId}</p>
                      <p className="text-gray-600">Shop: {order.store}</p>
                      <p className="text-gray-600">
                        Ngày: {new Date(order.timestamp).toLocaleDateString('vi-VN')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">
                        Tổng: {DENOMINATION + order.total.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-600">
                        Trạng thái: {order.status || 'Đang xử lý'}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {order.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex border-t pt-4">
                        <div className="w-20 h-20 flex-shrink-0">
                          <Image
                            className="w-full h-full object-cover"
                            src={Array.isArray(item.image) ? item.image[0] : item.image}
                            alt={item.name}
                            width={80}
                            height={80}
                          />
                        </div>
                        <div className="ml-4 flex-grow">
                          <Link 
                            href={`/${order.store}/product/${slugify(item.sku)}`}
                            className="text-lg hover:text-blue-600"
                          >
                            {titleIfy(item.name)}
                          </Link>
                          <p className="text-gray-600">SKU: {item.sku}</p>
                          <p className="text-gray-600">Số lượng: {item.quantity}</p>
                          <p className="text-gray-800">
                            {DENOMINATION + (item.price * item.quantity).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default PurchaseHistory 