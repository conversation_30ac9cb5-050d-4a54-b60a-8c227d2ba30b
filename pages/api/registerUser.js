import axios from 'axios';

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const apiUrl = 'https://workflow.abnasia.org/webhook/06ad7897-0eaa-46d1-b8b8-dff4635453829d3e';

    const payload  = req.body;
    axios.post(apiUrl, payload)
      .then(response => {
        // Handle successful response
        console.log('User Registered Successfully:', response.data);
        res.status(200).json({ message: 'User Registered Successfully' });
      })
      .catch(error => {
        // Handle error
        console.error('Error registering user:', error.response ? error.response.data : error.message);
        res.status(500).json({ error: 'Failed to register user' });
      });
  } else {
    res.status(405).json({ error: 'Method Not Allowed' }); // Send a 405 Method Not Allowed response for non-POST requests
  }
}
