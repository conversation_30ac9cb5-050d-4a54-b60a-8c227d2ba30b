import { promises as fs } from 'fs';
import path from 'path';
import { IncomingForm } from 'formidable';
import { v4 as uuidv4 } from 'uuid';
import { 
  addDocument, 
  updateCustomerDocumentIndex
} from '../../../../utils/orderUtils';

// Config for formidable to disable file write to disk (we'll handle that ourselves)
export const config = {
  api: {
    bodyParser: false,
  },
};

// Allowed file types
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

// Max file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Base data directory
const dataDir = path.join(process.cwd(), 'data');

/**
 * Parse multipart form data
 * @param {Object} req - The HTTP request object
 * @returns {Promise<Object>} Parsed fields and file
 */
function parseMultipartForm(req) {
  return new Promise((resolve, reject) => {
    const form = new IncomingForm({
      keepExtensions: true,
      maxFileSize: MAX_FILE_SIZE
    });

    form.parse(req, (err, fields, files) => {
      if (err) {
        reject(err);
        return;
      }

      // Get file from files object (first file)
      const file = files.document ? files.document[0] : null;

      // Convert fields from arrays to single values
      const processedFields = {};
      Object.keys(fields).forEach(key => {
        processedFields[key] = Array.isArray(fields[key]) ? fields[key][0] : fields[key];
      });

      resolve({
        fields: processedFields,
        file
      });
    });
  });
}

/**
 * Save uploaded file to the appropriate location
 * @param {Object} file - The uploaded file
 * @param {string} customerId - The customer ID
 * @param {string} documentType - The type of document
 * @returns {Promise<Object>} File metadata
 */
async function saveFile(file, customerId, documentType) {
  if (!file) throw new Error('No file provided');

  // Validate file type
  if (!ALLOWED_TYPES.includes(file.mimetype)) {
    throw new Error(`Invalid file type: ${file.mimetype}. Allowed types: ${ALLOWED_TYPES.join(', ')}`);
  }

  // Create customer directory if it doesn't exist
  const customerDir = path.join(dataDir, 'customers', customerId);
  const documentsDir = path.join(customerDir, 'documents');

  try {
    await fs.mkdir(customerDir, { recursive: true });
    await fs.mkdir(documentsDir, { recursive: true });
  } catch (error) {
    console.error('Error creating directories:', error);
    throw new Error('Failed to create customer directories');
  }

  // Generate unique filename with original extension
  const extension = path.extname(file.originalFilename);
  const newFilename = `${documentType}_${Date.now()}${extension}`;
  const filePath = path.join(documentsDir, newFilename);

  // Read file data and save it
  try {
    const data = await fs.readFile(file.filepath);
    await fs.writeFile(filePath, data);
    
    // Delete the temporary file
    try {
      await fs.unlink(file.filepath);
    } catch (unlinkError) {
      console.error('Error removing temporary file:', unlinkError);
      // Continue execution even if temporary file cleanup fails
    }
    
    // Create file metadata
    const fileMetadata = {
      filename: newFilename,
      originalFilename: file.originalFilename,
      type: file.mimetype,
      size: file.size,
      path: filePath,
      documentType: documentType,
      uploadDate: new Date().toISOString(),
      customerId: customerId
    };
    
    return fileMetadata;
  } catch (error) {
    console.error('Error saving file:', error);
    throw new Error('Failed to save uploaded file');
  }
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Parse multipart form data
    const { fields, file } = await parseMultipartForm(req);

    // Check required fields
    const customerId = fields.customerId;
    const documentType = fields.documentType;
    const orderId = fields.orderId || null;

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID is required'
      });
    }

    if (!documentType) {
      return res.status(400).json({
        success: false,
        message: 'Document type is required'
      });
    }

    if (!file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Save the file
    const savedFile = await saveFile(file, customerId, documentType);

    // Add the document to the central documents.json file
    const documentData = {
      id: uuidv4(),
      customerId,
      documentType,
      orderId,
      filename: savedFile.filename,
      originalFilename: savedFile.originalFilename,
      type: savedFile.type,
      size: savedFile.size,
      path: savedFile.path,
      uploadDate: savedFile.uploadDate,
      url: `/api/customer/document/${customerId}/${savedFile.filename}`
    };
    
    addDocument(documentData);

    // Document-order relationships are now handled through the central documents.json file
    // Customer order files are no longer maintained

    // Return success response with file details
    return res.status(200).json({
      success: true,
      message: 'Document uploaded successfully',
      filename: savedFile.filename,
      path: savedFile.path
    });
  } catch (error) {
    console.error('Error uploading document:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while uploading the document',
      error: error.message
    });
  }
} 