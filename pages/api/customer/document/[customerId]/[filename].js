import { readFile, stat } from 'fs/promises';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { customerId, filename } = req.query;

    if (!customerId || !filename) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID and filename are required'
      });
    }

    // Validate the filename to prevent directory traversal attacks
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    // Construct the file path
    const filePath = path.join(
      process.cwd(),
      'data',
      'customers',
      customerId,
      'documents',
      filename
    );

    try {
      // Check if the file exists and get its stats
      const fileStat = await stat(filePath);
      
      if (!fileStat.isFile()) {
        return res.status(404).json({
          success: false,
          message: 'File not found'
        });
      }

      // Determine the content type based on file extension
      const ext = path.extname(filename).toLowerCase();
      let contentType = 'application/octet-stream';

      switch (ext) {
        case '.pdf':
          contentType = 'application/pdf';
          break;
        case '.jpg':
        case '.jpeg':
          contentType = 'image/jpeg';
          break;
        case '.png':
          contentType = 'image/png';
          break;
        case '.gif':
          contentType = 'image/gif';
          break;
      }

      // Read the file
      const fileData = await readFile(filePath);

      // Set appropriate headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
      res.setHeader('Content-Length', fileStat.size);

      // Send the file
      res.status(200).send(fileData);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({
          success: false,
          message: 'File not found'
        });
      }
      throw error;
    }
  } catch (error) {
    console.error('Error serving document file:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while serving the document file',
      error: error.message
    });
  }
} 