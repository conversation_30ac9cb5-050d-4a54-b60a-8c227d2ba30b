import fs from 'fs';
import path from 'path';

/**
 * Customer API endpoint for updating order item usage information
 * 
 * This endpoint allows customers to save usage tracking information
 * for SIM cards and topup cards they have purchased.
 * 
 * Supported methods:
 * - POST: Update usage information for an order item
 * - GET: Retrieve usage information for customer's orders
 */

const ORDERS_FILE_PATH = path.join(process.cwd(), 'data', 'orders.json');

// Helper function to read orders from file
const readOrdersFile = () => {
  try {
    if (!fs.existsSync(ORDERS_FILE_PATH)) {
      return [];
    }
    const fileContents = fs.readFileSync(ORDERS_FILE_PATH, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading orders file:', error);
    return [];
  }
};

// Helper function to write orders to file
const writeOrdersFile = (orders) => {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(ORDERS_FILE_PATH);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    fs.writeFileSync(ORDERS_FILE_PATH, JSON.stringify(orders, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('Error writing orders file:', error);
    return false;
  }
};

// Helper function to validate customer access to order
const validateCustomerAccess = (order, customerInfo) => {
  // Check if customer email matches
  if (order.customerEmail && customerInfo.email) {
    return order.customerEmail.toLowerCase() === customerInfo.email.toLowerCase();
  }
  
  // Check if customer phone matches
  if (order.customerPhone && customerInfo.phone) {
    return order.customerPhone === customerInfo.phone;
  }
  
  // Check if customer ID matches
  if (order.customerId && customerInfo.customerId) {
    return order.customerId === customerInfo.customerId;
  }
  
  return false;
};

export default async function handler(req, res) {
  const { method } = req;
  
  console.log(`[CUSTOMER_ORDER_USAGE_API] ${method} request received`);
  
  try {
    switch (method) {
      case 'POST':
        return await handleUpdateUsage(req, res);
      case 'GET':
        return await handleGetUsage(req, res);
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ 
          success: false, 
          message: `Method ${method} Not Allowed` 
        });
    }
  } catch (error) {
    console.error('[CUSTOMER_ORDER_USAGE_API_ERROR]', error);
    return res.status(500).json({
      success: false,
      message: 'Internal Server Error',
      error: error.message
    });
  }
}

// Handle POST request to update usage information
async function handleUpdateUsage(req, res) {
  const { 
    orderId, 
    itemIndex, 
    usedFor, 
    usageNotes, 
    customerEmail, 
    customerPhone 
  } = req.body;
  
  // Validate required fields
  if (!orderId || itemIndex === undefined) {
    return res.status(400).json({
      success: false,
      message: 'Order ID and item index are required'
    });
  }
  
  if (!customerEmail && !customerPhone) {
    return res.status(400).json({
      success: false,
      message: 'Customer email or phone is required for verification'
    });
  }
  
  // Read orders
  const orders = readOrdersFile();
  
  // Find the order
  const orderIndex = orders.findIndex(order => order.id === orderId);
  if (orderIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Order not found'
    });
  }
  
  const order = orders[orderIndex];
  
  // Validate customer access
  const customerInfo = { email: customerEmail, phone: customerPhone };
  if (!validateCustomerAccess(order, customerInfo)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Customer information does not match order.'
    });
  }
  
  // Validate item index
  if (!order.items || !order.items[itemIndex]) {
    return res.status(404).json({
      success: false,
      message: 'Order item not found'
    });
  }
  
  const item = order.items[itemIndex];
  
  // Check if item is SIM or topup card (trackable items)
  if (!item.type || !['sim', 'topup'].includes(item.type)) {
    return res.status(400).json({
      success: false,
      message: 'Usage tracking is only available for SIM cards and topup cards'
    });
  }
  
  // Update usage information
  const currentTime = new Date().toISOString();
  
  orders[orderIndex].items[itemIndex] = {
    ...item,
    usedFor: usedFor || item.usedFor,
    usageNotes: usageNotes || item.usageNotes,
    usageDate: item.usageDate || currentTime, // Only set if not already set
    usageUpdatedAt: currentTime
  };
  
  // Update order timestamp
  orders[orderIndex].updatedAt = currentTime;
  
  // Save to file
  const saveSuccess = writeOrdersFile(orders);
  if (!saveSuccess) {
    return res.status(500).json({
      success: false,
      message: 'Failed to save usage information'
    });
  }
  
  console.log(`[CUSTOMER_ORDER_USAGE_API] Usage updated for order ${orderId}, item ${itemIndex}`);
  
  return res.status(200).json({
    success: true,
    message: 'Usage information updated successfully',
    data: {
      orderId,
      itemIndex,
      usageInfo: {
        usedFor: orders[orderIndex].items[itemIndex].usedFor,
        usageNotes: orders[orderIndex].items[itemIndex].usageNotes,
        usageDate: orders[orderIndex].items[itemIndex].usageDate,
        usageUpdatedAt: orders[orderIndex].items[itemIndex].usageUpdatedAt
      }
    }
  });
}

// Handle GET request to retrieve usage information
async function handleGetUsage(req, res) {
  const { customerEmail, customerPhone, orderId } = req.query;
  
  if (!customerEmail && !customerPhone) {
    return res.status(400).json({
      success: false,
      message: 'Customer email or phone is required'
    });
  }
  
  // Read orders
  const orders = readOrdersFile();
  
  // Filter orders for this customer
  const customerInfo = { email: customerEmail, phone: customerPhone };
  let customerOrders = orders.filter(order => validateCustomerAccess(order, customerInfo));
  
  // If specific order ID requested, filter further
  if (orderId) {
    customerOrders = customerOrders.filter(order => order.id === orderId);
  }
  
  // Extract usage information for trackable items
  const usageData = customerOrders.map(order => ({
    orderId: order.id,
    orderDate: order.createdAt,
    status: order.status,
    items: order.items
      .map((item, index) => ({
        index,
        name: item.name,
        type: item.type,
        // SIM specific fields
        simNumber: item.simNumber,
        phoneNumber: item.phoneNumber,
        iccid: item.iccid,
        // Topup specific fields
        cardNumber: item.cardNumber,
        cardSerial: item.cardSerial,
        topupCode: item.topupCode,
        // Usage tracking fields
        usedFor: item.usedFor,
        usageNotes: item.usageNotes,
        usageDate: item.usageDate,
        usageUpdatedAt: item.usageUpdatedAt,
        // Only include if it's a trackable item
        isTrackable: ['sim', 'topup'].includes(item.type)
      }))
      .filter(item => item.isTrackable) // Only return trackable items
  }));
  
  return res.status(200).json({
    success: true,
    data: usageData,
    total: usageData.length
  });
}