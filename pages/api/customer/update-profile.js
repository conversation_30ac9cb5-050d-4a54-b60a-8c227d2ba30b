import fs from 'fs';
import path from 'path';

// Path to customer data file
const dataDir = path.join(process.cwd(), 'data');
const customersFilePath = path.join(dataDir, 'customers.json');

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { customerId, addressData } = req.body;

    // Validate required data
    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }
    
    if (!addressData || !addressData.street || !addressData.city) {
      return res.status(400).json({ error: 'Address information is incomplete' });
    }
    
    console.log(`[API] Updating profile for customer ${customerId} with address data`);

    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Load existing customers or create empty array
    let customers = [];
    if (fs.existsSync(customersFilePath)) {
      try {
        const customersData = fs.readFileSync(customersFilePath, 'utf8');
        customers = JSON.parse(customersData);
      } catch (error) {
        console.error(`[API] Error reading customers file:`, error);
        // Continue with empty customers array if file is corrupted
      }
    }
    
    // Find customer or create a new one
    let customer = customers.find(c => c.id === customerId);
    
    if (!customer) {
      customer = {
        id: customerId,
        createdAt: new Date().toISOString(),
        addresses: []
      };
      customers.push(customer);
    }
    
    // Update customer with new address data
    customer.lastUpdated = new Date().toISOString();
    
    // Check if this address already exists
    let addressExists = false;
    for (let i = 0; i < customer.addresses.length; i++) {
      const addr = customer.addresses[i];
      
      // If street and city match, update the existing address
      if (addr.street === addressData.street && addr.city === addressData.city) {
        customer.addresses[i] = {
          ...addr,
          ...addressData,
          lastUsed: new Date().toISOString()
        };
        addressExists = true;
        break;
      }
    }
    
    // If address doesn't exist, add it
    if (!addressExists) {
      customer.addresses.push({
        ...addressData,
        isDefault: customer.addresses.length === 0, // Make default if first address
        lastUsed: new Date().toISOString(),
        createdAt: new Date().toISOString()
      });
    }
    
    // If this is the only address, make it the default shipping address
    if (customer.addresses.length === 1) {
      customer.defaultShippingAddress = 0; // Index of the default address
    }
    
    // Write updated customers back to file
    fs.writeFileSync(customersFilePath, JSON.stringify(customers, null, 2));
    console.log(`[API] Profile updated successfully for customer ${customerId}`);
    
    return res.status(200).json({ 
      success: true, 
      message: 'Profile updated successfully',
      profileData: { 
        customerId,
        addressCount: customer.addresses.length
      }
    });
    
  } catch (error) {
    console.error('[API] Error updating customer profile:', error);
    return res.status(500).json({ 
      error: 'Failed to update profile', 
      message: error.message 
    });
  }
} 