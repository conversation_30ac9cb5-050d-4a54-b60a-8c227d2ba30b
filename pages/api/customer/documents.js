import fs from 'fs';
import path from 'path';

// Path to the customers.json file
const customersPath = path.join(process.cwd(), 'data', 'customers.json');
// Base path for customer documents
const documentsBaseDir = path.join(process.cwd(), 'data', 'documents', 'customers');

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { customerId } = req.query;
    
    if (!customerId) {
      return res.status(400).json({ success: false, error: 'Customer ID is required' });
    }
    
    // Read the customers.json file
    if (fs.existsSync(customersPath)) {
      try {
        const customersData = fs.readFileSync(customersPath, 'utf8');
        const customers = JSON.parse(customersData);
        
        // Find the customer with the matching ID
        const customer = customers.find(c => c.id === customerId);
        
        if (!customer) {
          return res.status(404).json({ 
            success: false, 
            error: 'Customer not found' 
          });
        }
        
        // Get documents from the customer data
        const documents = customer.documents || [];
        
        // Add document URLs for each document
        const documentsWithUrls = documents.map(doc => ({
          ...doc,
          url: `/api/customer/${customerId}/document/${doc.filename}`
        }));
        
        // Group documents by type
        const documentsByType = documentsWithUrls.reduce((grouped, doc) => {
          const type = doc.documentType || 'unknown';
          if (!grouped[type]) {
            grouped[type] = [];
          }
          grouped[type].push(doc);
          return grouped;
        }, {});
        
        return res.status(200).json({ 
          success: true, 
          documents: documentsWithUrls,
          documentsByType
        });
      } catch (error) {
        console.error('Error reading customers.json:', error);
        return res.status(500).json({ 
          success: false, 
          error: 'Error reading customer data' 
        });
      }
    }
    
    // Fallback to scanning the documents directory if customers.json doesn't exist
    const documentsDir = path.join(documentsBaseDir, customerId);
    if (!fs.existsSync(documentsDir)) {
      // No documents found
      return res.status(200).json({ success: true, documents: [], documentsByType: {} });
    }
    
    // Helper function to determine document type from filename
    const getDocumentType = (filename) => {
      const lowerFilename = filename.toLowerCase();
      
      if (lowerFilename.includes('id') || lowerFilename.includes('cmnd') || lowerFilename.includes('cccd')) {
        return 'idCard';
      } else if (lowerFilename.includes('photo') || lowerFilename.includes('portrait') || lowerFilename.includes('chan_dung')) {
        return 'photo';
      } else if (lowerFilename.includes('residence') || lowerFilename.includes('cu_tru') || lowerFilename.includes('ho_khau')) {
        return 'proofOfResidence';
      } else if (lowerFilename.includes('tax') || lowerFilename.includes('thue')) {
        return 'taxRegistration';
      } else if (lowerFilename.includes('business') || lowerFilename.includes('kinh_doanh') || lowerFilename.includes('dkkd')) {
        return 'businessLicense';
      }
      return 'other';
    };
    
    // Helper function to determine document status from filename
    const getDocumentStatus = (filename) => {
      const lowerFilename = filename.toLowerCase();
      if (lowerFilename.includes('approved') || lowerFilename.includes('da_duyet')) {
        return 'approved';
      } else if (lowerFilename.includes('rejected') || lowerFilename.includes('tu_choi')) {
        return 'rejected';
      }
      return 'pending_approval';
    };
    
    // Read documents directory
    const files = fs.readdirSync(documentsDir);
    
    // Filter out metadata files and map to document objects
    const documents = files
      .filter(file => {
        const filePath = path.join(documentsDir, file);
        const stats = fs.statSync(filePath);
        return stats.isFile() && !file.endsWith('.json');
      })
      .map(filename => {
        const filePath = path.join(documentsDir, filename);
        const stats = fs.statSync(filePath);
        const metadataPath = path.join(documentsDir, `${filename}.json`);
        
        // Check if metadata file exists
        if (fs.existsSync(metadataPath)) {
          try {
            // Read and parse metadata
            const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
            
            // Don't include the actual file path for security
            const secureMetadata = { ...metadata };
            if (secureMetadata.storagePath) {
              delete secureMetadata.storagePath;
            }
            
            // Add the document URL
            secureMetadata.url = `/api/documents/view/${secureMetadata.documentType || getDocumentType(filename)}/${filename}?customerId=${customerId}`;
            
            return secureMetadata;
          } catch (error) {
            // If metadata can't be read, return basic info
            console.error(`Error reading metadata for ${filename}:`, error);
          }
        }
        
        // If no metadata exists or error reading it, return basic info
        return {
          customerId,
          filename,
          originalName: filename,
          documentType: getDocumentType(filename),
          status: getDocumentStatus(filename),
          uploadedAt: stats.birthtime.toISOString(),
          createdAt: stats.birthtime.toISOString(),
          updatedAt: stats.mtime.toISOString(),
          size: stats.size,
          url: `/api/documents/view/${getDocumentType(filename)}/${filename}?customerId=${customerId}`
        };
      });
    
    // Group documents by type
    const documentsByType = documents.reduce((grouped, doc) => {
      const type = doc.documentType || 'unknown';
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(doc);
      return grouped;
    }, {});
    
    return res.status(200).json({ 
      success: true, 
      documents,
      documentsByType
    });
    
  } catch (error) {
    console.error('Error retrieving documents:', error);
    res.status(500).json({ success: false, error: error.message });
  }
}