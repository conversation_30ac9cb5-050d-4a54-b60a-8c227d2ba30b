import fs from 'fs';
import path from 'path';

/**
 * API endpoint to get customer data from customers.json
 * 
 * @param {object} req - Next.js request object
 * @param {object} res - Next.js response object
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }
  
  try {
    const { customerId } = req.query;
    
    // Validate customerId
    if (!customerId) {
      return res.status(400).json({ success: false, message: 'Customer ID is required' });
    }
    
    // Path to customers.json file
    const customersFilePath = path.join(process.cwd(), 'data', 'customers.json');
    
    // Read customers.json file
    const customersData = JSON.parse(fs.readFileSync(customersFilePath, 'utf8'));
    
    // Find customer by ID
    const customer = customersData.find(c => c.id === customerId);
    
    if (!customer) {
      return res.status(404).json({ success: false, message: 'Customer not found' });
    }
    
    // Return customer data
    return res.status(200).json(customer);
  } catch (error) {
    console.error('Error fetching customer data:', error);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
} 