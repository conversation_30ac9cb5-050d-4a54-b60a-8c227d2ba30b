import withApiAuth from '../../../lib/apiAuth';

function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  const { name, email, password, phone } = req.body;
  
  // Validate required fields
  if (!name || !email) {
    return res.status(400).json({ error: 'Name and email are required' });
  }
  
  // Return a mock created customer
  return res.status(201).json({
    id: `CUST${Date.now().toString().slice(-6)}`,
    name,
    email,
    phone: phone || '',
    createdAt: new Date().toISOString()
  });
}

export default withApiAuth(handler); 