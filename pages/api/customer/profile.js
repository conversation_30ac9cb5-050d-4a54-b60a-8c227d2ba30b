import { readFile, writeFile, mkdir } from 'fs/promises';
import path from 'path';
import fs from 'fs';

// Path to customers.json file
const dataDir = path.join(process.cwd(), 'data');
const customersFilePath = path.join(dataDir, 'customers.json');

// Helper to ensure the directory exists
async function ensureDir(dirPath) {
  try {
    await mkdir(dirPath, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      throw error;
    }
  }
}

// Helper to read all customers from the central file
async function readAllCustomers() {
  try {
    // Ensure data directory exists
    await ensureDir(dataDir);
    
    // Check if customers.json exists
    if (fs.existsSync(customersFilePath)) {
      const customersData = await readFile(customersFilePath, 'utf8');
      
      // Handle empty file case
      if (!customersData || customersData.trim() === '') {
        console.log('customers.json is empty, initializing with empty array');
        await writeFile(customersFilePath, JSON.stringify([], null, 2));
        return [];
      }
      
      try {
        const customers = JSON.parse(customersData);
        
        // Ensure customers is an array
        if (!Array.isArray(customers)) {
          console.warn('customers.json did not contain an array, reinitializing with empty array');
          await writeFile(customersFilePath, JSON.stringify([], null, 2));
          return [];
        }
        
        return customers;
      } catch (error) {
        console.error('Error parsing customers.json, reinitializing file:', error);
        // If there's a parsing error, recreate the file with an empty array
        await writeFile(customersFilePath, JSON.stringify([], null, 2));
        return [];
      }
    } else {
      // If file doesn't exist, create it with an empty array
      await writeFile(customersFilePath, JSON.stringify([], null, 2));
      return [];
    }
  } catch (error) {
    console.error('Error loading customers:', error);
    return [];
  }
}

// Helper to save all customers to the central file
async function saveAllCustomers(customers) {
  try {
    // Ensure data directory exists
    await ensureDir(dataDir);
    
    // Write customers to file
    await writeFile(customersFilePath, JSON.stringify(customers, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving customers:', error);
    return false;
  }
}

export default async function handler(req, res) {
  // Add CORS headers for development
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Create a sanitized copy of the request body
    const requestBody = { ...req.body };
    
    // Handle case where phone number has been spread as individual digits
    const digits = {};
    let isDigitObject = false;
    
    // Look for digit properties (0, 1, 2, etc.)
    for (let i = 0; i < 15; i++) {
      if (requestBody[i] !== undefined) {
        digits[i] = requestBody[i];
        isDigitObject = true;
        // Remove the digit property to clean up the object
        delete requestBody[i];
      }
    }
    
    // If digits were found, reconstruct the phone number
    if (isDigitObject) {
      console.log("Detected phone digits spread in object, reconstructing phone number...");
      
      // Reconstruct phone number from sorted digits
      const phoneNumber = Object.keys(digits)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map(key => digits[key])
        .join('');
      
      // Set the phone number directly in profile data
      requestBody.phone = phoneNumber;
      
      console.log(`Reconstructed phone: ${phoneNumber}`);
    }
    
    console.log("Received profile update request:", requestBody);
    const { customerId, ...profileData } = requestBody;

    if (!customerId) {
      return res.status(400).json({ success: false, message: 'Customer ID is required' });
    }

    // Read all customers from the central file
    const customers = await readAllCustomers();
    
    // Find existing customer or create a new one
    let customer = customers.find(c => c.id === customerId);
    let isNewCustomer = false;
    
    if (!customer) {
      isNewCustomer = true;
      customer = {
        id: customerId,
        createdAt: new Date().toISOString(),
        addresses: []
      };
    }
    
    // Create an updated customer object with the new profile data
    const updatedCustomer = {
      ...customer,
      ...profileData,
      id: customerId,
      lastUpdated: new Date().toISOString()
    };
    
    // Ensure the customer has an addresses array
    if (!updatedCustomer.addresses) {
      updatedCustomer.addresses = [];
    }
    
    // Check if profile data includes address information
    if (profileData.address && profileData.city) {
      // Check if this address already exists
      const newAddress = {
        street: profileData.address,
        city: profileData.city,
        district: profileData.district || '',
        country: profileData.country || 'Vietnam',
        lastUsed: new Date().toISOString(),
        createdAt: new Date().toISOString()
      };
      
      let addressExists = false;
      for (let i = 0; i < updatedCustomer.addresses.length; i++) {
        const addr = updatedCustomer.addresses[i];
        
        // If street and city match, update the existing address
        if (addr.street === newAddress.street && addr.city === newAddress.city) {
          updatedCustomer.addresses[i] = {
            ...addr,
            ...newAddress,
            lastUsed: new Date().toISOString()
          };
          addressExists = true;
          break;
        }
      }
      
      // If address doesn't exist, add it
      if (!addressExists) {
        updatedCustomer.addresses.push({
          ...newAddress,
          isDefault: updatedCustomer.addresses.length === 0 // Make default if first address
        });
      }
    }
    
    // Update or add the customer to the array
    if (isNewCustomer) {
      customers.push(updatedCustomer);
    } else {
      const index = customers.findIndex(c => c.id === customerId);
      customers[index] = updatedCustomer;
    }
    
    // Save all customers back to the file
    await saveAllCustomers(customers);
    console.log("Profile successfully saved for customer:", customerId);

    return res.status(200).json({
      success: true,
      message: 'Customer profile updated successfully',
      profile: updatedCustomer
    });
  } catch (error) {
    console.error('Error updating customer profile:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while updating the customer profile',
      error: error.message
    });
  }
} 