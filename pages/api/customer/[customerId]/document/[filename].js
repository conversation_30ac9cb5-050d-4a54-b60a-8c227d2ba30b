import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { customerId, filename } = req.query;
    
    console.log('Serving customer document:', { customerId, filename });

    if (!customerId || !filename) {
      return res.status(400).json({ success: false, message: 'Customer ID and filename are required' });
    }

    // Construct the absolute file path
    const absoluteFilePath = path.join(process.cwd(), 'data', 'documents', 'customers', customerId, filename);
    
    console.log('Looking for file at:', absoluteFilePath);

    // Check if file exists
    if (!fs.existsSync(absoluteFilePath)) {
      console.log('File not found:', absoluteFilePath);
      return res.status(404).json({ success: false, message: 'File not found' });
    }

    // Get file stats
    const stats = fs.statSync(absoluteFilePath);
    if (!stats.isFile()) {
      return res.status(404).json({ success: false, message: 'Not a file' });
    }

    // Determine content type based on file extension
    const ext = path.extname(filename).toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (ext) {
      case '.pdf':
        contentType = 'application/pdf';
        break;
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.bmp':
        contentType = 'image/bmp';
        break;
      case '.doc':
        contentType = 'application/msword';
        break;
      case '.docx':
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        break;
      case '.txt':
        contentType = 'text/plain';
        break;
    }

    // Set response headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'private, no-cache');

    console.log('Streaming file:', filename, 'Content-Type:', contentType);

    // Stream the file
    const fileStream = fs.createReadStream(absoluteFilePath);
    
    fileStream.on('error', (error) => {
      console.error('Error streaming file:', error);
      if (!res.headersSent) {
        res.status(500).json({ success: false, message: 'Error reading file' });
      }
    });

    fileStream.pipe(res);

  } catch (error) {
    console.error('Error serving customer document:', error);
    if (!res.headersSent) {
      return res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
} 