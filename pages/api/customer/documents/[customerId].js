import { readFile } from 'fs/promises';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { customerId } = req.query;

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID is required'
      });
    }

    // Path to the customers.json file
    const customersPath = path.join(process.cwd(), 'data', 'customers.json');

    try {
      // Read the customers data
      const fileContent = await readFile(customersPath, 'utf8');
      const customers = JSON.parse(fileContent);
      
      // Find the customer with the matching ID
      const customer = customers.find(c => c.id === customerId);
      
      if (!customer) {
        return res.status(404).json({
          success: false,
          message: 'Customer not found'
        });
      }
      
      // Get documents from the customer data
      const documents = customer.documents || [];

      // Return the document list
      return res.status(200).json({
        success: true,
        documents: documents
      });
    } catch (error) {
      // If there's an error reading the file or parsing JSON
      console.error('Error reading customers file:', error);
      
      return res.status(500).json({
        success: false,
        message: 'Error reading customer data',
        error: error.message
      });
    }
  } catch (error) {
    console.error('Error fetching customer documents:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while fetching customer documents',
      error: error.message
    });
  }
} 