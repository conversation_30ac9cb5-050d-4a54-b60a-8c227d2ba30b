import { IncomingForm } from 'formidable';
import fs from 'fs';
import path from 'path';

// Set to true to disable the default body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

// Store in private data directory, not public
const dataDir = path.join(process.cwd(), 'data', 'customers');
// Path to customers.json file
const customersJsonPath = path.join(process.cwd(), 'data', 'customers.json');

/**
 * Ensure upload directories exist
 * 
 * @param {string} customerId - The customer ID
 */
const ensureDirectories = (customerId) => {
  // Ensure base customer directory exists
  const customerDir = path.join(dataDir, customerId);
  if (!fs.existsSync(customerDir)) {
    fs.mkdirSync(customerDir, { recursive: true });
  }
  
  // Ensure documents directory exists
  const documentsDir = path.join(customerDir, 'documents');
  if (!fs.existsSync(documentsDir)) {
    fs.mkdirSync(documentsDir, { recursive: true });
  }
  
  return documentsDir;
};

/**
 * Get a safe filename
 * 
 * @param {string} originalFilename - The original filename
 * @param {string} documentType - The document type
 * @returns {string} A safe filename
 */
const getSafeFilename = (originalFilename, documentType) => {
  const timestamp = Date.now();
  const extension = path.extname(originalFilename) || '.jpg';
  const safeName = `${documentType}_${timestamp}${extension}`;
  return safeName;
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    // Create a new formidable form with options
    const form = new IncomingForm({
      multiples: false,
      keepExtensions: true,
      // You might want to set maxFileSize depending on your needs (10MB here)
      maxFileSize: 10 * 1024 * 1024,
    });
    
    // Parse the form
    const [fields, files] = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) return reject(err);
        resolve([fields, files]);
      });
    });

    // Validate fields
    const customerId = fields.customerId?.[0] || fields.customerId;
    const documentType = fields.documentType?.[0] || fields.documentType;
    const orderId = fields.orderId?.[0] || fields.orderId;
    
    // Get the file based on file structure
    const file = files.file?.[0] || files.file;

    if (!customerId) {
      return res.status(400).json({ success: false, error: 'Customer ID is required' });
    }

    if (!documentType) {
      return res.status(400).json({ success: false, error: 'Document type is required' });
    }

    if (!file) {
      return res.status(400).json({ success: false, error: 'No file uploaded' });
    }

    console.log("Processing upload:", { customerId, documentType, orderId });
    console.log("File details:", { 
      name: file.originalFilename || file.name, 
      size: file.size,
      path: file.filepath || file.path
    });

    // Make sure directories exist
    const documentsDir = ensureDirectories(customerId);
    
    // Create a safe filename
    const originalFilename = file.originalFilename || file.name;
    const safeFilename = getSafeFilename(originalFilename, documentType);
    const filePath = path.join(documentsDir, safeFilename);
    
    // Get the file path from the uploaded file
    const uploadedFilePath = file.filepath || file.path;
    
    // Read the file and write to the destination
    try {
      const data = fs.readFileSync(uploadedFilePath);
      fs.writeFileSync(filePath, data);
      
      console.log(`File saved successfully to ${filePath}`);
      
      // Remove the temporary file
      fs.unlinkSync(uploadedFilePath);
      
      // Save document metadata to a JSON file
      const metadata = {
        customerId,
        documentType,
        originalFilename,
        filename: safeFilename,
        size: file.size,
        uploadDate: new Date().toISOString(),
        orderId: orderId || null,
        // The path is a server-side path, not accessible from browser
        storagePath: filePath
      };
      
      // Save metadata to JSON file
      const metadataPath = path.join(documentsDir, `${safeFilename}.json`);
      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
      
      // Update document in customers.json
      updateCustomerDocuments(customerId, {
        documentType: documentType,
        filename: safeFilename,
        originalFilename: originalFilename,
        uploadDate: metadata.uploadDate,
        size: file.size,
        orderId: orderId || null
      });
      
      // Return success response (don't include actual file path for security)
      res.status(200).json({
        success: true,
        filename: safeFilename,
        documentType,
        uploadDate: metadata.uploadDate
      });
    } catch (fileError) {
      console.error('Error saving file:', fileError);
      return res.status(500).json({ success: false, error: 'Error saving file', details: fileError.message });
    }
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
}

/**
 * Update the customer documents in customers.json
 * 
 * @param {string} customerId - The customer ID
 * @param {object} documentMetadata - The document metadata
 */
function updateCustomerDocuments(customerId, documentMetadata) {
  try {
    // Read customers.json file
    if (!fs.existsSync(customersJsonPath)) {
      console.error('customers.json file not found');
      return;
    }
    
    const customersData = fs.readFileSync(customersJsonPath, 'utf8');
    let customers;
    
    try {
      customers = JSON.parse(customersData);
    } catch (error) {
      console.error('Error parsing customers.json:', error);
      return;
    }
    
    // Find the customer
    const customerIndex = customers.findIndex(c => c.id === customerId);
    
    if (customerIndex === -1) {
      console.error(`Customer with ID ${customerId} not found`);
      return;
    }
    
    // Add the document to the customer's documents array
    if (!customers[customerIndex].documents) {
      customers[customerIndex].documents = [];
    }
    
    customers[customerIndex].documents.push(documentMetadata);
    customers[customerIndex].lastUpdated = new Date().toISOString();
    
    // Write the updated customers data back to the file
    fs.writeFileSync(customersJsonPath, JSON.stringify(customers, null, 2));
    
    console.log(`Document ${documentMetadata.filename} added to customer ${customerId}`);
  } catch (error) {
    console.error('Error updating customer documents:', error);
    // This is a non-critical operation, continue execution
  }
} 