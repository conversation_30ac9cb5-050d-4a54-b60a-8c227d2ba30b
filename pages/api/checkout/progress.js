import fs from 'fs';
import path from 'path';

// Path to checkout progress file
const dataDir = path.join(process.cwd(), 'data');
const progressFilePath = path.join(dataDir, 'checkout-progress.json');

// Ensure data directory exists
const ensureDataDirectory = () => {
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Load checkout progress data
const loadProgressData = () => {
  try {
    ensureDataDirectory();
    if (fs.existsSync(progressFilePath)) {
      const data = fs.readFileSync(progressFilePath, 'utf8');
      return JSON.parse(data);
    }
    return {};
  } catch (error) {
    console.error('[CHECKOUT_PROGRESS_API] Error loading progress data:', error);
    return {};
  }
};

// Save checkout progress data
const saveProgressData = (data) => {
  try {
    ensureDataDirectory();
    fs.writeFileSync(progressFilePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('[CHECKOUT_PROGRESS_API] Error saving progress data:', error);
    return false;
  }
};

export default async function handler(req, res) {
  const { method } = req;
  const { orderId } = req.query;

  // Validate orderId for all operations
  if (!orderId) {
    return res.status(400).json({ 
      success: false, 
      error: 'Order ID is required' 
    });
  }

  try {
    if (method === 'GET') {
      // Load progress for specific order
      const allProgress = loadProgressData();
      const orderProgress = allProgress[orderId] || null;
      
      console.log(`[CHECKOUT_PROGRESS_API] Loading progress for order ${orderId}:`, !!orderProgress);
      
      return res.status(200).json({
        success: true,
        orderId,
        progress: orderProgress
      });
      
    } else if (method === 'POST') {
      // Save progress for specific order
      const progressData = req.body;
      
      if (!progressData) {
        return res.status(400).json({ 
          success: false, 
          error: 'Progress data is required' 
        });
      }
      
      // Load existing progress data
      const allProgress = loadProgressData();
      
      // Add/update progress for this order
      allProgress[orderId] = {
        orderId,
        timestamp: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        ...progressData
      };
      
      // Save back to file
      const saved = saveProgressData(allProgress);
      
      if (saved) {
        console.log(`[CHECKOUT_PROGRESS_API] Progress saved for order ${orderId}`);
        return res.status(200).json({
          success: true,
          message: 'Progress saved successfully',
          orderId
        });
      } else {
        return res.status(500).json({
          success: false,
          error: 'Failed to save progress data'
        });
      }
      
    } else if (method === 'DELETE') {
      // Clear progress for specific order
      const allProgress = loadProgressData();
      
      if (allProgress[orderId]) {
        delete allProgress[orderId];
        const saved = saveProgressData(allProgress);
        
        if (saved) {
          console.log(`[CHECKOUT_PROGRESS_API] Progress cleared for order ${orderId}`);
          return res.status(200).json({
            success: true,
            message: 'Progress cleared successfully',
            orderId
          });
        } else {
          return res.status(500).json({
            success: false,
            error: 'Failed to clear progress data'
          });
        }
      } else {
        return res.status(404).json({
          success: false,
          error: 'No progress found for this order'
        });
      }
      
    } else {
      // Method not allowed
      return res.status(405).json({ 
        success: false, 
        error: 'Method not allowed' 
      });
    }
    
  } catch (error) {
    console.error('[CHECKOUT_PROGRESS_API] Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
}
