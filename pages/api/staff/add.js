import { addStaff } from '../../../utils/staffUtils';

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const staffData = req.body;

    // Validate required fields
    if (!staffData.name || !staffData.email || !staffData.role) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: name, email, and role are required'
      });
    }

    // Add the new staff member
    const newStaff = addStaff(staffData);

    return res.status(201).json({
      success: true,
      message: 'Staff member added successfully',
      staff: newStaff
    });
  } catch (error) {
    console.error('Error adding staff member:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
} 