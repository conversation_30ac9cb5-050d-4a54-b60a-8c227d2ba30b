import { deleteStaff } from '../../../utils/staffUtils';

export default function handler(req, res) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { id } = req.query;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Staff ID is required as a query parameter'
      });
    }

    // Delete the staff member
    const deleted = deleteStaff(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: `Staff member with ID ${id} not found`
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Staff member deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting staff member:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
} 