import { getAllStaff, getStaffByStore } from '../../../utils/staffUtils';

export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Check for storeId query parameter
    const { storeId } = req.query;
    
    // Get staff data
    const staffData = storeId 
      ? getStaffByStore(storeId)
      : getAllStaff();
    
    return res.status(200).json({
      success: true,
      staff: staffData
    });
  } catch (error) {
    console.error('Error fetching staff data:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
} 