import { updateStaff } from '../../../utils/staffUtils';

export default function handler(req, res) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { id } = req.query;
    const updateData = req.body;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Staff ID is required as a query parameter'
      });
    }

    // Update the staff member
    const updatedStaff = updateStaff(id, updateData);

    if (!updatedStaff) {
      return res.status(404).json({
        success: false,
        message: `Staff member with ID ${id} not found`
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Staff member updated successfully',
      staff: updatedStaff
    });
  } catch (error) {
    console.error('Error updating staff member:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
} 