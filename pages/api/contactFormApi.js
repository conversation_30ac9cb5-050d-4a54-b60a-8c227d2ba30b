import nodemailer from 'nodemailer';

// SMTP Configuration - Fill out these credentials
const SMTP_CONFIG = {
  host: 'email-smtp.us-east-1.amazonaws.com',
  port: 587,
  secure: false,
  auth: {
    user: 'AKIA5BI3T5T7MTKUMZEF',
    pass: 'BLsRI5+AnugtvExnbs4SOFKvlBYghpmxZR2jUeioGQNU'
  }
};

// Email configuration
const EMAIL_CONFIG = {
  from: '<EMAIL>', // Sender email (should match SMTP auth user)
  to: '<EMAIL>', // Where contact form submissions should be sent
  replyTo: '<EMAIL>' // Reply-to address
};

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Phương thức không được phép. Chỉ chấp nhận yêu cầu POST.'
    });
  }

  try {
    // Extract form data from request body
    const { name, email, phone, subject, message } = req.body;

    // Validate required fields
    if (!name || !email || !message) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin bắt buộc. Họ tên, email và tin nhắn là bắt buộc.'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Định dạng email không hợp lệ.'
      });
    }

    // Create transporter
    const transporter = nodemailer.createTransport(SMTP_CONFIG);

    // Verify SMTP connection
    try {
      await transporter.verify();
      console.log('Kết nối SMTP thành công');
    } catch (error) {
      console.error('Kết nối SMTP thất bại:', error);
      return res.status(500).json({
        success: false,
        message: 'Lỗi cấu hình dịch vụ email. Vui lòng thử lại sau.'
      });
    }

    // Prepare email content
    const emailSubject = subject ? `[SIM & More] Biểu mẫu liên hệ: ${subject}` : '[SIM & More] Tin nhắn liên hệ mới từ [SIM & More] Shop';

    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
          Tin nhắn liên hệ mới từ website
        </h2>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="color: #007bff; margin-top: 0;">Thông tin liên hệ</h3>
          <p><strong>Họ tên:</strong> ${name}</p>
          <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
          ${phone ? `<p><strong>Số điện thoại:</strong> ${phone}</p>` : ''}
          ${subject ? `<p><strong>Chủ đề:</strong> ${subject}</p>` : ''}
        </div>

        <div style="background-color: #ffffff; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px;">
          <h3 style="color: #007bff; margin-top: 0;">Nội dung tin nhắn</h3>
          <p style="line-height: 1.6; white-space: pre-wrap;">${message}</p>
        </div>

        <div style="margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 5px; font-size: 12px; color: #6c757d;">
          <p><strong>Chi tiết gửi tin:</strong></p>
          <p>Thời gian: ${new Date().toLocaleString('vi-VN')}</p>
          <p>IP: ${req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'Không xác định'}</p>
          <p>Trình duyệt: ${req.headers['user-agent'] || 'Không xác định'}</p>
        </div>
      </div>
    `;

    const textContent = `
Tin nhắn liên hệ mới từ website

Thông tin liên hệ:
Họ tên: ${name}
Email: ${email}
${phone ? `Số điện thoại: ${phone}` : ''}
${subject ? `Chủ đề: ${subject}` : ''}

Nội dung tin nhắn:
${message}

Chi tiết gửi tin:
Thời gian: ${new Date().toLocaleString('vi-VN')}
IP: ${req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'Không xác định'}
Trình duyệt: ${req.headers['user-agent'] || 'Không xác định'}
    `;

    // Email options
    const mailOptions = {
      from: EMAIL_CONFIG.from,
      to: EMAIL_CONFIG.to,
      replyTo: email, // Set reply-to as the form submitter's email
      subject: emailSubject,
      text: textContent,
      html: htmlContent
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);

    console.log('Gửi email thành công:', {
      messageId: info.messageId,
      from: EMAIL_CONFIG.from,
      to: EMAIL_CONFIG.to,
      subject: emailSubject
    });

    // Send success response
    return res.status(200).json({
      success: true,
      message: 'Tin nhắn đã được gửi thành công! Chúng tôi sẽ phản hồi bạn sớm nhất có thể.',
      messageId: info.messageId
    });

  } catch (error) {
    console.error('Lỗi API biểu mẫu liên hệ:', error);

    // Send error response
    return res.status(500).json({
      success: false,
      message: 'Không thể gửi tin nhắn. Vui lòng thử lại sau.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
