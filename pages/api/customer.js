import fs from 'fs';
import path from 'path';
import withApiAuth from '../../lib/apiAuth';

// Path to customers data file
const dataDir = path.join(process.cwd(), 'data');
const customersFile = path.join(dataDir, 'customers.json');

async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  console.log('API Request:', req.method, req.url);
  console.log('Query params:', req.query);

  // Get customer ID from query params
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'Customer ID is required (use ?id=CUST001)' });
  }

  console.log('Looking for customer ID:', id);

  // Ensure customers data file exists
  if (!fs.existsSync(customersFile)) {
    console.error('Customers file does not exist:', customersFile);
    return res.status(404).json({ error: 'Customer data not found' });
  }

  // Read customers data
  let customers = [];
  try {
    const data = fs.readFileSync(customersFile, 'utf8');
    console.log('Customer data file content (first 200 chars):', data.substring(0, 200) + '...');
    
    customers = JSON.parse(data);
    console.log('Parsed customers data type:', Array.isArray(customers) ? 'Array' : typeof customers);
    
    if (!Array.isArray(customers)) {
      if (customers.customers && Array.isArray(customers.customers)) {
        customers = customers.customers;
        console.log('Extracted customers from nested structure');
      } else {
        console.error('Customers data is not an array');
        return res.status(500).json({ error: 'Invalid customers data format' });
      }
    }
  } catch (error) {
    console.error('Error reading customers file:', error);
    return res.status(500).json({ error: 'Error reading customer data', message: error.message });
  }

  // Find the customer by ID
  console.log(`Looking for customer with ID: ${id}`);
  console.log('Available customer IDs:', customers.map(c => c.id || c.customerId).join(', '));
  
  const customer = customers.find(c => 
    (c.id === id) || (c.customerId === id)
  );
  
  if (!customer) {
    console.error(`Customer not found with ID: ${id}`);
    return res.status(404).json({ error: 'Customer not found' });
  }

  console.log(`Found customer:`, JSON.stringify(customer, null, 2));

  try {
    // Return customer data without password
    const { password, ...customerData } = customer;
    return res.status(200).json(customerData);
  } catch (error) {
    console.error('Error processing customer data:', error);
    return res.status(500).json({ error: 'Error fetching customer', message: error.message });
  }
}

export default withApiAuth(handler); 