import fs from 'fs';
import path from 'path';
import withApiAuth from '../../lib/apiAuth';

function handler(req, res) {
  const info = {
    cwd: process.cwd(),
    dataDir: path.join(process.cwd(), 'data'),
    customersFile: path.join(process.cwd(), 'data', 'customers.json'),
    customersFileExists: fs.existsSync(path.join(process.cwd(), 'data', 'customers.json')),
    libApiAuthPath: require.resolve('../../lib/apiAuth')
  };
  
  // Try to read the customers file
  if (info.customersFileExists) {
    try {
      const data = fs.readFileSync(info.customersFile, 'utf8');
      info.customersFileSize = data.length;
      info.customersFileSample = data.substring(0, 100) + '...';
    } catch (error) {
      info.readError = error.message;
    }
  }
  
  res.status(200).json(info);
}

export default withApiAuth(handler); 