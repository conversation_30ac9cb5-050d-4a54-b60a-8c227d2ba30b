import { fetchStore } from '../../utils/storeProvider'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const allStores = await fetchStore()
    res.status(200).json(allStores)
  } catch (error) {
    console.error('Error fetching stores:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
}