import axios from 'axios';
import DENOMINATION from "../../utils/currencyProvider";

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const { topic, shopId, name, toanha, floorroom, mobile, email, time, ngaygiaohang, message, cart, orderId, total } = req.body;

    // Construct the payload data for the API
    const payload = {
      orderid: orderId,
      active: 2, //order just received, need to confirm inventory
      paid: 0,
      shop: shopId,
      action: topic + ', ' + name + ', ' + toanha + ', ' + floorroom + ', ' + mobile + ', ' + email + ', ' + time + ', ' + ngaygiaohang,
      delivery: new Date().toISOString(), // Example delivery time
      notes: 'Notes: ' + message, 
      cart: JSON.stringify(cart.map(item => ({
        name: item.name,
        price: DENOMINATION + item.price,
        quantity: item.quantity,
        image: Array.isArray(item.image) && item.image.length > 0 ? item.image[0] : 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp',
        totalPrice: DENOMINATION + item.price * item.quantity
      }))),
      total: DENOMINATION + total // Total price
    };

    // Define the URL of the API endpoint
    const apiUrl = 'https://workflow.abnasia.org/webhook/06ad7897-0eaa-46d1-b8b8-dff453829d363s';

    // Make a POST request to the API
    axios.post(apiUrl, payload)
      .then(response => {
        // Handle successful response
        console.log('Order created successfully:', response.data);
        res.status(200).json({ message: 'Order created successfully' });
      })
      .catch(error => {
        // Handle error
        console.error('Error creating order:', error.response ? error.response.data : error.message);
        res.status(500).json({ error: 'Failed to create order' });
      });
  } else {
    res.status(405).json({ error: 'Method Not Allowed' }); // Send a 405 Method Not Allowed response for non-POST requests
  }
}
