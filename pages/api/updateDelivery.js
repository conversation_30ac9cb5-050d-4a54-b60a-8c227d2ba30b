import axios from 'axios';
import DENOMINAT<PERSON> from "../../utils/currencyProvider";

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const { shipperconfirmChecked, buyerconfirmChecked, sellerconfirmChecked, deliveryproofChecked, shippergps, orderid } = req.body;

    // Construct the payload data for the API
    const payload = {
      orderid: orderid,
      shipperconfirmChecked: shipperconfirmChecked,
      buyerconfirmChecked: buyerconfirmChecked,
      sellerconfirmChecked: sellerconfirmChecked,
      deliveryproofChecked: deliveryproofChecked,
      shippergps: shippergps
    };

    // Define the URL of the API endpoint
    const apiUrl = 'https://workflow.abnasia.org/webhook/06ad7897-0eaa-46d1-b8b8-dff8434529d353';

    // Make a POST request to the API
    axios.post(apiUrl, payload)
      .then(response => {
        // Handle successful response
        console.log('Order created successfully:', response.data);
        res.status(200).json({ message: 'Order created successfully' });
      })
      .catch(error => {
        // Handle error
        console.error('Error creating order:', error.response ? error.response.data : error.message);
        res.status(500).json({ error: 'Failed to create order' });
      });
  } else {
    res.status(405).json({ error: 'Method Not Allowed' }); // Send a 405 Method Not Allowed response for non-POST requests
  }
}
