import { NextApiRequest, NextApiResponse } from 'next';
import { getOrdersByPhone, checkAndUpdateExpiredOrders } from '../../../utils/orderUtils';

export default async function handler(req, res) {
  // Allow GET method only
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { phoneNumber } = req.query;

    // Validate phoneNumber parameter
    if (!phoneNumber) {
      return res.status(400).json({ 
        success: false, 
        message: 'Phone number is required'
      });
    }

    // Check for expired orders before returning results
    checkAndUpdateExpiredOrders();

    // Get orders by phone number
    const orders = getOrdersByPhone(phoneNumber);

    // Return the results
    return res.status(200).json({
      success: true,
      count: orders.length,
      orders: orders
    });
  } catch (error) {
    console.error('[Orders API] Error fetching orders by customer:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching orders',
      error: error.message
    });
  }
} 