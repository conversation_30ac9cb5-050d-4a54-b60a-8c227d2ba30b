import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { orderId, itemId } = req.query;
  const updateData = req.body;

  if (!orderId || itemId === undefined) {
    return res.status(400).json({ error: 'Order ID and item ID are required' });
  }

  const itemIndex = parseInt(itemId);
  if (isNaN(itemIndex)) {
    return res.status(400).json({ error: 'Item ID must be a valid number' });
  }

  try {
    let foundOrder = null;
    let orderFilePath = null;

    // First try to find in central orders.json
    const ordersFile = path.join(process.cwd(), 'data', 'orders.json');
    if (fs.existsSync(ordersFile)) {
      try {
        const data = fs.readFileSync(ordersFile, 'utf8');
        const orders = JSON.parse(data);
        if (Array.isArray(orders)) {
                      const orderIndex = orders.findIndex(order => 
              order.id === orderId || 
              order.orderId === orderId
            );
          
          if (orderIndex !== -1) {
            foundOrder = orders[orderIndex];
            // Update the item
            if (foundOrder.items && foundOrder.items[itemIndex]) {
              foundOrder.items[itemIndex] = { ...foundOrder.items[itemIndex], ...updateData };
              orders[orderIndex] = foundOrder;
              fs.writeFileSync(ordersFile, JSON.stringify(orders, null, 2));
              return res.status(200).json({ success: true, order: foundOrder });
            }
          }
        }
      } catch (error) {
        console.error('Error reading central orders file:', error);
      }
    }

    // If not found in central file, search in customer directories
    const customersDir = path.join(process.cwd(), 'data', 'customers');
    if (fs.existsSync(customersDir)) {
      const customerDirs = fs.readdirSync(customersDir);
      
      for (const customerDir of customerDirs) {
        const ordersDir = path.join(customersDir, customerDir, 'orders');
        if (fs.existsSync(ordersDir)) {
          const orderFiles = fs.readdirSync(ordersDir);
          
          for (const orderFile of orderFiles) {
            if (orderFile.endsWith('.json')) {
              try {
                orderFilePath = path.join(ordersDir, orderFile);
                const orderData = fs.readFileSync(orderFilePath, 'utf8');
                const order = JSON.parse(orderData);
                
                if (order.id === orderId || order.orderId === orderId) {
                  foundOrder = order;
                  break;
                }
              } catch (error) {
                console.error(`Error reading order file ${orderFile}:`, error);
              }
            }
          }
          
          if (foundOrder) break;
        }
      }
    }

    if (!foundOrder) {
      return res.status(404).json({ error: 'Order not found' });
    }

    if (!foundOrder.items || !foundOrder.items[itemIndex]) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Update the item
    foundOrder.items[itemIndex] = { ...foundOrder.items[itemIndex], ...updateData };
    
    // Add updated timestamp
    foundOrder.updatedAt = new Date().toISOString();

    // Save back to the file
    if (orderFilePath) {
      fs.writeFileSync(orderFilePath, JSON.stringify(foundOrder, null, 2));
    }

    return res.status(200).json({ success: true, order: foundOrder });

  } catch (error) {
    console.error('Error updating order item:', error);
    return res.status(500).json({ error: 'Internal server error', message: error.message });
  }
} 