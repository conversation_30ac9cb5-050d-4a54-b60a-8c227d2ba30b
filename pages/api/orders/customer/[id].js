import fs from 'fs';
import path from 'path';

// Path to data files
const dataDir = path.join(process.cwd(), 'data');
const ordersFile = path.join(dataDir, 'orders.json');
const customersFile = path.join(dataDir, 'customers.json');

async function handler(req, res) {
  // Only allow GET requests for this endpoint
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Get customer ID from the URL
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'Customer ID is required' });
  }

  // Ensure orders data file exists
  if (!fs.existsSync(ordersFile)) {
    return res.status(404).json({ error: 'Orders data not found' });
  }

  // Read orders data
  let orders = [];
  try {
    const data = fs.readFileSync(ordersFile, 'utf8');
    orders = JSON.parse(data);
    if (!Array.isArray(orders)) {
      orders = [];
    }
  } catch (error) {
    console.error('Error reading orders file:', error);
    return res.status(500).json({ error: 'Error reading order data' });
  }

  // Find all orders for the customer
  const customerOrders = orders.filter(order => order.customerId === id);

  // Get query parameters for filtering and sorting
  const {
    status,
    limit = 50,
    skip = 0,
    sort = 'createdAt',
    order = 'desc'
  } = req.query;

  // Filter by status if provided
  let filteredOrders = customerOrders;
  if (status) {
    filteredOrders = filteredOrders.filter(order => order.status === status);
  }

  // Sort orders
  if (filteredOrders.length > 0 && filteredOrders[0][sort] !== undefined) {
    filteredOrders.sort((a, b) => {
      if (order === 'asc') {
        return a[sort] > b[sort] ? 1 : -1;
      } else {
        return a[sort] < b[sort] ? 1 : -1;
      }
    });
  }

  // Paginate results
  const startIndex = parseInt(skip, 10) || 0;
  const endIndex = startIndex + (parseInt(limit, 10) || 50);
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  // Check if the customer exists
  let customerExists = false;
  try {
    if (fs.existsSync(customersFile)) {
      const customerData = fs.readFileSync(customersFile, 'utf8');
      const customers = JSON.parse(customerData);
      customerExists = customers.some(customer => customer.id === id);
    }
  } catch (error) {
    console.error('Error checking customer:', error);
    // We'll continue even if customer check fails
  }

  // Return the orders
  return res.status(200).json({
    success: true,
    customerId: id,
    customerExists,
    total: filteredOrders.length,
    orders: paginatedOrders
  });
}

export default handler; 