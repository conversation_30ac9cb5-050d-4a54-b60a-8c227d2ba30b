import fs from 'fs';
import path from 'path';

const ORDERS_FILE = path.join(process.cwd(), 'data', 'orders.json');

// Helper function to read orders
const readOrders = () => {
  try {
    if (!fs.existsSync(ORDERS_FILE)) {
      return [];
    }
    const data = fs.readFileSync(ORDERS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading orders:', error);
    return [];
  }
};

// Helper function to write orders
const writeOrders = (orders) => {
  try {
    const dir = path.dirname(ORDERS_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(ORDERS_FILE, JSON.stringify(orders, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing orders:', error);
    return false;
  }
};

// Helper function to verify order ownership
const verifyOrderOwnership = (order, customerData) => {
  if (!order || !customerData) return false;
  
  // Check if customer owns this order
  return order.customerId === customerData.id ||
         order.customerId === customerData.phone ||
         order.customerPhone === customerData.phone ||
         order.customerEmail === customerData.email;
};

export default async function handler(req, res) {
  const { orderId } = req.query;
  
  if (!orderId) {
    return res.status(400).json({ success: false, message: 'Order ID is required' });
  }

  // Set response headers for UTF-8 encoding
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  
  // Get customer data from request body only (avoid header encoding issues)
  let customerData;
  try {
    customerData = req.body.customerData;
  } catch (error) {
    return res.status(401).json({ success: false, message: 'Invalid customer data' });
  }

  if (!customerData) {
    return res.status(401).json({ success: false, message: 'Customer authentication required' });
  }

  const orders = readOrders();
  const orderIndex = orders.findIndex(order => order.id === orderId);
  
  if (orderIndex === -1) {
    return res.status(404).json({ success: false, message: 'Order not found' });
  }

  const order = orders[orderIndex];
  
  // Verify ownership
  if (!verifyOrderOwnership(order, customerData)) {
    return res.status(403).json({ success: false, message: 'Access denied' });
  }

  // Initialize notes array if it doesn't exist
  if (!order.notes) {
    order.notes = [];
  }

  switch (req.method) {
    case 'GET':
      // Get all notes for the order
      return res.status(200).json({
        success: true,
        notes: order.notes
      });

    case 'POST':
      // Add a new note
      const { text } = req.body;
      
      if (!text || text.trim().length === 0) {
        return res.status(400).json({ success: false, message: 'Note text is required' });
      }

      const newNote = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        text: text.trim(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      order.notes.push(newNote);
      orders[orderIndex] = order;
      
      if (writeOrders(orders)) {
        return res.status(201).json({
          success: true,
          note: newNote,
          message: 'Note added successfully'
        });
      } else {
        return res.status(500).json({ success: false, message: 'Failed to save note' });
      }

    case 'PUT':
      // Update an existing note
      const { noteId, text: updatedText } = req.body;
      
      if (!noteId) {
        return res.status(400).json({ success: false, message: 'Note ID is required' });
      }
      
      if (!updatedText || updatedText.trim().length === 0) {
        return res.status(400).json({ success: false, message: 'Note text is required' });
      }

      const noteIndex = order.notes.findIndex(note => note.id === noteId);
      
      if (noteIndex === -1) {
        return res.status(404).json({ success: false, message: 'Note not found' });
      }

      order.notes[noteIndex] = {
        ...order.notes[noteIndex],
        text: updatedText.trim(),
        updatedAt: new Date().toISOString()
      };
      
      orders[orderIndex] = order;
      
      if (writeOrders(orders)) {
        return res.status(200).json({
          success: true,
          note: order.notes[noteIndex],
          message: 'Note updated successfully'
        });
      } else {
        return res.status(500).json({ success: false, message: 'Failed to update note' });
      }

    case 'DELETE':
      // Delete a note
      const { noteId: deleteNoteId } = req.body;
      
      if (!deleteNoteId) {
        return res.status(400).json({ success: false, message: 'Note ID is required' });
      }

      const deleteNoteIndex = order.notes.findIndex(note => note.id === deleteNoteId);
      
      if (deleteNoteIndex === -1) {
        return res.status(404).json({ success: false, message: 'Note not found' });
      }

      order.notes.splice(deleteNoteIndex, 1);
      orders[orderIndex] = order;
      
      if (writeOrders(orders)) {
        return res.status(200).json({
          success: true,
          message: 'Note deleted successfully'
        });
      } else {
        return res.status(500).json({ success: false, message: 'Failed to delete note' });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      return res.status(405).json({ success: false, message: `Method ${req.method} not allowed` });
  }
}