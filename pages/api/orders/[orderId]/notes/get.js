import fs from 'fs';
import path from 'path';

const ORDERS_FILE = path.join(process.cwd(), 'data', 'orders.json');

// Helper function to read orders
const readOrders = () => {
  try {
    if (!fs.existsSync(ORDERS_FILE)) {
      return [];
    }
    const data = fs.readFileSync(ORDERS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading orders:', error);
    return [];
  }
};

// Helper function to verify order ownership
const verifyOrderOwnership = (order, customerData) => {
  if (!order || !customerData) return false;
  
  // Check if customer owns this order
  return order.customerId === customerData.id ||
         order.customerId === customerData.phone ||
         order.customerPhone === customerData.phone ||
         order.customerEmail === customerData.email;
};

export default async function handler(req, res) {
  // Set response headers for UTF-8 encoding
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, message: `Method ${req.method} not allowed` });
  }

  const { orderId } = req.query;
  
  if (!orderId) {
    return res.status(400).json({ success: false, message: 'Order ID is required' });
  }

  // Get customer data from request body
  const { customerData } = req.body;
  
  if (!customerData) {
    return res.status(401).json({ success: false, message: 'Customer authentication required' });
  }

  const orders = readOrders();
  const order = orders.find(order => order.id === orderId);
  
  if (!order) {
    return res.status(404).json({ success: false, message: 'Order not found' });
  }

  // Verify ownership
  if (!verifyOrderOwnership(order, customerData)) {
    return res.status(403).json({ success: false, message: 'Access denied' });
  }

  // Return notes for the order
  return res.status(200).json({
    success: true,
    notes: order.notes || []
  });
}