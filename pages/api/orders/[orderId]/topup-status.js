import fs from 'fs';
import path from 'path';

const ORDERS_FILE = path.join(process.cwd(), 'data', 'orders.json');

const readOrdersData = () => {
  try {
    if (fs.existsSync(ORDERS_FILE)) {
      const data = fs.readFileSync(ORDERS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading orders data:', error);
    return [];
  }
};

export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  const { orderId } = req.query;

  if (!orderId) {
    return res.status(400).json({
      success: false,
      error: 'Order ID is required'
    });
  }

  try {
    const orders = readOrdersData();
    const order = orders.find(o => o.id === orderId);

    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    // Check if order has IF topup information
    const hasTopup = !!(order.ifTopup && order.ifTopup.status === 'completed');
    
    const status = {
      hasTopup,
      toppedUpBy: order.ifTopup?.adminUser || null,
      toppedUpAt: order.ifTopup?.completedAt || null,
      transactionId: order.ifTopup?.transactionId || null,
      productId: order.ifTopup?.productId || null,
      phoneNumber: order.ifTopup?.phoneNumber || null,
      error: order.ifTopup?.error || null
    };

    return res.status(200).json({
      success: true,
      orderId,
      status
    });
  } catch (error) {
    console.error('Error checking topup status:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
} 