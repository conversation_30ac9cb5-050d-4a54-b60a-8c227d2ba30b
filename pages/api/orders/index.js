import fs from 'fs'
import path from 'path'
import { 
  getAllOrders, 
  saveOrders, 
  getCustomerOrders,
  addOrder,
  updateOrder as updateOrderUtil,
  getOrderById
} from '../../../utils/orderUtils'

// Path to orders.json file
const dataDir = path.join(process.cwd(), 'data')
const ordersFile = path.join(dataDir, 'orders.json')

export default async function handler(req, res) {
  // Ensure data directory exists
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true })
  }
  
  // GET - retrieve orders (all or by customerId)
  if (req.method === 'GET') {
    try {
      const { customerId } = req.query
      
      // If customerId provided, return only that customer's orders
      if (customerId) {
        const customerOrders = getCustomerOrders(customerId)
        return res.status(200).json({
          customerId,
          total: customerOrders.length,
          orders: customerOrders
        })
      }
      
      // Otherwise return all orders
      const orders = getAllOrders()
      return res.status(200).json({
        total: orders.length,
        orders
      })
    } catch (error) {
      console.error('Error retrieving orders:', error)
      return res.status(500).json({ 
        error: 'Error retrieving orders', 
        message: error.message 
      })
    }
  }
  
  // POST - create a new order
  if (req.method === 'POST') {
    return createOrder(req, res)
  }
  
  // PUT - update an existing order
  if (req.method === 'PUT') {
    try {
      const { id, ...updateData } = req.body
      
      if (!id) {
        return res.status(400).json({ 
          error: 'Order ID is required for updates' 
        })
      }
      
      const updatedOrder = updateOrderUtil(id, updateData)
      
      if (!updatedOrder) {
        return res.status(404).json({ 
          error: 'Order not found' 
        })
      }
      
      return res.status(200).json(updatedOrder)
    } catch (error) {
      console.error('Error updating order:', error)
      return res.status(500).json({ 
        error: 'Error updating order', 
        message: error.message 
      })
    }
  }
  
  // Method not allowed
  return res.status(405).json({ error: 'Method not allowed' })
}

// Create a new order
async function createOrder(req, res) {
  try {
    const { 
      customerId, 
      items, 
      billingAddress, 
      shippingAddress, 
      paymentMethod,
      currency,
      subtotal,
      tax,
      shipping,
      total,
      status = 'pending'
    } = req.body
    
    // Validate required fields
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ 
        error: 'At least one item is required' 
      })
    }
    
    // Calculate totals if not provided
    let orderSubtotal = subtotal
    if (!orderSubtotal) {
      orderSubtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    }
    
    let orderTotal = total
    if (!orderTotal) {
      orderTotal = orderSubtotal + (parseFloat(tax) || 0) + (parseFloat(shipping) || 0)
    }
    
    // Create new order
    const now = new Date().toISOString()
    const newOrder = {
      customerId,
      items,
      billingAddress,
      shippingAddress: shippingAddress || billingAddress,
      paymentMethod,
      currency: currency || 'USD',
      subtotal: parseFloat(orderSubtotal),
      tax: parseFloat(tax || 0),
      shipping: parseFloat(shipping || 0),
      total: parseFloat(orderTotal),
      status,
      notes: '',
      createdAt: now,
      updatedAt: now
    }
    
    // Add the order to the central orders.json file
    // This will also handle creating per-customer order files for backward compatibility
    const createdOrder = addOrder(newOrder)
    
    // Return the created order
    return res.status(201).json(createdOrder)
  } catch (error) {
    console.error('Error creating order:', error)
    return res.status(500).json({ 
      error: 'Error creating order', 
      message: error.message 
    })
  }
} 