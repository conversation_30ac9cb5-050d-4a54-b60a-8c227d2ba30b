import fs from 'fs'
import path from 'path'
import {
  getOrderById,
  updateOrder,
  getAllOrders,
  saveOrders
} from '../../../utils/orderUtils';

// Path to orders data file
const dataDir = path.join(process.cwd(), 'data')
const ordersFile = path.join(dataDir, 'orders.json')

export default async function handler(req, res) {
  // Get order ID from the URL
  const { orderId } = req.query

  if (!orderId) {
    return res.status(400).json({ error: 'Order ID is required' })
  }

  console.log(`[API] Processing ${req.method} request for order ID: ${orderId}`)

  // GET - retrieve a specific order
  if (req.method === 'GET') {
    let foundOrder = null;

    // First try to read from central orders.json
    try {
      if (fs.existsSync(ordersFile)) {
        const data = fs.readFileSync(ordersFile, 'utf8')
        const orders = JSON.parse(data)
        if (Array.isArray(orders)) {
          // Find all matching orders
          const matchingOrders = orders.filter(order => 
            order.id === orderId || 
            order.orderId === orderId
          );
          
          if (matchingOrders.length > 0) {
            // If multiple orders with same ID exist, prioritize the one with items
            foundOrder = matchingOrders.find(order => order.items && order.items.length > 0) || matchingOrders[0];
          }
        }
      }
    } catch (error) {
      console.error('[API] Error reading central orders file:', error)
    }

    // If not found in central file, search in customer directories
    if (!foundOrder) {
      try {
        const customersDir = path.join(process.cwd(), 'data', 'customers');
        if (fs.existsSync(customersDir)) {
          const customerDirs = fs.readdirSync(customersDir);
          
          for (const customerDir of customerDirs) {
            const ordersDir = path.join(customersDir, customerDir, 'orders');
            if (fs.existsSync(ordersDir)) {
              const orderFiles = fs.readdirSync(ordersDir);
              
              for (const orderFile of orderFiles) {
                if (orderFile.endsWith('.json')) {
                  try {
                    const orderFilePath = path.join(ordersDir, orderFile);
                    const orderData = fs.readFileSync(orderFilePath, 'utf8');
                    const order = JSON.parse(orderData);
                    
                    if (order.id === orderId || order.orderId === orderId) {
                      foundOrder = order;
                      break;
                    }
                  } catch (error) {
                    console.error(`[API] Error reading order file ${orderFile}:`, error);
                  }
                }
              }
              
              if (foundOrder) break;
            }
          }
        }
      } catch (error) {
        console.error('[API] Error searching customer directories:', error);
      }
    }

    if (!foundOrder) {
      console.log(`[API] Order with ID ${orderId} not found`)
      return res.status(404).json({ 
        success: false, 
        error: 'Order not found' 
      })
    }

    console.log(`[API] Found order for ${orderId}`)

    return res.status(200).json({ 
      success: true, 
      order: foundOrder
    })
  }

  // PUT - update a specific order
  if (req.method === 'PUT') {
    try {
      const updateData = req.body;
      
      const updatedOrder = updateOrder(orderId, updateData);
      
      if (!updatedOrder) {
        return res.status(404).json({ 
          success: false,
          error: 'Order not found' 
        });
      }
      
      return res.status(200).json({ 
        success: true,
        order: updatedOrder 
      });
    } catch (error) {
      console.error(`[API] Error updating order ${orderId}:`, error);
      return res.status(500).json({ 
        success: false,
        error: 'Error updating order', 
        message: error.message 
      });
    }
  }

  // DELETE - remove a specific order
  if (req.method === 'DELETE') {
    try {
      // Get all orders
      const orders = getAllOrders();
      
      // Find the order index
      const orderIndex = orders.findIndex(order => 
        order.id === orderId || 
        order.orderId === orderId
      );
      
      if (orderIndex === -1) {
        return res.status(404).json({ 
          success: false,
          error: 'Order not found' 
        });
      }
      
      // Remove the order
      const removedOrder = orders.splice(orderIndex, 1)[0];
      
      // Save the updated orders
      saveOrders(orders);
      
      return res.status(200).json({
        success: true,
        message: 'Order deleted successfully',
        order: removedOrder
      });
    } catch (error) {
      console.error(`[API] Error deleting order ${orderId}:`, error);
      return res.status(500).json({ 
        success: false,
        error: 'Error deleting order', 
        message: error.message 
      });
    }
  }

  // Method not allowed
  return res.status(405).json({ 
    success: false,
    error: 'Method not allowed' 
  });
}