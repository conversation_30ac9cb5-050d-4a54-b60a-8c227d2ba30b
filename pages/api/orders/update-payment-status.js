import { NextApiRequest, NextApiResponse } from 'next';
import { getOrderById, updateOrderPaymentStatus } from '../../../utils/orderUtils';

export default async function handler(req, res) {
  // Allow POST method only
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { orderId, paymentStatus, paymentDetails } = req.body;

    // Validate required parameters
    if (!orderId || !paymentStatus) {
      return res.status(400).json({ 
        success: false, 
        message: 'Order ID and payment status are required'
      });
    }

    // Validate payment status value
    const validStatuses = ['paid', 'not_paid', 'failed', 'refunded'];
    if (!validStatuses.includes(paymentStatus)) {
      return res.status(400).json({ 
        success: false, 
        message: `Invalid payment status. Must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Check if order exists
    const existingOrder = getOrderById(orderId);
    if (!existingOrder) {
      return res.status(404).json({ 
        success: false, 
        message: `Order with ID ${orderId} not found`
      });
    }

    // Update the order payment status
    const success = updateOrderPaymentStatus(orderId, paymentStatus, paymentDetails || {});

    if (!success) {
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to update order payment status'
      });
    }

    // Get the updated order
    const updatedOrder = getOrderById(orderId);

    // Return success response
    return res.status(200).json({
      success: true,
      message: `Order payment status updated to ${paymentStatus}`,
      order: updatedOrder
    });
  } catch (error) {
    console.error('[Orders API] Error updating order payment status:', error);
    return res.status(500).json({
      success: false,
      message: 'Error updating order payment status',
      error: error.message
    });
  }
} 