import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Simple admin check
    const isAdmin = true; // For development, always allow access
    
    if (!isAdmin) {
      return res.status(401).json({ 
        success: false, 
        message: 'Unauthorized - admin access required' 
      });
    }

    const { storeId } = req.query;

    // Read orders from JSON file
    const dataFilePath = path.join(process.cwd(), 'data', 'orders.json');
    const fileData = fs.readFileSync(dataFilePath, 'utf8');
    let orders = JSON.parse(fileData);

    // Filter by store if provided
    if (storeId) {
      orders = orders.filter(order => order.storeId === storeId);
    }

    // Sort by creation date (newest first)
    orders.sort((a, b) => {
      const dateA = new Date(a.createdAt || a.updatedAt || 0);
      const dateB = new Date(b.createdAt || b.updatedAt || 0);
      return dateB - dateA; // Descending order
    });

    // Normalize orders to ensure they have the expected fields
    const processedOrders = orders.map(order => {
      const now = new Date();
      const validUntil = order.validUntil ? new Date(order.validUntil) : null;
      
      // Get customer info from various possible locations in the data
      const customerName = order.customerName || 
                          (order.recipientInfo ? order.recipientInfo.fullName : null) || 
                          'Unknown';
      
      const customerPhone = order.customerPhone || 
                           (order.recipientInfo ? order.recipientInfo.phoneNumber : null) || 
                           '';
      
      const customerEmail = order.customerEmail || 
                           (order.recipientInfo ? order.recipientInfo.email : null) || 
                           '';
      
      return {
        id: order.id || '',
        customerId: order.customerId || '',
        customerName,
        customerPhone,
        customerEmail,
        items: order.items || [],
        totalAmount: order.totalAmount || order.amount || 0,
        currency: order.currency || 'VND',
        status: order.status || 'pending',
        paymentStatus: order.paymentStatus || 'not_paid',
        paymentMethod: order.paymentMethod || '',
        paymentSubMethod: order.paymentSubMethod || '',
        createdAt: order.createdAt || '',
        updatedAt: order.updatedAt || '',
        validUntil: order.validUntil || '',
        isExpired: order.isExpired !== undefined ? order.isExpired : (validUntil ? validUntil < now : false),
        storeId: order.storeId || '',
        paymentInfo: order.paymentInfo || null,
        shippingAddress: order.shippingAddress || 
                        (order.recipientInfo ? order.recipientInfo.address : null) || 
                        '',
        fee: order.fee || 0,
        // Preserve all other fields from the original order
        ...order
      };
    });

    return res.status(200).json({ 
      success: true, 
      orders: processedOrders 
    });
  } catch (error) {
    console.error('Error fetching all orders:', error);
    
    // If the file doesn't exist, return an empty array
    if (error.code === 'ENOENT') {
      return res.status(200).json({ 
        success: true, 
        orders: [] 
      });
    }
    
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message
    });
  }
} 