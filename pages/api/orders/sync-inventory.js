import { getAllOrders, saveOrders } from '../../../utils/orderUtils';
import inventoryData from '../../../utils/inventory.json';

/**
 * API endpoint to sync order prices and currencies with inventory data
 * This fixes the currency inconsistency and updates prices with real inventory data
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { orderId } = req.body;
    
    // Get all orders
    const orders = getAllOrders();
    let updatedCount = 0;
    
    // Create a map of inventory items by SKU for quick lookup
    const inventoryMap = new Map();
    inventoryData.forEach(item => {
      if (item.sku) {
        inventoryMap.set(item.sku, item);
      }
    });
    
    // Function to update a single order
    const updateOrderWithInventory = (order) => {
      let orderUpdated = false;
      const updatedOrder = { ...order };
      
      // Update items/products with inventory data
      if (order.items && order.items.length > 0) {
        updatedOrder.items = order.items.map(item => {
          if (item.sku) {
            const inventoryItem = inventoryMap.get(item.sku);
            if (inventoryItem) {
              const updatedItem = { ...item };
              
              // Update price if different
              if (inventoryItem.price !== undefined && inventoryItem.price !== item.price) {
                updatedItem.price = inventoryItem.price;
                orderUpdated = true;
              }
              
              // Update currency based on inventory
              if (inventoryItem.currency && inventoryItem.currency !== order.currency) {
                updatedOrder.currency = inventoryItem.currency;
                orderUpdated = true;
              }
              
              return updatedItem;
            }
          }
          return item;
        });
      }
      
      // Update products array if it exists
      if (order.products && order.products.length > 0) {
        updatedOrder.products = order.products.map(product => {
          if (product.sku) {
            const inventoryItem = inventoryMap.get(product.sku);
            if (inventoryItem) {
              const updatedProduct = { ...product };
              
              // Update price if different
              if (inventoryItem.price !== undefined && inventoryItem.price !== product.price) {
                updatedProduct.price = inventoryItem.price;
                orderUpdated = true;
              }
              
              // Update currency based on inventory
              if (inventoryItem.currency && inventoryItem.currency !== order.currency) {
                updatedOrder.currency = inventoryItem.currency;
                orderUpdated = true;
              }
              
              return updatedProduct;
            }
          }
          return product;
        });
      }
      
      // Recalculate total amount if items were updated
      if (orderUpdated) {
        let totalAmount = 0;
        
        // Calculate from items
        if (updatedOrder.items) {
          totalAmount = updatedOrder.items.reduce((sum, item) => {
            return sum + (item.price * item.quantity);
          }, 0);
        }
        
        // Calculate from products if items don't exist
        if (updatedOrder.products && (!updatedOrder.items || updatedOrder.items.length === 0)) {
          totalAmount = updatedOrder.products.reduce((sum, product) => {
            return sum + (product.price * product.quantity);
          }, 0);
        }
        
        updatedOrder.totalAmount = totalAmount;
        updatedOrder.amount = totalAmount; // Keep both for compatibility
        updatedOrder.updatedAt = new Date().toISOString();
      }
      
      return orderUpdated ? updatedOrder : null;
    };
    
    // Update specific order or all orders
    if (orderId) {
      // Update specific order
      const orderIndex = orders.findIndex(order => order.id === orderId || order.orderId === orderId);
      if (orderIndex === -1) {
        return res.status(404).json({
          success: false,
          error: 'Order not found'
        });
      }
      
      const updatedOrder = updateOrderWithInventory(orders[orderIndex]);
      if (updatedOrder) {
        orders[orderIndex] = updatedOrder;
        updatedCount = 1;
      }
    } else {
      // Update all orders
      for (let i = 0; i < orders.length; i++) {
        const updatedOrder = updateOrderWithInventory(orders[i]);
        if (updatedOrder) {
          orders[i] = updatedOrder;
          updatedCount++;
        }
      }
    }
    
    // Save updated orders
    if (updatedCount > 0) {
      saveOrders(orders);
    }
    
    return res.status(200).json({
      success: true,
      message: `Successfully updated ${updatedCount} order(s) with inventory data`,
      updatedCount,
      orderId: orderId || null
    });
    
  } catch (error) {
    console.error('Error syncing orders with inventory:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to sync orders with inventory data',
      details: error.message
    });
  }
}