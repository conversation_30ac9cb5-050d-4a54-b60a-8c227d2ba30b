import nodemailer from 'nodemailer';

// SMTP Email configuration - hardcoded
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: 'email-smtp.us-east-1.amazonaws.com',        // Replace with your SMTP host
    port: 587,                         // Replace with your SMTP port (587 for TLS, 465 for SSL, 25 for non-secure)
    secure: false,                     // true for 465, false for other ports
    auth: {
      user: 'AKIA5BI3T5T7LIJKMFOC',      // Replace with your SMTP username
      pass: 'BK6UCzJke2U3RVa3BvNCjA7gvWf9sdHQZvLOxSmmG/kp'       // Replace with your SMTP password
    },
    tls: {
      rejectUnauthorized: false        // Accept self-signed certificates if needed
    }
  });
};

// Admin email to receive notifications - hardcoded
const ADMIN_EMAIL = '<EMAIL>';

const formatPaymentNotificationEmail = (paymentData) => {
  const { 
    orderId, 
    paymentMethod, 
    status, 
    totalAmount, 
    currency, 
    timestamp,
    customerInfo,
    items
  } = paymentData;

  const vietnameseStatus = {
    'INITIATED': 'Đã khởi tạo',
    'COMPLETED': 'Hoàn thành',
    'FAILED': 'Thất bại',
    'PENDING': 'Đang chờ'
  };

  const paymentMethodNames = {
    '7-Eleven (iBON)': '7-11 iBON',
    '7-Eleven (Credit/Debit Card)': '7-11 Card',
    'FamilyMart': 'FamilyMart',
    'SINOPAC QR Payment': 'Sinopac Dynamic QR Code'
  };

  const statusIcon = {
    'INITIATED': '🔄',
    'COMPLETED': '✅',
    'FAILED': '❌',
    'PENDING': '⏳'
  };

  return {
    subject: `${statusIcon[status]} Thanh toán ${vietnameseStatus[status]} - Đơn hàng #${orderId}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">
            ${statusIcon[status]} Thông báo thanh toán
          </h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 20px; border: 1px solid #e9ecef;">
          <h2 style="color: #495057; margin-top: 0;">Chi tiết đơn hàng</h2>
          
          <table style="width: 100%; border-collapse: collapse;">
            <tr style="border-bottom: 1px solid #dee2e6;">
              <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Mã đơn hàng:</td>
              <td style="padding: 8px 0;">#${orderId}</td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
              <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Trạng thái:</td>
              <td style="padding: 8px 0;">
                <span style="background: ${status === 'COMPLETED' ? '#d4edda' : status === 'INITIATED' ? '#cce7ff' : status === 'FAILED' ? '#f8d7da' : '#fff3cd'}; 
                             color: ${status === 'COMPLETED' ? '#155724' : status === 'INITIATED' ? '#004085' : status === 'FAILED' ? '#721c24' : '#856404'}; 
                             padding: 4px 8px; border-radius: 4px; font-weight: bold;">
                  ${vietnameseStatus[status]}
                </span>
              </td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
              <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Phương thức thanh toán:</td>
              <td style="padding: 8px 0;">${paymentMethodNames[paymentMethod] || paymentMethod}</td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
              <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Số tiền:</td>
              <td style="padding: 8px 0; font-weight: bold; font-size: 18px; color: #007bff;">
                ${currency}${totalAmount?.toLocaleString() || 'N/A'}
              </td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
              <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Thời gian:</td>
              <td style="padding: 8px 0;">${new Date(timestamp).toLocaleString('vi-VN')}</td>
            </tr>
          </table>
        </div>

        ${customerInfo ? `
        <div style="background: #e3f2fd; padding: 20px; border: 1px solid #bbdefb;">
          <h3 style="color: #1976d2; margin-top: 0;">Thông tin khách hàng</h3>
          <table style="width: 100%; border-collapse: collapse;">
            ${customerInfo.name ? `
            <tr>
              <td style="padding: 4px 0; font-weight: bold; color: #6c757d; width: 30%;">Tên:</td>
              <td style="padding: 4px 0;">${customerInfo.name}</td>
            </tr>` : ''}
            ${customerInfo.email ? `
            <tr>
              <td style="padding: 4px 0; font-weight: bold; color: #6c757d;">Email:</td>
              <td style="padding: 4px 0;">${customerInfo.email}</td>
            </tr>` : ''}
            ${customerInfo.phone ? `
            <tr>
              <td style="padding: 4px 0; font-weight: bold; color: #6c757d;">Điện thoại:</td>
              <td style="padding: 4px 0;">${customerInfo.phone}</td>
            </tr>` : ''}
          </table>
        </div>` : ''}

        ${items && items.length > 0 ? `
        <div style="background: #f3e5f5; padding: 20px; border: 1px solid #ce93d8;">
          <h3 style="color: #7b1fa2; margin-top: 0;">Sản phẩm (${items.length})</h3>
          ${items.map(item => `
            <div style="border-bottom: 1px solid #e1bee7; padding: 8px 0;">
              <strong>${item.name || item.sku}</strong><br>
              <span style="color: #6c757d; font-size: 14px;">
                SKU: ${item.sku} | Số lượng: ${item.quantity} | Giá: ${item.currency || currency}${item.price?.toLocaleString()}
              </span>
            </div>
          `).join('')}
        </div>` : ''}

        <div style="background: #fff; padding: 20px; border: 1px solid #e9ecef; border-radius: 0 0 8px 8px;">
          <p style="margin: 0; color: #6c757d; font-size: 14px;">
            📧 Email tự động từ hệ thống MAG GROUP<br>
            🕒 ${new Date().toLocaleString('vi-VN')}
          </p>
        </div>
      </div>
    `,
    text: `
      THÔNG BÁO THANH TOÁN - ${vietnameseStatus[status]}
      
      Mã đơn hàng: #${orderId}
      Trạng thái: ${vietnameseStatus[status]}
      Phương thức: ${paymentMethodNames[paymentMethod] || paymentMethod}
      Số tiền: ${currency}${totalAmount?.toLocaleString() || 'N/A'}
      Thời gian: ${new Date(timestamp).toLocaleString('vi-VN')}
      
      ${customerInfo ? `
      THÔNG TIN KHÁCH HÀNG:
      Tên: ${customerInfo.name || 'N/A'}
      Email: ${customerInfo.email || 'N/A'}
      Điện thoại: ${customerInfo.phone || 'N/A'}
      ` : ''}
      
      ${items && items.length > 0 ? `
      SẢN PHẨM (${items.length}):
      ${items.map(item => `- ${item.name || item.sku} (${item.quantity}x ${item.currency || currency}${item.price?.toLocaleString()})`).join('\n')}
      ` : ''}
    `
  };
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const paymentData = req.body;

    // Validate required fields
    if (!paymentData.orderId || !paymentData.paymentMethod || !paymentData.status) {
      return res.status(400).json({ 
        error: 'Missing required fields: orderId, paymentMethod, status' 
      });
    }

    // Create email content
    const emailContent = formatPaymentNotificationEmail(paymentData);

    // Create transporter
    const transporter = createTransporter();

    // Send email
    const mailOptions = {
      from: '<EMAIL>',   // Replace with your sender email
      to: ADMIN_EMAIL,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text
    };

    console.log(`[EMAIL_NOTIFICATION] Sending payment alert for order ${paymentData.orderId}`);

    const info = await transporter.sendMail(mailOptions);

    console.log(`[EMAIL_NOTIFICATION] Email sent successfully: ${info.messageId}`);

    res.status(200).json({ 
      success: true, 
      message: 'Email notification sent successfully',
      messageId: info.messageId
    });

  } catch (error) {
    console.error('[EMAIL_NOTIFICATION] Error sending email:', error);
    
    // Don't fail the payment process if email fails
    res.status(200).json({ 
      success: false, 
      error: error.message,
      warning: 'Email notification failed but payment process continues'
    });
  }
} 