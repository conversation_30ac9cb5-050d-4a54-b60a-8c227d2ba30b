import { v4 as uuidv4 } from 'uuid';
import inventoryData from '../../utils/inventory.json';
import fs from 'fs';
import path from 'path';
import {
  getCurrentInventoryCount,
  getInventoryType,
  getInventoryItems,
  getAvailableInventoryItems,
  isInStock,
  isLowStock,
  isOutOfStock,
  filterInventoryItems,
  sortInventoryItems,
  paginateInventoryItems,
  getInventoryStatistics
} from '../../utils/inventoryManager.js';

// Initialize inventory items with data from JSON file
let inventoryItems = inventoryData.map(item => ({
  id: item.sku || uuidv4(),
  sku: item.sku,
  name: item.name,
  brand: item.provider || 'Unknown',
  price: parseFloat(item.price),
  categories: Array.isArray(item.categories) ? item.categories : [item.categories],
  image: Array.isArray(item.image) ? item.image[0] : item.image,
  description: item.description,
  currentInventory: getCurrentInventoryCount(item), // Backward compatibility
  inventory: item.inventory || { type: 'same', count: getCurrentInventoryCount(item) }, // New structure
  currency: item.currency,
  buyfrom: item.buyfrom,
  unit: item.unit,
  activestatus: item.activestatus,
  configurations: item.configurations,
  // Include important fields for IF topup functionality
  auto_activate: item.auto_activate,
  sku_manufacturer: item.sku_manufacturer,
  notes: item.notes,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}));

// Function to save inventory back to JSON file
function saveInventoryToFile() {
  const inventoryPath = path.join(process.cwd(), 'utils', 'inventory.json');
  
  // Read the current file content
  const currentFileContent = JSON.parse(fs.readFileSync(inventoryPath, 'utf8'));
  
  // Update only the changed items
  const updatedContent = currentFileContent.map(originalItem => {
    const updatedItem = inventoryItems.find(item => item.sku === originalItem.sku);
    if (updatedItem) {
      // Update price and inventory structure
      const updatedItemData = {
        ...originalItem,
        price: updatedItem.price
      };
      
      // Handle new inventory structure
      if (updatedItem.inventory) {
        updatedItemData.inventory = updatedItem.inventory;
        // Remove old currentInventory field if it exists
        delete updatedItemData.currentInventory;
      } else {
        // Fallback to old structure for backward compatibility
        updatedItemData.currentInventory = updatedItem.currentInventory;
      }
      
      return updatedItemData;
    }
    return originalItem;
  });
  
  fs.writeFileSync(inventoryPath, JSON.stringify(updatedContent, null, 2));
}

// Debug logging
console.log('Debug: First few items in inventoryItems:');
console.log(inventoryItems.slice(0, 3));

export default function handler(req, res) {
  const { method } = req;

  switch (method) {
    case 'GET':
      return getInventory(req, res);
    case 'POST':
      return createInventoryItem(req, res);
    case 'PUT':
      return updateInventoryItem(req, res);
    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT']);
      res.status(405).end(`Method ${method} Not Allowed`);
  }
}

// GET /api/inventory
async function getInventory(req, res) {
  try {
    const { 
      id, 
      sku, 
      type, 
      stockStatus, 
      category, 
      provider, 
      search, 
      minPrice, 
      maxPrice,
      sortBy,
      sortOrder,
      page,
      limit,
      stats
    } = req.query;
    
    // Return statistics if requested
    if (stats === 'true') {
      const statistics = getInventoryStatistics(inventoryItems);
      return res.status(200).json(statistics);
    }
    
    if (id || sku) {
      // Get specific item by ID or SKU
      const item = inventoryItems.find(item => 
        (id && item.id === id) || (sku && item.sku === sku)
      );
      
      if (!item) {
        return res.status(404).json({ error: 'Item not found' });
      }
      
      return res.status(200).json(item);
    }
    
    // Apply filters
    let filteredItems = filterInventoryItems(inventoryItems, {
      type,
      stockStatus,
      category,
      provider,
      sku,
      search,
      minPrice: minPrice ? parseFloat(minPrice) : undefined,
      maxPrice: maxPrice ? parseFloat(maxPrice) : undefined
    });
    
    // Apply sorting
    if (sortBy) {
      filteredItems = sortInventoryItems(filteredItems, sortBy, sortOrder);
    }
    
    // Apply pagination
    if (page && limit) {
      const paginatedResult = paginateInventoryItems(
        filteredItems, 
        parseInt(page), 
        parseInt(limit)
      );
      return res.status(200).json(paginatedResult);
    }
    
    // Return all filtered items
    return res.status(200).json(filteredItems);
  } catch (error) {
    return res.status(500).json({ error: 'Failed to fetch inventory' });
  }
}

// POST /api/inventory
async function createInventoryItem(req, res) {
  try {
    const { name, brand, price, categories, image, description, currentInventory, inventory, sku } = req.body;

    // Validate required fields
    if (!name || !brand || !price || !categories || !description || !image || !sku) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Validate inventory data
    let inventoryData;
    if (inventory && typeof inventory === 'object') {
      inventoryData = inventory;
      if (!inventory.type || !['separate', 'same'].includes(inventory.type)) {
        return res.status(400).json({ error: 'Invalid inventory type. Must be "separate" or "same"' });
      }
      if (typeof inventory.count !== 'number' || inventory.count < 0) {
        return res.status(400).json({ error: 'Invalid inventory count' });
      }
    } else if (currentInventory !== undefined) {
      // Backward compatibility
      inventoryData = { type: 'same', count: parseInt(currentInventory) };
    } else {
      return res.status(400).json({ error: 'Missing inventory data' });
    }

    // Create new inventory item
    const newItem = {
      id: sku,
      sku,
      name,
      brand,
      price: parseFloat(price),
      categories: Array.isArray(categories) ? categories : categories.split(',').map(cat => cat.trim()),
      image,
      description,
      currentInventory: inventoryData.count, // Backward compatibility
      inventory: inventoryData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add to inventory
    inventoryItems.push(newItem);

    // Save to file
    saveInventoryToFile();

    return res.status(201).json(newItem);
  } catch (error) {
    return res.status(500).json({ error: 'Failed to create inventory item' });
  }
}

// PUT /api/inventory
async function updateInventoryItem(req, res) {
  try {
    const { id, price, currentInventory, inventory } = req.body;

    // Validate required fields
    if (!id) {
      return res.status(400).json({ error: 'Missing required fields: id' });
    }
    
    if (!price && !inventory && currentInventory === undefined) {
      return res.status(400).json({ error: 'At least price or inventory data must be provided' });
    }

    // Find and update the item by ID or SKU
    const itemIndex = inventoryItems.findIndex(item => item.id === id || item.sku === id);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Validate inventory data
    let inventoryData;
    if (inventory && typeof inventory === 'object') {
      inventoryData = inventory;
      if (!inventory.type || !['separate', 'same'].includes(inventory.type)) {
        return res.status(400).json({ error: 'Invalid inventory type. Must be "separate" or "same"' });
      }
      if (typeof inventory.count !== 'number' || inventory.count < 0) {
        return res.status(400).json({ error: 'Invalid inventory count' });
      }
    } else if (currentInventory !== undefined) {
      // Backward compatibility
      inventoryData = { type: 'same', count: parseInt(currentInventory) };
    } else {
      // Keep existing inventory structure if not provided
      inventoryData = inventoryItems[itemIndex].inventory;
    }

    // Update only allowed fields
    const updatedItem = {
      ...inventoryItems[itemIndex],
      updatedAt: new Date().toISOString()
    };
    
    // Update price if provided
    if (price !== undefined) {
      updatedItem.price = parseFloat(price);
    }
    
    // Update inventory if provided
    if (inventoryData) {
      updatedItem.currentInventory = inventoryData.count; // Backward compatibility
      updatedItem.inventory = inventoryData;
    }
    
    inventoryItems[itemIndex] = updatedItem;

    // Save to file
    saveInventoryToFile();

    return res.status(200).json(inventoryItems[itemIndex]);
  } catch (error) {
    return res.status(500).json({ error: 'Failed to update inventory item' });
  }
}