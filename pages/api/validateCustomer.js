import { fetchStore } from '../../utils/storeProvider';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { customerId, customerInfo } = req.body;
    
    if (!customerId) {
      return res.status(400).json({ success: false, message: 'Customer ID is required' });
    }
    
    console.log('[CUSTOMER_VALIDATE] Validating customer:', customerId);
    
    // Here you would typically validate the customer against your database
    // For now, we'll just return success and merge their carts
    
    // Get previous user ID from request if available
    const previousId = req.body.previousId;
    
    // If we have a previousId and it's different from customerId, merge carts
    if (previousId && previousId !== customerId) {
      try {
        console.log('[CUSTOMER_VALIDATE] Will merge carts from', previousId, 'to', customerId);
        
        // Call the merge cart API
        const mergeResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/api/cart/merge`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            customerId,
            previousId,
            storeId: req.body.storeId || null
          })
        });
        
        if (mergeResponse.ok) {
          console.log('[CUSTOMER_VALIDATE] Cart merge successful');
        } else {
          console.error('[CUSTOMER_VALIDATE] Cart merge failed:', await mergeResponse.text());
        }
      } catch (mergeError) {
        console.error('[CUSTOMER_VALIDATE] Error merging carts:', mergeError);
      }
    }
    
    // Return customer validation result
    return res.status(200).json({
      success: true,
      customerId,
      message: 'Customer validated successfully',
      // Include any customer info that should be stored
      customerInfo: {
        ...customerInfo,
        lastValidated: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('[CUSTOMER_VALIDATE] Error:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
} 