import fs from 'fs';
import path from 'path';

const LOYALTY_RULES_FILE = path.join(process.cwd(), 'data', 'loyalty-rules.json');

// Ensure data directory exists
const ensureDataDirectory = () => {
  const dataDir = path.dirname(LOYALTY_RULES_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Read loyalty rules from file
const readLoyaltyRules = () => {
  try {
    if (!fs.existsSync(LOYALTY_RULES_FILE)) {
      return null;
    }
    const data = fs.readFileSync(LOYALTY_RULES_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading loyalty rules:', error);
    return null;
  }
};

// Write loyalty rules to file
const writeLoyaltyRules = (rules) => {
  try {
    ensureDataDirectory();
    rules.lastUpdated = new Date().toISOString();
    fs.writeFileSync(LOYALTY_RULES_FILE, JSON.stringify(rules, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing loyalty rules:', error);
    return false;
  }
};

// Validate loyalty rules structure
const validateLoyaltyRules = (rules) => {
  const errors = [];
  
  if (!rules || typeof rules !== 'object') {
    errors.push('Rules must be an object');
    return errors;
  }
  
  // Check required top-level properties
  const requiredProps = ['version', 'settings', 'customerTiers', 'earningRules'];
  requiredProps.forEach(prop => {
    if (!rules[prop]) {
      errors.push(`Missing required property: ${prop}`);
    }
  });
  
  // Validate customer tiers
  if (rules.customerTiers) {
    Object.entries(rules.customerTiers).forEach(([tierKey, tier]) => {
      if (!tier.name || typeof tier.minPoints !== 'number' || typeof tier.maxPoints !== 'number') {
        errors.push(`Invalid tier configuration: ${tierKey}`);
      }
      if (tier.minPoints >= tier.maxPoints) {
        errors.push(`Invalid tier range for ${tierKey}: minPoints must be less than maxPoints`);
      }
    });
  }
  
  // Validate earning rules
  if (rules.earningRules) {
    Object.entries(rules.earningRules).forEach(([categoryKey, category]) => {
      if (!category.rules || !Array.isArray(category.rules)) {
        errors.push(`Invalid earning rules category: ${categoryKey}`);
      } else {
        category.rules.forEach((rule, index) => {
          if (!rule.id || !rule.name || !rule.type || typeof rule.value !== 'number') {
            errors.push(`Invalid earning rule in ${categoryKey} at index ${index}`);
          }
        });
      }
    });
  }
  
  return errors;
};

export default async function handler(req, res) {
  const { method } = req;
  
  try {
    switch (method) {
      case 'GET':
        const rules = readLoyaltyRules();
        if (!rules) {
          return res.status(404).json({ error: 'Loyalty rules not found' });
        }
        return res.status(200).json(rules);
        
      case 'PUT':
        const { rules: newRules } = req.body;
        
        if (!newRules) {
          return res.status(400).json({ error: 'Rules data is required' });
        }
        
        // Validate the new rules
        const validationErrors = validateLoyaltyRules(newRules);
        if (validationErrors.length > 0) {
          return res.status(400).json({ 
            error: 'Invalid rules configuration', 
            details: validationErrors 
          });
        }
        
        // Save the rules
        const success = writeLoyaltyRules(newRules);
        if (!success) {
          return res.status(500).json({ error: 'Failed to save loyalty rules' });
        }
        
        return res.status(200).json({ 
          message: 'Loyalty rules updated successfully',
          lastUpdated: newRules.lastUpdated
        });
        
      case 'PATCH':
        // Partial update of specific rule sections
        const { section, data } = req.body;
        
        if (!section || !data) {
          return res.status(400).json({ error: 'Section and data are required for partial update' });
        }
        
        const currentRules = readLoyaltyRules();
        if (!currentRules) {
          return res.status(404).json({ error: 'Loyalty rules not found' });
        }
        
        // Update specific section
        const validSections = ['settings', 'customerTiers', 'earningRules', 'redemptionRules', 'specialEvents', 'notifications'];
        if (!validSections.includes(section)) {
          return res.status(400).json({ error: `Invalid section. Must be one of: ${validSections.join(', ')}` });
        }
        
        currentRules[section] = { ...currentRules[section], ...data };
        
        // Validate updated rules
        const patchValidationErrors = validateLoyaltyRules(currentRules);
        if (patchValidationErrors.length > 0) {
          return res.status(400).json({ 
            error: 'Invalid rules configuration after update', 
            details: patchValidationErrors 
          });
        }
        
        // Save updated rules
        const patchSuccess = writeLoyaltyRules(currentRules);
        if (!patchSuccess) {
          return res.status(500).json({ error: 'Failed to save updated loyalty rules' });
        }
        
        return res.status(200).json({ 
          message: `Loyalty rules section '${section}' updated successfully`,
          lastUpdated: currentRules.lastUpdated
        });
        
      case 'POST':
        // Reset to default rules or create backup
        const { action } = req.body;
        
        if (action === 'backup') {
          const existingRules = readLoyaltyRules();
          if (!existingRules) {
            return res.status(404).json({ error: 'No rules to backup' });
          }
          
          const backupPath = path.join(process.cwd(), 'data', `loyalty-rules-backup-${Date.now()}.json`);
          try {
            fs.writeFileSync(backupPath, JSON.stringify(existingRules, null, 2));
            return res.status(200).json({ 
              message: 'Backup created successfully',
              backupPath: path.basename(backupPath)
            });
          } catch (error) {
            return res.status(500).json({ error: 'Failed to create backup' });
          }
        }
        
        return res.status(400).json({ error: 'Invalid action. Supported actions: backup' });
        
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'PATCH', 'POST']);
        return res.status(405).json({ error: `Method ${method} not allowed` });
    }
  } catch (error) {
    console.error('Loyalty rules API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}