import fs from 'fs';
import path from 'path';

export default function handler(req, res) {
  try {
    // Read orders from the JSON file
    const ordersFilePath = path.join(process.cwd(), 'data', 'orders.json');
    const ordersData = fs.existsSync(ordersFilePath) 
      ? JSON.parse(fs.readFileSync(ordersFilePath, 'utf8')) 
      : [];

    // Calculate total sales by currency
    const totalSales = ordersData
      .filter(order => order.paymentStatus === 'paid') // Only count paid orders
      .reduce((currencyTotals, order) => {
        const amount = order.totalAmount || 0;
        const currency = order.currency || 'VND';
        
        if (!currencyTotals[currency]) {
          currencyTotals[currency] = 0;
        }
        currencyTotals[currency] += amount;
        
        return currencyTotals;
      }, {});

    // Return the data
    res.status(200).json({
      success: true,
      orders: ordersData,
      stats: {
        totalSales,
        totalOrders: ordersData.length
      }
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch orders data',
      message: error.message
    });
  }
} 