import fs from 'fs';
import path from 'path';

const APPROVAL_HISTORY_FILE = path.join(process.cwd(), 'data', 'approval-history.json');

// Ensure data directory exists
const ensureDataDirectory = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Read approval history from file
const readApprovalHistory = () => {
  try {
    ensureDataDirectory();
    if (fs.existsSync(APPROVAL_HISTORY_FILE)) {
      const data = fs.readFileSync(APPROVAL_HISTORY_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading approval history:', error);
    return [];
  }
};

// Write approval history to file
const writeApprovalHistory = (history) => {
  try {
    ensureDataDirectory();
    fs.writeFileSync(APPROVAL_HISTORY_FILE, JSON.stringify(history, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing approval history:', error);
    return false;
  }
};

// Add new approval history entry
const addApprovalHistoryEntry = (entry) => {
  const history = readApprovalHistory();
  const newEntry = {
    id: Date.now().toString(),
    timestamp: new Date().toISOString(),
    ...entry
  };
  
  history.unshift(newEntry); // Add to beginning of array
  
  // Keep only last 100 entries per customer
  const customerHistory = history.filter(h => h.customerId === entry.customerId);
  if (customerHistory.length > 100) {
    const otherHistory = history.filter(h => h.customerId !== entry.customerId);
    const trimmedCustomerHistory = customerHistory.slice(0, 100);
    const updatedHistory = [...trimmedCustomerHistory, ...otherHistory];
    writeApprovalHistory(updatedHistory);
  } else {
    writeApprovalHistory(history);
  }
  
  return newEntry;
};

// Get approval history for a specific customer
const getCustomerApprovalHistory = (customerId) => {
  const history = readApprovalHistory();
  return history
    .filter(entry => entry.customerId === customerId)
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
};

// Generate mock history data for demo purposes
const generateMockHistory = (customerId) => {
  const mockHistory = [
    {
      customerId,
      section: 'phone',
      oldStatus: 'pending',
      newStatus: 'rejected',
      reason: 'Số điện thoại không hợp lệ hoặc không thể xác minh',
      adminId: 'admin_001',
      timestamp: '2024-01-21T09:15:00Z'
    },
    {
      customerId,
      section: 'fullName',
      oldStatus: 'pending',
      newStatus: 'approved',
      reason: '',
      adminId: 'admin_001',
      timestamp: '2024-01-20T14:30:00Z'
    },
    {
      customerId,
      section: 'email',
      oldStatus: 'pending',
      newStatus: 'approved',
      reason: '',
      adminId: 'admin_002',
      timestamp: '2024-01-20T11:45:00Z'
    },
    {
      customerId,
      section: 'passportNumber',
      oldStatus: 'pending',
      newStatus: 'requires_update',
      reason: 'Cần cung cấp ảnh hộ chiếu rõ nét hơn và đầy đủ thông tin',
      adminId: 'admin_001',
      timestamp: '2024-01-19T16:20:00Z'
    },
    {
      customerId,
      section: 'currentAddress',
      oldStatus: 'pending',
      newStatus: 'approved',
      reason: '',
      adminId: 'admin_003',
      timestamp: '2024-01-19T10:10:00Z'
    }
  ];
  
  return mockHistory.map((entry, index) => ({
    id: `mock_${customerId}_${index}`,
    ...entry
  }));
};

export default async function handler(req, res) {
  if (req.method === 'GET') {
    try {
      const { customerId } = req.query;
      
      if (!customerId) {
        return res.status(400).json({
          success: false,
          error: 'Customer ID is required'
        });
      }
      
      let history = getCustomerApprovalHistory(customerId);
      
      // If no history found, generate mock data for demo
      if (history.length === 0) {
        history = generateMockHistory(customerId);
      }
      
      res.status(200).json({
        success: true,
        history
      });
    } catch (error) {
      console.error('Error fetching approval history:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch approval history'
      });
    }
  } else if (req.method === 'POST') {
    try {
      const { customerId, section, oldStatus, newStatus, reason, adminId } = req.body;
      
      if (!customerId || !section || !newStatus) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: customerId, section, newStatus'
        });
      }
      
      const entry = addApprovalHistoryEntry({
        customerId,
        section,
        oldStatus,
        newStatus,
        reason: reason || '',
        adminId: adminId || 'system'
      });
      
      res.status(201).json({
        success: true,
        entry
      });
    } catch (error) {
      console.error('Error adding approval history entry:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to add approval history entry'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).json({
      success: false,
      error: `Method ${req.method} not allowed`
    });
  }
}

// Export helper functions for use in other API routes
export { addApprovalHistoryEntry, getCustomerApprovalHistory };