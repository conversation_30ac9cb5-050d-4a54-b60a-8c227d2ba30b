import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { sheetsId } = req.body;

  if (!sheetsId) {
    return res.status(400).json({ error: 'Google Sheets ID is required' });
  }

  // Validate required environment variables
  console.log('Checking environment variables...');
  
  if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL) {
    console.error('Missing GOOGLE_SERVICE_ACCOUNT_EMAIL environment variable');
    return res.status(500).json({ 
      error: 'Server configuration error: Missing Google service account email' 
    });
  }
  
  if (!process.env.GOOGLE_PRIVATE_KEY) {
    console.error('Missing GOOGLE_PRIVATE_KEY environment variable');
    return res.status(500).json({ 
      error: 'Server configuration error: Missing Google private key' 
    });
  }

  console.log('Environment variables validated successfully');
  console.log('Service account email:', process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL);
  console.log('Private key length:', process.env.GOOGLE_PRIVATE_KEY?.length || 0);

  try {
    // Initialize the service account auth
    let privateKey;
    try {
      privateKey = process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n');
      console.log('Private key processed successfully');
    } catch (keyError) {
      console.error('Error processing private key:', keyError);
      return res.status(500).json({ 
        error: 'Server configuration error: Invalid private key format' 
      });
    }

    console.log('Initializing JWT authentication...');
    const serviceAccountAuth = new JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      key: privateKey,
      scopes: [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive.file',
      ],
    });

    // Initialize the Google Spreadsheet
    console.log('Initializing Google Spreadsheet with ID:', sheetsId);
    const doc = new GoogleSpreadsheet(sheetsId, serviceAccountAuth);
    
    // Load document properties and worksheets
    console.log('Loading document info...');
    await doc.loadInfo();
    console.log('Document loaded successfully. Title:', doc.title);
    
    // Get the first sheet
    const sheet = doc.sheetsByIndex[0];
    console.log('First sheet found:', sheet?.title || 'No sheet found');
    
    if (!sheet) {
      return res.status(404).json({ error: 'No sheets found in this document' });
    }

    // Load the sheet data
    await sheet.loadCells();
    
    // Get sheet dimensions
    const rowCount = sheet.rowCount;
    const columnCount = sheet.columnCount;
    
    // Extract headers (first row)
    const headers = [];
    for (let col = 0; col < columnCount; col++) {
      const cell = sheet.getCell(0, col);
      headers.push(cell.value || '');
    }
    
    // Extract data rows (skip header row)
    const rows = [];
    for (let row = 1; row < Math.min(rowCount, 101); row++) { // Limit to 100 data rows
      const rowData = [];
      for (let col = 0; col < columnCount; col++) {
        const cell = sheet.getCell(row, col);
        rowData.push(cell.value || '');
      }
      
      // Only add row if it has some data
      if (rowData.some(cell => cell !== '')) {
        rows.push(rowData);
      }
    }

    const responseData = {
      title: doc.title,
      sheetTitle: sheet.title,
      rowCount: rows.length + 1, // +1 for header
      columnCount,
      headers,
      rows,
      sheetsId
    };

    res.status(200).json(responseData);
  } catch (error) {
    console.error('Error fetching Google Sheets data:', error);
    console.error('Error stack:', error.stack);
    
    // Ensure we always return JSON, never HTML
    res.setHeader('Content-Type', 'application/json');
    
    // Handle specific error types
    if (error.message.includes('Unable to parse')) {
      return res.status(400).json({ error: 'Invalid Google Sheets ID format' });
    }
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Google Sheets not found or not accessible' });
    }
    
    if (error.message.includes('permission') || error.message.includes('403')) {
      return res.status(403).json({ error: 'Permission denied. Please share the sheet with the service account.' });
    }
    
    if (error.message.includes('401') || error.message.includes('unauthorized')) {
      return res.status(401).json({ error: 'Authentication failed. Please check service account credentials.' });
    }
    
    return res.status(500).json({ 
      error: 'Failed to fetch Google Sheets data',
      details: error.message,
      type: error.constructor.name
    });
  }
}