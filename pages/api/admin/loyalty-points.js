import fs from 'fs';
import path from 'path';

const LOYALTY_POINTS_FILE = path.join(process.cwd(), 'data', 'loyalty-points.json');
const LOYALTY_RULES_FILE = path.join(process.cwd(), 'data', 'loyalty-rules.json');
const CUSTOMERS_FILE = path.join(process.cwd(), 'data', 'customers.json');
const ORDERS_FILE = path.join(process.cwd(), 'data', 'orders.json');

// Ensure data directory exists
const ensureDataDirectory = () => {
  const dataDir = path.dirname(LOYALTY_POINTS_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Read loyalty points data
const readLoyaltyPoints = () => {
  try {
    if (!fs.existsSync(LOYALTY_POINTS_FILE)) {
      return { customers: {}, transactions: [] };
    }
    const data = fs.readFileSync(LOYALTY_POINTS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading loyalty points:', error);
    return { customers: {}, transactions: [] };
  }
};

// Write loyalty points data
const writeLoyaltyPoints = (data) => {
  try {
    ensureDataDirectory();
    fs.writeFileSync(LOYALTY_POINTS_FILE, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing loyalty points:', error);
    return false;
  }
};

// Read loyalty rules
const readLoyaltyRules = () => {
  try {
    if (!fs.existsSync(LOYALTY_RULES_FILE)) {
      return null;
    }
    const data = fs.readFileSync(LOYALTY_RULES_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading loyalty rules:', error);
    return null;
  }
};

// Calculate customer tier based on total points
const calculateCustomerTier = (totalPoints, rules) => {
  if (!rules || !rules.customerTiers) {
    return 'bronze';
  }
  
  const tiers = Object.entries(rules.customerTiers)
    .sort(([,a], [,b]) => b.minPoints - a.minPoints);
  
  for (const [tierKey, tier] of tiers) {
    if (totalPoints >= tier.minPoints && totalPoints <= tier.maxPoints) {
      return tierKey;
    }
  }
  
  return 'bronze';
};

// Calculate points for an order
const calculateOrderPoints = (order, customerId, rules) => {
  if (!rules || !rules.settings.enabled || !rules.earningRules) {
    return { points: 0, breakdown: [] };
  }
  
  const loyaltyData = readLoyaltyPoints();
  const customerData = loyaltyData.customers[customerId] || { totalPoints: 0, tier: 'bronze' };
  const customerTier = calculateCustomerTier(customerData.totalPoints, rules);
  const tierMultiplier = rules.customerTiers[customerTier]?.multiplier || 1.0;
  
  let totalPoints = 0;
  const breakdown = [];
  
  // Purchase points
  if (rules.earningRules.purchase && rules.earningRules.purchase.enabled) {
    for (const rule of rules.earningRules.purchase.rules) {
      let rulePoints = 0;
      let applicable = true;
      
      // Check conditions
      if (rule.conditions) {
        if (rule.conditions.minAmount && order.amount < rule.conditions.minAmount) {
          applicable = false;
        }
        if (rule.conditions.maxAmount && order.amount > rule.conditions.maxAmount) {
          applicable = false;
        }
        if (rule.conditions.excludeCategories && order.items) {
          const hasExcludedCategory = order.items.some(item => 
            rule.conditions.excludeCategories.some(cat => 
              item.name.toLowerCase().includes(cat.toLowerCase())
            )
          );
          if (hasExcludedCategory) {
            applicable = false;
          }
        }
        if (rule.conditions.productCategories && rule.conditions.productCategories.length > 0 && order.items) {
          const hasRequiredCategory = order.items.some(item => 
            rule.conditions.productCategories.some(cat => 
              item.name.toLowerCase().includes(cat.toLowerCase())
            )
          );
          if (!hasRequiredCategory) {
            applicable = false;
          }
        }
      }
      
      if (applicable) {
        if (rule.type === 'percentage') {
          rulePoints = Math.floor((order.amount * rule.value / 100) * tierMultiplier);
        } else if (rule.type === 'fixed') {
          if (rule.conditions && rule.conditions.productCategories) {
            // Count items in specific categories
            const categoryItems = order.items ? order.items.filter(item => 
              rule.conditions.productCategories.some(cat => 
                item.name.toLowerCase().includes(cat.toLowerCase())
              )
            ).length : 0;
            rulePoints = Math.floor(categoryItems * rule.value * tierMultiplier);
          } else {
            rulePoints = Math.floor(rule.value * tierMultiplier);
          }
        }
        
        if (rulePoints > 0) {
          totalPoints += rulePoints;
          breakdown.push({
            ruleId: rule.id,
            ruleName: rule.name,
            points: rulePoints,
            description: rule.description
          });
        }
      }
    }
  }
  
  return { points: totalPoints, breakdown, tierMultiplier, customerTier };
};

// Add points transaction
const addPointsTransaction = (customerId, points, type, description, orderId = null, breakdown = []) => {
  const loyaltyData = readLoyaltyPoints();
  
  // Initialize customer if not exists
  if (!loyaltyData.customers[customerId]) {
    loyaltyData.customers[customerId] = {
      totalPoints: 0,
      availablePoints: 0,
      tier: 'bronze',
      joinDate: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };
  }
  
  // Update customer points
  loyaltyData.customers[customerId].totalPoints += points;
  loyaltyData.customers[customerId].availablePoints += points;
  loyaltyData.customers[customerId].lastActivity = new Date().toISOString();
  
  // Recalculate tier
  const rules = readLoyaltyRules();
  if (rules) {
    loyaltyData.customers[customerId].tier = calculateCustomerTier(
      loyaltyData.customers[customerId].totalPoints, 
      rules
    );
  }
  
  // Add transaction record
  const transaction = {
    id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    customerId,
    points,
    type, // 'earned', 'redeemed', 'adjusted', 'expired'
    description,
    orderId,
    breakdown,
    createdAt: new Date().toISOString(),
    status: 'completed'
  };
  
  loyaltyData.transactions.push(transaction);
  
  // Save data
  writeLoyaltyPoints(loyaltyData);
  
  return {
    transaction,
    customerData: loyaltyData.customers[customerId]
  };
};

export default async function handler(req, res) {
  const { method } = req;
  
  try {
    switch (method) {
      case 'GET':
        const { customerId, orderId, type, page = 1, limit = 50 } = req.query;
        const getLoyaltyData = readLoyaltyPoints();
        
        if (customerId) {
          // Get specific customer's points and transactions
          const customerData = getLoyaltyData.customers[customerId];
          if (!customerData) {
            return res.status(404).json({ error: 'Customer not found' });
          }
          
          const customerTransactions = getLoyaltyData.transactions
            .filter(t => t.customerId === customerId)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          
          return res.status(200).json({
            customer: customerData,
            transactions: customerTransactions,
            totalTransactions: customerTransactions.length
          });
        }
        
        // Get all transactions with filtering
        let filteredTransactions = getLoyaltyData.transactions;
        
        if (orderId) {
          filteredTransactions = filteredTransactions.filter(t => t.orderId === orderId);
        }
        
        if (type) {
          filteredTransactions = filteredTransactions.filter(t => t.type === type);
        }
        
        // Sort by creation date (newest first)
        filteredTransactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);
        
        return res.status(200).json({
          transactions: paginatedTransactions,
          totalTransactions: filteredTransactions.length,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(filteredTransactions.length / limit),
          customers: getLoyaltyData.customers
        });
        
      case 'POST':
        const { action, customerId: postCustomerId, orderId: postOrderId, points: manualPoints, description: manualDescription } = req.body;
        
        if (action === 'calculate') {
          // Calculate points for an order
          if (!postOrderId) {
            return res.status(400).json({ error: 'Order ID is required for calculation' });
          }
          
          // Read order data
          let orderData = null;
          try {
            if (fs.existsSync(ORDERS_FILE)) {
              const ordersData = JSON.parse(fs.readFileSync(ORDERS_FILE, 'utf8'));
              orderData = ordersData.find(order => order.id === postOrderId);
            }
          } catch (error) {
            console.error('Error reading orders:', error);
          }
          
          if (!orderData) {
            return res.status(404).json({ error: 'Order not found' });
          }
          
          const rules = readLoyaltyRules();
          if (!rules) {
            return res.status(404).json({ error: 'Loyalty rules not found' });
          }
          
          const calculation = calculateOrderPoints(orderData, orderData.customerId, rules);
          
          return res.status(200).json({
            orderId: postOrderId,
            customerId: orderData.customerId,
            calculation
          });
        }
        
        if (action === 'award') {
          // Award points for an order
          if (!postOrderId) {
            return res.status(400).json({ error: 'Order ID is required for awarding points' });
          }
          
          // Check if points already awarded for this order
          const awardLoyaltyData = readLoyaltyPoints();
          const existingTransaction = awardLoyaltyData.transactions.find(t => t.orderId === postOrderId && t.type === 'earned');
          if (existingTransaction) {
            return res.status(400).json({ error: 'Points already awarded for this order' });
          }
          
          // Read order data
          let orderData = null;
          try {
            if (fs.existsSync(ORDERS_FILE)) {
              const ordersData = JSON.parse(fs.readFileSync(ORDERS_FILE, 'utf8'));
              orderData = ordersData.find(order => order.id === postOrderId);
            }
          } catch (error) {
            console.error('Error reading orders:', error);
          }
          
          if (!orderData) {
            return res.status(404).json({ error: 'Order not found' });
          }
          
          const rules = readLoyaltyRules();
          if (!rules) {
            return res.status(404).json({ error: 'Loyalty rules not found' });
          }
          
          const calculation = calculateOrderPoints(orderData, orderData.customerId, rules);
          
          if (calculation.points > 0) {
            const result = addPointsTransaction(
              orderData.customerId,
              calculation.points,
              'earned',
              `Tích điểm từ đơn hàng #${postOrderId}`,
              postOrderId,
              calculation.breakdown
            );
            
            return res.status(200).json({
              message: 'Points awarded successfully',
              ...result,
              calculation
            });
          } else {
            return res.status(200).json({
              message: 'No points to award for this order',
              calculation
            });
          }
        }
        
        if (action === 'adjust') {
          // Manual points adjustment
          if (!postCustomerId || typeof manualPoints !== 'number') {
            return res.status(400).json({ error: 'Customer ID and points are required for adjustment' });
          }
          
          const result = addPointsTransaction(
            postCustomerId,
            manualPoints,
            'adjusted',
            manualDescription || `Điều chỉnh thủ công ${manualPoints > 0 ? '+' : ''}${manualPoints} điểm`
          );
          
          return res.status(200).json({
            message: 'Points adjusted successfully',
            ...result
          });
        }
        
        return res.status(400).json({ error: 'Invalid action. Supported actions: calculate, award, adjust' });
        
      case 'PUT':
        // Update customer tier manually or reset points
        const { customerId: putCustomerId, tier, resetPoints } = req.body;
        
        if (!putCustomerId) {
          return res.status(400).json({ error: 'Customer ID is required' });
        }
        
        const updateLoyaltyData = readLoyaltyPoints();
        if (!updateLoyaltyData.customers[putCustomerId]) {
          return res.status(404).json({ error: 'Customer not found' });
        }
        
        if (tier) {
          updateLoyaltyData.customers[putCustomerId].tier = tier;
          updateLoyaltyData.customers[putCustomerId].lastActivity = new Date().toISOString();
        }
        
        if (resetPoints === true) {
          updateLoyaltyData.customers[putCustomerId].totalPoints = 0;
          updateLoyaltyData.customers[putCustomerId].availablePoints = 0;
          updateLoyaltyData.customers[putCustomerId].tier = 'bronze';
          updateLoyaltyData.customers[putCustomerId].lastActivity = new Date().toISOString();
          
          // Add reset transaction
          const resetTransaction = {
            id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            customerId: putCustomerId,
            points: -updateLoyaltyData.customers[putCustomerId].totalPoints,
            type: 'adjusted',
            description: 'Reset điểm thành viên',
            createdAt: new Date().toISOString(),
            status: 'completed'
          };
          
          updateLoyaltyData.transactions.push(resetTransaction);
        }
        
        writeLoyaltyPoints(updateLoyaltyData);
        
        return res.status(200).json({
          message: 'Customer data updated successfully',
          customer: updateLoyaltyData.customers[putCustomerId]
        });
        
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT']);
        return res.status(405).json({ error: `Method ${method} not allowed` });
    }
  } catch (error) {
    console.error('Loyalty points API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}