import fs from 'fs';
import path from 'path';
import jwt from 'jsonwebtoken';
import { serialize } from 'cookie';

// JWT secret for admin tokens
const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'admin-secret-key-change-in-production';

// Path to credentials file
const CREDENTIALS_FILE = path.join(process.cwd(), 'utils', 'credentials.json');

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Ph<PERSON>ơng thức không được phép'
    });
  }

  try {
    const { username, password } = req.body;

    // Validate input
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Tên đăng nhập và mật khẩu là bắt buộc'
      });
    }

    // Read credentials file
    if (!fs.existsSync(CREDENTIALS_FILE)) {
      console.error('Credentials file not found:', CREDENTIALS_FILE);
      return res.status(500).json({
        success: false,
        message: 'Server configuration error'
      });
    }

    let credentials;
    try {
      const credentialsData = fs.readFileSync(CREDENTIALS_FILE, 'utf8');
      credentials = JSON.parse(credentialsData);
    } catch (error) {
      console.error('Error reading credentials file:', error);
      return res.status(500).json({
        success: false,
        message: 'Server configuration error'
      });
    }

    // Find matching user
    const user = credentials.users?.find(
      u => u.username === username && u.password === password
    );

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Tên đăng nhập hoặc mật khẩu không đúng'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        username: user.username,
        role: 'admin',
        iat: Math.floor(Date.now() / 1000)
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Set HTTP-only cookie for additional security
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours in seconds
      path: '/'
    };

    res.setHeader('Set-Cookie', serialize('adminToken', token, cookieOptions));

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Đăng nhập thành công',
      token: token, // Also return token for localStorage
      user: {
        username: user.username,
        role: 'admin'
      }
    });

  } catch (error) {
    console.error('Admin login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Lỗi máy chủ nội bộ'
    });
  }
}
