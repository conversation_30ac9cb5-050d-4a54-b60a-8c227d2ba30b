import fs from 'fs';
import path from 'path';

const CUSTOMERS_FILE = path.join(process.cwd(), 'data', 'customers.json');
const APPROVAL_LOG_FILE = path.join(process.cwd(), 'data', 'logs', 'approval-history.json');

// Ensure directories exist
const ensureDirectoryExists = (filePath) => {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// Read customers data
const readCustomers = () => {
  try {
    if (!fs.existsSync(CUSTOMERS_FILE)) {
      return [];
    }
    const data = fs.readFileSync(CUSTOMERS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading customers file:', error);
    return [];
  }
};

// Write customers data
const writeCustomers = (customers) => {
  try {
    ensureDirectoryExists(CUSTOMERS_FILE);
    fs.writeFileSync(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing customers file:', error);
    return false;
  }
};

// Log approval action
const logApprovalAction = (action) => {
  try {
    ensureDirectoryExists(APPROVAL_LOG_FILE);
    
    let logs = [];
    if (fs.existsSync(APPROVAL_LOG_FILE)) {
      const data = fs.readFileSync(APPROVAL_LOG_FILE, 'utf8');
      logs = JSON.parse(data);
    }
    
    logs.push({
      ...action,
      timestamp: new Date().toISOString(),
      id: Date.now().toString()
    });
    
    // Keep only last 1000 logs
    if (logs.length > 1000) {
      logs = logs.slice(-1000);
    }
    
    fs.writeFileSync(APPROVAL_LOG_FILE, JSON.stringify(logs, null, 2));
  } catch (error) {
    console.error('Error logging approval action:', error);
  }
};

export default function handler(req, res) {
  const { method } = req;

  switch (method) {
    case 'GET':
      return handleGet(req, res);
    case 'POST':
      return handlePost(req, res);
    case 'PUT':
      return handlePut(req, res);
    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT']);
      return res.status(405).json({ error: `Method ${method} not allowed` });
  }
}

// Get customers pending approval
function handleGet(req, res) {
  try {
    const { filter = 'all', customerId } = req.query;
    const customers = readCustomers();
    
    // If customerId is provided, return specific customer data
    if (customerId) {
      const customer = customers.find(c => c.id === customerId);
      if (customer) {
        res.status(200).json({
          success: true,
          customer
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Customer not found'
        });
      }
      return;
    }
    
    // Filter customers that have approval status
    const customersWithApproval = customers.filter(customer => 
      customer.approvalStatus && 
      typeof customer.approvalStatus === 'object'
    );
    
    let filteredCustomers = customersWithApproval;
    
    if (filter !== 'all') {
      filteredCustomers = customersWithApproval.filter(customer => {
        const statuses = Object.values(customer.approvalStatus);
        
        switch (filter) {
          case 'pending':
            return statuses.includes('pending');
          case 'approved':
            return statuses.every(status => status === 'approved');
          case 'rejected':
            return statuses.includes('rejected');
          default:
            return true;
        }
      });
    }
    
    // Sort by last modified date (newest first)
    filteredCustomers.sort((a, b) => {
      const dateA = new Date(a.lastModified || a.createdAt || 0);
      const dateB = new Date(b.lastModified || b.createdAt || 0);
      return dateB - dateA;
    });
    
    res.status(200).json({
      success: true,
      customers: filteredCustomers,
      total: filteredCustomers.length
    });
  } catch (error) {
    console.error('Error fetching customers for approval:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch customers' 
    });
  }
}

// Create new approval request
function handlePost(req, res) {
  try {
    const { customerId, sections } = req.body;
    
    if (!customerId || !sections) {
      return res.status(400).json({
        success: false,
        error: 'Customer ID and sections are required'
      });
    }
    
    const customers = readCustomers();
    const customerIndex = customers.findIndex(c => c.id === customerId);
    
    if (customerIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found'
      });
    }
    
    // Initialize approval status
    const approvalStatus = {};
    sections.forEach(section => {
      approvalStatus[section] = 'pending';
    });
    
    customers[customerIndex] = {
      ...customers[customerIndex],
      approvalStatus,
      submittedAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };
    
    if (writeCustomers(customers)) {
      // Log the action
      logApprovalAction({
        action: 'submitted_for_approval',
        customerId,
        sections,
        adminId: req.body.adminId || 'system'
      });
      
      res.status(200).json({
        success: true,
        message: 'Approval request created successfully',
        customer: customers[customerIndex]
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to save approval request'
      });
    }
  } catch (error) {
    console.error('Error creating approval request:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create approval request'
    });
  }
}

// Update approval status
function handlePut(req, res) {
  try {
    const { customerId, section, status, reason, adminId } = req.body;
    
    if (!customerId || !section || !status) {
      return res.status(400).json({
        success: false,
        error: 'Customer ID, section, and status are required'
      });
    }
    
    if (!['pending', 'approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Must be pending, approved, or rejected'
      });
    }
    
    const customers = readCustomers();
    const customerIndex = customers.findIndex(c => c.id === customerId);
    
    if (customerIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found'
      });
    }
    
    const customer = customers[customerIndex];
    
    // Initialize approval status if it doesn't exist
    if (!customer.approvalStatus) {
      customer.approvalStatus = {};
    }
    
    // Update approval status
    customer.approvalStatus[section] = status;
    customer.lastModified = new Date().toISOString();
    
    // Add reason if provided
    if (reason) {
      if (!customer.approvalReasons) {
        customer.approvalReasons = {};
      }
      customer.approvalReasons[section] = reason;
    }
    
    customers[customerIndex] = customer;
    
    if (writeCustomers(customers)) {
      // Log the action
      logApprovalAction({
        action: 'approval_status_updated',
        customerId,
        section,
        status,
        reason,
        adminId: adminId || 'unknown',
        customerName: customer.name || customer.fullName
      });
      
      res.status(200).json({
        success: true,
        message: 'Approval status updated successfully',
        customer: customers[customerIndex]
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to save approval status'
      });
    }
  } catch (error) {
    console.error('Error updating approval status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update approval status'
    });
  }
}