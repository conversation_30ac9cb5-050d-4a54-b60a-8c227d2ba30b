import fs from 'fs';
import path from 'path';

/**
 * Admin API endpoint for fetching payment callback logs
 * 
 * This endpoint serves payment callbacks from the centralized orders.json system
 * replacing the old separate logsapn.json and logsFamilyMart.json files.
 * 
 * Query parameters:
 * - paymentMethod: Filter by payment method ('7-eleven', 'familymart', or 'all')
 * - limit: Number of callbacks to return (default: 100)
 * - offset: Number of callbacks to skip for pagination (default: 0)
 * - status: Filter by callback status
 * - dateFrom: Filter callbacks from date (ISO string)
 * - dateTo: Filter callbacks to date (ISO string)
 */

// Helper to read orders.json
const readOrdersFile = () => {
  try {
    const ordersPath = path.join(process.cwd(), 'data', 'orders.json');
    if (!fs.existsSync(ordersPath)) {
      return [];
    }
    const fileContents = fs.readFileSync(ordersPath, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading orders.json:', error);
    return [];
  }
};

// Extract all payment callbacks from orders with order context
const extractPaymentCallbacks = (orders) => {
  const callbacks = [];
  
  orders.forEach(order => {
    if (order.paymentCallbacks && Array.isArray(order.paymentCallbacks)) {
      order.paymentCallbacks.forEach(callback => {
        callbacks.push({
          ...callback,
          // Add order context to each callback
          orderId: order.id,
          orderId: order.id,
          orderStatus: order.status,
          orderPaymentStatus: order.paymentStatus,
          orderAmount: order.totalAmount || order.amount,
          customerEmail: order.customerEmail,
          createdAt: order.createdAt
        });
      });
    }
  });
  
  // Sort by timestamp (newest first)
  callbacks.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  
  return callbacks;
};

// Apply filters to callbacks
const applyFilters = (callbacks, filters) => {
  let filtered = [...callbacks];
  
  // Filter by payment method
  if (filters.paymentMethod && filters.paymentMethod !== 'all') {
    filtered = filtered.filter(callback => 
      callback.paymentMethod === filters.paymentMethod
    );
  }
  
  // Filter by status (from request payload or response status)
  if (filters.status) {
    filtered = filtered.filter(callback => {
      const requestStatus = callback.requestPayload?.status;
      const responseStatus = callback.responseStatus;
      return requestStatus === filters.status || 
             responseStatus.toString() === filters.status;
    });
  }
  
  // Filter by date range
  if (filters.dateFrom) {
    const fromDate = new Date(filters.dateFrom);
    filtered = filtered.filter(callback => 
      new Date(callback.timestamp) >= fromDate
    );
  }
  
  if (filters.dateTo) {
    const toDate = new Date(filters.dateTo);
    filtered = filtered.filter(callback => 
      new Date(callback.timestamp) <= toDate
    );
  }
  
  return filtered;
};

// Generate summary statistics
const generateStats = (callbacks) => {
  const stats = {
    total: callbacks.length,
    byPaymentMethod: {},
    byStatus: {},
    byResponseStatus: {},
    avgProcessingTime: 0,
    recentCallbacks: 0 // last 24 hours
  };
  
  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  let totalProcessingTime = 0;
  let validProcessingTimes = 0;
  
  callbacks.forEach(callback => {
    // Count by payment method
    const method = callback.paymentMethod;
    stats.byPaymentMethod[method] = (stats.byPaymentMethod[method] || 0) + 1;
    
    // Count by request payload status
    const requestStatus = callback.requestPayload?.status || 'unknown';
    stats.byStatus[requestStatus] = (stats.byStatus[requestStatus] || 0) + 1;
    
    // Count by response status
    const responseStatus = callback.responseStatus.toString();
    stats.byResponseStatus[responseStatus] = (stats.byResponseStatus[responseStatus] || 0) + 1;
    
    // Calculate average processing time
    if (callback.processingTimeMs && typeof callback.processingTimeMs === 'number') {
      totalProcessingTime += callback.processingTimeMs;
      validProcessingTimes++;
    }
    
    // Count recent callbacks
    if (new Date(callback.timestamp) > yesterday) {
      stats.recentCallbacks++;
    }
  });
  
  // Calculate average processing time
  if (validProcessingTimes > 0) {
    stats.avgProcessingTime = Math.round(totalProcessingTime / validProcessingTimes);
  }
  
  return stats;
};

export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    // Extract query parameters
    const {
      paymentMethod = 'all',
      limit = '100',
      offset = '0',
      status,
      dateFrom,
      dateTo,
      includeStats = 'true'
    } = req.query;

    // Load orders and extract callbacks
    const orders = readOrdersFile();
    let callbacks = extractPaymentCallbacks(orders);

    // Apply filters
    const filters = {
      paymentMethod,
      status,
      dateFrom,
      dateTo
    };
    callbacks = applyFilters(callbacks, filters);

    // Generate statistics before pagination
    let stats = null;
    if (includeStats === 'true') {
      stats = generateStats(callbacks);
    }

    // Apply pagination
    const limitNum = parseInt(limit, 10);
    const offsetNum = parseInt(offset, 10);
    const paginatedCallbacks = callbacks.slice(offsetNum, offsetNum + limitNum);

    // Return response
    return res.status(200).json({
      success: true,
      data: {
        callbacks: paginatedCallbacks,
        pagination: {
          total: callbacks.length,
          limit: limitNum,
          offset: offsetNum,
          hasMore: offsetNum + limitNum < callbacks.length
        },
        filters,
        stats
      }
    });

  } catch (error) {
    console.error('Error fetching payment callbacks:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
} 