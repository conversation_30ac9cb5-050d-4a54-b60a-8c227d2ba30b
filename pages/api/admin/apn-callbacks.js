import fs from 'fs';
import path from 'path';

// Enhanced callback tracking with detailed logging
function logCallbackEvent(eventData) {
  const logPath = path.join(process.cwd(), 'data', 'logs', 'callback-events.json');
  
  try {
    // Ensure directory exists
    const logDir = path.dirname(logPath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    // Read existing events
    let events = [];
    if (fs.existsSync(logPath)) {
      const data = fs.readFileSync(logPath, 'utf8');
      events = JSON.parse(data);
    }
    
    // Add new event
    const event = {
      id: `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      ...eventData
    };
    
    events.push(event);
    
    // Keep only last 500 events
    if (events.length > 500) {
      events.splice(0, events.length - 500);
    }
    
    fs.writeFileSync(logPath, JSON.stringify(events, null, 2));
    return event;
  } catch (error) {
    console.error('[CALLBACK_EVENT_LOG] Error logging event:', error);
    return null;
  }
}

export default async function handler(req, res) {
  if (req.method === 'POST') {
    // Log a new callback event
    const callbackData = req.body;
    
    const event = logCallbackEvent({
      type: 'callback_received',
      provider: callbackData.provider,
      orderId: callbackData.orderId,
      transactionId: callbackData.transactionId,
      status: callbackData.status,
      amount: callbackData.amount,
      requestPayload: callbackData.requestPayload,
      responseStatus: callbackData.responseStatus,
      processingTimeMs: callbackData.processingTimeMs,
      clientIP: callbackData.clientIP,
      userAgent: callbackData.userAgent,
      orderFound: callbackData.orderFound,
      errorMessage: callbackData.errorMessage
    });
    
    return res.status(201).json({
      success: true,
      event,
      message: 'Callback event logged successfully'
    });
  }
  
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    // Apply filters from query parameters
    const { 
      page = 1, 
      limit = 50, 
      provider, 
      status, 
      dateFrom, 
      dateTo, 
      orderId, 
      transactionId,
      export: exportData 
    } = req.query;

    // Define log file paths
    const logPaths = {
      apn: path.join(process.cwd(), 'data', 'callbacks', 'logsapn.json'),
      familymart: path.join(process.cwd(), 'data', 'callbacks', 'logsFamilyMart.json'),
      ifCard: path.join(process.cwd(), 'logs', 'if-card-callbacks.log'),
      sinopac: path.join(process.cwd(), 'logs', 'sinopac-callbacks.log'),
      orders: path.join(process.cwd(), 'data', 'orders.json')
    };

    console.log('[APN_CALLBACKS_API] Reading callback logs from files...');
    
    // Function to safely read JSON file
    const readJsonFile = (filePath) => {
      try {
        if (fs.existsSync(filePath)) {
          const data = fs.readFileSync(filePath, 'utf8');
          return JSON.parse(data);
        }
        return [];
      } catch (error) {
        console.error(`Error reading ${filePath}:`, error.message);
        return [];
      }
    };

    // Function to safely read log file (plain text logs)
    const readLogFile = (filePath) => {
      try {
        if (fs.existsSync(filePath)) {
          const data = fs.readFileSync(filePath, 'utf8');
          return data.split('\n').filter(line => line.trim()).map(line => {
            try {
              // Try to parse each line as JSON or extract callback info
              const timestamp = line.match(/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z)/)?.[1];
              return {
                timestamp: timestamp || new Date().toISOString(),
                rawLog: line,
                parsed: false
              };
            } catch {
              return {
                timestamp: new Date().toISOString(),
                rawLog: line,
                parsed: false
              };
            }
          });
        }
        return [];
      } catch (error) {
        console.error(`Error reading ${filePath}:`, error.message);
        return [];
      }
    };

    // Read all callback sources
    const apnLogs = readJsonFile(logPaths.apn);
    const familymartLogs = readJsonFile(logPaths.familymart);
    const ifCardLogs = readLogFile(logPaths.ifCard);
    const sinopacLogs = readLogFile(logPaths.sinopac);
    
    // Read orders to get embedded callback logs
    const orders = readJsonFile(logPaths.orders);

    // Extract and flatten all callbacks from all sources
    const allCallbacks = [];

    console.log('[APN_CALLBACKS_API] Processing callback sources:', {
      apnLogs: apnLogs.length,
      familymartLogs: familymartLogs.length,
      ifCardLogs: ifCardLogs.length,
      sinopacLogs: sinopacLogs.length,
      ordersWithCallbacks: orders.filter(o => o.paymentCallbacks?.length > 0).length
    });

    // Process 7-Eleven APN logs from server.js middleware
    apnLogs.forEach((log, index) => {
      let orderId = 'Unknown';
      let transactionId = null;
      let amount = null;
      
      // Extract order info from request payload
      if (log.requestPayload) {
        orderId = log.requestPayload.order_no || log.requestPayload.trans_id || 'Unknown';
        transactionId = log.requestPayload.trans_id;
        amount = parseFloat(log.requestPayload.amount) || null;
      }

      allCallbacks.push({
        id: `apn-${index}-${log.timestamp}`,
        orderId: orderId,
        timestamp: log.timestamp,
        method: 'convenience_store',
        submethod: 'seven_eleven_ibon',
        status: log.responseStatus >= 200 && log.responseStatus < 300 ? 'success' : 'failed',
        httpStatus: log.responseStatus,
        response: log.responseBody,
        details: `APN callback - Processing time: ${log.processingTimeMs}ms`,
        paymentProvider: '7-Eleven',
        transactionId: transactionId,
        amount: amount,
        currency: 'NT$',
        customerName: 'Unknown',
        attempts: 1,
        lastAttempt: log.timestamp,
        source: 'server_middleware'
      });
    });

    // Process FamilyMart logs 
    familymartLogs.forEach((log, index) => {
      let orderId = 'Unknown';
      let transactionId = null;
      let amount = null;
      
      // Extract info from FamilyMart log structure
      if (log.requestPayload) {
        orderId = log.requestPayload.ORDER_NO || log.requestPayload.PIN_CODE || 'Unknown';
        transactionId = log.requestPayload.PAYMENT_NO;
        amount = parseFloat(log.requestPayload.AMOUNT) || null;
      }

      allCallbacks.push({
        id: `familymart-${index}-${log.timestamp}`,
        orderId: orderId,
        timestamp: log.timestamp,
        method: 'convenience_store',
        submethod: 'family_mart',
        status: log.responseStatus >= 200 && log.responseStatus < 300 ? 'success' : 'failed',
        httpStatus: log.responseStatus,
        response: log.responseBody,
        details: `FamilyMart callback - Processing time: ${log.processingTimeMs}ms`,
        paymentProvider: 'FamilyMart',
        transactionId: transactionId,
        amount: amount,
        currency: 'NT$',
        customerName: 'Unknown',
        attempts: 1,
        lastAttempt: log.timestamp,
        source: 'familymart_callback'
      });
    });

    // Process IF Card logs (Taiwan mobile operator)
    ifCardLogs.forEach((log, index) => {
      if (log.parsed === false && log.rawLog.includes('IF CARD CALLBACK')) {
        allCallbacks.push({
          id: `ifcard-${index}-${log.timestamp}`,
          orderId: 'Unknown',
          timestamp: log.timestamp,
          method: 'mobile_operator',
          submethod: 'if_card',
          status: log.rawLog.includes('error') || log.rawLog.includes('Error') ? 'failed' : 'success',
          httpStatus: 200,
          response: null,
          details: log.rawLog,
          paymentProvider: 'IF Card',
          transactionId: null,
          amount: null,
          currency: 'NT$',
          customerName: 'Unknown',
          attempts: 1,
          lastAttempt: log.timestamp,
          source: 'if_card_log'
        });
      }
    });

    // Process Sinopac logs
    sinopacLogs.forEach((log, index) => {
      if (log.parsed === false && log.rawLog.includes('SinoPAC')) {
        allCallbacks.push({
          id: `sinopac-${index}-${log.timestamp}`,
          orderId: 'Unknown',
          timestamp: log.timestamp,
          method: 'bank_transfer',
          submethod: 'sinopac',
          status: log.rawLog.includes('error') || log.rawLog.includes('Error') ? 'failed' : 'success',
          httpStatus: 200,
          response: null,
          details: log.rawLog,
          paymentProvider: 'Sinopac Bank',
          transactionId: null,
          amount: null,
          currency: 'NT$',
          customerName: 'Unknown',
          attempts: 1,
          lastAttempt: log.timestamp,
          source: 'sinopac_log'
        });
      }
    });

    // Process embedded callbacks in orders (modern approach)
    orders.forEach(order => {
      if (order.paymentCallbacks && order.paymentCallbacks.length > 0) {
        order.paymentCallbacks.forEach((callback, index) => {
          // Determine payment provider from order data
          let paymentProvider = 'Unknown';
          let paymentMethod = order.paymentMethod || '';
          let paymentSubMethod = order.paymentSubMethod || '';
          
          if (paymentSubMethod === 'seven_eleven_ibon' || paymentMethod === 'ibon') {
            paymentProvider = '7-Eleven';
          } else if (paymentSubMethod === 'seven_eleven_card' || paymentMethod === 'seven_eleven_card') {
            paymentProvider = '7-Eleven Card';
          } else if (paymentSubMethod === 'family_mart' || paymentMethod === 'familymart') {
            paymentProvider = 'FamilyMart';
          } else if (paymentMethod === 'sinopac' || paymentMethod === 'sinopac_bank') {
            paymentProvider = 'Sinopac Bank';
          } else if (paymentMethod === 'convenience_store') {
            paymentProvider = paymentSubMethod === 'family_mart' ? 'FamilyMart' : '7-Eleven';
          }

          // Determine callback status
          let status = 'unknown';
          let httpStatus = callback.responseStatus || callback.status || callback.httpStatus || 0;
          
          if (httpStatus >= 200 && httpStatus < 300) {
            status = 'success';
          } else if (httpStatus >= 400 && httpStatus < 500) {
            status = 'failed';
          } else if (httpStatus >= 500) {
            status = 'error';
          } else if (callback.pending || callback.status === 'pending') {
            status = 'pending';
          }

          // Parse response data safely
          let responseData = null;
          try {
            if (typeof callback.response === 'string') {
              responseData = JSON.parse(callback.response);
            } else if (callback.response) {
              responseData = callback.response;
            }
          } catch (e) {
            responseData = callback.responseBody || callback.response || null;
          }

          // Create standardized callback object
          allCallbacks.push({
            id: `order-${order.id || order._id}-callback-${index}-${callback.timestamp || Date.now()}`,
            orderId: order.id || order._id?.toString(),
            timestamp: callback.timestamp || callback.createdAt || order.createdAt,
            method: paymentMethod,
            submethod: paymentSubMethod,
            status: status,
            httpStatus: httpStatus,
            response: responseData,
            details: callback.details || callback.message || callback.error || `Processing time: ${callback.processingTimeMs}ms`,
            paymentProvider: paymentProvider,
            transactionId: callback.transactionId || callback.trans_id || order.trans_id,
            amount: order.totalAmount || order.amount,
            currency: order.currency || 'NT$',
            customerName: order.customerName || order.recipientInfo?.fullName || 'Unknown',
            attempts: callback.attempts || 1,
            lastAttempt: callback.lastAttempt || callback.timestamp,
            source: 'order_embedded'
          });
        });
      }
    });

    // Apply filters
    let filteredCallbacks = allCallbacks;
    
    if (provider) {
      filteredCallbacks = filteredCallbacks.filter(callback => 
        callback.paymentProvider && callback.paymentProvider.toLowerCase().includes(provider.toLowerCase())
      );
    }
    
    if (status) {
      filteredCallbacks = filteredCallbacks.filter(callback => callback.status === status);
    }
    
    if (dateFrom) {
      const fromDate = new Date(dateFrom);
      filteredCallbacks = filteredCallbacks.filter(callback => new Date(callback.timestamp) >= fromDate);
    }
    
    if (dateTo) {
      const toDate = new Date(dateTo);
      filteredCallbacks = filteredCallbacks.filter(callback => new Date(callback.timestamp) <= toDate);
    }
    
    if (orderId) {
      filteredCallbacks = filteredCallbacks.filter(callback => 
        callback.orderId && callback.orderId.toLowerCase().includes(orderId.toLowerCase())
      );
    }
    
    if (transactionId) {
      filteredCallbacks = filteredCallbacks.filter(callback => 
        callback.transactionId && callback.transactionId.toLowerCase().includes(transactionId.toLowerCase())
      );
    }

    // Sort callbacks by timestamp (newest first)
    filteredCallbacks.sort((a, b) => {
      const dateA = new Date(a.timestamp);
      const dateB = new Date(b.timestamp);
      return dateB.getTime() - dateA.getTime();
    });

    // Handle export
    if (exportData === 'true') {
      const csvHeaders = 'Timestamp,Method,Submethod,Order ID,Transaction ID,Status,Amount,Currency,Provider,Source\n';
      const csvRows = filteredCallbacks.map(callback => {
        const timestamp = new Date(callback.timestamp).toISOString();
        const details = callback.details ? JSON.stringify(callback.details).replace(/"/g, '""') : '';
        return `"${timestamp}","${callback.method}","${callback.submethod}","${callback.orderId}","${callback.transactionId || ''}","${callback.status}","${callback.amount || ''}","${callback.currency}","${callback.paymentProvider}","${callback.source}"`;
      }).join('\n');
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="apn-callbacks-${new Date().toISOString().split('T')[0]}.csv"`);
      return res.status(200).send(csvHeaders + csvRows);
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedCallbacks = filteredCallbacks.slice(startIndex, endIndex);

    console.log('[APN_CALLBACKS_API] Processed callbacks:', {
      total: filteredCallbacks.length,
      bySource: {
        server_middleware: filteredCallbacks.filter(c => c.source === 'server_middleware').length,
        familymart_callback: filteredCallbacks.filter(c => c.source === 'familymart_callback').length,
        if_card_log: filteredCallbacks.filter(c => c.source === 'if_card_log').length,
        sinopac_log: filteredCallbacks.filter(c => c.source === 'sinopac_log').length,
        order_embedded: filteredCallbacks.filter(c => c.source === 'order_embedded').length
      }
    });

    return res.status(200).json({
      success: true,
      callbacks: paginatedCallbacks,
      total: filteredCallbacks.length,
      page: parseInt(page),
      limit: parseInt(limit),
      summary: {
        totalCallbacks: filteredCallbacks.length,
        successful: filteredCallbacks.filter(c => c.status === 'success').length,
        failed: filteredCallbacks.filter(c => c.status === 'failed').length,
        pending: filteredCallbacks.filter(c => c.status === 'pending').length,
        sources: {
          apnLogs: apnLogs.length,
          familymartLogs: familymartLogs.length,
          ifCardLogs: ifCardLogs.length,
          sinopacLogs: sinopacLogs.length,
          ordersWithCallbacks: orders.filter(o => o.paymentCallbacks?.length > 0).length
        },
        byProvider: {
          'server_middleware': filteredCallbacks.filter(c => c.source === 'server_middleware').length,
          'familymart_callback': filteredCallbacks.filter(c => c.source === 'familymart_callback').length,
          'if_card_log': filteredCallbacks.filter(c => c.source === 'if_card_log').length,
          'sinopac_log': filteredCallbacks.filter(c => c.source === 'sinopac_log').length,
          'order_embedded': filteredCallbacks.filter(c => c.source === 'order_embedded').length
        }
      },
      message: `Found ${filteredCallbacks.length} callbacks from all payment channels`
    });

  } catch (error) {
    console.error('[APN_CALLBACKS_API] Error fetching callback history:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch callback history from log files',
      details: error.message
    });
  }
}