import jwt from 'jsonwebtoken';

// JWT secret for admin tokens
const JWT_SECRET = process.env.ADMIN_JWT_SECRET || 'admin-secret-key-change-in-production';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: '<PERSON><PERSON>ơng thức không được phép'
    });
  }

  try {
    // Get token from Authorization header or cookie
    let token = null;
    
    // Check Authorization header first
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    
    // If no Authorization header, check cookie
    if (!token && req.cookies.adminToken) {
      token = req.cookies.adminToken;
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Không có token được cung cấp'
      });
    }

    // Verify the token
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      
      // Check if token is for admin role
      if (decoded.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Vai trò token không hợp lệ'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Token đã được xác thực',
        user: {
          username: decoded.username,
          role: decoded.role
        }
      });

    } catch (jwtError) {
      console.error('JWT verification error:', jwtError);
      return res.status(401).json({
        success: false,
        message: 'Token không hợp lệ hoặc đã hết hạn'
      });
    }

  } catch (error) {
    console.error('Admin verify error:', error);
    return res.status(500).json({
      success: false,
      message: 'Lỗi máy chủ nội bộ'
    });
  }
}
