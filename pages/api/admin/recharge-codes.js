import { 
  findDocuments, 
  findDocument, 
  insertDocument, 
  updateDocument, 
  deleteDocument, 
  logOperation 
} from '../../../utils/dbUtils';
import { withAdminAuth } from '../../../utils/auth';

/**
 * Admin API endpoint for managing phone recharge codes
 * 
 * Supported methods:
 * - GET: List or retrieve recharge codes
 * - POST: Add new recharge codes
 * - PUT: Update existing recharge codes
 * - DELETE: Delete recharge codes
 */
async function handler(req, res) {
  const { method } = req;

  // Add logging
  console.log(`[ADMIN_API] ${method} /api/admin/recharge-codes`);
  
  try {
    switch (method) {
      case 'GET':
        return await handleGet(req, res);
      case 'POST':
        return await handlePost(req, res);
      case 'PUT':
        return await handlePut(req, res);
      case 'DELETE':
        return await handleDelete(req, res);
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({ success: false, message: `Method ${method} Not Allowed` });
    }
  } catch (error) {
    console.error(`[ADMIN_API_ERROR] ${error.message}`, error);
    return res.status(500).json({ success: false, message: 'Internal Server Error', error: error.message });
  }
}

/**
 * Handle GET request - List or retrieve recharge codes
 */
async function handleGet(req, res) {
  const { id, productType, status } = req.query;
  
  // If ID is provided, retrieve a specific recharge code
  if (id) {
    const code = await findDocument('recharge_codes', { _id: id });
    
    if (!code) {
      return res.status(404).json({ success: false, message: 'Recharge code not found' });
    }
    
    return res.status(200).json({ success: true, data: code });
  }
  
  // Build query based on filters
  const query = {};
  
  if (productType) {
    query.productType = productType;
  }
  
  if (status) {
    query.status = status;
  }
  
  // Get all recharge codes
  const codes = await findDocuments('recharge_codes', query);
  
  // Get count of available codes by product type
  const codeStats = await findDocuments('recharge_codes', { status: 'available' });
  const countByType = {};
  
  codeStats.forEach(code => {
    const type = code.productType || 'unknown';
    countByType[type] = (countByType[type] || 0) + 1;
  });
  
  return res.status(200).json({ 
    success: true, 
    data: codes,
    stats: {
      total: codes.length,
      available: codeStats.length,
      countByType
    }
  });
}

/**
 * Handle POST request - Add new recharge codes
 */
async function handlePost(req, res) {
  const { codes, productType, expiryDate } = req.body;
  
  if (!codes || !Array.isArray(codes) || codes.length === 0) {
    return res.status(400).json({ success: false, message: 'Codes array is required' });
  }
  
  if (!productType) {
    return res.status(400).json({ success: false, message: 'Product type is required' });
  }
  
  const results = [];
  const errors = [];
  
  // Process each code
  for (const code of codes) {
    try {
      // Check if code already exists
      const existingCode = await findDocument('recharge_codes', { code, productType });
      
      if (existingCode) {
        errors.push({ code, error: 'Code already exists' });
        continue;
      }
      
      // Insert new code
      const newCode = {
        code,
        productType,
        status: 'available',
        createdAt: new Date(),
        expiryDate: expiryDate ? new Date(expiryDate) : null,
        usedAt: null,
        usedBy: null,
        orderId: null
      };
      
      const result = await insertDocument('recharge_codes', newCode);
      results.push({ code, _id: result.insertedId });
      
      // Log operation
      await logOperation('recharge_code_created', { 
        code,
        productType,
        admin: req.user.email 
      });
    } catch (error) {
      errors.push({ code, error: error.message });
    }
  }
  
  return res.status(201).json({ 
    success: true, 
    message: `Added ${results.length} recharge codes`,
    data: { results, errors } 
  });
}

/**
 * Handle PUT request - Update existing recharge code
 */
async function handlePut(req, res) {
  const { id } = req.query;
  const updates = req.body;
  
  if (!id) {
    return res.status(400).json({ success: false, message: 'Code ID is required' });
  }
  
  // Find existing code
  const existingCode = await findDocument('recharge_codes', { _id: id });
  
  if (!existingCode) {
    return res.status(404).json({ success: false, message: 'Recharge code not found' });
  }
  
  // Prepare update data
  const updateData = { $set: {} };
  
  // Only allow updating specific fields
  const allowedUpdates = ['status', 'expiryDate', 'notes'];
  
  for (const key of allowedUpdates) {
    if (updates[key] !== undefined) {
      updateData.$set[key] = updates[key];
    }
  }
  
  // Add updated timestamp
  updateData.$set.updatedAt = new Date();
  
  // Update the code
  const result = await updateDocument('recharge_codes', { _id: id }, updateData);
  
  // Log operation
  await logOperation('recharge_code_updated', { 
    code: existingCode.code,
    updates: updateData.$set,
    admin: req.user.email 
  });
  
  return res.status(200).json({ 
    success: true, 
    message: 'Recharge code updated',
    data: result 
  });
}

/**
 * Handle DELETE request - Delete recharge code
 */
async function handleDelete(req, res) {
  const { id } = req.query;
  
  if (!id) {
    return res.status(400).json({ success: false, message: 'Code ID is required' });
  }
  
  // Find existing code
  const existingCode = await findDocument('recharge_codes', { _id: id });
  
  if (!existingCode) {
    return res.status(404).json({ success: false, message: 'Recharge code not found' });
  }
  
  // Delete the code
  const result = await deleteDocument('recharge_codes', { _id: id });
  
  // Log operation
  await logOperation('recharge_code_deleted', { 
    code: existingCode.code,
    productType: existingCode.productType,
    admin: req.user.email 
  });
  
  return res.status(200).json({ 
    success: true, 
    message: 'Recharge code deleted',
    data: result 
  });
}

// Export with admin authentication middleware
export default withAdminAuth(handler); 