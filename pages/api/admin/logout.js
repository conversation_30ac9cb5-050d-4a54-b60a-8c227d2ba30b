import { serialize } from 'cookie';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: '<PERSON><PERSON><PERSON><PERSON> thức không được phép'
    });
  }

  try {
    // Clear the admin token cookie
    res.setHeader('Set-Cookie', serialize('adminToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // Expire immediately
      path: '/'
    }));

    return res.status(200).json({
      success: true,
      message: '<PERSON><PERSON><PERSON> xuất thành công'
    });

  } catch (error) {
    console.error('Admin logout error:', error);
    return res.status(500).json({
      success: false,
      message: 'Lỗi máy chủ nội bộ'
    });
  }
}
