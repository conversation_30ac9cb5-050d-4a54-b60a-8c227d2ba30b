import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';
import { addOrder } from '../../../utils/orderUtils';

// Helper function to read customer data
const getCustomerById = (customerId) => {
  try {
    const customersFilePath = path.join(process.cwd(), 'data', 'customers.json');
    if (!fs.existsSync(customersFilePath)) {
      console.error('customers.json file not found');
      return null;
    }
    
    const customersData = fs.readFileSync(customersFilePath, 'utf8');
    const customers = JSON.parse(customersData);
    
    return customers.find(customer => customer.id === customerId);
  } catch (error) {
    console.error('Error reading customer data:', error);
    return null;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { sheetsId, sku, columns, customerId } = req.body;

    // Validate required fields
    if (!sheetsId || !sku || !columns || !Array.isArray(columns) || !customerId) {
      return res.status(400).json({ 
        error: 'Missing required fields: sheetsId, sku, columns array, and customerId' 
      });
    }

    // Validate environment variables
    const requiredEnvVars = [
      'GOOGLE_SERVICE_ACCOUNT_EMAIL',
      'GOOGLE_PRIVATE_KEY'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        console.error(`Missing environment variable: ${envVar}`);
        return res.status(500).json({ 
          error: `Server configuration error: Missing ${envVar}` 
        });
      }
    }

    // Initialize Google Sheets API
    let privateKey;
    try {
      privateKey = process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n');
    } catch (error) {
      console.error('Error processing private key:', error);
      return res.status(500).json({ 
        error: 'Server configuration error: Invalid private key format' 
      });
    }

    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: privateKey,
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });

    const sheets = google.sheets({ version: 'v4', auth });

    // First, get the current sheet data to find the SKU row
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: sheetsId,
      range: 'A:Z',
    });

    const rows = response.data.values || [];
    if (rows.length === 0) {
      return res.status(404).json({ error: 'No data found in the sheet' });
    }

    // Find the row with the matching SKU (assuming SKU is in column A)
    let skuRowIndex = -1;
    for (let i = 0; i < rows.length; i++) {
      if (rows[i][0] && rows[i][0].toString().trim() === sku.trim()) {
        skuRowIndex = i;
        break;
      }
    }

    if (skuRowIndex === -1) {
      return res.status(404).json({ error: `SKU '${sku}' not found in the sheet` });
    }

    // Get the actual values from the specified columns before clearing them
    const skuRow = rows[skuRowIndex];
    const columnValues = [];
    const batchUpdateRequests = [];
    
    for (const columnIndex of columns) {
      // Get the actual value from this column
      const columnValue = skuRow[columnIndex] || '';
      const columnLetter = String.fromCharCode(65 + columnIndex);
      
      columnValues.push({
        columnIndex: columnIndex,
        columnLetter: columnLetter,
        value: columnValue
      });
      
      // Convert column index to letter (0=A, 1=B, etc.)
      const cellRange = `${columnLetter}${skuRowIndex + 1}`;
      
      batchUpdateRequests.push({
        range: cellRange,
        values: [['']],
      });
    }

    // Execute batch update to clear the cells
    if (batchUpdateRequests.length > 0) {
      await sheets.spreadsheets.values.batchUpdate({
        spreadsheetId: sheetsId,
        resource: {
          valueInputOption: 'RAW',
          data: batchUpdateRequests,
        },
      });
    }

    // Create offline purchase order
    try {
      const customer = getCustomerById(customerId);
      if (!customer) {
        console.warn(`Customer with ID ${customerId} not found, but continuing with Google Sheets update`);
      }

      // Create offline order
      const timestamp = Date.now();
      const offlineOrder = {
        id: `MAG-OFFLINE-${timestamp}`,
        customerId: customerId,
        customerInfo: customer ? {
          name: customer.name || 'Unknown Customer',
          email: customer.email || '',
          phone: customer.phone || ''
        } : {
          name: 'Unknown Customer',
          email: '',
          phone: ''
        },
        items: [{
          name: `Google Sheets Assignment - SKU: ${sku}`,
          description: `Assigned columns: ${columns.join(', ')} from Google Sheet ${sheetsId}`,
          quantity: 1,
          price: 0,
          sku: sku
        }],
        paymentMethod: 'offline_purchase',
        status: 'completed',
        paymentStatus: 'paid',
        amount: 0,
        currency: 'VND',
        createdAt: new Date(timestamp).toISOString(),
        updatedAt: new Date(timestamp).toISOString(),
        metadata: {
          type: 'google_sheets_assignment',
          sheetsId: sheetsId,
          assignedColumns: columns,
          assignedColumnValues: columnValues,
          assignedSku: sku
        }
      };

      // Add the order using orderUtils
      await addOrder(offlineOrder);
      console.log(`Created offline purchase order: ${offlineOrder.id} for customer: ${customerId}`);
    } catch (orderError) {
      console.error('Error creating offline purchase order:', orderError);
      // Don't fail the main operation if order creation fails
    }

    console.log(`Successfully sold items for SKU: ${sku}, cleared ${batchUpdateRequests.length} cells`);
    
    return res.status(200).json({ 
      success: true,
      message: `Đã xuất kho thành công ${columns.length} sản phẩm cho SKU: ${sku} cho khách hàng: ${getCustomerById(customerId)?.name || 'Unknown Customer'}. Đã tạo bản ghi mua hàng offline.`,
      clearedCells: batchUpdateRequests.length,
      sku: sku
    });

  } catch (error) {
    console.error('Error selling items from Google Sheets:', error);
    
    // Return JSON error response
    return res.status(500).json({ 
      error: 'Failed to sell items from Google Sheets',
      details: error.message 
    });
  }
}