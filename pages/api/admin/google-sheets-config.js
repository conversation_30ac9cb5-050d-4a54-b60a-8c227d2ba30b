export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const googleSheetsId = process.env.GOOGLE_SHEETS_INVENTORY_ID;
    
    if (!googleSheetsId) {
      return res.status(500).json({ error: 'GOOGLE_SHEETS_INVENTORY_ID not found in environment variables' });
    }

    res.status(200).json({ googleSheetsId });
  } catch (error) {
    console.error('Error fetching Google Sheets config:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}