import fs from 'fs';
import path from 'path';

// Path to APN calls log file
const APN_CALLS_LOG_PATH = path.join(process.cwd(), 'data', 'logs', 'apn-calls.json');

// Ensure log directory exists
function ensureLogDirectory() {
  const logDir = path.dirname(APN_CALLS_LOG_PATH);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
}

// Read existing APN calls log
function readApnCallsLog() {
  try {
    if (!fs.existsSync(APN_CALLS_LOG_PATH)) {
      return [];
    }
    const data = fs.readFileSync(APN_CALLS_LOG_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('[APN_CALLS_API] Error reading APN calls log:', error);
    return [];
  }
}

// Write APN calls log
function writeApnCallsLog(calls) {
  try {
    ensureLogDirectory();
    fs.writeFileSync(APN_CALLS_LOG_PATH, JSON.stringify(calls, null, 2), 'utf8');
  } catch (error) {
    console.error('[APN_CALLS_API] Error writing APN calls log:', error);
    throw error;
  }
}

// Log an outgoing APN call
function logApnCall(callData) {
  const calls = readApnCallsLog();
  
  const logEntry = {
    id: `call-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date().toISOString(),
    direction: 'outgoing',
    provider: callData.provider,
    endpoint: callData.endpoint,
    method: callData.method || 'POST',
    orderId: callData.orderId,
    transactionId: callData.transactionId,
    amount: callData.amount,
    currency: callData.currency || 'NT$',
    requestHeaders: callData.requestHeaders || {},
    requestPayload: callData.requestPayload || {},
    responseStatus: callData.responseStatus,
    responseHeaders: callData.responseHeaders || {},
    responseBody: callData.responseBody,
    processingTimeMs: callData.processingTimeMs,
    success: callData.success,
    errorMessage: callData.errorMessage,
    retryCount: callData.retryCount || 0,
    clientIP: callData.clientIP,
    userAgent: callData.userAgent,
    metadata: callData.metadata || {}
  };
  
  calls.push(logEntry);
  
  // Keep only last 1000 calls to prevent file from growing too large
  if (calls.length > 1000) {
    calls.splice(0, calls.length - 1000);
  }
  
  writeApnCallsLog(calls);
  return logEntry;
}

export default async function handler(req, res) {
  try {
    if (req.method === 'GET') {
      // Get APN calls history with optional filtering
      const { 
        provider, 
        orderId, 
        transactionId,
        status, 
        dateFrom,
        dateTo,
        page = 1,
        limit = 50,
        export: exportData 
      } = req.query;
      
      let calls = readApnCallsLog();
      
      // Apply filters
      if (provider) {
        calls = calls.filter(call => 
          call.provider.toLowerCase().includes(provider.toLowerCase())
        );
      }
      
      if (orderId) {
        calls = calls.filter(call => 
          call.orderId && call.orderId.toLowerCase().includes(orderId.toLowerCase())
        );
      }
      
      if (transactionId) {
        calls = calls.filter(call => 
          call.transactionId && call.transactionId.toLowerCase().includes(transactionId.toLowerCase())
        );
      }
      
      if (status) {
        if (status === 'success') {
          calls = calls.filter(call => call.success === true);
        } else if (status === 'failed') {
          calls = calls.filter(call => call.success === false);
        }
      }
      
      if (dateFrom) {
        const fromDate = new Date(dateFrom);
        calls = calls.filter(call => new Date(call.timestamp) >= fromDate);
      }
      
      if (dateTo) {
        const toDate = new Date(dateTo);
        calls = calls.filter(call => new Date(call.timestamp) <= toDate);
      }
      
      // Sort by timestamp (newest first)
      calls.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      // Handle export
      if (exportData === 'true') {
        const csvHeaders = 'Timestamp,Provider,Order ID,Transaction ID,Endpoint,Status,Processing Time (ms),Error\n';
        const csvRows = calls.map(call => {
          const timestamp = new Date(call.timestamp).toISOString();
          const status = call.success ? 'Success' : 'Failed';
          const error = call.errorMessage ? call.errorMessage.replace(/"/g, '""') : '';
          return `"${timestamp}","${call.provider}","${call.orderId}","${call.transactionId || ''}","${call.endpoint}","${status}","${call.processingTimeMs}","${error}"`;
        }).join('\n');
        
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="apn-calls-${new Date().toISOString().split('T')[0]}.csv"`);
        return res.status(200).send(csvHeaders + csvRows);
      }
      
      // Apply pagination
      const total = calls.length;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + parseInt(limit);
      const paginatedCalls = calls.slice(startIndex, endIndex);
      
      // Calculate statistics
      const stats = {
        total: total,
        successful: calls.filter(call => call.success === true).length,
        failed: calls.filter(call => call.success === false).length,
        byProvider: {},
        avgProcessingTime: 0
      };
      
      // Group by provider
      calls.forEach(call => {
        if (!stats.byProvider[call.provider]) {
          stats.byProvider[call.provider] = {
            total: 0,
            successful: 0,
            failed: 0,
            avgProcessingTime: 0
          };
        }
        
        stats.byProvider[call.provider].total++;
        if (call.success) {
          stats.byProvider[call.provider].successful++;
        } else {
          stats.byProvider[call.provider].failed++;
        }
      });
      
      // Calculate average processing times
      const validProcessingTimes = calls.filter(call => call.processingTimeMs > 0);
      if (validProcessingTimes.length > 0) {
        stats.avgProcessingTime = Math.round(
          validProcessingTimes.reduce((sum, call) => sum + call.processingTimeMs, 0) / validProcessingTimes.length
        );
        
        Object.keys(stats.byProvider).forEach(provider => {
          const providerCalls = calls.filter(call => call.provider === provider && call.processingTimeMs > 0);
          if (providerCalls.length > 0) {
            stats.byProvider[provider].avgProcessingTime = Math.round(
              providerCalls.reduce((sum, call) => sum + call.processingTimeMs, 0) / providerCalls.length
            );
          }
        });
      }
      
      return res.status(200).json({
        success: true,
        calls: paginatedCalls,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit),
        statistics: stats,
        message: `Retrieved ${paginatedCalls.length} APN calls`
      });
      
    } else if (req.method === 'POST') {
      // Log a new outgoing APN call
      const callData = req.body;
      
      // Validate required fields
      if (!callData.provider || !callData.endpoint || !callData.orderId) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: provider, endpoint, orderId'
        });
      }
      
      const logEntry = logApnCall(callData);
      
      return res.status(201).json({
        success: true,
        logEntry,
        message: 'APN call logged successfully'
      });
      
    } else if (req.method === 'DELETE') {
      // Clear APN calls log (admin only)
      const { confirm } = req.query;
      
      if (confirm !== 'true') {
        return res.status(400).json({
          success: false,
          error: 'Confirmation required. Add ?confirm=true to clear logs'
        });
      }
      
      writeApnCallsLog([]);
      
      return res.status(200).json({
        success: true,
        message: 'APN calls log cleared successfully'
      });
      
    } else {
      return res.status(405).json({
        success: false,
        error: 'Method not allowed'
      });
    }
    
  } catch (error) {
    console.error('[APN_CALLS_API] Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      details: error.message
    });
  }
}

// Export the logging function for use in other modules
export { logApnCall };