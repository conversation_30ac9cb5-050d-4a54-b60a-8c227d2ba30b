import fs from 'fs';
import path from 'path';

// Path to orders file
const dataDir = path.join(process.cwd(), 'data');
const ordersFilePath = path.join(dataDir, 'orders.json');

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const orderDetails = req.body;
    
    // Validate required data
    if (!orderDetails || !orderDetails.orderId) {
      return res.status(400).json({ error: 'Order ID is required' });
    }
    
    console.log(`[API] Saving order ${orderDetails.orderId} to orders.json`);

    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Load existing orders or create empty array
    let orders = [];
    if (fs.existsSync(ordersFilePath)) {
      try {
        const ordersData = fs.readFileSync(ordersFilePath, 'utf8');
        orders = JSON.parse(ordersData);
      } catch (error) {
        console.error(`[API] Error reading orders file:`, error);
        // Continue with empty orders array if file is corrupted
      }
    }
    
    // Check if order already exists (check both id and orderId fields)
    const existingOrderIndex = orders.findIndex(order => 
      order.orderId === orderDetails.orderId || 
      order.id === orderDetails.orderId ||
      order.orderId === orderDetails.id ||
      order.id === orderDetails.id
    );

    if (existingOrderIndex >= 0) {
      // Update existing order
      console.log(`[API] Updating existing order ${orderDetails.orderId}`);
      orders[existingOrderIndex] = {
        ...orders[existingOrderIndex],
        ...orderDetails,
        lastUpdated: Date.now()
      };
    } else {
      // Add new order
      console.log(`[API] Adding new order ${orderDetails.orderId}`);
      orders.push({
        ...orderDetails,
        timestamp: Date.now(),
        lastUpdated: Date.now()
      });
    }

    // Write updated orders back to file
    fs.writeFileSync(ordersFilePath, JSON.stringify(orders, null, 2));
    console.log(`[API] Order saved successfully to orders.json`);
    
    return res.status(200).json({ 
      success: true, 
      message: 'Order saved successfully'
    });
    
  } catch (error) {
    console.error('[API] Error saving order:', error);
    return res.status(500).json({ 
      error: 'Failed to save order', 
      message: error.message 
    });
  }
} 