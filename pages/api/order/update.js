import { promises as fs } from 'fs';
import path from 'path';
import { 
  getOrderById, 
  updateOrder, 
  getAllOrders,
  saveOrders 
} from '../../../utils/orderUtils';
import inventoryData from '../../../utils/inventory.json';

// Add CORS headers for development
const addCorsHeaders = (res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  return res;
};

export default async function handler(req, res) {
  // Add CORS headers for development
  addCorsHeaders(res);

  // Handle OPTIONS request for CORS
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const { customerId, orderId, ...updateData } = req.body;

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID is required'
      });
    }

    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: 'Order ID is required'
      });
    }

    // Ensure we have a valid orders.json file
    const ordersFilePath = path.join(process.cwd(), 'data', 'orders.json');
    
    try {
      // Check if file exists and is valid
      try {
        await fs.access(ordersFilePath);
      } catch (error) {
        // File doesn't exist, create empty array
        await fs.writeFile(ordersFilePath, JSON.stringify([], null, 2));
      }
      
      // Verify file contains valid JSON
      const fileContent = await fs.readFile(ordersFilePath, 'utf8');
      if (!fileContent || fileContent.trim() === '') {
        await fs.writeFile(ordersFilePath, JSON.stringify([], null, 2));
      } else {
        try {
          JSON.parse(fileContent);
        } catch (error) {
          // File contains invalid JSON, reset it
          console.error('Invalid JSON in orders.json, resetting file');
          await fs.writeFile(ordersFilePath, JSON.stringify([], null, 2));
        }
      }
    } catch (error) {
      console.error('Error validating orders file:', error);
    }

    // Get the existing order from the centralized orders.json
    const existingOrder = getOrderById(orderId);

    // Create inventory map for quick lookup
    const inventoryMap = new Map();
    inventoryData.forEach(item => {
      if (item.sku) {
        inventoryMap.set(item.sku, item);
      }
    });

    // Function to update prices and currency from inventory
    const updateWithInventoryData = (orderData) => {
      const updated = { ...orderData };
      
      // Update items with inventory data
      if (updated.items && updated.items.length > 0) {
        updated.items = updated.items.map(item => {
          if (item.sku) {
            const inventoryItem = inventoryMap.get(item.sku);
            if (inventoryItem) {
              return {
                ...item,
                price: inventoryItem.price || item.price
              };
            }
          }
          return item;
        });
        
        // Update currency based on first item's inventory data
        const firstItemSku = updated.items[0]?.sku;
        if (firstItemSku) {
          const inventoryItem = inventoryMap.get(firstItemSku);
          if (inventoryItem && inventoryItem.currency) {
            updated.currency = inventoryItem.currency;
          }
        }
      }
      
      // Update products with inventory data
      if (updated.products && updated.products.length > 0) {
        updated.products = updated.products.map(product => {
          if (product.sku) {
            const inventoryItem = inventoryMap.get(product.sku);
            if (inventoryItem) {
              return {
                ...product,
                price: inventoryItem.price || product.price
              };
            }
          }
          return product;
        });
        
        // Update currency based on first product's inventory data
        const firstProductSku = updated.products[0]?.sku;
        if (firstProductSku) {
          const inventoryItem = inventoryMap.get(firstProductSku);
          if (inventoryItem && inventoryItem.currency) {
            updated.currency = inventoryItem.currency;
          }
        }
      }
      
      // Recalculate total amount
      let totalAmount = 0;
      if (updated.items) {
        totalAmount = updated.items.reduce((sum, item) => {
          return sum + (item.price * item.quantity);
        }, 0);
      } else if (updated.products) {
        totalAmount = updated.products.reduce((sum, product) => {
          return sum + (product.price * product.quantity);
        }, 0);
      }
      
      if (totalAmount > 0) {
        updated.totalAmount = totalAmount;
        updated.amount = totalAmount;
      }
      
      return updated;
    };

    // Create a new order object if it doesn't exist, with inventory data applied
    let orderToUpdate = existingOrder 
      ? { ...existingOrder, ...updateData, updatedAt: new Date().toISOString() }
      : { 
          id: orderId, 
          customerId, 
          ...updateData, 
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
    
    // Apply inventory data updates
    const updatedOrder = updateWithInventoryData(orderToUpdate);

    // If updating an existing order
    if (existingOrder) {
      // Update the order in the centralized orders.json 
      const result = updateOrder(orderId, updatedOrder);
      
      if (!result) {
        console.log(`Order update via orderUtils failed, using direct update for order ${orderId}`);
        // If updateOrder returned null, try direct manipulation
        try {
          let orders = getAllOrders();
          const orderIndex = orders.findIndex(order => 
            order.id === orderId || order.orderId === orderId
          );
          
          if (orderIndex >= 0) {
            orders[orderIndex] = { ...orders[orderIndex], ...updatedOrder };
            saveOrders(orders);
          } else {
            // Order not found in array, add it
            orders.push(updatedOrder);
            saveOrders(orders);
          }
        } catch (directError) {
          console.error('Error in direct order update:', directError);
        }
      }
    } else {
      // New order - add it to the orders array (but check for duplicates first)
      try {
        let orders = getAllOrders();
        
        // Check if order already exists by ID
        const existingIndex = orders.findIndex(order => 
          order.id === orderId || order.orderId === orderId
        );
        
        if (existingIndex >= 0) {
          // Order already exists, update it instead
          console.log(`Order ${orderId} already exists, updating instead of adding`);
          orders[existingIndex] = { ...orders[existingIndex], ...updatedOrder };
        } else {
          // Safe to add new order
          orders.push(updatedOrder);
        }
        saveOrders(orders);
      } catch (addError) {
        console.error('Error adding new order:', addError);
      }
    }

    // Customer order files are no longer maintained - using central orders.json only

    return res.status(200).json({
      success: true,
      message: 'Order information updated successfully',
      order: updatedOrder
    });
  } catch (error) {
    console.error('Error updating order information:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while updating the order information',
      error: error.message
    });
  }
}