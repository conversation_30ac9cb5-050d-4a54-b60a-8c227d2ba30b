import withApiAuth from '../../../lib/apiAuth';

function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  const { customerId, items, billingAddress, shippingAddress, paymentMethod, currency, total } = req.body;
  
  // Validate required fields
  if (!customerId || !items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({ error: 'Customer ID and at least one item are required' });
  }
  
  // Generate an order ID
  const orderId = `ORD${Date.now().toString().slice(-6)}`;
  
  // Return a mock created order
  return res.status(201).json({
    id: orderId,

    customerId,
    items,
    billingAddress: billingAddress || '',
    shippingAddress: shippingAddress || billingAddress || '',
    paymentMethod: paymentMethod || 'credit_card',
    currency: currency || 'USD',
    total: total || items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
    status: 'pending',
    createdAt: new Date().toISOString()
  });
}

export default withApiAuth(handler); 