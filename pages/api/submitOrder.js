import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import { addOrder } from '../../utils/orderUtils';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const orderData = req.body;
    console.log('[Order API] Submitting order:', orderData);

    // Ensure required fields
    if (!orderData.orderId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required field: orderId'
      });
    }

    // Prepare order record with all relevant details
    const currentTime = new Date();
    const orderRecord = {
      id: orderData.orderId,
      customerId: orderData.customerInfo?.phone || 'guest',
      customerName: orderData.customerInfo?.fullName || orderData.customerInfo?.name || 'Guest',
      customerEmail: orderData.customerInfo?.email || null,
      customerPhone: orderData.customerInfo?.phone || null,
      items: orderData.items || [],
      paymentMethod: orderData.paymentMethod || 'unknown',
      paymentSubMethod: orderData.paymentSubMethod || null,
      amount: orderData.amount || 0,
      fee: orderData.fee || 0,
      totalAmount: orderData.totalAmount || 0,
      currency: orderData.currency || 'VND',
      status: 'pending',  // pending, paid, expired, cancelled
      paymentStatus: 'not_paid', // not_paid, paid, failed, refunded
      createdAt: currentTime.toISOString(),
      updatedAt: currentTime.toISOString(),
      storeId: orderData.storeId || null,
      paymentInfo: orderData.paymentInfo || null,
      
      // Payment validity details
      validUntil: orderData.paymentInfo?.orderExpireDate || 
                 (orderData.paymentMethod === 'convenience_store' 
                  ? new Date(currentTime.getTime() + 24 * 60 * 60 * 1000).toISOString() // 24 hours for convenience stores
                  : new Date(currentTime.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()), // 7 days default
      
      isExpired: false,
      expiryReason: null,
      
      // Additional data that might be useful
      shippingAddress: orderData.shippingAddress || orderData.customerInfo?.address || null,
      notes: orderData.message || orderData.customerInfo?.message || null,
      
      // For phone recharge products
      rechargeInfo: orderData.rechargeInfo || null
    };

    // Add the order to the centralized orders.json file
    const savedOrder = addOrder(orderRecord);

    return res.status(200).json({
      success: true,
      message: 'Order submitted successfully',
      orderId: orderRecord.id,
      order: savedOrder
    });
  } catch (error) {
    console.error('[Order API] Error submitting order:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while submitting the order',
      error: error.message
    });
  }
} 