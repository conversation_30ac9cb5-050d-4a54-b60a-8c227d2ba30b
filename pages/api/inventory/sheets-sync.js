import googleSheetsInventoryService from '../../../services/GoogleSheetsInventoryService';
import inventoryData from '../../../utils/inventory.json';
import fs from 'fs';
import path from 'path';

/**
 * API endpoint for Google Sheets inventory synchronization
 * Handles bidirectional sync between local inventory and Google Sheets
 */

// Helper function to save inventory data to local JSON file
function saveInventoryToFile(data) {
  const inventoryPath = path.join(process.cwd(), 'utils', 'inventory.json');
  fs.writeFileSync(inventoryPath, JSON.stringify(data, null, 2));
}

// Helper function to get current inventory count from new or old structure
function getCurrentInventoryCount(item) {
  if (item.inventory && typeof item.inventory === 'object') {
    // For separate type, auto-calculate count from items array
    if (item.inventory.type === 'separate' && Array.isArray(item.inventory.items)) {
      return item.inventory.items.length;
    }
    return parseInt(item.inventory.count) || 0;
  }
  return parseInt(item.currentInventory) || 0;
}

// Helper function to normalize inventory data
function normalizeInventoryData(data) {
  return data.map(item => ({
    ...item,
    currentInventory: getCurrentInventoryCount(item),
    inventory: item.inventory || { 
      type: 'same', 
      count: getCurrentInventoryCount(item) 
    }
  }));
}

export default async function handler(req, res) {
  try {
    const { method } = req;

    switch (method) {
      case 'GET':
        return await handleGetSync(req, res);
      case 'POST':
        return await handlePostSync(req, res);
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${method} not allowed` });
    }
  } catch (error) {
    console.error('Error in sheets-sync API:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

/**
 * GET /api/inventory/sheets-sync
 * Get sync status and Google Sheets URL
 */
async function handleGetSync(req, res) {
  try {
    const { action } = req.query;

    if (action === 'status') {
      // Return sync status and configuration
      const hasCredentials = !!(process.env.GOOGLE_SHEETS_INVENTORY_ID && 
                               process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL && 
                               process.env.GOOGLE_PRIVATE_KEY);
      
      const sheetUrl = googleSheetsInventoryService.getSheetUrl();
      
      return res.status(200).json({
        configured: hasCredentials,
        sheetUrl,
        lastSync: null, // TODO: Implement last sync tracking
        totalItems: inventoryData.length
      });
    }

    if (action === 'from-sheets') {
      // Sync from Google Sheets to local
      const sheetsData = await googleSheetsInventoryService.syncFromSheets();
      
      return res.status(200).json({
        success: true,
        message: `Successfully fetched ${sheetsData.length} items from Google Sheets`,
        data: sheetsData,
        count: sheetsData.length
      });
    }

    // Default: return current local inventory
    const normalizedData = normalizeInventoryData(inventoryData);
    return res.status(200).json({
      success: true,
      data: normalizedData,
      count: normalizedData.length
    });

  } catch (error) {
    console.error('Error in GET sheets-sync:', error);
    return res.status(500).json({ 
      error: 'Failed to process sync request',
      message: error.message 
    });
  }
}

/**
 * POST /api/inventory/sheets-sync
 * Perform sync operations
 */
async function handlePostSync(req, res) {
  try {
    const { action, data: requestData } = req.body;

    if (!action) {
      return res.status(400).json({ error: 'Missing required field: action' });
    }

    switch (action) {
      case 'to-sheets':
        return await syncToSheets(req, res);
      case 'from-sheets':
        return await syncFromSheets(req, res);
      case 'bidirectional':
        return await bidirectionalSync(req, res);
      case 'update-item':
        return await updateItemInSheets(req, res);
      case 'add-item':
        return await addItemToSheets(req, res);
      case 'delete-item':
        return await deleteItemFromSheets(req, res);
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Error in POST sheets-sync:', error);
    return res.status(500).json({ 
      error: 'Failed to process sync request',
      message: error.message 
    });
  }
}

/**
 * Sync local inventory to Google Sheets
 */
async function syncToSheets(req, res) {
  try {
    const normalizedData = normalizeInventoryData(inventoryData);
    const result = await googleSheetsInventoryService.syncToSheets(normalizedData);
    
    return res.status(200).json({
      success: true,
      message: `Successfully synced ${result.count} items to Google Sheets`,
      count: result.count,
      sheetUrl: googleSheetsInventoryService.getSheetUrl()
    });
  } catch (error) {
    console.error('Error syncing to sheets:', error);
    return res.status(500).json({ 
      error: 'Failed to sync to Google Sheets',
      message: error.message 
    });
  }
}

/**
 * Sync from Google Sheets to local inventory
 */
async function syncFromSheets(req, res) {
  try {
    const sheetsData = await googleSheetsInventoryService.syncFromSheets();
    
    // Save to local file
    saveInventoryToFile(sheetsData);
    
    return res.status(200).json({
      success: true,
      message: `Successfully synced ${sheetsData.length} items from Google Sheets`,
      count: sheetsData.length,
      data: sheetsData
    });
  } catch (error) {
    console.error('Error syncing from sheets:', error);
    return res.status(500).json({ 
      error: 'Failed to sync from Google Sheets',
      message: error.message 
    });
  }
}

/**
 * Bidirectional sync (merge changes)
 */
async function bidirectionalSync(req, res) {
  try {
    // First, get data from Google Sheets
    const sheetsData = await googleSheetsInventoryService.syncFromSheets();
    
    // Create a map of sheets data by SKU
    const sheetsMap = new Map();
    sheetsData.forEach(item => {
      if (item.sku) {
        sheetsMap.set(item.sku, item);
      }
    });
    
    // Merge with local data (local data takes precedence for conflicts)
    const normalizedLocal = normalizeInventoryData(inventoryData);
    const mergedData = [];
    const processedSkus = new Set();
    
    // Process local items
    normalizedLocal.forEach(localItem => {
      if (localItem.sku) {
        const sheetsItem = sheetsMap.get(localItem.sku);
        if (sheetsItem) {
          // Merge: use local data but update with sheets data if local is older
          const localDate = new Date(localItem.lastUpdated || 0);
          const sheetsDate = new Date(sheetsItem.lastUpdated || 0);
          
          if (sheetsDate > localDate) {
            mergedData.push(sheetsItem);
          } else {
            mergedData.push(localItem);
          }
        } else {
          // Local item not in sheets, keep local
          mergedData.push(localItem);
        }
        processedSkus.add(localItem.sku);
      }
    });
    
    // Add items that exist only in sheets
    sheetsData.forEach(sheetsItem => {
      if (sheetsItem.sku && !processedSkus.has(sheetsItem.sku)) {
        mergedData.push(sheetsItem);
      }
    });
    
    // Save merged data locally
    saveInventoryToFile(mergedData);
    
    // Sync merged data back to sheets
    await googleSheetsInventoryService.syncToSheets(mergedData);
    
    return res.status(200).json({
      success: true,
      message: `Successfully performed bidirectional sync with ${mergedData.length} items`,
      count: mergedData.length,
      localItems: normalizedLocal.length,
      sheetsItems: sheetsData.length,
      mergedItems: mergedData.length
    });
  } catch (error) {
    console.error('Error in bidirectional sync:', error);
    return res.status(500).json({ 
      error: 'Failed to perform bidirectional sync',
      message: error.message 
    });
  }
}

/**
 * Update a specific item in Google Sheets
 */
async function updateItemInSheets(req, res) {
  try {
    const { sku, itemData } = req.body;
    
    if (!sku || !itemData) {
      return res.status(400).json({ error: 'Missing required fields: sku, itemData' });
    }
    
    await googleSheetsInventoryService.updateItem(sku, itemData);
    
    return res.status(200).json({
      success: true,
      message: `Successfully updated item ${sku} in Google Sheets`
    });
  } catch (error) {
    console.error('Error updating item in sheets:', error);
    return res.status(500).json({ 
      error: 'Failed to update item in Google Sheets',
      message: error.message 
    });
  }
}

/**
 * Add a new item to Google Sheets
 */
async function addItemToSheets(req, res) {
  try {
    const { itemData } = req.body;
    
    if (!itemData || !itemData.sku) {
      return res.status(400).json({ error: 'Missing required fields: itemData with sku' });
    }
    
    await googleSheetsInventoryService.addItem(itemData);
    
    return res.status(200).json({
      success: true,
      message: `Successfully added item ${itemData.sku} to Google Sheets`
    });
  } catch (error) {
    console.error('Error adding item to sheets:', error);
    return res.status(500).json({ 
      error: 'Failed to add item to Google Sheets',
      message: error.message 
    });
  }
}

/**
 * Delete an item from Google Sheets
 */
async function deleteItemFromSheets(req, res) {
  try {
    const { sku } = req.body;
    
    if (!sku) {
      return res.status(400).json({ error: 'Missing required field: sku' });
    }
    
    await googleSheetsInventoryService.deleteItem(sku);
    
    return res.status(200).json({
      success: true,
      message: `Successfully deleted item ${sku} from Google Sheets`
    });
  } catch (error) {
    console.error('Error deleting item from sheets:', error);
    return res.status(500).json({ 
      error: 'Failed to delete item from Google Sheets',
      message: error.message 
    });
  }
}