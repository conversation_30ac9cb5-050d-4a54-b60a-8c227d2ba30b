import { v4 as uuidv4 } from 'uuid';
import inventoryData from '../../../utils/inventory.json';
import fs from 'fs';
import path from 'path';

// Helper function to get current inventory count from new or old structure
function getCurrentInventoryCount(item) {
  if (item.inventory && typeof item.inventory === 'object') {
    // For separate type, auto-calculate count from items array
    if (item.inventory.type === 'separate' && Array.isArray(item.inventory.items)) {
      return item.inventory.items.length;
    }
    return parseInt(item.inventory.count) || 0;
  }
  return parseInt(item.currentInventory) || 0;
}

// Helper function to get inventory type
function getInventoryType(item) {
  if (item.inventory && typeof item.inventory === 'object') {
    return item.inventory.type || 'same';
  }
  return 'same'; // Default for legacy items
}

// Helper function to get inventory items array (for separate type)
function getInventoryItems(item) {
  if (item.inventory && typeof item.inventory === 'object' && item.inventory.type === 'separate') {
    return item.inventory.items || [];
  }
  return [];
}

// Helper function to validate inventory item format
function validateInventoryItem(item) {
  if (typeof item === 'string') {
    return { id: uuidv4(), value: item.trim(), createdAt: new Date().toISOString() };
  }
  if (typeof item === 'object' && item.value) {
    return {
      id: item.id || uuidv4(),
      value: item.value.trim(),
      createdAt: item.createdAt || new Date().toISOString(),
      usedAt: item.usedAt || null,
      status: item.status || 'available'
    };
  }
  return null;
}

// Initialize inventory items with data from JSON file
let inventoryItems = inventoryData.map(item => ({
  id: item.sku || uuidv4(),
  sku: item.sku,
  name: item.name,
  brand: item.provider || 'Unknown',
  price: parseFloat(item.price),
  categories: Array.isArray(item.categories) ? item.categories : [item.categories],
  image: Array.isArray(item.image) ? item.image[0] : item.image,
  description: item.description,
  currentInventory: getCurrentInventoryCount(item), // Backward compatibility
  inventory: item.inventory || { type: 'same', count: getCurrentInventoryCount(item) }, // New structure
  currency: item.currency,
  buyfrom: item.buyfrom,
  unit: item.unit,
  activestatus: item.activestatus,
  configurations: item.configurations,
  auto_activate: item.auto_activate,
  sku_manufacturer: item.sku_manufacturer,
  notes: item.notes,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}));

// Function to save inventory back to JSON file
function saveInventoryToFile() {
  const inventoryPath = path.join(process.cwd(), 'utils', 'inventory.json');
  
  // Read the current file content
  const currentFileContent = JSON.parse(fs.readFileSync(inventoryPath, 'utf8'));
  
  // Update only the changed items
  const updatedContent = currentFileContent.map(originalItem => {
    const updatedItem = inventoryItems.find(item => item.sku === originalItem.sku);
    if (updatedItem) {
      // Update price and inventory structure
      const updatedItemData = {
        ...originalItem,
        price: updatedItem.price
      };
      
      // Handle new inventory structure
      if (updatedItem.inventory) {
        updatedItemData.inventory = updatedItem.inventory;
        // Auto-calculate count for separate type
        if (updatedItem.inventory.type === 'separate' && Array.isArray(updatedItem.inventory.items)) {
          updatedItemData.inventory.count = updatedItem.inventory.items.length;
        }
        // Remove old currentInventory field if it exists
        delete updatedItemData.currentInventory;
      } else {
        // Fallback to old structure for backward compatibility
        updatedItemData.currentInventory = updatedItem.currentInventory;
      }
      
      return updatedItemData;
    }
    return originalItem;
  });
  
  fs.writeFileSync(inventoryPath, JSON.stringify(updatedContent, null, 2));
}

export default function handler(req, res) {
  const { method } = req;

  switch (method) {
    case 'GET':
      return getInventoryManagement(req, res);
    case 'POST':
      return manageInventoryItems(req, res);
    case 'PUT':
      return updateInventoryManagement(req, res);
    case 'DELETE':
      return deleteInventoryItems(req, res);
    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      res.status(405).end(`Method ${method} Not Allowed`);
  }
}

// GET /api/inventory/manage - Enhanced inventory queries
async function getInventoryManagement(req, res) {
  try {
    const { 
      type, 
      sku, 
      category, 
      provider, 
      lowStock, 
      outOfStock, 
      search,
      page = 1,
      limit = 50,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;
    
    let filteredItems = [...inventoryItems];
    
    // Filter by inventory type
    if (type) {
      if (type === 'separate') {
        filteredItems = filteredItems.filter(item => getInventoryType(item) === 'separate');
      } else if (type === 'else' || type === 'same') {
        filteredItems = filteredItems.filter(item => getInventoryType(item) !== 'separate');
      }
    }
    
    // Filter by SKU
    if (sku) {
      filteredItems = filteredItems.filter(item => 
        item.sku && item.sku.toLowerCase().includes(sku.toLowerCase())
      );
    }
    
    // Filter by category
    if (category) {
      filteredItems = filteredItems.filter(item => 
        item.categories && item.categories.some(cat => 
          cat.toLowerCase().includes(category.toLowerCase())
        )
      );
    }
    
    // Filter by provider
    if (provider) {
      filteredItems = filteredItems.filter(item => 
        item.brand && item.brand.toLowerCase().includes(provider.toLowerCase())
      );
    }
    
    // Filter by stock levels
    if (lowStock) {
      const threshold = parseInt(lowStock) || 10;
      filteredItems = filteredItems.filter(item => {
        const count = getCurrentInventoryCount(item);
        return count > 0 && count <= threshold;
      });
    }
    
    if (outOfStock === 'true') {
      filteredItems = filteredItems.filter(item => getCurrentInventoryCount(item) === 0);
    }
    
    // Search functionality
    if (search) {
      const searchTerm = search.toLowerCase();
      filteredItems = filteredItems.filter(item => 
        (item.name && item.name.toLowerCase().includes(searchTerm)) ||
        (item.sku && item.sku.toLowerCase().includes(searchTerm)) ||
        (item.description && item.description.toLowerCase().includes(searchTerm))
      );
    }
    
    // Sorting
    filteredItems.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'count':
          aValue = getCurrentInventoryCount(a);
          bValue = getCurrentInventoryCount(b);
          break;
        case 'price':
          aValue = a.price || 0;
          bValue = b.price || 0;
          break;
        case 'name':
        default:
          aValue = (a.name || '').toLowerCase();
          bValue = (b.name || '').toLowerCase();
      }
      
      if (sortOrder === 'desc') {
        return aValue < bValue ? 1 : -1;
      }
      return aValue > bValue ? 1 : -1;
    });
    
    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedItems = filteredItems.slice(startIndex, endIndex);
    
    // Add computed fields
    const enhancedItems = paginatedItems.map(item => ({
      ...item,
      inventoryType: getInventoryType(item),
      currentCount: getCurrentInventoryCount(item),
      availableItems: getInventoryItems(item),
      isLowStock: getCurrentInventoryCount(item) <= 10 && getCurrentInventoryCount(item) > 0,
      isOutOfStock: getCurrentInventoryCount(item) === 0
    }));
    
    return res.status(200).json({
      items: enhancedItems,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredItems.length,
        totalPages: Math.ceil(filteredItems.length / parseInt(limit))
      },
      summary: {
        totalItems: inventoryItems.length,
        filteredItems: filteredItems.length,
        separateTypeItems: inventoryItems.filter(item => getInventoryType(item) === 'separate').length,
        sameTypeItems: inventoryItems.filter(item => getInventoryType(item) === 'same').length,
        lowStockItems: inventoryItems.filter(item => {
          const count = getCurrentInventoryCount(item);
          return count > 0 && count <= 10;
        }).length,
        outOfStockItems: inventoryItems.filter(item => getCurrentInventoryCount(item) === 0).length
      }
    });
  } catch (error) {
    console.error('Error in getInventoryManagement:', error);
    return res.status(500).json({ error: 'Failed to fetch inventory management data' });
  }
}

// POST /api/inventory/manage - Bulk operations and item management
async function manageInventoryItems(req, res) {
  try {
    const { action, sku, items, pasteData } = req.body;
    
    if (!action || !sku) {
      return res.status(400).json({ error: 'Missing required fields: action, sku' });
    }
    
    // Find the inventory item
    const itemIndex = inventoryItems.findIndex(item => item.sku === sku);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }
    
    const inventoryItem = inventoryItems[itemIndex];
    
    switch (action) {
      case 'addItems':
        return addInventoryItems(inventoryItem, itemIndex, items, res);
      case 'removeItems':
        return removeInventoryItems(inventoryItem, itemIndex, items, res);
      case 'bulkPaste':
        return bulkPasteItems(inventoryItem, itemIndex, pasteData, res);
      case 'clearItems':
        return clearInventoryItems(inventoryItem, itemIndex, res);
      case 'adjustCount':
        return adjustInventoryCount(inventoryItem, itemIndex, req.body.count, res);
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Error in manageInventoryItems:', error);
    return res.status(500).json({ error: 'Failed to manage inventory items' });
  }
}

// Helper function to add inventory items
function addInventoryItems(inventoryItem, itemIndex, items, res) {
  if (!Array.isArray(items) || items.length === 0) {
    return res.status(400).json({ error: 'Items must be a non-empty array' });
  }
  
  // Ensure inventory structure exists
  if (!inventoryItem.inventory) {
    inventoryItem.inventory = { type: 'separate', items: [], count: 0 };
  }
  
  if (inventoryItem.inventory.type !== 'separate') {
    return res.status(400).json({ error: 'Can only add items to separate type inventory' });
  }
  
  // Validate and add items
  const validatedItems = items.map(validateInventoryItem).filter(item => item !== null);
  
  if (!Array.isArray(inventoryItem.inventory.items)) {
    inventoryItem.inventory.items = [];
  }
  
  inventoryItem.inventory.items.push(...validatedItems);
  inventoryItem.inventory.count = inventoryItem.inventory.items.length;
  inventoryItem.currentInventory = inventoryItem.inventory.count;
  inventoryItem.updatedAt = new Date().toISOString();
  
  inventoryItems[itemIndex] = inventoryItem;
  saveInventoryToFile();
  
  return res.status(200).json({
    message: `Added ${validatedItems.length} items successfully`,
    item: inventoryItem,
    addedItems: validatedItems
  });
}

// Helper function to remove inventory items
function removeInventoryItems(inventoryItem, itemIndex, items, res) {
  if (!Array.isArray(items) || items.length === 0) {
    return res.status(400).json({ error: 'Items must be a non-empty array' });
  }
  
  if (inventoryItem.inventory.type !== 'separate') {
    return res.status(400).json({ error: 'Can only remove items from separate type inventory' });
  }
  
  if (!Array.isArray(inventoryItem.inventory.items)) {
    return res.status(400).json({ error: 'No items to remove' });
  }
  
  // Remove items by ID or value
  const itemsToRemove = Array.isArray(items) ? items : [items];
  const removedItems = [];
  
  itemsToRemove.forEach(itemToRemove => {
    const index = inventoryItem.inventory.items.findIndex(item => 
      item.id === itemToRemove || item.value === itemToRemove
    );
    if (index !== -1) {
      removedItems.push(inventoryItem.inventory.items.splice(index, 1)[0]);
    }
  });
  
  inventoryItem.inventory.count = inventoryItem.inventory.items.length;
  inventoryItem.currentInventory = inventoryItem.inventory.count;
  inventoryItem.updatedAt = new Date().toISOString();
  
  inventoryItems[itemIndex] = inventoryItem;
  saveInventoryToFile();
  
  return res.status(200).json({
    message: `Removed ${removedItems.length} items successfully`,
    item: inventoryItem,
    removedItems
  });
}

// Helper function for bulk paste functionality
function bulkPasteItems(inventoryItem, itemIndex, pasteData, res) {
  if (!pasteData || typeof pasteData !== 'string') {
    return res.status(400).json({ error: 'Paste data must be a string' });
  }
  
  // Ensure inventory structure exists
  if (!inventoryItem.inventory) {
    inventoryItem.inventory = { type: 'separate', items: [], count: 0 };
  }
  
  if (inventoryItem.inventory.type !== 'separate') {
    return res.status(400).json({ error: 'Can only paste items to separate type inventory' });
  }
  
  // Parse paste data - support multiple formats
  const lines = pasteData.split(/\r?\n/).filter(line => line.trim());
  const newItems = [];
  
  lines.forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine) {
      // Support comma-separated values in a single line
      const values = trimmedLine.split(',').map(v => v.trim()).filter(v => v);
      values.forEach(value => {
        const validatedItem = validateInventoryItem(value);
        if (validatedItem) {
          newItems.push(validatedItem);
        }
      });
    }
  });
  
  if (newItems.length === 0) {
    return res.status(400).json({ error: 'No valid items found in paste data' });
  }
  
  if (!Array.isArray(inventoryItem.inventory.items)) {
    inventoryItem.inventory.items = [];
  }
  
  inventoryItem.inventory.items.push(...newItems);
  inventoryItem.inventory.count = inventoryItem.inventory.items.length;
  inventoryItem.currentInventory = inventoryItem.inventory.count;
  inventoryItem.updatedAt = new Date().toISOString();
  
  inventoryItems[itemIndex] = inventoryItem;
  saveInventoryToFile();
  
  return res.status(200).json({
    message: `Pasted ${newItems.length} items successfully`,
    item: inventoryItem,
    addedItems: newItems
  });
}

// Helper function to clear all inventory items
function clearInventoryItems(inventoryItem, itemIndex, res) {
  if (inventoryItem.inventory.type !== 'separate') {
    return res.status(400).json({ error: 'Can only clear items from separate type inventory' });
  }
  
  const clearedCount = inventoryItem.inventory.items ? inventoryItem.inventory.items.length : 0;
  
  inventoryItem.inventory.items = [];
  inventoryItem.inventory.count = 0;
  inventoryItem.currentInventory = 0;
  inventoryItem.updatedAt = new Date().toISOString();
  
  inventoryItems[itemIndex] = inventoryItem;
  saveInventoryToFile();
  
  return res.status(200).json({
    message: `Cleared ${clearedCount} items successfully`,
    item: inventoryItem
  });
}

// Helper function to adjust inventory count
function adjustInventoryCount(inventoryItem, itemIndex, newCount, res) {
  if (typeof newCount !== 'number' || newCount < 0) {
    return res.status(400).json({ error: 'Count must be a non-negative number' });
  }
  
  if (inventoryItem.inventory.type === 'separate') {
    return res.status(400).json({ 
      error: 'Cannot manually adjust count for separate type inventory. Count is auto-calculated from items array.' 
    });
  }
  
  inventoryItem.inventory.count = newCount;
  inventoryItem.currentInventory = newCount;
  inventoryItem.updatedAt = new Date().toISOString();
  
  inventoryItems[itemIndex] = inventoryItem;
  saveInventoryToFile();
  
  return res.status(200).json({
    message: 'Inventory count adjusted successfully',
    item: inventoryItem
  });
}

// PUT /api/inventory/manage - Update inventory management settings
async function updateInventoryManagement(req, res) {
  try {
    const { sku, inventoryType, settings } = req.body;
    
    if (!sku) {
      return res.status(400).json({ error: 'Missing required field: sku' });
    }
    
    const itemIndex = inventoryItems.findIndex(item => item.sku === sku);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }
    
    const inventoryItem = inventoryItems[itemIndex];
    
    // Update inventory type
    if (inventoryType && ['separate', 'same'].includes(inventoryType)) {
      const currentType = getInventoryType(inventoryItem);
      
      if (currentType !== inventoryType) {
        if (inventoryType === 'separate') {
          // Convert to separate type
          const currentCount = getCurrentInventoryCount(inventoryItem);
          inventoryItem.inventory = {
            type: 'separate',
            items: [],
            count: 0
          };
        } else {
          // Convert to same type
          const currentCount = getCurrentInventoryCount(inventoryItem);
          inventoryItem.inventory = {
            type: 'same',
            count: currentCount
          };
        }
      }
    }
    
    // Update other settings
    if (settings && typeof settings === 'object') {
      Object.keys(settings).forEach(key => {
        if (['lowStockThreshold', 'autoReorder', 'reorderPoint'].includes(key)) {
          if (!inventoryItem.settings) {
            inventoryItem.settings = {};
          }
          inventoryItem.settings[key] = settings[key];
        }
      });
    }
    
    inventoryItem.updatedAt = new Date().toISOString();
    inventoryItems[itemIndex] = inventoryItem;
    saveInventoryToFile();
    
    return res.status(200).json({
      message: 'Inventory management settings updated successfully',
      item: inventoryItem
    });
  } catch (error) {
    console.error('Error in updateInventoryManagement:', error);
    return res.status(500).json({ error: 'Failed to update inventory management settings' });
  }
}

// DELETE /api/inventory/manage - Delete inventory items or reset inventory
async function deleteInventoryItems(req, res) {
  try {
    const { sku, itemIds, resetInventory } = req.body;
    
    if (!sku) {
      return res.status(400).json({ error: 'Missing required field: sku' });
    }
    
    const itemIndex = inventoryItems.findIndex(item => item.sku === sku);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }
    
    const inventoryItem = inventoryItems[itemIndex];
    
    if (resetInventory) {
      // Reset entire inventory
      if (inventoryItem.inventory.type === 'separate') {
        inventoryItem.inventory.items = [];
        inventoryItem.inventory.count = 0;
      } else {
        inventoryItem.inventory.count = 0;
      }
      inventoryItem.currentInventory = 0;
    } else if (itemIds && Array.isArray(itemIds)) {
      // Delete specific items
      if (inventoryItem.inventory.type !== 'separate') {
        return res.status(400).json({ error: 'Can only delete specific items from separate type inventory' });
      }
      
      const deletedItems = [];
      itemIds.forEach(itemId => {
        const index = inventoryItem.inventory.items.findIndex(item => item.id === itemId);
        if (index !== -1) {
          deletedItems.push(inventoryItem.inventory.items.splice(index, 1)[0]);
        }
      });
      
      inventoryItem.inventory.count = inventoryItem.inventory.items.length;
      inventoryItem.currentInventory = inventoryItem.inventory.count;
      
      return res.status(200).json({
        message: `Deleted ${deletedItems.length} items successfully`,
        item: inventoryItem,
        deletedItems
      });
    }
    
    inventoryItem.updatedAt = new Date().toISOString();
    inventoryItems[itemIndex] = inventoryItem;
    saveInventoryToFile();
    
    return res.status(200).json({
      message: 'Inventory reset successfully',
      item: inventoryItem
    });
  } catch (error) {
    console.error('Error in deleteInventoryItems:', error);
    return res.status(500).json({ error: 'Failed to delete inventory items' });
  }
}