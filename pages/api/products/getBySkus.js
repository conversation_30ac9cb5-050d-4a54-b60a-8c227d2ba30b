import { fetchStore } from '../../../utils/storeProvider';

export default async function handler(req, res) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { skus, storeId } = req.body;
    
    // Validate request body
    if (!skus || !Array.isArray(skus) || !storeId) {
      return res.status(400).json({ error: 'Invalid request. Required: skus array and storeId' });
    }

    // Get store data
    const allStores = await fetchStore();
    const store = allStores.find(s => s.storeId === storeId);
    
    if (!store) {
      return res.status(404).json({ error: `Store with ID ${storeId} not found` });
    }
    
    // Get inventory for this store
    const inventory = store.inventory || [];
    
    // Find products by SKUs
    const products = [];
    
    skus.forEach(sku => {
      const product = inventory.find(item => item.sku === sku);
      if (product) {
        products.push({
          id: product.id,
          sku: product.sku,
          name: product.name,
          price: product.price,
          currency: product.currency,
          image: product.image,
          currentInventory: product.currentInventory,
        });
      }
    });
    
    // If no products found, add sample data for testing
    if (products.length === 0 && process.env.NODE_ENV === 'development') {
      console.log('[API] No products found in inventory, adding sample data for testing');
      
      // Sample product data for testing
      skus.forEach(sku => {
        products.push({
          id: sku,
          sku: sku,
          name: `Product ${sku}`,
          price: 150,
          currency: 'NT',
          image: ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp"],
          currentInventory: 10,
        });
      });
    }
    
    return res.status(200).json({ products });
  } catch (error) {
    console.error('Error fetching products by SKUs:', error);
    return res.status(500).json({ error: 'Failed to fetch products' });
  }
} 