import nodemailer from 'nodemailer';
import DENOMINAT<PERSON> from "../../utils/currencyProvider";

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const { topic, shopId, name, toanha, floorroom, mobile, email, time, ngaygiaohang, message, cart, orderId, total } = req.body;
    
    // Create Nodemailer transporter
    const transporter = nodemailer.createTransport({
      host: process.env.AWS_SES_HOST,
      port: process.env.AWS_SES_PORT,
      secure: false,
      auth: {
        user: process.env.AWS_SES_USER,
        pass: process.env.AWS_SES_PASS
      }
    });

    // Construct the HTML table for cart details
    const cartTable = `
      <table style="border-collapse: collapse; border: 1px dotted black;">
        <thead>
          <tr>            
            <th style="border: 1px dotted black; padding: 8px;">HÌNH ẢNH</th>
            <th style="border: 1px dotted black; padding: 8px;">SẢN PHẨM</th>
            <th style="border: 1px dotted black; padding: 8px;">ĐƠN GIÁ</th>
            <th style="border: 1px dotted black; padding: 8px;">SỐ LƯỢNG</th>
            <th style="border: 1px dotted black; padding: 8px;">THÀNH TIỀN</th>
          </tr>
        </thead>
        <tbody>
          ${cart.map(item => `
            <tr>
              <td style="border: 1px dotted black; padding: 8px;"><img src="${Array.isArray(item.image) && item.image.length > 0 ? item.image[0] : 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp'}" alt="${item.name}" style="max-width: 100px;"></td>
              <td style="border: 1px dotted black; padding: 8px;">${item.name}</td>
              <td style="border: 1px dotted black; padding: 8px;">${DENOMINATION + item.price}</td>
              <td style="border: 1px dotted black; padding: 8px;">${item.quantity}</td>
              <td style="border: 1px dotted black; padding: 8px;">${DENOMINATION + item.price*item.quantity}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      <table style="border-collapse: collapse; border: 1px dotted black;">
        <tbody>
          <tr>
            <td style="border: 1px dotted black; padding: 8px;">TỔNG GIÁ TRỊ ĐƠN HÀNG:</td>
            <td style="border: 1px dotted black; padding: 8px;">${DENOMINATION}${total}</td>
          </tr>
        </tbody>
      </table>
    `;

    // Construct the HTML table for other details
    const otherDetailsTable = `
      <table style="border-collapse: collapse; border: 1px dotted black;">
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Đơn mua:</td>
          <td style="border: 1px dotted black; padding: 8px;">${topic}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Tên người mua:</td>
          <td style="border: 1px dotted black; padding: 8px;">${name}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Toà nhà tại Times City:</td>
          <td style="border: 1px dotted black; padding: 8px;">${toanha}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Số Tầng và Căn:</td>
          <td style="border: 1px dotted black; padding: 8px;">${floorroom}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Mobile:</td>
          <td style="border: 1px dotted black; padding: 8px;">${mobile}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Email:</td>
          <td style="border: 1px dotted black; padding: 8px;">${email}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Thời gian giao hàng mong muốn:</td>
          <td style="border: 1px dotted black; padding: 8px;">${time}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Ngày giao hàng:</td>
          <td style="border: 1px dotted black; padding: 8px;">${ngaygiaohang}</td>
        </tr>
        <tr>
          <td style="border: 1px dotted black; padding: 8px;">Lời dặn dò khác:</td>
          <td style="border: 1px dotted black; padding: 8px;">${message}</td>
        </tr>
      </table>
    `;

    // Compose email message
    const mailOptions = {
      from: 'ABN ASIA <<EMAIL>>',
      to: email,
      bcc: '<EMAIL>',
      subject: `${topic} MÃ ĐƠN HÀNG: ${orderId} - Tên người mua: ${name} Toà nhà: ${toanha}`,
      html: `
        <p>THÔNG TIN ĐƠN HÀNG: ${orderId} VỚI TỔNG CHI PHÍ: ${DENOMINATION} ${total}</p>
        ${otherDetailsTable}
        <p>CHI TIẾT ĐƠN HÀNG:</p>
        ${cartTable}
      `
    };

    try {
      await transporter.sendMail(mailOptions);
      res.status(200).json({ message: 'Email sent successfully' });
    } catch (error) {
      console.error('Error sending email:', error);
      res.status(500).json({ error: 'Failed to send email' });
    }
  } else {
    res.status(405).json({ error: 'Method Not Allowed' });
  }
}
