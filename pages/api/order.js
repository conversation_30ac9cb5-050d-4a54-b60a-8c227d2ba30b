import withApiAuth from '../../lib/apiAuth';

function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  const { id } = req.query;
  
  if (!id) {
    return res.status(400).json({ error: 'Order ID is required' });
  }
  
  // Return a mock order
  return res.status(200).json({
    id,

    customerId: 'CUST001',
    items: [
      {
        name: 'Product 1',
        price: 29.99,
        quantity: 2
      },
      {
        name: 'Product 2',
        price: 49.99,
        quantity: 1
      }
    ],
    billingAddress: {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zip: '12345',
      country: 'USA'
    },
    shippingAddress: {
      street: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      zip: '12345',
      country: 'USA'
    },
    paymentMethod: 'credit_card',
    currency: 'USD',
    subtotal: 109.97,
    tax: 8.80,
    shipping: 5.99,
    total: 124.76,
    status: 'processing',
    createdAt: '2024-04-10T00:00:00Z',
    updatedAt: '2024-04-10T00:00:00Z'
  });
}

export default withA<PERSON><PERSON><PERSON>(handler); 