import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import { serialize } from 'cookie';

// Path to customers data file
const dataDir = path.join(process.cwd(), 'data');
const customersFile = path.join(dataDir, 'customers.json');

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Extract credentials from request body
    const { customerId, password } = req.body;

    if (!customerId || !password) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID and password are required'
      });
    }

    console.log('Login attempt:', { customerId, password: '******' });

    // Check if customers file exists
    if (!fs.existsSync(customersFile)) {
      console.log('Creating customers file...');
      // Create directory if it doesn't exist
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      
      // Create empty customers file
      fs.writeFileSync(customersFile, JSON.stringify([]));
    }

    // Read customers data
    let customers: any[] = [];
    try {
      const data = fs.readFileSync(customersFile, 'utf8');
      customers = JSON.parse(data);
      if (!Array.isArray(customers)) {
        console.error('Customers data is not an array');
        customers = [];
      }
    } catch (error) {
      console.error('Error reading customers file:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Error reading customer data' 
      });
    }

    // Find customer by ID and password
    const customer = customers.find(c => c.id === customerId && c.password === password);

    // Handle test accounts for demo purposes
    if (!customer && (
      (customerId === 'test-user-1' && password === 'password123') ||
      (customerId === 'test-user-2' && password === 'password456')
    )) {
      // Create a test customer for demo
      const testCustomer = {
        id: customerId,
        customerId: customerId,
        name: customerId === 'test-user-1' ? 'Test User 1' : 'Test User 2',
        email: customerId === 'test-user-1' ? '<EMAIL>' : '<EMAIL>',
        phone: customerId === 'test-user-1' ? '**********' : '**********',
        password: password, // Plain text password
        address: '123 Test St, Test City',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      return res.status(200).json({
        success: true,
        message: 'Login successful (demo account)',
        customer: {
          id: testCustomer.id,
          name: testCustomer.name,
          email: testCustomer.email,
          phone: testCustomer.phone,
          address: testCustomer.address
        }
      });
    }

    if (!customer) {
      return res.status(401).json({
        success: false,
        message: 'Đăng nhập hoặc Mật khẩu không chính xác'
      });
    }

    // Set cookie for authentication
    const token = customer.id;
    res.setHeader('Set-Cookie', serialize('customerToken', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV !== 'development',
      maxAge: 60 * 60 * 24 * 7, // 1 week
      path: '/',
      sameSite: 'strict'
    }));

    // Send success response (never include password in response)
    const { password: _, ...customerData } = customer;
    
    return res.status(200).json({
      success: true,
      message: 'Login successful',
      customer: customerData
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
} 