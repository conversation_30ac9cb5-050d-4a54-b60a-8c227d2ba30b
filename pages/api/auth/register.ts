import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { name, email, phone, password, dateOfBirth, gender, storeId } = req.body;

    // Validate required fields
    if (!name || !email || !phone || !password || !storeId) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng điền đầy đủ thông tin'
      });
    }

    // Read existing customers
    const customersPath = path.join(process.cwd(), 'data', 'customers.json');
    const customers = JSON.parse(fs.readFileSync(customersPath, 'utf8'));

    // Check if email already exists
    const existingUser = customers.find((customer: any) => 
      customer.email === email || customer.phone === phone
    );
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: existingUser.email === email ? 
          'Email này đã được đăng ký' : 
          'Số điện thoại này đã được đăng ký'
      });
    }

    // Create new customer using phone as ID
    const newCustomer = {
      id: phone,
      name,
      email,
      password,
      phone,
      addresses: [],
      orders: [],
      purchaseHistory: [],
      preferredPaymentMethod: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      dateOfBirth: dateOfBirth || '',
      gender: gender || ''
    };

    // Add to customers array
    customers.push(newCustomer);

    // Write back to file
    fs.writeFileSync(customersPath, JSON.stringify(customers, null, 2));

    // Return success without password
    const { password: _, ...customerData } = newCustomer;
    return res.status(201).json({
      success: true,
      message: 'Đăng ký thành công',
      customer: customerData
    });

  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({
      success: false,
      message: 'Đã xảy ra lỗi khi đăng ký. Vui lòng thử lại sau.'
    });
  }
} 