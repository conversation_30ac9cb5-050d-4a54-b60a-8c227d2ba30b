import { NextApiRequest, NextApiResponse } from 'next';
import { serialize } from 'cookie';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Clear the authentication cookie by setting an expired date
  const cookie = serialize('customerToken', '', {
    maxAge: -1,
    path: '/',
  });

  // Set the cookie header
  res.setHeader('Set-Cookie', cookie);

  // Send the response
  return res.status(200).json({ success: true, message: 'Logged out successfully' });
} 