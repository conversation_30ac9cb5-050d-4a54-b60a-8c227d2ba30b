import jwt from 'jsonwebtoken';
import fs from 'fs';
import path from 'path';

const JWT_SECRET = process.env.JWT_SECRET || 'magshop-dev-secret';
const dataFilePath = path.join(process.cwd(), 'data', 'customers.json');

const handler = async (req, res) => {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }

  const token = authHeader.split(' ')[1];

  try {
    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET);
    const customerId = decoded.id;

    // Read customers data
    let data = { customers: [] };
    try {
      const fileData = fs.readFileSync(dataFilePath, 'utf8');
      data = JSON.parse(fileData);
    } catch (err) {
      return res.status(404).json({ message: 'Customer data not found' });
    }

    // Find customer by ID
    const customer = data.customers.find(c => c.id === customerId);
    
    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    // Return customer data without password
    const { password, ...customerData } = customer;
    
    return res.status(200).json({
      message: 'Token verified',
      customer: customerData
    });
  } catch (error) {
    console.error('Token verification error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    }
    
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export default handler; 