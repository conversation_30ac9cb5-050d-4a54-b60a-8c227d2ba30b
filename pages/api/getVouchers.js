import axios from 'axios';

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const { shopid, discountcode } = req.body;

    try {
      // Define the URL of the API endpoint
      const apiUrl = `https://workflow.abnasia.org/webhook/06ad7897-0eaa-46d1-b8b8-dff843485498j43?shopid=${shopid}&discountcode=${discountcode}`;

      // Make a GET request to the API
      const response = await axios.get(apiUrl);

      // Handle successful response
      res.status(200).json(response.data);
    } catch (error) {
      // Handle error
      console.error('Error fetching discount data:', error);
      res.status(500).json({ error: 'Failed to fetch discount data' });
    }
  } else {
    res.status(405).json({ error: 'Method Not Allowed' });
  }
}