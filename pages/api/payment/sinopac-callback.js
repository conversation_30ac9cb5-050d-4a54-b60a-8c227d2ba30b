import crypto from 'crypto';
import { updateOrder, updateOrderPaymentStatus } from '../../../utils/orderUtils';

// SinoPAC API credentials - matching the order API
const SINOPAC_API = {
  MERCHANT_ID: 'NA0511_001',
  HASH: {
    A1: 'F342DAABD58249D8',
    A2: 'D3E28D4E9A4E4EE2',
    B1: 'C61852BEBDA44676',
    B2: '1BD9BDB007E34418'
  },
  X_KEY: 'b5e6986d-8636-4aa0-8c93-441ad14b2098'
};

// Helper functions (copied from order.js for consistency)
const strToHexBytes = (string) => {
  const hex = [];
  for (let i = 0; i < string.length; i += 2) {
    hex.push(parseInt(string.substr(i, 2), 16));
  }
  return hex;
};

const hexBytesToString = (hex) => {
  let result = '';
  for (let i = 0; i < hex.length; i++) {
    let str = hex[i].toString(16);
    if (str.length < 2) {
      str = '0' + str;
    }
    result += str;
  }
  return result.toUpperCase();
};

const setXOR = (byte1, byte2) => {
  const result = [];
  for (let i = 0; i < byte1.length; i++) {
    result[i] = byte1[i] ^ byte2[i];
  }
  return result;
};

const getHashID = (hash) => {
  const Byte_A1 = strToHexBytes(hash.A1);
  const Byte_A2 = strToHexBytes(hash.A2);
  const Byte_B1 = strToHexBytes(hash.B1);
  const Byte_B2 = strToHexBytes(hash.B2);
  
  const XOR1 = setXOR(Byte_A1, Byte_A2);
  const XOR2 = setXOR(Byte_B1, Byte_B2);
  
  const result = hexBytesToString(XOR1) + hexBytesToString(XOR2);
  return result;
};

const getIV = (nonce) => {
  const data = crypto.createHash('sha256').update(nonce).digest('hex').toUpperCase();
  return data.substr(data.length - 16, 16);
};

// AES CBC decryption (following PHP openssl_decrypt format)
const decryptAesCBC = (data, key, iv) => {
  try {
    // Convert hex data to buffer
    const encryptedBuffer = Buffer.from(data, 'hex');
    
    // Convert key and iv to Buffer
    const keyBuffer = Buffer.from(key, 'hex');
    const ivBuffer = Buffer.from(iv, 'hex');
    
    // Create decipher
    const decipher = crypto.createDecipheriv('aes-256-cbc', keyBuffer, ivBuffer);
    decipher.setAutoPadding(false);
    
    let decrypted = decipher.update(encryptedBuffer, null, 'utf8');
    decrypted += decipher.final('utf8');
    
    // Remove padding
    const padding = decrypted.charCodeAt(decrypted.length - 1);
    const result = decrypted.slice(0, -padding);
    
    return result;
  } catch (error) {
    console.error('AES decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
};

// Function to query order status for PayToken notifications
const queryOrderStatus = async (payToken, targetUrl) => {
  try {
    // Import required functions - we'll implement a simplified version
    const axios = require('axios');
    
    const service = {
      ShopNo: SINOPAC_API.MERCHANT_ID,
      PayToken: payToken
    };
    
    // This would use the same APIService structure as order.js
    // For now, we'll log the requirement
    console.log('[SinoPAC Callback] OrderPayQuery required for PayToken:', payToken);
    
    // TODO: Implement full OrderPayQuery using APIService structure
    // This should follow the same pattern as order.js with getNonce, getHashID, etc.
    
    return {
      success: true,
      message: 'OrderPayQuery functionality needs implementation'
    };
    
  } catch (error) {
    console.error('Error querying order status:', error);
    throw error;
  }
};

export default async function handler(req, res) {
  try {
    console.log('[SinoPAC Callback] Received callback', {
      method: req.method,
      query: req.query,
      body: req.body,
      headers: req.headers
    });

    // Handle POST requests (backend notifications from SinoPAC)
    if (req.method === 'POST') {
      // Get raw body content (following PHP file_get_contents('php://input'))
      const rawData = req.body;
      
      console.log('[SinoPAC Callback] Raw data received:', rawData);
      
      // Parse the incoming request following PHP sample structure
      if (rawData && rawData.ShopNo && rawData.PayToken) {
        console.log('[SinoPAC Callback] Processing PayToken notification');
        
        // This is a PayToken notification - we need to query the order status
        // Using OrderPayQuery service as shown in PHP sample
        
        try {
          // Import the APIService function from order.js (or implement it here)
          // For now, we'll handle the basic case
          
          const paymentData = {
            shopNo: rawData.ShopNo,
            payToken: rawData.PayToken,
            orderNo: rawData.OrderNo || '',
            status: 'S' // Assume success for now
          };
          
          console.log('[SinoPAC Callback] Payment data:', paymentData);
          
          // Update order status
          if (paymentData.orderNo) {
            await updateOrderPaymentStatus(paymentData.orderNo, 'paid', {
              provider: 'sinopac',
              payToken: paymentData.payToken,
              shopNo: paymentData.shopNo,
              statusDetail: 'Payment completed via backend notification',
              rawResponse: rawData
            });
            
            console.log(`[SinoPAC Callback] Payment for order ${paymentData.orderNo} marked as PAID`);
          }
          
          // Return success response to SinoPAC (following PHP sample)
          console.log('[SinoPAC Callback] Sending success response to SinoPAC');
          return res.status(200).json({ Status: "S" });
          
        } catch (error) {
          console.error('[SinoPAC Callback] Error processing PayToken:', error);
          return res.status(200).json({ Status: "F", Message: error.message });
        }
      }
      
      // Handle encrypted message responses
      if (rawData && rawData.Message && rawData.Nonce) {
        console.log('[SinoPAC Callback] Processing encrypted message response');
        
        try {
          // Get HashID
          const hashID = getHashID(SINOPAC_API.HASH);
          
          // Get IV from response nonce
          const iv = getIV(rawData.Nonce);
          
          // Decrypt the message
          const decryptedData = decryptAesCBC(rawData.Message, hashID, iv);
          const paymentResult = JSON.parse(decryptedData);
          
          console.log('[SinoPAC Callback] Decrypted payment result:', paymentResult);
          
          // Process based on payment result
          if (paymentResult.OrderNo) {
            const isSuccess = paymentResult.TSResultCode === '00' || paymentResult.Status === 'S';
            const status = isSuccess ? 'paid' : 'failed';
            
            await updateOrderPaymentStatus(paymentResult.OrderNo, status, {
              provider: 'sinopac',
              transactionId: paymentResult.TSNo || '',
              amount: paymentResult.Amount || 0,
              resultCode: paymentResult.TSResultCode || '',
              resultMessage: paymentResult.TSResultMsg || '',
              statusDetail: isSuccess ? 'Payment completed successfully' : 'Payment failed',
              rawResponse: paymentResult
            });
            
            console.log(`[SinoPAC Callback] Payment for order ${paymentResult.OrderNo} marked as ${status.toUpperCase()}`);
          }
          
          return res.status(200).json({ Status: "S" });
          
        } catch (error) {
          console.error('[SinoPAC Callback] Error processing encrypted message:', error);
          return res.status(200).json({ Status: "F", Message: error.message });
        }
      }
      
      // Handle simple status updates
      const paymentData = rawData;
      const orderId = paymentData.OrderNo;
      
      if (!orderId) {
        console.error('[SinoPAC Callback] Missing OrderNo in callback data');
        return res.status(200).json({ Status: "F", Message: 'Missing OrderNo' });
      }
      
      console.log(`[SinoPAC Callback] Processing simple status update for order ${orderId}`);
      
      // Check payment status
      if (paymentData.Status === 'S') {
        await updateOrderPaymentStatus(orderId, 'paid', {
          provider: 'sinopac',
          transactionId: paymentData.TransactionNo || '',
          amount: paymentData.Amount || 0,
          statusDetail: 'Payment completed successfully',
          rawResponse: paymentData
        });
        
        console.log(`[SinoPAC Callback] Payment for order ${orderId} marked as PAID`);
      } else if (paymentData.Status === 'F') {
        await updateOrderPaymentStatus(orderId, 'failed', {
          provider: 'sinopac',
          errorCode: paymentData.ErrorCode || '',
          errorMessage: paymentData.ErrorMessage || 'Payment failed',
          rawResponse: paymentData
        });
        
        console.log(`[SinoPAC Callback] Payment for order ${orderId} marked as FAILED`);
      } else {
        await updateOrderPaymentStatus(orderId, 'processing', {
          provider: 'sinopac',
          statusDetail: 'Payment is being processed',
          rawResponse: paymentData
        });
        
        console.log(`[SinoPAC Callback] Payment for order ${orderId} marked as PROCESSING`);
      }
      
      // Return success to SinoPAC
      return res.status(200).json({ Status: "S" });
    }
    
    // Handle GET requests (browser redirects after payment)
    if (req.method === 'GET') {
      const { OrderNo, Status, PayToken } = req.query;
      
      console.log('[SinoPAC Callback] Processing GET redirect', { OrderNo, Status, PayToken });
      
      // Redirect to order confirmation or failure page based on status
      if (Status === 'S' && OrderNo) {
        return res.redirect(`/orders/${OrderNo}?status=success`);
      } else if (OrderNo) {
        return res.redirect(`/orders/${OrderNo}?status=failed`);
      } else {
        return res.redirect('/orders');
      }
    }
    
    // Method not allowed for other request types
    return res.status(405).json({ Status: "F", Message: 'Method not allowed' });
    
  } catch (error) {
    console.error('[SinoPAC Callback] Error processing callback:', error);
    return res.status(500).json({ Status: "F", Message: 'Server error' });
  }
} 