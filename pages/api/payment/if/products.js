import fs from 'fs';
import path from 'path';

export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Load configuration from the same path as the script
    const configPath = path.join(process.cwd(), 'components/taiwan/operators/if/config.json');
    
    // Check if config file exists
    if (!fs.existsSync(configPath)) {
      console.error('Config file not found at:', configPath);
      return res.status(404).json({ 
        success: false, 
        error: 'Configuration file not found' 
      });
    }

    // Read and parse config file
    const configData = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configData);

    // Extract products
    const products = config.productCodes || [];

    // Group products by type for better organization
    const topupProducts = products.filter(p => 
      p.name.toLowerCase().includes('recharge') || 
      p.name.toLowerCase().includes('instant')
    );
    
    const wirelessProducts = products.filter(p => 
      p.name.toLowerCase().includes('wireless') || 
      p.name.toLowerCase().includes('plan')
    );

    // Return products with additional metadata
    return res.status(200).json({
      success: true,
      products: products,
      categorized: {
        topup: topupProducts,
        wireless: wirelessProducts
      },
      total: products.length,
      config: {
        merchantID: config.merchantID,
        apiUrl: config.liveUrl,
        testUrl: config.testUrl
      }
    });

  } catch (error) {
    console.error('Error loading IF products:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to load products',
      details: error.message 
    });
  }
} 