import { checkCardStatus } from '../../../../components/taiwan/operators/if/service';

export default async function handler(req, res) {
  if (req.method !== 'GET' && req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  const isDebugMode = (req.body?.debug === true || req.query?.debug === 'true') || 
                      process.env.NODE_ENV !== 'production';
  
  // Get transaction ID from query params or request body
  const transactionId = req.query.transactionId || req.body.transactionId;
  
  if (!transactionId) {
    return res.status(400).json({
      success: false,
      error: 'Transaction ID is required',
      debug: isDebugMode ? { requestData: req.method === 'POST' ? req.body : req.query } : undefined
    });
  }
  
  try {
    // Call the card status check service
    const result = await checkCardStatus(transactionId);
    
    // Return the result
    if (result.success) {
      return res.status(200).json({
        success: true,
        data: result.data,
        debug: isDebugMode ? { requestTime: new Date().toISOString() } : undefined
      });
    } else {
      return res.status(400).json({
        success: false,
        error: result.error,
        resultCode: result.resultCode,
        debug: isDebugMode ? { 
          requestTime: new Date().toISOString(),
          errorDetails: result
        } : undefined
      });
    }
  } catch (error) {
    console.error('[IF CARD API] Unexpected error checking status:', error);
    
    return res.status(500).json({
      success: false,
      error: 'An unexpected error occurred while checking card status. Please try again later.',
      debug: isDebugMode ? { 
        requestTime: new Date().toISOString(),
        errorMessage: error.message
      } : undefined
    });
  }
} 