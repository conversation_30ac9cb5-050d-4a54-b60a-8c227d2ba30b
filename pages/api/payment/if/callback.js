import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import config from '../../../../components/taiwan/operators/if/config.json';

// Helper for logging
const logIFCallback = (message, data = null) => {
  console.log(`[IF CARD CALLBACK] ${message}`, data ? JSON.stringify(data) : '');
  
  // Write to log file for debugging
  try {
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    const logFile = path.join(logDir, 'if-card-callbacks.log');
    const logEntry = `${new Date().toISOString()} - ${message} - ${data ? JSON.stringify(data) : ''}\n`;
    
    fs.appendFileSync(logFile, logEntry);
  } catch (err) {
    console.error('Error writing to log file:', err);
  }
};

// Helper to read JSON file
const readJsonFile = (filePath) => {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    if (!fs.existsSync(fullPath)) {
      return [];
    }
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    logIFCallback(`Error reading file ${filePath}`, { error: error.message });
    return [];
  }
};

// Helper to write JSON file
const writeJsonFile = (filePath, data) => {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    const dirPath = path.dirname(fullPath);
    
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    fs.writeFileSync(fullPath, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    logIFCallback(`Error writing file ${filePath}`, { error: error.message });
    return false;
  }
};

// Validate callback hash
function validateHash(data, providedHash) {
  try {
    // Reconstruct the hash string based on received data
    const { transaction_id, card_number, timestamp, status } = data;
    
    const dataString = `${transaction_id}|${card_number}|${timestamp}|${status}`;
    
    const hashString = config.security.hashFormat
      .replace('${cust_id}', config.merchantID)
      .replace('${data}', dataString)
      .replace('${hash_base}', config.hashBase);
    
    const calculatedHash = crypto.createHash(config.security.hashAlgorithm)
      .update(hashString)
      .digest('hex');
    
    return calculatedHash === providedHash;
  } catch (error) {
    logIFCallback('Error validating hash', { error: error.message });
    return false;
  }
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Log the received callback
  logIFCallback('Received callback', { body: req.body, headers: req.headers });
  
  try {
    const {
      transaction_id,
      card_number,
      card_value,
      timestamp,
      status,
      message,
      hash
    } = req.body;
    
    // Validate required fields
    if (!transaction_id || !hash || !status) {
      logIFCallback('Missing required fields', { received: req.body });
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Validate hash
    const isValidHash = validateHash(req.body, hash);
    if (!isValidHash) {
      logIFCallback('Invalid hash', { 
        received: hash,
        transaction_id,
        status
      });
      return res.status(403).json({ error: 'Invalid hash' });
    }
    
    // Process the callback based on status
    const callbackData = {
      transactionId: transaction_id,
      cardNumber: card_number ? `****${card_number.slice(-4)}` : null,
      cardValue: card_value,
      status,
      message,
      receivedAt: new Date().toISOString()
    };
    
    // Store callback in callbacks.json
    const callbacksPath = 'data/if-card-callbacks.json';
    const existingCallbacks = readJsonFile(callbacksPath);
    
    existingCallbacks.push(callbackData);
    writeJsonFile(callbacksPath, existingCallbacks);
    
    // Update order status if applicable
    if (transaction_id.startsWith('order_')) {
      // Extract order ID from transaction ID
      const orderId = transaction_id.replace('order_', '');
      const ordersPath = 'data/orders.json';
      const orders = readJsonFile(ordersPath);
      
      const orderIndex = orders.findIndex(order => order.id === orderId);
      
      if (orderIndex !== -1) {
        const order = orders[orderIndex];
        
        // Update order with card activation status
        orders[orderIndex] = {
          ...order,
          ifCardActivation: {
            status,
            message,
            cardValue: card_value,
            processedAt: new Date().toISOString()
          },
          updatedAt: new Date().toISOString()
        };
        
        writeJsonFile(ordersPath, orders);
        logIFCallback(`Updated order ${orderId} with activation status`, { status });
      } else {
        logIFCallback(`Order not found for transaction ${transaction_id}`);
      }
    }
    
    // Return success response to the IF system
    return res.status(200).json({ success: true });
    
  } catch (error) {
    logIFCallback('Error processing callback', { 
      error: error.message,
      stack: error.stack
    });
    
    return res.status(500).json({ error: 'Internal server error' });
  }
} 