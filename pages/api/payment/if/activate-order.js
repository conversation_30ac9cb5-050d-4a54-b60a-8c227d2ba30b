import fs from 'fs';
import path from 'path';
import { activateCard } from '../../../../components/taiwan/operators/if/service';

const ORDERS_FILE = path.join(process.cwd(), 'data', 'orders.json');

const readOrdersData = () => {
  try {
    if (fs.existsSync(ORDERS_FILE)) {
      const data = fs.readFileSync(ORDERS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading orders data:', error);
    return [];
  }
};

const writeOrdersData = (orders) => {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(ORDERS_FILE);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    fs.writeFileSync(ORDERS_FILE, JSON.stringify(orders, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing orders data:', error);
    return false;
  }
};

const updateOrderWithTopup = (orderId, topupData) => {
  const orders = readOrdersData();
  const orderIndex = orders.findIndex(o => o.id === orderId);
  
  if (orderIndex === -1) {
    return false;
  }
  
  orders[orderIndex] = {
    ...orders[orderIndex],
    ifTopup: topupData,
    updatedAt: new Date().toISOString()
  };
  
  return writeOrdersData(orders);
};

export default async function handler(req, res) {
  const requestStartTime = Date.now();
  const requestId = `if-order-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  
  if (req.method !== 'POST') {
    console.log(`[IF-ORDER-API:${requestId}] Rejected ${req.method} request`);
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      requestId
    });
  }

  const isDebugMode = process.env.NODE_ENV !== 'production' || req.body.debug === true;
  
  console.log(`[IF-ORDER-API:${requestId}] Received order topup request`, {
    orderId: req.body.orderId,
    phoneNumber: req.body.phoneNumber ? `${req.body.phoneNumber.substring(0, 4)}****${req.body.phoneNumber.slice(-2)}` : undefined,
    productId: req.body.productId,
    adminUser: req.body.adminUser,
    userAgent: req.headers['user-agent'],
    ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
  });
  
  const { orderId, phoneNumber, productId, adminUser, timestamp } = req.body;
  
  // Validate required fields
  if (!orderId || !phoneNumber || !productId || !adminUser) {
    const missingFields = [];
    if (!orderId) missingFields.push('orderId');
    if (!phoneNumber) missingFields.push('phoneNumber');
    if (!productId) missingFields.push('productId');
    if (!adminUser) missingFields.push('adminUser');
    
    console.log(`[IF-ORDER-API:${requestId}] Validation failed - Missing fields:`, missingFields);
    return res.status(400).json({
      success: false,
      error: `Missing required fields: ${missingFields.join(', ')}`,
      requestId
    });
  }
  
  // Validate phone number format
  if (!/^\d{10}$/.test(phoneNumber)) {
    console.log(`[IF-ORDER-API:${requestId}] Invalid phone number format`);
    return res.status(400).json({
      success: false,
      error: 'Phone number must be 10 digits',
      requestId
    });
  }
  
  try {
    // Check if order exists
    const orders = readOrdersData();
    const order = orders.find(o => o.id === orderId);
    
    if (!order) {
      console.log(`[IF-ORDER-API:${requestId}] Order not found: ${orderId}`);
      return res.status(404).json({
        success: false,
        error: 'Order not found',
        requestId
      });
    }
    
    // Check if already topped up
    if (order.ifTopup && order.ifTopup.status === 'completed') {
      console.log(`[IF-ORDER-API:${requestId}] Order already topped up: ${orderId}`);
      return res.status(400).json({
        success: false,
        error: 'Order has already been topped up',
        requestId,
        existingTopup: {
          adminUser: order.ifTopup.adminUser,
          completedAt: order.ifTopup.completedAt,
          transactionId: order.ifTopup.transactionId
        }
      });
    }
    
    // Generate transaction orderId for IF service
    const ifOrderId = `${orderId}_topup_${Date.now()}`;
    
    console.log(`[IF-ORDER-API:${requestId}] Processing topup for order ${orderId} with IF orderId: ${ifOrderId}`);
    
    // Log start of topup attempt
    const topupStartData = {
      status: 'in_progress',
      orderId,
      phoneNumber,
      productId,
      adminUser,
      startedAt: new Date().toISOString(),
      ifOrderId,
      requestId
    };
    
    updateOrderWithTopup(orderId, topupStartData);
    
    // Call the IF card activation service
    const result = await activateCard({
      phoneNumber,
      productId,
      orderId: ifOrderId
    });
    
    // Prepare topup completion data
    let topupCompleteData;
    
    if (result.success) {
      const responseTime = Date.now() - requestStartTime;
      console.log(`[IF-ORDER-API:${requestId}] Topup successful for order ${orderId} - Response time: ${responseTime}ms`);
      
      topupCompleteData = {
        status: 'completed',
        orderId,
        phoneNumber,
        productId,
        adminUser,
        startedAt: topupStartData.startedAt,
        completedAt: new Date().toISOString(),
        ifOrderId,
        transactionId: result.data?.transactionId || ifOrderId,
        resultCode: result.data?.resultCode,
        message: result.data?.message,
        cardValue: result.data?.cardValue,
        productName: result.data?.productName,
        responseTime,
        requestId
      };
      
      // Update order with successful topup
      updateOrderWithTopup(orderId, topupCompleteData);
      
      return res.status(200).json({
        success: true,
        data: {
          transactionId: result.data?.transactionId || ifOrderId,
          orderId,
          productId,
          adminUser,
          completedAt: topupCompleteData.completedAt,
          cardValue: result.data?.cardValue,
          message: result.data?.message
        },
        requestId,
        responseTime
      });
      
    } else {
      const responseTime = Date.now() - requestStartTime;
      console.log(`[IF-ORDER-API:${requestId}] Topup failed for order ${orderId} - ${result.error} (${result.resultCode}) - Response time: ${responseTime}ms`);
      
      topupCompleteData = {
        status: 'failed',
        orderId,
        phoneNumber,
        productId,
        adminUser,
        startedAt: topupStartData.startedAt,
        failedAt: new Date().toISOString(),
        ifOrderId,
        error: result.error,
        resultCode: result.resultCode,
        responseTime,
        requestId
      };
      
      // Update order with failed topup
      updateOrderWithTopup(orderId, topupCompleteData);
      
      return res.status(400).json({
        success: false,
        error: result.error,
        resultCode: result.resultCode,
        orderId,
        requestId,
        responseTime
      });
    }
    
  } catch (error) {
    const responseTime = Date.now() - requestStartTime;
    console.error(`[IF-ORDER-API:${requestId}] Unexpected error for order ${orderId} - Response time: ${responseTime}ms`, error);
    
    // Log failed topup attempt
    const topupErrorData = {
      status: 'error',
      orderId,
      phoneNumber,
      productId,
      adminUser,
      startedAt: new Date().toISOString(),
      errorAt: new Date().toISOString(),
      error: error.message,
      responseTime,
      requestId
    };
    
    updateOrderWithTopup(orderId, topupErrorData);
    
    const errorMessage = process.env.NODE_ENV === 'production'
      ? 'An unexpected error occurred. Please try again later.'
      : error.message;
    
    return res.status(500).json({
      success: false,
      error: errorMessage,
      orderId,
      requestId,
      responseTime,
      debug: isDebugMode ? {
        errorMessage: error.message,
        errorStack: process.env.NODE_ENV === 'production' ? undefined : error.stack
      } : undefined
    });
  }
}