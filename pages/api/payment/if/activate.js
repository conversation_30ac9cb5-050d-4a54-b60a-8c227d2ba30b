import { activateCard } from '../../../../components/taiwan/operators/if/service';

export default async function handler(req, res) {
  // Track request time for performance monitoring
  const requestStartTime = Date.now();
  const requestId = `if-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  
  // Only allow POST method
  if (req.method !== 'POST') {
    console.log(`[IF-API:${requestId}] Rejected ${req.method} request`);
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed',
      requestId
    });
  }

  // Determine debug mode - set to false in production unless explicitly requested
  const isDebugMode = process.env.NODE_ENV !== 'production' || req.body.debug === true;
  
  // Log request (without sensitive data)
  console.log(`[IF-API:${requestId}] Received activation request`, {
    phoneNumber: req.body.phoneNumber ? `${req.body.phoneNumber.substring(0, 4)}****${req.body.phoneNumber.slice(-2)}` : undefined,
    productId: req.body.productId,
    orderId: req.body.orderId,
    userAgent: req.headers['user-agent'],
    ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
  });
  
  // Extract and validate request body
  const { phoneNumber, productId, orderId } = req.body;
  
  if (!phoneNumber || !productId) {
    const missingFields = [];
    if (!phoneNumber) missingFields.push('phoneNumber');
    if (!productId) missingFields.push('productId');
    
    console.log(`[IF-API:${requestId}] Validation failed - Missing fields:`, missingFields);
    return res.status(400).json({
      success: false,
      error: `Missing required fields: ${missingFields.join(', ')}`,
      requestId,
      debug: isDebugMode ? { requestBody: req.body } : undefined
    });
  }
  
  // Validate phone number format - Taiwanese numbers are 10 digits
  if (!/^\d{10}$/.test(phoneNumber)) {
    console.log(`[IF-API:${requestId}] Invalid phone number format`);
    return res.status(400).json({
      success: false,
      error: 'Phone number must be 10 digits',
      requestId,
      debug: isDebugMode ? { phoneNumber } : undefined
    });
  }
  
  try {
    // Generate orderId if not provided
    const finalOrderId = orderId || `order_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
    
    console.log(`[IF-API:${requestId}] Calling VRC service with orderId: ${finalOrderId}`);
    
    // Call the card activation service
    const result = await activateCard({
      phoneNumber,
      productId,
      orderId: finalOrderId
    });
    
    // Return the result
    if (result.success) {
      const responseTime = Date.now() - requestStartTime;
      console.log(`[IF-API:${requestId}] Success - Response time: ${responseTime}ms`);
      
      return res.status(200).json({
        success: true,
        data: result.data,
        requestId,
        responseTime,
        debug: isDebugMode ? { 
          requestTime: new Date().toISOString(),
          responseTime
        } : undefined
      });
    } else {
      const responseTime = Date.now() - requestStartTime;
      console.log(`[IF-API:${requestId}] Failed - ${result.error} (${result.resultCode}) - Response time: ${responseTime}ms`);
      
      return res.status(400).json({
        success: false,
        error: result.error,
        resultCode: result.resultCode,
        requestId,
        responseTime,
        debug: isDebugMode ? { 
          requestTime: new Date().toISOString(),
          responseTime,
          errorDetails: result
        } : undefined
      });
    }
  } catch (error) {
    const responseTime = Date.now() - requestStartTime;
    console.error(`[IF-API:${requestId}] Unexpected error - Response time: ${responseTime}ms`, error);
    
    // Handle specific error types
    const errorMessage = process.env.NODE_ENV === 'production' 
      ? 'An unexpected error occurred. Please try again later.'
      : error.message;
    
    return res.status(500).json({
      success: false,
      error: errorMessage,
      requestId,
      responseTime,
      debug: isDebugMode ? { 
        requestTime: new Date().toISOString(),
        responseTime,
        errorMessage: error.message,
        errorStack: process.env.NODE_ENV === 'production' ? undefined : error.stack
      } : undefined
    });
  }
} 