import fs from 'fs';
import path from 'path';

// Helper for logging
const log711CardResult = (message, data = null) => {
  console.log(`[7-11 CARD RESULT API] ${message}`, data ? JSON.stringify(data) : '');
};

// Helper to read JSON file
const readJsonFile = (filePath) => {
  const fullPath = path.join(process.cwd(), filePath);
  const fileContents = fs.readFileSync(fullPath, 'utf8');
  return JSON.parse(fileContents);
};

// Helper to write JSON file
const writeJsonFile = (filePath, data) => {
  const fullPath = path.join(process.cwd(), filePath);
  fs.writeFileSync(fullPath, JSON.stringify(data, null, 2), 'utf8');
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  const { orderId, resultCode, message, allParams } = req.body;

  if (!orderId) {
    log711CardResult('Missing order ID', req.body);
    return res.status(400).json({ 
      success: false, 
      error: 'Order ID is required' 
    });
  }

  log711CardResult(`Processing payment result for order ${orderId}`, {
    resultCode,
    message,
    params: allParams
  });

  try {
    // Read orders from JSON file
    const orders = readJsonFile('data/orders.json');
    
    // Find the order in the JSON data
    const orderIndex = orders.findIndex(order => order.id === orderId);
    
    if (orderIndex === -1) {
      log711CardResult(`Order not found: ${orderId}`);
      return res.status(404).json({ 
        success: false, 
        error: 'Order not found' 
      });
    }

    const order = orders[orderIndex];

    log711CardResult(`Found order ${orderId}`, { 
      storeId: order.storeId,
      paymentMethod: order.paymentMethod
    });

    // Determine payment status based on result code
    // Common result codes:
    // 0000 = Success
    // 1000 = Pending
    // Other = Various error conditions
    const isSuccess = resultCode === '0000';
    const isPending = resultCode === '1000';
    
    let paymentStatus = 'failed';
    if (isSuccess) {
      paymentStatus = 'success';
    } else if (isPending) {
      paymentStatus = 'pending';
    }

    // Update the order with payment result
    orders[orderIndex] = {
      ...order,
      status: paymentStatus,
      paymentResult: {
        resultCode,
        message,
        receivedAt: new Date().toISOString(),
        allParams
      },
      updatedAt: new Date().toISOString()
    };

    // Write updated orders back to file
    writeJsonFile('data/orders.json', orders);

    log711CardResult(`Updated order ${orderId} status to ${paymentStatus}`);

    // Return the order data and status for the frontend
    return res.status(200).json({
      success: true,
      paymentStatus,
      orderData: {
        orderId: order.id,
        date: order.createdAt,
        totalAmount: order.totalAmount || order.amount,
        currency: order.currency,
        storeId: order.storeId
      }
    });

  } catch (error) {
    log711CardResult(`Error processing payment result for order ${orderId}`, {
      error: error.message,
      stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      error: 'Internal server error while processing payment result'
    });
  }
} 