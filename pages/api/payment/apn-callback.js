/**
 * 7-Eleven APN (Active Payment Notification) Callback Endpoint
 * 
 * This API handles payment notifications from the 7-Eleven payment system.
 * It updates order statuses in the orders.json file and appends callback logs 
 * directly to each order for centralized payment lifecycle tracking.
 * 
 * No database is used - this is a pure file-based implementation.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// Path to orders.json file
const ORDERS_FILE_PATH = path.join(process.cwd(), 'data', 'orders.json');

console.log('[APN_CALLBACK_INIT] Current working directory:', process.cwd());
console.log('[APN_CALLBACK_INIT] Orders file path:', ORDERS_FILE_PATH);

// Create callback log entry for appending to order
function createCallbackLogEntry(requestData, responseData, startTime, orderRef = null) {
  // Calculate processing time
  const endTime = Date.now();
  const processingTime = endTime - startTime;
  
  return {
    timestamp: new Date().toISOString(),
    paymentMethod: '7-eleven',
    callbackType: 'apn',
    requestPayload: requestData,
    responseStatus: responseData.status,
    responseBody: responseData.body,
    processingTimeMs: processingTime,
    clientIP: responseData.clientIP || 'unknown',
    userAgent: responseData.userAgent || 'unknown',
    orderFound: !!orderRef
  };
}

// Verify checksum from APN
function verifyChecksum(api_id, trans_id, amount, status, nonce, checksum) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  const calculatedChecksum = crypto.createHash('md5').update(message).digest('hex');
  return calculatedChecksum === checksum;
}

// Load orders from file
function loadOrders() {
  try {
    const data = fs.readFileSync(ORDERS_FILE_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`[APN_CALLBACK] Error loading orders: ${error.message}`);
    return null;
  }
}

// Save orders to file
function saveOrders(orders) {
  try {
    fs.writeFileSync(ORDERS_FILE_PATH, JSON.stringify(orders, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error(`[APN_CALLBACK] Error saving orders: ${error.message}`);
    return false;
  }
}

// Update order status and append callback log
function updateOrderWithCallback(order, status, transId, callbackLog) {
  // Map 7-11 status codes to our internal status
  switch (status) {
    case 'A': // Waiting for payment
      order.status = 'pending';
      order.paymentStatus = 'waiting_payment';
      break;
    case 'B': // Paid by payer
      order.status = 'processing';
      order.paymentStatus = 'paid';
      break;
    case 'D': // Overdue payment
      order.status = 'cancelled';
      order.paymentStatus = 'expired';
      order.isExpired = true;
      order.expiryReason = 'payment_expired';
      break;
    default:
      order.status = 'pending';
      order.paymentStatus = 'unknown';
  }
  
  // Store the trans_id from 7-Eleven in the order if it's not already set
  if (transId && !order.trans_id) {
    order.trans_id = transId;
  }
  
  // Initialize payment callbacks array if not exists
  if (!order.paymentCallbacks) {
    order.paymentCallbacks = [];
  }
  
  // Append the callback log to the order
  order.paymentCallbacks.push(callbackLog);
  
  // Update timestamp
  order.updatedAt = new Date().toISOString();
  
  return order;
}

export default async function handler(req, res) {
  // Record start time for performance logging
  const startTime = Date.now();
  
  // Extract client info for logging
  const clientIP = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const userAgent = req.headers['user-agent'] || 'unknown';
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    const responseData = {
      status: 405,
      body: 'Method Not Allowed',
      clientIP,
      userAgent
    };
    // For method errors, we can't link to an order, so just log the attempt
    console.log('[APN_CALLBACK] Method not allowed:', JSON.stringify(createCallbackLogEntry({ method: req.method }, responseData, startTime)));
    return res.status(405).end('Method Not Allowed');
  }
  
  try {
    // Log the APN callback payload
    console.log('[APN_CALLBACK] APN callback received:', req.body);
    
    const {
      api_id,
      trans_id,
      order_no,
      amount,
      status,
      nonce,
      checksum,
      payment_code,
      payment_detail
    } = req.body;
    
    // Validate required fields
    if (!api_id || !trans_id || !order_no || !amount || !status || !nonce || !checksum) {
      const responseData = {
        status: 400,
        body: 'Bad Request: Missing required fields',
        clientIP,
        userAgent
      };
      // Log validation error (can't link to order since we don't have valid data)
      console.log('[APN_CALLBACK] Validation error:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error('[APN_CALLBACK] Missing required fields');
      return res.status(400).end('Bad Request: Missing required fields');
    }
    
    // Verify checksum
    const isValidChecksum = verifyChecksum(api_id, trans_id, amount, status, nonce, checksum);
    
    if (!isValidChecksum) {
      const responseData = {
        status: 400,
        body: 'Bad Request: Invalid checksum',
        clientIP,
        userAgent
      };
      // Log checksum error (can't link to order since checksum is invalid)
      console.log('[APN_CALLBACK] Checksum error:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error('[APN_CALLBACK] Invalid checksum');
      return res.status(400).end('Bad Request: Invalid checksum');
    }
    
    // Load orders from file
    const orders = loadOrders();
    
    if (!orders) {
      const responseData = {
        status: 500,
        body: 'Internal Server Error: Failed to load orders',
        clientIP,
        userAgent
      };
      // Log system error
      console.log('[APN_CALLBACK] System error:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error('[APN_CALLBACK] Failed to load orders');
      return res.status(500).end('Internal Server Error: Failed to load orders');
    }
    
    // Find the order
    const orderIndex = orders.findIndex(o => 
      o.trans_id === trans_id || o.id === order_no
    );
    
    if (orderIndex === -1) {
      const responseData = {
        status: 404,
        body: 'Not Found: Order not found',
        clientIP,
        userAgent
      };
      // Log order not found error
      console.log('[APN_CALLBACK] Order not found:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error(`[APN_CALLBACK] Order not found: trans_id=${trans_id}, order_no=${order_no}`);
      return res.status(404).end('Not Found: Order not found');
    }
    
    // Create successful response data for logging
    const responseData = {
      status: 200,
      body: 'OK',
      clientIP,
      userAgent
    };
    
    // Create callback log entry
    const callbackLog = createCallbackLogEntry(req.body, responseData, startTime, orders[orderIndex]);
    
    // Update order status and append callback log
    const updatedOrder = updateOrderWithCallback(orders[orderIndex], status, trans_id, callbackLog);
    orders[orderIndex] = updatedOrder;
    
    // Save updated orders
    const saved = saveOrders(orders);
    
    if (!saved) {
      const errorResponseData = {
        status: 500,
        body: 'Internal Server Error: Failed to save orders',
        clientIP,
        userAgent
      };
      console.log('[APN_CALLBACK] Save error:', JSON.stringify(createCallbackLogEntry(req.body, errorResponseData, startTime, orders[orderIndex])));
      console.error('[APN_CALLBACK] Failed to save orders');
      return res.status(500).end('Internal Server Error: Failed to save orders');
    }
    
    console.log(`[APN_CALLBACK] Successfully updated order ${order_no} to status ${status} and logged callback`);
    
    // Return OK response
    return res.status(200).end('OK');
  } catch (error) {
    console.error('[APN_CALLBACK] Error processing APN callback:', error);
    
    const responseData = {
      status: 500,
      body: 'Internal Server Error',
      error: error.message,
      clientIP,
      userAgent
    };
    console.log('[APN_CALLBACK] Exception error:', JSON.stringify(createCallbackLogEntry(req.body || {}, responseData, startTime)));
    
    return res.status(500).end('Internal Server Error');
  }
} 