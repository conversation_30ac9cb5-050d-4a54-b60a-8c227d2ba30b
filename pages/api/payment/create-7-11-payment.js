import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

// Add detailed debug logging function
const logApi = (step, data = null) => {
  const timestamp = new Date().toISOString();
  const prefix = `[7-11 API][${timestamp}][${step}]`;
  
  if (data) {
    try {
      // Try to stringify the data for better readability in logs
      const stringData = typeof data === 'object' ? 
        JSON.stringify(data, (key, value) => {
          // Special handling for sensitive values
          if (key === 'password' || key === 'API_PASSWORD' || key === 'token' || key.toLowerCase().includes('password')) {
            return '[REDACTED]';
          }
          return value;
        }, 2) : 
        data;
      console.log(`${prefix}:`, stringData);
    } catch (e) {
      console.log(`${prefix}: (Data too large or circular - showing raw)`, data);
    }
  } else {
    console.log(prefix);
  }
};

// Configuration - NO MOCK MODE as per request
const USE_MOCK_API = false;

// Import configuration from config.json
const config = require('../../../components/taiwan/payment/methods/711/config.json');
const { tw: { tw711: { card, ibon } } } = config;

// Log configuration values for debugging
logApi('CONFIG_LOAD', {
  merchantID: card.merchantID,
  liveUrl: card.liveUrl,
  ibon: {
    merchantID: ibon.merchantID,
    hasPassword: !!ibon.password,
    hasApiPassword: !!ibon.apiPassword,
  }
});

// Update configuration values
const MERCHANT_ID = card.merchantID;
const API_PASSWORD = card.merchantPassword;
const HASH_BASE = card.hashBase;
const LINK_ID = card.linkID;
const TEST_URL = card.testUrl;
const LIVE_URL = card.liveUrl;
const RETURN_URL = card.returnURL;
const NOTIFY_URL = card.notifyURL;
const MIN_AMOUNT = card.limits.minAmount;
const MAX_AMOUNT = card.limits.maxAmount;
const EXPIRY_HOURS = card.limits.expiryHours;
const FEE_AMOUNT = card.fee.amount;
const FEE_CURRENCY = card.fee.currency;
const API_VERSION = card.apiSpecifications.version;
const HASH_ALGORITHM = card.security.hashAlgorithm;
const HASH_FORMAT = card.security.hashFormat;
const TIMEOUT = card.security.timeout;
const CURRENCY = card.localization.currency;
const LANGUAGE = card.localization.language;
const DATE_FORMAT = card.localization.dateFormat;

// IBON specific configuration
const IBON_MERCHANT_ID = ibon.merchantID;
const IBON_PASSWORD = ibon.password;
const IBON_API_PASSWORD = ibon.apiPassword;
const IBON_TEST_URL = ibon.testUrl;
const IBON_RETURN_URL = ibon.returnURL;
const IBON_CLIENT_BACK_URL = ibon.clientBackURL;
const IBON_PAYMENT_INFO_URL = ibon.paymentInfoURL;
const IBON_CLIENT_REDIRECT_URL = ibon.clientRedirectURL;
const IBON_NOTIFY_URL = ibon.notifyURL;
const IBON_CUSTOMER_URL = ibon.customerURL;
const IBON_LANGUAGE = ibon.language;
const IBON_VERSION = ibon.version;
const IBON_RESPOND_TYPE = ibon.respondType;

// --- Mock Data ---
const generateMockSuccessResponse = (merchantTradeNo, totalAmount, expireDateString) => ({
  status: 'OK',
  msg: 'Mock Success',
  cust_order_no: merchantTradeNo,
  order_amount: totalAmount,
  expire_date: expireDateString,
  ibon_code: `MOCK${Date.now() % 100000}`,
  ibon_shopid: 'MOCKID',
  virtual_account: '',
  // Generate plausible mock barcode strings (adjust length/format if needed)
  st_barcode1: `M1${(Math.random() * 1e8).toFixed(0).padStart(8, '0')}`,
  st_barcode2: `M2${(Math.random() * 1e15).toFixed(0).padStart(15, '0')}`,
  st_barcode3: `M3${(Math.random() * 1e15).toFixed(0).padStart(15, '0')}`,
  bill_amount: totalAmount + 35, // Example fee
  cs_fee: 35, // Example fee
  cvs_acquirer_type: '2', // Assuming Anyuan
  short_url: `https://mock.url/Q/${merchantTradeNo.slice(-6)}`,
  print_invoice: '0',
  vehicle_type: '',
  vehicle_barcode: '',
  donate_invoice: '0',
  love_code: '',
});
// --- End Mock Data ---

// Helper function to get Bearer Token
async function getAuthToken() {
  logApi('AUTH_TOKEN_START', { 
    using_mock: USE_MOCK_API,
    config: {
      merchantID: MERCHANT_ID,
      tokenUrl: `${LIVE_URL}/Token`,
      passwordLength: API_PASSWORD ? API_PASSWORD.length : 0,
      ibonPasswordLength: IBON_PASSWORD ? IBON_PASSWORD.length : 0,
      ibonApiPasswordLength: IBON_API_PASSWORD ? IBON_API_PASSWORD.length : 0,
      timeout: 15000
    }
  });
  
  // --- MOCK Check --- (keeping the logic but setting USE_MOCK_API to false)
  if (USE_MOCK_API) {
    logApi('AUTH_TOKEN_MOCK', 'Using mock token');
    return 'mock-bearer-token-' + Date.now();
  }
  // --- End MOCK Check ---

  try {
    const tokenUrl = `${LIVE_URL}/Token`;
    
    // Try with IBON_PASSWORD first (this seems to be the correct one based on config)
    const requestBody = `grant_type=password&username=${MERCHANT_ID}&password=${IBON_PASSWORD}`;
    
    logApi('AUTH_TOKEN_REQUEST_DETAILS', { 
      url: tokenUrl,
      requestBodyFormat: "grant_type=password&username={MERCHANT_ID}&password={IBON_PASSWORD}",
      actualMerchantID: MERCHANT_ID,
      tryingWith: "IBON_PASSWORD (W7529992P$)",
      ibonPasswordProvided: !!IBON_PASSWORD,
      ibonPasswordLength: IBON_PASSWORD ? IBON_PASSWORD.length : 0,
      baseUrl: LIVE_URL,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
    
    try {
      const response = await axios.post(tokenUrl, requestBody, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 15000
      });
      
      if (response.data && response.data.access_token) {
        logApi('AUTH_TOKEN_SUCCESS_WITH_IBON_PASSWORD', { 
          tokenPreview: response.data.access_token.substring(0, 5) + '...',
          tokenLength: response.data.access_token.length,
          tokenType: response.data.token_type,
          expiresIn: response.data.expires_in
        });
        return response.data.access_token;
      } else {
        logApi('AUTH_TOKEN_FAILURE_WITH_IBON_PASSWORD', {
          status: response.status,
          statusText: response.statusText,
          responseDateLength: JSON.stringify(response.data).length,
          responseData: response.data
        });
        // Fall through to try with API_PASSWORD
      }
    } catch (ibon_error) {
      logApi('AUTH_TOKEN_ERROR_WITH_IBON_PASSWORD', { 
        name: ibon_error.name,
        message: ibon_error.message,
        isAxiosError: ibon_error.isAxiosError,
        status: ibon_error.response?.status,
        statusText: ibon_error.response?.statusText,
        data: ibon_error.response?.data,
      });
      // Fall through to try with API_PASSWORD
    }
    
    // If IBON_PASSWORD didn't work, try with API_PASSWORD
    const apiPasswordRequestBody = `grant_type=password&username=${MERCHANT_ID}&password=${API_PASSWORD}`;
    
    logApi('AUTH_TOKEN_REQUEST_DETAILS_FALLBACK', { 
      url: tokenUrl,
      requestBodyFormat: "grant_type=password&username={MERCHANT_ID}&password={API_PASSWORD}",
      actualMerchantID: MERCHANT_ID,
      tryingWith: "API_PASSWORD",
      apiPasswordProvided: !!API_PASSWORD,
      apiPasswordLength: API_PASSWORD ? API_PASSWORD.length : 0,
      baseUrl: LIVE_URL,
    });
    
    const apiResponse = await axios.post(tokenUrl, apiPasswordRequestBody, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      timeout: 15000
    });
    
    if (apiResponse.data && apiResponse.data.access_token) {
      logApi('AUTH_TOKEN_SUCCESS_WITH_API_PASSWORD', { 
        tokenPreview: apiResponse.data.access_token.substring(0, 5) + '...',
        tokenLength: apiResponse.data.access_token.length,
        tokenType: apiResponse.data.token_type,
        expiresIn: apiResponse.data.expires_in
      });
      return apiResponse.data.access_token;
    } else {
      logApi('AUTH_TOKEN_FAILURE_WITH_API_PASSWORD', {
        status: apiResponse.status,
        statusText: apiResponse.statusText,
        responseDateLength: JSON.stringify(apiResponse.data).length,
        responseData: apiResponse.data
      });
      throw new Error('Failed to obtain authentication token: No token in response');
    }
  } catch (error) {
    logApi('AUTH_TOKEN_ERROR', { 
      name: error.name,
      message: error.message,
      isAxiosError: error.isAxiosError,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * API endpoint for generating 7-11 payment barcodes using CvsOrderAppend2
 * @param {NextApiRequest} req
 * @param {NextApiResponse} res
 */
export default async function handler(req, res) {
  const requestStart = Date.now();
  logApi('REQUEST_RECEIVED', { 
    method: req.method, 
    url: req.url,
    headers: req.headers,
    query: req.query,
    body: req.body,
    mockEnabled: USE_MOCK_API
  });

  // Log configuration values for easier debugging
  logApi('CONFIG_VALUES', {
    merchantID: MERCHANT_ID,
    ibonMerchantID: IBON_MERCHANT_ID,
    liveUrl: LIVE_URL,
    ibonTestUrl: IBON_TEST_URL,
    hasPassword: !!API_PASSWORD,
    hasIbonPassword: !!IBON_PASSWORD,
    hasIbonApiPassword: !!IBON_API_PASSWORD,
    linkID: LINK_ID,
    hasHashBase: !!HASH_BASE,
    minAmount: MIN_AMOUNT,
    maxAmount: MAX_AMOUNT,
    feeAmount: FEE_AMOUNT,
    currency: CURRENCY
  });

  // Standard CORS Headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*'); // TODO: Restrict in production
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    logApi('OPTIONS_REQUEST', 'Responding to preflight request');
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    logApi('METHOD_NOT_ALLOWED', { method: req.method });
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  let token;
  let orderPayload; // Define here to use in mock response generation

  try {
    logApi('PARSING_REQUEST_BODY', { rawBody: req.body });
    
    // --- Prepare Order Creation Payload (Do this before token potentially) ---
    const {
      MerchantTradeNo, // Should be unique, e.g., cust_order_no
      TotalAmount,     // order_amount
      TradeDesc,       // Optional description, map to order_detail
      ItemName,        // Optional description, map to order_detail
      CustomerName,    // payer_name
      CustomerPhone,   // payer_mobile
      CustomerEmail,   // payer_email
      StoreExpireMinutes // Use minutes for clarity, default 10 for ibon
      // Ignore StoreExpireDate if using minutes
    } = req.body;

    // Log the parsed fields
    logApi('PARSED_REQUEST_FIELDS', {
      MerchantTradeNo,
      TotalAmount,
      TradeDesc,
      ItemName,
      CustomerName,
      CustomerPhone,
      CustomerEmail,
      StoreExpireMinutes
    });

    // Detailed validation with field-by-field checks
    const validationIssues = [];
    
    // Check each required field
    if (!MerchantTradeNo) validationIssues.push('Missing MerchantTradeNo');
    else if (MerchantTradeNo.length > 29) validationIssues.push(`MerchantTradeNo too long: ${MerchantTradeNo.length} chars (max 29)`);
    
    if (!TotalAmount) validationIssues.push('Missing TotalAmount');
    else {
      const parsedAmount = parseInt(TotalAmount, 10);
      if (isNaN(parsedAmount)) validationIssues.push(`TotalAmount not a number: ${TotalAmount}`);
      else if (parsedAmount < MIN_AMOUNT) validationIssues.push(`TotalAmount ${parsedAmount} below minimum ${MIN_AMOUNT}`);
      else if (parsedAmount > MAX_AMOUNT) validationIssues.push(`TotalAmount ${parsedAmount} above maximum ${MAX_AMOUNT}`);
    }
    
    if (!CustomerName) validationIssues.push('Missing CustomerName');
    else if (CustomerName.match(/[^\x00-\x7F]/)) validationIssues.push('CustomerName contains non-ASCII characters');
    
    if (!CustomerPhone) validationIssues.push('Missing CustomerPhone');
    else if (!CustomerPhone.match(/^\d{8,15}$/)) validationIssues.push(`CustomerPhone format invalid: ${CustomerPhone}`);
    
    if (!CustomerEmail) validationIssues.push('Missing CustomerEmail');
    else if (!CustomerEmail.includes('@')) validationIssues.push(`CustomerEmail format invalid: ${CustomerEmail}`);

    // If validation issues found, return 400 with details
    if (validationIssues.length > 0) {
      logApi('VALIDATION_FAILED', { validationIssues });
      return res.status(400).json({
        success: false,
        message: `Validation failed: ${validationIssues.join(', ')}`,
        validationIssues
      });
    }
    
    logApi('VALIDATION_PASSED', 'All required fields present and validated');

    // Calculate expiry date (e.g., 7 days from now)
    const now = new Date();
    const expiryDate = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days expiry
    const expireDateString = expiryDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    
    logApi('EXPIRY_DATE_CALCULATED', {
      now: now.toISOString(),
      expiryDate: expiryDate.toISOString(),
      expireDateString
    });

    // Sanitize order_detail - Basic ASCII and simpler text
    const simpleTradeDesc = TradeDesc ? TradeDesc.replace(/[^\x00-\x7F]/g, "") : 'Website Order';
    const simpleItemName = ItemName ? ItemName.replace(/[^\x00-\x7F]/g, "") : 'Items';
    const sanitizedOrderDetail = `${simpleTradeDesc} / ${simpleItemName}`.substring(0, 500);
    
    logApi('SANITIZED_ORDER_DETAIL', {
      original: { TradeDesc, ItemName },
      sanitized: { simpleTradeDesc, simpleItemName, sanitizedOrderDetail }
    });

    // Ensure MerchantTradeNo doesn't exceed 30 characters (7-11 API limit)
    const safeOrderNo = MerchantTradeNo.substring(0, 29);
    if (safeOrderNo !== MerchantTradeNo) {
      logApi('ORDER_NO_TRUNCATED', {
        original: MerchantTradeNo,
        truncated: safeOrderNo,
        originalLength: MerchantTradeNo.length
      });
    }
    
    // Construct payload with detailed logging
    const amountInt = parseInt(TotalAmount, 10);
    logApi('AMOUNT_CONVERSION', {
      original: TotalAmount,
      parsed: amountInt,
      originalType: typeof TotalAmount
    });
    
    orderPayload = {
      cmd: "CvsOrderAppend2",
      cust_id: MERCHANT_ID,
      cust_order_no: safeOrderNo,
      order_amount: amountInt,
      expire_date: expireDateString,
      payment_type: "0",
      payer_name: CustomerName,
      payer_mobile: CustomerPhone,
      payer_email: CustomerEmail,
      order_detail: sanitizedOrderDetail,
    };
    
    logApi('ORDER_PAYLOAD_CONSTRUCTED', orderPayload);

    // --- MOCK Check ---
    /* if (USE_MOCK_API) {
        console.log('[7-11 API] MOCK MODE: Returning mock success response.');
        const mockResponseData = generateMockSuccessResponse(MerchantTradeNo, amountInt, expireDateString);
         // Simulate slight delay
        await new Promise(resolve => setTimeout(resolve, 500));
        // Directly structure the success response as expected by the frontend
        return res.status(200).json({
            success: true,
            message: 'Payment barcodes generated successfully (Mock).',
            data: {
                barcodes: {
                    barcode1: mockResponseData.st_barcode1,
                    barcode2: mockResponseData.st_barcode2,
                    barcode3: mockResponseData.st_barcode3,
                    expiresInMinutes: 10080
                },
                ibonShopId: mockResponseData.ibon_shopid,
                ibonPaymentCode: mockResponseData.ibon_code,
                expireDate: mockResponseData.expire_date,
                merchantTradeNo: mockResponseData.cust_order_no,
                tradeAmt: mockResponseData.order_amount,
                billAmount: mockResponseData.bill_amount,
                fee: mockResponseData.cs_fee,
                paymentType: 'IBON_REALTIME_MOCK',
                shortUrl: mockResponseData.short_url
            }
        });
    } */
    // --- End MOCK Check ---

    // --- Get Authentication Token ---
    logApi('AUTHENTICATION_START', { 
      merchantID: MERCHANT_ID,
      tokenEndpoint: `${LIVE_URL}/Token`,
      apiPasswordProvided: !!API_PASSWORD,
      ibonPasswordProvided: !!IBON_PASSWORD
    });
    
    try {
      token = await getAuthToken();
      logApi('AUTHENTICATION_COMPLETE', { 
        tokenReceived: !!token, 
        tokenLength: token ? token.length : 0,
        tokenFirstChars: token ? token.substring(0, 5) + '...' : 'NO_TOKEN'
      });
    } catch (authError) {
      logApi('AUTHENTICATION_FAILED', { 
        message: authError.message,
        stack: authError.stack,
        responseData: authError.response?.data,
        responseStatus: authError.response?.status,
        configValues: {
          merchantID: MERCHANT_ID,
          apiPasswordProvided: !!API_PASSWORD,
          apiPasswordLength: API_PASSWORD ? API_PASSWORD.length : 0,
          liveUrl: LIVE_URL,
          tokenEndpoint: `${LIVE_URL}/Token`
        }
      });
      throw new Error(`Authentication failed: ${authError.message}`);
    }

    console.log('[7-11 API] Calling Create Order endpoint (CvsOrderAppend2) with payload:', JSON.stringify(orderPayload));

    // --- Call Create Order Endpoint ---
    const createOrderUrl = `${LIVE_URL}/api/Collect`;
    logApi('CREATE_ORDER_REQUEST', { 
      url: createOrderUrl,
      payload: orderPayload,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token ? token.substring(0, 5) + '...' : 'MISSING_TOKEN'}`
      },
      merchantID: MERCHANT_ID,
      requestPath: '/api/Collect'
    });
    
    let apiStartTime = Date.now();
    try {
      const response = await axios.post(createOrderUrl, orderPayload, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        timeout: 30000
      });
      
      let apiDuration = Date.now() - apiStartTime;
      logApi('CREATE_ORDER_RESPONSE', {
        duration: `${apiDuration}ms`,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      });
  
      // --- 4. Process Real Response ---
      if (response.data && response.data.status === 'OK') {
        logApi('RESPONSE_STATUS_OK', 'Processing successful response');
        
        const barcode1 = response.data.st_barcode1;
        const barcode2 = response.data.st_barcode2;
        const barcode3 = response.data.st_barcode3;
        
        logApi('BARCODES_RECEIVED', {
          barcode1: barcode1 ? `${barcode1.substring(0, 3)}...` : 'MISSING',
          barcode2: barcode2 ? `${barcode2.substring(0, 3)}...` : 'MISSING',
          barcode3: barcode3 ? `${barcode3.substring(0, 3)}...` : 'MISSING',
          allPresent: barcode1 && barcode2 && barcode3
        });
  
        if (barcode1 && barcode2 && barcode3) {
          const paymentData = {
             barcodes: {
                barcode1: barcode1,
                barcode2: barcode2,
                barcode3: barcode3,
                expiresInMinutes: 10080 // 7 days validity (10080 minutes)
             },
             ibonShopId: response.data.ibon_shopid || '',
             ibonPaymentCode: response.data.ibon_code || '',
             expireDate: response.data.expire_date || expireDateString,
             merchantTradeNo: response.data.cust_order_no || MerchantTradeNo,
             tradeAmt: response.data.order_amount || amountInt,
             billAmount: response.data.bill_amount || '',
             fee: response.data.cs_fee || 0,
             paymentType: 'IBON_REALTIME',
             shortUrl: response.data.short_url || ''
          };
          logApi('SUCCESS_RESPONSE_READY', { 
            responseTime: `${Date.now() - requestStart}ms`,
            paymentDataKeys: Object.keys(paymentData),
            barcodesSample: {
              barcode1: barcode1.substring(0, 5) + '...',
              barcode2: barcode2.substring(0, 5) + '...',
              barcode3: barcode3.substring(0, 5) + '...'
            }
          });
          
          return res.status(200).json({ success: true, message: 'Payment barcodes generated successfully.', data: paymentData });
        } else {
          logApi('MISSING_BARCODE_DATA', { responseData: response.data });
          throw new Error('Payment provider response missing required barcode data.');
        }
      } else {
        const errorMessage = response.data?.msg || 'Failed to create payment order.';
        logApi('RESPONSE_STATUS_NOT_OK', { 
          status: response.data?.status,
          errorMessage,
          responseData: response.data
        });
        throw new Error(errorMessage);
      }
    } catch (apiError) {
      let apiDuration = Date.now() - apiStartTime;
      logApi('CREATE_ORDER_API_ERROR', {
        duration: `${apiDuration}ms`,
        name: apiError.name,
        message: apiError.message,
        isAxiosError: apiError.isAxiosError,
        response: apiError.response?.data,
        status: apiError.response?.status
      });
      throw apiError; // Re-throw to be caught by outer handler
    }
  } catch (error) {
    const errorTime = Date.now() - requestStart;
    logApi('ERROR_CAUGHT', { 
      errorTime: `${errorTime}ms`,
      name: error.name,
      message: error.message,
      stack: error.stack?.split('\n').slice(0, 5).join('\n'),
      isAxiosError: error.isAxiosError
    });
    
    let statusCode = 500;
    let message = error.message || 'Error generating payment code';
    let errorDetails = error.message;

    if (axios.isAxiosError(error)) {
      logApi('AXIOS_ERROR_DETAILS', {
        message: error.message,
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        data: error.response?.data,
        headers: error.response?.headers,
        requestData: error.config?.data
      });
      
      message = error.response?.data?.msg || error.message || message;
      errorDetails = JSON.stringify(error.response?.data || error.message);
      statusCode = error.response?.status || 500;
    }

    // Specifically handle the "T-ibon data exchange exception"
    if (message.includes('T-ibon資料交換異常') || message.includes('T-ibon data exchange exception')) {
        message = "Payment provider system (ibon) is temporarily unavailable. Please try again later.";
        // Keep statusCode 500 or maybe use 503 Service Unavailable? 500 is probably fine.
    }

    // If authentication failed
    if (message.includes('Authentication failed') || message.includes('Failed to obtain authentication token')) {
      statusCode = 401;
      message = "Payment gateway authentication failed. Please try again later or contact support.";
    }
    
    const responseTime = Date.now() - requestStart;
    logApi('SENDING_ERROR_RESPONSE', { 
      responseTime: `${responseTime}ms`,
      statusCode, 
      message,
      errorDetails: errorDetails.substring(0, 500) // Limit long error details
    });

    return res.status(statusCode).json({
      success: false,
      message: message,
      error: errorDetails
    });
  } finally {
    const totalTime = Date.now() - requestStart;
    logApi('REQUEST_COMPLETE', { 
      totalProcessingTime: `${totalTime}ms`
    });
  }
} 