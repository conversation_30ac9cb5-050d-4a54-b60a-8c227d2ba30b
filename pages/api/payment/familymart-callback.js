/**
 * FamilyMart Payment Notification Callback Endpoint
 * 
 * This API handles payment notifications from the FamilyMart payment system.
 * It updates order statuses in the orders.json file and appends callback logs 
 * directly to each order for centralized payment lifecycle tracking.
 * 
 * Based on FamilyMart documentation v3.04
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// Path to orders.json file
const ORDERS_FILE_PATH = path.join(process.cwd(), 'data', 'orders.json');

console.log('[FAMILYMART_CALLBACK_INIT] Current working directory:', process.cwd());
console.log('[FAMILYMART_CALLBACK_INIT] Orders file path:', ORDERS_FILE_PATH);

// FamilyMart merchant credentials
// These should match the values you provide to FamilyMart
const MERCHANT_ID = process.env.FAMILYMART_MERCHANT_ID || 'MAGSHOP_TEST';
const MERCHANT_SECRET = process.env.FAMILYMART_MERCHANT_SECRET || 'Mw9p7QxR6aT2sZ8e';

// Create callback log entry for appending to order
function createCallbackLogEntry(requestData, responseData, startTime, orderRef = null) {
  // Calculate processing time
  const endTime = Date.now();
  const processingTime = endTime - startTime;
  
  return {
    timestamp: new Date().toISOString(),
    paymentMethod: 'familymart',
    callbackType: 'payment_notification',
    requestPayload: requestData,
    responseStatus: responseData.status,
    responseBody: responseData.body,
    responseHeaders: responseData.headers || {},
    processingTimeMs: processingTime,
    clientIP: responseData.clientIP || 'unknown',
    userAgent: responseData.userAgent || 'unknown',
    orderFound: !!orderRef
  };
}

// Verify authentication
function verifyAuthentication(ecId, securityToken) {
  // Check if the EC_ID matches our merchant ID
  if (ecId !== MERCHANT_ID) {
    console.error(`[FAMIPORT_CALLBACK] Invalid merchant ID: ${ecId}`);
    return false;
  }

  // If security token is provided, validate it
  if (securityToken) {
    const calculatedToken = crypto
      .createHash('md5')
      .update(`${MERCHANT_ID}|${MERCHANT_SECRET}`)
      .digest('hex')
      .toUpperCase();
    
    if (securityToken !== calculatedToken) {
      console.error(`[FAMIPORT_CALLBACK] Invalid security token`);
      return false;
    }
  }

  return true;
}

// Verify PIN_CODE format
function verifyPinCodeFormat(pinCode) {
  // PIN_CODE is typically 10-12 characters
  return pinCode && pinCode.length >= 10 && pinCode.length <= 12;
}

// Load orders from file
function loadOrders() {
  try {
    const data = fs.readFileSync(ORDERS_FILE_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`[FAMIPORT_CALLBACK] Error loading orders: ${error.message}`);
    return null;
  }
}

// Save orders to file
function saveOrders(orders) {
  try {
    fs.writeFileSync(ORDERS_FILE_PATH, JSON.stringify(orders, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error(`[FAMIPORT_CALLBACK] Error saving orders: ${error.message}`);
    return false;
  }
}

// Update order status and append callback log
function updateOrderWithCallback(order, statusCode, requestPayload, callbackLog) {
  // Based on FamilyMart documentation, typical status codes are:
  // 0 = Success
  // 1 = Pending/Waiting for payment
  // 2 = Expired
  // 3 = Cancelled
  
  switch (String(statusCode)) {
    case '0': // Payment success
      order.status = 'processing';
      order.paymentStatus = 'paid';
      break;
    case '1': // Pending/Waiting for payment
      order.status = 'pending';
      order.paymentStatus = 'waiting_payment';
      break;
    case '2': // Expired
      order.status = 'cancelled';
      order.paymentStatus = 'expired';
      order.isExpired = true;
      order.expiryReason = 'payment_expired';
      break;
    case '3': // Cancelled
      order.status = 'cancelled';
      order.paymentStatus = 'cancelled';
      break;
    default:
      order.status = 'pending';
      order.paymentStatus = 'unknown';
  }
  
  // Add FamilyMart specific payment details
  order.paymentInfo = order.paymentInfo || {};
  order.paymentInfo.pinCode = requestPayload.PIN_CODE;
  order.paymentInfo.paymentNo = requestPayload.PAYMENT_NO;
  order.paymentInfo.barcodes = {
    barcode1: requestPayload.BARCODE1,
    barcode2: requestPayload.BARCODE2,
    barcode3: requestPayload.BARCODE3
  };
  order.paymentInfo.storeId = requestPayload.STORE_ID;
  order.paymentInfo.paymentDate = requestPayload.PAYMENT_DATE;
  order.paymentInfo.orderDate = requestPayload.ORDER_DATE;
  order.paymentSubMethod = 'family_mart';
  
  // Initialize payment callbacks array if not exists
  if (!order.paymentCallbacks) {
    order.paymentCallbacks = [];
  }
  
  // Append the callback log to the order
  order.paymentCallbacks.push(callbackLog);
  
  // Update timestamp
  order.updatedAt = new Date().toISOString();
  
  return order;
}

// Helper function to format success response
function formatSuccessResponse() {
  return '<?xml version="1.0" encoding="utf-8"?>' +
    '<EC_RESPONSE>' +
    '<HEADER><RESULT_CODE>0</RESULT_CODE><RESULT_MSG>Success</RESULT_MSG></HEADER>' +
    '<BODY></BODY>' +
    '</EC_RESPONSE>';
}

// Helper function to format error response
function formatErrorResponse(code, message) {
  return '<?xml version="1.0" encoding="utf-8"?>' +
    '<EC_RESPONSE>' +
    `<HEADER><RESULT_CODE>${code}</RESULT_CODE><RESULT_MSG>${message}</RESULT_MSG></HEADER>` +
    '<BODY></BODY>' +
    '</EC_RESPONSE>';
}

export default async function handler(req, res) {
  // Record start time for performance logging
  const startTime = Date.now();
  
  // Extract client info for logging
  const clientIP = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const userAgent = req.headers['user-agent'] || 'unknown';
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    const responseData = {
      status: 405,
      body: 'Method Not Allowed',
      headers: { 'Allow': 'POST' },
      clientIP,
      userAgent
    };
    // For method errors, we can't link to an order, so just log the attempt
    console.log('[FAMILYMART_CALLBACK] Method not allowed:', JSON.stringify(createCallbackLogEntry({ method: req.method }, responseData, startTime)));
    return res.status(405).end('Method Not Allowed');
  }
  
  try {
    // Log the FamilyMart callback payload
    console.log('[FAMIPORT_CALLBACK] FamilyMart payment notification received:', req.body);
    
    // Extract fields from the callback
    // Based on FamilyMart documentation
    const {
      EC_ID,            // Merchant ID
      PIN_CODE,         // Payment PIN code (payment receipt number)
      ORDER_NO,         // Order number
      ORDER_DATE,       // Order date
      AMOUNT,           // Payment amount
      STATUS_CODE,      // Payment status code
      PAYMENT_NO,       // FamilyMart payment number
      PAYMENT_DATE,     // Payment date
      STORE_ID,         // Store ID where payment was made
      BARCODE1,         // Barcode 1 (if available)
      BARCODE2,         // Barcode 2 (if available)
      BARCODE3,         // Barcode 3 (if available)
      SECURITY_TOKEN    // Optional security token
    } = req.body;
    
    // Validate required fields
    if (!PIN_CODE || !ORDER_NO || !AMOUNT || !STATUS_CODE || !EC_ID) {
      const responseData = {
        status: 400,
        body: formatErrorResponse('1001', 'Missing required fields'),
        headers: { 'Content-Type': 'text/xml' },
        clientIP,
        userAgent
      };
      // Log validation error
      console.log('[FAMILYMART_CALLBACK] Validation error:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error('[FAMIPORT_CALLBACK] Missing required fields');
      res.setHeader('Content-Type', 'text/xml');
      return res.status(400).end(formatErrorResponse('1001', 'Missing required fields'));
    }
    
    // Verify merchant authentication
    if (!verifyAuthentication(EC_ID, SECURITY_TOKEN)) {
      const responseData = {
        status: 401,
        body: formatErrorResponse('1002', 'Authentication failed'),
        headers: { 'Content-Type': 'text/xml' },
        clientIP,
        userAgent
      };
      // Log authentication error
      console.log('[FAMILYMART_CALLBACK] Authentication error:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error('[FAMIPORT_CALLBACK] Authentication failed');
      res.setHeader('Content-Type', 'text/xml');
      return res.status(401).end(formatErrorResponse('1002', 'Authentication failed'));
    }
    
    // Basic validation - verify PIN_CODE format
    if (!verifyPinCodeFormat(PIN_CODE)) {
      const responseData = {
        status: 400,
        body: formatErrorResponse('1003', 'Invalid PIN_CODE format'),
        headers: { 'Content-Type': 'text/xml' },
        clientIP,
        userAgent
      };
      // Log PIN validation error
      console.log('[FAMILYMART_CALLBACK] PIN validation error:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error('[FAMIPORT_CALLBACK] Invalid PIN_CODE format');
      res.setHeader('Content-Type', 'text/xml');
      return res.status(400).end(formatErrorResponse('1003', 'Invalid PIN_CODE format'));
    }
    
    // Load orders from file
    const orders = loadOrders();
    
    if (!orders) {
      const responseData = {
        status: 500,
        body: formatErrorResponse('9001', 'Failed to load orders'),
        headers: { 'Content-Type': 'text/xml' },
        clientIP,
        userAgent
      };
      // Log system error
      console.log('[FAMILYMART_CALLBACK] System error:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error('[FAMIPORT_CALLBACK] Failed to load orders');
      res.setHeader('Content-Type', 'text/xml');
      return res.status(500).end(formatErrorResponse('9001', 'Failed to load orders'));
    }
    
    // Find the order by PIN_CODE or ORDER_NO
    const orderIndex = orders.findIndex(o => 
      (o.paymentInfo && o.paymentInfo.pinCode === PIN_CODE) || 
              o.id === ORDER_NO
    );
    
    if (orderIndex === -1) {
      const responseData = {
        status: 404,
        body: formatErrorResponse('1004', 'Order not found'),
        headers: { 'Content-Type': 'text/xml' },
        clientIP,
        userAgent
      };
      // Log order not found error
      console.log('[FAMILYMART_CALLBACK] Order not found:', JSON.stringify(createCallbackLogEntry(req.body, responseData, startTime)));
      console.error(`[FAMIPORT_CALLBACK] Order not found: PIN_CODE=${PIN_CODE}, ORDER_NO=${ORDER_NO}`);
      res.setHeader('Content-Type', 'text/xml');
      return res.status(404).end(formatErrorResponse('1004', 'Order not found'));
    }
    
    // Create successful response data for logging
    const responseData = {
      status: 200,
      body: formatSuccessResponse(),
      headers: { 'Content-Type': 'text/xml' },
      clientIP,
      userAgent
    };
    
    // Create callback log entry
    const callbackLog = createCallbackLogEntry(req.body, responseData, startTime, orders[orderIndex]);
    
    // Update order status and append callback log
    const updatedOrder = updateOrderWithCallback(orders[orderIndex], STATUS_CODE, req.body, callbackLog);
    orders[orderIndex] = updatedOrder;
    
    // Save updated orders
    const saved = saveOrders(orders);
    
    if (!saved) {
      const errorResponseData = {
        status: 500,
        body: formatErrorResponse('9002', 'Failed to save orders'),
        headers: { 'Content-Type': 'text/xml' },
        clientIP,
        userAgent
      };
      console.log('[FAMILYMART_CALLBACK] Save error:', JSON.stringify(createCallbackLogEntry(req.body, errorResponseData, startTime, orders[orderIndex])));
      console.error('[FAMIPORT_CALLBACK] Failed to save orders');
      res.setHeader('Content-Type', 'text/xml');
      return res.status(500).end(formatErrorResponse('9002', 'Failed to save orders'));
    }
    
    console.log(`[FAMIPORT_CALLBACK] Successfully updated order ${ORDER_NO} to status ${STATUS_CODE} and logged callback`);
    
    // Return OK response with XML format as expected by FamilyMart
    res.setHeader('Content-Type', 'text/xml');
    return res.status(200).end(formatSuccessResponse());
  } catch (error) {
    console.error('[FAMIPORT_CALLBACK] Error processing FamilyMart notification:', error);
    
    const responseData = {
      status: 500,
      body: formatErrorResponse('9999', 'Internal Server Error'),
      headers: { 'Content-Type': 'text/xml' },
      error: error.message,
      clientIP,
      userAgent
    };
    console.log('[FAMILYMART_CALLBACK] Exception error:', JSON.stringify(createCallbackLogEntry(req.body || {}, responseData, startTime)));
    
    // Return error in XML format as expected by FamilyMart
    res.setHeader('Content-Type', 'text/xml');
    return res.status(500).end(formatErrorResponse('9999', 'Internal Server Error'));
  }
} 