import fs from 'fs';
import path from 'path';

const PAYMENT_STATUS_FILE = path.join(process.cwd(), 'data', 'payment-status.json');

// Ensure data directory exists
const ensureDataDirectory = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Read payment status data
const readPaymentStatusData = () => {
  try {
    ensureDataDirectory();
    if (fs.existsSync(PAYMENT_STATUS_FILE)) {
      const data = fs.readFileSync(PAYMENT_STATUS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return {};
  } catch (error) {
    console.error('Error reading payment status data:', error);
    return {};
  }
};

// Write payment status data
const writePaymentStatusData = (data) => {
  try {
    ensureDataDirectory();
    fs.writeFileSync(PAYMENT_STATUS_FILE, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing payment status data:', error);
    return false;
  }
};

export default function handler(req, res) {
  if (req.method === 'POST') {
    try {
      const paymentRecord = req.body;
      
      // Validate required fields
      if (!paymentRecord.orderId || !paymentRecord.paymentMethod || !paymentRecord.status) {
        return res.status(400).json({ 
          error: 'Missing required fields: orderId, paymentMethod, status' 
        });
      }

      // Read existing data
      const existingData = readPaymentStatusData();
      
      // Add or update payment record
      existingData[paymentRecord.orderId] = {
        ...paymentRecord,
        serverTimestamp: new Date().toISOString()
      };

      // Write updated data
      const writeSuccess = writePaymentStatusData(existingData);
      
      if (writeSuccess) {
        console.log(`[PAYMENT_API] Payment status saved for order ${paymentRecord.orderId}`);
        res.status(200).json({ 
          success: true, 
          message: 'Payment status saved successfully',
          orderId: paymentRecord.orderId
        });
      } else {
        res.status(500).json({ error: 'Failed to save payment status' });
      }
      
    } catch (error) {
      console.error('[PAYMENT_API] Error handling payment status:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else if (req.method === 'GET') {
    try {
      const { orderId } = req.query;
      
      if (orderId) {
        // Get specific order payment status
        const data = readPaymentStatusData();
        const paymentStatus = data[orderId] || null;
        
        res.status(200).json({ 
          success: true, 
          paymentStatus 
        });
      } else {
        // Get all payment statuses (for admin use)
        const data = readPaymentStatusData();
        res.status(200).json({ 
          success: true, 
          paymentStatuses: data 
        });
      }
    } catch (error) {
      console.error('[PAYMENT_API] Error retrieving payment status:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
} 