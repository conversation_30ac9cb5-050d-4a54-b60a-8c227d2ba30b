import crypto from 'crypto';
import axios from 'axios';

// Import configuration from config.json
const config = require('../../../components/taiwan/payment/methods/FamilyMart/config.json');

// Use direct access to config values instead of destructuring
const MERCHANT_ID = config.api.credentials.merchant_id;
const TEST_MODE = config.api.credentials.test_mode;
const API_ENDPOINT = config.api.credentials.api_endpoint;
const MIN_AMOUNT = config.payment.limits.min_amount;
const MAX_AMOUNT = config.payment.limits.max_amount;
const EXPIRY_HOURS = config.payment.limits.expiry_hours;
const FEE_AMOUNT = config.payment.fee.amount;
const FEE_CURRENCY = config.payment.fee.currency;
const HASH_ALGORITHM = config.security.hash_algorithm;
const TIMEOUT = config.security.timeout;
const CURRENCY = config.localization.currency;

// Generate a hash for FamilyMart API authentication
const generateHash = (data) => {
  // Use SHA256 as default fallback
  const algorithm = HASH_ALGORITHM || 'sha256';
  return crypto.createHash(algorithm).update(data).digest('hex').toUpperCase();
};

// Helper function to get Bearer Token
async function getAuthToken() {
  try {
    const tokenUrl = `${API_ENDPOINT}/Token`;
    const requestBody = `grant_type=password&username=${MERCHANT_ID}&password=${config.api.credentials.api_key}`;
    console.log('[FAMILYMART API] Requesting Token from:', tokenUrl);
    const response = await axios.post(tokenUrl, requestBody, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      timeout: TIMEOUT || 15000
    });
    if (response.data && response.data.access_token) {
      console.log('[FAMILYMART API] Successfully obtained Token.');
      return response.data.access_token;
    } else {
      console.error('[FAMILYMART API] Failed to get token, response:', response.data);
      throw new Error('Failed to obtain authentication token.');
    }
  } catch (error) {
    console.error('[FAMILYMART API] Error obtaining token:', error.response?.data || error.message);
    throw error;
  }
}

// Mock data generation for testing without real API access
const generateMockSuccessResponse = (merchantTradeNo, totalAmount, expireDateString) => ({
  RtnCode: '1',
  RtnMsg: 'Success (Mock)',
  TradeNo: `FM${Date.now()}`,
  PaymentNo: `PM${(Math.random() * 1e8).toFixed(0).padStart(8, '0')}`,
  Barcode1: `B1${(Math.random() * 1e8).toFixed(0).padStart(8, '0')}`,
  Barcode2: `B2${(Math.random() * 1e15).toFixed(0).padStart(15, '0')}`,
  Barcode3: `B3${(Math.random() * 1e15).toFixed(0).padStart(15, '0')}`,
  ExpireDate: expireDateString
});

/**
 * API endpoint for generating FamilyMart payment barcodes
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
export default async function handler(req, res) {
  // Standard CORS Headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Prepare Order Creation Payload
    const {
      MerchantTradeNo,
      TotalAmount,
      TradeDesc,
      ItemName,
      CustomerName,
      CustomerPhone,
      CustomerEmail,
      FamilyMartStore,
      StoreCode,
      StoreExpireMinutes = 60 * 24 * 7 // Default 7 days
    } = req.body;

    // Basic validation
    if (!MerchantTradeNo || !TotalAmount || !CustomerName || !CustomerPhone || !FamilyMartStore || !StoreCode) {
      console.error('[FAMILYMART API] Missing required fields from frontend request:', req.body);
      return res.status(400).json({
        success: false,
        message: 'Missing required fields (MerchantTradeNo, TotalAmount, CustomerName, CustomerPhone, FamilyMartStore, StoreCode)'
      });
    }

    // Calculate expiry date (e.g., 7 days from now)
    const now = new Date();
    const expiryDate = new Date(now.getTime() + StoreExpireMinutes * 60 * 1000);
    const expireDateString = expiryDate.toISOString().split('T')[0]; // YYYY-MM-DD format

    // Sanitize order_detail - Basic ASCII and simpler text
    const simpleTradeDesc = TradeDesc ? TradeDesc.replace(/[^\x00-\x7F]/g, "") : 'Website Order';
    const simpleItemName = ItemName ? ItemName.replace(/[^\x00-\x7F]/g, "") : 'Items';
    const sanitizedOrderDetail = `${simpleTradeDesc} / ${simpleItemName}`.substring(0, 500);

    // Ensure MerchantTradeNo doesn't exceed 30 characters
    const safeOrderNo = MerchantTradeNo.substring(0, 29);
    
    // Construct payload
    const amountInt = parseInt(TotalAmount, 10); // Ensure integer
    const orderPayload = {
      MerchantID: MERCHANT_ID,
      MerchantTradeNo: safeOrderNo,
      MerchantTradeDate: now.toISOString(),
      PaymentType: 'CVS',
      TotalAmount: amountInt,
      TradeDesc: sanitizedOrderDetail,
      ItemName: simpleItemName,
      ReturnURL: `${process.env.NEXT_PUBLIC_API_URL || ''}/api/payment/family-mart-callback`,
      CustomerName: CustomerName,
      CustomerPhone: CustomerPhone,
      CustomerEmail: CustomerEmail || '',
      StoreInfo: FamilyMartStore,
      StoreCode: StoreCode,
      ExpireDate: expireDateString,
      PaymentInfoURL: `${process.env.NEXT_PUBLIC_API_URL || ''}/api/payment/family-mart-notification`,
      ClientBackURL: `${process.env.NEXT_PUBLIC_API_URL || ''}/orders`
    };

    // For Hash generation
    const checkData = Object.entries(orderPayload)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    const checkMacValue = generateHash(checkData);
    orderPayload.CheckMacValue = checkMacValue;

    // Use mock data for development
    // const USE_MOCK_DATA = process.env.USE_MOCK_FAMILYMART_API === 'true' || TEST_MODE;
    const USE_MOCK_DATA = TEST_MODE;
    
    if (USE_MOCK_DATA) {
      // Simulate slight delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockResponse = generateMockSuccessResponse(
        safeOrderNo, 
        amountInt, 
        expireDateString
      );
      
      return res.status(200).json({
        success: true,
        message: 'Payment barcodes generated successfully (Mock).',
        data: {
          PaymentNo: mockResponse.PaymentNo,
          Barcode: [mockResponse.Barcode1, mockResponse.Barcode2, mockResponse.Barcode3].join(','),
          TradeNo: mockResponse.TradeNo,
          ExpireDate: mockResponse.ExpireDate
        }
      });
    }

    // In production, use real API
    console.log('[FAMILYMART API] Sending payment request:', orderPayload);
    
    // Get authentication token
    let token;
    try {
      token = await getAuthToken();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Failed to authenticate with FamilyMart API',
        error: error.message
      });
    }

    // Call the FamilyMart API
    const apiResponse = await axios.post(API_ENDPOINT, orderPayload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      timeout: TIMEOUT || 30000
    });

    console.log('[FAMILYMART API] Response received:', apiResponse.data);

    if (apiResponse.data && apiResponse.data.RtnCode === '1') {
      return res.status(200).json({
        success: true,
        message: 'Payment barcodes generated successfully.',
        data: {
          PaymentNo: apiResponse.data.PaymentNo,
          Barcode: [
            apiResponse.data.Barcode1, 
            apiResponse.data.Barcode2, 
            apiResponse.data.Barcode3
          ].join(','),
          TradeNo: apiResponse.data.TradeNo,
          ExpireDate: apiResponse.data.ExpireDate
        }
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'FamilyMart API returned an error',
        error: apiResponse.data.RtnMsg || 'Unknown error'
      });
    }
  } catch (error) {
    console.error('[FAMILYMART API] Error processing payment:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while processing the payment',
      error: error.message
    });
  }
} 