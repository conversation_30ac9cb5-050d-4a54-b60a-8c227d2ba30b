import axios from 'axios';
import crypto from 'crypto';
import path from 'path';
import fs from 'fs';

// Load SINOPAC config
const configPath = path.join(process.cwd(), 'config', 'sinopac.json');
const SINOPAC_API = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// Official SinoPAC Sandbox Environment:
// Portal: https://sandbox.sinopac.com/DSF.Portal/ (admin/admin)
// Encryption Test: https://sandbox.sinopac.com/QPay.ApiClient/Calc/Encrypt
// Decryption Test: https://sandbox.sinopac.com/QPay.ApiClient/Calc/Descrypt

// Helper function to convert string to hex bytes array
const strToHexBytes = (string) => {
  const hex = [];
  for (let i = 0; i < string.length; i += 2) {
    hex.push(parseInt(string.substr(i, 2), 16));
  }
  return hex;
};

// Helper function to convert hex bytes array to string
const hexBytesToString = (hex) => {
  let result = '';
  for (let i = 0; i < hex.length; i++) {
    let str = hex[i].toString(16);
    if (str.length < 2) {
      str = '0' + str;
    }
    result += str;
  }
  return result.toUpperCase();
};

// XOR calculation method
const setXOR = (byte1, byte2) => {
  const result = [];
  for (let i = 0; i < byte1.length; i++) {
    result[i] = byte1[i] ^ byte2[i];
  }
  return result;
};

// Get HashID calculation method (following PHP sample exactly)
const getHashID = (hash) => {
  const Byte_A1 = strToHexBytes(hash.A1);
  const Byte_A2 = strToHexBytes(hash.A2);
  const Byte_B1 = strToHexBytes(hash.B1);
  const Byte_B2 = strToHexBytes(hash.B2);
  
  const XOR1 = setXOR(Byte_A1, Byte_A2);
  const XOR2 = setXOR(Byte_B1, Byte_B2);
  
  const result = hexBytesToString(XOR1) + hexBytesToString(XOR2);
  return result;
};

// Get IV calculation method
const getIV = (nonce) => {
  const data = crypto.createHash('sha256').update(nonce).digest('hex').toUpperCase();
  return data.substr(data.length - 32, 32); // Take last 32 characters (16 bytes in hex)
};

// SHA256 hash function
const SHA256 = (data) => {
  return crypto.createHash('sha256').update(data).digest('hex').toUpperCase();
};

// Get Sign calculation method (following PHP sample exactly)
const getSign = (data, nonce, hashid) => {
  // Remove null values and filter only first-level variables
  const filteredData = {};
  Object.keys(data).forEach(key => {
    if (data[key] !== null && data[key] !== undefined && data[key] !== '' && typeof data[key] !== 'object') {
      filteredData[key] = data[key];
    }
  });
  
  // Sort fields in ascending order
  const sortedKeys = Object.keys(filteredData).sort();
  
  // Build content string
  let content = '';
  sortedKeys.forEach(key => {
    content += key + '=' + filteredData[key] + '&';
  });
  content = content.slice(0, -1); // Remove last &
  
  // Add nonce and hashid
  content += nonce + hashid;
  
  // SHA256 hash
  return SHA256(content);
};

// AES CBC encryption (following PHP openssl_encrypt format)
const encryptAesCBC = (data, key, iv) => {
  try {
    // Convert key and iv to Buffer
    const keyBuffer = Buffer.from(key, 'hex');
    const ivBuffer = Buffer.from(iv, 'hex');
    
    // Use AES-128-CBC for 16-byte key, AES-256-CBC for 32-byte key
    const cipherMode = keyBuffer.length === 16 ? 'aes-128-cbc' : 'aes-256-cbc';
    const cipher = crypto.createCipheriv(cipherMode, keyBuffer, ivBuffer);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return encrypted.toUpperCase();
  } catch (error) {
    console.error('AES encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

// Get Nonce from SinoPAC API
const getNonce = async (shopNo, targetUrl) => {
  try {
    const url = `${targetUrl}/Nonce`;
    const postData = { ShopNo: shopNo };
    
    console.log('Getting nonce from:', url);
    console.log('Nonce request data:', postData);
    
    const response = await axios.post(url, postData, {
      headers: {
        'Content-Type': 'application/json',
        'X-KeyID': SINOPAC_API.X_KEY
      },
      timeout: 30000
    });
    
    console.log('Nonce response:', response.data);
    return response.data.Nonce;
  } catch (error) {
    console.error('Error getting nonce:', error.response?.data || error.message);
    throw new Error('Failed to get nonce from SinoPAC API');
  }
};

// Main API service function (following PHP sample structure)
const APIService = async (service, objService, targetUrl) => {
  try {
    const url = `${targetUrl}/Order`;
    
    // Get Nonce value
    console.log('Step 1: Getting nonce...');
    const nonce = await getNonce(SINOPAC_API.MERCHANT_ID, targetUrl);
    
    // Get HashID
    console.log('Step 2: Calculating HashID...');
    const hashID = getHashID(SINOPAC_API.HASH);
    console.log('HashID:', hashID);
    
    // Get IV
    console.log('Step 3: Calculating IV...');
    const iv = getIV(nonce);
    console.log('IV:', iv);
    
    // Get Sign
    console.log('Step 4: Calculating Sign...');
    const sign = getSign(objService, nonce, hashID);
    console.log('Sign:', sign);
    
    // Remove null values from objService
    const filteredObjService = {};
    Object.keys(objService).forEach(key => {
      if (objService[key] !== null && objService[key] !== undefined && objService[key] !== '') {
        filteredObjService[key] = objService[key];
      }
    });
    
    console.log('Filtered service object:', filteredObjService);
    
    // Encrypt message content
    console.log('Step 5: Encrypting message...');
    const message = encryptAesCBC(JSON.stringify(filteredObjService), hashID, iv);
    console.log('Encrypted message:', message);
    
    // Build request object (following PHP API class structure)
    const request = {
      Version: '1.0.0',
      ShopNo: SINOPAC_API.MERCHANT_ID,
      APIService: service,
      Sign: sign,
      Nonce: nonce,
      Message: message
    };
    
    console.log('Final request:', request);
    
    // Make API call
    console.log('Step 6: Making API call to:', url);
    const response = await axios.post(url, request, {
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'X-KeyID': SINOPAC_API.X_KEY
      },
      timeout: 30000
    });
    
    console.log('API response:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('APIService error:', error.response?.data || error.message);
    throw error;
  }
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Validate required fields
    const requiredFields = ['OrderNo', 'Amount', 'PrdtName'];
    const missingFields = requiredFields.filter(field => !req.body[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({
        message: 'Missing required fields',
        fields: missingFields
      });
    }

    // Validate amount
    if (isNaN(req.body.Amount) || req.body.Amount <= 0) {
      return res.status(400).json({
        message: 'Invalid amount',
        amount: req.body.Amount
      });
    }

    // Get the API endpoint based on environment
    const apiEndpoint = SINOPAC_API.TEST_MODE ? 
      SINOPAC_API.SANDBOX_ENDPOINT : 
      SINOPAC_API.PRODUCTION_ENDPOINT;

    console.log('Using API endpoint:', apiEndpoint);

    // Mock mode for development
    if (SINOPAC_API.MOCK_MODE || req.query.mock === 'true') {
      console.log('=== SINOPAC MOCK MODE ACTIVE ===');
      
      const mockResponse = {
        Status: "S",
        Message: "Mock payment order created successfully",
        Result: {
          OrderNo: req.body.OrderNo,
          Amount: req.body.Amount,
          ATMInfo: {
            BankCode: "008",
            AccountNo: "****************",
          },
          QRCode: "mock_qr_code_data",
          PaymentURL: `http://localhost:3000/mock-payment/${req.body.OrderNo}`,
          ExpireDate: "********",
          TSResultCode: "00",
          TSResultMsg: "Mock success"
        }
      };
      
      return res.status(200).json(mockResponse);
    }

    // Create the service object following PHP OrderCreate class structure
    const orderService = {
      ShopNo: SINOPAC_API.MERCHANT_ID,
      OrderNo: req.body.OrderNo,
      Amount: Math.round(req.body.Amount).toString(), // Convert to string like PHP sample
      CurrencyID: 'TWD',
      PrdtName: req.body.PrdtName.substring(0, 50),
      Memo: (req.body.Memo || "Online order").substring(0, 100),
      Param1: '',
      Param2: '',
      Param3: '',
      ReturnURL: req.body.ReturnURL || `${req.headers.origin}/api/payment/sinopac-callback`,
      BackendURL: `${req.headers.origin}/api/payment/sinopac-callback`,
      PayType: req.body.PayType || 'A' // A = ATM, C = Credit Card, M = Mobile
    };

    // Add payment type specific parameters
    if (orderService.PayType === 'A') {
      // ATM payment - add expiry date (10 days from now)
      const expireDate = new Date();
      expireDate.setDate(expireDate.getDate() + 10);
      const expireDateStr = expireDate.getFullYear() + 
        String(expireDate.getMonth() + 1).padStart(2, '0') + 
        String(expireDate.getDate()).padStart(2, '0');
      
      // Properly structure ATMParam as object field following PHP sample
      orderService['ATMParam.ExpireDate'] = expireDateStr;
    } else if (orderService.PayType === 'C') {
      // Credit card payment
      orderService['CardParam.AutoBilling'] = 'Y';
    } else if (orderService.PayType === 'M') {
      // Mobile payment
      orderService['MobileParam.ExpMinutes'] = '10';
    }

    console.log('Order service object:', orderService);

    // Call APIService function
    const response = await APIService('OrderCreate', orderService, apiEndpoint);
    
    console.log('SinoPAC API response:', response);
    return res.status(200).json(response);
    
  } catch (error) {
    console.error('SinoPAC API Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });

    // Return appropriate error response
    if (error.response) {
      return res.status(error.response.status).json({
        message: 'Error from SinoPAC API',
        error: error.response.data
      });
    } else if (error.request) {
      return res.status(500).json({
        message: 'No response from SinoPAC API',
        error: 'Network error or timeout'
      });
    } else {
      return res.status(500).json({
        message: 'Error processing payment request',
        error: error.message
      });
    }
  }
}