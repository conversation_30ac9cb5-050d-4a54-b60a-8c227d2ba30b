import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import config from '../../../../components/taiwan/payment/methods/711/config.json';

// Helper for logging
const log711CardNotify = (message, data = null) => {
  console.log(`[7-11 CARD NOTIFY API] ${message}`, data ? JSON.stringify(data) : '');
};

// Helper to read JSON file
const readJsonFile = (filePath) => {
  const fullPath = path.join(process.cwd(), filePath);
  const fileContents = fs.readFileSync(fullPath, 'utf8');
  return JSON.parse(fileContents);
};

// Helper to write JSON file
const writeJsonFile = (filePath, data) => {
  const fullPath = path.join(process.cwd(), filePath);
  fs.writeFileSync(fullPath, JSON.stringify(data, null, 2), 'utf8');
};

// Helper to append to notification log file
const appendNotification = (notification) => {
  try {
    const logDir = path.join(process.cwd(), 'data/logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    const logFile = path.join(logDir, 'payment_notifications.json');
    let notifications = [];
    
    if (fs.existsSync(logFile)) {
      const fileContent = fs.readFileSync(logFile, 'utf8');
      if (fileContent.trim()) {
        notifications = JSON.parse(fileContent);
      }
    }
    
    notifications.push(notification);
    fs.writeFileSync(logFile, JSON.stringify(notifications, null, 2), 'utf8');
  } catch (error) {
    console.error('Error writing notification log:', error);
  }
};

export default async function handler(req, res) {
  // The 7-11 payment system might use either POST or GET for notifications
  if (req.method !== 'POST' && req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // Get the notification data from either POST body or GET query parameters
  const notifyData = req.method === 'POST' ? req.body : req.query;
  
  log711CardNotify('Received payment notification', {
    method: req.method,
    data: notifyData,
    headers: req.headers
  });

  try {
    // Extract key parameters
    const { 
      order_id: orderId, 
      result_code: resultCode,
      status,
      hash,
      timestamp,
      amount
    } = notifyData;

    if (!orderId) {
      log711CardNotify('Missing order ID in notification', notifyData);
      return res.status(400).json({ 
        success: false, 
        error: 'Order ID is required' 
      });
    }

    // Verify the hash if provided
    if (hash) {
      const { tw: { tw711: { card } } } = config;
      const MERCHANT_ID = card.merchantID;
      const HASH_BASE = card.hashBase;
      const HASH_ALGORITHM = card.security.hashAlgorithm;
      const HASH_FORMAT = card.security.hashFormat;

      // Create verification data string (may need adjustment based on 7-11's actual format)
      const verificationData = `${orderId}|${amount || ''}|${timestamp || ''}`;
      
      // Generate the hash for verification
      const hashString = HASH_FORMAT
        .replace('${cust_id}', MERCHANT_ID)
        .replace('${data}', verificationData)
        .replace('${hash_base}', HASH_BASE);
      
      const calculatedHash = crypto.createHash(HASH_ALGORITHM).update(hashString).digest('hex');
      
      log711CardNotify('Hash verification', {
        provided: hash,
        calculated: calculatedHash,
        match: hash === calculatedHash
      });

      // If hashes don't match, it might be a fraudulent request
      if (hash !== calculatedHash) {
        log711CardNotify('Hash verification failed', {
          providedHash: hash,
          calculatedHash
        });
        // Continue processing but log the warning
        // Some payment providers don't require strict hash verification for notifications
      }
    }

    // Store the raw notification
    const notification = {
      orderId,
      type: '711_card',
      method: req.method,
      data: notifyData,
      headers: req.headers,
      receivedAt: new Date().toISOString()
    };
    appendNotification(notification);
    
    // Read orders from JSON file
    const orders = readJsonFile('data/orders.json');
    
    // Find the order in the JSON data
    const orderIndex = orders.findIndex(order => order.id === orderId);
    
    if (orderIndex === -1) {
      log711CardNotify(`Order not found: ${orderId}`);
      // Still return 200 to acknowledge receipt - this is important for most payment providers
      return res.status(200).json({ 
        success: false, 
        message: 'Notification received, but order not found' 
      });
    }

    const order = orders[orderIndex];

    log711CardNotify(`Found order ${orderId}`, { 
      storeId: order.storeId,
      currentStatus: order.status
    });

    // Determine payment status based on result code or status field
    let paymentStatus = 'unknown';
    if (resultCode === '0000' || status === 'success') {
      paymentStatus = 'success';
    } else if (resultCode === '1000' || status === 'pending') {
      paymentStatus = 'pending';
    } else {
      paymentStatus = 'failed';
    }

    // Update the order with notification data
    orders[orderIndex] = {
      ...order,
      status: paymentStatus,
      notificationReceived: true,
      paymentNotification: {
        resultCode,
        status,
        receivedAt: new Date().toISOString(),
        rawData: notifyData
      },
      updatedAt: new Date().toISOString()
    };

    // Write updated orders back to file
    writeJsonFile('data/orders.json', orders);

    log711CardNotify(`Updated order ${orderId} status to ${paymentStatus}`);

    // Always respond with 200 OK for payment notifications
    // This tells the payment provider we received their notification
    return res.status(200).json({
      success: true,
      message: 'Notification received and processed successfully'
    });

  } catch (error) {
    log711CardNotify('Error processing notification', {
      error: error.message,
      stack: error.stack
    });
    
    // Still return 200 to acknowledge receipt
    return res.status(200).json({
      success: false,
      message: 'Notification received but could not be processed',
      error: 'Internal server error'
    });
  }
} 