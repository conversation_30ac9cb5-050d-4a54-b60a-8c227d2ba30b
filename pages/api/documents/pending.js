import fs from 'fs';
import path from 'path';

export default function handler(req, res) {
  try {
    console.log('Fetching pending documents...');
    const customersFilePath = path.join(process.cwd(), 'data', 'customers.json');
    console.log('Customers file path:', customersFilePath);
    
    // Check if the customers file exists
    if (!fs.existsSync(customersFilePath)) {
      console.log('Customers file not found');
      return res.status(200).json({
        success: true,
        documents: []
      });
    }
    
    // Read the customers file
    const customers = JSON.parse(fs.readFileSync(customersFilePath, 'utf8'));
    console.log(`Found ${customers.length} customers`);
    
    const pendingDocuments = [];
    
    // Loop through each customer
    customers.forEach(customer => {
      const customerId = customer.id;
      
      // Skip if documents array doesn't exist
      if (!customer.documents || !Array.isArray(customer.documents)) {
        console.log(`No documents for customer ${customerId}`);
        return;
      }
      
      console.log(`Checking documents for customer ${customerId}`);
      
      // Process each document
      customer.documents.forEach(documentData => {
        // Check if the document is pending approval or doesn't have a status (default to pending)
        if (!documentData.status || documentData.status === 'pending_approval') {
          console.log(`Found pending document: ${documentData.filename}`);
          
          // Get customer name
          const customerName = customer.name || 
                             customer.personalDetails?.name || 
                             `${customer.personalDetails?.firstName || ''} ${customer.personalDetails?.lastName || ''}`.trim() || 
                             customerId;
          
          // Determine the best ID to use for the document
          const documentId = documentData.id || documentData.filename;
          
          // Add document to the pending list
          pendingDocuments.push({
            id: documentId,
            documentType: documentData.documentType,
            uploadDate: documentData.uploadDate,
            customerId,
            customerName,
            status: documentData.status || 'pending_approval',
            orderId: documentData.orderId,
            originalFilename: documentData.originalFilename
          });
        }
      });
    });
    
    console.log(`Found ${pendingDocuments.length} pending documents`);
    
    res.status(200).json({
      success: true,
      documents: pendingDocuments
    });
  } catch (error) {
    console.error('Error fetching pending documents:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch pending documents',
      message: error.message,
      stack: error.stack
    });
  }
} 