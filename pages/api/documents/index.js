import { 
  getAllDocuments, 
  getCustomerDocuments, 
  getOrderDocuments,
  addDocument
} from '../../../utils/orderUtils';

export default async function handler(req, res) {
  // GET - retrieve documents
  if (req.method === 'GET') {
    try {
      const { customerId, orderId } = req.query;
      
      // Return documents for a specific customer
      if (customerId) {
        const customerDocuments = getCustomerDocuments(customerId);
        return res.status(200).json({
          customerId,
          total: customerDocuments.length,
          documents: customerDocuments
        });
      }
      
      // Return documents for a specific order
      if (orderId) {
        const orderDocuments = getOrderDocuments(orderId);
        return res.status(200).json({
          orderId,
          total: orderDocuments.length,
          documents: orderDocuments
        });
      }
      
      // Return all documents
      const documents = getAllDocuments();
      return res.status(200).json({
        total: documents.length,
        documents
      });
    } catch (error) {
      console.error('Error retrieving documents:', error);
      return res.status(500).json({ 
        error: 'Error retrieving documents', 
        message: error.message 
      });
    }
  }
  
  // POST - add a new document reference to the centralized system
  if (req.method === 'POST') {
    try {
      const documentData = req.body;
      
      // Validate required fields
      if (!documentData.customerId) {
        return res.status(400).json({ 
          error: 'Customer ID is required' 
        });
      }
      
      if (!documentData.filename) {
        return res.status(400).json({ 
          error: 'Filename is required' 
        });
      }
      
      // Add the document to the central documents.json file
      const addedDocument = addDocument(documentData);
      
      return res.status(201).json(addedDocument);
    } catch (error) {
      console.error('Error adding document:', error);
      return res.status(500).json({ 
        error: 'Error adding document', 
        message: error.message 
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({ error: 'Method not allowed' });
} 