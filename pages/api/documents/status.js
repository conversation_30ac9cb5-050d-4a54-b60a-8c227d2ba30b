import fs from 'fs';
import path from 'path';
import { createRouter } from 'next-connect';

// Create API route handler
const router = createRouter({
  onError: (err, req, res) => {
    console.error('API error:', err);
    res.status(500).json({ success: false, error: err.message });
  },
  onNoMatch: (req, res) => {
    res.status(405).json({ success: false, error: `Method '${req.method}' not allowed` });
  },
});

// Path to customers data file
const CUSTOMERS_FILE = path.join(process.cwd(), 'data', 'customers.json');

// Helper function to parse document ID
function parseDocumentId(documentId) {
  // Format: customerId-filename
  const parts = documentId.split('-');
  if (parts.length < 2) {
    throw new Error('Invalid document ID format');
  }
  
  const customerId = parts[0];
  // Join remaining parts in case filename contains hyphens
  const filename = parts.slice(1).join('-');
  
  return { customerId, filename };
}

// POST handler to update document status
router.post(async (req, res) => {
  try {
    const { documentId, status, adminId } = req.body;
    
    // Validate required fields
    if (!documentId || !status || !adminId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }
    
    // Validate status value
    if (!['pending', 'approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status value'
      });
    }
    
    // Parse the document ID to get customer ID and filename
    let customerId, filename;
    try {
      const parsed = parseDocumentId(documentId);
      customerId = parsed.customerId;
      filename = parsed.filename;
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid document ID format'
      });
    }
    
    // Read customers data
    if (!fs.existsSync(CUSTOMERS_FILE)) {
      return res.status(404).json({
        success: false,
        error: 'Customers file not found'
      });
    }
    
    const customersData = fs.readFileSync(CUSTOMERS_FILE, 'utf8');
    const customers = JSON.parse(customersData);
    
    // Find the customer and update document status
    const customer = customers.find(c => c.id === customerId);
    if (!customer) {
      return res.status(404).json({
        success: false,
        error: `Customer not found with ID: ${customerId}`
      });
    }
    
    // Initialize documents array if it doesn't exist
    if (!customer.documents) {
      customer.documents = [];
    }
    
    // Find the document
    const documentIndex = customer.documents.findIndex(doc => doc.filename === filename);
    if (documentIndex === -1) {
      return res.status(404).json({
        success: false,
        error: `Document not found with filename: ${filename}`
      });
    }
    
    // Update document status
    customer.documents[documentIndex].status = status;
    customer.documents[documentIndex].statusUpdatedAt = new Date().toISOString();
    customer.documents[documentIndex].statusUpdatedBy = adminId;
    
    // Save the updated data
    fs.writeFileSync(CUSTOMERS_FILE, JSON.stringify(customers, null, 2));
    
    console.log(`Document status updated: ${customerId}/${filename} -> ${status}`);
    
    res.status(200).json({
      success: true,
      message: `Document status updated to ${status}`,
      document: {
        id: documentId,
        status,
        customerId,
        filename
      }
    });
  } catch (error) {
    console.error('Error updating document status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update document status'
    });
  }
});

// Export the handler
export default router.handler(); 