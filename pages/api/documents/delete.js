import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { store } = req.query;
    const { documentId, fileName, url } = req.body;

    if (!store) {
      return res.status(400).json({ 
        success: false, 
        message: 'Store ID is required' 
      });
    }

    if (!documentId && !fileName && !url) {
      return res.status(400).json({ 
        success: false, 
        message: 'Document identifier is required (documentId, fileName, or url)' 
      });
    }

    console.log('Delete request:', { store, documentId, fileName, url });

    // Try to determine the file path to delete
    let fileToDelete = null;
    
    if (url) {
      // Extract filename from URL
      if (url.includes('/api/customer/') && url.includes('/document/')) {
        const urlParts = url.split('/');
        const filenameFromUrl = urlParts[urlParts.length - 1];
        fileToDelete = filenameFromUrl;
      } else if (url.startsWith('/data/documents/customers/')) {
        const urlParts = url.split('/');
        const filenameFromUrl = urlParts[urlParts.length - 1];
        fileToDelete = filenameFromUrl;
      }
    }
    
    if (!fileToDelete && fileName) {
      fileToDelete = fileName;
    }

    // Delete the physical file if we found it
    if (fileToDelete) {
      const documentsDir = path.join(process.cwd(), 'data', 'documents', 'customers', store);
      const filePath = path.join(documentsDir, fileToDelete);
      
      if (fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          console.log('Successfully deleted file:', filePath);
        } catch (deleteError) {
          console.error('Error deleting file:', deleteError);
          // Continue with database cleanup even if file deletion fails
        }
      } else {
        console.log('File not found at:', filePath);
        // Continue to remove from customers.json even if physical file not found
      }
    }

    // Remove document reference from customers.json
    try {
      // Try multiple possible paths for customers.json
      const possiblePaths = [
        path.join(process.cwd(), 'data', 'customers.json'),
        path.join(process.cwd(), 'data', store, 'customers.json'),
        path.join(process.cwd(), 'customers.json')
      ];
      
      let customersFilePath = null;
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          customersFilePath = possiblePath;
          break;
        }
      }
      
      if (!customersFilePath) {
        console.log('customers.json not found in any of these locations:', possiblePaths);
        return res.status(200).json({
          success: true,
          message: 'Document deleted successfully (file removed, but customers.json not found)',
          deletedFile: fileToDelete
        });
      }
      
      console.log('Found customers.json at:', customersFilePath);
      if (fs.existsSync(customersFilePath)) {
        const customersData = JSON.parse(fs.readFileSync(customersFilePath, 'utf8'));
        let documentRemoved = false;
        
        console.log('Looking for document to delete:', fileToDelete);
        console.log('Total customers to check:', customersData.length);
        
        // Find and remove the document from customer records
        for (let i = 0; i < customersData.length; i++) {
          const customer = customersData[i];
          if (customer.documents && Array.isArray(customer.documents)) {
            console.log(`Customer ${i} has ${customer.documents.length} documents`);
            
            const originalLength = customer.documents.length;
            customer.documents = customer.documents.filter(doc => {
              const docFileName = doc.filename || doc.fileName || doc.url?.split('/').pop();
              console.log(`Comparing: ${docFileName} vs ${fileToDelete}`);
              return docFileName !== fileToDelete;
            });
            
            if (customer.documents.length < originalLength) {
              documentRemoved = true;
              console.log(`Removed document from customer ${i} (${customer.id || customer.phone})`);
            }
          } else {
            console.log(`Customer ${i} has no documents or documents is not an array`);
          }
        }
        
        if (documentRemoved) {
          fs.writeFileSync(customersFilePath, JSON.stringify(customersData, null, 2));
          console.log('Document reference removed from customers.json');
        } else {
          console.log('No document reference found to remove from customers.json');
        }
      } else {
        console.log('customers.json file not found at:', customersFilePath);
      }
    } catch (customerUpdateError) {
      console.error('Error updating customer records:', customerUpdateError);
      // Continue anyway, file was deleted
    }

    return res.status(200).json({
      success: true,
      message: 'Document deleted successfully',
      deletedFile: fileToDelete
    });

  } catch (error) {
    console.error('Error deleting document:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
} 