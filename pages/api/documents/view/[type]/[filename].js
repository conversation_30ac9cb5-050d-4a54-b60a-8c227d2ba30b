import { 
  documentExists, 
  getDocumentInfo, 
  readDocument,
  extractCustomerIdFromFilename
} from '../../../../../utils/documentStorage';
import { isCustomerAuthenticated, getCustomerInfo, getCustomerId } from '../../../../../utils/customerAuth';

/**
 * API handler for retrieving document files
 * 
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Extract parameters from the URL
    const { type, filename } = req.query;
    const { customerId } = req.query;  // Optional customer ID from query
    
    // Validate parameters
    if (!type || !filename) {
      return res.status(400).json({ error: 'Missing document type or filename' });
    }
    
    // Get customer ID from query, auth, or extract from filename
    let docCustomerId = customerId;
    
    if (!docCustomerId) {
      // Try to get from authenticated user
      docCustomerId = getCustomerId();
      
      // If still not available, try to extract from filename
      if (!docCustomerId) {
        docCustomerId = extractCustomerIdFromFilename(filename);
      }
    }
    
    if (!docCustomerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }
    
    // Verify that the document exists
    const exists = await documentExists(type, filename, docCustomerId);
    if (!exists) {
      return res.status(404).json({ error: 'Document not found' });
    }
    
    // Security check - Only allow access to documents belonging to the authenticated customer
    if (!isCustomerAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const customerInfo = getCustomerInfo();
    const authenticatedCustomerId = getCustomerId();
    
    // Check if the requested document belongs to the authenticated customer
    // or if the user has admin privileges (not implemented here)
    const isAdmin = req.query.adminAccess === 'true'; // This should use a more robust check
    const isAuthorizedAccess = isAdmin || authenticatedCustomerId === docCustomerId;
    
    if (!isAuthorizedAccess) {
      return res.status(403).json({ error: 'Forbidden - You can only access your own documents' });
    }
    
    // Get document info (size, MIME type)
    const { size, mimeType } = await getDocumentInfo(type, filename, docCustomerId);
    
    // Set appropriate headers
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', size);
    res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
    
    // Security headers
    res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    // Read the document and send it as the response
    const data = await readDocument(type, filename, docCustomerId);
    res.send(data);
    
  } catch (error) {
    console.error('Error serving document:', error);
    res.status(500).json({ error: 'Error serving document', message: error.message });
  }
}

/**
 * This function would check if the document belongs to the specified customer
 * In a real implementation, this would query a database
 */
async function checkDocumentOwnership(customerId, documentType, filename) {
  // Query database to check if the document belongs to the customer
  // This is just an example and should be replaced with actual implementation
  try {
    // Example query: SELECT * FROM customer_documents WHERE 
    //   customer_id = customerId AND document_type = documentType AND filename = filename
    
    // Return true if the document belongs to the customer, false otherwise
    return true; // Placeholder
  } catch (error) {
    console.error('Error checking document ownership:', error);
    return false;
  }
} 