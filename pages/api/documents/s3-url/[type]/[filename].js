import { 
  documentExists, 
  getDocumentUrl
} from '../../../../../utils/s3DocumentStorage';
import { isCustomerAuthenticated, getCustomerInfo } from '../../../../../utils/customerAuth';

/**
 * API handler for generating presigned URLs for S3 document access
 * 
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Extract parameters from the URL
    const { type, filename } = req.query;
    
    // Validate parameters
    if (!type || !filename) {
      return res.status(400).json({ error: 'Missing document type or filename' });
    }
    
    // Verify that the document exists in S3
    const exists = await documentExists(type, filename);
    if (!exists) {
      return res.status(404).json({ error: 'Document not found' });
    }
    
    // Security check - Only allow access to documents belonging to the authenticated customer
    if (!isCustomerAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const customerInfo = getCustomerInfo();
    
    // In a real implementation, you would check if the requested document belongs to the customer
    // For example, by querying a database or checking document metadata
    // This is a simplified example:
    /*
    const isCustomerDocument = await checkDocumentOwnership(customerInfo.id, type, filename);
    if (!isCustomerDocument) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    */
    
    // Generate a presigned URL (valid for 5 minutes)
    const url = await getDocumentUrl(type, filename, 300);
    
    // Return the URL in the response
    res.status(200).json({ url });
    
  } catch (error) {
    console.error('Error generating document URL:', error);
    res.status(500).json({ error: 'Error generating document URL' });
  }
}

/**
 * This function would check if the document belongs to the specified customer
 * In a real implementation, this would query a database
 */
async function checkDocumentOwnership(customerId, documentType, filename) {
  // Query database to check if the document belongs to the customer
  // This is just an example and should be replaced with actual implementation
  try {
    // Example query: SELECT * FROM customer_documents WHERE 
    //   customer_id = customerId AND document_type = documentType AND filename = filename
    
    // Return true if the document belongs to the customer, false otherwise
    return true; // Placeholder
  } catch (error) {
    console.error('Error checking document ownership:', error);
    return false;
  }
} 