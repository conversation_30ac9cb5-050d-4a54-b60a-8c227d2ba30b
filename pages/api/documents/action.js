import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { documentId, customerId, action, documentType, filename } = req.body;

    if (!customerId || !action || !documentType || !filename) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }

    // Load customers data
    const customersDataPath = path.join(process.cwd(), 'data', 'customers.json');
    
    if (!fs.existsSync(customersDataPath)) {
      return res.status(404).json({
        success: false,
        message: 'Customers data not found'
      });
    }

    const customersData = JSON.parse(fs.readFileSync(customersDataPath, 'utf8'));
    const customerIndex = customersData.findIndex(customer => customer.id === customerId);
    
    if (customerIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    const customer = customersData[customerIndex];
    customer.documents = customer.documents || [];

    // Find the document
    const documentIndex = customer.documents.findIndex(
      doc => doc.documentType === documentType && doc.filename === filename
    );

    if (documentIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    const document = customer.documents[documentIndex];

    switch (action) {
      case 'approve':
        document.status = 'approved';
        document.approvedDate = new Date().toISOString();
        break;
        
      case 'reject':
        document.status = 'rejected';
        document.rejectedDate = new Date().toISOString();
        break;
        
      case 'delete':
        // Remove document from array
        customer.documents.splice(documentIndex, 1);
        
        // Delete the physical file
        const filePath = path.join(process.cwd(), 'data', 'documents', 'customers', customerId, filename);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        break;
        
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    // Save updated customers data
    fs.writeFileSync(customersDataPath, JSON.stringify(customersData, null, 2));

    return res.status(200).json({
      success: true,
      message: `Document ${action}d successfully`,
      document: action === 'delete' ? null : document
    });

  } catch (error) {
    console.error('Error handling document action:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}