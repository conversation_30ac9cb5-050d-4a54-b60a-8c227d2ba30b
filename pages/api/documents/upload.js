import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Disable the default body parser to handle form data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  console.log('Document upload API called:', {
    method: req.method,
    query: req.query,
    headers: req.headers['content-type']
  });

  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get the store parameter from the query
    const { store } = req.query;
    
    console.log('Store parameter:', store);
    
    if (!store || typeof store !== 'string') {
      console.log('Store ID validation failed:', { store, type: typeof store });
      return res.status(400).json({ success: false, message: 'Store ID is required' });
    }

    // Parse the form data first to get the customerId
    const form = formidable({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB max file size
    });

    // Wait for the form to be parsed
    const [fields, files] = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve([fields, files]);
      });
    });

    // Get the uploaded file and fields
    const fileArray = files.file;
    const file = Array.isArray(fileArray) ? fileArray[0] : fileArray;
    const documentType = Array.isArray(fields.documentType) ? fields.documentType[0] : String(fields.documentType || '');
    const customerId = Array.isArray(fields.customerId) ? fields.customerId[0] : String(fields.customerId || '');
    const customerName = Array.isArray(fields.customerName) ? fields.customerName[0] : String(fields.customerName || '');
    const isUpdate = Array.isArray(fields.isUpdate) 
      ? fields.isUpdate[0] === 'true' 
      : String(fields.isUpdate || '') === 'true';

    console.log('Parsed form data:', {
      file: file ? { name: file.originalFilename, size: file.size, type: file.mimetype } : null,
      documentType,
      customerId,
      customerName,
      isUpdate
    });

    if (!file) {
      console.log('No file uploaded in request');
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    if (!documentType) {
      console.log('Document type missing');
      return res.status(400).json({ success: false, message: 'Document type is required' });
    }

    if (!customerId) {
      console.log('Customer ID missing');
      return res.status(400).json({ success: false, message: 'Customer ID is required' });
    }

    // Set up the upload directory in the data/documents/customers/[customerid] folder
    const uploadDir = path.join(process.cwd(), 'data', 'documents', 'customers', customerId);
    
    console.log('Upload directory path:', uploadDir);
    
    // Create the directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      console.log('Creating upload directory:', uploadDir);
      fs.mkdirSync(uploadDir, { recursive: true });
    } else {
      console.log('Upload directory already exists');
    }

    // Generate a unique filename
    const fileExtension = path.extname(file.originalFilename || '');
    const safeDocumentType = documentType.replace(/[^a-zA-Z0-9-_]/g, '_');
    const newFilename = `${safeDocumentType}_${Date.now()}_${uuidv4()}${fileExtension}`;
    const newFilePath = path.join(uploadDir, newFilename);

    console.log('Moving file from:', file.filepath, 'to:', newFilePath);
    
    // Rename the file to the unique filename and move it to the customer's directory
    try {
      fs.copyFileSync(file.filepath, newFilePath);
      fs.unlinkSync(file.filepath); // Remove the temporary file
      console.log('File moved successfully');
    } catch (fileError) {
      console.error('Error moving file:', fileError);
      throw new Error(`Failed to save file: ${fileError.message}`);
    }

    // Store relative path to the document for internal use
    const relativePath = path.join('documents', 'customers', customerId, newFilename).replace(/\\/g, '/');
    console.log('Relative path for document:', relativePath);

    // Fetch current customer data to update documents list
    let customerData;
    try {
      // In a real application, you would fetch the customer data from your database
      // For this example, we'll simulate by reading from a JSON file
      const customerDataPath = path.join(process.cwd(), 'data', 'customers.json');
      
      if (fs.existsSync(customerDataPath)) {
        const customersData = JSON.parse(fs.readFileSync(customerDataPath, 'utf8'));
        customerData = customersData.find((customer) => customer.id === customerId);
      }
      
      // Customer data will only come from customers.json
    } catch (error) {
      console.error('Error fetching customer data:', error);
    }

    // Add document to customer data or update existing document
    if (customerData) {
      customerData.documents = customerData.documents || [];
      
      const existingDocIndex = customerData.documents.findIndex(
        (doc) => doc.documentType === documentType
      );
      
      const newDoc = {
        documentType,
        filename: newFilename,
        originalFilename: file.originalFilename,
        uploadDate: new Date().toISOString(),
        size: file.size,
        status: 'pending_approval',
        path: relativePath,
      };
      
      if (existingDocIndex !== -1 && isUpdate) {
        // Update existing document
        customerData.documents[existingDocIndex] = {
          ...customerData.documents[existingDocIndex],
          ...newDoc,
        };
      } else {
        // Add new document
        customerData.documents.push(newDoc);
      }

      // Save the updated customer data
      try {
        // First try to update in the main customers.json file
        const customersDataPath = path.join(process.cwd(), 'data', 'customers.json');
        if (fs.existsSync(customersDataPath)) {
          const customersData = JSON.parse(fs.readFileSync(customersDataPath, 'utf8'));
          const customerIndex = customersData.findIndex((customer) => customer.id === customerId);
          
          if (customerIndex !== -1) {
            customersData[customerIndex] = customerData;
            fs.writeFileSync(customersDataPath, JSON.stringify(customersData, null, 2));
          }
        }
        
        // Only use customers.json - no individual profile files
      } catch (error) {
        console.error('Error updating customer data:', error);
      }
    }

    // Return success response with path to the file
    const responseData = {
      success: true,
      message: 'File uploaded successfully',
      filename: newFilename,
      originalFilename: file.originalFilename,
      size: file.size,
      path: relativePath,
      uploadDate: new Date().toISOString(),
      status: 'pending_approval'
    };
    
    console.log('Returning success response:', responseData);
    return res.status(200).json(responseData);
  } catch (error) {
    console.error('Error handling file upload:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
} 