import { documentExists, getDocumentInfo } from '../../../../../utils/documentStorage';
import { isCustomerAuthenticated, getCustomerInfo } from '../../../../../utils/customerAuth';

/**
 * API handler for retrieving document metadata
 * 
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Extract parameters from the URL
    const { type, filename } = req.query;
    
    // Validate parameters
    if (!type || !filename) {
      return res.status(400).json({ error: 'Missing document type or filename' });
    }
    
    // Verify that the document exists
    const exists = await documentExists(type, filename);
    if (!exists) {
      return res.status(404).json({ error: 'Document not found' });
    }
    
    // Security check - Only allow access to documents belonging to the authenticated customer
    if (!isCustomerAuthenticated()) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const customerInfo = getCustomerInfo();
    
    // In a real implementation, you would check if the requested document belongs to the customer
    // For example, by querying a database or checking document metadata
    
    // Get document info (size, MIME type)
    const { size, mimeType } = await getDocumentInfo(type, filename);
    
    // Return the document metadata
    res.status(200).json({
      documentType: type,
      filename,
      size,
      mimeType,
      viewUrl: `/api/documents/view/${type}/${filename}`
    });
    
  } catch (error) {
    console.error('Error retrieving document metadata:', error);
    res.status(500).json({ error: 'Error retrieving document metadata' });
  }
} 