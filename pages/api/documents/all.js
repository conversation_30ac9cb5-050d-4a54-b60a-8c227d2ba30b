import { createRouter } from 'next-connect';
import fs from 'fs';
import path from 'path';

// Path to customers data file
const CUSTOMERS_FILE = path.join(process.cwd(), 'data', 'customers.json');

// Create API route handler
const router = createRouter({
  onError: (err, req, res) => {
    console.error('API error:', err);
    res.status(500).json({ success: false, error: err.message });
  },
  onNoMatch: (req, res) => {
    res.status(405).json({ success: false, error: `Method '${req.method}' not allowed` });
  },
});

// GET handler to fetch all documents
router.get(async (req, res) => {
  try {
    // Check if customers file exists
    if (!fs.existsSync(CUSTOMERS_FILE)) {
      return res.status(404).json({
        success: false,
        error: 'Customers file not found'
      });
    }
    
    // Read customers data
    const customersData = fs.readFileSync(CUSTOMERS_FILE, 'utf8');
    const customers = JSON.parse(customersData);
    const documents = [];
    
    // Extract documents from all customers
    for (const customer of customers) {
      if (!customer.documents || !Array.isArray(customer.documents)) {
        continue;
      }
      
      // Add each document with customer information
      for (const doc of customer.documents) {
        documents.push({
          id: `${customer.id}-${doc.filename}`,
          customerId: customer.id,
          customerName: customer.personalDetails?.name || 
                        `${customer.personalDetails?.firstName || ''} ${customer.personalDetails?.lastName || ''}`,
          documentType: doc.documentType,
          filename: doc.filename,
          originalName: doc.originalFilename || doc.originalName,
          uploadDate: doc.uploadDate,
          status: doc.status || 'pending',
          statusUpdatedAt: doc.statusUpdatedAt,
          statusUpdatedBy: doc.statusUpdatedBy,
          orderId: doc.orderId
        });
      }
    }
    
    res.status(200).json({
      success: true,
      documents: documents
    });
  } catch (error) {
    console.error('Error fetching documents:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch documents'
    });
  }
});

// Export the handler
export default router.handler(); 