import fs from 'fs';
import path from 'path';

/**
 * API endpoint to fetch checkout logs
 * 
 * @param {Object} req - Next.js request object
 * @param {Object} res - Next.js response object
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get query parameters
    const { date, limit = 100 } = req.query;
    
    // Base directory for checkout logs
    const LOG_BASE_DIR = path.join(process.cwd(), 'data', 'logs', 'checkout');
    
    // If no date provided, return today's logs
    let logFilePath;
    
    if (date) {
      // Parse the provided date (expected format: YYYY-MM-DD)
      const [year, month, day] = date.split('-').map(part => part.trim());
      
      if (!year || !month || !day) {
        return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
      }
      
      logFilePath = path.join(LOG_BASE_DIR, year, month, `${date}.json`);
    } else {
      // Use today's date
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const today = `${year}-${month}-${day}`;
      
      logFilePath = path.join(LOG_BASE_DIR, String(year), month, `${today}.json`);
    }
    
    // Check if the log file exists
    if (!fs.existsSync(logFilePath)) {
      return res.status(404).json({ error: 'No logs found for the specified date' });
    }
    
    // Read the log file
    const logFileContent = fs.readFileSync(logFilePath, 'utf8');
    const logs = logFileContent ? JSON.parse(logFileContent) : [];
    
    // Apply limit if provided
    const limitedLogs = limit ? logs.slice(-parseInt(limit, 10)) : logs;
    
    // Return the logs
    return res.status(200).json({
      date: date || new Date().toISOString().split('T')[0],
      count: limitedLogs.length,
      totalEntries: logs.length,
      logs: limitedLogs
    });
  } catch (error) {
    console.error('Error fetching checkout logs:', error);
    return res.status(500).json({ error: 'Failed to fetch checkout logs' });
  }
} 