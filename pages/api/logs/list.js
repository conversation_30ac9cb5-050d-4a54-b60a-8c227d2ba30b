import fs from 'fs';
import path from 'path';

/**
 * API endpoint to list available checkout log dates
 * 
 * @param {Object} req - Next.js request object
 * @param {Object} res - Next.js response object
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Base directory for checkout logs
    const LOG_BASE_DIR = path.join(process.cwd(), 'data', 'logs', 'checkout');

    // Check if the logs directory exists
    if (!fs.existsSync(LOG_BASE_DIR)) {
      return res.status(404).json({ error: 'No logs directory found' });
    }

    // Get all available years
    const years = fs.readdirSync(LOG_BASE_DIR)
      .filter(item => fs.statSync(path.join(LOG_BASE_DIR, item)).isDirectory());

    // For each year, get all available months and days
    const availableDates = [];

    for (const year of years) {
      const yearPath = path.join(LOG_BASE_DIR, year);
      const months = fs.readdirSync(yearPath)
        .filter(item => fs.statSync(path.join(yearPath, item)).isDirectory());

      for (const month of months) {
        const monthPath = path.join(yearPath, month);
        const logFiles = fs.readdirSync(monthPath)
          .filter(file => file.endsWith('.json'));

        for (const logFile of logFiles) {
          // Extract date from filename (YYYY-MM-DD.json)
          const date = logFile.replace('.json', '');
          
          try {
            // Get file stats to determine size and entry count
            const filePath = path.join(monthPath, logFile);
            const stats = fs.statSync(filePath);
            
            // Read file to count entries
            const content = fs.readFileSync(filePath, 'utf8');
            const entries = content ? JSON.parse(content).length : 0;
            
            availableDates.push({
              date,
              size: stats.size,
              entries,
              path: filePath.replace(process.cwd(), '')
            });
          } catch (error) {
            // If there's an error reading the file, just add the date
            availableDates.push({
              date,
              error: 'Error reading file stats'
            });
          }
        }
      }
    }

    // Sort dates in descending order (newest first)
    availableDates.sort((a, b) => {
      return new Date(b.date) - new Date(a.date);
    });

    // Return the list of available dates
    return res.status(200).json({
      count: availableDates.length,
      dates: availableDates
    });
  } catch (error) {
    console.error('Error listing checkout logs:', error);
    return res.status(500).json({ error: 'Failed to list checkout logs', details: error.message });
  }
} 