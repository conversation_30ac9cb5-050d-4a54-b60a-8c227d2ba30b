import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  console.log('[CART_GET_ALL] Request received:', { method: req.method, query: req.query });
  
  // Only allow GET requests
  if (req.method !== 'GET') {
    console.log('[CART_GET_ALL] Method not allowed:', req.method);
    return res.status(405).json({ 
      success: false,
      message: 'Method not allowed' 
    });
  }

  try {
    const { userId } = req.query;
    console.log('[CART_GET_ALL] Request parameters:', { userId });

    // Validate required parameter
    if (!userId) {
      console.log('[CART_GET_ALL] Missing required parameter: userId');
      return res.status(400).json({ 
        success: false,
        message: 'User ID is required' 
      });
    }
    
    // Prepare file path
    const dataDir = path.join(process.cwd(), 'data', 'carts');
    const cartsFilePath = path.join(dataDir, 'carts.json');
    console.log('[CART_GET_ALL] File path:', cartsFilePath);
    
    // Check if the carts file exists
    if (!fs.existsSync(cartsFilePath)) {
      console.log('[CART_GET_ALL] Cart file does not exist:', cartsFilePath);
      return res.status(200).json({ 
        success: true,
        carts: {} 
      });
    }
    
    // Read carts data
    try {
      console.log('[CART_GET_ALL] Reading cart file');
      const fileData = fs.readFileSync(cartsFilePath, 'utf8');
      const cartsData = JSON.parse(fileData);
      
      // Find all carts for this user - using various matching patterns
      const userCarts = {};
      for (const [cartId, cartData] of Object.entries(cartsData.carts)) {
        // Match if:
        // 1. The cart's userId property matches the requested userId
        // 2. The cartId starts with the userId (e.g., "0912345678_magshop")
        // 3. The cartId includes "_userId_" somewhere in the middle
        // 4. The cartId includes "userId_" as a pattern
        if (cartData.userId === userId || 
            cartId.startsWith(userId) || 
            cartId.includes(`_${userId}_`) ||
            cartId.includes(`${userId}_`)) {
          userCarts[cartId] = cartData;
        }
      }
      
      console.log('[CART_GET_ALL] Found carts for user:', Object.keys(userCarts).length);
      
      return res.status(200).json({ 
        success: true,
        carts: userCarts
      });
    } catch (error) {
      console.error('[CART_GET_ALL] Error reading cart data:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Failed to read cart data'
      });
    }
  } catch (error) {
    console.error('[CART_GET_ALL] Error retrieving carts:', error);
    return res.status(500).json({ 
      success: false,
      message: 'Failed to retrieve carts'
    });
  }
} 