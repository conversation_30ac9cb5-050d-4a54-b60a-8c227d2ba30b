import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  console.log('[CART_SYNC] Request received:', { method: req.method });
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    console.log('[CART_SYNC] Method not allowed:', req.method);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, storeId, cart, numberOfItemsInCart, total } = req.body;
    console.log('[CART_SYNC] Request body received:', { userId, storeId, numberOfItemsInCart, total, cartItemCount: cart?.length });

    // Validate required fields
    if (!userId || !storeId || !cart || typeof numberOfItemsInCart !== 'number') {
      console.log('[CART_SYNC] Missing required fields:', { userId, storeId, hasCart: !!cart, numberOfItemsInCart });
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create a unique ID combining userId and storeId
    const cartId = `${userId}_${storeId}`;
    console.log('[CART_SYNC] Cart ID:', cartId);
    
    // Prepare directory and file paths
    const dataDir = path.join(process.cwd(), 'data', 'carts');
    const cartsFilePath = path.join(dataDir, 'carts.json');
    console.log('[CART_SYNC] File paths:', { dataDir, cartsFilePath });
    
    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      console.log('[CART_SYNC] Creating data directory:', dataDir);
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Read existing carts data
    let cartsData = { carts: {} };
    if (fs.existsSync(cartsFilePath)) {
      try {
        console.log('[CART_SYNC] Reading existing cart file');
        const fileData = fs.readFileSync(cartsFilePath, 'utf8');
        cartsData = JSON.parse(fileData);
        console.log('[CART_SYNC] Existing cart data loaded, cart count:', Object.keys(cartsData.carts).length);
      } catch (error) {
        console.error('[CART_SYNC] Error reading carts file:', error);
        // If error reading, start with empty carts data
        cartsData = { carts: {} };
      }
    } else {
      console.log('[CART_SYNC] Cart file does not exist, will create new file');
    }
    
    // Update or add the cart data
    cartsData.carts[cartId] = {
      userId,
      storeId,
      cart,
      numberOfItemsInCart,
      total,
      lastUpdated: new Date().toISOString()
    };
    console.log('[CART_SYNC] Updated cart data prepared with cartId:', cartId);
    
    // Write updated cart data back to file
    try {
      console.log('[CART_SYNC] Writing to file:', cartsFilePath);
      fs.writeFileSync(cartsFilePath, JSON.stringify(cartsData, null, 2));
      console.log('[CART_SYNC] File write successful');
    } catch (error) {
      console.error('[CART_SYNC] Error writing to file:', error);
      return res.status(500).json({ 
        success: false,
        error: 'Failed to write cart data to file'
      });
    }
    
    // Return success response
    console.log('[CART_SYNC] Request completed successfully');
    return res.status(200).json({ 
      success: true,
      message: 'Cart synced successfully'
    });
  } catch (error) {
    console.error('[CART_SYNC] Error syncing cart:', error);
    return res.status(500).json({ 
      success: false,
      error: 'Failed to sync cart'
    });
  }
} 