import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  console.log('[CART_GET] Request received:', { method: req.method, query: req.query });
  
  // Only allow GET requests
  if (req.method !== 'GET') {
    console.log('[CART_GET] Method not allowed:', req.method);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userId, storeId } = req.query;
    console.log('[CART_GET] Request parameters:', { userId, storeId });

    // Validate required parameters
    if (!userId || !storeId) {
      console.log('[CART_GET] Missing required parameters:', { userId, storeId });
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Create a unique ID combining userId and storeId
    const cartId = `${userId}_${storeId}`;
    console.log('[CART_GET] Cart ID:', cartId);
    
    // Prepare file path
    const dataDir = path.join(process.cwd(), 'data', 'carts');
    const cartsFilePath = path.join(dataDir, 'carts.json');
    console.log('[CART_GET] File paths:', { dataDir, cartsFilePath });
    
    // Check if the carts file exists
    if (!fs.existsSync(cartsFilePath)) {
      console.log('[CART_GET] Cart file does not exist:', cartsFilePath);
      return res.status(404).json({ 
        success: false,
        error: 'No cart data found' 
      });
    }
    
    // Read carts data
    try {
      console.log('[CART_GET] Reading cart file');
      const fileData = fs.readFileSync(cartsFilePath, 'utf8');
      const cartsData = JSON.parse(fileData);
      console.log('[CART_GET] Cart data loaded, available carts:', Object.keys(cartsData.carts));
      
      // Check if this specific cart exists
      if (!cartsData.carts[cartId]) {
        console.log('[CART_GET] Cart not found with ID:', cartId);
        return res.status(404).json({ 
          success: false,
          error: 'Cart not found' 
        });
      }
      
      // Return the cart data
      console.log('[CART_GET] Found cart for ID:', cartId);
      return res.status(200).json({ 
        success: true,
        data: cartsData.carts[cartId]
      });
      
    } catch (error) {
      console.error('[CART_GET] Error reading cart data:', error);
      return res.status(500).json({ 
        success: false,
        error: 'Failed to read cart data'
      });
    }
  } catch (error) {
    console.error('[CART_GET] Error retrieving cart:', error);
    return res.status(500).json({ 
      success: false,
      error: 'Failed to retrieve cart'
    });
  }
} 