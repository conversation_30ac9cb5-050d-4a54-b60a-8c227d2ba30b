import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  console.log('[CART_MERGE] Request received:', { method: req.method });
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    console.log('[CART_MERGE] Method not allowed:', req.method);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { customerId, previousId, storeId } = req.body;
    console.log('[CART_MERGE] Request body received:', { customerId, previousId, storeId });

    // Validate required fields
    if (!customerId || !previousId) {
      console.log('[CART_MERGE] Missing required fields:', { customerId, previousId });
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Prepare directory and file paths
    const dataDir = path.join(process.cwd(), 'data', 'carts');
    const cartsFilePath = path.join(dataDir, 'carts.json');
    console.log('[CART_MERGE] File paths:', { dataDir, cartsFilePath });
    
    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      console.log('[CART_MERGE] Creating data directory:', dataDir);
      fs.mkdirSync(dataDir, { recursive: true });
      return res.status(404).json({ 
        success: false, 
        message: 'No cart data found to merge' 
      });
    }
    
    // Read existing carts data
    if (!fs.existsSync(cartsFilePath)) {
      console.log('[CART_MERGE] Carts file does not exist:', cartsFilePath);
      return res.status(404).json({ 
        success: false, 
        message: 'No cart data found to merge' 
      });
    }
    
    // Read and parse carts file
    let cartsData;
    try {
      console.log('[CART_MERGE] Reading carts file');
      const fileData = fs.readFileSync(cartsFilePath, 'utf8');
      cartsData = JSON.parse(fileData);
      console.log('[CART_MERGE] Cart data loaded');
    } catch (error) {
      console.error('[CART_MERGE] Error reading carts file:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to read carts data' 
      });
    }
    
    // Find previous user's carts
    const previousUserCarts = {};
    const customerCarts = {};
    
    console.log('[CART_MERGE] Looking for carts to merge');
    
    // Create cart IDs based on store parameter or find all carts
    if (storeId) {
      // If storeId is provided, just merge for that specific store
      const previousCartId = `${previousId}_${storeId}`;
      const customerCartId = `${customerId}_${storeId}`;
      
      if (cartsData.carts[previousCartId]) {
        previousUserCarts[previousCartId] = cartsData.carts[previousCartId];
      }
      
      if (cartsData.carts[customerCartId]) {
        customerCarts[customerCartId] = cartsData.carts[customerCartId];
      }
    } else {
      // If no storeId, find all carts for both IDs across all stores
      for (const [cartId, cartData] of Object.entries(cartsData.carts)) {
        // Match previous user carts
        if (cartData.userId === previousId || 
            cartId.startsWith(previousId) || 
            cartId.includes(`_${previousId}_`) ||
            cartId.includes(`${previousId}_`)) {
          previousUserCarts[cartId] = cartData;
        }
        
        // Match customer carts
        if (cartData.userId === customerId || 
            cartId.startsWith(customerId) || 
            cartId.includes(`_${customerId}_`) ||
            cartId.includes(`${customerId}_`)) {
          customerCarts[cartId] = cartData;
        }
      }
    }
    
    console.log('[CART_MERGE] Found carts:', { 
      previousUserCarts: Object.keys(previousUserCarts),
      customerCarts: Object.keys(customerCarts)
    });
    
    // Merge carts
    let mergeCount = 0;
    for (const [previousCartId, previousCartData] of Object.entries(previousUserCarts)) {
      // Get the store ID from the previous cart
      const cartStoreId = previousCartData.storeId;
      const customerCartId = `${customerId}_${cartStoreId}`;
      
      // Skip if no items to merge
      if (!previousCartData.cart || previousCartData.cart.length === 0) {
        console.log(`[CART_MERGE] No items to merge for store ${cartStoreId}`);
        continue;
      }
      
      // If customer doesn't have a cart for this store, create it
      if (!cartsData.carts[customerCartId]) {
        console.log(`[CART_MERGE] Creating new customer cart for store ${cartStoreId}`);
        cartsData.carts[customerCartId] = {
          userId: customerId,
          storeId: cartStoreId,
          cart: [],
          numberOfItemsInCart: 0,
          total: 0,
          lastUpdated: new Date().toISOString()
        };
      }
      
      // Merge cart items
      const customerCart = cartsData.carts[customerCartId];
      
      // Track which items were merged
      const mergedItems = [];
      
      // Add items from previous cart to customer cart if they don't exist
      for (const item of previousCartData.cart) {
        // Check if item already exists in customer cart
        const existingItemIndex = customerCart.cart.findIndex(
          cartItem => cartItem.id === item.id || cartItem.sku === item.sku
        );
        
        if (existingItemIndex >= 0) {
          // If item exists, add quantities
          customerCart.cart[existingItemIndex].quantity += item.quantity;
          mergedItems.push(item.sku || item.id);
        } else {
          // If item doesn't exist, add it
          customerCart.cart.push(item);
          mergedItems.push(item.sku || item.id);
        }
      }
      
      // Update customer cart metadata
      customerCart.numberOfItemsInCart = customerCart.cart.length;
      customerCart.total = customerCart.cart.reduce((sum, item) => 
        sum + (item.price * item.quantity), 0
      );
      customerCart.lastUpdated = new Date().toISOString();
      
      console.log(`[CART_MERGE] Merged ${mergedItems.length} items for store ${cartStoreId}:`, mergedItems);
      
      // Keep track of merges
      mergeCount++;
      
      // Remove the previous user's cart for this store (optional, can keep for history)
      delete cartsData.carts[previousCartId];
    }
    
    // Save updated cart data
    try {
      console.log('[CART_MERGE] Writing updated cart data to file');
      fs.writeFileSync(cartsFilePath, JSON.stringify(cartsData, null, 2));
      console.log('[CART_MERGE] Cart data saved successfully');
    } catch (error) {
      console.error('[CART_MERGE] Error writing cart data:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to save merged cart data' 
      });
    }
    
    // Return success
    return res.status(200).json({ 
      success: true, 
      message: `Successfully merged ${mergeCount} carts` 
    });
  } catch (error) {
    console.error('[CART_MERGE] Error merging carts:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to merge carts' 
    });
  }
} 