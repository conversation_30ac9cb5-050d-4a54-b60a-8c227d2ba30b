import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log('[CART_DIRECT_DATA] Request received');
  
  try {
    // Read the carts.json file containing real cart data
    const cartsPath = path.join(process.cwd(), 'data/carts/carts.json');
    console.log('[CART_DIRECT_DATA] Reading carts file from:', cartsPath);
    
    const cartsData = fs.readFileSync(cartsPath, 'utf8');
    const carts = JSON.parse(cartsData);
    
    console.log('[CART_DIRECT_DATA] Found cart keys:', Object.keys(carts.carts));
    
    // Return the cart data with success flag to match expected format
    res.status(200).json({
      success: true,
      carts: carts.carts
    });
  } catch (error) {
    console.error('[CART_DIRECT_DATA] Error loading cart data:', error);
    res.status(500).json({ success: false, error: 'Failed to load cart data' });
  }
} 