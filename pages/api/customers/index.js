import fs from 'fs';
import path from 'path';

// Path to customers data file
const dataDir = path.join(process.cwd(), 'data');
const customersFile = path.join(dataDir, 'customers.json');

async function handler(req, res) {
  // Ensure customers data file exists
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  if (!fs.existsSync(customersFile)) {
    fs.writeFileSync(customersFile, JSON.stringify([]));
  }

  // Read customers data
  let customers = [];
  try {
    const data = fs.readFileSync(customersFile, 'utf8');
    customers = JSON.parse(data);
    if (!Array.isArray(customers)) {
      // Handle potential nested structure from old format
      if (customers.customers && Array.isArray(customers.customers)) {
        customers = customers.customers;
      } else {
        customers = [];
      }
    }
  } catch (error) {
    console.error('Error reading customers file:', error);
    customers = [];
  }

  // Handle different request methods
  switch (req.method) {
    case 'GET':
      return getCustomers(req, res, customers);
    case 'POST':
      return createCustomer(req, res, customers);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

// Get list of customers with filtering options
function getCustomers(req, res, customers) {
  try {
    console.log('GET /api/customers - Processing request');
    const { search, limit = 50, skip = 0, sort = 'createdAt', order = 'desc' } = req.query;
    
    // Filter customers if search param exists
    let filteredCustomers = customers;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredCustomers = customers.filter(customer => 
        customer.name?.toLowerCase().includes(searchLower) || 
        customer.email?.toLowerCase().includes(searchLower) ||
        customer.phone?.includes(search)
      );
    }
    
    // Sort customers (safely)
    if (filteredCustomers.length > 0 && filteredCustomers[0][sort] !== undefined) {
      filteredCustomers.sort((a, b) => {
        if (order === 'asc') {
          return a[sort] > b[sort] ? 1 : -1;
        } else {
          return a[sort] < b[sort] ? 1 : -1;
        }
      });
    }
    
    // Paginate results
    const paginatedCustomers = filteredCustomers.slice(parseInt(skip), parseInt(skip) + parseInt(limit));
    
    // Remove passwords from response
    const sanitizedCustomers = paginatedCustomers.map(customer => {
      const { password, ...rest } = customer;
      return rest;
    });
    
    console.log(`GET /api/customers - Returning ${sanitizedCustomers.length} customers`);
    
    return res.status(200).json({
      success: true,
      total: filteredCustomers.length,
      customers: sanitizedCustomers
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    return res.status(500).json({ error: 'Error fetching customers', message: error.message });
  }
}

// Create a new customer
async function createCustomer(req, res, customers) {
  try {
    console.log('POST /api/customers - Processing request');
    const { name, email, password, phone, address } = req.body;
    
    // Validate required fields
    if (!name || !email || !password) {
      return res.status(400).json({ error: 'Name, email, and password are required' });
    }
    
    // Check if email already exists
    if (customers.some(customer => customer.email?.toLowerCase() === email.toLowerCase())) {
      return res.status(409).json({ error: 'Email already registered' });
    }
    
    // Generate a customer ID
    const id = `CUST${String(customers.length + 1).padStart(3, '0')}`;
    
    // Create new customer
    const now = new Date().toISOString();
    const newCustomer = {
      id,
      name,
      email,
      password, // Store password as plain text
      phone: phone || '',
      addresses: address ? [{ address }] : [],
      createdAt: now,
      updatedAt: now
    };
    
    // Add to customers array
    customers.push(newCustomer);
    
    // Save to file
    fs.writeFileSync(customersFile, JSON.stringify(customers, null, 2));
    
    // Return customer without password
    const { password: _, ...customerData } = newCustomer;
    
    console.log('Customer created with ID:', id);
    console.log('Returning response:', customerData);
    
    return res.status(201).json(customerData);
  } catch (error) {
    console.error('Error creating customer:', error);
    return res.status(500).json({ error: 'Error creating customer', message: error.message });
  }
}
export default handler; 
