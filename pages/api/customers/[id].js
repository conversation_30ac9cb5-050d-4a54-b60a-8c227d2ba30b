import fs from 'fs';
import path from 'path';
import { promises as fsPromises } from 'fs';

// Path to the customer data file
const dataFilePath = path.join(process.cwd(), 'data', 'customers.json');

// Helper function to get the customers data
const getCustomersData = async () => {
  try {
    // Check if file exists, create it if it doesn't
    if (!fs.existsSync(dataFilePath)) {
      await fsPromises.writeFile(dataFilePath, JSON.stringify([]), 'utf8');
      return [];
    }

    // Read the file
    const jsonData = await fsPromises.readFile(dataFilePath, 'utf8');
    return JSON.parse(jsonData);
  } catch (error) {
    console.error('Error reading customers data:', error);
    return [];
  }
};

// Helper function to write the customers data
const writeCustomersData = async (data) => {
  try {
    await fsPromises.writeFile(dataFilePath, JSON.stringify(data, null, 2), 'utf8');
    return { success: true };
  } catch (error) {
    console.error('Error writing customers data:', error);
    return { success: false, error: error.message };
  }
};

export default async function handler(req, res) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'Customer ID is required' });
  }

  try {
    // Get all customers
    const customersData = await getCustomersData();

    // GET request - Retrieve a specific customer
    if (req.method === 'GET') {
      const customer = customersData.find(c => c.id === id);
      
      if (!customer) {
        return res.status(404).json({ error: 'Customer not found' });
      }
      
      return res.status(200).json(customer);
    }
    
    // PUT request - Update a specific customer
    if (req.method === 'PUT') {
      const customerIndex = customersData.findIndex(c => c.id === id);
      
      if (customerIndex === -1) {
        return res.status(404).json({ error: 'Customer not found' });
      }
      
      const updatedData = {
        ...customersData[customerIndex],
        ...req.body,
        updatedAt: new Date().toISOString()
      };
      
      // Make sure the ID is preserved
      updatedData.id = id;
      
      // Update the customer data
      customersData[customerIndex] = updatedData;
      
      // Write the updated data
      const writeResult = await writeCustomersData(customersData);
      
      if (!writeResult.success) {
        return res.status(500).json({ error: 'Failed to update customer data', details: writeResult.error });
      }
      
      return res.status(200).json(updatedData);
    }
    
    // DELETE request - Delete a specific customer
    if (req.method === 'DELETE') {
      const customerIndex = customersData.findIndex(c => c.id === id);
      
      if (customerIndex === -1) {
        return res.status(404).json({ error: 'Customer not found' });
      }
      
      // Remove the customer
      customersData.splice(customerIndex, 1);
      
      // Write the updated data
      const writeResult = await writeCustomersData(customersData);
      
      if (!writeResult.success) {
        return res.status(500).json({ error: 'Failed to delete customer', details: writeResult.error });
      }
      
      return res.status(200).json({ message: 'Customer deleted successfully' });
    }
    
    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in customer API:', error);
    return res.status(500).json({ error: 'Internal server error', details: error.message });
  }
} 