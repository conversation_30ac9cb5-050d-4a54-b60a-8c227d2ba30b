import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Simple admin check
    const isAdmin = true; // For development, always allow access
    
    if (!isAdmin) {
      return res.status(401).json({ 
        success: false, 
        message: 'Unauthorized - admin access required' 
      });
    }

    const { storeId } = req.query;

    // Read customers from JSON file
    const customersFilePath = path.join(process.cwd(), 'data', 'customers.json');
    const customersData = fs.readFileSync(customersFilePath, 'utf8');
    let customers = JSON.parse(customersData);

    // Filter by store if provided
    if (storeId) {
      customers = customers.filter(customer => customer.storeId === storeId);
    }

    // Map customers to the expected format while preserving original structure
    const formattedCustomers = customers.map(customer => ({
      id: customer.id || '',
      name: customer.name || 'Unknown',
      phone: customer.phone || '',
      email: customer.email || '',
      // Keep all original fields
      ...customer
    }));

    return res.status(200).json({ 
      success: true, 
      customers: formattedCustomers 
    });
  } catch (error) {
    console.error('Error fetching customer list:', error);
    
    // If the file doesn't exist, return an empty array
    if (error.code === 'ENOENT') {
      return res.status(200).json({ 
        success: true, 
        customers: [] 
      });
    }
    
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message
    });
  }
} 