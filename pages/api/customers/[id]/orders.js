import fs from 'fs'
import path from 'path'

// Path to data files
const dataDir = path.join(process.cwd(), 'data')
const ordersFile = path.join(dataDir, 'orders.json')

export default async function handler(req, res) {
  // Only allow GET requests for this endpoint
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Get customer ID from the URL
  const { id } = req.query

  if (!id) {
    return res.status(400).json({ error: 'Customer ID is required' })
  }

  console.log('Fetching orders for customer ID:', id);

  // Read orders data
  let orders = []
  try {
    if (fs.existsSync(ordersFile)) {
      const data = fs.readFileSync(ordersFile, 'utf8')
      orders = JSON.parse(data)
      if (!Array.isArray(orders)) {
        orders = []
      }
    }
  } catch (error) {
    console.error('Error reading orders file:', error)
    return res.status(500).json({ error: 'Error reading order data' })
  }

  // Filter orders by customerId
  const customerOrders = orders.filter(order => order.customerId === id)
  console.log(`Found ${customerOrders.length} orders for customer ${id}`);

  // Sort by date (newest first)
  customerOrders.sort((a, b) => {
    const dateA = new Date(a.createdAt || a.date || 0)
    const dateB = new Date(b.createdAt || b.date || 0)
    return dateB - dateA
  })

  return res.status(200).json({ 
    success: true, 
    total: customerOrders.length,
    orders: customerOrders 
  })
} 