import fs from 'fs';
import path from 'path';
import { promises as fsPromises } from 'fs';
import bcrypt from 'bcryptjs';

// Path to the customer data file
const dataFilePath = path.join(process.cwd(), 'data', 'customers.json');

// Helper function to get the customers data
const getCustomersData = async () => {
  try {
    // Check if file exists, create it if it doesn't
    if (!fs.existsSync(dataFilePath)) {
      await fsPromises.writeFile(dataFilePath, JSON.stringify([]), 'utf8');
      return [];
    }

    // Read the file
    const jsonData = await fsPromises.readFile(dataFilePath, 'utf8');
    return JSON.parse(jsonData);
  } catch (error) {
    console.error('Error reading customers data:', error);
    return [];
  }
};

// Helper function to write the customers data
const writeCustomersData = async (data) => {
  try {
    await fsPromises.writeFile(dataFilePath, JSON.stringify(data, null, 2), 'utf8');
    return { success: true };
  } catch (error) {
    console.error('Error writing customers data:', error);
    return { success: false, error: error.message };
  }
};

export default async function handler(req, res) {
  // Only allow POST method for password changes
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      message: 'Method not allowed' 
    });
  }

  const { id } = req.query;
  const { currentPassword, newPassword } = req.body;

  // Validate inputs
  if (!id) {
    return res.status(400).json({ 
      success: false,
      message: 'Customer ID is required' 
    });
  }

  if (!currentPassword || !newPassword) {
    return res.status(400).json({ 
      success: false,
      message: 'Current password and new password are required' 
    });
  }

  try {
    // Get all customers
    const customersData = await getCustomersData();
    
    // Find the customer by ID
    const customerIndex = customersData.findIndex(c => c.id === id);
    
    if (customerIndex === -1) {
      return res.status(404).json({ 
        success: false,
        message: 'Customer not found' 
      });
    }

    const customer = customersData[customerIndex];
    
    // Check if the customer has a password set
    if (!customer.password && !customer.passwordHash) {
      return res.status(400).json({ 
        success: false,
        message: 'This account does not have a password set' 
      });
    }

    // Verify the current password
    let isPasswordValid = false;
    
    if (customer.passwordHash) {
      // If using bcrypt hash
      isPasswordValid = await bcrypt.compare(currentPassword, customer.passwordHash);
    } else if (customer.password) {
      // If using plain text password (not secure, but matches current data)
      isPasswordValid = currentPassword === customer.password;
    }
    
    if (!isPasswordValid) {
      return res.status(400).json({ 
        success: false,
        message: 'Current password is incorrect' 
      });
    }

    // Hash the new password
    const saltRounds = 10;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
    
    // Update the password
    customersData[customerIndex] = {
      ...customer,
      password: newPassword, // Keep plain text for compatibility
      passwordHash: newPasswordHash, // Add secure hash for future
      updatedAt: new Date().toISOString()
    };
    
    // Write the updated data
    const writeResult = await writeCustomersData(customersData);
    
    if (!writeResult.success) {
      return res.status(500).json({ 
        success: false,
        message: 'Failed to update password',
        details: writeResult.error 
      });
    }
    
    return res.status(200).json({ 
      success: true,
      message: 'Password updated successfully' 
    });
  } catch (error) {
    console.error('Error changing password:', error);
    return res.status(500).json({ 
      success: false,
      message: 'Internal server error',
      details: error.message 
    });
  }
} 