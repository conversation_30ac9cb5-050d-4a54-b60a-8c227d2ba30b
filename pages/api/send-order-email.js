import nodemailer from 'nodemailer';

// SMTP Configuration - Using the same working credentials as contact form
const SMTP_CONFIG = {
  host: 'email-smtp.us-east-1.amazonaws.com',
  port: 587,
  secure: false,
  auth: {
    user: 'AKIA5BI3T5T7MTKUMZEF',
    pass: 'BLsRI5+AnugtvExnbs4SOFKvlBYghpmxZR2jUeioGQNU'
  }
};

// Email configuration
const EMAIL_CONFIG = {
  from: '<EMAIL>',
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { orderId, customerEmail, customerName, orderData } = req.body;

  // Validate required fields
  if (!orderId || !customerEmail || !orderData) {
    return res.status(400).json({ message: 'Missing required fields' });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(customerEmail)) {
    return res.status(400).json({ message: 'Invalid email format' });
  }

  try {
    // Create transporter
    const transporter = nodemailer.createTransport(SMTP_CONFIG);

    // Verify SMTP connection
    await transporter.verify();

    // Format currency
    const formatCurrency = (amount, currency = 'VND') => {
      if (!amount) return '0';
      if (currency === 'NT$' || currency === 'NT') {
        return `${amount.toLocaleString()} NT$`;
      }
      return `${amount.toLocaleString()} ${currency}`;
    };

    // Calculate order total
    const calculateOrderTotal = (order) => {
      if (order.totalAmount && order.totalAmount > 0) {
        return order.totalAmount;
      }
      if (order.amount && order.amount > 0) {
        return order.amount;
      }
      if (order.items && order.items.length > 0) {
        const itemsTotal = order.items.reduce((total, item) => {
          const price = Number(item.price) || 0;
          const quantity = Number(item.quantity) || 0;
          return total + (price * quantity);
        }, 0);
        if (itemsTotal > 0) {
          return itemsTotal;
        }
      }
      return order.totalAmount || order.amount || 0;
    };

    // Create email HTML content
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .order-details { background-color: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
          .item-row { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee; }
          .total-row { font-weight: bold; font-size: 1.1em; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 0.9em; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Xác nhận đơn hàng</h1>
            <p>Đơn hàng #${orderId}</p>
          </div>
          
          <div class="content">
            <p>Xin chào ${customerName || 'Khách hàng'},</p>
            <p>Cảm ơn bạn đã đặt hàng! Dưới đây là thông tin chi tiết đơn hàng của bạn:</p>
            
            <div class="order-details">
              <h3>Thông tin đơn hàng</h3>
              <p><strong>Mã đơn hàng:</strong> ${orderId}</p>
              <p><strong>Ngày đặt hàng:</strong> ${new Date(orderData.createdAt).toLocaleString('vi-VN')}</p>
              <p><strong>Trạng thái:</strong> ${orderData.status === 'pending' ? 'Chờ xác nhận' : 
                orderData.status === 'processing' ? 'Đang xử lý' :
                orderData.status === 'completed' ? 'Hoàn thành' :
                orderData.status === 'cancelled' ? 'Đã hủy' : 
                orderData.status || 'Không xác định'}</p>
              <p><strong>Trạng thái thanh toán:</strong> ${orderData.paymentStatus === 'paid' ? 'Đã thanh toán' : 
                orderData.paymentStatus === 'not_paid' ? 'Chưa thanh toán' : 
                orderData.paymentStatus === 'failed' ? 'Thanh toán thất bại' :
                orderData.paymentStatus || 'Không xác định'}</p>
            </div>
            
            ${orderData.items && orderData.items.length > 0 ? `
            <div class="order-details">
              <h3>Chi tiết sản phẩm</h3>
              ${orderData.items.map(item => `
                <div class="item-row">
                  <span>${item.name || 'Sản phẩm'} x ${item.quantity}</span>
                  <span>${formatCurrency(Number(item.price) * item.quantity, orderData.currency)}</span>
                </div>
              `).join('')}
              
              <div class="item-row total-row">
                <span>Tổng cộng:</span>
                <span>${formatCurrency(calculateOrderTotal(orderData), orderData.currency)}</span>
              </div>
              ${orderData.fee > 0 ? `
              <div class="item-row">
                <span>Phí:</span>
                <span>${formatCurrency(orderData.fee, orderData.currency)}</span>
              </div>
              ` : ''}
            </div>
            ` : ''}
            
            ${orderData.paymentMethod === 'convenience_store' && orderData.paymentInfo ? `
            <div class="order-details">
              <h3>Thông tin thanh toán</h3>
              <p><strong>Phương thức:</strong> Thanh toán tại cửa hàng tiện lợi</p>
              ${orderData.paymentSubMethod === 'seven_eleven_ibon' && orderData.paymentInfo.ibonPaymentCode ? `
                <p><strong>Mã thanh toán ibon:</strong> ${orderData.paymentInfo.ibonPaymentCode}</p>
                <p>Vui lòng đến máy iBON tại cửa hàng 7-Eleven để thanh toán.</p>
              ` : ''}
              ${orderData.paymentSubMethod === 'family_mart' && orderData.paymentInfo.pinCode ? `
                <p><strong>Mã FamiPort:</strong> ${orderData.paymentInfo.pinCode}</p>
                <p>Vui lòng đến máy FamiPort tại cửa hàng FamilyMart để thanh toán.</p>
              ` : ''}
            </div>
            ` : ''}
            
            ${orderData.shippingAddress ? `
            <div class="order-details">
              <h3>Địa chỉ giao hàng</h3>
              <p>${orderData.shippingAddress}</p>
            </div>
            ` : ''}
            
            <p>Nếu bạn có bất kỳ câu hỏi nào về đơn hàng, vui lòng liên hệ với chúng tôi.</p>
            <p>Trân trọng,<br>Đội ngũ hỗ trợ khách hàng</p>
          </div>
          
          <div class="footer">
            <p>Email này được gửi tự động, vui lòng không trả lời.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Create plain text version for better compatibility
    const emailText = `
      Xác nhận đơn hàng #${orderId}
      
      Xin chào ${customerName || 'Khách hàng'},
      
      Cảm ơn bạn đã đặt hàng! Dưới đây là thông tin chi tiết đơn hàng của bạn:
      
      Mã đơn hàng: ${orderId}
      Ngày đặt hàng: ${new Date(orderData.createdAt).toLocaleString('vi-VN')}
      Trạng thái: ${orderData.status === 'pending' ? 'Chờ xác nhận' : 
        orderData.status === 'processing' ? 'Đang xử lý' :
        orderData.status === 'completed' ? 'Hoàn thành' :
        orderData.status === 'cancelled' ? 'Đã hủy' : 
        orderData.status || 'Không xác định'}
      Trạng thái thanh toán: ${orderData.paymentStatus === 'paid' ? 'Đã thanh toán' : 
        orderData.paymentStatus === 'not_paid' ? 'Chưa thanh toán' : 
        orderData.paymentStatus === 'failed' ? 'Thanh toán thất bại' :
        orderData.paymentStatus || 'Không xác định'}
      
      ${orderData.items && orderData.items.length > 0 ? 
        'Chi tiết sản phẩm:\n' + 
        orderData.items.map(item => `- ${item.name || 'Sản phẩm'} x ${item.quantity}: ${formatCurrency(Number(item.price) * item.quantity, orderData.currency)}`).join('\n') +
        `\n\nTổng cộng: ${formatCurrency(calculateOrderTotal(orderData), orderData.currency)}` : 
        `Tổng thanh toán: ${formatCurrency(calculateOrderTotal(orderData), orderData.currency)}`}
      
      Nếu bạn có bất kỳ câu hỏi nào về đơn hàng, vui lòng liên hệ với chúng tôi.
      
      Trân trọng,
      Đội ngũ hỗ trợ khách hàng
      
      Email này được gửi tự động vào ${new Date().toLocaleString('vi-VN')}
    `;

    // Send email
    const info = await transporter.sendMail({
      from: `"Xác nhận đơn hàng" <${EMAIL_CONFIG.from}>`,
      to: customerEmail,
      subject: `Xác nhận đơn hàng #${orderId}`,
      text: emailText,
      html: emailHtml,
    });

    console.log('Email sent successfully:', info.messageId);

    res.status(200).json({ 
      success: true,
      message: 'Email sent successfully',
      messageId: info.messageId 
    });
  } catch (error) {
    console.error('Error sending email:', error);
    
    res.status(500).json({ 
      error: 'Failed to send email',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
} 