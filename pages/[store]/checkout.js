import React, { useState, useEffect, useContext, Suspense } from "react"
import Head from "next/head"
import {
  SiteContext,
  ContextProviderComponent,
} from "../../context/mainContext"
import { FaLongArrowAltLeft, FaTimes, FaMoneyBill, FaWallet, FaCreditCard, FaPaypal, FaCcVisa, FaCcMastercard } from "react-icons/fa"
import Link from "next/link"
import Image from "../../components/Image"
import { Elements } from "@stripe/react-stripe-js"
import { loadStripe } from "@stripe/stripe-js"
import PaymentFormCOD from "../../components/PaymentFormCOD"
import { fetchStore } from "../../utils/storeProvider"
import { fetchInventory } from "../../utils/inventoryProvider"
import axios from "axios"
import { useRouter } from "next/router"
import dynamic from 'next/dynamic'
import { getCustomerId } from "../../utils/customerAuth"
import MasterPaymentForms from "../../components/taiwan/payment/MasterPaymentForms"
import PaymentSection from "../../components/PaymentSection"
import { 
  AVAILABLE_CURRENCIES, 
  getPaymentMethodsByCurrentcy, 
  filterCartByCurrency 
} from "../../utils/paymentUtils"
// Import the header components
import DesktopHeader from "../../templates/shein/components/DesktopHeader"
import MobileHeader from "../../templates/shein/components/MobileHeader"
// Import the checkout logger
import {
  logCheckoutEvent,
  logPaymentApiCall,
  logPaymentApiResponse,
  logCheckoutProcess,
  logTransaction,
  logCheckoutError
} from "../../utils/checkoutLogger"

import { getProductImage, handleImageError } from '../../utils/imageUtils'

// Import checkout progress utilities
import {
  CheckoutProgress,
  AbandonedCheckoutRecovery
} from "../../utils/checkoutProgress"

// Dynamically import ProductCheckoutProcess with ssr disabled
const ProductCheckoutProcess = dynamic(
  () => import('../../components/ProductCheckoutProcess'),
  { ssr: false }
)

// Dynamically import the new single-page checkout component
const ProductCheckoutSinglePage = dynamic(
  () => import('../../components/ProductCheckoutSinglePage'),
  { ssr: false }
)

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)

// Add logging utility - Replace the old console.log based function
const logCheckout = (message, data = null) => {
  // Use the new logging system
  return logCheckoutProcess('general', { message, data });
}

// Mapping object to associate each payment method with its corresponding component
const PaymentFormComponents = {
  'cod': PaymentFormCOD,
  '7-11': MasterPaymentForms,
  'family-mart': MasterPaymentForms,
  'ok-mart': MasterPaymentForms
  /* 'hi-life': MasterPaymentForms */
}

// Helper to normalize currency codes
const normalizeCurrency = (currency) => {
  if (!currency) return currency;

  // Handle Taiwan Dollar variations
  if (currency === 'NT' || currency === 'NT$' || currency === 'NTD' || currency === 'TWD') {
    return 'NT$';
  }

  return currency.toUpperCase(); // Normalize all currency codes to uppercase
};

// Product type detection function
const ProductType = {
  // Detect product type from item data
  detect: (item) => {
    // Build detailed analysis for debugging
    const analysis = {
      steps: [],
      conclusion: { type: 'DEFAULT', reason: 'No categories to analyze' }
    };
    
    // Step 1: Check if categories exist
    if (!item.categories) {
      analysis.steps.push(`No categories property found on product ${item.sku || 'unknown'}`);
      analysis.conclusion = { type: 'DEFAULT', reason: 'Missing categories property' };
      return {
        ...analysis.conclusion,
        analysis: analysis.steps
      };
    }
    
    if (!Array.isArray(item.categories)) {
      analysis.steps.push(`Categories property is not an array for product ${item.sku || 'unknown'}`);
      analysis.steps.push(`Found instead: ${typeof item.categories}`);
      analysis.conclusion = { type: 'DEFAULT', reason: 'Categories not in array format' };
      return {
        ...analysis.conclusion,
        analysis: analysis.steps
      };
    }
    
    if (item.categories.length === 0) {
      analysis.steps.push(`Categories array is empty for product ${item.sku || 'unknown'}`);
      analysis.conclusion = { type: 'DEFAULT', reason: 'Empty categories array' };
      return {
        ...analysis.conclusion,
        analysis: analysis.steps
      };
    }
    
    // Step 2: Normalize categories
    const upperCategories = item.categories.map(cat => 
      typeof cat === 'string' ? cat.trim().toUpperCase() : ''
    );
    
    analysis.steps.push(`Tìm thấy ${upperCategories.length} loại sản phẩm: [${upperCategories.join(', ')}]`);
    
    // Step 3: Check for exact category matches
    if (upperCategories.includes('CARD')) {
      analysis.steps.push('Tìm thấy đúng loại sản phẩm "CARD"');
      analysis.conclusion = { type: 'CARD', reason: 'Tìm thấy đúng loại sản phẩm "CARD"' };
    } else if (upperCategories.includes('SIM')) {
      analysis.steps.push('Tìm thấy đúng loại sản phẩm "SIM"');
      analysis.conclusion = { type: 'SIM', reason: 'Tìm thấy đúng loại sản phẩm "SIM"' };
    } else if (upperCategories.includes('TRAVELSIM')) {
      analysis.steps.push('Tìm thấy đúng loại sản phẩm "TRAVELSIM"');
      analysis.conclusion = { type: 'TRAVELSIM', reason: 'Tìm thấy đúng loại sản phẩm "TRAVELSIM"' };
    } else {
      analysis.steps.push('Không tìm thấy đúng loại sản phẩm CARD, SIM, hoặc TRAVELSIM');
      analysis.conclusion = { type: 'DEFAULT', reason: 'Không tìm thấy đúng loại sản phẩm' };
    }
    
    // Include analysis in the return object
    return {
      ...analysis.conclusion,
      analysis: analysis.steps
    };
  }
};

function CheckoutWithContext(props) {
  return (
    <ContextProviderComponent>
      <SiteContext.Consumer>
        {(context) => (
          <Elements stripe={stripePromise}>
            <Checkout {...props} context={context} />
          </Elements>
        )}
      </SiteContext.Consumer>
    </ContextProviderComponent>
  )
}

const calculateShipping = () => {
  return 0
}

/* const affiliateIds = ["aff001", "aff002", "aff003"]; */

// Function to save address to user profile
const saveAddressToProfile = async (addressData) => {
  try {
    // Get the current customer ID
    const customerId = getCustomerId();
    
    if (!customerId) {
      logCheckout("Không tìm thấy ID khách hàng, bỏ qua cập nhật thông tin khách hàng");
      return false;
    }
    
    // Validate address data
    if (!addressData || !addressData.street || !addressData.city) {
      logCheckout("Thông tin địa chỉ không đầy đủ, không thể lưu vào thông tin khách hàng", addressData);
      
      logCheckoutError('save-address-validation', 'Incomplete address data', { 
        addressData,
        customerId 
      });
      
      return false;
    }
    
    logCheckout("Lưu thông tin địa chỉ vào thông tin khách hàng", { customerId, ...addressData });
    
    // Call API to update the user profile with address data
    const requestPayload = {
      customerId,
      addressData: {
        street: addressData.street,
        city: addressData.city,
        district: addressData.district || '',
        country: addressData.country || 'Vietnam'
      }
    };
    
    // Log the API call
    logPaymentApiCall(
      'customer-api',
      '/api/customer/update-profile',
      requestPayload,
      { method: 'POST' }
    );
    
    const response = await axios.post('/api/customer/update-profile', requestPayload);
    
    // Log the API response
    logPaymentApiResponse(
      'customer-api',
      '/api/customer/update-profile',
      response.data,
      response.status
    );
    
    if (response.status === 200) {
      logCheckout("Lưu thông tin địa chỉ vào thông tin khách hàng thành công", response.data);
      
      // Log the successful address update
      logCheckoutProcess('address_update', {
        success: true,
        customerId,
        address: addressData,
        response: response.data
      });
      
      return true;
    } else {
      logCheckout("Lưu thông tin địa chỉ vào thông tin khách hàng thất bại", response.data);
      
      // Log the failed address update
      logCheckoutError('save-address-api', 'Failed API response', {
        status: response.status,
        data: response.data,
        request: requestPayload
      });
      
      return false;
    }
  } catch (error) {
    logCheckout("Lưu thông tin địa chỉ vào thông tin khách hàng thất bại", error);
    console.error("Failed to update profile with address:", error);
    
    // Log the error
    logCheckoutError('save-address', error, {
      addressData,
      customerId: getCustomerId()
    });
    
    return false;
  }
};

// Group cart items by currency and product type
const groupCartItemsByCurrencyAndType = (cart) => {
  // First, normalize currencies for consistency
  const normalizedCart = cart.map(item => ({
    ...item,
    currency: normalizeCurrency(item.currency || 'VND')
  }));
  
  // Group by currency first, then by product type
  return normalizedCart.reduce((groups, item) => {
    const currency = item.currency;
    
    // Define product type based on categories
    const productTypeResult = ProductType.detect(item);
    const productType = productTypeResult.type;
    
    // Log the categorization for debugging purposes
    console.log(`[CHECKOUT] Product ${item.sku} categorized as ${productType} from categories:`, item.categories);
    if (productTypeResult.analysis) {
      console.log(`[CHECKOUT] Analysis for ${item.sku}:`, productTypeResult.analysis);
    }
    
    // Create currency group if it doesn't exist
    if (!groups[currency]) {
      groups[currency] = {
        currency,
        types: {}
      };
    }
    
    // Create product type group if it doesn't exist
    if (!groups[currency].types[productType]) {
      groups[currency].types[productType] = {
        type: productType,
        items: []
      };
    }
    
    // Add item to its group
    groups[currency].types[productType].items.push(item);
    
    return groups;
  }, {});
};

// Function to extract all unique product types from cart
const getUniqueProductTypes = (cart) => {
  const productTypes = new Set();
  
  if (cart && cart.length > 0) {
    cart.forEach(item => {
      const productTypeResult = ProductType.detect(item);
      productTypes.add(productTypeResult.type);
    });
  }
  
  return Array.from(productTypes);
};

const Checkout = ({ context, storeObject = "" }) => {
  logCheckout("Initializing Checkout", { storeId: storeObject?.storeId });
  
  const [errorMessage, setErrorMessage] = useState(null)
  const [orderCompleted, setOrderCompleted] = useState(false)
  const [panelVisible, setPanelVisible] = useState(false)
  const [selectedCurrency, setSelectedCurrency] = useState(storeObject.currency || 'VND')
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState(
      storeObject.paymentmethods ? 
        storeObject.paymentmethods.split(',')[0].trim() : 
        'inquiry'
    )
  
  // Set productCheckoutVisible to true by default
  const [productCheckoutVisible, setProductCheckoutVisible] = useState(true)
  
  // Add state for payment method modal
  const [showPaymentMethodsModal, setShowPaymentMethodsModal] = useState(false)
  const [filterCurrency, setFilterCurrency] = useState(null)
  const [showAllPaymentMethods, setShowAllPaymentMethods] = useState(false)
  // Add state for grouped cart
  const [groupedCart, setGroupedCart] = useState({})
  const [groupTotals, setGroupTotals] = useState({})
  // Add state for grouped cart by currency and type
  const [groupedCartByType, setGroupedCartByType] = useState({});
  // Add state for product details with categories
  const [productDetails, setProductDetails] = useState({});
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);

  // Add state for address fields
  const [addressData, setAddressData] = useState({
    street: '',
    city: '',
    district: '',
    country: 'Vietnam'
  })

  // Add state for abandoned checkout recovery
  const [isRecovering, setIsRecovering] = useState(false)
  const [recoveredData, setRecoveredData] = useState(null)

  // Add state for header functionality
  const [isMobile, setIsMobile] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false)

  const { affiliate } = useContext(SiteContext)
  const router = useRouter()
  const { advance, resume, orderId: resumeOrderId } = router.query

  logCheckout("Router query params", router.query);

  const [affiliateCodeInput, setAffiliateCodeInput] = useState(affiliate || "")
  const { addAffiliate, getAllAffiliateIDs, emptyAffiliateArray } =
    useContext(SiteContext)

  /* const affiliateIds = getAllAffiliateIDs("vanggoc", ""); */
  const affiliateIds = getAllAffiliateIDs(storeObject.storeId, "")
  logCheckout("Available affiliate IDs", affiliateIds);
  /* alert(JSON.stringify(affiliateIds)); */
  //console.log(JSON.stringify(affiliateIds));

  const allowedPaymentMethods = storeObject.paymentmethods ? 
    storeObject.paymentmethods.split(',').map(method => method.trim()) : 
    Object.keys(PaymentFormComponents); // Use all available payment methods if none specified
  
  logCheckout("Allowed payment methods", allowedPaymentMethods);

  const handlePaymentMethodChange = (event) => {
    const newMethod = event.target.value;
    logCheckout("Payment method changed", { from: selectedPaymentMethod, to: newMethod });
    setSelectedPaymentMethod(newMethod)
  }

  const handleAffiliateCodeInputChange = (event) => {
    setAffiliateCodeInput(event.target.value)
  }

  const applyAffiliate = () => {
    /* if (affiliateCodeInput.trim() === "") {
      alert("Mã CSKH không hợp lệ.")
      return
    } */

    const newAffiliate = {
      affiliateid: affiliateCodeInput,
      timestamp: Date.now(), // Current timestamp
      storeid: storeObject.storeId,
      sku: "",
    }
    //alert(JSON.stringify(newAffiliate))
    logCheckout("Applying affiliate", newAffiliate);
    addAffiliate(newAffiliate)
  }

  const handleAffiliateDropdownChange = (event) => {
    const newValue = event.target.value;
    logCheckout("Affiliate dropdown changed", { value: newValue });
    setAffiliateCodeInput(event.target.value, () => {
      applyAffiliate()
    })
  }

  // Handle address field changes
  const handleAddressChange = (e) => {
    const { name, value } = e.target;
    logCheckout(`Updating address field: ${name}=${value}`);
    setAddressData(prev => {
      const updated = {
        ...prev,
        [name]: value
      };
      logCheckout("Updated address data", updated);
      return updated;
    });
  }

  const handleSubmit = async (event) => {
    event.preventDefault()
    logCheckout("Form submission initiated");
    
    // Log the form submission event with details
    logCheckoutProcess('form_submission', {
      paymentMethod: selectedPaymentMethod,
      currency: selectedCurrency,
      total: totalForCurrentStore,
      orderId: orderId,
      storeId: storeObject.storeId,
      addressData,
      itemCount: filteredCartItems.length
    });
    
    // Your existing submit logic here
  }

  const { numberOfItemsInCart, cart, total } = context
  const cartEmpty = numberOfItemsInCart === 0

  // Add state for unique product types in the cart
  const [uniqueProductTypes, setUniqueProductTypes] = useState([]);

  // Wrapper for groupCartItemsByCurrencyAndType that uses enriched product data
  const getGroupedCartByType = (cart) => {
    // Use enriched products when grouping
    const enrichedCart = cart.map(item => getEnrichedProduct(item));
    return groupCartItemsByCurrencyAndType(enrichedCart);
  };
  
  // Wrapper for getUniqueProductTypes that uses enriched product data
  const getEnrichedUniqueProductTypes = (cart) => {
    // Use enriched products when getting unique types
    const enrichedCart = cart.map(item => getEnrichedProduct(item));
    return getUniqueProductTypes(enrichedCart);
  };
  
  // Add useEffect to handle abandoned checkout recovery
  useEffect(() => {
    const recoverCheckout = async () => {
      if (resume === 'true' && resumeOrderId) {
        logCheckout("Attempting to recover abandoned checkout", { orderId: resumeOrderId });
        setIsRecovering(true);

        try {
          // Load the abandoned checkout data (now async)
          const savedProgress = await CheckoutProgress.loadProgress(resumeOrderId);

          if (savedProgress && savedProgress.progress) {
            logCheckout("Found saved checkout progress", savedProgress);

            // Restore the checkout with recovered data
            const restored = AbandonedCheckoutRecovery.restoreCheckout(savedProgress);

            if (restored.success) {
              setRecoveredData(restored);

              // If we have section data, use it to pre-fill address and other fields
              const progress = savedProgress.progress;

              if (progress.sectionData && progress.sectionData.recipient) {
                const recipientData = progress.sectionData.recipient;
                if (recipientData.address) {
                  setAddressData(recipientData.address);
                  logCheckout("Restored address data", recipientData.address);
                }
              }

              // If payment method was saved, restore it
              if (progress.paymentMethod) {
                setSelectedPaymentMethod(progress.paymentMethod);
                logCheckout("Restored payment method", progress.paymentMethod);
              }

              logCheckout("Successfully restored abandoned checkout", {
                orderId: resumeOrderId,
                restoredAt: restored.restoredAt
              });
            } else {
              logCheckout("Failed to restore checkout", restored);
            }
          } else {
            logCheckout("No saved progress found for order", { orderId: resumeOrderId });
          }
        } catch (error) {
          logCheckoutError('checkout-recovery', 'Error during checkout recovery', {
            orderId: resumeOrderId,
            error: error.message
          });
        } finally {
          setIsRecovering(false);
        }
      }
    };

    recoverCheckout();
  }, [resume, resumeOrderId]);

  // Update useEffect to identify unique product types
  useEffect(() => {
    // Skip processing if cart is empty
    if (!cart || cart.length === 0) {
      setGroupedCart({});
      setGroupTotals({});
      setGroupedCartByType({});
      setUniqueProductTypes([]);
      return;
    }

    // Prevent unnecessary reprocessing
    const processCart = () => {
      // Group cart items by currency
      const grouped = cart.reduce((groups, item) => {
        // Normalize the currency for consistency
        const itemCurrency = normalizeCurrency(item.currency) || "Unknown";

        if (!groups[itemCurrency]) {
          groups[itemCurrency] = {
            currency: itemCurrency,
            items: []
          };
        }

        groups[itemCurrency].items.push(item);
        return groups;
      }, {});
      
      setGroupedCart(grouped);
      
      // Calculate total for each currency group
      const totals = Object.keys(grouped).reduce((totals, currency) => {
        const group = grouped[currency];
        totals[currency] = group.items.reduce(
          (sum, item) => sum + (item.price * item.quantity),
          0
        );
        return totals;
      }, {});
      
      setGroupTotals(totals);
      
      // Handle initial product data loading
      if (Object.keys(productDetails).length === 0) {
        // Do initial grouping without enriched data
        const initialGroupedByType = groupCartItemsByCurrencyAndType(cart);
        setGroupedCartByType(initialGroupedByType);
        
        // Use regular function for product types
        const types = getUniqueProductTypes(cart);
        setUniqueProductTypes(types);
      } else {
        // Use enriched data once product details are loaded
        const groupedByType = getGroupedCartByType(cart);
        setGroupedCartByType(groupedByType);
        
        // Use enriched function for product types
        const types = getEnrichedUniqueProductTypes(cart);
        setUniqueProductTypes(types);
      }
      
      // Get the current product types (either enriched or not)
      const currentProductTypes = Object.keys(productDetails).length > 0 
        ? getEnrichedUniqueProductTypes(cart)
        : getUniqueProductTypes(cart);
      
      // If there are special product types in the cart, ensure productCheckoutVisible is true
      // But only do this once to prevent infinite re-renders
      if (currentProductTypes.some(type => ['SIM', 'CARD', 'TRAVELSIM'].includes(type)) && !productCheckoutVisible) {
        logCheckout("Tìm thấy loại sản phẩm đặc biệt trong giỏ hàng, đảm bảo quy trình thanh toán là hiển thị", {
          foundTypes: currentProductTypes.filter(type => ['SIM', 'CARD', 'TRAVELSIM'].includes(type))
        });
        setProductCheckoutVisible(true);
      }
      
      logCheckout("Nhóm giỏ hàng theo loại tiền tệ và loại sản phẩm", { 
        currencies: Object.keys(grouped),
        totals,
        uniqueProductTypes: currentProductTypes,
        hasProductDetails: Object.keys(productDetails).length > 0
      });
    };

    // Debounce the processing to prevent multiple runs
    const debounceTimer = setTimeout(processCart, 0);
    return () => clearTimeout(debounceTimer);
  }, [cart, productDetails, productCheckoutVisible]); // Add productCheckoutVisible as a dependency

  // Add state to track filtered cart items
  const [filteredCartItems, setFilteredCartItems] = useState([])
  
  // When selected currency or cart changes, update the filtered cart items
  useEffect(() => {
    if (selectedCurrency && cart && cart.length > 0) {
      const itemsWithMatchingCurrency = filterCartByCurrency(cart, selectedCurrency);
      setFilteredCartItems(itemsWithMatchingCurrency);
      logCheckout("Lọc giỏ hàng theo loại tiền tệ", { 
        currency: selectedCurrency,
        totalItems: cart.length,
        filteredItems: itemsWithMatchingCurrency.length
      });
    } else {
      setFilteredCartItems([]);
    }
  }, [selectedCurrency, cart]);

  // Update the calculateTotalForCurrentStore function to use filtered items
  const calculateTotalForCurrentStore = (cart) => {
    // Only calculate total for items with matching currency
    let total = 0;
    for (const item of filteredCartItems) {
      if (item.store === storeObject.storeId) {
        total += item.price * item.quantity;
      }
    }
    logCheckout("Calculated store total for currency", { 
      storeId: storeObject.storeId, 
      currency: selectedCurrency,
      total,
      itemCount: filteredCartItems.length
    });
    return total;
  };

  // Update the totalForCurrentStore calculation
  let totalForCurrentStore = calculateTotalForCurrentStore(cart);

  // Fix orderId generation to prevent hydration mismatches
  const [orderId, setOrderId] = useState("")
  
  useEffect(() => {
    // Only generate orderId on client-side
    if (typeof window !== 'undefined') {
      // Use resumed order ID if available, otherwise generate new one
      const newOrderId = resumeOrderId || `MAG-${Date.now() % 100000}`;
      logCheckout(resumeOrderId ? "Sử dụng ID đơn hàng đã lưu" : "Tạo ID đơn hàng mới", newOrderId);
      setOrderId(newOrderId)
    }
  }, [storeObject, resumeOrderId])

  // Add mobile detection useEffect
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Add search handler function
  const handleSearch = (query) => {
    setSearchQuery(query);
    // Add search logic here if needed
    logCheckout("Search query updated", { query });
  };

  const togglePanel = () => {
    const newState = !panelVisible;
    logCheckout("Chuyển đổi trạng thái hiển thị panel", { from: panelVisible, to: newState });
    setPanelVisible(newState)
  }

  // Add a new state for payment initiated status
  const [paymentInitiated, setPaymentInitiated] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState(null)

  // Add a function to check if an order has already been paid for
  const checkPaymentStatus = (orderId) => {
    logCheckout("Kiểm tra trạng thái thanh toán cho đơn hàng", orderId);
    
    try {
      // Check localStorage for existing orders
      const existingLocalOrders = JSON.parse(localStorage.getItem('orders') || '[]');
      const existingOrder = existingLocalOrders.find(order => order.orderId === orderId);
      
      // Log the payment status check
      logCheckoutProcess('payment_status_check', {
        orderId,
        orderFound: !!existingOrder,
        paymentInitiated: existingOrder?.paymentInitiated || false,
        paymentMethod: existingOrder?.paymentMethod || null,
        status: existingOrder?.status || null
      });
      
      if (existingOrder && existingOrder.paymentInitiated) {
        logCheckout("Đơn hàng đã có thanh toán được khởi tạo", { 
          orderId, 
          paymentMethod: existingOrder.paymentMethod,
          status: existingOrder.status
        });
        return {
          initiated: true,
          method: existingOrder.paymentMethod,
          status: existingOrder.status
        };
      }
      
      return { initiated: false };
    } catch (error) {
      console.error("Lỗi kiểm tra trạng thái thanh toán:", error);
      logCheckout("Lỗi kiểm tra trạng thái thanh toán", error);
      
      // Log the error
      logCheckoutError('payment-status-check', error, { orderId });
      
      return { initiated: false };
    }
  };

  // Modify saveOrderToLocalStorage to mark payment as initiated
  const saveOrderToLocalStorage = async (orderDetails) => {
    logCheckout("Saving order", orderDetails);
    
    try {
      // Build the order object
      const newOrder = {
        orderId: orderDetails.orderId,
        store: storeObject.storeId,
        total: totalForCurrentStore,
        items: filteredCartItems.filter(item => item.store === storeObject.storeId), // Use filtered items
        currency: selectedCurrency, // Add the currency explicitly
        status: orderDetails.status || 'Đang xử lý', // Allow passing custom status
        paymentMethod: selectedPaymentMethod,
        paymentInitiated: true, // Mark that payment has been initiated
        affiliate: affiliateCodeInput || null,
        // Add address data to the order
        billingAddress: orderDetails.billingAddress || addressData,
        createdAt: new Date().toISOString()
      };

      // Log the transaction details
      logTransaction(newOrder.orderId, 'INITIATED', {
        store: newOrder.store,
        total: newOrder.total,
        currency: newOrder.currency,
        paymentMethod: newOrder.paymentMethod,
        items: newOrder.items.map(item => ({
          sku: item.sku,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          currency: item.currency
        })),
        billingAddress: newOrder.billingAddress
      });

      // First, save to localStorage for backward compatibility
      const existingLocalOrders = JSON.parse(localStorage.getItem('orders') || '[]');
      const existingOrderIndex = existingLocalOrders.findIndex(order => order.orderId === orderDetails.orderId);
      
      if (existingOrderIndex >= 0) {
        existingLocalOrders[existingOrderIndex] = {
          ...existingLocalOrders[existingOrderIndex],
          ...newOrder
        };
      } else {
        existingLocalOrders.push(newOrder);
      }
      
      localStorage.setItem('orders', JSON.stringify(existingLocalOrders));
      logCheckout("Lưu đơn hàng vào localStorage cho tương thích với phiên bản cũ");
      
      // Also update our state to reflect payment has been initiated
      setPaymentInitiated(true);
      setPaymentMethod(selectedPaymentMethod);
      
      // Then, save to server via central orders.json API endpoint only
      try {
        // Use the centralized endpoint that only writes to orders.json
        logCheckout("Lưu đơn hàng vào API trung tâm", { endpoint: '/api/order/save-to-central' });
        
        // Log API call details
        logPaymentApiCall(
          'central-api', 
          '/api/order/save-to-central',
          newOrder,
          { method: 'POST' }
        );
        
        const response = await axios.post('/api/order/save-to-central', newOrder);
        
        // Log API response
        logPaymentApiResponse(
          'central-api',
          '/api/order/save-to-central',
          response.data,
          response.status
        );
        
        if (response.status === 200) {
          logCheckout("Order saved to central orders file", response.data);
          
          // Log transaction update
          logTransaction(newOrder.orderId, 'SAVED_TO_CENTRAL', {
            responseStatus: response.status,
            responseData: response.data
          });
        } else {
          logCheckout("Lưu đơn hàng vào file trung tâm thất bại", response.data);
          
          // Log error
          logCheckoutError('api-central', 'Lưu đơn hàng vào file trung tâm thất bại', {
            status: response.status,
            data: response.data,
            order: newOrder
          });
        }
      } catch (apiError) {
        logCheckout("Lỗi API lưu đơn hàng", apiError);
        console.error("Lỗi API lưu đơn hàng:", apiError);
        
        // Log the API error
        logCheckoutError('api-central', apiError, {
          endpoint: '/api/order/save-to-central',
          order: newOrder
        });
      }
      
      // Save address to user profile if address data is available
      if (orderDetails.billingAddress || (addressData.street && addressData.city)) {
        const addressToSave = orderDetails.billingAddress || addressData;
        
        logCheckout("Lưu thông tin địa chỉ vào thông tin khách hàng", addressToSave);
        
        try {
          // Log API call for profile update
          logPaymentApiCall(
            'customer-api',
            '/api/customer/update-profile',
            {
              customerId: getCustomerId(),
              addressData: addressToSave
            },
            { method: 'POST' }
          );
          
          const profileUpdateResult = await saveAddressToProfile(addressToSave);
          
          // Log result of address save
          logCheckoutProcess('address_save', {
            success: profileUpdateResult,
            address: addressToSave
          });
        } catch (addressError) {
          logCheckoutError('save-address', addressError, { address: addressToSave });
        }
      }
      
      return newOrder;
    } catch (error) {
      logCheckout("Lỗi trong saveOrderToLocalStorage", error);
      console.error("Lỗi lưu đơn hàng:", error);
      
      // Log the error
      logCheckoutError('save-order', error, {
        orderId: orderDetails.orderId,
        context: 'saveOrderToLocalStorage'
      });
      
      return null;
    }
  }

  if (orderCompleted) {
    logCheckout("Đơn hàng đã hoàn thành, hiển thị thông báo thành công");
    return (
      <div>
        <h3>Thanks! Your order has been successfully processed.</h3>
      </div>
    )
  }

  // Get the selected payment form component based on the selected payment method
  const SelectedPaymentForm = PaymentFormComponents[selectedPaymentMethod]
  logCheckout("Thành phần thanh toán đã chọn", { 
    method: selectedPaymentMethod, 
    componentExists: !!SelectedPaymentForm,
    availableMethods: Object.keys(PaymentFormComponents)
  });
  
  // If payment method doesn't exist, log the error and fallback to inquiry
  useEffect(() => {
    if (!SelectedPaymentForm && selectedPaymentMethod) {
      logCheckout("Lỗi: Phương thức thanh toán không có thành phần", { 
        selectedMethod: selectedPaymentMethod,
        availableMethods: Object.keys(PaymentFormComponents)
      });
      
      // Only for debugging - log the payment methods
      console.log("DEBUG - Phương thức thanh toán đã chọn:", selectedPaymentMethod);
      console.log("DEBUG - Các thành phần thanh toán có sẵn:", Object.keys(PaymentFormComponents));
      console.log("DEBUG - Phương thức thanh toán ở Taiwan:", getPaymentMethodsByCurrentcy('NT$').map(m => m.id));
      
      // Don't automatically fallback to inquiry anymore
      /* if (Object.keys(PaymentFormComponents).includes('inquiry')) {
        logCheckout("Falling back to inquiry payment method");
        setSelectedPaymentMethod('inquiry');
      } */
    }
  }, [selectedPaymentMethod, SelectedPaymentForm]);

  const isAdvanceMode = () => {
    return advance === "1"
  }

  // Update the useEffect to check for existing payment when component mounts
  useEffect(() => {
    logCheckout("Thành phần thanh toán đã chọn", { 
      storeId: storeObject.storeId,
      orderId,
      selectedPaymentMethod,
      cartSize: numberOfItemsInCart,
      total: totalForCurrentStore
    });
    
    // Check if payment has already been initiated for this order
    if (orderId) {
      const paymentStatus = checkPaymentStatus(orderId);
      if (paymentStatus.initiated) {
        setPaymentInitiated(true);
        setPaymentMethod(paymentStatus.method);
        setErrorMessage(`Thanh toán cho đơn hàng này đã được khởi tại bằng ${paymentStatus.method}. Vui lòng kiểm tra trạng thái đơn hàng của bạn.`);
      }
    }
    
    // Read data from URL parameters if available
    try {
      if (router.query.data) {
        const checkoutData = JSON.parse(decodeURIComponent(router.query.data));
        logCheckout("Phân tích dữ liệu thanh toán từ URL", checkoutData);
        
        // Set currency if provided
        if (checkoutData && checkoutData.currency) {
          setSelectedCurrency(checkoutData.currency);
          logCheckout("Currency loaded from URL", { currency: checkoutData.currency });
        }
        
        // Remove the individual product fetching from URL data
        // We'll use the inventory provider to get all product details at once
        
        // Handle method parameter which could affect checkout process display
        if (checkoutData && checkoutData.method) {
          // Ensure productCheckoutVisible is true for any method
          setProductCheckoutVisible(true);
          logCheckout("Tham số phương thức đã tìm thấy trong URL", { method: checkoutData.method });
          
          // For specific product types, ensure checkout process is visible
          if (checkoutData.method === "standard" && cart && cart.length > 0) {
            // Check for all supported product types
            const hasSpecialProducts = cart.some(item => {
              // Check categories first
              if (item.categories && Array.isArray(item.categories)) {
                const cats = item.categories.map(c => typeof c === 'string' ? c.trim().toUpperCase() : '');
                if (cats.some(c => c === 'SIM' || c === 'CARD' || c === 'TRAVELSIM' || c.includes('SIM'))) {
                  return true;
                }
              }
              
              // Check SKU
              if (item.sku) {
                const sku = item.sku.toUpperCase();
                if (sku.includes('SIM') || sku.includes('CARD') || sku.includes('PIN') || 
                    sku.includes('TOPUP') || sku.includes('ESIM') || sku.includes('ROAMING')) {
                  return true;
                }
              }
              
              // Check name
              if (item.name) {
                const name = item.name.toUpperCase();
                if (name.includes('SIM') || name.includes('MẠNG') || name.includes('CARD') || 
                    name.includes('NẠP') || name.includes('PIN') || name.includes('TOPUP') || 
                    name.includes('TRAVEL') || name.includes('ESIM') || name.includes('ROAMING') || 
                    name.includes('DU LỊCH')) {
                  return true;
                }
              }
              
              return false;
            });
            
            if (hasSpecialProducts) {
              logCheckout("Tìm thấy loại sản phẩm đặc biệt với phương thức chuẩn, đảm bảo quy trình thanh toán là hiển thị");
              setProductCheckoutVisible(true);
            }
          }
        }
      }
    } catch (error) {
      console.error("Lỗi phân tích dữ liệu thanh toán từ URL:", error);
      logCheckout("Lỗi tải dữ liệu từ URL", error);
    }
    
    // Load customer address from profile
    const loadCustomerAddress = async () => {
      try {
        const customerId = getCustomerId();
        
        if (!customerId) {
          logCheckout("Không tìm thấy ID khách hàng, không thể tải địa chỉ");
          return;
        }
        
        logCheckout("Tải địa chỉ khách hàng từ thông tin khách hàng", { customerId });
        
        const response = await axios.get(`/api/customer/get-profile?customerId=${customerId}`);
        
        if (response.status === 200 && response.data.customer) {
          const customerData = response.data.customer;
          
          // Check if addresses array exists in the profile
          if (customerData.addresses && Array.isArray(customerData.addresses)) {
            logCheckout("Địa chỉ khách hàng đã tìm thấy trong thông tin khách hàng", customerData.addresses);
            
            // Find the default address or the most recently used address
            let addressToUse = null;
            
            // First try to find the default address
            addressToUse = customerData.addresses.find(addr => addr.isDefault === true);
            
            // If no default, try to find the most recently used one
            if (!addressToUse) {
              // Sort by lastUsed date (newest first) and take the first one
              const sortedAddresses = [...customerData.addresses]
                .filter(addr => addr.lastUsed)
                .sort((a, b) => new Date(b.lastUsed) - new Date(a.lastUsed));
              
              if (sortedAddresses.length > 0) {
                addressToUse = sortedAddresses[0];
              }
            }
            
            // If still no address, take the first non-empty one
            if (!addressToUse) {
              addressToUse = customerData.addresses.find(addr => addr.street && addr.street.trim() !== '');
            }
            
            // If we found an address, use it
            if (addressToUse) {
              logCheckout("Sử dụng địa chỉ từ thông tin khách hàng", addressToUse);
              
              // Update the address state with the profile data
              setAddressData({
                street: addressToUse.street || '',
                city: addressToUse.city || '',
                district: addressToUse.district || addressToUse.state || '',
                country: addressToUse.country || 'Vietnam'
              });
            } else {
              logCheckout("Không tìm thấy địa chỉ khả dụng trong thông tin khách hàng");
            }
          } else if (customerData.addressData) {
            // Fallback to older addressData format if present
            logCheckout("Sử dụng địa chỉ từ thông tin khách hàng", customerData.addressData);
            
            setAddressData({
              street: customerData.addressData.street || '',
              city: customerData.addressData.city || '',
              district: customerData.addressData.district || '',
              country: customerData.addressData.country || 'Vietnam'
            });
          } else {
            logCheckout("Không tìm thấy địa chỉ trong thông tin khách hàng");
          }
        }
      } catch (error) {
        console.error("Lỗi tải địa chỉ khách hàng:", error);
        logCheckout("Lỗi tải địa chỉ khách hàng", error);
      }
    };
    
    loadCustomerAddress();
    
    // Also load product details right away to ensure we have categories for type detection
    // Use a flag to ensure this only happens once
    const loadProductData = () => {
      if (!isLoadingProducts) {
        loadProductDetailsWithCategories();
      }
    };

    // Only load product details once on mount
    loadProductData();
    
    // Log when component unmounts
    return () => {
      logCheckout("Thành phần thanh toán đã bị hủy");
    };
  }, []); // Empty dependency array ensures this only runs once on mount

  // Get payment methods for the current store's currency
  const getCurrencyForStore = () => {
    return storeObject.currency || 'VND'; // Default to VND if not specified
  };

  const availablePaymentMethods = getPaymentMethodsByCurrentcy(getCurrencyForStore());

  // Filter by allowed payment methods from store configuration
  const filteredPaymentMethods = allowedPaymentMethods
    ? availablePaymentMethods.filter(method => allowedPaymentMethods.includes(method.id))
    : availablePaymentMethods;

  // Update the handleCurrencyChange function to filter cart items
  const handleCurrencyChange = (currency) => {
    logCheckout("Currency changed", { from: selectedCurrency, to: currency });
    setSelectedCurrency(currency);
    
    // Filter cart items by the selected currency
    const itemsWithMatchingCurrency = filterCartByCurrency(cart, currency);
    setFilteredCartItems(itemsWithMatchingCurrency);
    
    // When currency changes, we may need to update available payment methods
    const availablePaymentMethods = getPaymentMethodsByCurrentcy(currency);
    const filteredMethods = allowedPaymentMethods
      ? availablePaymentMethods.filter(method => allowedPaymentMethods.includes(method.id))
      : availablePaymentMethods;
      
    if (filteredMethods.length > 0) {
      // Find if current payment method is available for new currency
      const isCurrentPaymentMethodAvailable = filteredMethods.some(method => 
        method.id === selectedPaymentMethod
      );
      
      if (!isCurrentPaymentMethodAvailable) {
        // If not available, switch to first available method
        setSelectedPaymentMethod(filteredMethods[0].id);
      }
    }
  };

  // Get the current currency and available currencies
  const getCurrentCurrency = () => {
    return selectedCurrency || getCurrencyForStore();
  };
  
  // Filter and get available currencies for this store
  const getAvailableCurrencies = () => {
    // This could be further customized based on store settings
    return AVAILABLE_CURRENCIES;
  };

  // Function to load full product details including categories
  const loadProductDetailsWithCategories = async () => {
    if (!context.cart || context.cart.length === 0 || isLoadingProducts) return;
    
    setIsLoadingProducts(true);
    logCheckout("Tải thông tin sản phẩm với loại sản phẩm từ kho");
    
    try {
      // Get all identifiers from cart
      const skus = context.cart.map(item => item.sku).filter(Boolean);
      const ids = context.cart.map(item => item.id).filter(Boolean);
      
      // Get URL item IDs if available
      const urlItemIds = [];
      if (router.query.data) {
        try {
          const checkoutData = JSON.parse(decodeURIComponent(router.query.data));
          if (checkoutData && checkoutData.items && Array.isArray(checkoutData.items)) {
            checkoutData.items.forEach(id => {
              if (id && !ids.includes(id)) {
                urlItemIds.push(id);
              }
            });
          }
        } catch (error) {
          console.error("Lỗi trích xuất ID sản phẩm từ URL:", error);
        }
      }
      
      logCheckout("Tải kho sản phẩm", { 
        skus, 
        ids, 
        urlItemIds,
        storeId: storeObject.storeId
      });
      
      // Get the complete inventory through the inventory provider
      const inventoryData = await fetchInventory(storeObject.storeId);
      logCheckout("Tải kho sản phẩm hoàn chỉnh", { count: inventoryData.length });
      
      // Create a map of products by SKU and ID for quick lookup
      const detailsMap = {};
      
      // Process inventory data to extract product details
      inventoryData.forEach(product => {
        if (product) {
          // Map by SKU
          if (product.sku) {
            detailsMap[product.sku] = product;
          }
          
          // Also map by ID
          if (product.id) {
            detailsMap[product.id] = product;
          }
        }
      });
      
      // Find matches for cart items
      const matchedItems = new Set();
      
      // Check SKU matches
      skus.forEach(sku => {
        if (detailsMap[sku]) {
          matchedItems.add(sku);
        }
      });
      
      // Check ID matches
      [...ids, ...urlItemIds].forEach(id => {
        if (detailsMap[id]) {
          matchedItems.add(id);
        }
      });
      
      logCheckout("Matched products from inventory", { 
        total: matchedItems.size,
        skuMatches: skus.filter(sku => detailsMap[sku]).length,
        idMatches: [...ids, ...urlItemIds].filter(id => detailsMap[id]).length
      });
      
      // Set the product details with the matched inventory data
      setProductDetails(detailsMap);
      
      // Note: We're not calling setGroupedCartByType directly here anymore
      // The useEffect with [cart, productDetails] dependencies will handle that
      
    } catch (error) {
      logCheckout("Lỗi tải thông tin sản phẩm từ kho", error);
      console.error("Lỗi tải thông tin sản phẩm từ kho:", error);
    } finally {
      setIsLoadingProducts(false);
    }
  };
  
  // Get product with complete details including categories
  const getEnrichedProduct = (item) => {
    if (productDetails[item.sku]) {
      // Merge cart item with product details but let cart item properties take precedence
      return {
        ...productDetails[item.sku],
        ...item
      };
    }
    return item;
  };

  return (
    <>
      {/* <ProfileLink /> */}
      
      {/* <ProfileLink store={storeObject.storeId} sku="" />{" "} */}
      
      {/* Add responsive headers */}
      {storeObject?.layouttemplate === 'shein' && isMobile && (
        <MobileHeader 
          store={storeObject.storeId}
          currentstore={storeObject}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          inventory={storeObject.inventory || []}
        />
      )}

      {storeObject?.layouttemplate === 'shein' && !isMobile && (
        <DesktopHeader 
          store={storeObject.storeId}
          currentstore={storeObject}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          inventory={storeObject.inventory || []}
        />
      )}
      
      {/* Need to decide what to do if the cart has more products */}
      <div className="flex flex-col items-center pb-10 min-h-screen overflow-auto">
        <Head>
          <title>{`ShopMe - đặt hàng`}</title>
          <meta name="description" content={`Check out`} />
          <meta property="og:title" content="ShopMe - Checkout" key="title" />
          <style jsx global>{`
            .no-scrollbar {
              -ms-overflow-style: none;  /* Internet Explorer and Edge */
              scrollbar-width: none;  /* Firefox */
            }
            .no-scrollbar::-webkit-scrollbar {
              display: none;  /* Chrome, Safari, Opera */
            }
          `}</style>
        </Head>
        <div className="flex flex-col w-full c_large:w-c_large px-4 md:px-8 lg:pl-16 lg:pr-4 overflow-visible">
          <div className="pt-10 pb-8">
            <h1
              className="text-3xl md:text-5xl font-light mb-4 md:mb-6"
              style={{ textTransform: "uppercase" }}
            >
              ĐƠN HÀNG: {orderId}
            </h1>
            <Link href={`/${storeObject.storeId}/cart`} aria-label="Cart">
              <div className="cursor-pointer flex items-center">
                <FaLongArrowAltLeft className="mr-2 text-gray-600" />
                <p className="text-gray-600 text-sm">QUAY LẠI</p>
              </div>
            </Link>
          </div>

          {/* Recovery notification banner */}
          {(resume === 'true' || recoveredData) && (
            <div className="mb-6 bg-blue-50 border-l-4 border-blue-500 p-4 rounded">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Đã khôi phục quá trình thanh toán
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      Chúng tôi đã khôi phục thông tin từ phiên thanh toán trước đó của bạn. 
                      Bạn có thể tiếp tục từ nơi đã dừng lại hoặc thay đổi thông tin nếu cần.
                    </p>
                    {recoveredData && (
                      <p className="mt-1 text-xs">
                        Khôi phục lúc: {new Date(recoveredData.restoredAt).toLocaleString('vi-VN')}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Loading indicator during recovery */}
          {isRecovering && (
            <div className="mb-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="animate-spin h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Đang khôi phục thông tin thanh toán...
                  </p>
                </div>
              </div>
            </div>
          )}

          {cartEmpty ? (
            <h3>Không có mặt hàng nào trong giỏ hàng. Đăng nhập hoặc đăng ký để đặt hàng và thanh toán.</h3>
          ) : (
            <div className="flex flex-col">
              {/* Display products grouped by currency and type */}
              <div className="mb-10">
                {Object.keys(groupedCartByType).length > 0 ? (
                  Object.keys(groupedCartByType).map(currency => {
                    const currencyGroup = groupedCartByType[currency];
                    const isSelectedCurrency = currency === selectedCurrency;
                    
                    // Calculate total for this currency
                    const currencyTotal = Object.values(currencyGroup.types).reduce((total, typeGroup) => {
                      return total + typeGroup.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                    }, 0);
                    
                    return (
                      <div key={currency} className="mb-8">
                        {/* Currency group header */}
                        <div 
                          className={`p-2 md:p-3 mb-3 md:mb-4 rounded flex items-center justify-between ${
                            isSelectedCurrency ? 'bg-gray-100 border border-gray-300' : 'bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center">
                            <span className="text-gray-700 font-medium">{currency}</span>
                            <span className="ml-2 bg-white text-black text-xs rounded-full px-2 py-0.5">
                              {Object.values(currencyGroup.types).reduce((count, typeGroup) => count + typeGroup.items.length, 0)} sản phẩm
                            </span>
                            {isSelectedCurrency && (
                              <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">
                                Đã chọn
                              </span>
                            )}
                          </div>
                          <span className="text-sm font-bold">
                            {currency} {currencyTotal.toLocaleString()}
                          </span>
                        </div>
                        
                        {/* Show products for all currencies - removed conditional check */}
                        <div className="space-y-6 md:space-y-8">
                          {/* Show all products for this currency */}
                          <div className="border rounded-lg overflow-hidden">
                            <div className="bg-gray-50 p-2 md:p-3 border-b flex justify-between items-center">
                              <h3 className="text-sm md:font-medium">Sản phẩm ({Object.values(currencyGroup.types).reduce((count, typeGroup) => count + typeGroup.items.length, 0)})</h3>
                              <span className="text-xs md:text-sm font-semibold">{currency} {currencyTotal.toLocaleString()}</span>
                            </div>
                            
                            <div className="p-2 md:p-4">
                              <div className="space-y-3 md:space-y-4 mb-4">
                                {/* Display products by type first */}
                                {Object.keys(currencyGroup.types).map(productType => {
                                  const typeGroup = currencyGroup.types[productType];
                                  const storeItems = typeGroup.items.filter(item => item.store === storeObject.storeId);
                                  
                                  if (storeItems.length === 0) return null;
                                  
                                  return (
                                    <div key={productType} className="mb-4 md:mb-6">
                                      <h4 className="font-medium mb-2 md:mb-3 text-gray-700">{productType} ({storeItems.length})</h4>
                                      <div className="space-y-3 md:space-y-4 pl-2 md:pl-4">
                                        {                                        storeItems.map((item, index) => {
                                          return (
                                            <div className="border-b py-2" key={index}>
                                              <div className="flex">
                                                <div className="w-12 h-12 md:w-16 md:h-16 rounded overflow-hidden flex-shrink-0 bg-white border border-gray-200">
                                                  <Image
                                                    src={getProductImage(item.image, item.sku, item.name)}
                                                    alt={item.name}
                                                    className="object-cover w-full h-full"
                                                    width={64}
                                                    height={64}
                                                    onError={(e) => handleImageError(e, item.sku, item.name)}
                                                  />
                                                </div>
                                                <div className="flex-1 min-w-0 pl-2 md:pl-4">
                                                  <p className="font-medium text-sm md:text-base truncate">{item.name}</p>
                                                  <p className="text-xs md:text-sm text-gray-600">SKU: {item.sku}</p>
                                                  
                                                  {/* Display product type with reason */}
                                                  <div className="text-xs md:text-sm">
                                                    <span className="text-blue-600 font-medium">
                                                      Type: {ProductType.detect(getEnrichedProduct(item)).type}
                                                    </span>
                                                    <span className="text-gray-500 ml-1">
                                                      ({ProductType.detect(getEnrichedProduct(item)).reason})
                                                    </span>
                                                    
                                                    {/* Product type analysis steps */}
                                                    <div className="mt-1 text-gray-600 text-xs bg-gray-50 p-1 rounded">
                                                      <div className="font-medium mb-0.5">Analysis:</div>
                                                      <ol className="list-decimal pl-4">
                                                        {ProductType.detect(getEnrichedProduct(item)).analysis && 
                                                          ProductType.detect(getEnrichedProduct(item)).analysis.map((step, i) => (
                                                            <li key={i} className="mb-0.5">{step}</li>
                                                          ))
                                                        }
                                                      </ol>
                                                    </div>
                                                  </div>
                                                  
                                                  <div className="flex justify-between items-center mt-1 md:mt-2">
                                                    <p className="text-xs md:text-sm text-gray-900">{item.currency}{item.price} x {item.quantity}</p>
                                                    <p className="text-sm md:text-base font-semibold">{item.currency}{(item.price * item.quantity).toLocaleString()}</p>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          );
                                        })}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                              
                              {/* Show ProductCheckoutSinglePage for all currencies - restructured to single page */}
                              {productCheckoutVisible && (
                                <div className="p-2 md:p-4 pt-0 overflow-visible">
                                  <ProductCheckoutSinglePage
                                    allProductTypes={Object.keys(currencyGroup.types)} // Pass all product types in this currency
                                    allProducts={Object.values(currencyGroup.types).flatMap(group =>
                                      group.items.filter(item => item.store === storeObject.storeId)
                                    )} // All products for this currency
                                    groupedProducts={Object.entries(currencyGroup.types).reduce((acc, [type, group]) => {
                                      acc[type] = group.items.filter(item => item.store === storeObject.storeId);
                                      return acc;
                                    }, {})} // Products grouped by type
                                    orderId={orderId}
                                    storeObject={storeObject}
                                    addressData={addressData}
                                    onAddressChange={handleAddressChange}
                                    saveAddressToProfile={saveAddressToProfile}
                                    selectedPaymentMethod={selectedPaymentMethod}
                                    setSelectedPaymentMethod={setSelectedPaymentMethod}
                                    selectedCurrency={currency} // Use this currency instead of selectedCurrency
                                    totalForCurrentStore={currencyTotal}
                                    filteredCartItems={Object.values(currencyGroup.types).flatMap(group =>
                                      group.items.filter(item => item.store === storeObject.storeId)
                                    )}
                                    cart={cart}
                                    context={context}
                                    orderCompleted={orderCompleted}
                                    setOrderCompleted={setOrderCompleted}
                                    allowedPaymentMethods={allowedPaymentMethods}
                                    saveOrderToLocalStorage={saveOrderToLocalStorage}
                                    paymentInitiated={paymentInitiated}
                                    paymentMethod={paymentMethod}
                                    recoveredData={recoveredData} // Pass recovered data for section restoration
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-10">
                    <p className="text-gray-500">Không có sản phẩm trong giỏ hàng</p>
                  </div>
                )}
              </div>

              <div className="w-full border-t border-gray-300 my-4"></div>

              {/* Address form section */}


              <div className="flex flex-wrap justify-between py-8"
                style={{ justifyContent: "flex-end" }}
              >
                {/* Panel 1: AFFILIATE related buttons and boxes */}
                {/* <AffiliatePanel
                  storeId={storeObject.storeId}
                  isAdvanceMode={isAdvanceMode()}
                  emptyAffiliateArray={emptyAffiliateArray}
                  applyAffiliate={applyAffiliate}
                  affiliateCodeInput={affiliateCodeInput}
                  handleAffiliateDropdownChange={handleAffiliateDropdownChange}
                  handleAffiliateCodeInputChange={handleAffiliateCodeInputChange}
                  affiliate={affiliate}
                  affiliateIds={affiliateIds}
                />
 */}
                {/* Panel 2: VOUCHER related button and input box - REMOVED as it's already in Cart page */}
                {/* <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-4 mb-4 md:mb-0 boxstyle_6">
                  <h2 className="text-lg font-bold mb-4">VOUCHER</h2>
                  <div className="flex flex-col md:flex-row">
                    <div className="mb-4 md:mb-0 md:mr-4">
                      <Button
                        title="ÁP DỤNG"
                        onClick={applyDiscount}
                        small={true}
                      />
                    </div>
                    <input
                      type="text"
                      id="discountCode"
                      name="discountCode"
                      value={discountCode}
                      onChange={handleDiscountCodeChange}
                      className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-3 mb-2 md:mb-0 md:mr-2 border border-gray-300 rounded-md"
                      placeholder={"VOUCHER"}
                    />
                  </div>
                </div> */}

                {/* Panel 3: PRICE AND GPS etc */}
                {/* <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-4 boxstyle_6"> */}
              </div>
              <div className="w-full md:w-full lg:w-full xl:w-full p-4 boxstyle_6">
                {/* <h2 className="text-lg font-bold mb-4">PRICE AND GPS</h2> */}

                <div className="flex items-center">
                  <p className="text-sm pr-10">
                    {storeObject.storemessage ? storeObject.storemessage : ""}
                  </p>
                </div>
                <div className="flex flex-col items-center md:flex-row">
                  {/* <div className="flex items-center mb-2">
                    <div className="boxstyle_1">
                      <p
                        className="text-sm pr-10"
                        style={{ textTransform: "uppercase" }}
                      >
                        MÃ ĐƠN HÀNG{" "}
                        <b>
                          <u>{orderId}</u>
                        </b>{" "}
                        SHOP{" "}
                        <b>
                          <u>{storeObject.storeId}:</u>
                        </b>
                      </p>
                    </div>
                  </div> */}
                  {/* <div className="flex flex-wrap md:flex-row flex-col justify-end">
                    <div className="mb-2 ml-4 boxstyle_6">
                      <p className="font-semibold tracking-wide mr-2">
                        TỔNG: {DENOMINATION + totalForCurrentStore.toLocaleString()}
                      </p>
                    </div>
                  </div> */}
                </div>
              </div>
              
              <div className="w-full border-t border-gray-300 my-4"></div>
              
              {/* Currency Selection */}
              {!cartEmpty && !productCheckoutVisible && (
                <div className="w-full px-2 md:px-4 mb-4 md:mb-6">
                  <div className="text-gray-700 font-medium mb-2 md:mb-3">Tiền tệ thanh toán</div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 md:gap-3">
                    {getAvailableCurrencies().map(currency => {
                      // Count items in this currency for the badge
                      const itemsCount = filterCartByCurrency(cart, currency.code).length;
                      
                      return (
                        <button
                          key={currency.code}
                          className={`border rounded-lg p-2 md:p-3 flex flex-col items-center justify-center ${
                            selectedCurrency === currency.code ? 
                            'border-black bg-gray-50' : 
                            'border-gray-200 hover:bg-gray-50'
                          } ${itemsCount === 0 ? 'opacity-50' : ''}`}
                          onClick={() => handleCurrencyChange(currency.code)}
                          disabled={itemsCount === 0}
                        >
                          <span className="text-base md:text-lg font-bold">{currency.symbol}</span>
                          <span className="text-xs md:text-sm text-gray-600 mt-1">{currency.name}</span>
                          {itemsCount > 0 && (
                            <span className="mt-1 bg-black text-white text-[10px] md:text-xs rounded-full px-2 py-0.5">
                              {itemsCount}
                            </span>
                          )}
                        </button>
                      );
                    })}
                  </div>
                  
                  {/* Add a notice about separate currency payments */}
                  <div className="mt-3 md:mt-4 bg-blue-50 border-l-4 border-blue-500 p-2 md:p-3 text-xs md:text-sm text-blue-700">
                    <p className="font-medium mb-1">Thông báo về tiền tệ</p>
                    <p>Các sản phẩm chỉ hiển thị khi bạn chọn đúng loại tiền tệ mà chúng được niêm yết.</p>
                    <p>Sản phẩm với các loại tiền tệ khác nhau cần được thanh toán riêng biệt.</p>
                  </div>
                </div>
              )}

              {/* Show currency filter results */}
              {!cartEmpty && !productCheckoutVisible && (
                <div className="w-full px-2 md:px-4 mb-3 md:mb-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-base md:text-lg font-medium">
                      Sản phẩm ({filteredCartItems.length}/{cart.length})
                    </h3>
                    <div className="text-xs md:text-sm text-gray-600">
                      Đang hiển thị sản phẩm dùng {selectedCurrency}
                    </div>
                  </div>
                  
                  {filteredCartItems.length === 0 && cart.length > 0 && (
                    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-2 md:p-3 mt-2 md:mt-3 mb-4">
                      <p className="text-xs md:text-sm text-yellow-700">
                        Không có sản phẩm nào được niêm yết bằng {selectedCurrency}. Vui lòng chọn tiền tệ khác.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Payment Section */}
              {!cartEmpty && !productCheckoutVisible && paymentInitiated && (
                <div className="mb-4 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        <strong>Chú ý:</strong> Đơn hàng này đã được thanh toán bằng phương thức {paymentMethod}. Vui lòng không thanh toán lại.
                      </p>
                      <p className="mt-1 text-sm text-yellow-700">
                        Để kiểm tra trạng thái đơn hàng hoặc tạo đơn hàng mới, vui lòng liên hệ với cửa hàng.
                      </p>
                    </div>
                  </div>
                </div>
              )}
              {!cartEmpty && !productCheckoutVisible && (
                <PaymentSection
                  selectedCurrency={selectedCurrency}
                  selectedPaymentMethod={selectedPaymentMethod}
                  setSelectedPaymentMethod={setSelectedPaymentMethod}
                  totalForCurrentStore={totalForCurrentStore}
                  orderId={orderId}
                  filteredCartItems={filteredCartItems}
                  storeObject={storeObject}
                  saveOrderToLocalStorage={saveOrderToLocalStorage}
                  addressData={addressData}
                  handleAddressChange={handleAddressChange}
                  saveAddressToProfile={saveAddressToProfile}
                  cart={cart}
                  productCheckoutVisible={productCheckoutVisible}
                  handleCurrencyChange={handleCurrencyChange}
                  context={context}
                  orderCompleted={orderCompleted}
                  setOrderCompleted={setOrderCompleted}
                  allowedPaymentMethods={allowedPaymentMethods}
                  paymentInitiated={paymentInitiated}
                  paymentMethod={paymentMethod}
                />
              )}

              {errorMessage && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">{errorMessage}</span>
                  <span className="absolute top-0 bottom-0 right-0 px-4 py-3" onClick={() => {
                    logCheckout("Error message dismissed");
                    setErrorMessage(null);
                  }}>
                    <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <title>Close</title>
                      <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                    </svg>
                  </span>
                </div>
              )}
            
              {/* <div className="flex justify-center mt-6">
                <button 
                  className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
                  onClick={() => {
                    logCheckout("Back to cart button clicked");
                    router.push(`/${storeObject.storeId}/cart`);
                  }}
                >
                  Quay lại giỏ hàng
                </button>
              </div> */}
            </div>
          )}
        </div>
      </div>
      
      {/* Add a link to checkout logs for the store owner */}
      {false && (
      <div className="text-center mt-8 mb-4 text-sm text-gray-500">
        <Link href={`/${storeObject.storeId}/logs/checkout`} className="text-blue-600 hover:underline">
          View Checkout Logs
        </Link>
      </div>
      )}
    </>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const stores = allstores.map((store) => store.name)
  const paths = []
  allstores.forEach((store) => {
    const storeId = store.storeId
    paths.push({
      params: {
        store: storeId,
      },
    })
  })
  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const inventory = await fetchInventory()
  const storeId = params.store // Extract storeId from the URL path params
  const store = allstores.find((store) => store.storeId === storeId)

  const inventoryCategorized = inventory.reduce((acc, next) => {
    const categories = next.categories
    categories.forEach((c) => {
      const index = acc.findIndex((item) => item.name === c)
      if (index !== -1) {
        const item = acc[index]
        item.itemCount = item.itemCount + 1
        acc[index] = item
      } else {
        const item = {
          name: c,
          image: next.image, //todo: not image[0]?
          itemCount: 1,
        }
        acc.push(item)
      }
    })
    return acc
  }, [])

  return {
    props: {
      storeObject: store,
    },
  }
}

export default CheckoutWithContext

