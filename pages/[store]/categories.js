import Head from 'next/head'
import { titleIfy , slugify } from '../../utils/helpers'
import { DisplayMedium } from '../../components'
import CartLink from '../../components/CartLink'
import { fetchStore } from '../../utils/storeProvider'
import { useRouter } from "next/router";
import categorygroupings from '../../utils/categoryGrouping';

function Categories ({ categories = [] }) {
  const router = useRouter();
  const { store } = router.query;
  return (
    <>
      <div className="w-full">
        <CartLink />
        <Head>
          <title>ShopMe</title>
          <meta name="description" content={`All categories`} />
          <meta property="og:title" content="All Categories" key="title" />
        </Head>
        <div className="
          pt-4 sm:pt-10 pb-8
        ">
          <h1 className="text-5xl font-light">DANH MỤC</h1>
        </div>
        <div className="flex flex-col items-center">
          
          {/* <div className="my-4 lg:my-8 flex flex-col lg:flex-row justify-between"> */}
          <div className="grid gap-4
          lg:grid-cols-5 md:grid-cols-4 grid-cols-1">
          {
            categories.map((category, index) => (
              <DisplayMedium
                key={index}
                imageSrc={category.image}
                subtitle={`${category.itemCount} sản phẩm`}
                title={titleIfy(category.name)}
                link={`/${store}/category/${slugify(category.name)}`}
              />
            ))
          }
          </div>
        </div>
      </div>
    </>
  )
}

export async function getStaticPaths () {
  //const categories = await fetchCategories()  
  const allstores = await fetchStore();
  const stores = allstores.map(store => store.name);
  /* const categoriesarray = await fetchCategories();
  const categories = categoriesarray.map(category => category.slug);
  const categoriesNames = categoriesarray.map(category => category.name);
 */
  const paths = [];
  allstores.forEach(store => {
    const storeId = store.storeId;
    paths.push({
      params: {
        store: storeId
      }
    });
  });
  /* console.log("paths built under category:")
  console.log(paths) */
  return {
    paths,
    fallback: false
  }
}

/* export async function getStaticProps() {
  const inventory = await fetchInventory();
  const inventoryCategories = [];

  inventory.forEach((product) => {
    product.categories.forEach((category) => {
      const slugifiedCategoryName = slugify(category);
      const existingCategoryIndex = inventoryCategories.findIndex(
        (item) => item.name === slugifiedCategoryName
      );

      if (existingCategoryIndex !== -1) {
        inventoryCategories[existingCategoryIndex].itemCount += 1;
      } else {
        inventoryCategories.push({
          name: slugifiedCategoryName,
          image:
            Array.isArray(product.image) && product.image.length > 0
              ? product.image[0]
              : 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp',
          itemCount: 1,
        });
      }
    });
  });

  return {
    props: {
      categories: inventoryCategories,
    },
  };
} */


export async function getStaticProps({ params }) {
  const storeId = params.store; // Extract storeId from the URL path params
  const allstores = await fetchStore();
  const store = allstores.find(store => store.storeId === storeId);

  if (!store) {
    throw new Error(`Store with ID ${storeId} not found.`);
  }

  const inventory = (store.inventory || []).filter(item => item.activestatus === "1"); // Get inventory for the specified store

  // Categorize the inventory
  /* let inventoryCategorized = inventory.reduce((acc, next) => {
    const categories = next.categories;
    categories.forEach(c => {
      const index = acc.findIndex(item => item.name === c);
      if (index !== -1) {
        const item = acc[index];
        item.itemCount = item.itemCount + 1;
        acc[index] = item;
      } else {
        const item = {
          name: c,
          image: next.image ? next.image[0] : "", // Use the first image if available
          itemCount: 1
        };
        acc.push(item);
      }
    });
    return acc;
  }, []); */

  let inventoryCategorized = inventory.reduce((acc, next) => {
    const categories = Array.isArray(next.categories) ? next.categories : [];
    categories.forEach(c => {
      const index = acc.findIndex(item => item.name === c);
      if (index !== -1) {
        const item = acc[index];
        item.itemCount = item.itemCount + 1;
        acc[index] = item;
      } else {
        const item = {
          name: c,
          image: next.image && next.image.length > 0 ? next.image[0] :  "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.3.webp",
          itemCount: 1
        };
        acc.push(item);
      }
    });
    return acc;
  }, []);

  function getCategoryBucketName(category) {
    for (const [bucket, values] of Object.entries(categorygroupings)) {
      if (values.includes(category)) {
        return bucket;
      }
    }
    return null; // Return null if category doesn't belong to any bucket
  }
  const sortedCategories = {};
  for (const [bucket, values] of Object.entries(categorygroupings)) {
    sortedCategories[bucket] = inventoryCategorized.filter(cat => getCategoryBucketName(cat.name) === bucket);
    sortedCategories[bucket].sort((a, b) => a.name.localeCompare(b.name)); // Sort within each bucket
  }
  // Get categories that don't belong to any bucket
  const uncategorizedCategories = inventoryCategorized.filter(cat => getCategoryBucketName(cat.name) === null);
  uncategorizedCategories.sort((a, b) => a.name.localeCompare(b.name)); // Sort uncategorized categories
  const finalCategories = Object.values(sortedCategories).reduce((acc, bucketCategories) => {
    acc.push(...bucketCategories);
    return acc;
  }, []);
  finalCategories.push(...uncategorizedCategories); // Append uncategorized categories to the end

  /* inventoryCategorized.sort((a, b) => a.name.localeCompare(b.name)); */

  return {
    props: {
      inventoryData: inventory,
      /* categories: inventoryCategorized */
      categories: finalCategories
    }
  };
}

export default Categories