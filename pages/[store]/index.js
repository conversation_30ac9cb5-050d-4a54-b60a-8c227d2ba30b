import { useRouter } from "next/router"
import { fetchStore } from "../../utils/storeProvider"
import { useEffect, useState, useContext } from "react"
import {
  SiteContext,
  ContextProviderComponent,
} from "../../context/mainContext"
import { useTranslation } from 'react-i18next';
import Head from 'next/head';
import Link from 'next/link';
import LanguageSwitcher from "../../components/LanguageSwitcher";

// Import the new components
import DefaultShopLayout from "../../templates/default/shop"
import MarketplaceShopLayout from "../../templates/marketplace/shop"
import ShopeeShopLayout from "../../templates/shopee/shop"
import Mag1ShopLayout from "../../templates/mag1/shop"
import Mag1ShopLayoutMobile from "../../templates/mag1/shop.mobile"
import SheinShopLayoutMobile from "../../templates/shein/shop.mobile"
import SheinShopLayout from "../../templates/shein/shop"
const Home = ({
  inventoryData = [],
  categories: categoryData = [],
  currentstore,
}) => {
  const router = useRouter()
  const { store } = router.query
  const currentUrl = typeof window !== "undefined" ? window.location.href : ""
  const { t } = useTranslation();

  // Early return if currentstore is not available
  if (!currentstore) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Loading store...</h2>
          <p className="text-gray-600">Please wait while we load the store information.</p>
        </div>
      </div>
    )
  }

  // Process hot products data
  const hotProductSKUs = currentstore.hotProductSKUs || []
  const hotProductsBySKU = {}
  const [filteredProducts, setFilteredProducts] = useState(inventoryData)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [isMobile, setIsMobile] = useState(false);

  // Process inventory data
  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredProducts(inventoryData)
    } else {
      setFilteredProducts(inventoryData.filter(product => 
        product.category === selectedCategory
      ))
    }
  }, [selectedCategory, inventoryData])

  inventoryData.forEach((product) => {
    if (hotProductSKUs.includes(product.sku)) {
      hotProductsBySKU[product.sku] = product
    }
  })
  const hotProducts = Object.values(hotProductsBySKU)

  // Get template from store settings or use default
  const template = currentstore?.layouttemplate || 'default'

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // Check initially
    checkMobile();
    
    // Add resize listener
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Render different layouts based on template
  const renderLayout = () => {
    const layoutProps = {
      currentstore,
      hotProducts,
      store,
      currentUrl,
      hotProductSKUs,
      hotProductsBySKU,
      products: filteredProducts,
      selectedCategory,
      setSelectedCategory,
      categories: categoryData,
      languageSwitcher: <LanguageSwitcher />
    }

    switch(template) {
      case 'marketplace':
        return <MarketplaceShopLayout {...layoutProps} />

      case 'shopee':
        return <ShopeeShopLayout {...layoutProps} />
      
      case 'mag1':
        return isMobile ? <Mag1ShopLayoutMobile {...layoutProps} /> : <Mag1ShopLayout {...layoutProps} />

      case 'shein':
        return isMobile ? <SheinShopLayoutMobile {...layoutProps} /> : <SheinShopLayout {...layoutProps} />

      default:
        return <DefaultShopLayout {...layoutProps} />
    }
  }

  return (
    <div>
      {/* <div className="fixed top-4 right-4 z-50">
        <LanguageSwitcher />
      </div> */}
      {renderLayout()}
    </div>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const paths = allstores.map((store) => ({
    params: { store: store.storeId }
  }))

  return {
    paths,
    fallback: false
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const store = allstores.find((s) => s.storeId === params.store)
  
  // Get inventory data for the store
  const inventory = (store.inventory || []).filter(item => item.activestatus === "1")

  return {
    props: {
      currentstore: store,
      inventoryData: inventory,
      categories: [], // Add real categories data if needed
    }
  }
}

const HomeWithContext = (props) => {
  return (
    <div>
      <ContextProviderComponent>
        <SiteContext.Consumer>
          {(context) => <Home {...props} context={context} />}
        </SiteContext.Consumer>
      </ContextProviderComponent>
    </div>
  )
}

export default HomeWithContext
