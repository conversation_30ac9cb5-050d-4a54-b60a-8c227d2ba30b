import Head from "next/head"
import CartLink from "../../components/CartLink"
import { fetchStore } from "../../utils/storeProvider"
import { useRouter } from "next/router"
import { useState, useEffect, useRef } from "react"
import MobileHeader from '../../templates/shein/components/MobileHeader'
import DesktopHeader from '../../templates/shein/components/DesktopHeader'
import React from "react"
import { motion, AnimatePresence, useScroll, useTransform, useMotionValue, useSpring } from "framer-motion"
import { 
  FaSearch, 
  FaShoppingCart, 
  FaCreditCard, 
  FaTruck, 
  FaCheckCircle, 
  FaHeadset, 
  FaHome, 
  FaFilter, 
  FaStar, 
  FaBox, 
  FaAngleDown, 
  FaAngleUp, 
  FaMapMarkerAlt, 
  FaMoneyBillWave, 
  FaShippingFast, 
  FaExchangeAlt,
  FaUserCog,
  FaArrowDown,
  FaRegLightbulb,
  FaShoppingBag,
  Fa<PERSON>witter,
  FaInstagram,
  FaFacebook,
  FaYoutube
} from "react-icons/fa"

export default function ShoppingGuidePage({ currentstore }) {
  const router = useRouter()
  const { store } = router.query
  const [isMobile, setIsMobile] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false)
  const [expandedSections, setExpandedSections] = useState({})
  const [activeSection, setActiveSection] = useState(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const scrollRef = useRef(null)
  const { scrollYProgress } = useScroll()
  const parallaxY = useTransform(scrollYProgress, [0, 1], [0, -100])
  const opacity = useTransform(scrollYProgress, [0, 0.2], [1, 0])
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 }
  const springScroll = useSpring(scrollYProgress, springConfig)

  // Mouse move handler for 3D effect
  const handleMouseMove = (e) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // Check initially
    checkMobile();
    
    // Set all sections to expanded initially
    const initialExpanded = {};
    sections.forEach((_, index) => {
      initialExpanded[index] = false;
    });
    // Start with the first section expanded
    initialExpanded[0] = true;
    setExpandedSections(initialExpanded);
    
    // Add resize listener
    window.addEventListener('resize', checkMobile);
    
    // Add mousemove listener for 3D effects
    window.addEventListener('mousemove', handleMouseMove);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
    // Implement search functionality if needed
  };

  // Toggle section expansion
  const toggleSection = (index) => {
    setExpandedSections(prev => {
      const newState = {...prev};
      Object.keys(newState).forEach(key => {
        newState[key] = parseInt(key) === index ? !prev[index] : false;
      });
      return newState;
    });
    setActiveSection(index);
    
    // Scroll to the section
    const sectionElement = document.getElementById(`section-${index}`);
    if (sectionElement) {
      sectionElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Particle animation config
  const generateParticles = () => {
    const particles = [];
    const icons = [
      <FaShoppingBag key="bag" className="text-blue-300" />,
      <FaShoppingCart key="cart" className="text-pink-300" />,
      <FaStar key="star" className="text-yellow-300" />,
      <FaBox key="box" className="text-purple-300" />,
      <FaCreditCard key="card" className="text-green-300" />,
      <FaTruck key="truck" className="text-red-300" />
    ];
    
    for (let i = 0; i < 15; i++) {
      const size = Math.random() * 12 + 8;
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      const duration = Math.random() * 20 + 10;
      const delay = Math.random() * 5;
      const rotate = Math.random() * 360;
      const iconIndex = Math.floor(Math.random() * icons.length);
      
      particles.push(
        <motion.div
          key={i}
          className="absolute z-0 opacity-40"
          style={{ 
            left: `${x}%`, 
            top: `${y}%`,
            fontSize: size
          }}
          animate={{ 
            y: [0, -20, 0],
            rotate: [rotate, rotate + 10, rotate],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{ 
            duration: duration, 
            repeat: Infinity,
            delay: delay,
            ease: "easeInOut"
          }}
        >
          {icons[iconIndex]}
        </motion.div>
      );
    }
    
    return particles;
  };

  const sections = [
    {
      title: "Tìm kiếm sản phẩm",
      icon: <FaSearch className="text-blue-500" />,
      color: "from-blue-400 to-blue-600",
      shadowColor: "rgba(59, 130, 246, 0.5)",
      items: [
        {
          subtitle: "Trang Chủ / Gợi Ý",
          icon: <FaHome className="text-green-500" />,
          content:
            "Người dùng mở ứng dụng và thường thấy các sản phẩm được gợi ý trên trang chủ, dựa vào lịch sử mua hàng, các xu hướng thịnh hành hoặc các chương trình khuyến mãi."
        },
        {
          subtitle: "Tìm Kiếm",
          icon: <FaSearch className="text-purple-500" />,
          content:
            "Người dùng có thể nhập từ khóa tìm kiếm vào ô tìm kiếm để tìm sản phẩm mong muốn, hoặc lọc theo danh mục."
        },
        {
          subtitle: "Danh Mục và Bộ Lọc",
          icon: <FaFilter className="text-orange-500" />,
          content:
            "Ứng dụng cung cấp các bộ lọc để người dùng có thể lựa chọn theo các tiêu chí như giá cả, đánh giá, thương hiệu, hoặc các đặc tính khác."
        }
      ]
    },
    {
      title: "Xem Chi Tiết Sản Phẩm",
      icon: <FaBox className="text-indigo-500" />,
      color: "from-indigo-400 to-indigo-600",
      shadowColor: "rgba(99, 102, 241, 0.5)",
      items: [
        {
          subtitle: "Trang Chi Tiết Sản Phẩm",
          icon: <FaBox className="text-indigo-500" />,
          content:
            "Khi người dùng chọn sản phẩm, họ sẽ được đưa đến trang chi tiết của sản phẩm đó, nơi cung cấp thông tin về mô tả sản phẩm, hình ảnh, giá cả, đánh giá của người dùng khác, và các tùy chọn."
        },
        {
          subtitle: "Đánh Giá và Nhận Xét",
          icon: <FaStar className="text-yellow-500" />,
          content:
            "Phần đánh giá và nhận xét giúp người dùng đưa ra quyết định mua hàng dựa trên kinh nghiệm của những người đã mua trước."
        },
        {
          subtitle: "Video Demo hoặc Ảnh 360 Độ",
          icon: <FaBox className="text-pink-500" />,
          content:
            "Một số sản phẩm có thể có video hướng dẫn sử dụng hoặc ảnh 360 độ để người dùng dễ dàng hình dung hơn."
        }
      ]
    },
    {
      title: "Thêm Sản Phẩm Vào Giỏ Hàng",
      icon: <FaShoppingCart className="text-green-500" />,
      color: "from-green-400 to-green-600",
      shadowColor: "rgba(16, 185, 129, 0.5)",
      items: [
        {
          subtitle: "Chọn Số Lượng",
          icon: <FaShoppingCart className="text-gray-500" />,
          content: "Người dùng có thể chọn số lượng sản phẩm muốn mua."
        },
        {
          subtitle: "Thêm Vào Giỏ Hàng",
          icon: <FaShoppingCart className="text-green-500" />,
          content:
            "Khi quyết định mua sản phẩm, người dùng sẽ nhấn nút \"Thêm vào giỏ hàng\" để lưu lại sản phẩm trước khi thanh toán."
        },
        {
          subtitle: "Kiểm Tra Giỏ Hàng",
          icon: <FaShoppingCart className="text-purple-500" />,
          content:
            "Người dùng có thể xem lại giỏ hàng của mình, kiểm tra các sản phẩm đã chọn, điều chỉnh số lượng hoặc xóa sản phẩm không muốn mua."
        }
      ]
    },
    {
      title: "Thanh Toán",
      icon: <FaCreditCard className="text-pink-500" />,
      color: "from-pink-400 to-pink-600",
      shadowColor: "rgba(244, 114, 182, 0.5)",
      items: [
        {
          subtitle: "Chọn Phương Thức Thanh Toán",
          icon: <FaMoneyBillWave className="text-green-500" />,
          content:
            "Sau khi kiểm tra giỏ hàng, người dùng sẽ chọn phương thức thanh toán, như thẻ tín dụng, ví điện tử, COD (thanh toán khi nhận hàng), hoặc chuyển khoản ngân hàng."
        },
        {
          subtitle: "Địa Chỉ Giao Hàng",
          icon: <FaMapMarkerAlt className="text-red-500" />,
          content:
            "Người dùng sẽ nhập hoặc chọn địa chỉ giao hàng, nếu chưa có thì có thể thêm địa chỉ mới."
        },
        {
          subtitle: "Chọn Phương Thức Giao Hàng",
          icon: <FaShippingFast className="text-purple-500" />,
          content:
            "Người dùng có thể chọn các phương thức giao hàng như giao hàng nhanh, giao hàng tiêu chuẩn, hoặc giao hàng miễn phí tùy vào ứng dụng và chính sách của cửa hàng."
        }
      ]
    },
    {
      title: "Xác Nhận Đơn Hàng",
      icon: <FaCheckCircle className="text-orange-500" />,
      color: "from-orange-400 to-orange-600",
      shadowColor: "rgba(251, 146, 60, 0.5)",
      items: [
        {
          subtitle: "Kiểm Tra Thông Tin Đơn Hàng",
          icon: <FaCheckCircle className="text-orange-500" />,
          content:
            "Trước khi xác nhận, người dùng sẽ được yêu cầu kiểm tra lại các thông tin về sản phẩm, địa chỉ giao hàng, phương thức thanh toán, và giá trị đơn hàng."
        },
        {
          subtitle: "Mã Giảm Giá/Khuyến Mãi",
          icon: <FaMoneyBillWave className="text-yellow-500" />,
          content:
            "Nếu có mã giảm giá hoặc chương trình khuyến mãi, người dùng có thể nhập mã để được giảm giá hoặc nhận ưu đãi."
        },
        {
          subtitle: "Xác Nhận Đơn Hàng",
          icon: <FaCheckCircle className="text-green-500" />,
          content:
            "Sau khi kiểm tra và đồng ý, người dùng sẽ nhấn nút \"Xác Nhận Đặt Hàng\" để hoàn tất quá trình thanh toán."
        }
      ]
    },
    {
      title: "Giao Hàng và Nhận Hàng",
      icon: <FaTruck className="text-purple-500" />,
      color: "from-purple-400 to-purple-600",
      shadowColor: "rgba(139, 92, 246, 0.5)",
      items: [
        {
          subtitle: "Xử Lý Đơn Hàng",
          icon: <FaBox className="text-blue-400" />,
          content:
            "Ứng dụng sẽ cập nhật tình trạng đơn hàng (đang xử lý, đã giao hàng, v.v.) và người dùng có thể theo dõi quá trình giao hàng qua hệ thống."
        },
        {
          subtitle: "Nhận Hàng",
          icon: <FaTruck className="text-green-500" />,
          content:
            "Khi sản phẩm được giao đến địa chỉ đã chỉ định, người dùng sẽ nhận hàng và có thể kiểm tra tình trạng sản phẩm."
        },
        {
          subtitle: "Đánh Giá Sản Phẩm",
          icon: <FaStar className="text-yellow-500" />,
          content:
            "Sau khi nhận hàng, người dùng có thể đánh giá và viết nhận xét về sản phẩm đã mua."
        }
      ]
    },
    {
      title: "Hỗ Trợ Sau Mua",
      icon: <FaHeadset className="text-teal-500" />,
      color: "from-teal-400 to-teal-600",
      shadowColor: "rgba(45, 212, 191, 0.5)",
      items: [
        {
          subtitle: "Chính Sách Đổi Trả",
          icon: <FaExchangeAlt className="text-indigo-500" />,
          content:
            "Nếu sản phẩm không phù hợp hoặc bị lỗi, người dùng có thể yêu cầu đổi trả hàng theo chính sách của ứng dụng."
        },
        {
          subtitle: "Hỗ Trợ Khách Hàng",
          icon: <FaUserCog className="text-purple-500" />,
          content:
            "Trong trường hợp có bất kỳ vấn đề nào với đơn hàng, người dùng có thể liên hệ bộ phận chăm sóc khách hàng của ứng dụng để giải quyết."
        }
      ]
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 40, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  const contentVariants = {
    hidden: { height: 0, opacity: 0 },
    visible: { 
      height: "auto", 
      opacity: 1,
      transition: { duration: 0.5, ease: "easeInOut" }
    }
  };

  const heroVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const calculate3DTransform = (index) => {
    if (typeof window === 'undefined') return {};
    
    const card = document.getElementById(`section-${index}`);
    if (!card) return {};
    
    const rect = card.getBoundingClientRect();
    const cardCenterX = rect.left + rect.width / 2;
    const cardCenterY = rect.top + rect.height / 2;
    
    // Calculate rotation based on mouse position relative to card center
    const rotateY = (mousePosition.x - cardCenterX) / 20;
    const rotateX = (cardCenterY - mousePosition.y) / 20;
    
    return {
      transform: `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`,
      transition: 'all 0.1s linear'
    };
  };

  // Progress bar styles
  const progressBarHeight = useTransform(springScroll, [0, 1], [0, 100]);

  return (
    <>
      <CartLink />
      <div className="w-full">
        <Head>
          <title>Hướng Dẫn Mua Sắm - SIM Online Store</title>
          <meta name="description" content="Hướng dẫn mua sắm trên SIM Online Store" />
          <meta property="og:title" content="Hướng Dẫn Mua Sắm - SIM Online Store" key="title" />
        </Head>
      </div>

      {currentstore?.layouttemplate === 'shein' && isMobile && (
        <MobileHeader 
          store={store}
          currentstore={currentstore}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}

      {currentstore?.layouttemplate === 'shein' && !isMobile && (
        <DesktopHeader 
          store={store}
          currentstore={currentstore}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}

      {/* Progress bar */}
      <motion.div 
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 z-50"
        style={{ scaleX: springScroll, transformOrigin: "0%" }}
      />

      {/* Floating navigation dots */}
      <div className="fixed right-5 top-1/2 transform -translate-y-1/2 z-40 hidden md:block bg-white/70 backdrop-blur-sm p-2 rounded-full shadow-lg border border-gray-200">
        {sections.map((section, index) => (
          <motion.div
            key={index}
            className={`my-3 cursor-pointer transition-all duration-300 rounded-lg overflow-hidden border ${activeSection === index ? 'border-' + section.color.split('-')[1] : 'border-gray-200'}`}
            whileHover={{ 
              scale: 1.2,
              boxShadow: `0 5px 10px -2px ${section.shadowColor}`
            }}
            onClick={() => toggleSection(index)}
          >
            <div 
              className={`w-4 h-4 flex items-center justify-center ${activeSection === index ? 'bg-gradient-to-br ' + section.color + ' text-white' : 'bg-gray-100'}`}
            >
              {activeSection === index && <div className="w-1 h-1 bg-white rounded-full"></div>}
            </div>
          </motion.div>
        ))}
      </div>

      <div className="min-h-screen w-full overflow-hidden relative" ref={scrollRef}>
        {/* Floating particles background */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          {generateParticles()}
        </div>

        {/* Hero section with parallax */}
        <motion.div 
          className="relative min-h-[70vh] flex flex-col items-center justify-center overflow-hidden bg-white"
          style={{ y: parallaxY }}
        >
          {/* Yellow navigation bar */}
          {/* <div className="absolute top-0 w-full bg-yellow-300 p-4 flex items-center z-10">
            <button className="mr-6 text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            <div className="flex space-x-6 text-gray-700 font-medium">
              <span>HOME</span>
              <span>SERVICE</span>
              <span>ABOUT</span>
              <span>CONTACT</span>
            </div>
            <div className="ml-auto relative">
              <input type="search" className="rounded-full px-4 py-1 w-40 focus:outline-none" placeholder="Search..." />
              <button className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div> */}

          <div className="w-full max-w-6xl mx-auto bg-white p-8 pt-24 flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 space-y-6">
              <h2 className="text-lg font-medium uppercase text-gray-700">SHOPPING CENTER</h2>
              <h1 className="text-5xl md:text-6xl font-bold text-pink-500">
              SIM & MORE
              </h1>
              <p className="text-gray-600 max-w-md">
                Khám phá trải nghiệm mua sắm trực tuyến tuyệt vời với SIM Online Store. Tìm hiểu các bước mua sắm dễ dàng và tiện lợi.
              </p>
              {/* <button className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-3 rounded-full font-medium transition duration-200">
                MORE INFO
              </button> */}
              {/* <div className="flex space-x-4 pt-4">
                <a href="#" className="text-blue-800"><FaTwitter size={20} /></a>
                <a href="#" className="text-blue-800"><FaInstagram size={20} /></a>
                <a href="#" className="text-blue-800"><FaFacebook size={20} /></a>
                <a href="#" className="text-blue-800"><FaYoutube size={20} /></a>
              </div> */}
            </div>
            
            <div className="md:w-1/2 relative mt-8 md:mt-0">
              <motion.div 
                className="relative"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                {/* Shopping cart and bags illustration */}
                <div className="relative">
                  <div className="absolute -top-12 -right-6 bg-pink-500 text-white px-3 py-1 rounded-md transform rotate-12">
                    <span className="font-bold">TÍCH ĐIỂM</span>
                  </div>
                  <div className="absolute -top-8 -left-12 bg-purple-500 text-white px-3 py-1 rounded-md transform -rotate-12">
                    <span className="font-bold">KHUYẾN MÃI</span>
                  </div>
                  <div className="w-56 h-56 bg-blue-100 rounded-full flex items-center justify-center border-4 border-blue-200">
                    <FaShoppingCart className="text-blue-500 text-6xl" />
                  </div>
                  <div className="absolute -bottom-10 -left-8">
                    <FaShoppingBag className="text-pink-400 text-5xl" />
                  </div>
                  <div className="absolute -right-10 top-10">
                    <FaShoppingBag className="text-yellow-400 text-5xl" />
                  </div>
                  <div className="absolute bottom-10 -right-8">
                    <FaBox className="text-purple-400 text-4xl" />
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          <div className="absolute bottom-0 w-full h-12 bg-yellow-300"></div>
        </motion.div>

        <motion.div 
          className="min-h-screen bg-gradient-to-b from-blue-50 to-white pt-12 pb-24 relative"
        >
          {/* Tooltip floating component */}
          <motion.div 
            className="hidden md:flex absolute left-10 top-32 p-4 bg-white/90 backdrop-blur-md rounded-xl shadow-lg max-w-xs z-30 border-2 border-blue-100"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
            whileHover={{ scale: 1.05, boxShadow: "0 20px 25px -5px rgba(59, 130, 246, 0.3), 0 10px 10px -5px rgba(59, 130, 246, 0.2)" }}
          >
            <div className="flex items-start">
              <div className="p-3 rounded-full bg-blue-100 mr-3 shadow-inner border border-blue-200">
                <FaRegLightbulb className="text-blue-500 text-xl" />
              </div>
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-100">
                <p className="text-gray-700 text-justify">Nhấn vào các mục để xem thông tin chi tiết hơn về quy trình mua sắm!</p>
              </div>
            </div>
          </motion.div>

          <motion.div 
            className="flex flex-col p-6 gap-10 max-w-5xl mx-auto text-gray-800 font-sans relative z-10"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {sections.map((section, index) => (
              <motion.div 
                id={`section-${index}`}
                key={index} 
                className={`bg-white rounded-2xl p-6 border-2 border-gray-200 transition-all duration-300 overflow-hidden backdrop-blur-sm`}
                variants={itemVariants}
                style={activeSection === index ? calculate3DTransform(index) : {}}
                whileHover={{ 
                  boxShadow: `0 20px 25px -5px ${section.shadowColor}, 0 10px 10px -5px rgba(0, 0, 0, 0.04)`,
                }}
              >
                <motion.div 
                  className="flex items-center justify-between cursor-pointer group"
                  onClick={() => toggleSection(index)}
                  whileHover={{ scale: 1.01 }}
                  layoutId={`section-header-${index}`}
                >
                  <div className="flex items-center gap-5">
                    <div className={`p-4 rounded-2xl bg-gradient-to-br ${section.color} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      {section.icon}
                    </div>
                    <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-700 to-gray-900">
                      {section.title}
                    </h2>
                  </div>
                  <motion.div 
                    className={`p-2 rounded-full bg-gray-100 text-gray-500 group-hover:bg-blue-100 group-hover:text-blue-500 transition-colors duration-300 border border-gray-200`}
                    whileHover={{ rotate: expandedSections[index] ? -180 : 180 }}
                    animate={{ rotate: expandedSections[index] ? 180 : 0 }}
                  >
                    <FaAngleDown size={20} />
                  </motion.div>
                </motion.div>

                <AnimatePresence>
                  {expandedSections[index] && (
                    <motion.div 
                      className="mt-6 pl-4 pt-4 border-l-2 border-t-2 border-blue-200 rounded-bl-xl"
                      variants={contentVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      layoutId={`section-content-${index}`}
                    >
                      <div className="flex flex-col gap-5 bg-blue-50/30 p-4 rounded-xl backdrop-blur-sm">
                        {section.items.map((item, idx) => (
                          <motion.div 
                            key={idx}
                            className="bg-white p-5 rounded-xl shadow-md border border-gray-200"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: idx * 0.15, duration: 0.5 }}
                            whileHover={{ 
                              scale: 1.02, 
                              boxShadow: `0 15px 25px -5px ${section.shadowColor}, 0 8px 10px -6px rgba(0, 0, 0, 0.1)`,
                            }}
                          >
                            <div className="flex items-center gap-3 mb-4">
                              <div className="p-3 bg-gradient-to-br from-gray-50 to-white rounded-full shadow-md">
                                {item.icon}
                              </div>
                              <h3 className="font-semibold text-lg text-gray-800">{item.subtitle}</h3>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                              <p className="text-gray-700 leading-relaxed text-base text-justify">{item.content}</p>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </>
  );
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const paths = []
  allstores.forEach((store) => {
    const storeId = store.storeId
    paths.push({
      params: {
        store: storeId,
      },
    })
  })
  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const storeId = params.store
  const store = allstores.find((store) => store.storeId === storeId)

  if (!store) {
    throw new Error(`Store with ID ${storeId} not found.`)
  }

  return {
    props: {
      currentstore: store,
    },
  }
} 