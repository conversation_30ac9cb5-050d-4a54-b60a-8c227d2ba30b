import Head from "next/head"
import {
  Tag,
  DisplaySmall,
  DisplayMedium,
  DisplayMediumNoBox,
} from "../../components"
import { titleIfy, slugify } from "../../utils/helpers"
import CartLink from "../../components/CartLink"
import { fetchStore } from "../../utils/storeProvider"
import { useRouter } from "next/router"
import Map from "../../components/Map"
import ReactMarkdown from "react-markdown"
import StoreIntroSection from "../../components/StoreIntroSection"

const Home = ({
  currentstore = "",
  inventoryData = [],
  categories: categoryData = [],
}) => {
  const hotProductSKUs =
    currentstore.hotProductSKUs !== null &&
    currentstore.hotProductSKUs !== undefined &&
    currentstore.hotProductSKUs !== ""
      ? currentstore.hotProductSKUs
      : []
  const hotCategoriesArray =
    currentstore.hotCategories !== null &&
    currentstore.hotCategories !== undefined &&
    currentstore.hotCategories !== ""
      ? currentstore.hotCategories
      : ["0", "1"]
  const hotProductsBySKU = {}
  inventoryData.forEach((product) => {
    if (hotProductSKUs.includes(product.sku)) {
      hotProductsBySKU[product.sku] = product
    }
  })

  const router = useRouter()
  const { store } = router.query

  let latitude = 21.028251553342592
  let longitude = 105.85235060315165

  if (currentstore.locations) {
    const latLongArr = currentstore.locations[0].latlong.split(",")
    latitude = parseFloat(latLongArr[0])
    longitude = parseFloat(latLongArr[1])
  }

  return (
    <>
      <CartLink />
      <div className="w-full">
        <Head>
          <title>SIM Online Store</title>
          <meta name="description" content="SIM Online Store" />
          <meta property="og:title" content="SIM Online Store" key="title" />
        </Head>
      </div>
      <StoreIntroSection 
        currentstore={currentstore}
        latitude={latitude}
        longitude={longitude}
      />
      <div className="my-8 flex flex-col lg:flex-row justify-center">
        <DisplayMediumNoBox
          imageSrc={
            currentstore.shopLinkQRCode !== null &&
            currentstore.shopLinkQRCode !== undefined &&
            currentstore.shopLinkQRCode !== ""
              ? currentstore.shopLinkQRCode
              : "/mag.logo.1.png"
          }
          //link={currentstore.shopLinkQRCode !== null && currentstore.shopLinkQRCode !== undefined && currentstore.shopLinkQRCode !== "" ? currentstore.shopLinkQRCode : '/payment_qrcode_thanhson_tcb.jpg'}
          link=""
          buttonText="QR WEBSITE"
        />
        <DisplayMediumNoBox
          imageSrc={
            currentstore.paymentQRs[0] !== null &&
            currentstore.paymentQRs[0] !== undefined &&
            currentstore.paymentQRs[0] !== ""
              ? currentstore.paymentQRs[0]
              : "/mag.logo.1.png"
          }
          link={
            currentstore.paymentQRs[0] !== null &&
            currentstore.paymentQRs[0] !== undefined &&
            currentstore.paymentQRs[0] !== ""
              ? currentstore.paymentQRs[0]
              : "/mag.logo.1.png"
          }
          buttonText="CHUYỂN KHOẢN"
        />
        <DisplayMediumNoBox
          imageSrc={
            currentstore.qr_line !== null &&
            currentstore.qr_line !== undefined &&
            currentstore.qr_line !== ""
              ? currentstore.qr_line
              : "/mag.logo.1.png"
          }
          link={
            currentstore.qr_line !== null &&
            currentstore.qr_line !== undefined &&
            currentstore.qr_line !== ""
              ? currentstore.qr_line
              : "/mag.logo.1.png"
          }
          buttonText="QR LINE"
        />
      </div>
      <div
        className="
        lg:my-8 lg:grid-cols-2
        grid-cols-1
        grid gap-4 my-4
      "
      >
        <>
          {hotCategoriesArray.map((category, index) => {
            const categoryItem = categoryData.find(
              (item) => item.name === category
            )
            return (
              <div key={index}>
                {categoryItem ? (
                  <DisplayMedium
                    /* imageSrc={categoryItem.image} */
                    imageSrc = {categoryItem.image ? categoryItem.image : "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.3.webp"}
                    /* imageSrc = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.3.webp" */
                    subtitle={`${categoryItem.itemCount} sản phẩm`}
                    title={titleIfy(categoryItem.name)}
                    link={`/${store}/category/${slugify(categoryItem.name)}`}
                  />
                ) : (
                  categoryData[index] && (
                    <DisplayMedium
                      /* imageSrc={categoryData[index].image} */
                      imageSrc={categoryData[index].image ? categoryData[index].image : "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.3.webp"}
                      subtitle={`${categoryData[index].itemCount} sản phẩm`}
                      title={titleIfy(categoryData[index].name)}
                      link={`/${store}/category/${slugify(
                        categoryData[index].name
                      )}`}
                    />
                  )
                )}
              </div>
            )
          })}
        </>
      </div>
      <div className="my-8 flex flex-col lg:flex-row justify-between">
        {hotProductSKUs.slice(0, 4).map((sku, index) => {
          const product = hotProductsBySKU[sku]
          if (
            !product ||
            !product.image ||
            !product.categories ||
            product.image.length === 0
          ) {
            // Return null or another component to handle the case where product data is incomplete
            return null
          }
          return (
            <DisplaySmall
              key={index}
              imageSrc={
                Array.isArray(product.image) && product.image.length > 0 && product.image[0]
                  ? product.image[0]
                  : "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp"
              }
              title={product.name}
              subtitle={product.categories[0]}
              link={`/${store}/product/${slugify(product.sku)}`}
            />
          )
        })}
      </div>
    </>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const stores = allstores.map((store) => store.name)
  const paths = []
  allstores.forEach((store) => {
    const storeId = store.storeId
    paths.push({
      params: {
        store: storeId,
      },
    })
  })
  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const storeId = params.store // Extract storeId from the URL path params
  // console.log("storeId storeId storeId storeId storeId storeId storeId ")
  // console.log(storeId)
  const store = allstores.find((store) => store.storeId === storeId)

  if (!store) {
    throw new Error(`Store with ID ${storeId} not found.`)
  }

  const inventory = (store.inventory || []).filter(
    (item) => item.activestatus === "1"
  ) // Get inventory for the specified store

  // Categorize the inventory
  const inventoryCategorized = inventory.reduce((acc, next) => {
    const categories = next.categories
    categories.forEach((c) => {
      const index = acc.findIndex((item) => item.name === c)
      if (index !== -1) {
        const item = acc[index]
        item.itemCount = item.itemCount + 1
        acc[index] = item
      } else {
        const item = {
          name: c,
          /* image: next.image ? next.image[0] : "", */ 
          image: next.image && next.image[0] ? next.image[0] : "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.3.webp",
          itemCount: 1,
        }
        acc.push(item)
      }
    })
    return acc
  }, [])

  return {
    props: {
      currentstore: store,
      inventoryData: inventory,
      categories: inventoryCategorized,
    },
  }
}

export default Home
