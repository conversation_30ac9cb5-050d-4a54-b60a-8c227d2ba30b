import Head from "next/head"
import { fetchStore } from "../../utils/storeProvider"
import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import StoreIntroSection from "../../components/StoreIntroSection"
import MobileHeader from '../../templates/shein/components/MobileHeader'
import DesktopHeader from '../../templates/shein/components/DesktopHeader'

const AppPolicy = ({ currentstore = "" }) => {
  const router = useRouter()
  const { store } = router.query
  const [language, setLanguage] = useState('vi') // Default to Vietnamese
  const [isMobile, setIsMobile] = useState(false)

  let latitude = 21.028251553342592
  let longitude = 105.85235060315165

  if (currentstore.locations) {
    const latLongArr = currentstore.locations[0].latlong.split(",")
    latitude = parseFloat(latLongArr[0])
    longitude = parseFloat(latLongArr[1])
  }

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // Check initially
    checkMobile();
    
    // Add resize listener
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
    }
  }, []);

  return (
    <>
      <div className="w-full">
        <Head>
          <title>{language === 'vi' ? 'Chính sách ứng dụng - SIM Online Store' : 'App Policy - SIM Online Store'}</title>
          <meta name="description" content={language === 'vi' ? 'Chính sách quyền riêng tư và sử dụng ứng dụng SIM Online Store' : 'Privacy and app usage policy for SIM Online Store'} />
          <meta property="og:title" content={language === 'vi' ? 'Chính sách ứng dụng - SIM Online Store' : 'App Policy - SIM Online Store'} key="title" />
        </Head>
      </div>
      
      {/* Header Components */}
      {isMobile ? (
        <MobileHeader 
          store={store}
          currentstore={currentstore}
          onSearch={() => {}} 
          searchQuery="" 
          showCategoriesPanel={false}
          setShowCategoriesPanel={() => {}}
        />
      ) : (
        <DesktopHeader 
          store={store}
          currentstore={currentstore}
          onSearch={() => {}} 
          searchQuery="" 
          showCategoriesPanel={false}
          setShowCategoriesPanel={() => {}}
        />
      )}

      <div className="container mx-auto py-10 px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          {/* Language Switcher */}
          <div className="mb-6 flex justify-end">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setLanguage('vi')}
                className={`px-4 py-2 rounded-md transition-colors ${
                  language === 'vi'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Tiếng Việt
              </button>
              <button
                onClick={() => setLanguage('en')}
                className={`px-4 py-2 rounded-md transition-colors ${
                  language === 'en'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                English
              </button>
            </div>
          </div>

          <main className="prose prose-lg max-w-none">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">
              {language === 'vi' ? 'Chính sách ứng dụng' : 'App Policy'}
            </h1>
            
            {language === 'vi' ? (
              // Vietnamese Content
              <>
                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Giới thiệu</h2>
                  <p className="text-gray-700 leading-relaxed">
                    Tại SIM (&quot;SIM&quot;, &quot;chúng tôi&quot;), chúng tôi mong muốn cung cấp dịch vụ tốt nhất cho bạn. Để làm được điều này,
                    chúng tôi cần thu thập một số dữ liệu, bao gồm cả thông tin cá nhân, có liên quan đến dịch vụ mà chúng tôi cung cấp.
                    Tuy nhiên, quyền riêng tư và bảo mật dữ liệu cá nhân của bạn luôn là ưu tiên hàng đầu của chúng tôi.
                    Chính sách quyền riêng tư này (&quot;Chính sách&quot;) sẽ giải thích chi tiết cách thức và lý do chúng tôi thu thập,
                    lưu trữ, chia sẻ và sử dụng dữ liệu cá nhân của bạn.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Về Chính sách này</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">Chính sách này giải thích chi tiết về việc thu thập, lưu trữ, chia sẻ và sử dụng dữ liệu cá nhân của bạn. Chính sách này áp dụng cho tất cả các dịch vụ do SIM cung cấp.</p>
                  <p className="text-gray-700 leading-relaxed mb-4">Nếu có dịch vụ nào được cung cấp với một chính sách bảo mật riêng, thì chính sách bảo mật riêng đó sẽ được ưu tiên áp dụng, còn những điều không được đề cập trong chính sách riêng đó sẽ tuân theo Chính sách này.</p>
                  <p className="text-gray-700 leading-relaxed">Nếu bạn có bất kỳ câu hỏi, khiếu nại hoặc đề xuất nào, vui lòng xem phần &quot;Liên hệ với chúng tôi&quot; bên dưới. Nếu bạn không đồng ý với nội dung của Chính sách này, bạn có quyền quyết định có tiếp tục sử dụng dịch vụ của SIM hay không.</p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Dữ liệu cá nhân mà chúng tôi thu thập từ bạn</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">Chúng tôi có thể cần bạn cung cấp một số thông tin cá nhân để có thể thực hiện các dịch vụ theo yêu cầu của bạn. Bạn có quyền từ chối cung cấp thông tin, nhưng trong nhiều trường hợp, điều đó đồng nghĩa với việc chúng tôi không thể cung cấp dịch vụ cho bạn.</p>
                  <p className="text-gray-700 leading-relaxed mb-4">Các thông tin chúng tôi thu thập bao gồm:</p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-2">
                    <li>Thông tin bạn cung cấp: Họ tên, số điện thoại, địa chỉ, số chứng minh thư/căn cước công dân, email, ID mạng xã hội.</li>
                    <li>Thông tin chúng tôi thu thập từ việc sử dụng dịch vụ của bạn: Cài đặt ứng dụng (khu vực, ngôn ngữ, múi giờ), ID ứng dụng, phiên bản ứng dụng, phiên bản hệ điều hành, nhật ký hoạt động (IP, thời gian truy cập, thời gian đăng ký).</li>
                    <li>Thông tin từ đối tác bên thứ ba: Chúng tôi có thể nhận thông tin từ đối tác quảng cáo hoặc đối tác hợp tác với chúng tôi để cung cấp dịch vụ.</li>
                    <li>Thông tin không thuộc dữ liệu cá nhân: ID thiết bị, nhật ký truy cập, thời gian sử dụng dịch vụ.</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Cách chúng tôi sử dụng thông tin cá nhân đã thu thập</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">Mục đích thu thập dữ liệu cá nhân bao gồm:</p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-2">
                    <li>Cung cấp dịch vụ cho bạn</li>
                    <li>Xác minh danh tính</li>
                    <li>Ngăn chặn gian lận</li>
                    <li>Bảo vệ hệ thống</li>
                    <li>Cung cấp các chương trình khuyến mãi phù hợp</li>
                    <li>Cá nhân hóa nội dung</li>
                    <li>Chẩn đoán và sửa lỗi hệ thống</li>
                    <li>Gửi thông báo đẩy</li>
                    <li>Cải thiện trải nghiệm người dùng</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Cách chúng tôi chia sẻ dữ liệu cá nhân với đối tác thứ ba</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">Chúng tôi không bán dữ liệu cá nhân của bạn. Tuy nhiên, trong một số trường hợp, chúng tôi có thể chia sẻ dữ liệu với:</p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-2">
                    <li>Công ty trong cùng tập đoàn với SIM</li>
                    <li>Nhà cung cấp dịch vụ và đối tác kinh doanh</li>
                    <li>Đối tác phân tích dữ liệu và đo lường hiệu suất</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Bảo mật</h2>
                  <p className="text-gray-700 leading-relaxed">
                    Chúng tôi áp dụng các biện pháp bảo vệ dữ liệu cá nhân, bao gồm sử dụng công nghệ mã hóa TLS/SSL để bảo vệ kết nối.
                    Tuy nhiên, không có hệ thống nào đảm bảo an toàn tuyệt đối. Nếu bạn nghi ngờ tài khoản của mình bị xâm phạm,
                    vui lòng liên hệ với chúng tôi.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Thời gian lưu trữ dữ liệu</h2>
                  <p className="text-gray-700 leading-relaxed">Chúng tôi sẽ lưu trữ dữ liệu cá nhân của bạn trong khoảng thời gian cần thiết để thực hiện các mục tiêu được nêu trong Chính sách này và theo quy định của pháp luật.</p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Yêu cầu xóa dữ liệu</h2>
                  <p className="text-gray-700 leading-relaxed">
                    Bạn có thể yêu cầu xóa dữ liệu cá nhân của mình, nhưng điều này cũng đồng nghĩa với việc tài khoản của bạn sẽ bị đóng và mọi dữ liệu liên quan (lịch sử mua hàng,...) sẽ bị mất.
                    Trong một số trường hợp, chúng tôi có thể phải giữ lại một số thông tin vì lý do pháp lý (ví dụ: tranh chấp tài khoản, nghĩa vụ thuế, chống gian lận).
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Thông tin từ trẻ em dưới tuổi quy định</h2>
                  <p className="text-gray-700 leading-relaxed">Chúng tôi không cung cấp dịch vụ trực tiếp cho trẻ em dưới tuổi quy định và không cố ý thu thập dữ liệu từ trẻ em. Nếu phát hiện ra dữ liệu trẻ em bị thu thập, chúng tôi sẽ xóa thông tin đó.</p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Thay đổi Chính sách</h2>
                  <p className="text-gray-700 leading-relaxed">
                    Chúng tôi có thể thay đổi Chính sách này bất cứ lúc nào. Mọi thay đổi sẽ được công bố trên trang này và có hiệu lực ngay khi được cập nhật.
                    Chúng tôi khuyến khích bạn kiểm tra Chính sách định kỳ.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Ngôn ngữ áp dụng</h2>
                  <p className="text-gray-700 leading-relaxed">Nếu có bất kỳ xung đột hoặc khác biệt nào giữa các phiên bản dịch của Chính sách này, phiên bản tiếng Việt sẽ được ưu tiên áp dụng.</p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">Liên hệ với chúng tôi</h2>
                  <address className="not-italic text-gray-700 leading-relaxed">
                    <strong className="text-gray-900">Công ty Jing Jia Ltd</strong><br />
                    Điện thoại: 07-5668139<br />
                    Địa chỉ: Số 9, tầng 25, số 3, đường Ziqiang 3rd, quận Lingya, thành phố Cao Hùng, Đài Loan<br />
                    Email: <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800 underline"><EMAIL></a>
                  </address>
                </section>
              </>
            ) : (
              // English Content
              <>
                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">1. Introduction</h2>
                  <p className="text-gray-700 leading-relaxed">
                    In SIM (&quot;SIM&quot;, &quot;we&quot;, or &quot;us&quot;), we want to provide the best possible service for you.
                    To do this we need to gather some data, including personally identifying data, that are relevant with the services we provide.
                    Even so, privacy and the security of your personal data will always be the outmost importance for us.
                    To fulfill our objective, through this Privacy Policy (&quot;Policy&quot;), we will explain in detail how and why we collect, store, share, and use your personal data that you entrust to us.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">2. About this Policy</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    This Policy explains in detail information related to collection, storage, sharing, and usage of personal data that we obtain from you.
                    This Policy applies for all services provided by SIM.
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Should there are services that we offer with a separate privacy policy, that separate privacy policy will prevail,
                    with provisions that are not covered will be covered by this Policy.
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    If you have any question, complaints, or suggestion, you can see the section &apos;Contact Us&apos; below.
                    Or, if you do not agree with the content of this Policy, please remember that it is your option to use the services provided by SIM.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">3. Personal data we collect from you</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    In order to provide our services, we may need you to provide us with personal information that are needed to perform the requested service.
                    You have the right to not provide us the required information; however, in most cases, that means we may not be able to provide services which requires that information to you.
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">Depending on which services you use, we collect these information:</p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-2">
                    <li>
                      <b>3.1 Information that you provided</b> - We may collect personal information that you provided to us which are needed to provide services you requested.
                      For example, in order to use our services, you provided us with your name, phone number, address, identity card number, email address, and your social media ID.
                    </li>
                    <li>
                      <b>3.2 Information that we collect from your usage</b> - We also collect information about your service usage. This information includes, but not limited to:
                      application settings (region, language, timezone), application ID, application version, SDK version, operating system version, and application status (e.g. installing, updating, deleting).
                      We also collect log information and your behavior from our server or third-party, including IP address, log information from usage of services (e.g. access time, registration time, activity time), and which pages and functionality you access.
                    </li>
                    <li>
                      <b>3.3 Information from third-party partners</b> - We also collect information from third-party partners about you, including from advertisers and partners who work with us so that we can provide services for you.
                      We will use these personal information if you have given us or our partners consent to share your data or when we have legitimate reason for using that personal information in providing service to you.
                    </li>
                    <li>
                      <b>3.4 Information which are not considered personal information</b> - We may also collect other information that are not directly tied to your personal information or may not be defined as a personal information according to the local law.
                      This type of information may include device ID, network monitoring data, page access activity, page access duration, etc.
                    </li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">4. How we use personal information we collected</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">Our objective for collecting personal information is to provide our services to you. In addition, those information is also used to ensure we follow the applicable laws and regulations. This includes:</p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-2">
                    <li>Providing services to you</li>
                    <li>Verifying your identity</li>
                    <li>Preventing fraud</li>
                    <li>Protecting and securing the system</li>
                    <li>Providing relevant promotion</li>
                    <li>Offering personalized services and contents</li>
                    <li>Diagnosing and fixing system errors</li>
                    <li>Providing push notification service</li>
                    <li>For marketing and advertising purposes</li>
                    <li>Improving user experience</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">5. How we share personal information to third-party partners</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">We do not sell your personal information to third-party providers. We sometimes need to share your personal information with third-party partners to provide or improve our product or services.</p>
                  <p className="text-gray-700 leading-relaxed mb-2"><b>Our Group Companies</b></p>
                  <p className="text-gray-700 leading-relaxed mb-4">We can share your personal information from time to time to affiliates of SIM.</p>
                  <p className="text-gray-700 leading-relaxed mb-2"><b>Third-party Providers and Business Partners</b></p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    We may use third party companies or individuals in providing our services. If needed, we will share your personal information to our service providers or business partners.
                    We ensure that your information is shared when it is necessary and that the reason is legitimate, specific, and important in order to provide services to you.
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    To perform analysis and measurement of your performance and services, we may provide non-personal information with third parties.
                    This information is used to help either us or third parties in performing the aforementioned measurement and analysis tasks.
                  </p>
                  <p className="text-gray-700 leading-relaxed">We are not responsible for the privacy policies of other organization.</p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">6. Security</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    We are aware of the importance of your personal information. Because of that, we implement appropriate procedures to protect your personal information.
                    To secure your connection to us, we use Transport Layer Security (TLS)/Secure Sockets Layer (SSL) technology. Please ensure that your connection is encrypted through TLS/SSL so that your data is transmitted and received securely.
                    All of your personal information is stored in servers with access control to prevent unauthorized access.
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    However, it is important to remember that there are no perfect secure system. If you feel that your account security is compromised, you can inform us by contacting us according to the &apos;Contact Us&apos; section.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">7. Retention Period</h2>
                  <p className="text-gray-700 leading-relaxed">We may retain your personal information for the required amount of time to fulfill the objectives outlined in this Policy and according to applicable laws and regulations.</p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">8. Data Deletion Request</h2>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    If you request us to delete your data, we will also required to close your account. Your data will then either be deleted or anonymized so that it will no longer identifies you.
                    Note that this means you will lose access to your account and all data associated with your account (such as purchase history) unless you back it up in advance.
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    However, in some cases, we are required to keep some of your personal data for legally justifiable reasons. For example:
                  </p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-2 mb-4">
                    <li>There is an unresolved issue with your account, such as outstanding amount or unresolved dispute</li>
                    <li>For our legal, tax, or accounting obligations</li>
                    <li>For fraud prevention</li>
                  </ul>
                  <p className="text-gray-700 leading-relaxed">
                    To request data deletion, you can either contact us through the information outlined in the &apos;Contact Us&apos; section,
                    or by sending an email to <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800 underline"><EMAIL></a>.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">9. Information from Underage Children</h2>
                  <p className="text-gray-700 leading-relaxed">
                    We do not offer direct services to underage children. In addition, we knowingly do not collect information from underage children.
                    If we discover that we have collected information from underage children, we will delete that information and stop providing service to the child.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">10. Changes to this Policy</h2>
                  <p className="text-gray-700 leading-relaxed">
                    We may change this Policy at any time. Every changes can be seen in this page. Changes to Policy applies from the moment the changes is published in this page.
                    We recommend that you check this page periodically to see whether there are changes to this Policy.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">11. Prevailing language</h2>
                  <p className="text-gray-700 leading-relaxed">
                    This Policy may be translated and executed in multiple languages other than Vietnamese (&quot;Vietnamese&quot;) which taken together is considered as a single Policy.
                    If there are any conflict or difference in meaning in the translated Policy, the vietnamese language version will prevail.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-4">12. Contact Us</h2>
                  <address className="not-italic text-gray-700 leading-relaxed">
                    京佳企業行 Jing jia ltd company<br />
                    07-5668139<br />
                    高雄市苓雅區自強三路3號25樓之9<br />
                    9-25 th Floor, No. 3, Ziqiang 3rd Road, Lingya District, Kaohsiung City, Taiwan.<br />
                    Email: <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800 underline"><EMAIL></a>
                  </address>
                </section>
              </>
            )}
          </main>
        </div>
      </div>
    </>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const paths = []
  allstores.forEach((store) => {
    const storeId = store.storeId
    paths.push({
      params: {
        store: storeId,
      },
    })
  })
  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const storeId = params.store
  const store = allstores.find((store) => store.storeId === storeId)

  if (!store) {
    throw new Error(`Store with ID ${storeId} not found.`)
  }

  return {
    props: {
      currentstore: store,
    },
  }
}

export default AppPolicy
