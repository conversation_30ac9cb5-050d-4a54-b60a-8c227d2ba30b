import Link from "next/link"
import { useState, useEffect, useContext } from "react"
import { FaTimes, FaLongArrowAltRight, FaSpinner, FaLink, FaShoppingCart, FaUser, FaUserPlus, FaArrowRight, FaTags, FaChevronRight, FaCreditCard, FaMoneyBill, FaWallet, FaPaypal, FaCcVisa, FaCcMastercard, FaHistory, FaExclamationTriangle, FaUndo, FaClock, FaCheckCircle } from "react-icons/fa"
import {
  SiteContext,
  ContextProviderComponent,
} from "../../context/mainContext"
import DENOMINATION from "../../utils/currencyProvider"
import { slugify } from "../../utils/helpers"
import QuantityPicker from "../../components/QuantityPicker"
import Image from "../../components/Image"
import Head from "next/head"
import CartLink from "../../components/CartLink"
import UserLocation from "../../utils/userLocation"
import { fetchStore } from "../../utils/storeProvider"
import { useRouter } from "next/router"
import Button from "../../components/Button"
import { getCustomerId, getCustomerInfo, isCustomerAuthenticated, clearCustomerAuth } from "../../utils/customerAuth"
import { 
  CheckoutProgress,
  AbandonedCheckoutRecovery,
  PaymentMonitor
} from "../../utils/checkoutProgress"
import MobileHeader from '../../templates/shein/components/MobileHeader'
import DesktopHeader from '../../templates/shein/components/DesktopHeader'
import PastOrders from '../../components/PastOrders'
// Helper to determine checkout method based on product category
const getCheckoutMethod = (item) => {
  // Extract checkout method from item's categories if available
  if (item.categories && Array.isArray(item.categories)) {
    // Check for specific category keywords that determine checkout method
    if (item.categories.some(cat => cat.toLowerCase().includes('digital') || cat.toLowerCase().includes('subscription'))) {
      return 'digital';
    }
    if (item.categories.some(cat => cat.toLowerCase().includes('physical') || cat.toLowerCase().includes('shipping'))) {
      return 'physical';
    }
    if (item.categories.some(cat => cat.toLowerCase().includes('service') || cat.toLowerCase().includes('booking'))) {
      return 'service';
    }
  }

  // Fallback method based on item type if available
  if (item.type) {
    return item.type.toLowerCase();
  }

  // Default checkout method
  return 'standard';
};

// Helper to get checkout method label
const getCheckoutMethodLabel = (method) => {
  switch (method) {
    case 'digital':
      return 'Digital Goods';
    case 'physical':
      return 'Physical Products';
    case 'service':
      return 'Services';
    default:
      return '-';
  }
};

// Helper to get checkout method icon
const getCheckoutMethodIcon = (method) => {
  switch (method) {
    case 'digital':
      return <FaWallet className="mr-2" />;
    case 'physical':
      return <FaMoneyBill className="mr-2" />;
    case 'service':
      return <FaCreditCard className="mr-2" />;
    default:
      return <FaShoppingCart className="mr-2" />;
  }
};

// Currency-specific payment methods
/* const getPaymentMethodsByCurrentcy = (currency) => {
  switch (currency) {
    case 'VND':
      return [
        { id: 'momo', name: 'MoMo', icon: '💰', bgColor: 'bg-pink-100', available: true },
        { id: 'vnpay', name: 'VNPay', icon: <FaCreditCard className="text-blue-600" />, bgColor: 'bg-blue-50', available: true },
        { id: 'zalopay', name: 'ZaloPay', icon: '💳', bgColor: 'bg-blue-100', available: true },
        { id: 'cod', name: 'Thanh toán khi nhận hàng', icon: <FaMoneyBill className="text-green-600" />, bgColor: 'bg-green-50', available: true },
        { id: 'bank', name: 'Chuyển khoản', icon: <FaWallet className="text-gray-600" />, bgColor: 'bg-gray-50', available: true }
      ];
    case 'USD':
      return [
        { id: 'paypal', name: 'PayPal', icon: <FaPaypal className="text-blue-600" />, bgColor: 'bg-blue-50', available: true },
        { id: 'stripe', name: 'Credit Card', icon: <FaCcVisa className="text-blue-700" />, bgColor: 'bg-gray-50', available: true },
        { id: 'bank', name: 'Bank Transfer', icon: <FaWallet className="text-gray-600" />, bgColor: 'bg-gray-50', available: true },
        { id: 'inquiry', name: 'Request Quote', icon: <FaUser className="text-gray-600" />, bgColor: 'bg-yellow-50', available: true },
        { id: 'cod', name: 'Cash on Delivery', icon: <FaMoneyBill className="text-green-600" />, bgColor: 'bg-green-50', available: true }
      ];
    case 'NT$':
      return [
        { id: 'linepay', name: 'LINE Pay', icon: '💱', bgColor: 'bg-green-50', available: true },
        { id: 'jkopay', name: 'JKO Pay', icon: <FaCreditCard className="text-purple-600" />, bgColor: 'bg-purple-50', available: true },
        { id: '711', name: '7-11 Payment', icon: '🏪', bgColor: 'bg-green-100', available: true },
        { id: 'familymart', name: 'FamilyMart', icon: '🏬', bgColor: 'bg-blue-100', available: true },
        { id: 'taiwanpay', name: 'Taiwan Pay', icon: <FaCcMastercard className="text-red-600" />, bgColor: 'bg-red-50', available: true },
        { id: 'bank', name: 'Bank Transfer', icon: <FaWallet className="text-gray-600" />, bgColor: 'bg-gray-50', available: true },
        { id: 'taiwanpayment', name: 'Taiwan Payment', icon: <FaCreditCard className="text-blue-600" />, bgColor: 'bg-blue-50', available: true }
      ];
    default:
      return [
        { id: 'bank', name: 'Bank Transfer', icon: <FaWallet className="text-gray-600" />, bgColor: 'bg-gray-50', available: true },
        { id: 'card', name: 'Credit Card', icon: <FaCcVisa className="text-blue-700" />, bgColor: 'bg-blue-50', available: true },
        { id: 'inquiry', name: 'Request Quote', icon: <FaUser className="text-gray-600" />, bgColor: 'bg-yellow-50', available: true },
        { id: 'cod', name: 'Cash on Delivery', icon: <FaMoneyBill className="text-green-600" />, bgColor: 'bg-green-50', available: true }
      ];
  }
}; */

// Define available currencies globally
/* const AVAILABLE_CURRENCIES = [
  { code: "VND", symbol: "VND", name: "Vietnamese Dong" },
  { code: "USD", symbol: "$", name: "US Dollar" },
  { code: "NT$", symbol: "NT$", name: "New Taiwan Dollar" },
]; */

// Helper to get all payment methods for all currencies
/* const getAllPaymentMethods = () => {
  const allMethods = {};

  // Collect methods from all currencies
  AVAILABLE_CURRENCIES.forEach(currency => {
    const methodsForCurrency = getPaymentMethodsByCurrentcy(currency.code);
    methodsForCurrency.forEach(method => {
      if (!allMethods[method.id]) {
        allMethods[method.id] = {
          ...method,
          currencies: [currency.code]
        };
      } else {
        allMethods[method.id].currencies.push(currency.code);
      }
    });
  });

  return Object.values(allMethods);
}; */

// Helper to normalize currency codes
const normalizeCurrency = (currency) => {
  if (!currency) return currency;

  // Handle Taiwan Dollar variations
  if (currency === 'NT' || currency === 'NT$' || currency === 'NTD' || currency === 'TWD') {
    return 'NT$';
  }

  return currency;
};

const Cart = ({ context, storeObject = "" }) => {
  const [renderClientSideComponent, setRenderClientSideComponent] =
    useState(false)
  const [selectedCheckoutGroup, setSelectedCheckoutGroup] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [productDetails, setProductDetails] = useState({})
  const [customerInfo, setCustomerInfo] = useState(null)
  const [isLinkingCart, setIsLinkingCart] = useState(false)
  const [linkCartMessage, setLinkCartMessage] = useState(null)
  const [isMobile, setIsMobile] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false)
  const [pastOrders, setPastOrders] = useState([])
  const [isPastOrdersLoading, setIsPastOrdersLoading] = useState(false)
  const [showPastOrders, setShowPastOrders] = useState(false)
  const [duplicatePaymentWarning, setDuplicatePaymentWarning] = useState(null)
  
  // Abandoned checkout states
  const [abandonedCheckouts, setAbandonedCheckouts] = useState([])
  const [showAbandonedCheckouts, setShowAbandonedCheckouts] = useState(false)
  const router = useRouter()
  const { store } = router.query

  // Detect mobile device
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    // Initial check
    checkIfMobile()
    
    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile)
    
    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Function to handle search
  const handleSearch = (query) => {
    setSearchQuery(query)
    if (query && query.trim() !== "") {
      router.push(`/${store}/search?q=${encodeURIComponent(query)}`)
    }
  }

  // Get allowed payment methods from store configuration, if available
  const allowedPaymentMethods = storeObject.paymentmethods ?
    storeObject.paymentmethods.split(',').map(method => method.trim()) :
    null; // null means all payment methods are allowed

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)

      // Load customer info if available
      if (typeof window !== "undefined" && isCustomerAuthenticated()) {
        const info = getCustomerInfo();
        const customerId = getCustomerId();

        setCustomerInfo(info);
        console.log('[CART_PAGE] Customer info loaded:', info);

        // Check if we need to force cart sync with the authenticated user
        if (context.forceCustomerCartSync && customerId) {
          try {
            console.log('[CART_PAGE] Forcing cart sync with customer ID:', customerId);
            context.forceCustomerCartSync(customerId, info);
          } catch (error) {
            console.error('[CART_PAGE] Error syncing cart with customer:', error);
          }
        }

        // Load past orders
        if (customerId) {
          fetchPastOrders(customerId);
        }
      }

      // Load abandoned checkouts
      loadAbandonedCheckouts();

      // Load cart data from server if available
      if (context.loadCartFromServer) {
        try {
          console.log('[CART_PAGE] Loading cart data from server');
          await context.loadCartFromServer()
          console.log('[CART_PAGE] Cart data loaded from server');
        } catch (error) {
          console.error('[CART_PAGE] Error loading cart data from server:', error)
        }
      }

      setRenderClientSideComponent(true)

      setTimeout(() => setIsLoading(false), 300); // Short delay for UI smoothness
    }

    loadData()
  }, [context])

  // Watch cart changes and load product details when cart changes
  useEffect(() => {
    if (context.cart && context.cart.length > 0) {
      loadProductDetails();
      
      // Check for duplicates whenever cart changes
      checkForDuplicatePayments();
    }
  }, [context.cart, pastOrders]);

  // Load product details from inventory
  const loadProductDetails = async () => {
    if (!context.cart || context.cart.length === 0 || !storeObject) return;

    try {
      console.log('[CART_PAGE] Loading product details for SKUs:', context.cart.map(item => item.sku));

      const skus = context.cart.map(item => item.sku);
      const storeId = storeObject.storeId;

      console.log('[CART_PAGE] Calling API with:', { skus, storeId });

      const response = await fetch('/api/products/getBySkus', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ skus, storeId }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[CART_PAGE] Product details loaded:', data);

        // Create a map of SKU to product details
        const detailsMap = {};
        // Check for the expected response format with products array
        if (data && data.products && Array.isArray(data.products)) {
          data.products.forEach(product => {
            if (product && product.sku) {
              detailsMap[product.sku] = product;
            }
          });

          console.log('[CART_PAGE] Processed product details map:', Object.keys(detailsMap));
          setProductDetails(detailsMap);
        } else {
          console.error('[CART_PAGE] Invalid response format from API:', data);
        }
      } else {
        console.error('[CART_PAGE] Failed to load product details:', await response.text());
      }
    } catch (error) {
      console.error('[CART_PAGE] Error loading product details:', error);
    }
  };

  const { numberOfItemsInCart, cart, removeFromCart, total, setItemQuantity, storePathname } =
    context
  const cartEmpty = numberOfItemsInCart === Number(0)

  // Group cart items by currency only (not by checkout method)
  const groupedCart = cart.reduce((groups, item) => {
    // Normalize the currency for consistency
    const itemCurrency = normalizeCurrency(item.currency) || "Unknown";

    if (!groups[itemCurrency]) {
      groups[itemCurrency] = {
        currency: itemCurrency,
        items: []
      };
    }

    groups[itemCurrency].items.push(item);
    return groups;
  }, {});

  // Calculate total for each currency group
  const groupTotals = Object.keys(groupedCart).reduce((totals, currency) => {
    const group = groupedCart[currency];
    totals[currency] = group.items.reduce(
      (sum, item) => sum + (item.price * item.quantity),
      0
    );
    return totals;
  }, {});

  // If we have groups but no selected checkout group, select the first one for checkout purposes
  useEffect(() => {
    if (Object.keys(groupedCart).length > 0) {
      if (!selectedCheckoutGroup || !groupedCart[selectedCheckoutGroup]) {
        setSelectedCheckoutGroup(Object.keys(groupedCart)[0]);
      }
    } else {
      setSelectedCheckoutGroup(null);
    }
  }, [groupedCart, selectedCheckoutGroup]);

  function increment(item) {
    item.quantity = item.quantity + 1
    setItemQuantity(item)
  }

  function decrement(item) {
    if (item.quantity === 1) return
    item.quantity = item.quantity - 1
    setItemQuantity(item)
  }

  // Function to load abandoned checkouts
  const loadAbandonedCheckouts = () => {
    try {
      console.log('[CART_PAGE] Loading abandoned checkouts');
      const abandoned = CheckoutProgress.getAbandonedCheckouts();
      
      if (abandoned.length > 0) {
        console.log('[CART_PAGE] Found abandoned checkouts:', abandoned.length);
        
        // Enrich abandoned checkouts with payment status
        const enrichedCheckouts = abandoned.map(checkout => {
          const paymentStatus = PaymentMonitor.checkPaymentStatus(checkout.orderId);
          return {
            ...checkout,
            paymentStatus,
            hasPaymentInitiated: !!paymentStatus
          };
        });
        
        // Sort by timestamp (most recent first)
        enrichedCheckouts.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        setAbandonedCheckouts(enrichedCheckouts);
        setShowAbandonedCheckouts(true);
      } else {
        setAbandonedCheckouts([]);
        setShowAbandonedCheckouts(false);
      }
    } catch (error) {
      console.error('[CART_PAGE] Error loading abandoned checkouts:', error);
    }
  };

  // Function to fetch past orders
  const fetchPastOrders = async (customerId) => {
    if (!customerId) return;
    
    setIsPastOrdersLoading(true);
    
    try {
      console.log('[CART_PAGE] Fetching past orders for customer:', customerId);
      const response = await fetch(`/api/customers/${customerId}/orders`);
      
      if (response.ok) {
        const data = await response.json();
        console.log('[CART_PAGE] Past orders loaded:', data);
        
        if (data.success && Array.isArray(data.orders)) {
          // Sort by date (newest first) and limit to last 3 orders
          const sortedOrders = data.orders.sort((a, b) => {
            const dateA = new Date(a.createdAt || a.date || 0);
            const dateB = new Date(b.createdAt || b.date || 0);
            return dateB - dateA;
          }).slice(0, 3);
          
          setPastOrders(sortedOrders);
        }
      } else {
        console.error('[CART_PAGE] Failed to load past orders:', await response.text());
      }
    } catch (error) {
      console.error('[CART_PAGE] Error fetching past orders:', error);
    } finally {
      setIsPastOrdersLoading(false);
    }
  };

  // Functions to handle abandoned checkouts
  const handleResumeCheckout = (checkout) => {
    console.log('[CART_PAGE] Resuming checkout:', checkout.orderId);
    
    try {
      // Navigate to the checkout page with the order ID
      const checkoutUrl = `/${store}/checkout?orderId=${checkout.orderId}&resume=true`;
      console.log('[CART_PAGE] Navigating to:', checkoutUrl);
      
      // Add window.location.href as fallback in case router.push fails
      if (router && router.push) {
        router.push(checkoutUrl);
      } else {
        console.log('[CART_PAGE] Router not available, using window.location');
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      console.error('[CART_PAGE] Error during checkout resume:', error);
      // Fallback to window.location
      const checkoutUrl = `/${store}/checkout?orderId=${checkout.orderId}&resume=true`;
      window.location.href = checkoutUrl;
    }
  };

  const handleDismissAbandonedCheckout = async (checkout) => {
    console.log('[CART_PAGE] Dismissing abandoned checkout:', checkout.orderId);

    try {
      await AbandonedCheckoutRecovery.dismissAbandonedCheckout(checkout.orderId);
    } catch (error) {
      console.warn('[CART_PAGE] Failed to dismiss checkout on server:', error);
    }

    // Reload abandoned checkouts
    loadAbandonedCheckouts();
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const checkoutTime = new Date(timestamp);
    const diffMs = now - checkoutTime;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} phút trước`;
    } else if (diffHours < 24) {
      return `${diffHours} giờ trước`;
    } else {
      return `${diffDays} ngày trước`;
    }
  };

  // Function to check if current cart items have been previously purchased
  const checkForDuplicatePayments = () => {
    if (!pastOrders.length || !context.cart.length) {
      setDuplicatePaymentWarning(null);
      return;
    }
    
    // Extract SKUs from current cart
    const cartSkus = context.cart.map(item => item.sku);
    
    // Check recent orders for matching items with initiated payments
    const recentOrdersWithSameItems = pastOrders.filter(order => {
      // Only consider orders with payment initiated or completed
      const paymentInitiated = order.paymentStatus === 'initiated' || 
                              order.paymentStatus === 'paid' || 
                              order.paymentStatus === 'completed';
      
      if (!paymentInitiated) return false;
      
      // Check if any items in this order match current cart
      return order.items && order.items.some(item => 
        cartSkus.includes(item.sku)
      );
    });
    
    if (recentOrdersWithSameItems.length > 0) {
      // Create warning message
      const latestOrder = recentOrdersWithSameItems[0];
      const status = latestOrder.paymentStatus === 'paid' || latestOrder.paymentStatus === 'completed' 
        ? 'đã thanh toán' 
        : 'đang chờ thanh toán';
      
      setDuplicatePaymentWarning({
        orderId: latestOrder.id,
        status: status,
        date: new Date(latestOrder.createdAt || latestOrder.date).toLocaleDateString(),
        order: latestOrder
      });
    } else {
      setDuplicatePaymentWarning(null);
    }
  };

  // Function to format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
  };

  // Function to get order status label
  const getOrderStatusLabel = (status) => {
    if (!status) return 'Trạng thái không xác định';
    
    switch(status.toLowerCase()) {
      case 'pending':
        return 'Đang xử lý';
      case 'initiated':
        return 'Đã khởi tạo thanh toán';
      case 'paid':
      case 'completed':
        return 'Đã thanh toán';
      case 'shipped':
        return 'Đã giao hàng';
      case 'delivered':
        return 'Đã nhận hàng';
      case 'cancelled':
        return 'Đã hủy';
      case 'refunded':
        return 'Đã hoàn tiền';
      case 'not_paid':
        return 'Chưa thanh toán';
      default:
        return status;
    }
  };

  // Function to get order status color
  const getOrderStatusColor = (status) => {
    if (!status) return 'gray';
    
    switch(status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'initiated':
        return 'text-blue-600 bg-blue-50';
      case 'paid':
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'shipped':
        return 'text-purple-600 bg-purple-50';
      case 'delivered':
        return 'text-green-700 bg-green-100';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      case 'refunded':
        return 'text-orange-600 bg-orange-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (!renderClientSideComponent) return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <FaSpinner className="animate-spin text-3xl text-gray-500 mb-4" />
      <p className="text-sm text-gray-600">Loading...</p>
    </div>
  );

  // Helper function to get product image
  const getProductImage = (item) => {
    // Try to get image from product details first
    if (productDetails[item.sku]) {
      const product = productDetails[item.sku];

      if (product.images && product.images.length > 0) {
        console.log(`[CART_PAGE] Using product detail image for ${item.sku}:`, product.images[0]);
        return product.images[0];
      }

      if (product.image) {
        if (Array.isArray(product.image) && product.image.length > 0) {
          console.log(`[CART_PAGE] Using product detail image array for ${item.sku}:`, product.image[0]);
          return product.image[0];
        }
        if (typeof product.image === 'string') {
          console.log(`[CART_PAGE] Using product detail image string for ${item.sku}:`, product.image);
          return product.image;
        }
      }
    }

    // Fall back to cart item image
    if (item.image) {
      if (Array.isArray(item.image) && item.image.length > 0) {
        console.log(`[CART_PAGE] Using cart item image array for ${item.sku}:`, item.image[0]);
        return item.image[0];
      }
      if (typeof item.image === 'string') {
        console.log(`[CART_PAGE] Using cart item image string for ${item.sku}:`, item.image);
        return item.image;
      }
    }

    // Default image as last resort
    console.log(`[CART_PAGE] Using default image for ${item.sku}`);
    return '/images/default-product-image.jpg';
  };

  // Handle checkout group change
  const handleCheckoutGroupChange = (groupKey) => {
    console.log("[CART] Checkout group changed to:", groupKey);
    setSelectedCheckoutGroup(groupKey);
  };

  // Function to link cart to the currently logged-in customer
  const linkCartToCustomer = async () => {
    setIsLinkingCart(true);
    setLinkCartMessage(null);

    try {
      // First check if user is already logged in
      const isLoggedIn = isCustomerAuthenticated();
      if (isLoggedIn) {
        const customerId = getCustomerId();
        const info = getCustomerInfo();

        // Set the customer info in state for display
        setCustomerInfo(info);

        // Force the cart to sync with the authenticated user
        context.forceCustomerCartSync(customerId, info);

        setLinkCartMessage({
          type: 'success',
          text: 'Giỏ hàng đã được liên kết với tài khoản của bạn'
        });
      } else {
        // User is not logged in, redirect to login page
        setLinkCartMessage({
          type: 'info',
          text: 'Đang chuyển hướng đến trang đăng nhập...'
        });

        // Redirect to login page
        setTimeout(() => {
          router.push(`/${store}/customer/login`);
        }, 1500);
      }
    } catch (error) {
      console.error('[CART_PAGE] Error linking cart to customer:', error);
      setLinkCartMessage({
        type: 'error',
        text: 'Có lỗi xảy ra khi liên kết giỏ hàng. Vui lòng thử lại.'
      });
    } finally {
      setIsLinkingCart(false);
    }
  };

  // Function to unlink cart from customer
  const unlinkCartFromCustomer = async () => {
    setIsLinkingCart(true);
    setLinkCartMessage(null);

    try {
      // Clear the customer authentication data
      clearCustomerAuth();

      // Clear the user ID to break the cart link
      localStorage.removeItem('SHOPME_USER_ID');

      // Clear the main cart storage
      localStorage.removeItem('SHOPME_');

      // Reset cart with empty values
      localStorage.setItem('SHOPME_', JSON.stringify({
        cart: [...context.cart], // Keep the current cart items
        numberOfItemsInCart: context.numberOfItemsInCart,
        total: context.total,
        affiliatearray: [],
        lastUpdated: new Date().toISOString()
      }));

      // Generate a new anonymous user ID
      const newUserId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
      localStorage.setItem('SHOPME_USER_ID', newUserId);

      // Set customer info to null to update UI
      setCustomerInfo(null);

      // Also need to clear additional auth data - this is critical
      localStorage.removeItem('customerData');
      localStorage.removeItem('user');
      if (store) {
        localStorage.removeItem(`${store}_customerData`);
      }

      // Clear cookie
      document.cookie = 'customerToken=; Max-Age=-99999999; Path=/;';

      setLinkCartMessage({
        type: 'success',
        text: 'Giỏ hàng đã được tách khỏi tài khoản của bạn'
      });

      // Force immediate page reload to ensure all client-side state is reset
      window.location.href = `/${store}/cart`;

    } catch (error) {
      console.error('[CART_PAGE] Error unlinking cart from customer:', error);
      setLinkCartMessage({
        type: 'error',
        text: 'Có lỗi xảy ra khi tách giỏ hàng. Vui lòng thử lại.'
      });
      setIsLinkingCart(false);
    }
  };

  return (
    <>
      <div className="flex flex-col w-full min-h-screen bg-white">
        <Head>
          <title>SIM ONLINE STORE - Order</title>
          <meta name="description" content={`MAG GROUP - Shopping cart`} />
          <meta property="og:title" content="MAG GROUP - Cart" key="title" />
        </Head>
        {storeObject?.layouttemplate === 'shein' && isMobile && (
        <MobileHeader 
          store={store}
          currentstore={storeObject}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          inventory={storeObject.inventory}
        />
      )}

      {storeObject?.layouttemplate === 'shein' && !isMobile && (
        <DesktopHeader 
          store={store}
          currentstore={storeObject}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}
        {/* SHEIN-style header */}
        <div className="sticky top-0 z-30 bg-white border-b border-gray-200">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <h1 className="text-center text-xl font-medium flex-1">Cart</h1>
            <button
              onClick={() => router.back()}
              className="absolute right-4"
              aria-label="Close"
            >
              <FaTimes className="text-xl" />
            </button>
          </div>

          {/* Ship to location bar - commented out for now
          <div className="bg-gray-50 py-2 px-4 border-t border-b border-gray-200">
            <div className="container mx-auto flex items-center justify-between">
              <div className="flex items-center text-sm">
                <span>Ship to Vietnam</span>
                <FaChevronRight className="ml-1 text-xs text-gray-400" />
              </div>
            </div>
          </div> */}
        </div>

        <div className="flex-1 container mx-auto px-4 py-4">
          {/* Link cart message */}
          {linkCartMessage && (
            <div className={`mb-4 p-3 border-l-4 ${linkCartMessage.type === 'success' ? 'bg-green-50 border-green-500 text-green-700' :
              linkCartMessage.type === 'error' ? 'bg-red-50 border-red-500 text-red-700' :
                'bg-blue-50 border-blue-500 text-blue-700'
              }`}>
              {linkCartMessage.text}
            </div>
          )}

          {/* Duplicate payment warning */}
          {duplicatePaymentWarning && (
            <div className="mb-4 p-4 bg-yellow-50 border-l-4 border-yellow-500 text-yellow-800">
              <div className="flex items-start">
                <FaExclamationTriangle className="text-yellow-500 mr-3 mt-1" />
                <div>
                  <p className="font-medium">Cảnh báo: Đơn hàng có thể đã được thanh toán</p>
                  <p className="mt-1 text-sm">
                    Bạn có một đơn hàng gần đây (#{duplicatePaymentWarning.orderId}) với các sản phẩm tương tự 
                    đã {duplicatePaymentWarning.status} vào ngày {duplicatePaymentWarning.date}.
                  </p>
                  <p className="mt-2 text-sm">
                    Vui lòng kiểm tra trạng thái đơn hàng trước khi thanh toán lại để tránh thanh toán trùng lặp.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Customer info - only shown when cart has items */}
          {!cartEmpty && isCustomerAuthenticated() && customerInfo && (
            <div className="mb-4 p-4 bg-gray-50 border border-gray-200">
              <div className="flex items-center">
                <FaUser className="text-sm text-gray-600 mr-2" />
                <h3 className="text-sm font-medium text-gray-800">Thông tin khách hàng</h3>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                {customerInfo.name && <p><span className="inline-block w-20">Tên:</span> {customerInfo.name}</p>}
                {customerInfo.email && <p><span className="inline-block w-20">Email:</span> {customerInfo.email}</p>}
                {customerInfo.phone && <p><span className="inline-block w-20">Điện thoại:</span> {customerInfo.phone}</p>}
              </div>
            </div>
          )}

          {/* Past orders toggle button (when authenticated) */}
          {isCustomerAuthenticated() && pastOrders.length > 0 && (
            <div className="mb-4">
              <button 
                onClick={() => setShowPastOrders(!showPastOrders)}
                className="flex items-center text-sm px-4 py-2 border border-gray-300 rounded"
              >
                <FaHistory className="mr-2" />
                {showPastOrders ? 'Ẩn đơn hàng gần đây' : 'Xem đơn hàng gần đây'}
                <FaChevronRight className={`ml-2 transform transition-transform ${showPastOrders ? 'rotate-90' : ''}`} />
              </button>
            </div>
          )}

          {/* Abandoned checkouts toggle button */}
          {abandonedCheckouts.length > 0 && (
            <div className="mb-4">
              <button 
                onClick={() => setShowAbandonedCheckouts(!showAbandonedCheckouts)}
                className="flex items-center text-sm px-4 py-2 border border-orange-300 bg-orange-50 rounded text-orange-700"
              >
                <FaUndo className="mr-2" />
                Quá trình thanh toán chưa hoàn tất ({abandonedCheckouts.length})
                <FaChevronRight className={`ml-2 transform transition-transform ${showAbandonedCheckouts ? 'rotate-90' : ''}`} />
              </button>
            </div>
          )}

          {/* Abandoned checkouts section */}
          {showAbandonedCheckouts && abandonedCheckouts.length > 0 && (
            <div className="mb-6 bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <FaClock className="text-orange-500 mr-2" />
                <h3 className="text-sm font-medium text-orange-800">Quá trình thanh toán chưa hoàn tất</h3>
              </div>
              
              <div className="space-y-3">
                {abandonedCheckouts.map((checkout) => (
                  <div key={checkout.orderId} className="bg-white border border-orange-200 rounded-lg p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <span className="text-sm font-medium text-gray-900">
                            Đơn hàng #{checkout.orderId}
                          </span>
                          {checkout.hasPaymentInitiated && (
                            <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                              <FaCheckCircle className="mr-1" />
                              Đã khởi tạo thanh toán
                            </span>
                          )}
                        </div>
                        
                        <div className="text-xs text-gray-600 space-y-1">
                          <p>
                            <span className="font-medium">Thời gian bỏ dở:</span> {formatTimeAgo(checkout.timestamp)}
                          </p>
                          {checkout.paymentStatus && (
                            <p>
                              <span className="font-medium">Phương thức thanh toán:</span> {checkout.paymentStatus.paymentMethod}
                            </p>
                          )}
                          {checkout.progress && checkout.progress.completedSections && (
                            <p>
                              <span className="font-medium">Tiến độ:</span> {Object.values(checkout.progress.completedSections).filter(Boolean).length} phần đã hoàn thành
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex space-x-2 ml-3">
                        <button
                          onClick={(e) => {
                            console.log('[CART_PAGE] Button clicked!', e);
                            e.preventDefault();
                            e.stopPropagation();
                            handleResumeCheckout(checkout);
                          }}
                          className="px-3 py-1 bg-orange-600 text-white text-xs rounded hover:bg-orange-700 flex items-center"
                          type="button"
                        >
                          <FaUndo className="mr-1" />
                          Tiếp tục
                        </button>
                        <button
                          onClick={() => handleDismissAbandonedCheckout(checkout)}
                          className="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded hover:bg-gray-400 flex items-center"
                        >
                          <FaTimes className="mr-1" />
                          Bỏ qua
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-3 text-xs text-orange-700">
                💡 Bạn có thể tiếp tục từ nơi đã dừng lại hoặc bắt đầu quá trình thanh toán mới.
              </div>
            </div>
          )}

          {/* Past orders section */}
          <PastOrders 
            showPastOrders={showPastOrders}
            pastOrders={pastOrders}
            isPastOrdersLoading={isPastOrdersLoading}
            store={store}
            formatDate={formatDate}
            getOrderStatusLabel={getOrderStatusLabel}
            getOrderStatusColor={getOrderStatusColor}
          />

          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-20">
              <FaSpinner className="animate-spin text-2xl text-gray-400 mb-4" />
              <p className="text-sm text-gray-500">Loading...</p>
            </div>
          ) : cartEmpty ? (
            <div className="flex flex-col items-center justify-center py-10">
              {/* SHEIN-style empty cart illustration */}
              <div className="w-24 h-24 mb-4 relative">
                <svg viewBox="0 0 120 120" className="w-full h-full text-gray-300">
                  <path d="M5,40 L25,40 L40,90 L95,90 L110,40 L40,40" fill="none" stroke="currentColor" strokeWidth="2" />
                  <circle cx="50" cy="110" r="5" fill="none" stroke="currentColor" strokeWidth="2" />
                  <circle cx="85" cy="110" r="5" fill="none" stroke="currentColor" strokeWidth="2" />
                </svg>
              </div>
              <p className="text-base font-medium mb-2">Giỏ hàng của bạn đang trống</p>

              {isCustomerAuthenticated() ? (
                <>
                  <p className="text-sm text-gray-500 mb-6">Sang shop để bắt đầu mua sắm</p>
                  <div className="w-full max-w-xs">
                    <Link href={`/${store}`}>
                      <button className="w-full py-3 bg-black text-white text-sm font-medium uppercase tracking-wide">
                        MUA SẮM NGAY
                      </button>
                    </Link>
                  </div>
                </>
              ) : (
                <>
                  <p className="text-sm text-gray-500 mb-6">Đăng nhập để xem giỏ hàng</p>
                  {/* SHEIN-style buttons */}
                  <div className="w-full max-w-xs space-y-3">
                    <Link href={`/${store}/customer/login`}>
                      <button className="w-full py-3 bg-black text-white text-sm font-medium uppercase tracking-wide">
                        ĐĂNG NHẬP / ĐĂNG KÝ
                      </button>
                    </Link>
                    <Link href={`/${store}`}>
                      <button className="w-full py-3 bg-white text-black border border-gray-300 text-sm font-medium uppercase tracking-wide">
                        MUA SẮM NGAY
                      </button>
                    </Link>
                    <div className="text-center mt-4">
                      <a
                        href={`/${store}`}
                        className="text-xs text-gray-500 hover:text-gray-700"
                      >
                        Quay lại cửa hàng
                      </a>
                    </div>
                  </div>
                </>
              )}

              {/* Product recommendations section */}
              <div className="w-full mt-12 pt-8 border-t border-gray-200">
                {/* <h2 className="text-center text-lg font-medium mb-6 flex items-center justify-center">
                  <span className="mx-3 text-gray-300">◆</span>
                  Bạn có thể thích
                  <span className="mx-3 text-gray-300">◆</span>
                </h2> */}

                {/* Here you would add product recommendations. 
                    This is a placeholder that would be populated with actual products */}
                <div className="grid grid-cols-2 gap-4">
                  {/* Recommendation products would go here */}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col">
              {/* Link/unlink cart button - only show when cart has items */}
              <div className="mb-4">
                {isCustomerAuthenticated() ? (
                  <button
                    onClick={customerInfo ? unlinkCartFromCustomer : linkCartToCustomer}
                    disabled={isLinkingCart}
                    className="text-sm px-4 py-2 border border-gray-300 rounded"
                  >
                    {isLinkingCart ? (
                      <FaSpinner className="animate-spin inline mr-1" />
                    ) : (
                      <FaLink className="inline mr-1 text-xs" />
                    )}
                    {customerInfo
                      ? 'Tách giỏ hàng khỏi tài khoản'
                      : 'Liên kết giỏ hàng với tài khoản'}
                  </button>
                ) : (
                  <button
                    onClick={linkCartToCustomer}
                    disabled={isLinkingCart}
                    className="text-sm px-4 py-2 border border-gray-300 rounded"
                  >
                    {isLinkingCart ? (
                      <FaSpinner className="animate-spin inline mr-1" />
                    ) : (
                      <FaLink className="inline mr-1 text-xs" />
                    )}
                    Đăng nhập để liên kết giỏ hàng
                  </button>
                )}
              </div>

              {/* Display all products grouped by currency */}
              {Object.keys(groupedCart).length > 0 && (
                <div className="mb-20">
                  {Object.keys(groupedCart).map(currency => {
                    const currencyGroup = groupedCart[currency];

                    return (
                      <div key={currency} className="mb-8">
                        {/* Currency group header */}
                        <div className="bg-gray-50 p-3 mb-4 rounded">
                          <div className="flex items-center text-sm font-medium">
                            <span className="text-gray-700 font-medium">{currency}</span>
                            <span className="ml-2 bg-white text-black text-xs rounded-full px-2 py-0.5">
                              {currencyGroup.items.length} sản phẩm
                            </span>
                            <span className="ml-auto text-sm font-bold">
                              {currency} {groupTotals[currency].toLocaleString()}
                            </span>
                          </div>
                        </div>

                        {/* Currency group items */}
                        <div className="space-y-4">
                          {currencyGroup.items.map((item) => {
                            const imageSrc = getProductImage(item);
                            const hasCustomConfig = item.configInputs && Object.keys(item.configInputs).length > 0;
                            const checkoutMethod = getCheckoutMethod(item);

                            return (
                              <div className="flex border-b border-gray-100 pb-4" key={item.id}>
                                {/* Product image */}
                                <div className="w-20 h-20 flex-shrink-0">
                                  <Link
                                    href={`/${item.store}/product/${slugify(item.sku)}`}
                                    target="_blank"
                                  >
                                    <Image
                                      className="w-full h-full object-cover"
                                      src={imageSrc}
                                      alt={item.name}
                                      width={80}
                                      height={80}
                                    />
                                  </Link>
                                </div>

                                {/* Product details */}
                                <div className="flex-1 pl-3 pr-1 flex flex-col min-w-0">
                                  <div className="flex justify-between items-start">
                                    <Link
                                      href={`/${item.store}/product/${slugify(item.sku)}`}
                                      target="_blank"
                                      className="text-sm font-medium line-clamp-2"
                                    >
                                      {item.name}
                                    </Link>
                                    <button
                                      onClick={() => removeFromCart(item)}
                                      className="text-gray-400 p-1"
                                      aria-label="Remove item"
                                    >
                                      <FaTimes className="text-xs" />
                                    </button>
                                  </div>

                                  <div className="text-xs text-gray-500 mt-1">
                                    <span className="mr-2">SKU: {item.sku}</span>
                                    <span>Shop: {item.store}</span>
                                    <span className="ml-2">{getCheckoutMethodLabel(checkoutMethod)}</span>
                                  </div>

                                  {hasCustomConfig && (
                                    <div className="mt-1 text-xs text-gray-600">
                                      <span className="font-medium">Tuỳ chỉnh:</span>
                                      {Object.entries(item.configInputs).map(([key, value]) => (
                                        <span key={key} className="ml-1">
                                          {key}: {value},
                                        </span>
                                      ))}
                                    </div>
                                  )}

                                  {/* Price and quantity */}
                                  <div className="flex justify-between items-end mt-auto pt-2">
                                    <div className="text-sm font-medium">
                                      {item.currency + item.price.toLocaleString()}
                                    </div>

                                    <div className="flex items-center">
                                      <QuantityPicker
                                        numberOfItems={item.quantity}
                                        increment={() => increment(item)}
                                        decrement={() => decrement(item)}
                                        hideQuantityLabel={true}
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer with total and checkout */}
        {selectedCheckoutGroup && groupedCart[selectedCheckoutGroup] && (
          <div className="bg-white border-t border-gray-200 pt-3">
            <div className="container mx-auto px-4">
              {/* Payment details section */}
              <div className="mb-4">
                <h3 className="text-lg font-medium mb-4">
                  Tổng thanh toán
                </h3>
                {/* Order summary with totals per currency */}
                <div className="py-3 border-t border-gray-200">
                  {Object.keys(groupedCart).map(currency => (
                    <div key={currency} className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">{currency} ({groupedCart[currency].items.length} sản phẩm):</span>
                      <span className="text-sm font-medium">
                        {currency} {groupTotals[currency].toLocaleString()}
                      </span>
                    </div>
                  ))}
                </div>
                {/* Payment method selection moved to checkout page */}
                {/* <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">Chọn phương thức thanh toán ở trang tiếp theo</p>
                </div> */}

                {/* Discount code */}
                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">Mã giảm giá</p>
                  <div className="flex">
                    <input
                      type="text"
                      placeholder="Nhập mã giảm giá"
                      className="flex-1 border border-gray-300 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-1 focus:ring-black"
                    />
                    <button
                      className="bg-gray-800 text-white rounded-r-lg px-4 py-2"
                      onClick={() => {
                        // Implementation of discount code application would go here
                        console.log("Applying discount code");
                      }}
                    >
                      Áp dụng
                    </button>
                  </div>
                </div>

              </div>

              {/* Checkout button */}
              <div className="py-3 border-t border-gray-200">
                <button
                  className="w-full bg-black text-white py-3 text-base font-medium tracking-wide rounded-md"
                  onClick={() => {
                    if (!selectedCheckoutGroup || !groupedCart[selectedCheckoutGroup]) {
                      return;
                    }

                    const currentGroup = groupedCart[selectedCheckoutGroup];
                    const currency = currentGroup.currency;
                    const totalAmount = groupTotals[selectedCheckoutGroup];

                    // Get checkout method from the first item in the group for simplicity
                    // In a real implementation, you might want to handle multiple checkout methods
                    const firstItem = currentGroup.items[0];
                    const checkoutMethod = getCheckoutMethod(firstItem);

                    // Create checkout data to pass in URL - removed payment method and added currency
                    const checkoutData = {
                      method: checkoutMethod,
                      currency,
                      total: totalAmount,
                      items: currentGroup.items.map(item => item.id)
                    };

                    // Encode checkout data for URL
                    const encodedData = encodeURIComponent(JSON.stringify(checkoutData));

                    // Redirect to the appropriate checkout page based on checkout method
                    switch (checkoutMethod) {
                      case 'digital':
                        router.push(`/${store}/checkout/digital?data=${encodedData}`);
                        break;
                      case 'physical':
                        router.push(`/${store}/checkout/shipping?data=${encodedData}`);
                        break;
                      case 'service':
                        router.push(`/${store}/checkout/service?data=${encodedData}`);
                        break;
                      default:
                        router.push(`/${store}/checkout?data=${encodedData}`);
                    }
                  }}
                >
                  Tiếp tục thanh toán
                </button>
              </div>
            </div>
          </div>
        )}

        {storeObject.locations && storeObject.localonly === "1" && (
          <div className="container mx-auto px-4 py-4 border-t border-gray-200">
            <UserLocation
              targetCoordinate={{
                latitude: parseFloat(
                  storeObject.locations[0].latlong.split(",")[0]
                ),
                longitude: parseFloat(
                  storeObject.locations[0].latlong.split(",")[1]
                ),
              }}
            />
          </div>
        )}
      </div>
    </>
  )
}

function CartWithContext(props) {
  //this is like this originally
  return (
    <ContextProviderComponent>
      <SiteContext.Consumer>
        {(context) => <Cart {...props} context={context} />}
      </SiteContext.Consumer>
    </ContextProviderComponent>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  /* console.log(allstores) */
  const stores = allstores.map((store) => store.name)
  const paths = []
  allstores.forEach((store) => {
    const storeId = store.storeId
    paths.push({
      params: {
        store: storeId,
      },
    })
  })
  /* console.log("paths from [store] cart.js:")
  console.log(paths) */

  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  /* const inventory = await fetchInventory() */
  const storeId = params.store // Extract storeId from the URL path params
  const store = allstores.find((store) => store.storeId === storeId)

  /* const inventoryCategorized = inventory.reduce((acc, next) => {
    const categories = next.categories
    categories.forEach(c => {
      const index = acc.findIndex(item => item.name === c)
      if (index !== -1) {
        const item = acc[index]
        item.itemCount = item.itemCount + 1
        acc[index] = item
      } else {
        const item = {
          name: c,
          image: next.image, //todo: not image[0]?
          itemCount: 1
        }
        acc.push(item)
      }
    })
    return acc
  }, []) */

  return {
    props: {
      storeObject: store,
    },
  }
}

export default CartWithContext
