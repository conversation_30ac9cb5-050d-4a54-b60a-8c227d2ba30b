import React, { useState, useEffect } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import ContactUs from '../../components/ContactUs'
import { fetchStore } from '../../utils/storeProvider'
import PaymentQRDisplay from "../../components/PaymentQRDisplay"
import SocialShare from "../../components/SocialShare"
import SocialMediaDisplay from "../../components/SocialMediaDisplay"
import MobileHeader from '../../templates/shein/components/MobileHeader'
import DesktopHeader from '../../templates/shein/components/DesktopHeader'

export default function Contact({ storeData }) {
  const [isMobile, setIsMobile] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // Check initially
    checkMobile();
    
    // Add resize listener
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query);
    // Implement search functionality if needed
  };

  // Get the current URL using Next.js router
  const router = useRouter()
  const { store } = router.query
  const currentUrl = typeof window !== 'undefined' 
    ? window.location.href 
    : `${process.env.NEXT_PUBLIC_SITE_URL || 'https://sim.dailoanshop.net'}${router.asPath}`
  
  console.log('Contact page rendering with storeData:', storeData);
  
  return (
    <>
      <Head>
        <title>Contact {storeData?.name || 'Us'} - SIM Online Store</title>
        <meta name="description" content={`Contact ${storeData?.name || 'MAG'} customer support`} />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      {storeData?.layouttemplate === 'shein' && isMobile && (
        <MobileHeader 
          store={store}
          currentstore={storeData}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          inventory={storeData.inventory}
        />
      )}

      {storeData?.layouttemplate === 'shein' && !isMobile && (
        <DesktopHeader 
          store={store}
          currentstore={storeData}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}

      {/* Main Content Container */}
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-6">
          
          {/* Hero Section */}
          <div className="text-center py-16 bg-white rounded-lg shadow-sm">
            <div className="max-w-3xl mx-auto">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Liên Hệ Với Chúng Tôi
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed">
                Chào mừng bạn đến với <span className="font-semibold text-gray-900">{storeData?.name || 'cửa hàng của chúng tôi'}</span>. 
                Chúng tôi luôn sẵn sàng hỗ trợ bạn với tất cả các câu hỏi về sản phẩm và dịch vụ.
              </p>
            </div>
          </div>

          {/* Contact Form Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Main Contact Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="border-b border-gray-200 p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Gửi Tin Nhắn</h2>
                  <p className="text-gray-600 mt-1">Chúng tôi sẽ phản hồi trong vòng 24 giờ</p>
                </div>
                <div className="p-6">
                  <ContactUs storeData={storeData} />
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              
              {/* Shop QR Code Section */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-6">
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      Cửa Hàng Online
                    </h3>
                    
                    {/* QR Code with clean styling */}
                    <div className="relative inline-block">
                      <div className="bg-white p-4 rounded-xl border-2 border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                        <img
                          src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.sim.more.qr.png"
                          alt="SIM & More QR Code"
                          className="w-40 h-40 object-contain"
                          loading="lazy"
                        />
                      </div>
                      <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        NEW
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <p className="text-sm text-gray-700 font-medium mb-2">
                        SIM & More Store
                      </p>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        Quét mã QR để truy cập cửa hàng online và khám phá các sản phẩm SIM tốt nhất
                      </p>
                    </div>
                    
                    {/* Action buttons */}
                    <div className="mt-4 flex flex-col sm:flex-row gap-2">
                      <button className="flex-1 bg-gray-900 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors duration-200">
                        Mở Cửa Hàng
                      </button>
                      <button className="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                        Chia Sẻ QR
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Contact Info */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    Thông Tin Liên Hệ
                  </h3>
                  <div className="space-y-4">
                    {storeData?.email?.[0] && (
                      <div className="flex items-center space-x-3 p-3 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          <span className="text-gray-600">✉️</span>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-500">Email</p>
                          <p className="font-medium text-gray-900">{storeData.email[0]}</p>
                        </div>
                      </div>
                    )}
                    
                    {storeData?.mobile?.[0] && (
                      <div className="flex items-center space-x-3 p-3 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          <span className="text-gray-600">📱</span>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-500">Điện thoại</p>
                          <p className="font-medium text-gray-900">{storeData.mobile[0]}</p>
                        </div>
                      </div>
                    )}

                    {storeData?.address && (
                      <div className="flex items-start space-x-3 p-3 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <span className="text-gray-600">📍</span>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-500">Địa chỉ</p>
                          <p className="font-medium text-gray-900 text-sm leading-relaxed">{storeData.address}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Business Hours */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    Giờ Làm Việc
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="text-gray-600">Thứ 2 - Thứ 6</span>
                      <span className="font-medium text-gray-900">8:00 - 18:00</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="text-gray-600">Thứ 7</span>
                      <span className="font-medium text-gray-900">8:00 - 16:00</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600">24/7</span>
                      <span className="font-medium text-red-600">Online</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social Media Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Kết Nối Với Chúng Tôi
              </h2>
              <SocialMediaDisplay store={storeData} />
            </div>
          </div>

          {/* Payment QR Section */}
          {storeData?.paymentQRs && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                  Phương Thức Thanh Toán
                </h2>
                <PaymentQRDisplay paymentQRs={storeData.paymentQRs} />
              </div>
            </div>
          )}

          {/* FAQ Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Câu Hỏi Thường Gặp
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-200">
                    <h3 className="font-semibold text-gray-900 mb-2">Làm thế nào để theo dõi đơn hàng?</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">Bạn có thể theo dõi đơn hàng bằng mã đơn hàng được gửi qua email hoặc SMS.</p>
                  </div>
                  <div className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-200">
                    <h3 className="font-semibold text-gray-900 mb-2">Chính sách đổi trả như thế nào?</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">Chúng tôi chấp nhận đổi trả trong vòng 7 ngày với điều kiện sản phẩm còn nguyên vẹn.</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-200">
                    <h3 className="font-semibold text-gray-900 mb-2">Các phương thức thanh toán?</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">Chúng tôi chấp nhận chuyển khoản ngân hàng, thẻ tín dụng và thanh toán khi nhận hàng.</p>
                  </div>
                  <div className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-200">
                    <h3 className="font-semibold text-gray-900 mb-2">Thời gian giao hàng?</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">Thời gian giao hàng từ 1-3 ngày làm việc tùy theo khu vực của bạn.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social Share Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Chia Sẻ Trang Này
              </h2>
              <div className="flex justify-center">
                <SocialShare url={currentUrl} />
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const paths = allstores.map((store) => ({
    params: {
      store: store.storeId,
    },
  }))

  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const storeId = params.store
  const store = allstores.find((store) => store.storeId === storeId)

  if (!store) {
    throw new Error(`Store with ID ${storeId} not found.`)
  }

  return {
    props: {
      storeData: store
    }
  }
} 