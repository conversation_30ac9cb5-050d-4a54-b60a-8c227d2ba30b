import React, { useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import axios from 'axios';

const Delivery = ({ orderid, setSelectedOrder }) => {
    const [checked, setChecked] = useState({
        shipperconfirmChecked: false,
        buyerconfirmChecked: false,
        sellerconfirmChecked: false,
        deliveryproofChecked: false,
        toanhaChecked: false,
        floorroomChecked: false,
        timeChecked: false,
        ngaygiaohangChecked: false,
        messageChecked: false,
        duatructiepchochunhaChecked: false,
        treoocuanhaChecked: false
        // Add more as needed
    });

    const initialValues = {
        orderid: orderid || '0',
        shipperconfirm: '',
        buyerconfirm: '',
        sellerconfirm: '',
        deliveryproof: '',
        toanha: '',
        floorroom: '',
        time: '',
        ngaygiaohang: '',
        message: '',
        shippergps: '',
    };

    /* const handleCheckboxChange = (fieldName) => {
        setChecked(prevState => ({
            ...prevState,
            [fieldName]: !prevState[fieldName]
        }));
    }; */

    const handleCheckboxChange = (fieldName) => {
      setChecked((prevState) => {
        const newState = { ...prevState };
    
        // Toggle the value of the clicked checkbox
        newState[fieldName] = !prevState[fieldName];
    
        // If the clicked checkbox is "duatructiepchochunhaChecked"
        if (fieldName === "duatructiepchochunhaChecked"|| fieldName === "treoocuanhaChecked") {
          if (fieldName === "duatructiepchochunhaChecked") {
            // Set the other checkbox ("treoocuanhaChecked") to the opposite value
            newState.treoocuanhaChecked = !newState.duatructiepchochunhaChecked;
          } else {
            // If the clicked checkbox is "treoocuanhaChecked"
            // Set the other checkbox ("duatructiepchochunhaChecked") to the opposite value
            newState.duatructiepchochunhaChecked = !newState.treoocuanhaChecked;
          }
        }
        return newState;
      });
    };

    const handleSubmit = async (values, { setSubmitting, resetForm }) => {
        try {
            // Handle form submission
    
            // Reset the form after successful submission
            resetForm();
    
            // Reset selectedOrder state
            setSelectedOrder(null);
        } catch (error) {
            console.error('Error submitting form:', error);
        } finally {
            setSubmitting(false);
        }
    };
    

    // Order table data
    /* const orders = [
        {
            shop: 'Shop A',
            orderid: 1234,
            active: true,
            paid: true,
            action: 'Deliver',
            notes: 'Special instructions',
            cart: ['Item 1', 'Item 2'],
            total: 99.99,
            delivery: '123 Main St, Anytown USA',
            deliverynote: 'Leave at front door'
        },
        {
            shop: 'Shop B',
            orderid: 5678,
            active: false,
            paid: false,
            action: 'Cancel',
            notes: 'Out of stock',
            cart: ['Item 3'],
            total: 19.99,
            delivery: '',
            deliverynote: ''
        }
    ]; */

    return (
        <div>
            <Formik initialValues={initialValues} onSubmit={handleSubmit}>
                {({ isSubmitting }) => (
                    <Form>
                    <table className="table">
                      <tbody>
                        <tr>
                          <td>
                            <label htmlFor="orderid">Order ID</label>
                          </td>
                          <td colSpan="2">
                            <Field type="text" id="orderid" name="orderid" />
                            <ErrorMessage name="orderid" component="div" />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="shipperconfirm">Shipper Confirmation</label>
                          </td>
                          <td>
                            <Field
                              type="text"
                              id="shipperconfirm"
                              name="shipperconfirm"
                            />
                            <ErrorMessage name="shipperconfirm" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="shipperconfirmChecked"
                              name="shipperconfirmChecked"
                              checked={checked.shipperconfirmChecked}
                              onChange={() =>
                                handleCheckboxChange("shipperconfirmChecked")
                              }
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="buyerconfirm">Buyer Confirmation</label>
                          </td>
                          <td>
                            <Field type="text" id="buyerconfirm" name="buyerconfirm" />
                            <ErrorMessage name="buyerconfirm" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="buyerconfirmChecked"
                              name="buyerconfirmChecked"
                              checked={checked.buyerconfirmChecked}
                              onChange={() => handleCheckboxChange("buyerconfirmChecked")}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="sellerconfirm">Seller Confirmation</label>
                          </td>
                          <td>
                            <Field type="text" id="sellerconfirm" name="sellerconfirm" />
                            <ErrorMessage name="sellerconfirm" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="sellerconfirmChecked"
                              name="sellerconfirmChecked"
                              checked={checked.sellerconfirmChecked}
                              onChange={() =>
                                handleCheckboxChange("sellerconfirmChecked")
                              }
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="deliveryproof">Delivery Proof</label>
                          </td>
                          <td>
                            <Field type="text" id="deliveryproof" name="deliveryproof" />
                            <ErrorMessage name="deliveryproof" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="deliveryproofChecked"
                              name="deliveryproofChecked"
                              checked={checked.deliveryproofChecked}
                              onChange={() =>
                                handleCheckboxChange("deliveryproofChecked")
                              }
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="toanha">Toà nhà</label>
                          </td>
                          <td>
                            <Field type="text" id="toanha" name="toanha" />
                            <ErrorMessage name="toanha" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="toanhaChecked"
                              name="toanhaChecked"
                              checked={checked.toanhaChecked}
                              onChange={() => handleCheckboxChange("toanhaChecked")}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="floorroom">Tầng, Số phòng</label>
                          </td>
                          <td>
                            <Field type="text" id="floorroom" name="floorroom" />
                            <ErrorMessage name="floorroom" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="floorroomChecked"
                              name="floorroomChecked"
                              checked={checked.floorroomChecked}
                              onChange={() => handleCheckboxChange("floorroomChecked")}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="time">Giờ giao hàng</label>
                          </td>
                          <td>
                            <Field type="text" id="time" name="time" />
                            <ErrorMessage name="time" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="timeChecked"
                              name="timeChecked"
                              checked={checked.timeChecked}
                              onChange={() => handleCheckboxChange("timeChecked")}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="ngaygiaohang">Ngày giao hàng</label>
                          </td>
                          <td>
                            <Field type="text" id="ngaygiaohang" name="ngaygiaohang" />
                            <ErrorMessage name="ngaygiaohang" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="ngaygiaohangChecked"
                              name="ngaygiaohangChecked"
                              checked={checked.ngaygiaohangChecked}
                              onChange={() => handleCheckboxChange("ngaygiaohangChecked")}
                            />
                          </td>
                        </tr>
                        <tr>
  <td>
    <label htmlFor="duatructiepchochunha">Da dua truc tiep cho chu nha</label>
  </td>
  <td>
    <Field type="text" id="duatructiepchochunha" name="Da dua truc tiep cho chu nha" />
    <ErrorMessage name="duatructiepchochunha" component="div" />
  </td>
  <td>
    <Field
      type="checkbox"
className="custom-checkbox"
      id="duatructiepchochunhaChecked"
      name="duatructiepchochunhaChecked"
      checked={checked.duatructiepchochunhaChecked}
      onChange={() => handleCheckboxChange("duatructiepchochunhaChecked")}
    />
  </td>
</tr>
<tr>
  <td>
    <label htmlFor="treoocuanha">Treo o cua nha</label>
  </td>
  <td>
    <Field type="text" id="treoocuanha" name="Treo o cua nha" />
    <ErrorMessage name="treoocuanha" component="div" />
  </td>
  <td>
    <Field
      type="checkbox"
className="custom-checkbox"
      id="treoocuanhaChecked"
      name="treoocuanhaChecked"
      checked={checked.treoocuanhaChecked}
      onChange={() => handleCheckboxChange("treoocuanhaChecked")}
    />
  </td>
</tr>
                        <tr>
                          <td>
                            <label htmlFor="message">Message</label>
                          </td>
                          <td>
                            <Field type="text" id="message" name="message" />
                            <ErrorMessage name="message" component="div" />
                          </td>
                          <td>
                            <Field
                              type="checkbox"
className="custom-checkbox"
                              id="messageChecked"
                              name="messageChecked"
                              checked={checked.messageChecked}
                              onChange={() => handleCheckboxChange("messageChecked")}
                            />
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <label htmlFor="shippergps">ShipperGPS</label>
                          </td>
                          <td colSpan="2">
                            <Field type="text" id="shippergps" name="shippergps" />
                            <ErrorMessage name="shippergps" component="div" />
                          </td>
                        </tr>
          
                        {/* {Object.entries(checked).map(([key, value]) => (
                                          <tr key={key}>
                                              <td></td>
                                              <td colSpan="2">
                                                  <Field type="checkbox"
className="custom-checkbox" id={key} name={key} checked={value} onChange={() => handleCheckboxChange(key)} />
                                              </td>
                                          </tr>
                                      ))} */}
                      </tbody>
                    </table>
                    <button type="submit" disabled={isSubmitting}>
                      Submit Delivery
                    </button>
                  </Form>
                )}
            </Formik>
        </div>
    );
};

const DeliveryTable = () => {
    const [selectedOrder, setSelectedOrder] = useState(null);
    //const [orders, setOrders] = useState([]);

    const handleOrderClick = (order) => {
        setSelectedOrder(order);
    };

    /* useEffect(() => {
        const fetchOrders = async () => {
            try {
                const response = await axios.get('/api/orders');
                setOrders(response.data);
            } catch (error) {
                console.error('Error fetching orders:', error);
            }
        };

        fetchOrders();
    }, []); */

    // Order table data
    const orders = [
        {
            shop: 'Shop A',
            orderid: 1234,
            active: true,
            paid: true,
            action: 'Deliver',
            notes: 'Special instructions',
            cart: ['Item 1', 'Item 2'],
            total: 99.99,
            delivery: '123 Main St, Anytown USA',
            deliverynote: 'Leave at front door'
        },
        {
            shop: 'Shop B',
            orderid: 5678,
            active: false,
            paid: false,
            action: 'Cancel',
            notes: 'Out of stock',
            cart: ['Item 3'],
            total: 19.99,
            delivery: '',
            deliverynote: ''
        }
    ];

    return (
        <div>
            {selectedOrder && (
                <div>
                    <h2>Delivery Form</h2>
                    <Delivery orderid={selectedOrder.orderid} setSelectedOrder={setSelectedOrder}/>
                </div>
            )}

            <h2>Order List</h2>
            <table>
                <thead>
                    <tr>
                        <th>Shop</th>
                        <th>OrderID</th>
                        <th>Active</th>
                        <th>Paid</th>
                        <th>Action</th>
                        <th>Notes</th>
                        <th>Cart</th>
                        <th>Total</th>
                        <th>Delivery</th>
                        <th>DeliveryNote</th>
                    </tr>
                </thead>
                <tbody>
                    {orders.map((order, index) => (
                        <tr key={index} onClick={() => handleOrderClick(order)}>
                            <td>{order.shop}</td>
                            <td>{order.orderid}</td>
                            <td>{order.active ? 'Yes' : 'No'}</td>
                            <td>{order.paid ? 'Yes' : 'No'}</td>
                            <td>{order.action}</td>
                            <td>{order.notes}</td>
                            <td>{order.cart.join(', ')}</td>
                            <td>${order.total.toFixed(2)}</td>
                            <td>{order.delivery}</td>
                            <td>{order.deliverynote}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default DeliveryTable;