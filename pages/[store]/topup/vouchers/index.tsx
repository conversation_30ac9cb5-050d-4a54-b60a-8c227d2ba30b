import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Head from 'next/head';
import { toast } from 'react-toastify';
import OrdersSection from '../../../../components/customer/OrdersSection';

const MyVouchers = () => {
  const router = useRouter();
  const { store } = router.query;
  const [loading, setLoading] = useState(true);
  const [customerData, setCustomerData] = useState<any>(null);
  const [isOrdersActive, setIsOrdersActive] = useState(true);

  useEffect(() => {
    // Check if running on mobile
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
      // Set viewport for mobile
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
      }
    }
  }, []);

  useEffect(() => {
    // Check authentication
    const storedCustomerData = localStorage.getItem('customerData');
    if (!storedCustomerData) {
      router.push(`/${store}/customer/login?redirect=${encodeURIComponent(router.asPath)}`);
      return;
    }

    try {
      const parsedData = JSON.parse(storedCustomerData);
      setCustomerData(parsedData);
      setLoading(false);
    } catch (error) {
      console.error('Error parsing customer data:', error);
      localStorage.removeItem('customerData');
      router.push(`/${store}/customer/login?redirect=${encodeURIComponent(router.asPath)}`);
      return;
    }
  }, [router, store]);

  const handleLogout = () => {
    localStorage.removeItem('customerData');
    document.cookie = 'customerData=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    toast.success('Đăng xuất thành công!');
    router.push(`/${store}`);
  };

  const toggleOrdersSection = () => {
    setIsOrdersActive(!isOrdersActive);
  };

  if (loading || !customerData) {
    return (
      <>
        <Head>
          <title>Đang tải...</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
        </Head>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Đang tải thông tin...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Đơn hàng của tôi - Voucher</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
      </Head>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="border-b border-gray-200 sticky top-0 bg-white z-10 shadow-sm">
          <div className="container mx-auto px-4 py-3 md:py-4 flex justify-between items-center">
            <div className="flex items-center">
              <h1 className="text-lg font-light tracking-wider text-gray-800 uppercase truncate">
                <span className="md:inline hidden">Đơn hàng của tôi</span>
                <span className="md:hidden">Đơn hàng</span>
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <a 
                href={`/${store}`}
                className="text-xs uppercase font-light tracking-wider text-gray-500 hover:text-gray-700 transition"
              >
                <span className="hidden md:inline">Quay lại cửa hàng</span>
                <span className="md:hidden">Cửa hàng</span>
              </a>
              <button 
                onClick={handleLogout}
                className="text-xs uppercase font-light tracking-wider text-gray-500 hover:text-red-500 transition"
              >
                Đăng Xuất
              </button>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-1 sm:px-4 md:px-6 py-4 md:py-8">
          <div className="w-full max-w-5xl mx-auto">
            {/* Welcome message */}
            <div className="mb-6">
              <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
                Xin chào, {customerData?.name || customerData?.phone || 'Khách hàng'}
              </h1>
              <p className="text-sm text-gray-500 mt-1">
                Quản lý đơn hàng và voucher của bạn
              </p>
            </div>
            
            {/* Orders Section */}
            <OrdersSection 
              isActive={isOrdersActive}
              toggleSection={toggleOrdersSection}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default MyVouchers;