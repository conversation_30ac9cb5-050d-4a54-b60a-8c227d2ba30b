import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import { FaArrowLeft, FaReceipt, FaDownload, FaPrint, FaShare, FaCheckCircle, FaCopy, FaCalendarAlt, FaCreditCard, FaTicketAlt } from "react-icons/fa"
import { fetchStore } from '../../../../utils/storeProvider'
import Layout from '../../../../layouts/layout'
import axios from 'axios'

const PurchaseReceipt = ({ allStores = [], categoriesarrayA = {} }) => {
  const router = useRouter()
  const { store, orderId } = router.query
  
  const [customerData, setCustomerData] = useState(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [receipt, setReceipt] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const fetchReceipt = async (customer) => {
    if (!orderId || !customer) {
      setLoading(false)
      setError('Không tìm thấy mã đơn hàng hoặc người dùng chưa xác thực')
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      // Fetch specific order by ID
      const response = await axios.get(`/api/orders/${orderId}`)
      
      if (!response.data.success) {
        setError('Order not found')
        setLoading(false)
        return
      }
      
      const order = response.data.order
      
      // Verify the order belongs to the current user
      const customerIdentifier = customer.phone || customer.id || customer.customerId
      const isPhoneNumber = /^\d+$/.test(customerIdentifier)
      
      let orderBelongsToUser = false
      if (isPhoneNumber) {
        orderBelongsToUser = order.customerPhone === customerIdentifier || 
                           order.customerId === customerIdentifier
      } else {
        orderBelongsToUser = order.customerId === customerIdentifier ||
                           order.customerName === customerIdentifier ||
                           order.customerPhone === customerIdentifier
      }
      
      if (!orderBelongsToUser) {
        setError('Không tìm thấy đơn hàng hoặc bị từ chối truy cập')
        setLoading(false)
        return
      }
        
        // Transform order data to receipt format
        const transformedReceipt = {
          orderId: order.id,
          status: order.status,
          purchaseDate: order.createdAt,
          completedDate: order.updatedAt,
          customer: {
            name: order.customerName,
            email: order.customerEmail,
            phone: order.customerPhone
          },
          items: order.items.map((item, index) => ({
            id: `ITEM${index + 1}`,
            productName: item.name,
            provider: item.networkProvider || extractProvider(item.name),
            country: extractCountry(item.name),
            type: determineItemType(item),
            quantity: item.quantity || 1,
            unitPrice: item.price,
            totalPrice: item.price * (item.quantity || 1),
            currency: order.currency || 'NT$',
            voucherCode: item.topupCode || item.cardPin || item.cardCode || item.cardNumber || item.simNumber,
            deliveryMethod: determineDeliveryMethod(item),
            instructions: generateInstructions(item)
          })),
          payment: {
            method: order.paymentMethod,
            cardLast4: order.paymentInfo?.cardLast4,
            cardType: order.paymentInfo?.cardType,
            transactionId: order.paymentInfo?.transactionId,
            subtotal: order.amount,
            tax: 0,
            discount: 0,
            total: order.amount,
            currency: order.currency || 'NT$'
          },
          billing: {
            name: order.customerName,
            address: order.shippingAddress?.address,
            city: order.shippingAddress?.city,
            postalCode: order.shippingAddress?.postalCode,
            country: order.shippingAddress?.country || 'Taiwan'
          }
        }
        
      setReceipt(transformedReceipt)
    } catch (error) {
      console.error('Error fetching receipt data:', error)
      setError('Không thể tải dữ liệu hóa đơn')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Check authentication using localStorage like the dashboard
    const storeSpecificCustomerData = localStorage.getItem(`customerData_${store}`)
    const genericCustomerData = localStorage.getItem('customerData')
    
    if (storeSpecificCustomerData || genericCustomerData) {
      try {
        const customer = JSON.parse(storeSpecificCustomerData || genericCustomerData)
        setCustomerData(customer)
        setIsAuthenticated(true)
        
        if (orderId) {
          fetchReceipt(customer)
        }
      } catch (error) {
        console.error('Error parsing customer data:', error)
        setIsAuthenticated(false)
        setLoading(false)
      }
    } else {
      setIsAuthenticated(false)
      setLoading(false)
    }
  }, [orderId, store])

  // Helper functions for data transformation
  const extractProvider = (productName) => {
    if (!productName) return 'Unknown'
    const name = productName.toLowerCase()
    if (name.includes('if') || name.includes('taiwan mobile')) return 'IF Taiwan Mobile'
    if (name.includes('chunghwa') || name.includes('cht')) return 'Chunghwa Telecom'
    if (name.includes('steam')) return 'Steam'
    if (name.includes('google play')) return 'Google Play'
    if (name.includes('apple')) return 'Apple'
    return 'Unknown'
  }

  const extractCountry = (productName) => {
    if (!productName) return 'Unknown'
    const name = productName.toLowerCase()
    if (name.includes('taiwan') || name.includes('if') || name.includes('chunghwa')) return 'Taiwan'
    if (name.includes('vietnam') || name.includes('viet')) return 'Vietnam'
    return 'Global'
  }

  const determineItemType = (item) => {
    if (item.type === 'sim') return 'SIM Card'
    if (item.topupCode || item.cardPin || item.cardCode) return 'PIN'
    if (item.phoneNumber) return 'Electronic'
    return 'Digital'
  }

  const determineDeliveryMethod = (item) => {
    if (item.type === 'sim') return 'Physical SIM'
    if (item.topupCode || item.cardPin || item.cardCode) return 'PIN'
    return 'Electronic'
  }

  const generateInstructions = (item) => {
    if (item.type === 'sim' && item.simNumber) {
      return `SIM Card Number: ${item.simNumber}${item.account ? `, Account: ${item.account}` : ''}. Insert the SIM card into your device and follow activation instructions.`
    }
    if (item.topupCode || item.cardPin || item.cardCode) {
      return `Use the PIN code to top up your account. Dial the appropriate number for your provider.`
    }
    if (item.phoneNumber) {
      return `Top-up has been automatically applied to your phone number: ${item.phoneNumber}`
    }
    return 'Please follow the provider\'s instructions for using this voucher.'
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const handlePrint = () => {
    window.print()
  }

  const handleDownload = () => {
    // In real app, this would generate and download a PDF
    alert('PDF download functionality would be implemented here')
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `Receipt ${receipt?.orderId}`,
        text: `Purchase receipt for order ${receipt?.orderId}`,
        url: window.location.href
      })
    } else {
      copyToClipboard(window.location.href)
      alert('Receipt link copied to clipboard')
    }
  }

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải hóa đơn...</p>
        </div>
      </div>
    )
  }

  // Show login prompt for unauthenticated users
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <FaReceipt className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Yêu Cầu Xác Thực</h2>
            <p className="text-gray-600 mb-6">Vui lòng đăng nhập để xem hóa đơn của bạn.</p>
            <Link href={`/${store}/customer/login?redirect=${encodeURIComponent(router.asPath)}`} legacyBehavior>
              <a className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                Đăng Nhập
              </a>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <FaReceipt className="w-16 h-16 text-red-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Lỗi Khi Tải Hóa Đơn</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="space-x-4">
              <button 
                onClick={() => window.location.reload()} 
                className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Thử Lại
              </button>
              <Link href={`/${store}/topup`} legacyBehavior>
                <a className="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                  <FaArrowLeft className="w-4 h-4 mr-2" />
                  Quay Lại Nạp Thẻ
                </a>
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show receipt not found state
  if (!receipt) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <FaReceipt className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Không Tìm Thấy Hóa Đơn</h2>
            <p className="text-gray-600 mb-6">Hóa đơn bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
            <Link href={`/${store}/topup`} legacyBehavior>
              <a className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <FaArrowLeft className="w-4 h-4 mr-2" />
                Quay Lại Nạp Thẻ
              </a>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Layout allStores={allStores} categoriesarrayA={categoriesarrayA}>
      <Head>
        <title>Hóa Đơn Mua Hàng #{receipt.orderId} - Cửa Hàng Nạp Thẻ</title>
        <meta name="description" content={`Hóa đơn mua hàng cho đơn hàng ${receipt.orderId}`} />
        <meta property="og:title" content={`Hóa Đơn Mua Hàng #{receipt.orderId} - Cửa Hàng Nạp Thẻ`} key="title" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b print:hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link href={`/${store}/topup`} legacyBehavior>
                <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <FaArrowLeft className="w-5 h-5 mr-2" />
                  Quay Lại
                </a>
              </Link>
              <div className="flex items-center space-x-3">
                <FaReceipt className="w-6 h-6 text-blue-600" />
                <div className="text-center">
                  <h1 className="text-xl font-bold text-gray-900">Hóa Đơn Mua Hàng</h1>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handlePrint}
                  className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
                  title="In Hóa Đơn"
                >
                  <FaPrint className="w-5 h-5" />
                </button>
                <button
                  onClick={handleDownload}
                  className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
                  title="Tải PDF"
                >
                  <FaDownload className="w-5 h-5" />
                </button>
                <button
                  onClick={handleShare}
                  className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
                  title="Chia Sẻ Hóa Đơn"
                >
                  <FaShare className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Receipt Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden print:shadow-none print:rounded-none">
            {/* Receipt Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">MagShop Top-Up</h2>
                  <p className="text-blue-100">Digital Voucher & Top-Up Services</p>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-2 mb-2">
                    <FaCheckCircle className="w-6 h-6 text-green-300" />
                    <span className="text-lg font-semibold">HOÀN THÀNH</span>
                  </div>
                  <p className="text-blue-100">Đơn Hàng #{receipt.orderId}</p>
                </div>
              </div>
            </div>

            {/* Order Information */}
            <div className="p-6 border-b border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                    <FaCalendarAlt className="w-4 h-4 mr-2 text-blue-600" />
                    Chi Tiết Đơn Hàng
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p><span className="text-gray-600">Mã Đơn Hàng:</span> {receipt.orderId}</p>
                    <p><span className="text-gray-600">Ngày Mua:</span> {formatDate(receipt.purchaseDate)}</p>
                    <p><span className="text-gray-600">Hoàn Thành:</span> {formatDate(receipt.completedDate)}</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Thông Tin Khách Hàng</h3>
                  <div className="space-y-1 text-sm">
                    <p><span className="text-gray-600">Tên:</span> {receipt.customer.name}</p>
                    <p><span className="text-gray-600">Email:</span> {receipt.customer.email}</p>
                    <p><span className="text-gray-600">Điện Thoại:</span> {receipt.customer.phone}</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                    <FaCreditCard className="w-4 h-4 mr-2 text-blue-600" />
                    Phương Thức Thanh Toán
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p><span className="text-gray-600">Phương Thức:</span> {receipt.payment.method}</p>
                    <p><span className="text-gray-600">Thẻ:</span> {receipt.payment.cardType} ****{receipt.payment.cardLast4}</p>
                    <p><span className="text-gray-600">Giao Dịch:</span> {receipt.payment.transactionId}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Items */}
            <div className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
                <FaTicketAlt className="w-4 h-4 mr-2 text-blue-600" />
                Sản Phẩm Đã Mua
              </h3>
              
              <div className="space-y-4">
                {receipt.items.map((item, index) => (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.productName}</h4>
                        <p className="text-sm text-gray-600">{item.provider} • {item.country}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            item.deliveryMethod === 'PIN' 
                              ? 'bg-purple-100 text-purple-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {item.deliveryMethod}
                          </span>
                          <span className="text-xs text-gray-500">SL: {item.quantity}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-lg text-blue-600">
                          {item.currency} {item.totalPrice.toLocaleString()}
                        </p>
                        {item.quantity > 1 && (
                          <p className="text-sm text-gray-500">
                            {item.currency} {item.unitPrice} mỗi cái
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {/* Voucher Code */}
                    {item.voucherCode && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Mã Voucher</span>
                          <button
                            onClick={() => copyToClipboard(item.voucherCode)}
                            className="text-blue-600 hover:text-blue-700 transition-colors"
                            title="Sao chép mã"
                          >
                            <FaCopy className="w-4 h-4" />
                          </button>
                        </div>
                        <p className="font-mono text-sm bg-white border rounded px-2 py-1">
                          {item.voucherCode}
                        </p>
                      </div>
                    )}
                    
                    {/* Instructions */}
                    <div className="bg-green-50 rounded-lg p-3">
                      <p className="text-sm font-medium text-green-700 mb-1">Hướng Dẫn Sử Dụng</p>
                      <p className="text-sm text-green-800">{item.instructions}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Payment Summary */}
            <div className="bg-gray-50 p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Tóm Tắt Thanh Toán</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Tạm Tính</span>
                  <span>{receipt.payment.currency} {receipt.payment.subtotal.toLocaleString()}</span>
                </div>
                {receipt.payment.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Giảm Giá</span>
                    <span>-{receipt.payment.currency} {receipt.payment.discount.toLocaleString()}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Thuế (5%)</span>
                  <span>{receipt.payment.currency} {receipt.payment.tax.toLocaleString()}</span>
                </div>
                <div className="border-t border-gray-300 pt-2">
                  <div className="flex justify-between font-bold text-lg">
                    <span>Tổng Cộng</span>
                    <span className="text-blue-600">
                      {receipt.payment.currency} {receipt.payment.total.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Billing Address */}
            <div className="p-6 border-t border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-2">Địa Chỉ Thanh Toán</h3>
              <div className="text-sm text-gray-600">
                <p>{receipt.billing.name}</p>
                <p>{receipt.billing.address}</p>
                <p>{receipt.billing.city}, {receipt.billing.postalCode}</p>
                <p>{receipt.billing.country}</p>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-100 p-6 text-center">
              <p className="text-sm text-gray-600 mb-2">
                Cảm ơn bạn đã mua hàng! Nếu có bất kỳ câu hỏi nào, vui lòng liên hệ đội ngũ hỗ trợ của chúng tôi.
              </p>
              <p className="text-xs text-gray-500">
                Đây là hóa đơn điện tử. Không cần chữ ký.
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8 bg-white rounded-xl shadow-lg p-6 print:hidden">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
              Tiếp Theo Là Gì?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href={`/${store}/topup/vouchers`} legacyBehavior>
                <a className="flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                  <FaTicketAlt className="w-4 h-4" />
                  <span>Xem Voucher Của Tôi</span>
                </a>
              </Link>
              <Link href={`/${store}/topup/buy`} legacyBehavior>
                <a className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                  <FaTicketAlt className="w-4 h-4" />
                  <span>Mua Thêm Voucher</span>
                </a>
              </Link>
              <Link href={`/${store}/topup/history`} legacyBehavior>
                <a className="flex items-center justify-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                  <FaReceipt className="w-4 h-4" />
                  <span>Lịch Sử Đơn Hàng</span>
                </a>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

// Export with static generation
export async function getStaticPaths() {
  const allStores = await fetchStore()
  const paths = allStores.map((store) => ({
    params: { store: store.storeId },
  }))

  return {
    paths,
    fallback: 'blocking',
  }
}

export async function getStaticProps({ params }) {
  const allStores = await fetchStore()
  const store = allStores.find((s) => s.storeId === params.store)
  
  if (!store) {
    return {
      notFound: true,
    }
  }

  // Create lightweight store data for layout (only essential fields)
  const lightweightStores = allStores.map(s => ({
    storeId: s.storeId,
    name: s.name,
    logo: s.logo,
    activestatus: s.activestatus
  }))

  // Create categories array structure for layout
  const categoriesarrayA = {
    [params.store]: [],
    all: []
  }

  return {
    props: {
      allStores: lightweightStores,
      categoriesarrayA,
      currentStore: {
        storeId: store.storeId,
        name: store.name,
        logo: store.logo,
        activestatus: store.activestatus,
        slogan: store.slogan
      },
    },
    revalidate: 60, // Revalidate every minute
  }
}

export default PurchaseReceipt