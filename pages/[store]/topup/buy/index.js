import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import { FaArrowLeft, FaChevronRight, FaShoppingCart, FaGlobe } from "react-icons/fa"
import { fetchStore } from '../../../../utils/storeProvider'
import { fetchInventory } from '../../../../utils/inventoryProvider'
import { slugify } from '../../../../utils/helpers'
import Layout from '../../../../layouts/layout'
import countriesConfig from '../../../../data/countries.json'

/**
 * TopUpBuyPage component - Shows country selection and products filtered by country
 * Extracts countries from inventory data and allows filtering products by selected country
 */

const TopUpBuyPage = ({ storeData, inventoryData = [], countriesData = [] }) => {
  const router = useRouter()
  const { store } = router.query
  const [selectedCountry, setSelectedCountry] = useState(null)
  const [selectedProvider, setSelectedProvider] = useState(null)
  const [filteredProducts, setFilteredProducts] = useState([])

  // Helper function to get country flag emoji
  const getCountryFlag = (countryName) => {
    const flagMap = {
      'Taiwan': '🇹🇼',
      'Hong Kong': '🇭🇰',
      'Singapore': '🇸🇬',
      'Malaysia': '🇲🇾',
      'Thailand': '🇹🇭',
      'Philippines': '🇵🇭',
      'Indonesia': '🇮🇩',
      'Vietnam': '🇻🇳',
      'Japan': '🇯🇵',
      'South Korea': '🇰🇷',
      'China': '🇨🇳',
      'India': '🇮🇳',
      'Australia': '🇦🇺',
      'New Zealand': '🇳🇿',
      'United States': '🇺🇸',
      'Canada': '🇨🇦',
      'United Kingdom': '🇬🇧',
      'Germany': '🇩🇪',
      'France': '🇫🇷',
      'Italy': '🇮🇹',
      'Spain': '🇪🇸',
      'Netherlands': '🇳🇱',
      'Belgium': '🇧🇪',
      'Switzerland': '🇨🇭',
      'Austria': '🇦🇹',
      'Sweden': '🇸🇪',
      'Norway': '🇳🇴',
      'Denmark': '🇩🇰',
      'Finland': '🇫🇮',
      'Russia': '🇷🇺',
      'Brazil': '🇧🇷',
      'Mexico': '🇲🇽',
      'Argentina': '🇦🇷',
      'Chile': '🇨🇱',
      'Colombia': '🇨🇴',
      'Peru': '🇵🇪',
      'Ecuador': '🇪🇨',
      'Uruguay': '🇺🇾',
      'Paraguay': '🇵🇾',
      'Bolivia': '🇧🇴',
      'Venezuela': '🇻🇪',
      'Guyana': '🇬🇾',
      'Suriname': '🇸🇷',
      'French Guiana': '🇬🇫',
      'South Africa': '🇿🇦',
      'Egypt': '🇪🇬',
      'Morocco': '🇲🇦',
      'Tunisia': '🇹🇳',
      'Algeria': '🇩🇿',
      'Libya': '🇱🇾',
      'Sudan': '🇸🇩',
      'Ethiopia': '🇪🇹',
      'Kenya': '🇰🇪',
      'Uganda': '🇺🇬',
      'Tanzania': '🇹🇿',
      'Rwanda': '🇷🇼',
      'Burundi': '🇧🇮',
      'Madagascar': '🇲🇬',
      'Mauritius': '🇲🇺',
      'Seychelles': '🇸🇨',
      'Comoros': '🇰🇲',
      'Mayotte': '🇾🇹',
      'Réunion': '🇷🇪'
    }
    return flagMap[countryName] || '🌍'
  }

  // Helper function to get provider icon/logo
  const getProviderIcon = (providerName) => {
    const providerLogos = {
      'OK': '/MAG/LOGO/ok-taiwanmobile-logo.webp',
      'IF': '/MAG/LOGO/if-logo-app.webp', 
      'CHUNGHOA': '/MAG/LOGO/cw-logo.webp',
      'CHUNGHWA': '/MAG/LOGO/cw-logo.webp'
    }
    
    return providerLogos[providerName] || null
  }
  const [loading, setLoading] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Get countries from configuration or fallback to inventory extraction
  const getUniqueCountries = () => {
    // Use countries configuration if available
    if (countriesData && countriesData.length > 0) {
      return countriesData.filter(countryConfig => {
        // Check if this country has any active products in inventory
        return inventoryData.some(item => 
          item.country === countryConfig.countryKey && item.activestatus === "1"
        )
      })
    }
    
    // Fallback to original behavior
    const countries = new Set()
    inventoryData.forEach(item => {
      if (item.country && item.activestatus === "1") {
        countries.add(item.country)
      }
    })
    return Array.from(countries).sort().map(country => ({
      countryKey: country,
      countryName: country,
      categoryMapping: 'general',
      flag: getCountryFlag(country)
    }))
  }

  // Extract unique providers for selected country
  const getUniqueProviders = (countryKey) => {
    const providers = new Set()
    inventoryData.forEach(item => {
      if (item.country === countryKey && item.provider && item.activestatus === "1") {
        providers.add(item.provider)
      }
    })
    return Array.from(providers).sort()
  }

  const uniqueCountries = getUniqueCountries()
  const uniqueProviders = selectedCountry ? getUniqueProviders(selectedCountry) : []

  // Filter products by selected country and provider
  useEffect(() => {
    if (selectedCountry && selectedProvider) {
      const filtered = inventoryData.filter(item => 
        item.country === selectedCountry &&
        item.provider === selectedProvider &&
        item.activestatus === "1" &&
        item.price !== 0
      )
      setFilteredProducts(filtered)
    } else {
      setFilteredProducts([])
    }
  }, [selectedCountry, selectedProvider, inventoryData])



  // Format product price
  const formatPrice = (price, currency = 'VND') => {
    if (currency === 'NT') {
      return `${price} NT`
    }
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price)
  }

  // Get product image
  const getProductImage = (item) => {
    if (Array.isArray(item.image) && item.image.length > 0) {
      return item.image[0]
    }
    return item.image || '/images/default-product.png'
  }

  // Handle country selection
  const handleCountrySelect = (countryKey) => {
    setSelectedCountry(countryKey)
    setSelectedProvider(null)
    setFilteredProducts([])
  }

  // Handle provider selection
  const handleProviderSelect = (provider) => {
    setSelectedProvider(provider)
  }

  // Handle back to country selection
  const handleBackToCountries = () => {
    setSelectedCountry(null)
    setSelectedProvider(null)
    setFilteredProducts([])
  }

  // Handle back to provider selection
  const handleBackToProviders = () => {
    setSelectedProvider(null)
    setFilteredProducts([])
  }

  // Render country selection view
  const renderCountrySelection = () => (
    <div className="space-y-3">
      {uniqueCountries.map((countryConfig) => (
        <button
          key={countryConfig.countryKey}
          onClick={() => handleCountrySelect(countryConfig.countryKey)}
          className="
            w-full bg-white rounded-lg shadow-sm border border-gray-200 
            hover:shadow-md hover:border-green-300 
            transition-all duration-200 transform hover:scale-[1.02]
            p-4 flex items-center justify-between
            group text-left
          "
        >
          <div className="flex items-center space-x-4">
            <div className="text-3xl">{countryConfig.flag}</div>
            <div>
              <h3 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                {countryConfig.countryName}
              </h3>
              <p className="text-sm text-gray-600">
                {inventoryData.filter(item => item.country === countryConfig.countryKey && item.activestatus === "1").length} sản phẩm có sẵn
              </p>
            </div>
          </div>
          <FaChevronRight className="w-5 h-5 text-gray-400 group-hover:text-green-500 transition-colors" />
        </button>
      ))}
    </div>
  )

  // Render provider selection view
  const renderProviderSelection = () => (
    <div>
      {/* Back button */}
      <button
        onClick={handleBackToCountries}
        className="mb-6 flex items-center text-gray-600 hover:text-gray-900 transition-colors"
      >
        <FaArrowLeft className="w-4 h-4 mr-2" />
        Quay lại Quốc gia
      </button>

      {/* Selected country header */}
      <div className="mb-6 text-center">
        <div className="text-4xl mb-2">{getCountryFlag(selectedCountry)}</div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedCountry}</h2>
        <p className="text-gray-600">Chọn nhà cung cấp để xem sản phẩm</p>
      </div>

      {/* Providers list */}
      <div className="space-y-3">
        {uniqueProviders.map((provider) => (
          <button
            key={provider}
            onClick={() => handleProviderSelect(provider)}
            className="
              w-full bg-white rounded-lg shadow-sm border border-gray-200 
              hover:shadow-md hover:border-green-300 
              transition-all duration-200 transform hover:scale-[1.02]
              p-4 flex items-center justify-between
              group text-left
            "
          >
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-lg flex items-center justify-center overflow-hidden bg-white border border-gray-200">
                {getProviderIcon(provider) ? (
                  <img 
                    src={getProviderIcon(provider)} 
                    alt={`${provider} logo`}
                    className="w-10 h-10 object-contain"
                    onError={(e) => {
                      e.target.style.display = 'none'
                      e.target.nextSibling.style.display = 'flex'
                    }}
                  />
                ) : null}
                <div 
                  className={`w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg ${
                    getProviderIcon(provider) ? 'hidden' : 'flex'
                  }`}
                >
                  {provider.charAt(0)}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                  {provider}
                </h3>
                <p className="text-sm text-gray-600">
                   {inventoryData.filter(item => 
                     item.country === selectedCountry && 
                     item.provider === provider && 
                     item.activestatus === "1"
                   ).length} sản phẩm có sẵn
                 </p>
              </div>
            </div>
            <FaChevronRight className="w-5 h-5 text-gray-400 group-hover:text-green-500 transition-colors" />
          </button>
        ))}
      </div>
    </div>
  )

  // Render products view
  const renderProducts = () => (
    <div>
      {/* Back button */}
      <button
        onClick={handleBackToProviders}
        className="mb-6 flex items-center text-gray-600 hover:text-gray-900 transition-colors"
      >
        <FaArrowLeft className="w-4 h-4 mr-2" />
        Quay lại Nhà cung cấp
      </button>

      {/* Selected country and provider header */}
      <div className="mb-6 text-center">
        <div className="text-4xl mb-2">{getCountryFlag(selectedCountry)}</div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedCountry} - {selectedProvider}</h2>
        <p className="text-gray-600">{filteredProducts.length} sản phẩm có sẵn</p>
      </div>

      {/* Products grid */}
      {filteredProducts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredProducts.map((product) => (
            <Link
              key={product.sku}
              href={`/${store}/product/${slugify(product.sku)}`}
              className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden block group cursor-pointer"
            >
              <div className="aspect-w-16 aspect-h-9 bg-gray-100">
                <img
                  src={getProductImage(product)}
                  alt={product.name}
                  className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-200"
                  onError={(e) => {
                    e.target.src = '/images/default-product.png'
                  }}
                />
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors">
                  {product.name}
                </h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {product.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold text-green-600">
                    {formatPrice(product.price, product.currency)}
                  </span>
                  <div className="bg-green-500 group-hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2">
                    <FaShoppingCart className="w-4 h-4" />
                    <span>Xem Chi tiết</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FaGlobe className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không có Sản phẩm</h3>
          <p className="text-gray-600">Hiện tại không có sản phẩm nào cho {selectedCountry}.</p>
        </div>
      )}
    </div>
  )

  return (
    <>
      <Head>
        <title>{selectedProvider ? `${selectedCountry} - ${selectedProvider} Sản phẩm` : selectedCountry ? `${selectedCountry} Nhà cung cấp` : 'Chọn Quốc gia'} - {storeData?.name || 'Cửa hàng Top-up'}</title>
        <meta name="description" content={selectedProvider ? `Duyệt sản phẩm ${selectedCountry} ${selectedProvider}` : selectedCountry ? `Chọn nhà cung cấp tại ${selectedCountry}` : 'Chọn quốc gia để xem sản phẩm có sẵn'} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Link href={`/${store}`} className="text-gray-600 hover:text-gray-900">
                  <FaArrowLeft className="w-5 h-5" />
                </Link>
                <h1 className="text-xl font-semibold text-gray-900">
                  {selectedProvider ? `${selectedCountry} - ${selectedProvider}` : selectedCountry ? selectedCountry : 'Chọn Quốc gia'}
                </h1>
              </div>
              <div className="flex items-center space-x-2">
                <FaGlobe className="w-5 h-5 text-gray-500" />
                <span className="text-sm text-gray-600">{storeData?.name}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
              <span className="ml-3 text-gray-600">Đang tải...</span>
            </div>
          ) : (
            <div>
              {!selectedCountry ? (
                <div>
                  <div className="text-center mb-8">
                    <FaGlobe className="w-12 h-12 text-green-500 mx-auto mb-4" />
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Chọn Quốc gia</h2>
                    <p className="text-gray-600">Chọn quốc gia để xem sản phẩm có sẵn</p>
                  </div>
                  {renderCountrySelection()}
                </div>
              ) : !selectedProvider ? (
                renderProviderSelection()
              ) : (
                renderProducts()
              )}
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default TopUpBuyPage

export async function getStaticPaths() {
  const { fetchStore } = await import('../../../../utils/storeProvider')
  const allStores = await fetchStore()
  
  const paths = allStores.map((store) => ({
    params: { store: store.storeId }
  }))

  return {
    paths,
    fallback: false
  }
}

export async function getStaticProps({ params }) {
  const { fetchStore } = await import('../../../../utils/storeProvider')
  const { fetchInventory } = await import('../../../../utils/inventoryProvider')
  
  const allStores = await fetchStore()
  const currentStore = allStores.find(store => store.storeId === params.store)
  const inventoryData = await fetchInventory()
  
  if (!currentStore) {
    return {
      notFound: true
    }
  }

  // Load countries configuration
  let countriesData = []
  try {
    countriesData = countriesConfig || []
  } catch (error) {
    console.warn('Countries configuration not found, using fallback')
  }

  return {
    props: {
      storeData: currentStore,
      inventoryData: inventoryData || [],
      countriesData
    }
  }
}