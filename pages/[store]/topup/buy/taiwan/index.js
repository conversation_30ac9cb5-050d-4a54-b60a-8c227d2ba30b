import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import { FaArrowLeft } from "react-icons/fa"

const TaiwanProviders = () => {
  const router = useRouter()
  const { store } = router.query
  const [isMobile, setIsMobile] = useState(false)
  const [providers, setProviders] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    const fetchProviders = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/inventory')
        if (!response.ok) {
          throw new Error('Failed to fetch providers')
        }
        const data = await response.json()
        
        // Extract unique providers from the inventory data
        const providerSet = new Set()
        const providerData = {}
        
        // data is an array directly, not an object with items property
        data?.forEach(item => {
          if (item.categories?.includes('Taiwan') && item.categories?.includes('Prepaid')) {
            // Find the provider category (should be the 3rd element after Taiwan and Prepaid)
            const providerCategory = item.categories?.find(cat => 
              cat !== 'Taiwan' && cat !== 'Prepaid' && cat !== 'CARD'
            )
            
            if (providerCategory && !providerSet.has(providerCategory)) {
              providerSet.add(providerCategory)
              
              // Map provider names and styling
              const providerInfo = {
                'IF': {
                  name: 'IF (Taiwan Mobile)',
                  description: 'Taiwan Mobile prepaid services',
                  bgColor: 'bg-blue-50',
                  borderColor: 'border-blue-200',
                  hoverColor: 'hover:border-blue-400'
                },
                'OK': {
                  name: 'OK Prepaid',
                  description: 'OK prepaid service provider',
                  bgColor: 'bg-green-50',
                  borderColor: 'border-green-200',
                  hoverColor: 'hover:border-green-400'
                },
                'CHUNGHOA': {
                  name: 'Chung Hwa Telecom',
                  description: 'Chunghwa Telecom prepaid services',
                  bgColor: 'bg-red-50',
                  borderColor: 'border-red-200',
                  hoverColor: 'hover:border-red-400'
                }
              }
              
              const info = providerInfo[providerCategory] || {
                name: providerCategory,
                description: `${providerCategory} prepaid services`,
                bgColor: 'bg-gray-50',
                borderColor: 'border-gray-200',
                hoverColor: 'hover:border-gray-400'
              }
              
              providerData[providerCategory] = {
                id: providerCategory.toLowerCase(),
                name: info.name,
                logo: `/images/providers/${providerCategory.toLowerCase()}-logo.png`,
                description: info.description,
                href: `/${store}/topup/buy/taiwan/${providerCategory.toLowerCase()}`,
                bgColor: info.bgColor,
                borderColor: info.borderColor,
                hoverColor: info.hoverColor,
                productCount: data.items?.filter(item => 
                  item.categories?.includes(providerCategory)
                ).length || 0
              }
            }
          }
        })
        
        // Convert to array and sort
        const providersArray = Object.values(providerData).sort((a, b) => 
          a.name.localeCompare(b.name)
        )
        
        setProviders(providersArray)
      } catch (err) {
        console.error('Error fetching providers:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    if (store) {
      fetchProviders()
    }
  }, [store])

  return (
    <>
      <Head>
        <title>Taiwan Prepaid Voucher - Top-Up Storefront</title>
        <meta name="description" content="Choose your Taiwan prepaid service provider - IF, OK, Chung Hwa Telecom" />
        <meta property="og:title" content="Taiwan Prepaid Voucher - Top-Up Storefront" key="title" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link href={`/${store}/topup/buy`} legacyBehavior>
                <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <FaArrowLeft className="w-5 h-5 mr-2" />
                  Back
                </a>
              </Link>
              <div className="flex items-center space-x-3">
                <span className="text-2xl">🇹🇼</span>
                <div className="text-center">
                  <h1 className="text-xl font-bold text-gray-900">Taiwan Prepaid Voucher</h1>
                </div>
              </div>
              <div className="w-16"></div> {/* Spacer for centering */}
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center text-sm text-gray-600">
              <span>Selected Product Category:</span>
              <span className="ml-2 font-medium text-gray-900">🇹🇼 Taiwan Prepaid Voucher</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
              <span className="ml-3 text-gray-600">Loading providers...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <div className="text-red-400 mr-3">⚠️</div>
                <div>
                  <h3 className="text-red-800 font-medium">Error Loading Providers</h3>
                  <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Title Section */}
          {!loading && !error && (
            <>
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Choose Your Provider
                </h2>
                <p className="text-gray-600">
                  Select from Taiwan's leading prepaid service providers
                </p>
              </div>

              {/* Provider Grid */}
              <div className={`grid gap-6 ${
                isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'
              }`}>
            {providers.map((provider) => (
              <Link key={provider.id} href={provider.href} legacyBehavior>
                <a className={`
                  ${provider.bgColor} ${provider.borderColor} ${provider.hoverColor}
                  border-2 rounded-xl p-6 transition-all duration-200 
                  transform hover:scale-105 hover:shadow-lg
                  flex flex-col items-center text-center space-y-4
                  group
                `}>
                  {/* Provider Logo Placeholder */}
                  <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-2xl font-bold text-gray-600">
                        {provider.name.charAt(0)}
                      </span>
                    </div>
                  </div>
                  
                  {/* Provider Info */}
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-1">
                      {provider.name}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {provider.description}
                    </p>
                  </div>
                  
                  {/* Action Indicator */}
                  <div className="text-xs text-gray-500 group-hover:text-gray-700 transition-colors">
                    Tap to view products →
                  </div>
                </a>
              </Link>
            ))}
          </div>

          {/* Info Section */}
          <div className="mt-12 bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
              Taiwan Prepaid Services
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">📱</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Mobile Top-ups</h4>
                <p className="text-gray-600">
                  Various denominations from NT$50 to NT$500+
                </p>
              </div>
              <div className="text-center">
                <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">🌐</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Internet Packages</h4>
                <p className="text-gray-600">
                  Data plans, unlimited packages, combo deals
                </p>
              </div>
              <div className="text-center">
                <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">📦</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Bulk Options</h4>
                <p className="text-gray-600">
                  Multi-piece offers with discounted pricing
                </p>
              </div>
            </div>
          </div>

          {/* Delivery Methods */}
          <div className="mt-8 bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
            <h3 className="text-lg font-semibold mb-4 text-center">
              Available Delivery Methods
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <h4 className="font-medium mb-2">⚡ Elektrik (Electronic)</h4>
                <p className="opacity-90">
                  Instant delivery directly to your mobile number
                </p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-4">
                <h4 className="font-medium mb-2">🔢 PIN Code</h4>
                <p className="opacity-90">
                  Generated code sent via email or SMS
                </p>
              </div>
            </div>
          </div>
              </>
            )}
        </div>
      </div>
    </>
  )
}

export default TaiwanProviders