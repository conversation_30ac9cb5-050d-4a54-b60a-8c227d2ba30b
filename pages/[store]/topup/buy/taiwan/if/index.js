import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import Image from "next/image"
import { FaArrowLeft, FaShoppingCart, FaTag, FaWifi, FaBoxes } from "react-icons/fa"

const IFProducts = () => {
  const router = useRouter()
  const { store } = router.query
  const [isMobile, setIsMobile] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [products, setProducts] = useState([])
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Fetch IF products from inventory API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/inventory?category=IF&category=Taiwan&category=Prepaid')
        if (!response.ok) {
          throw new Error('Failed to fetch products')
        }
        const data = await response.json()
        
        // Transform inventory data to match the expected product structure
        const transformedProducts = data.items?.map(item => ({
          id: item.sku,
          name: item.name,
          type: item.categories?.includes('CARD') ? 'basic' : 'internet',
          category: item.categories?.includes('CARD') ? 'Basic Top-Up' : 'Internet Package',
          denomination: item.price,
          price: item.price,
          currency: item.currency || 'NT$',
          deliveryMethod: item.configurations?.mobiletopup ? 'Elektrik' : 'PIN',
          description: item.description || 'Electronic delivery to mobile number',
          icon: item.categories?.includes('CARD') ? '📱' : '🌐',
          popular: false,
          image: Array.isArray(item.image) ? item.image[0] : item.image,
          sku: item.sku,
          provider: item.provider
        })) || []
        
        setProducts(transformedProducts)
        
        // Generate dynamic categories based on actual product data
        const productTypes = new Set(transformedProducts.map(product => product.type))
        const dynamicCategories = [
          { id: 'all', name: 'All Products', icon: FaTag }
        ]
        
        // Add categories based on what's actually available in the products
        if (productTypes.has('basic')) {
          dynamicCategories.push({ id: 'basic', name: 'Basic Top-Up', icon: FaShoppingCart })
        }
        if (productTypes.has('internet')) {
          dynamicCategories.push({ id: 'internet', name: 'Internet Packages', icon: FaWifi })
        }
        if (productTypes.has('bulk')) {
          dynamicCategories.push({ id: 'bulk', name: 'Bulk Packages', icon: FaBoxes })
        }
        
        setCategories(dynamicCategories)
      } catch (err) {
        console.error('Error fetching products:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.type === selectedCategory)

  const handleAddToCart = (product) => {
    // Navigate to checkout with SKU
    router.push(`/${store}/checkout?sku=${product.sku}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <div className="text-gray-600">Loading IF products...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-2">Error loading products</div>
          <div className="text-gray-600">{error}</div>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>IF Taiwan Mobile - Top-Up Products</title>
        <meta name="description" content="Purchase IF Taiwan Mobile prepaid vouchers, internet packages, and bulk deals" />
        <meta property="og:title" content="IF Taiwan Mobile - Top-Up Products" key="title" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link href={`/${store}/topup/buy/taiwan`} legacyBehavior>
                <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <FaArrowLeft className="w-5 h-5 mr-2" />
                  Back
                </a>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-blue-600">IF</span>
                </div>
                <div className="text-center">
                  <h1 className="text-xl font-bold text-gray-900">IF Taiwan Mobile</h1>
                </div>
              </div>
              <div className="w-16"></div>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center text-sm text-gray-600">
              <span>Selected Provider:</span>
              <span className="ml-2 font-medium text-gray-900">IF Taiwan Mobile</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Category Filter */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => {
                const IconComponent = category.icon
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`
                      flex items-center space-x-2 px-4 py-2 rounded-lg transition-all
                      ${selectedCategory === category.id
                        ? 'bg-green-500 text-white shadow-md'
                        : 'bg-white text-gray-700 border border-gray-300 hover:border-green-300'
                      }
                    `}
                  >
                    <IconComponent className="w-4 h-4" />
                    <span className="text-sm font-medium">{category.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Products Grid */}
          <div className={`grid gap-6 ${
            isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'
          }`}>
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-200 overflow-hidden group"
              >
                {/* Product Header */}
                <div className="p-6">
                  {product.image && (
                    <div className="mb-4">
                      <Image
                        src={product.image}
                        alt={product.name}
                        width={100}
                        height={60}
                        className="mx-auto object-contain"
                      />
                    </div>
                  )}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{product.icon}</div>
                      <div>
                        <h3 className="font-bold text-gray-900">{product.name}</h3>
                        <p className="text-sm text-gray-600">{product.category}</p>
                      </div>
                    </div>
                    {product.popular && (
                      <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                        Popular
                      </span>
                    )}
                  </div>

                  {/* Price */}
                  <div className="mb-4">
                    <div className="text-2xl font-bold text-green-600">
                      {product.currency} {product.price.toLocaleString()}
                    </div>
                    {product.bulkInfo && (
                      <div className="text-sm text-gray-600">
                        {product.bulkInfo.quantity} pieces × {product.currency}{product.bulkInfo.pricePerUnit}
                        <span className="text-green-600 font-medium ml-2">
                          Save {product.currency}{product.bulkInfo.savings}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Delivery Method */}
                  <div className="mb-4">
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-600">Delivery:</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        product.deliveryMethod === 'Elektrik' 
                          ? 'bg-blue-100 text-blue-800'
                          : product.deliveryMethod === 'PIN'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {product.deliveryMethod}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{product.description}</p>
                  </div>
                </div>

                {/* Action Button */}
                <div className="px-6 pb-6">
                  <button
                    onClick={() => handleAddToCart(product)}
                    className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                  >
                    <FaShoppingCart className="w-4 h-4" />
                    <span>Select Product</span>
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Info Section */}
          <div className="mt-12 bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
              IF Taiwan Mobile Services
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Delivery Methods:</h4>
                <ul className="space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    <span><strong>Elektrik:</strong> Instant delivery to mobile number</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                    <span><strong>PIN:</strong> Code sent via email/SMS</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Product Types:</h4>
                <ul className="space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    <span><strong>Basic Top-Up:</strong> Various denominations</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    <span><strong>Internet:</strong> Data packages and unlimited plans</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                    <span><strong>Bulk:</strong> Multi-piece discounted offers</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default IFProducts