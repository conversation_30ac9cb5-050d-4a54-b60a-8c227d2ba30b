import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import { FaArrowLeft, FaShoppingCart, FaPhone, FaEnvelope, FaCreditCard, FaCheck, FaInfoCircle } from "react-icons/fa"

const IFProductDetail = () => {
  const router = useRouter()
  const { store, productId } = router.query
  const [product, setProduct] = useState(null)
  const [phoneNumber, setPhoneNumber] = useState('')
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Product data - same as in index.js
  const products = [
    {
      id: 'if-50-elektrik',
      name: 'IF 50 NT',
      type: 'basic',
      category: 'Basic Top-Up',
      denomination: 50,
      price: 48,
      currency: 'NT$',
      deliveryMethod: 'Elektrik',
      description: 'Electronic delivery to mobile number',
      icon: '📱',
      popular: true,
      features: [
        'Instant delivery to mobile number',
        'No PIN code required',
        'Direct credit to account',
        '24/7 availability'
      ],
      instructions: [
        'Enter your IF Taiwan mobile number',
        'Complete payment',
        'Credit will be added instantly to your account',
        'Check your balance by dialing *123#'
      ]
    },
    {
      id: 'if-50-pin',
      name: 'IF 50 NT',
      type: 'basic',
      category: 'Basic Top-Up',
      denomination: 50,
      price: 50,
      currency: 'NT$',
      deliveryMethod: 'PIN',
      description: 'PIN code via email/SMS',
      icon: '🔢',
      popular: false,
      features: [
        'PIN code sent via email/SMS',
        'Can be used anytime',
        'Transferable to others',
        'Valid for 12 months'
      ],
      instructions: [
        'Enter your email address',
        'Complete payment',
        'Receive PIN code via email/SMS',
        'Dial *123*PIN# to redeem'
      ]
    },
    {
      id: 'if-150-elektrik',
      name: 'IF 150 NT',
      type: 'basic',
      category: 'Basic Top-Up',
      denomination: 150,
      price: 145,
      currency: 'NT$',
      deliveryMethod: 'Elektrik',
      description: 'Electronic delivery to mobile number',
      icon: '📱',
      popular: true,
      features: [
        'Instant delivery to mobile number',
        'No PIN code required',
        'Direct credit to account',
        '24/7 availability'
      ],
      instructions: [
        'Enter your IF Taiwan mobile number',
        'Complete payment',
        'Credit will be added instantly to your account',
        'Check your balance by dialing *123#'
      ]
    }
  ]

  useEffect(() => {
    if (productId) {
      const foundProduct = products.find(p => p.id === productId)
      setProduct(foundProduct)
      if (!foundProduct) {
        setError('Product not found')
      }
    }
  }, [productId])

  const handlePurchase = async () => {
    if (!product) return
    
    // Validate required fields
    if (product.deliveryMethod === 'Elektrik' && !phoneNumber) {
      setError('Please enter your mobile number')
      return
    }
    
    if (product.deliveryMethod === 'PIN' && !email) {
      setError('Please enter your email address')
      return
    }

    setIsLoading(true)
    setError('')
    
    try {
      // Add to cart and redirect to checkout
      const cartItem = {
        sku: product.id,
        name: product.name,
        price: product.price,
        currency: product.currency,
        quantity: 1,
        type: 'topup',
        category: 'taiwan-if',
        deliveryMethod: product.deliveryMethod,
        phoneNumber: product.deliveryMethod === 'Elektrik' ? phoneNumber : undefined,
        email: product.deliveryMethod === 'PIN' ? email : undefined,
        productData: product
      }
      
      // Get existing cart from localStorage
      const existingCart = JSON.parse(localStorage.getItem('cart') || '[]')
      
      // Add new item to cart
      const updatedCart = [...existingCart, cartItem]
      
      // Save to localStorage
      localStorage.setItem('cart', JSON.stringify(updatedCart))
      
      // Redirect to checkout
      router.push(`/${store}/checkout`)
      
    } catch (err) {
      setError('Failed to add product to cart. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  if (!product && !error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product...</p>
        </div>
      </div>
    )
  }

  if (error && !product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Product Not Found</h1>
          <p className="text-gray-600 mb-6">The requested product could not be found.</p>
          <Link href={`/${store}/topup/buy/taiwan/if`} legacyBehavior>
            <a className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              Back to Products
            </a>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{product.name} - IF Taiwan Mobile Top-Up</title>
        <meta name="description" content={`Purchase ${product.name} - ${product.description}`} />
        <meta property="og:title" content={`${product.name} - IF Taiwan Mobile Top-Up`} key="title" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link href={`/${store}/topup/buy/taiwan/if`} legacyBehavior>
                <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <FaArrowLeft className="w-5 h-5 mr-2" />
                  Back to Products
                </a>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-blue-600">IF</span>
                </div>
                <div className="text-center">
                  <h1 className="text-xl font-bold text-gray-900">IF Taiwan Mobile</h1>
                </div>
              </div>
              <div className="w-16"></div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Product Information */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="text-center mb-6">
                <div className="text-6xl mb-4">{product.icon}</div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">{product.name}</h2>
                <p className="text-gray-600">{product.category}</p>
                <div className="mt-4">
                  <span className="text-3xl font-bold text-green-600">
                    {product.currency} {product.price.toLocaleString()}
                  </span>
                </div>
              </div>

              {/* Delivery Method */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Delivery Method</h3>
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-2 rounded-full text-sm font-medium ${
                    product.deliveryMethod === 'Elektrik' 
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-purple-100 text-purple-800'
                  }`}>
                    {product.deliveryMethod}
                  </span>
                  <span className="text-gray-600">{product.description}</span>
                </div>
              </div>

              {/* Features */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Features</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <FaCheck className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Instructions */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">How it works</h3>
                <ol className="space-y-2">
                  {product.instructions.map((instruction, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <span className="bg-green-100 text-green-800 text-xs font-bold px-2 py-1 rounded-full flex-shrink-0">
                        {index + 1}
                      </span>
                      <span className="text-gray-700">{instruction}</span>
                    </li>
                  ))}
                </ol>
              </div>
            </div>

            {/* Purchase Form */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Purchase {product.name}</h3>
              
              {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
                  <FaInfoCircle className="inline mr-2" />
                  {error}
                </div>
              )}
              
              {success && (
                <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded text-sm">
                  <FaCheck className="inline mr-2" />
                  {success}
                </div>
              )}

              <div className="space-y-4">
                {/* Phone Number Input for Elektrik */}
                {product.deliveryMethod === 'Elektrik' && (
                  <div>
                    <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-2">
                      <FaPhone className="inline mr-2" />
                      IF Taiwan Mobile Number
                    </label>
                    <input
                      type="tel"
                      id="phoneNumber"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, ''))}
                      placeholder="0912345678"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      required
                      maxLength="10"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter your 10-digit IF Taiwan mobile number
                    </p>
                  </div>
                )}

                {/* Email Input for PIN */}
                {product.deliveryMethod === 'PIN' && (
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      <FaEnvelope className="inline mr-2" />
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      PIN code will be sent to this email address
                    </p>
                  </div>
                )}

                {/* Order Summary */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Order Summary</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Product:</span>
                      <span className="font-medium">{product.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Denomination:</span>
                      <span className="font-medium">{product.currency} {product.denomination}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Delivery:</span>
                      <span className="font-medium">{product.deliveryMethod}</span>
                    </div>
                    <div className="border-t pt-2 mt-2">
                      <div className="flex justify-between text-lg font-bold">
                        <span>Total:</span>
                        <span className="text-green-600">{product.currency} {product.price.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Purchase Button */}
                <button
                  onClick={handlePurchase}
                  disabled={isLoading || 
                    (product.deliveryMethod === 'Elektrik' && !phoneNumber) ||
                    (product.deliveryMethod === 'PIN' && !email)
                  }
                  className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Processing...</span>
                    </>
                  ) : (
                    <>
                      <FaCreditCard className="w-4 h-4" />
                      <span>Add to Cart & Checkout</span>
                    </>
                  )}
                </button>

                <p className="text-xs text-gray-500 text-center">
                  By purchasing, you agree to our terms and conditions
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default IFProductDetail