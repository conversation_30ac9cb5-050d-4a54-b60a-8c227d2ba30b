import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import Image from "next/image"
import { FaArrowLeft, FaShoppingCart, FaTag, FaWifi, FaBoxes } from "react-icons/fa"

const OKProducts = () => {
  const router = useRouter()
  const { store } = router.query
  const [isMobile, setIsMobile] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [products, setProducts] = useState([])
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Fetch OK products from inventory API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/inventory')
        if (!response.ok) {
          throw new Error('Failed to fetch products')
        }
        const data = await response.json()
        
        // Transform inventory data to match the expected product structure
        // Filter for OK Taiwan Prepaid products and transform
        const okProducts = data?.filter(item => 
          item.categories?.includes('OK') && 
          item.categories?.includes('Taiwan') && 
          item.categories?.includes('Prepaid')
        ) || []
        
        const transformedProducts = okProducts.map(item => ({
          id: item.sku,
          name: item.name,
          type: item.categories?.includes('CARD') ? 'basic' : 'internet',
          category: item.categories?.includes('CARD') ? 'Basic Top-Up' : 'Internet Package',
          denomination: item.price,
          price: item.price,
          currency: item.currency || 'NT$',
          deliveryMethod: item.configurations?.mobiletopup ? 'Elektrik' : 'PIN',
          description: item.description || 'Electronic delivery to mobile number',
          icon: item.categories?.includes('CARD') ? '📱' : '🌐',
          popular: false,
          image: Array.isArray(item.image) ? item.image[0] : item.image,
          sku: item.sku,
          provider: item.provider
        })) || []
        
        setProducts(transformedProducts)
        
        // Generate dynamic categories based on actual product data
        const productTypes = new Set(transformedProducts.map(product => product.type))
        const dynamicCategories = [
          { id: 'all', name: 'All Products', icon: FaTag }
        ]
        
        // Add categories based on what's actually available in the products
        if (productTypes.has('basic')) {
          dynamicCategories.push({ id: 'basic', name: 'Basic Top-Up', icon: FaShoppingCart })
        }
        if (productTypes.has('internet')) {
          dynamicCategories.push({ id: 'internet', name: 'Internet Packages', icon: FaWifi })
        }
        if (productTypes.has('bulk')) {
          dynamicCategories.push({ id: 'bulk', name: 'Bulk Packages', icon: FaBoxes })
        }
        
        setCategories(dynamicCategories)
      } catch (err) {
        console.error('Error fetching products:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.type === selectedCategory)

  const handleAddToCart = (product) => {
    // Navigate to checkout with SKU
    router.push(`/${store}/checkout?sku=${product.sku}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-blue-600 font-medium">Loading OK Prepaid products...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-blue-600 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-blue-800 mb-2">Error Loading Products</h2>
          <p className="text-blue-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>OK Prepaid - Taiwan Prepaid | {store}</title>
        <meta name="description" content="Buy OK Prepaid top-ups and internet packages for Taiwan. Instant delivery, competitive prices." />
      </Head>
      
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Link href={`/${store}/topup/buy/taiwan`} className="text-blue-600 hover:text-blue-700 transition-colors">
                  <FaArrowLeft className="w-5 h-5" />
                </Link>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">OK Prepaid</h1>
                  <p className="text-sm text-gray-600">Taiwan Prepaid Services</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">Products Available</div>
                <div className="text-lg font-bold text-blue-600">{products.length}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Category Filter */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => {
                const IconComponent = category.icon
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`
                      flex items-center space-x-2 px-4 py-2 rounded-full transition-all duration-200
                      ${
                        selectedCategory === category.id
                          ? 'bg-blue-600 text-white shadow-lg'
                          : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200'
                      }
                    `}
                  >
                    <IconComponent className="w-4 h-4" />
                    <span className="font-medium">{category.name}</span>
                    <span className="text-xs opacity-75">
                      ({category.id === 'all' ? products.length : products.filter(p => p.type === category.id).length})
                    </span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Products Grid */}
          {filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📱</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Products Found</h3>
              <p className="text-gray-600">No products available in this category.</p>
            </div>
          ) : (
            <div className={`grid gap-6 ${
              isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            }`}>
              {filteredProducts.map((product) => (
                <div key={product.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 overflow-hidden">
                  {/* Product Image */}
                  <div className="relative h-48 bg-gradient-to-br from-blue-100 to-blue-200">
                    {product.image ? (
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none'
                        }}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <span className="text-6xl">{product.icon}</span>
                      </div>
                    )}
                    
                    {/* Category Badge */}
                    <div className="absolute top-3 left-3">
                      <span className="bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                        {product.category}
                      </span>
                    </div>
                    
                    {/* Popular Badge */}
                    {product.popular && (
                      <div className="absolute top-3 right-3">
                        <span className="bg-yellow-400 text-yellow-900 text-xs font-medium px-2 py-1 rounded-full">
                          Popular
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {/* Product Info */}
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                      {product.name}
                    </h3>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Price:</span>
                        <span className="text-lg font-bold text-blue-600">
                          {product.currency}{product.price}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Delivery:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {product.deliveryMethod}
                        </span>
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {product.description}
                    </p>
                    
                    {/* Add to Cart Button */}
                    <button
                      onClick={() => handleAddToCart(product)}
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center space-x-2"
                    >
                      <FaShoppingCart className="w-4 h-4" />
                      <span>Add to Cart</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Info Section */}
          <div className="mt-12 bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
              About OK Prepaid
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">💳</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Prepaid Cards</h4>
                <p className="text-gray-600">
                  Convenient prepaid vouchers for mobile services
                </p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">⚡</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Instant Delivery</h4>
                <p className="text-gray-600">
                  Electronic top-ups delivered immediately
                </p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">💰</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Competitive Rates</h4>
                <p className="text-gray-600">
                  Best value prepaid options for Taiwan
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default OKProducts