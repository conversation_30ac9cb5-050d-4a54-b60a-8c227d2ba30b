import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import { FaShoppingCart, FaTicketAlt, FaReceipt, FaClock, FaArrowLeft, FaShieldAlt, FaGlobe, FaCreditCard } from "react-icons/fa"
import { fetchStore } from '../../../utils/storeProvider'
import Layout from '../../../layouts/layout'
import MobileHeader from '../../../templates/shein/components/MobileHeader'
import DesktopHeader from '../../../templates/shein/components/DesktopHeader'

const TopUpStorefront = ({ allStores = [], categoriesarrayA = {}, currentStore }) => {
  const router = useRouter()
  const { store } = router.query
  const [storeData, setStoreData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false)

  // Fetch store data on client side if not provided via SSR
  useEffect(() => {
    const loadStoreData = async () => {
      if (!store || !currentStore) return
      
      setLoading(true)
      
      try {
        setStoreData(currentStore)
      } catch (error) {
        console.error('Error loading store data:', error)
        setStoreData(null)
      } finally {
        setLoading(false)
      }
    }

    loadStoreData()
  }, [store, currentStore])
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768) // Match SHEIN theme breakpoint
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query)
    // Implement search functionality if needed
  }

  const navigationSections = [
    {
      id: 'buy-voucher',
      title: 'Nạp Thẻ',
      icon: FaCreditCard,
      href: `/${store}/topup/buy`,
    },
    {
      id: 'my-voucher',
      title: 'Thẻ đã mua',
      icon: FaTicketAlt,
      href: `/${store}/topup/vouchers`,
    },
    {
      id: 'my-order',
      title: 'Hoá đơn',
      icon: FaReceipt,
      href: `/${store}/topup/receipts`,
    }
  ]

  if (loading) {
    return (
      <Layout allStores={allStores} categoriesarrayA={categoriesarrayA}>
        <div className="flex items-center justify-center px-4 py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600 text-sm">Đang tải dữ liệu cửa hàng...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout allStores={allStores} categoriesarrayA={categoriesarrayA}>
      <Head>
        <title>{store ? `${store.charAt(0).toUpperCase() + store.slice(1)} ` : ''}Cửa Hàng Nạp Tiền | MagShop</title>
        <meta name="description" content="Mua thẻ SIM và voucher nạp tiền để kết nối toàn cầu" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="theme-color" content="#fb923c" />
      </Head>

      {/* SHEIN Theme Headers */}
      {storeData?.layouttemplate === 'shein' && isMobile && (
        <MobileHeader 
          store={store}
          currentstore={storeData}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}

      {storeData?.layouttemplate === 'shein' && !isMobile && (
        <DesktopHeader 
          store={store}
          currentstore={storeData}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}

      {/* Default Header for non-SHEIN themes */}
      {storeData?.layouttemplate !== 'shein' && (
        <div className="bg-white shadow-sm border-b w-full">
          <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
            <div className="flex items-center justify-start h-14 sm:h-16">
              <div className="flex items-center">
                <Link href={`/${store}`} legacyBehavior>
                  <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                    <FaArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                    <span className="text-sm sm:text-base">
                      <span className="sm:hidden">Quay lại</span>
                      <span className="hidden sm:inline">Quay lại Cửa hàng</span>
                    </span>
                  </a>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="bg-gray-50 min-h-[calc(100vh-120px)] w-full overflow-x-hidden">
        <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-8 w-full">
          {/* Welcome Section */}
          {/* <div className={`text-center ${isMobile ? 'mb-6' : 'mb-12'}`}>
            <h2 className={`${isMobile ? 'text-xl' : 'text-3xl'} font-bold text-gray-900 ${isMobile ? 'mb-2' : 'mb-4'}`}>
              {storeData?.name || store?.toUpperCase()} Shop Top-up
            </h2>
          </div> */}

          {/* Navigation Grid */}
          <div className="space-y-3 w-full">
            {navigationSections.map((section) => {
              const IconComponent = section.icon
              return (
                <Link key={section.id} href={section.href} legacyBehavior>
                  <a className="bg-white rounded-lg p-4 shadow-sm border flex items-center space-x-3 hover:shadow-md transition-shadow w-full">
                    <IconComponent className="w-6 h-6 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700 font-medium">{section.title}</span>
                  </a>
                </Link>
              )
            })}
          </div>
        </div>
      </div>
    </Layout>
  )
}

// Export with static generation
export async function getStaticPaths() {
  const allStores = await fetchStore()
  const paths = allStores.map((store) => ({
    params: { store: store.storeId },
  }))

  return {
    paths,
    fallback: 'blocking',
  }
}

export async function getStaticProps({ params }) {
  const allStores = await fetchStore()
  const store = allStores.find((s) => s.storeId === params.store)
  
  if (!store) {
    return {
      notFound: true,
    }
  }

  // Create lightweight store data for layout (only essential fields)
  const lightweightStores = allStores.map(s => ({
    storeId: s.storeId,
    name: s.name,
    logo: s.logo,
    activestatus: s.activestatus,
    layouttemplate: s.layouttemplate
  }))

  // Create categories array structure for layout
  const categoriesarrayA = {
    [params.store]: [],
    all: []
  }

  return {
    props: {
      allStores: lightweightStores,
      categoriesarrayA,
      currentStore: {
        storeId: store.storeId,
        name: store.name,
        logo: store.logo,
        activestatus: store.activestatus,
        slogan: store.slogan,
        layouttemplate: store.layouttemplate
      },
    },
    revalidate: 60, // Revalidate every minute
  }
}

export default TopUpStorefront