import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import { FaArrowLeft, FaHistory, FaSearch, FaFilter, FaCalendarAlt, FaEye, FaDownload, FaTicketAlt, FaCheckCircle, FaClock, FaTimesCircle, FaExclamationTriangle } from "react-icons/fa"
import { fetchStore } from '../../../../utils/storeProvider'
import Layout from '../../../../layouts/layout'

const OrderHistory = ({ allStores = [], categoriesarrayA = {} }) => {
  const router = useRouter()
  const { store } = router.query
  const [isMobile, setIsMobile] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Sample order data - in real app, this would come from API
  const orders = [
    {
      id: 'ORD-2024-001234',
      status: 'completed',
      orderDate: '2024-01-15T10:30:00Z',
      completedDate: '2024-01-15T10:32:15Z',
      items: [
        {
          productName: 'IF 150 NT Top-up',
          provider: 'IF Taiwan Mobile',
          country: 'Taiwan',
          quantity: 1,
          price: 150
        },
        {
          productName: 'Steam Wallet 500 NT',
          provider: 'Steam',
          country: 'Global',
          quantity: 1,
          price: 500
        }
      ],
      total: 682.5,
      currency: 'NT$',
      paymentMethod: 'Credit Card',
      voucherCount: 2
    },
    {
      id: 'ORD-2024-001233',
      status: 'completed',
      orderDate: '2024-01-12T14:20:00Z',
      completedDate: '2024-01-12T14:22:45Z',
      items: [
        {
          productName: 'GoPay Top-up 50,000 IDR',
          provider: 'GoPay',
          country: 'Indonesia',
          quantity: 1,
          price: 50000
        }
      ],
      total: 52500,
      currency: 'IDR',
      paymentMethod: 'Bank Transfer',
      voucherCount: 1
    },
    {
      id: 'ORD-2024-001232',
      status: 'processing',
      orderDate: '2024-01-14T09:15:00Z',
      items: [
        {
          productName: 'IF Internet 30 Days 599',
          provider: 'IF Taiwan Mobile',
          country: 'Taiwan',
          quantity: 2,
          price: 599
        }
      ],
      total: 1258.95,
      currency: 'NT$',
      paymentMethod: 'Credit Card',
      voucherCount: 2
    },
    {
      id: 'ORD-2024-001231',
      status: 'failed',
      orderDate: '2024-01-10T16:45:00Z',
      failedDate: '2024-01-10T16:47:30Z',
      items: [
        {
          productName: 'PlayStation Store 1000 NT',
          provider: 'PlayStation',
          country: 'Taiwan',
          quantity: 1,
          price: 1000
        }
      ],
      total: 1050,
      currency: 'NT$',
      paymentMethod: 'Credit Card',
      voucherCount: 0,
      failureReason: 'Payment declined'
    },
    {
      id: 'ORD-2024-001230',
      status: 'cancelled',
      orderDate: '2024-01-08T11:30:00Z',
      cancelledDate: '2024-01-08T12:00:00Z',
      items: [
        {
          productName: 'Garena Shells 1000',
          provider: 'Garena',
          country: 'Global',
          quantity: 1,
          price: 300
        }
      ],
      total: 315,
      currency: 'NT$',
      paymentMethod: 'Credit Card',
      voucherCount: 0,
      cancellationReason: 'Customer request'
    }
  ]

  const statusOptions = [
    { value: 'all', label: 'All Status', count: orders.length },
    { value: 'completed', label: 'Completed', count: orders.filter(o => o.status === 'completed').length },
    { value: 'processing', label: 'Processing', count: orders.filter(o => o.status === 'processing').length },
    { value: 'failed', label: 'Failed', count: orders.filter(o => o.status === 'failed').length },
    { value: 'cancelled', label: 'Cancelled', count: orders.filter(o => o.status === 'cancelled').length }
  ]

  const dateOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'quarter', label: 'Last 3 Months' },
    { value: 'year', label: 'This Year' }
  ]

  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'amount_high', label: 'Amount: High to Low' },
    { value: 'amount_low', label: 'Amount: Low to High' }
  ]

  const filterOrdersByDate = (order) => {
    if (dateFilter === 'all') return true
    
    const orderDate = new Date(order.orderDate)
    const now = new Date()
    
    switch (dateFilter) {
      case 'today':
        return orderDate.toDateString() === now.toDateString()
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        return orderDate >= weekAgo
      case 'month':
        return orderDate.getMonth() === now.getMonth() && orderDate.getFullYear() === now.getFullYear()
      case 'quarter':
        const threeMonthsAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        return orderDate >= threeMonthsAgo
      case 'year':
        return orderDate.getFullYear() === now.getFullYear()
      default:
        return true
    }
  }

  const filteredOrders = orders
    .filter(order => {
      const matchesSearch = order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           order.items.some(item => 
                             item.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                             item.provider.toLowerCase().includes(searchQuery.toLowerCase())
                           )
      const matchesStatus = statusFilter === 'all' || order.status === statusFilter
      const matchesDate = filterOrdersByDate(order)
      return matchesSearch && matchesStatus && matchesDate
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.orderDate) - new Date(a.orderDate)
        case 'oldest':
          return new Date(a.orderDate) - new Date(b.orderDate)
        case 'amount_high':
          return b.total - a.total
        case 'amount_low':
          return a.total - b.total
        default:
          return new Date(b.orderDate) - new Date(a.orderDate)
      }
    })

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'processing': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <FaCheckCircle className="w-4 h-4 text-green-600" />
      case 'processing': return <FaClock className="w-4 h-4 text-yellow-600" />
      case 'failed': return <FaTimesCircle className="w-4 h-4 text-red-600" />
      case 'cancelled': return <FaExclamationTriangle className="w-4 h-4 text-gray-600" />
      default: return <FaClock className="w-4 h-4 text-gray-600" />
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTotalStats = () => {
    const completed = orders.filter(o => o.status === 'completed')
    const totalSpent = completed.reduce((sum, order) => sum + order.total, 0)
    const totalVouchers = completed.reduce((sum, order) => sum + order.voucherCount, 0)
    
    return {
      totalOrders: orders.length,
      completedOrders: completed.length,
      totalSpent,
      totalVouchers
    }
  }

  const stats = getTotalStats()

  return (
    <Layout allStores={allStores} categoriesarrayA={categoriesarrayA}>
      <Head>
        <title>Order History - Top-Up Storefront</title>
        <meta name="description" content="View your complete order history and transaction details" />
        <meta property="og:title" content="Order History - Top-Up Storefront" key="title" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-purple-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link href={`/${store}/topup`} legacyBehavior>
                <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <FaArrowLeft className="w-5 h-5 mr-2" />
                  Back
                </a>
              </Link>
              <div className="flex items-center space-x-3">
                <FaHistory className="w-6 h-6 text-purple-600" />
                <div className="text-center">
                  <h1 className="text-xl font-bold text-gray-900">Order History</h1>
                </div>
              </div>
              <div className="w-16"></div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">{stats.totalOrders}</p>
                <p className="text-sm text-gray-600">Total Orders</p>
              </div>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{stats.completedOrders}</p>
                <p className="text-sm text-gray-600">Completed</p>
              </div>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{stats.totalVouchers}</p>
                <p className="text-sm text-gray-600">Vouchers</p>
              </div>
            </div>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">NT$ {stats.totalSpent.toLocaleString()}</p>
                <p className="text-sm text-gray-600">Total Spent</p>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search orders..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              
              {/* Status Filter */}
              <div className="flex items-center space-x-2">
                <FaFilter className="text-gray-400 w-4 h-4" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label} ({option.count})
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Date Filter */}
              <div className="flex items-center space-x-2">
                <FaCalendarAlt className="text-gray-400 w-4 h-4" />
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {dateOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Orders List */}
          <div className="space-y-4">
            {filteredOrders.length === 0 ? (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
                <FaHistory className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery || statusFilter !== 'all' || dateFilter !== 'all'
                    ? 'Try adjusting your search or filter criteria'
                    : 'You haven\'t placed any orders yet'}
                </p>
                <Link href={`/${store}/topup/buy`} legacyBehavior>
                  <a className="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                    <FaTicketAlt className="w-4 h-4 mr-2" />
                    Place Your First Order
                  </a>
                </Link>
              </div>
            ) : (
              filteredOrders.map((order) => (
                <div key={order.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  <div className="p-6">
                    {/* Order Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(order.status)}
                        <div>
                          <h3 className="font-bold text-gray-900">{order.id}</h3>
                          <p className="text-sm text-gray-600">{formatDate(order.orderDate)}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                        <span className="text-lg font-bold text-purple-600">
                          {order.currency} {order.total.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    {/* Order Items */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Items ({order.items.length})</h4>
                      <div className="space-y-2">
                        {order.items.map((item, index) => (
                          <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                            <div className="flex-1">
                              <p className="font-medium text-gray-900">{item.productName}</p>
                              <p className="text-sm text-gray-600">{item.provider} • {item.country}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">Qty: {item.quantity}</p>
                              <p className="text-sm text-gray-600">{order.currency} {item.price.toLocaleString()}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Order Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                      <div>
                        <p className="text-gray-600">Payment Method</p>
                        <p className="font-medium">{order.paymentMethod}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Vouchers</p>
                        <p className="font-medium">{order.voucherCount} voucher(s)</p>
                      </div>
                      <div>
                        <p className="text-gray-600">
                          {order.status === 'completed' && 'Completed'}
                          {order.status === 'failed' && 'Failed'}
                          {order.status === 'cancelled' && 'Cancelled'}
                          {order.status === 'processing' && 'Status'}
                        </p>
                        <p className="font-medium">
                          {order.completedDate && formatDate(order.completedDate)}
                          {order.failedDate && formatDate(order.failedDate)}
                          {order.cancelledDate && formatDate(order.cancelledDate)}
                          {order.status === 'processing' && 'In Progress'}
                        </p>
                      </div>
                    </div>

                    {/* Failure/Cancellation Reason */}
                    {(order.failureReason || order.cancellationReason) && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                        <p className="text-sm font-medium text-red-700">
                          {order.status === 'failed' ? 'Failure Reason:' : 'Cancellation Reason:'}
                        </p>
                        <p className="text-sm text-red-600">
                          {order.failureReason || order.cancellationReason}
                        </p>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="flex items-center space-x-4">
                        {order.status === 'completed' && (
                          <>
                            <Link href={`/${store}/topup/receipt?orderId=${order.id}`} legacyBehavior>
                              <a className="flex items-center space-x-1 text-purple-600 hover:text-purple-700 transition-colors">
                                <FaEye className="w-4 h-4" />
                                <span className="text-sm">View Receipt</span>
                              </a>
                            </Link>
                            <Link href={`/${store}/topup/vouchers`} legacyBehavior>
                              <a className="flex items-center space-x-1 text-green-600 hover:text-green-700 transition-colors">
                                <FaTicketAlt className="w-4 h-4" />
                                <span className="text-sm">View Vouchers</span>
                              </a>
                            </Link>
                          </>
                        )}
                        {order.status === 'processing' && (
                          <span className="text-sm text-yellow-600">Processing your order...</span>
                        )}
                        {(order.status === 'failed' || order.status === 'cancelled') && (
                          <Link href={`/${store}/topup/buy`} legacyBehavior>
                            <a className="flex items-center space-x-1 text-purple-600 hover:text-purple-700 transition-colors">
                              <FaTicketAlt className="w-4 h-4" />
                              <span className="text-sm">Try Again</span>
                            </a>
                          </Link>
                        )}
                      </div>
                      <button className="text-gray-400 hover:text-gray-600 transition-colors">
                        <FaDownload className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Quick Actions */}
          <div className="mt-12 bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
              Quick Actions
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link href={`/${store}/topup/buy`} legacyBehavior>
                <a className="flex items-center justify-center space-x-2 bg-purple-500 hover:bg-purple-600 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                  <FaTicketAlt className="w-4 h-4" />
                  <span>Buy New Voucher</span>
                </a>
              </Link>
              <Link href={`/${store}/topup/vouchers`} legacyBehavior>
                <a className="flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                  <FaTicketAlt className="w-4 h-4" />
                  <span>View My Vouchers</span>
                </a>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

// Export with static generation
export async function getStaticPaths() {
  const allStores = await fetchStore()
  const paths = allStores.map((store) => ({
    params: { store: store.storeId },
  }))

  return {
    paths,
    fallback: 'blocking',
  }
}

export async function getStaticProps({ params }) {
  const allStores = await fetchStore()
  const store = allStores.find((s) => s.storeId === params.store)
  
  if (!store) {
    return {
      notFound: true,
    }
  }

  // Create lightweight store data for layout (only essential fields)
  const lightweightStores = allStores.map(s => ({
    storeId: s.storeId,
    name: s.name,
    logo: s.logo,
    activestatus: s.activestatus
  }))

  // Create categories array structure for layout
  const categoriesarrayA = {
    [params.store]: [],
    all: []
  }

  return {
    props: {
      allStores: lightweightStores,
      categoriesarrayA,
      currentStore: {
        storeId: store.storeId,
        name: store.name,
        logo: store.logo,
        activestatus: store.activestatus,
        slogan: store.slogan
      },
    },
    revalidate: 60,
  }
}

export default OrderHistory