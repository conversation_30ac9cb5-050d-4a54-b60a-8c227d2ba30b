import { useRouter } from "next/router"
import { useState, useEffect } from "react"
import Head from "next/head"
import Link from "next/link"
import { FaArrowLeft, FaReceipt, FaSearch, FaCalendarAlt, FaEye } from "react-icons/fa"
import { fetchStore } from '../../../../utils/storeProvider'
import Layout from '../../../../layouts/layout'
import axios from 'axios'

const PurchaseReceipts = ({ allStores = [], categoriesarrayA = {} }) => {
  const router = useRouter()
  const { store } = router.query
  
  const [customerData, setCustomerData] = useState(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [receipts, setReceipts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredReceipts, setFilteredReceipts] = useState([])

  const fetchReceipts = async (customer) => {
    if (!customer) {
      setLoading(false)
      setError('Người dùng chưa xác thực')
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      // Use the same admin orders endpoint as customer dashboard
      const storeParam = store
      const response = await axios.get(`/api/admin/orders${storeParam ? `?storeId=${storeParam}` : ''}`)
      
      if (!response.data.success) {
        setError('Không thể tải danh sách hóa đơn')
        setLoading(false)
        return
      }
      
      const allOrders = response.data.orders || []
      
      // Filter orders for the specific customer using same logic as dashboard
      const customerIdentifier = customer.phone || customer.id || customer.customerId
      const isPhoneNumber = /^\d+$/.test(customerIdentifier)
      
      const filteredOrders = allOrders.filter(order => {
        if (isPhoneNumber) {
          // Match by phone number or customerId for phone numbers
          return order.customerPhone === customerIdentifier || 
                 order.customerId === customerIdentifier
        } else {
          // Match by customerId, customerName, or customerPhone for non-phone identifiers
          return order.customerId === customerIdentifier ||
                 order.customerName === customerIdentifier ||
                 order.customerPhone === customerIdentifier
        }
      })
      
      // Transform orders to receipt format
      const transformedReceipts = filteredOrders.map(order => ({
        orderId: order.id,
        status: order.status,
        purchaseDate: order.createdAt,
        completedDate: order.updatedAt,
        customer: {
          name: order.customerName || 'Không xác định',
          email: order.customerEmail || '',
          phone: order.customerPhone || ''
        },
        total: order.amount || 0,
        currency: order.currency || 'NT$',
        itemCount: order.items?.length || 0,
        paymentMethod: order.paymentMethod || 'Không xác định'
      }))
      
      setReceipts(transformedReceipts)
      setFilteredReceipts(transformedReceipts)
    } catch (error) {
      console.error('Error fetching receipts:', error)
      setError('Không thể tải dữ liệu hóa đơn')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Check authentication using localStorage
    const storeSpecificCustomerData = localStorage.getItem(`customerData_${store}`)
    const genericCustomerData = localStorage.getItem('customerData')
    
    if (storeSpecificCustomerData || genericCustomerData) {
      try {
        const customer = JSON.parse(storeSpecificCustomerData || genericCustomerData)
        setCustomerData(customer)
        setIsAuthenticated(true)
        fetchReceipts(customer)
      } catch (error) {
        console.error('Error parsing customer data:', error)
        setIsAuthenticated(false)
        setLoading(false)
      }
    } else {
      setIsAuthenticated(false)
      setLoading(false)
    }
  }, [store])

  useEffect(() => {
    // Filter receipts based on search term
    if (!searchTerm) {
      setFilteredReceipts(receipts)
    } else {
      const filtered = receipts.filter(receipt => 
        (receipt.orderId || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (receipt.customer?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (receipt.customer?.phone || '').includes(searchTerm)
      )
      setFilteredReceipts(filtered)
    }
  }, [searchTerm, receipts])

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'Hoàn thành'
      case 'pending':
        return 'Đang xử lý'
      case 'failed':
        return 'Thất bại'
      case 'cancelled':
        return 'Đã hủy'
      default:
        return status || 'Không xác định'
    }
  }

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải danh sách hóa đơn...</p>
        </div>
      </div>
    )
  }

  // Show login prompt for unauthenticated users
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <FaReceipt className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Yêu Cầu Xác Thực</h2>
            <p className="text-gray-600 mb-6">Vui lòng đăng nhập để xem danh sách hóa đơn của bạn.</p>
            <Link href={`/${store}/customer/login?redirect=${encodeURIComponent(router.asPath)}`} legacyBehavior>
              <a className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                Đăng Nhập
              </a>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Layout allStores={allStores} categoriesarrayA={categoriesarrayA}>
      <Head>
        <title>Danh Sách Hóa Đơn - Cửa Hàng Nạp Thẻ</title>
        <meta name="description" content="Xem tất cả hóa đơn mua hàng của bạn" />
        <meta property="og:title" content="Danh Sách Hóa Đơn - Cửa Hàng Nạp Thẻ" key="title" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link href={`/${store}/topup`} legacyBehavior>
                <a className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <FaArrowLeft className="w-5 h-5 mr-2" />
                  Quay Lại
                </a>
              </Link>
              <div className="flex items-center space-x-3">
                <FaReceipt className="w-6 h-6 text-blue-600" />
                <div className="text-center">
                  <h1 className="text-xl font-bold text-gray-900">Danh Sách Hóa Đơn</h1>
                </div>
              </div>
              <div className="w-20"></div> {/* Spacer for balance */}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Search Bar */}
          <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Tìm kiếm theo mã đơn hàng, tên khách hàng hoặc số điện thoại..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Error State */}
          {error && (
            <div className="bg-white rounded-xl shadow-lg p-8 text-center mb-6">
              <FaReceipt className="w-16 h-16 text-red-300 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Lỗi Khi Tải Dữ Liệu</h2>
              <p className="text-gray-600 mb-6">{error}</p>
              <button 
                onClick={() => window.location.reload()} 
                className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Thử Lại
              </button>
            </div>
          )}

          {/* Receipts List */}
          {!error && (
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              {filteredReceipts.length === 0 ? (
                <div className="p-8 text-center">
                  <FaReceipt className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {searchTerm ? 'Không Tìm Thấy Kết Quả' : 'Chưa Có Hóa Đơn'}
                  </h3>
                  <p className="text-gray-600">
                    {searchTerm 
                      ? 'Không có hóa đơn nào phù hợp với từ khóa tìm kiếm.'
                      : 'Bạn chưa có hóa đơn nào. Hãy mua sắm để tạo hóa đơn đầu tiên!'}
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredReceipts.map((receipt) => (
                    <div key={receipt.orderId} className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              #{receipt.orderId}
                            </h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(receipt.status)}`}>
                              {getStatusText(receipt.status)}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-2">
                              <FaCalendarAlt className="w-4 h-4" />
                              <span>{formatDate(receipt.purchaseDate)}</span>
                            </div>
                            <div>
                              <span className="font-medium">Khách hàng:</span> {receipt.customer?.name || 'Không xác định'}
                            </div>
                            <div>
                              <span className="font-medium">Số lượng:</span> {receipt.itemCount} sản phẩm
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <p className="text-lg font-semibold text-blue-600">
                              {receipt.currency} {(receipt.total || 0).toLocaleString()}
                            </p>
                            <p className="text-sm text-gray-500">{receipt.paymentMethod || 'Không xác định'}</p>
                          </div>
                          
                          <Link href={`/${store}/topup/receipt?orderId=${receipt.orderId}`} legacyBehavior>
                            <a className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                              <FaEye className="w-4 h-4" />
                              <span>Xem</span>
                            </a>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}

export async function getServerSideProps(context) {
  const { store } = context.query
  
  try {
    const storeData = await fetchStore(store)
    
    if (!storeData) {
      return {
        notFound: true,
      }
    }
    
    return {
      props: {
        allStores: storeData.allStores || [],
        categoriesarrayA: storeData.categoriesarrayA || {},
      },
    }
  } catch (error) {
    console.error('Error fetching store data:', error)
    return {
      props: {
        allStores: [],
        categoriesarrayA: {},
      },
    }
  }
}

export default PurchaseReceipts