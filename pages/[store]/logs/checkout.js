import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaCalendar, FaFilter, FaSearch, FaDownload, FaSync, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';

// Component to display checkout logs
export default function CheckoutLogs() {
  const [dates, setDates] = useState([]);
  const [selectedDate, setSelectedDate] = useState('');
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Get store ID from URL
  const router = useRouter();
  const { store: storeId } = router.query;
  
  // Filtering states
  const [eventTypeFilter, setEventTypeFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredLogs, setFilteredLogs] = useState([]);
  const [expandedLog, setExpandedLog] = useState(null);
  
  // Load available log dates
  useEffect(() => {
    if (!storeId) return; // Wait for storeId to be available
    
    const fetchDates = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/logs/list');
        if (response.data && response.data.dates) {
          setDates(response.data.dates);
          // Select most recent date by default if available
          if (response.data.dates.length > 0) {
            setSelectedDate(response.data.dates[0].date);
          }
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch log dates:', error);
        setError('Failed to fetch available log dates');
        setLoading(false);
      }
    };

    fetchDates();
  }, [storeId]);

  // Load logs for selected date
  useEffect(() => {
    if (!selectedDate || !storeId) return;

    const fetchLogs = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/logs/checkout?date=${selectedDate}`);
        if (response.data && response.data.logs) {
          // Filter logs for this store if needed
          const storeSpecificLogs = response.data.logs.filter(log => {
            // Include logs that mention this store ID or don't have store info
            if (!log.data) return true;
            
            // Check for store ID in various places in the log data
            const logString = JSON.stringify(log.data).toLowerCase();
            return logString.includes(storeId.toLowerCase());
          });
          
          setLogs(storeSpecificLogs);
          setFilteredLogs(storeSpecificLogs);
        }
        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch logs:', error);
        setError(`Failed to fetch logs for ${selectedDate}`);
        setLoading(false);
      }
    };

    fetchLogs();
  }, [selectedDate, storeId]);

  // Apply filters when logs, event type filter, or search term changes
  useEffect(() => {
    if (!logs.length) {
      setFilteredLogs([]);
      return;
    }

    let filtered = [...logs];

    // Filter by event type
    if (eventTypeFilter) {
      filtered = filtered.filter(log => log.event === eventTypeFilter);
    }

    // Filter by search term (across all fields)
    if (searchTerm) {
      filtered = filtered.filter(log => {
        const logStr = JSON.stringify(log).toLowerCase();
        return logStr.includes(searchTerm.toLowerCase());
      });
    }

    setFilteredLogs(filtered);
  }, [logs, eventTypeFilter, searchTerm]);

  // Get unique event types from logs
  const getEventTypes = () => {
    if (!logs.length) return [];
    const types = [...new Set(logs.map(log => log.event))];
    return types;
  };

  // Function to refresh logs
  const refreshLogs = async () => {
    if (!selectedDate || !storeId) return;
    
    try {
      setLoading(true);
      const response = await axios.get(`/api/logs/checkout?date=${selectedDate}`);
      if (response.data && response.data.logs) {
        // Filter logs for this store
        const storeSpecificLogs = response.data.logs.filter(log => {
          if (!log.data) return true;
          const logString = JSON.stringify(log.data).toLowerCase();
          return logString.includes(storeId.toLowerCase());
        });
        
        setLogs(storeSpecificLogs);
        setFilteredLogs(storeSpecificLogs);
      }
      setLoading(false);
    } catch (error) {
      console.error('Failed to refresh logs:', error);
      setError(`Failed to refresh logs for ${selectedDate}`);
      setLoading(false);
    }
  };

  // Function to download logs as JSON
  const downloadLogs = () => {
    if (!filteredLogs.length) return;
    
    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const exportFileDefaultName = `${storeId}-checkout-logs-${selectedDate}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  // Function to format timestamp
  const formatTimestamp = (timestamp) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  // Function to render event-specific information
  const renderEventInfo = (log) => {
    switch (log.event) {
      case 'PAYMENT_API_CALL':
        return (
          <div className="text-sm">
            <span className="font-medium">{log.data.provider}</span>
            <span className="mx-1">→</span>
            <code className="bg-gray-100 px-1 rounded">{log.data.endpoint}</code>
          </div>
        );
      case 'PAYMENT_API_RESPONSE':
        return (
          <div className="text-sm">
            <span className="font-medium">{log.data.provider}</span>
            <span className="mx-1">←</span>
            <code className="bg-gray-100 px-1 rounded">{log.data.endpoint}</code>
            <span className={`ml-2 px-1.5 py-0.5 rounded text-xs ${
              log.data.statusCode >= 200 && log.data.statusCode < 300
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {log.data.statusCode}
            </span>
          </div>
        );
      case 'TRANSACTION':
        return (
          <div className="text-sm">
            <span className="font-medium">Order:</span> {log.data.orderId}
            <span className="mx-1">•</span>
            <span className={`px-1.5 py-0.5 rounded text-xs ${
              log.data.status === 'INITIATED' ? 'bg-blue-100 text-blue-800' :
              log.data.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
              log.data.status === 'FAILED' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {log.data.status}
            </span>
          </div>
        );
      case 'CHECKOUT_PROCESS':
        return (
          <div className="text-sm">
            <span className="font-medium">Process:</span> {log.data.process}
            {log.data.data && log.data.data.message && (
              <span className="ml-2 text-gray-600">"{log.data.data.message}"</span>
            )}
          </div>
        );
      case 'ERROR':
        return (
          <div className="text-sm text-red-600">
            <span className="font-medium">Source:</span> {log.data.source}
            <span className="mx-1">•</span>
            <span>{log.data.error.message}</span>
          </div>
        );
      default:
        return (
          <div className="text-sm">
            <pre className="whitespace-pre-wrap">{JSON.stringify(log.data, null, 2).substring(0, 100)}...</pre>
          </div>
        );
    }
  };

  return (
    <>
      <Head>
        <title>{storeId ? `${storeId} - Checkout Logs` : 'Checkout Logs'}</title>
      </Head>
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Checkout Logs</h1>
          <Link href={`/${storeId}`} className="text-blue-600 hover:underline">
            Back to Store
          </Link>
        </div>
        
        {/* Date selection and filters */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4 md:items-center mb-4">
            {/* Date Selector */}
            <div className="md:w-1/3">
              <label className="block text-sm font-medium text-gray-700 mb-1">Select Date</label>
              <div className="relative">
                <select
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  disabled={loading || !dates.length}
                >
                  {!dates.length && <option value="">No dates available</option>}
                  {dates.map((date) => (
                    <option key={date.date} value={date.date}>
                      {date.date} ({date.entries} entries)
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <FaCalendar className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Event Type Filter */}
            <div className="md:w-1/3">
              <label className="block text-sm font-medium text-gray-700 mb-1">Event Type</label>
              <div className="relative">
                <select
                  value={eventTypeFilter}
                  onChange={(e) => setEventTypeFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  disabled={loading || !logs.length}
                >
                  <option value="">All Events</option>
                  {getEventTypes().map((type) => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <FaFilter className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Search */}
            <div className="md:w-1/3">
              <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search in logs..."
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  disabled={loading || !logs.length}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <FaSearch className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex justify-end gap-2">
            <button
              onClick={refreshLogs}
              disabled={loading || !selectedDate}
              className="flex items-center px-3 py-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FaSync className={`mr-1.5 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={downloadLogs}
              disabled={loading || !filteredLogs.length}
              className="flex items-center px-3 py-1.5 bg-green-50 text-green-600 rounded hover:bg-green-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FaDownload className="mr-1.5" />
              Download
            </button>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="bg-red-50 text-red-700 p-4 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Log table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Event Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Details
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                      Loading...
                    </td>
                  </tr>
                ) : filteredLogs.length === 0 ? (
                  <tr>
                    <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                      No logs found
                    </td>
                  </tr>
                ) : (
                  filteredLogs.map((log, index) => (
                    <React.Fragment key={index}>
                      <tr className={expandedLog === index ? 'bg-blue-50' : index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatTimestamp(log.timestamp)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                            ${log.event === 'PAYMENT_API_CALL' ? 'bg-purple-100 text-purple-800' : 
                              log.event === 'PAYMENT_API_RESPONSE' ? 'bg-blue-100 text-blue-800' : 
                              log.event === 'TRANSACTION' ? 'bg-green-100 text-green-800' : 
                              log.event === 'CHECKOUT_PROCESS' ? 'bg-yellow-100 text-yellow-800' : 
                              log.event === 'ERROR' ? 'bg-red-100 text-red-800' : 
                              'bg-gray-100 text-gray-800'}`}
                          >
                            {log.event}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {renderEventInfo(log)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => setExpandedLog(expandedLog === index ? null : index)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            {expandedLog === index ? <FaChevronUp /> : <FaChevronDown />}
                          </button>
                        </td>
                      </tr>
                      {expandedLog === index && (
                        <tr>
                          <td colSpan="4" className="px-6 py-4 bg-gray-50 border-b">
                            <div className="overflow-x-auto">
                              <pre className="text-xs whitespace-pre-wrap bg-gray-800 text-white p-4 rounded">
                                {JSON.stringify(log, null, 2)}
                              </pre>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                )}
              </tbody>
            </table>
          </div>
          {/* Pagination could be added here */}
        </div>
        
        {/* Stats */}
        {filteredLogs.length > 0 && (
          <div className="mt-4 text-sm text-gray-500">
            Showing {filteredLogs.length} of {logs.length} logs
          </div>
        )}
      </div>
    </>
  );
} 