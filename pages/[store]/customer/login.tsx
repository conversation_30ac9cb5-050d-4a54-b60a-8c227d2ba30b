import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import Head from 'next/head';
import { SiteContext, ContextProviderComponent } from '../../../context/mainContext';
import MobileHeader from '../../../templates/shein/components/MobileHeader';
import DesktopHeader from '../../../templates/shein/components/DesktopHeader';

// Modified component to accept context as a prop
const LoginComponent: React.FC<{ context: any }> = ({ context }) => {
  const router = useRouter();
  const { store } = router.query;
  // Now use context directly from props instead of useContext
  const { forceCustomerCartSync } = context;
  const [formData, setFormData] = useState({
    customerId: '',
    password: '',
    storeId: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [storeInfo, setStoreInfo] = useState<any>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Check initially
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (store) {
      setFormData(prev => ({ ...prev, storeId: store as string }));
      
      // Fetch store information
      const fetchStoreInfo = async () => {
        try {
          const response = await fetch(`/api/stores/get?storeId=${store}`);
          if (response.ok) {
            const data = await response.json();
            setStoreInfo(data.store);
          }
        } catch (error) {
          console.error('Error fetching store info:', error);
        }
      };
      
      fetchStoreInfo();
    }
  }, [store]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          storeId: store
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Store customer data in store-specific localStorage
        localStorage.setItem(`${store}_customerData`, JSON.stringify(data.customer));
        // Also store in default location for backward compatibility
        localStorage.setItem('customerData', JSON.stringify(data.customer));
        localStorage.setItem('user', JSON.stringify(data.customer));
        
        // Set authentication cookie
        Cookies.set('customerToken', data.customer.id, { expires: 7 }); // Expires in 7 days
        
        // Force customer cart sync to ensure cart is tied to logged-in customer
        if (forceCustomerCartSync) {
          forceCustomerCartSync(data.customer.id, data.customer);
        } else {
          console.warn('forceCustomerCartSync is not available, cart may not be properly linked');
        }
        
        // Check for a payment return URL
        const paymentReturnUrl = localStorage.getItem('paymentReturnUrl');
        if (paymentReturnUrl) {
          localStorage.removeItem('paymentReturnUrl');
          router.push(paymentReturnUrl);
        } else {
          // Redirect to store-specific dashboard
          router.push(`/${store}/customer/dashboard`);
        }
      } else {
        setError(data.message || 'Thông tin đăng nhập không hợp lệ');
      }
    } catch (err) {
      setError('Đã xảy ra lỗi. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  // If store not loaded yet, show loading indicator
  if (!store) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  return (
    <>
      <Head>
        <title>{storeInfo?.name || 'Cửa hàng'} - Đăng Nhập Khách Hàng</title>
      </Head>
      
      {isMobile && storeInfo && (
        <>
        <MobileHeader
          store={store as string}
          currentstore={storeInfo || {
            logo: "/default-logo.png",
            name: "Cửa hàng"
          }}
          onSearch={(query: string) => {}}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          inventory={[]}
        />
        </>
      )}

      {!isMobile && storeInfo && (
        <>
        <DesktopHeader
          store={store as string}
          currentstore={storeInfo || {
            logo: "/default-logo.png",
            name: "Cửa hàng"
          }}
          onSearch={(query: string) => {}}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
        </>
      )}
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-10">
            <h1 className="text-2xl font-light tracking-wider text-gray-800 uppercase mb-3">
              Cổng Khách Hàng {storeInfo?.name && `- ${storeInfo.name}`}
            </h1>
            <div className="w-16 h-1 mx-auto bg-gray-200 mb-6"></div>
          </div>
          
          {error && (
            <div className="mb-6 p-2 border border-gray-300 text-center text-sm text-gray-600">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="customerId" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                Mã Khách Hàng
              </label>
              <input
                id="customerId"
                name="customerId"
                type="text"
                required
                value={formData.customerId}
                onChange={(e) => setFormData({ ...formData, customerId: e.target.value })}
                className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                placeholder="Nhập mã khách hàng của bạn"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="password" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                Mật Khẩu
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                placeholder="Nhập mật khẩu của bạn"
              />
            </div>
            
            <div className="pt-4">
              <button
                type="submit"
                disabled={loading}
                className={`w-full py-3 border border-gray-300 text-sm uppercase tracking-wider font-light hover:bg-gray-50 ${
                  loading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {loading ? 'Đang đăng nhập...' : 'Đăng Nhập'}
              </button>
            </div>
            
            <div className="pt-2">
              <a
                href={`/${store}/customer/register`}
                className="block w-full py-3 text-center border border-blue-600 text-blue-600 text-sm uppercase tracking-wider font-light hover:bg-blue-50 transition-colors"
              >
                Đăng Ký Tài Khoản Mới
              </a>
            </div>
            
            <div className="text-center mt-4">
              <a
                href={`/${store}`}
                className="text-xs text-gray-500 hover:text-gray-700 inline-flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Quay lại cửa hàng
              </a>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

// Wrap the component with the context provider
const StoreCustomerLogin: React.FC = () => (
  <ContextProviderComponent>
    <SiteContext.Consumer>
      {context => <LoginComponent context={context} />}
    </SiteContext.Consumer>
  </ContextProviderComponent>
);

export default StoreCustomerLogin;