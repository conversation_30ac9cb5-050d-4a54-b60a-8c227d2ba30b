import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';

const StoreCustomerIndex = () => {
  const router = useRouter();
  const { store } = router.query;

  useEffect(() => {
    if (!store) return;
    
    // Check if the user is authenticated
    const customerToken = Cookies.get('customerToken');
    const customerData = localStorage.getItem(`${store}_customerData`) || localStorage.getItem('customerData');
    
    if (customerToken && customerData) {
      // If authenticated, redirect to dashboard
      router.push(`/${store}/customer/dashboard`);
    } else {
      // If not authenticated, redirect to login
      router.push(`/${store}/customer/login`);
    }
  }, [router, store]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>Redirecting...</p>
    </div>
  );
};

export default StoreCustomerIndex; 