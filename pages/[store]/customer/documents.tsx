import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFile, faFileAlt, faFilePdf, faFileImage, faUpload, faEye, faDownload, faTrashAlt, faSpinner } from '@fortawesome/free-solid-svg-icons';
import Head from 'next/head';
import Link from 'next/link';
import Documents from '../../../components/customer/Documents';

const CustomerDocumentsPage: React.FC = () => {
  const router = useRouter();
  const { store } = router.query;
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!store) return;

    // Check if customer is authenticated
    const checkAuth = async () => {
      setIsLoading(true);
      try {
        const storageKey = `${store}_customerData`;
        const data = localStorage.getItem(storageKey) || localStorage.getItem('customerData');
        
        if (data) {
          setIsAuthenticated(true);
        } else {
          // Redirect to login if not authenticated
          router.push(`/${store}/customer/login?redirect=documents`);
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [store, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <FontAwesomeIcon icon={faSpinner} spin className="text-3xl text-blue-500 mb-4" />
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div>
      <Head>
        <title>Tài liệu của tôi | {store}</title>
      </Head>
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex flex-wrap items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">Tài liệu của tôi</h1>
            <p className="text-gray-600">Quản lý và xem tất cả tài liệu của bạn</p>
          </div>
          
          <div className="mt-4 md:mt-0">
            <Link 
              href={`/${store}/customer/profile`} 
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FontAwesomeIcon icon={faUpload} className="mr-2 -ml-1" />
              Tải lên tài liệu mới
            </Link>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <Documents />
        </div>
      </div>
    </div>
  );
};

export default CustomerDocumentsPage; 