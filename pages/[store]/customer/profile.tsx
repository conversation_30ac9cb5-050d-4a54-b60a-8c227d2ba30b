import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, faSignOutAlt, faCog, faShoppingBag, 
  faHeart, faAddressCard, faFile, faLock 
} from '@fortawesome/free-solid-svg-icons';
import ProfileSection from '../../../components/customer/ProfileSection';

const CustomerProfilePage: React.FC = () => {
  const router = useRouter();
  const { store } = router.query;
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [customerData, setCustomerData] = useState<any>(null);
  
  useEffect(() => {
    if (!store) return;

    // Check if customer is authenticated
    const checkAuth = async () => {
      setIsLoading(true);
      try {
        const storageKey = `${store}_customerData`;
        const data = localStorage.getItem(storageKey) || localStorage.getItem('customerData');
        
        if (data) {
          const parsedData = JSON.parse(data);
          setCustomerData(parsedData);
          setIsAuthenticated(true);
        } else {
          // Redirect to login if not authenticated
          router.push(`/${store}/customer/login?redirect=profile`);
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [store, router]);

  const handleLogout = () => {
    const storageKey = `${store}_customerData`;
    localStorage.removeItem(storageKey);
    localStorage.removeItem('customerData');
    router.push(`/${store}/customer/login`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        <p className="mt-4 text-gray-600">Đang tải...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <Head>
        <title>Hồ sơ của tôi | {store}</title>
      </Head>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-64 shrink-0">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-white">
                    <FontAwesomeIcon icon={faUser} className="text-xl" />
                  </div>
                  <div className="ml-3 truncate">
                    <p className="text-white font-medium truncate">
                      {customerData?.personalDetails?.name || customerData?.name || 'Khách hàng'}
                    </p>
                    <p className="text-indigo-100 text-sm truncate">
                      {customerData?.contactInfo?.email || customerData?.email || ''}
                    </p>
                  </div>
                </div>
              </div>

              <div className="py-2">
                <nav className="space-y-1">
                  <Link
                    href={`/${store}/customer/profile`}
                    className="flex items-center px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50"
                  >
                    <FontAwesomeIcon icon={faUser} className="mr-3 h-5 w-5 text-indigo-500" />
                    Hồ sơ của tôi
                  </Link>

                  <Link
                    href={`/${store}/customer/orders`}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  >
                    <FontAwesomeIcon icon={faShoppingBag} className="mr-3 h-5 w-5 text-gray-400" />
                    Đơn hàng của tôi
                  </Link>

                  <Link
                    href={`/${store}/customer/addresses`}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  >
                    <FontAwesomeIcon icon={faAddressCard} className="mr-3 h-5 w-5 text-gray-400" />
                    Sổ địa chỉ
                  </Link>

                  <Link
                    href={`/${store}/customer/documents`}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  >
                    <FontAwesomeIcon icon={faFile} className="mr-3 h-5 w-5 text-gray-400" />
                    Tài liệu của tôi
                  </Link>                  

                  <Link
                    href={`/${store}/customer/change-password`}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  >
                    <FontAwesomeIcon icon={faLock} className="mr-3 h-5 w-5 text-gray-400" />
                    Đổi mật khẩu
                  </Link>

                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  >
                    <FontAwesomeIcon icon={faSignOutAlt} className="mr-3 h-5 w-5 text-gray-400" />
                    Đăng xuất
                  </button>
                </nav>
              </div>
            </div>
          </div>

          {/* Main content */}
          <div className="flex-1">
            <ProfileSection />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerProfilePage; 