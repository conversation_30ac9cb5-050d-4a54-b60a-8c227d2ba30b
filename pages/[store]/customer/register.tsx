import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import Head from 'next/head';
import { SiteContext, ContextProviderComponent } from '../../../context/mainContext';

const RegisterComponent: React.FC<{ context: any }> = ({ context }) => {
  const router = useRouter();
  const { store } = router.query;
  const { forceCustomerCartSync } = context;
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    dateOfBirth: '',
    gender: '',
    storeId: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [storeInfo, setStoreInfo] = useState<any>(null);

  useEffect(() => {
    if (store) {
      setFormData(prev => ({ ...prev, storeId: store as string }));
      
      // Fetch store information
      const fetchStoreInfo = async () => {
        try {
          const response = await fetch(`/api/stores/get?storeId=${store}`);
          if (response.ok) {
            const data = await response.json();
            setStoreInfo(data.store);
          }
        } catch (error) {
          console.error('Error fetching store info:', error);
        }
      };
      
      fetchStoreInfo();
    }
  }, [store]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Mật khẩu không khớp');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          storeId: store
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Option to auto-login after successful registration
        if (data.customer && data.customer.id) {
          // Store customer data
          localStorage.setItem(`${store}_customerData`, JSON.stringify(data.customer));
          localStorage.setItem('customerData', JSON.stringify(data.customer));
          localStorage.setItem('user', JSON.stringify(data.customer));
          
          // Set authentication cookie
          Cookies.set('customerToken', data.customer.id, { expires: 7 });
          
          // Force customer cart sync
          if (forceCustomerCartSync) {
            forceCustomerCartSync(data.customer.id, data.customer);
          } else {
            console.warn('forceCustomerCartSync is not available, cart may not be properly linked');
          }
          
          // Check for a payment return URL
          const paymentReturnUrl = localStorage.getItem('paymentReturnUrl');
          if (paymentReturnUrl) {
            localStorage.removeItem('paymentReturnUrl');
            router.push(paymentReturnUrl);
          } else {
            // Redirect to dashboard instead of showing success message
            router.push(`/${store}/customer/dashboard`);
          }
          return;
        }
        
        // If auto-login isn't implemented, show success message
        setSuccess(true);
        setFormData({
          name: '',
          email: '',
          phone: '',
          password: '',
          confirmPassword: '',
          dateOfBirth: '',
          gender: '',
          storeId: store as string
        });
      } else {
        setError(data.message || 'Đăng ký không thành công. Vui lòng thử lại.');
      }
    } catch (err) {
      setError('Đã xảy ra lỗi. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  if (!store) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  return (
    <>
      <Head>
        <title>{storeInfo?.name || 'Cửa hàng'} - Đăng Ký Tài Khoản</title>
      </Head>
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-10">
            <h1 className="text-2xl font-light tracking-wider text-gray-800 uppercase mb-3">
              Đăng Ký Tài Khoản {storeInfo?.name && `- ${storeInfo.name}`}
            </h1>
            <div className="w-16 h-1 mx-auto bg-gray-200 mb-6"></div>
          </div>
          
          {error && (
            <div className="mb-6 p-2 border border-red-300 bg-red-50 text-center text-sm text-red-600">
              {error}
            </div>
          )}

          {success && (
            <div className="mb-6 text-center">
              <div className="p-3 border border-green-300 bg-green-50 text-green-600 text-sm mb-4">
                Đăng ký tài khoản thành công!
              </div>
              <div className="space-y-4">
                <p className="text-gray-600">Bạn có muốn đăng nhập ngay bây giờ?</p>
                <div className="flex justify-center gap-4">
                  <a
                    href={`/${store}/customer/login`}
                    className="px-6 py-2 bg-blue-600 text-white text-sm uppercase tracking-wider font-light hover:bg-blue-700 transition-colors rounded"
                  >
                    Đăng Nhập
                  </a>
                  <button
                    onClick={() => setSuccess(false)}
                    className="px-6 py-2 border border-gray-300 text-gray-600 text-sm uppercase tracking-wider font-light hover:bg-gray-50 transition-colors rounded"
                  >
                    Đăng Ký Tài Khoản Khác
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {!success && (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <label htmlFor="name" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                  Họ và Tên
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                  placeholder="Nhập họ và tên của bạn"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                  Email
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                  placeholder="Nhập địa chỉ email của bạn"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="phone" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                  Số Điện Thoại
                </label>
                <input
                  id="phone"
                  name="phone"
                  type="tel"
                  required
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                  placeholder="Nhập số điện thoại của bạn"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="dateOfBirth" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                  Ngày Sinh
                </label>
                <input
                  id="dateOfBirth"
                  name="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => setFormData({ ...formData, dateOfBirth: e.target.value })}
                  className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="gender" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                  Giới Tính
                </label>
                <select
                  id="gender"
                  name="gender"
                  value={formData.gender}
                  onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
                  className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                >
                  <option value="">Chọn giới tính</option>
                  <option value="male">Nam</option>
                  <option value="female">Nữ</option>
                  <option value="other">Khác</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <label htmlFor="password" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                  Mật Khẩu
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                  placeholder="Tạo mật khẩu mới"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="block text-xs uppercase tracking-wide text-gray-500 font-light">
                  Xác Nhận Mật Khẩu
                </label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                  className="w-full border-b border-gray-300 py-2 focus:border-gray-500 focus:outline-none bg-transparent"
                  placeholder="Nhập lại mật khẩu"
                />
              </div>
              
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={loading}
                  className={`w-full py-3 bg-blue-600 text-white text-sm uppercase tracking-wider font-light hover:bg-blue-700 transition-colors ${
                    loading ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {loading ? 'Đang xử lý...' : 'Đăng Ký'}
                </button>
              </div>
              
              <div className="text-center mt-4">
                <a
                  href={`/${store}/customer/login`}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  Đã có tài khoản? Đăng nhập
                </a>
              </div>
            </form>
          )}
        </div>
      </div>
    </>
  );
};

const StoreCustomerRegister: React.FC = () => (
  <ContextProviderComponent>
    <SiteContext.Consumer>
      {context => <RegisterComponent context={context} />}
    </SiteContext.Consumer>
  </ContextProviderComponent>
);

export default StoreCustomerRegister; 