import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import Head from 'next/head';
import Profile from '../../../components/customer/Profile';
import Orders from '../../../components/customer/Orders';
import Documents from '../../../components/customer/Documents';
import OrdersSection from '../../../components/customer/OrdersSection';
import DocumentsSection from '../../../components/customer/DocumentsSection';

const StoreCustomerDashboard: React.FC = () => {
  const router = useRouter();
  const { store } = router.query;
  const [customerName, setCustomerName] = useState('');
  const [storeInfo, setStoreInfo] = useState<any>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState<'profile' | 'orders' | 'documents'>('orders');

  useEffect(() => {
    if (!store) return;
    
    // Check authentication
    const customerData = localStorage.getItem(`${store}_customerData`) || localStorage.getItem('customerData');
    if (!customerData) {
      router.push(`/${store}/customer/login`);
    } else {
      try {
        const customer = JSON.parse(customerData);
        setCustomerName(customer.name || 'Khách hàng');
      } catch (e) {
        console.error('Error parsing customer data:', e);
      }
    }
    
    // Fetch store information if needed
    const fetchStoreInfo = async () => {
      try {
        const response = await fetch(`/api/stores/get?storeId=${store}`);
        if (response.ok) {
          const data = await response.json();
          setStoreInfo(data.store);
        }
      } catch (error) {
        console.error('Error fetching store info:', error);
      }
    };
    
    fetchStoreInfo();
  }, [router, store]);

  const handleLogout = async () => {
    // Clear local storage for this store
    if (store) {
      localStorage.removeItem(`${store}_customerData`);
    }
    localStorage.removeItem('customerData');
    localStorage.removeItem('user');
    
    // Clear the user ID to break the cart link
    localStorage.removeItem('SHOPME_USER_ID');
    
    // Clear the main cart storage
    localStorage.removeItem('SHOPME_');
    
    // Reset cart with empty values
    localStorage.setItem('SHOPME_', JSON.stringify({
      cart: [],
      numberOfItemsInCart: 0,
      total: 0,
      affiliatearray: [],
      lastUpdated: new Date().toISOString()
    }));
    
    // Generate a new anonymous user ID
    const newUserId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    localStorage.setItem('SHOPME_USER_ID', newUserId);
    
    // Clear the authentication cookie (client-side)
    Cookies.remove('customerToken');
    
    // Call logout API to clear cookie server-side
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Error during logout:', error);
    }
    
    // Force page reload to ensure all client-side state is reset
    window.location.href = `/${store}`;
    return; // Prevent router.push from executing after redirect
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Set active section (only one can be active at a time)
  const setSection = (section: 'profile' | 'orders' | 'documents') => {
    setActiveSection(section);
  };

  // If store not loaded yet, show loading indicator
  if (!store) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-pulse flex space-x-2">
          <div className="h-3 w-3 bg-blue-400 rounded-full"></div>
          <div className="h-3 w-3 bg-blue-400 rounded-full"></div>
          <div className="h-3 w-3 bg-blue-400 rounded-full"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>{storeInfo?.name || 'Cửa hàng'} - Thông tin tài khoản</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
      </Head>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="border-b border-gray-200 sticky top-0 bg-white z-10 shadow-sm">
          <div className="container mx-auto px-4 py-3 md:py-4 flex justify-between items-center">
            <div className="flex items-center">
              <button 
                onClick={toggleMobileMenu}
                className="md:hidden mr-3 text-gray-500 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <h1 className="text-lg font-light tracking-wider text-gray-800 uppercase truncate">
                <span className="md:inline hidden">Cổng Khách Hàng</span>
                <span>{storeInfo?.name && ` - ${storeInfo.name}`}</span>
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <a 
                href={`/${store}`}
                className="text-xs uppercase font-light tracking-wider text-gray-500 hover:text-gray-700 transition"
              >
                <span className="hidden md:inline">Quay lại cửa hàng</span>
                <span className="md:hidden">Cửa hàng</span>
              </a>
              <button 
                onClick={handleLogout}
                className="text-xs uppercase font-light tracking-wider text-gray-500 hover:text-red-500 transition"
              >
                Đăng Xuất
              </button>
            </div>
          </div>
        </header>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden fixed inset-0 z-20 bg-gray-800 bg-opacity-75">
            <div className="bg-white h-full w-64 p-5 shadow-lg">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Menu</h2>
                <button onClick={toggleMobileMenu} className="text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="flex flex-col space-y-4">
                <button 
                  onClick={() => {
                    setSection('orders');
                    setIsMobileMenuOpen(false);
                  }}
                  className="flex items-center space-x-2 py-2 px-4 hover:bg-gray-100 rounded-lg"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                    <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                  </svg>
                  <span>Đơn hàng</span>
                </button>
                <button 
                  onClick={() => {
                    setSection('documents');
                    setIsMobileMenuOpen(false);
                  }}
                  className="flex items-center space-x-2 py-2 px-4 hover:bg-gray-100 rounded-lg"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                  </svg>
                  <span>Tài liệu</span>
                </button>
                <button 
                  onClick={() => {
                    setSection('profile');
                    setIsMobileMenuOpen(false);
                  }}
                  className="flex items-center space-x-2 py-2 px-4 hover:bg-gray-100 rounded-lg"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                  <span>Lý lịch</span>
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="container mx-auto px-1 sm:px-4 md:px-6 py-4 md:py-8">
          <div className="w-full max-w-5xl mx-auto">
            {/* Welcome message and date */}
            <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
              <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
                Xin chào, {customerName}
              </h1>
              <p className="text-sm text-gray-500 mt-1 md:mt-0">
                {new Date().toLocaleDateString('vi-VN', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
            
            {/* Horizontal Navigation Tabs */}
            <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-0" aria-label="Tabs">
                  <button
                    onClick={() => setSection('orders')}
                    className={`flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                      activeSection === 'orders'
                        ? 'border-blue-500 text-blue-600 bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                      <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                    </svg>
                    <span>Đơn hàng</span>
                  </button>
                  <button
                    onClick={() => setSection('documents')}
                    className={`flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                      activeSection === 'documents'
                        ? 'border-blue-500 text-blue-600 bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                    </svg>
                    <span>Tài liệu</span>
                  </button>
                  <button
                    onClick={() => setSection('profile')}
                    className={`flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                      activeSection === 'profile'
                        ? 'border-blue-500 text-blue-600 bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                    <span>Lý lịch</span>
                  </button>
                </nav>
              </div>
            </div>

            {/* Dashboard sections */}
            <div className="space-y-6">
              {/* Main content area */}
              <div className="w-full space-y-6">
                {/* Content based on active section */}
                {activeSection === 'profile' && (
                  <div className="bg-white rounded-xl shadow-sm">
                    <div className="p-1 sm:p-4 md:p-6">
                      <Profile />
                    </div>
                  </div>
                )}
                
                {activeSection === 'orders' && (
                  <OrdersSection 
                    isActive={true}
                    toggleSection={() => setSection('orders')}
                  />
                )}
                
                {activeSection === 'documents' && (
                  <DocumentsSection
                    isActive={true}
                    toggleSection={() => setSection('documents')}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default StoreCustomerDashboard; 