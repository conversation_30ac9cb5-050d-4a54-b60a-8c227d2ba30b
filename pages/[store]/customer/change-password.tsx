import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock, faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import { SiteContext, ContextProviderComponent } from '../../../context/mainContext';

const ChangePasswordComponent: React.FC<{ context: any }> = ({ context }) => {
  const router = useRouter();
  const { store } = router.query;
  const [customerData, setCustomerData] = useState<any>(null);
  const [storeInfo, setStoreInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    if (!store) return;
    
    // Check authentication
    const customerData = localStorage.getItem(`${store}_customerData`) || localStorage.getItem('customerData');
    if (!customerData) {
      router.push(`/${store}/customer/login`);
    } else {
      try {
        const parsedData = JSON.parse(customerData);
        setCustomerData(parsedData);
      } catch (e) {
        console.error('Error parsing customer data:', e);
        router.push(`/${store}/customer/login`);
      }
    }
    
    // Fetch store information if needed
    const fetchStoreInfo = async () => {
      try {
        const response = await fetch(`/api/stores/get?storeId=${store}`);
        if (response.ok) {
          const data = await response.json();
          setStoreInfo(data.store);
        }
      } catch (error) {
        console.error('Error fetching store info:', error);
      }
    };
    
    fetchStoreInfo();
  }, [router, store]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const validateForm = () => {
    // Check if passwords are filled
    if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
      setMessage({ 
        text: 'Vui lòng điền đầy đủ thông tin', 
        type: 'error' 
      });
      return false;
    }

    // Check if new password matches confirm password
    if (formData.newPassword !== formData.confirmPassword) {
      setMessage({ 
        text: 'Mật khẩu mới và xác nhận mật khẩu không khớp', 
        type: 'error' 
      });
      return false;
    }

    // Check password length
    if (formData.newPassword.length < 6) {
      setMessage({ 
        text: 'Mật khẩu mới phải có ít nhất 6 ký tự', 
        type: 'error' 
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous messages
    setMessage({ text: '', type: '' });
    
    // Validate the form
    if (!validateForm()) {
      return;
    }
    
    if (!customerData || !customerData.id) {
      setMessage({ 
        text: 'Không tìm thấy thông tin khách hàng', 
        type: 'error' 
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/customers/${customerData.id}/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: formData.currentPassword,
          newPassword: formData.newPassword
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({
          text: 'Đổi mật khẩu thành công',
          type: 'success'
        });
        
        // Clear form
        setFormData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        setMessage({
          text: data.message || 'Đổi mật khẩu thất bại',
          type: 'error'
        });
      }
    } catch (error) {
      console.error('Error changing password:', error);
      setMessage({
        text: 'Có lỗi xảy ra, vui lòng thử lại sau',
        type: 'error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackClick = () => {
    router.push(`/${store}/customer/dashboard`);
  };

  if (!store || !customerData) {
    return <div className="min-h-screen flex items-center justify-center">Đang tải...</div>;
  }

  return (
    <>
      <Head>
        <title>{storeInfo?.name || 'Cửa hàng'} - Thay Đổi Mật Khẩu</title>
      </Head>
      <div className="min-h-screen bg-white">
        {/* Header */}
        <header className="border-b border-gray-200">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <h1 className="text-lg font-light tracking-wider text-gray-800 uppercase">
              Cổng Khách Hàng {storeInfo?.name && `- ${storeInfo.name}`}
            </h1>
            <button
              onClick={() => router.push(`/${store}`)}
              className="text-xs uppercase font-light tracking-wider text-gray-500 hover:text-gray-700"
            >
              Quay lại cửa hàng
            </button>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Thay Đổi Mật Khẩu</h2>
            
            {message.text && (
              <div className={`mb-4 p-3 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                {message.text}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Current Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700">Mật Khẩu Hiện Tại</label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FontAwesomeIcon icon={faLock} className="text-gray-400" />
                  </div>
                  <input
                    type={showCurrentPassword ? "text" : "password"}
                    name="currentPassword"
                    value={formData.currentPassword}
                    onChange={handleInputChange}
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    >
                      <FontAwesomeIcon icon={showCurrentPassword ? faEyeSlash : faEye} />
                    </button>
                  </div>
                </div>
              </div>

              {/* New Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700">Mật Khẩu Mới</label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FontAwesomeIcon icon={faLock} className="text-gray-400" />
                  </div>
                  <input
                    type={showNewPassword ? "text" : "password"}
                    name="newPassword"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    >
                      <FontAwesomeIcon icon={showNewPassword ? faEyeSlash : faEye} />
                    </button>
                  </div>
                </div>
              </div>

              {/* Confirm Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700">Xác Nhận Mật Khẩu Mới</label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FontAwesomeIcon icon={faLock} className="text-gray-400" />
                  </div>
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    >
                      <FontAwesomeIcon icon={showConfirmPassword ? faEyeSlash : faEye} />
                    </button>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex space-x-4">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="inline-flex justify-center py-2 px-4 border border-indigo-600 rounded-md text-sm font-medium text-indigo-600 hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {isLoading ? 'Đang Xử Lý...' : 'Đổi Mật Khẩu'}
                </button>
                <button
                  type="button"
                  onClick={handleBackClick}
                  className="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Quay Lại
                </button>
              </div>
            </form>
            
            <div className="mt-4 text-xs text-gray-500">
              <p>Lưu ý: Mật khẩu phải có ít nhất 6 ký tự và không nên chứa thông tin cá nhân dễ đoán.</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const ChangePassword: React.FC = () => (
  <ContextProviderComponent>
    <SiteContext.Consumer>
      {context => <ChangePasswordComponent context={context} />}
    </SiteContext.Consumer>
  </ContextProviderComponent>
);

export default ChangePassword; 