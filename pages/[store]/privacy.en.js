import Head from "next/head"
import CartLink from "../../components/CartLink"
import { fetchStore } from "../../utils/storeProvider"
import { useRouter } from "next/router"
import ReactMarkdown from "react-markdown"
import StoreIntroSection from "../../components/StoreIntroSection"

const PrivacyPolicy = ({ currentstore = "" }) => {
  const router = useRouter()
  const { store } = router.query

  let latitude = 21.028251553342592
  let longitude = 105.85235060315165

  if (currentstore.locations) {
    const latLongArr = currentstore.locations[0].latlong.split(",")
    latitude = parseFloat(latLongArr[0])
    longitude = parseFloat(latLongArr[1])
  }

  const privacyPolicyContent = `
# Privacy Policy

**Available in**  
VIETNAMESE  
English

## 1. Introduction

In SIM  ("SIM", "we", or "us"), we want to provide the best possible service for you. To do this we need to gather some data, including personally identifying data, that are relevant with the services we provide. Even so, privacy and the security of your personal data will always be the outmost importance for us. To fulfill our objective, through this Privacy Policy ("Policy"), we will explain in detail how and why we collect, store, share, and use your personal data that you entrust to us.

## 2. About this Policy

This Policy explains in detail information related to collection, storage, sharing, and usage of personal data that we obtain from you. This Policy applies for all services provided by SIM.

Should there are services that we offer with a separate privacy policy, that separate privacy policy will prevail, with provisions that are not covered will be covered by this Policy.

If you have any question, complaints, or suggestion, you can see the section 'Contact Us' below. Or, if you do not agree with the content of this Policy, please remember that it is your option to use the services provided by SIM.

## 3. Personal data we collect from you

In order to provide our services, we may need you to provide us with personal information that are needed to perform the requested service. You have the right to not provide us the required information; however, in most cases, that means we may not be able to provide services which requires that information to you.

Depending on which services you use, we collect these information:

### 3.1 Information that you provided

We may collect personal information that you provided to us which are needed to provide services you requested. For example, in order to use our services, you provided us with your name, phone number, address, identity card number, email address, and your social media ID.

### 3.2 Information that we collect from your usage

We also collect information about your service usage. This information includes, but not limited to: application settings (region, language, timezone), application ID, application version, SDK version, operating system version, and application status (e.g. installing, updating, deleting). We also collect log information and your behavior from our server or third-party, including IP address, log information from usage of services (e.g. access time, registration time, activity time), and which pages and functionality you access.

### 3.3 Information from third-party partners

We also collect information from third-party partners about you, including from advertisers and partners who work with us so that we can provide services for you. We will use these personal information if you have given us or our partners consent to share your data or when we have legitimate reason for using that personal information in providing service to you.

### 3.4 Information which are not considered personal information

We may also collect other information that are not directly tied to your personal information or may not be defined as a personal information according to the local law. This type of information may include device ID, network monitoring data, page access activity, page access duration, etc.

## 4. How we use personal information we collected

Our objective for collecting personal information is to provide our services to you. In addition, those information is also used to ensure we follow the applicable laws and regulations. This includes:

- Providing services to you
- Verifying your identity
- Preventing fraud
- Protecting and securing the system
- Providing relevant promotion
- Offering personalized services and contents
- Diagnosing and fixing system errors
- Providing push notification service
- For marketing and advertising purposes
- Improving user experience

## 5. How we share personal information to third-party partners

We do not sell your personal information to third-party providers. We sometimes need to share your personal information with third-party partners to provide or improve our product or services.

### Our Group Companies

We can share your personal information from time to time to affiliates of SIM.

### Third-party Providers and Business Partners

We may use third party companies or individuals in providing our services. If needed, we will share your personal information to our service providers or business partners. We ensure that your information is shared when it is necessary and that the reason is legitimate, specific, and important in order to provide services to you.

To perform analysis and measurement of your performance and services, we may provide non-personal information with third parties. This information is used to help either us or third parties in performing the aforementioned measurement and analysis tasks.

We are not responsible for the privacy policies of other organization.

## 6. Security

We are aware of the importance of your personal information. Because of that, we implement appropriate procedures to protect your personal information. To secure your connection to us, we use Transport Layer Security (TLS)/Secure Sockets Layer (SSL) technology. Please ensure that your connection is encrypted through TLS/SSL so that your data is transmitted and received securely. All of your personal information is stored in servers with access control to prevent unauthorized access.

However, it is important to remember that there are no perfect secure system. If you feel that your account security is compromised, you can inform us by contacting us according to the 'Contact Us' section.

## 7. Retention Period

We may retain your personal information for the required amount of time to fulfill the objectives outlined in this Policy and according to applicable laws and regulations.

## 8. Data Deletion Request

If you request us to delete your data, we will also required to close your account. Your data will then either be deleted or anonymized so that it will no longer identifies you. Note that this means you will lose access to your account and all data associated with your account (such as purchase history) unless you back it up in advance.

However, in some cases, we are required to keep some of your personal data for legally justifiable reasons. For example:
- There is an unresolved issue with your account, such as outstanding amount or unresolved dispute
- For our legal, tax, or accounting obligations
- For fraud prevention

To request data deletion, you can either contact us through the information outlined in the 'Contact Us' section, or by sending an <NAME_EMAIL>.

## 9. Information from Underage Children

We do not offer direct services to underage children. In addition, we knowingly do not collect information from underage children. If we discover that we have collected information from underage children, we will delete that information and stop providing service to the child.

## 10. Changes to this Policy

We may change this Policy at any time. Every changes can be seen in this page. Changes to Policy applies from the moment the changes is published in this page. We recommend that you check this page periodically to see whether there are changes to this Policy.

## 11. Prevailing language

This Policy may be translated and executed in multiple languages other than Vietnamese ("Vietnamese") which taken together is considered as a single Policy. If there are any conflict or difference in meaning in the translated Policy, the vietnamese language version will prevail.

## 12. Contact Us

If you have any comment, complaint, suggestion, or question about this Policy, you can contact us at:

**Address**

京佳企業行  Jing jia ltd company  
07-5668139  
高雄市苓雅區自強三路3號25樓之9  
9th Floor, No. 3, Ziqiang 3rd Road, Lingya District, Kaohsiung City, Taiwan.
`;

  return (
    <>
      <CartLink />
      <div className="w-full">
        <Head>
          <title>Privacy Policy - SIM Online Store</title>
          <meta name="description" content="SIM Online Store Privacy Policy" />
          <meta property="og:title" content="Privacy Policy - SIM Online Store" key="title" />
        </Head>
      </div>

      <div className="container mx-auto py-10 px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <ReactMarkdown>{privacyPolicyContent}</ReactMarkdown>
        </div>
      </div>
    </>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const paths = []
  allstores.forEach((store) => {
    const storeId = store.storeId
    paths.push({
      params: {
        store: storeId,
      },
    })
  })
  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const storeId = params.store
  const store = allstores.find((store) => store.storeId === storeId)

  if (!store) {
    throw new Error(`Store with ID ${storeId} not found.`)
  }

  return {
    props: {
      currentstore: store,
    },
  }
}

export default PrivacyPolicy 