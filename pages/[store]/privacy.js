import Head from "next/head"
import CartLink from "../../components/CartLink"
import { fetchStore } from "../../utils/storeProvider"
import { useRouter } from "next/router"
import ReactMarkdown from "react-markdown"
import StoreIntroSection from "../../components/StoreIntroSection"

const PrivacyPolicy = ({ currentstore = "" }) => {
  const router = useRouter()
  const { store } = router.query

  let latitude = 21.028251553342592
  let longitude = 105.85235060315165

  if (currentstore.locations) {
    const latLongArr = currentstore.locations[0].latlong.split(",")
    latitude = parseFloat(latLongArr[0])
    longitude = parseFloat(latLongArr[1])
  }

  const privacyPolicyContent = `
# Chính sách quyền riêng tư

**Có sẵn bằng**  
VIETNAMESE  
English

## Giới thiệu

Tại SIM ("SIM", "chúng tôi"), chúng tôi mong muốn cung cấp dịch vụ tốt nhất cho bạn. <PERSON><PERSON> làm được điề<PERSON> này, chúng tôi cần thu thập một số dữ liệu, bao gồm cả thông tin cá nhân, có liên quan đến dịch vụ mà chúng tôi cung cấp. Tuy nhiên, quyền riêng tư và bảo mật dữ liệu cá nhân của bạn luôn là ưu tiên hàng đầu của chúng tôi. Chính sách quyền riêng tư này ("Chính sách") sẽ giải thích chi tiết cách thức và lý do chúng tôi thu thập, lưu trữ, chia sẻ và sử dụng dữ liệu cá nhân của bạn.

## Về Chính sách này

Chính sách này giải thích chi tiết về việc thu thập, lưu trữ, chia sẻ và sử dụng dữ liệu cá nhân của bạn. Chính sách này áp dụng cho tất cả các dịch vụ do SIM cung cấp.

Nếu có dịch vụ nào được cung cấp với một chính sách bảo mật riêng, thì chính sách bảo mật riêng đó sẽ được ưu tiên áp dụng, còn những điều không được đề cập trong chính sách riêng đó sẽ tuân theo Chính sách này.

Nếu bạn có bất kỳ câu hỏi, khiếu nại hoặc đề xuất nào, vui lòng xem phần "Liên hệ với chúng tôi" bên dưới. Nếu bạn không đồng ý với nội dung của Chính sách này, bạn có quyền quyết định có tiếp tục sử dụng dịch vụ của SIM hay không.

## Dữ liệu cá nhân mà chúng tôi thu thập từ bạn

Chúng tôi có thể cần bạn cung cấp một số thông tin cá nhân để có thể thực hiện các dịch vụ theo yêu cầu của bạn. Bạn có quyền từ chối cung cấp thông tin, nhưng trong nhiều trường hợp, điều đó đồng nghĩa với việc chúng tôi không thể cung cấp dịch vụ cho bạn.

Các thông tin chúng tôi thu thập bao gồm:

### Thông tin bạn cung cấp

Họ tên, số điện thoại, địa chỉ, số chứng minh thư/căn cước công dân, email, ID mạng xã hội.

### Thông tin chúng tôi thu thập từ việc sử dụng dịch vụ của bạn

Cài đặt ứng dụng (khu vực, ngôn ngữ, múi giờ), ID ứng dụng, phiên bản ứng dụng, phiên bản hệ điều hành, nhật ký hoạt động (IP, thời gian truy cập, thời gian đăng ký).

### Thông tin từ đối tác bên thứ ba

Chúng tôi có thể nhận thông tin từ đối tác quảng cáo hoặc đối tác hợp tác với chúng tôi để cung cấp dịch vụ.

### Thông tin không thuộc dữ liệu cá nhân

ID thiết bị, nhật ký truy cập, thời gian sử dụng dịch vụ.

## Cách chúng tôi sử dụng thông tin cá nhân đã thu thập

Mục đích thu thập dữ liệu cá nhân bao gồm:

- Cung cấp dịch vụ cho bạn
- Xác minh danh tính
- Ngăn chặn gian lận
- Bảo vệ hệ thống
- Cung cấp các chương trình khuyến mãi phù hợp
- Cá nhân hóa nội dung
- Chẩn đoán và sửa lỗi hệ thống
- Gửi thông báo đẩy
- Cải thiện trải nghiệm người dùng

## Cách chúng tôi chia sẻ dữ liệu cá nhân với đối tác thứ ba

Chúng tôi không bán dữ liệu cá nhân của bạn. Tuy nhiên, trong một số trường hợp, chúng tôi có thể chia sẻ dữ liệu với:

### Công ty trong cùng tập đoàn với SIM

### Nhà cung cấp dịch vụ và đối tác kinh doanh

### Đối tác phân tích dữ liệu và đo lường hiệu suất

## Bảo mật

Chúng tôi áp dụng các biện pháp bảo vệ dữ liệu cá nhân, bao gồm sử dụng công nghệ mã hóa TLS/SSL để bảo vệ kết nối. Tuy nhiên, không có hệ thống nào đảm bảo an toàn tuyệt đối. Nếu bạn nghi ngờ tài khoản của mình bị xâm phạm, vui lòng liên hệ với chúng tôi.

## Thời gian lưu trữ dữ liệu

Chúng tôi sẽ lưu trữ dữ liệu cá nhân của bạn trong khoảng thời gian cần thiết để thực hiện các mục tiêu được nêu trong Chính sách này và theo quy định của pháp luật.

## Yêu cầu xóa dữ liệu

Bạn có thể yêu cầu xóa dữ liệu cá nhân của mình, nhưng điều này cũng đồng nghĩa với việc tài khoản của bạn sẽ bị đóng và mọi dữ liệu liên quan (lịch sử mua hàng,...) sẽ bị mất. Trong một số trường hợp, chúng tôi có thể phải giữ lại một số thông tin vì lý do pháp lý (ví dụ: tranh chấp tài khoản, nghĩa vụ thuế, chống gian lận).

## Thông tin từ trẻ em dưới tuổi quy định

Chúng tôi không cung cấp dịch vụ trực tiếp cho trẻ em dưới tuổi quy định và không cố ý thu thập dữ liệu từ trẻ em. Nếu phát hiện ra dữ liệu trẻ em bị thu thập, chúng tôi sẽ xóa thông tin đó.

## Thay đổi Chính sách

Chúng tôi có thể thay đổi Chính sách này bất cứ lúc nào. Mọi thay đổi sẽ được công bố trên trang này và có hiệu lực ngay khi được cập nhật. Chúng tôi khuyến khích bạn kiểm tra Chính sách định kỳ.

## Ngôn ngữ áp dụng

Nếu có bất kỳ xung đột hoặc khác biệt nào giữa các phiên bản dịch của Chính sách này, phiên bản tiếng Việt sẽ được ưu tiên áp dụng.

## Liên hệ với chúng tôi

**Địa chỉ**

京佳企業行 Jing Jia Ltd  
Điện thoại: 07-5668139  
Địa chỉ: Số 9, tầng 25, số 3, đường Ziqiang 3rd, quận Lingya, thành phố Cao Hùng, Đài Loan  
Email: <EMAIL>
`;

  return (
    <>
      <CartLink />
      <div className="w-full">
        <Head>
          <title>Privacy Policy - SIM Online Store</title>
          <meta name="description" content="SIM Online Store Privacy Policy" />
          <meta property="og:title" content="Privacy Policy - SIM Online Store" key="title" />
        </Head>
      </div>

      <div className="container mx-auto py-10 px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <ReactMarkdown>{privacyPolicyContent}</ReactMarkdown>
        </div>
      </div>
    </>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  const paths = []
  allstores.forEach((store) => {
    const storeId = store.storeId
    paths.push({
      params: {
        store: storeId,
      },
    })
  })
  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const storeId = params.store
  const store = allstores.find((store) => store.storeId === storeId)

  if (!store) {
    throw new Error(`Store with ID ${storeId} not found.`)
  }

  return {
    props: {
      currentstore: store,
    },
  }
}

export default PrivacyPolicy 