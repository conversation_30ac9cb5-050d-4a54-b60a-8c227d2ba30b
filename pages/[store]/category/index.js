import { useRouter } from "next/router";
// catgory/[category]/subcategory/[subcategory].tsx
const SubcategoryDynamicPage = () => {
  const router = useRouter();
  const { store, name } = router.query;
  return (
    <div>
      <pre>        
        Store : {store} <br></br>
        This the the default category route, served from [store]/category/index.js <br></br>
      </pre>
    </div>
  );
};
export default SubcategoryDynamicPage;