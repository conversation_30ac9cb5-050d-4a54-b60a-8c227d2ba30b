import { useRouter } from "next/router";
import Head from 'next/head'
import ListItem from '../../../components/ListItem'
import ListItemProduct from '../../../components/ListItemProduct'
import { titleIfy, slugify } from '../../../utils/helpers'
import fetchCategories from '../../../utils/categoryProvider'
import fetchCategoriesOneShop from '../../../utils/categoryProviderOneShop'
import fetchInventoryForCategoryAndShop from '../../../utils/inventoryForCategoryAndShop'
import CartLink from '../../../components/CartLink'
import { multistores, fetchStore } from "../../../utils/storeProvider";
import ProductList from '../../../components/ProductList'

const Category = (props) => {
  const { inventory, categorySlug, categoryName } = props
  const router = useRouter();
  const { store } = router.query;
  return (
    <>
      <CartLink />
      <Head>
        <title>ShopMe - {categorySlug}</title>
        <meta name="description" content={`ShopMe - ${categorySlug}`} />
        <meta property="og:title" content={`ShopMe - ${categorySlug}`} key="title" />
      </Head>
      <div className="flex flex-col items-center">
        <div className="max-w-fw flex flex-col w-full">
          <div className="pt-4 sm:pt-10 pb-8">
            {/* <h1 className="text-5xl font-light">{titleIfy(title)}</h1> */}
            <h1 className="text-5xl font-light">{categoryName}</h1>
          </div>

          <div>
            <ProductList 
              products={inventory}
              store={store}
              title={categoryName}
            />
          </div>
        </div>
      </div>
    </>
  )
}

export async function getStaticPaths () {  
  const allstores = await fetchStore();
  const categoriesarrayA = await fetchCategories();
  const categoriesarray = categoriesarrayA.all; //all categories from the entire inventory of all stores mixed together  

  const paths = []; 
  for (const store of allstores) {
    const storeId = store.storeId;
    const categoriesarrayThisShop = await fetchCategoriesOneShop(storeId);
    const categories2 = categoriesarrayThisShop.map(category => category.slug);
    //console.log(JSON.stringify(categoriesarrayThisShop)); //It prints like this for each shop: [{"slug":"cay-canh","name":"Cây cảnh"},{"slug":"sen-đa","name":"Sen đá"}]

    for (const category of categories2) {        
      paths.push({
        params: {
          store: storeId,
          name: slugify(category)
        }
      });
    }
  }
  return {
    paths,
    fallback: false
  }
}

export async function getStaticProps({ params }) {
  const { store, name } = params;

  /* const categoriesArray = await fetchCategories();
  const categoriesForThisShop = categoriesArray[store]; */
  const categoriesForThisShop2 = await fetchCategoriesOneShop(store);

  const currentCategory = categoriesForThisShop2.find(categoryitem => categoryitem.slug === name);
  const categoryName = currentCategory && currentCategory.name ? currentCategory.name : "";

  const inventory = await fetchInventoryForCategoryAndShop(name, store) ?? [];

  return {
    props: {
      inventory,
      categorySlug: name, 
      categoryName: categoryName
    }
  };
}


export default Category
