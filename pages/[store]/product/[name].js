import { FaBell } from "react-icons/fa"
import { use<PERSON><PERSON>er } from "next/router"
import { useState, useEffect, useContext, Component } from "react"
import Head from "next/head"
import Button from "../../../components/Button"
import QuantityPicker from "../../../components/QuantityPicker"
import { slugify, appendOrUpdateAffiliateParam } from "../../../utils/helpers"
import CartLink from "../../../components/CartLink"
import {
  SiteContext,
  ContextProviderComponent,
} from "../../../context/mainContext"
import ImageCarousel from "../../../components/ImageCarousel"
import parse from "html-react-parser"
import { fetchStore } from "../../../utils/storeProvider"
import ListItem from "../../../components/ListItem"
import DENOMINATION from "../../../utils/currencyProvider"
import fetchInventoryForShop from "../../../utils/inventoryForShop"
import emailjs from "emailjs-com"
import axios from "axios"
import ProfileLink from "../../../components/ProfileLink"
import { DisplayMediumNoBox, TravelSimGuide } from "../../../components"
import TaiwanSimGuide from "../../../components/TaiwanSimGuide"
import TaiwanServiceGuide from "../../../components/TaiwanServiceGuide"
import { Share } from '@capacitor/share';
import ProductConfigurations from '../../../components/ProductConfigurations';
import SubscriptionPanel from "../../../components/SubscriptionPanel"
import TawkButton from "../../../components/chat/TawkButton"
import TawkEmbed from "../../../components/chat/TawkEmbed"
import QABox from "../../../components/faq/QABox"
import qaData from './qaData.json'
import MobileHeader from '../../../templates/shein/components/MobileHeader'
import DesktopHeader from '../../../templates/shein/components/DesktopHeader'

import {
  FacebookShareButton,
  FacebookMessengerShareButton,
  TwitterShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
  TelegramShareButton,
  ViberShareButton,
  /* PinterestShareButton, */
  LineShareButton,
  EmailShareButton,
} from "react-share"

import {
  FacebookIcon,
  FacebookMessengerIcon,
  TwitterIcon,
  LinkedinIcon,
  WhatsappIcon,
  TelegramIcon,
  ViberIcon,
  /* PinterestIcon, */
  LineIcon,
  EmailIcon,
} from "react-share"

import SocialMediaDisplay from "../../../components/SocialMediaDisplay"
import PaymentQRDisplay from "../../../components/PaymentQRDisplay"
import SocialShare from "../../../components/SocialShare"
import RecentlyViewed from "../../../components/RecentlyViewed"

// Error boundary component
class ErrorBoundary extends Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error }
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo })
    console.error("Product page error:", error, errorInfo)

    // Log to server
    try {
      fetch('/api/log-error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          error: error.toString(),
          errorInfo: JSON.stringify(errorInfo),
          location: window.location.href
        })
      });
    } catch (e) {
      console.error("Failed to log error:", e);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-8 max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h1>
          <p className="mb-4">We're sorry, there was an error loading this product.</p>
          <div className="bg-gray-100 p-4 rounded mb-4 overflow-auto">
            <p className="font-medium">Error:</p>
            <pre className="text-red-500 text-sm">{this.state.error && this.state.error.toString()}</pre>

            {this.state.errorInfo && (
              <>
                <p className="font-medium mt-4">Component Stack:</p>
                <pre className="text-sm overflow-auto">
                  {this.state.errorInfo.componentStack}
                </pre>
              </>
            )}
          </div>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            Reload Page
          </button>
        </div>
      )
    }

    return this.props.children
  }
}

const ItemView = (props) => {
  const { product, currentstore, inventory } = props
  if (!product) {
    // Handle the case when product is undefined
    return <div>Product not found.</div>
  }

  const {
    price,
    priceUpper,
    discountrate,
    discountenddate,
    image,
    name,
    description,
    descriptionFull,
    currentInventory,
    sku,
    currency,
    wine_color,
    wine_grapetype,
    wine_tasting,
    wine_country,
    wine_region,
    wine_producer,
    wine_season,
    wine_alcohol_concentration,
    wine_dungtich,
  } = product

  const [numberOfItems, setNumberOfItems] = useState(1)
  const [subscribed, setSubscribed] = useState(false)
  const [showThankYou, setShowThankYou] = useState(false)
  const [error, setError] = useState(null)
  const { recordUserDetails, getPreferredAffiliate, addAffiliate } = useContext(SiteContext)
  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext)
  const [email, setEmail] = useState(contextEmail || "")
  const [phone, setPhone] = useState(contextPhone || "")
  const [isFocused, setIsFocused] = useState(false)
  const [copied, setCopied] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [searchResults, setSearchResults] = useState([])
  const [showCategoriesPanel, setShowCategoriesPanel] = useState(false)
  const [configInputs, setConfigInputs] = useState({})
  const [isFormValid, setIsFormValid] = useState(true)
  const [affiliateIdState, setAffiliateIdState] = useState(getPreferredAffiliate() || "")

  const router = useRouter()
  const { store, affiliate } = router.query

  const [isMobile, setIsMobile] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Set client flag to true after hydration
    setIsClient(true);
    
    const checkMobile = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(window.innerWidth <= 768);
      }
    };

    // Check initially
    checkMobile();

    // Add resize listener
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', checkMobile);
    }

    // Cleanup
    return () => {
       if (typeof window !== 'undefined') {
         window.removeEventListener('resize', checkMobile);
       }
     };
   }, []);

  // Handle search
  const handleSearch = (query) => {
    setSearchQuery(query)
    if (query.trim() === '') {
      setShowSearchResults(false)
      setSearchResults([])
      return
    }

    const results = inventory.filter(product =>
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.sku.toLowerCase().includes(query.toLowerCase())
    )
    setSearchResults(results)
    setShowSearchResults(true)
  }



  // Get addToCart from context safely
  const addToCart = props?.context?.addToCart || (() => {
    console.warn('addToCart is not available during SSR')
  })
  
  // Ensure addToCart is available before using it
  const safeAddToCart = (product) => {
    if (typeof addToCart === 'function') {
      addToCart(product)
    } else {
      console.warn('addToCart function is not available')
    }
  }

  const handleFocus = () => setIsFocused(true)
  const handleBlur = () => setIsFocused(false)

  const handleEmailChange = (event) => {
    setEmail(event.target.value)
  }
  const handlePhoneChange = (event) => {
    setPhone(event.target.value)
  }

  const handleSubscription = async (shopid, sku) => {
    if (!(contextEmail || contextPhone)) {
      alert("Xin điền email hoặc điện thoại để nhận thông báo.")
      return // Exit the function without subscribing
    }

    try {
      setEmail(contextEmail)
      setPhone(contextPhone)
      if (!subscribed) {
        setSubscribed(true)
        emailjs
          .send(
            "shopme.aws.emailjs", // Your service ID
            //'shopme_promotionsub_1', // Your template ID
            "voaa_email_template_1",
            {
              //message: `${email}\n${phone}\n\n\nURL: ${currentUrl}`
              message: `${contextEmail}\n${contextPhone}\n${sku}\n\nURL: ${currentUrl}`, //sku
            },
            "QFig8Xb2RUx8ujepR" // Your user ID
          )
          .then((response) => {
            console.log(
              "Email sent successfully!",
              response.status,
              response.text
            )
          })
          .catch((error) => {
            console.error("Failed to send email:", error)
          })
        /* const payload = {
          active: "1",
          userid: email,
          email: email,
          mobile: phone,
          shop: "vanggoc",
          product: "winestore.1",
          notes: ""
        }; */

        const payload = {
          active: "1",
          userid: contextEmail,
          email: contextEmail,
          mobile: contextPhone,
          shop: shopid,
          product: sku,
          notes: "",
        }

        try {
          //await axios.post('/api/registerUser', {}); //working now
          await axios.post("/api/registerUser", payload)
          console.log("User Registration Successful")

          //recordUserDetails(email, phone); //no need to do here anymore since I already have the useEffect to do this anytime email or phone changes.
        } catch (error) {
          console.error("User Registration Failed:", error)
        }
      } else {
        setSubscribed(false)
      }
    } catch (error) {
      console.error("An error occurred while subscribing user:", error)
      // Handle error, show error message, etc.
    }
  }

  const [showProblemForm, setShowProblemForm] = useState(false)
  const [problemDescription, setProblemDescription] = useState("")

  const handleProblemSubmit = (e) => {
    e.preventDefault()
    if (problemDescription.trim() !== "") {
      // Send email
      emailjs
        .send(
          "shopme.aws.emailjs", // Your service ID
          "voaa_email_template_1", // Your template ID
          {
            message: `${problemDescription}\n\nURL: ${currentUrl}`,
          },
          "QFig8Xb2RUx8ujepR" // Your user ID
        )
        .then((response) => {
          console.log(
            "Email sent successfully!",
            response.status,
            response.text
          )
          // Clear the textarea after successful submission
          setProblemDescription("")
          setShowThankYou(true)
        })
        .catch((error) => {
          console.error("Failed to send email:", error)
        })
    } else {
      // Handle case where problemDescription is empty
      console.log("Problem description is empty. Cannot send email.")
    }
  }

  const constructProductDetails = () => {
    const {
      name,
      price,
      priceUpper,
      discountrate,
      discountenddate,
      description,
      currency,
      wine_color,
      wine_grapetype,
      wine_tasting,
      wine_country,
      wine_region,
      wine_producer,
      wine_season,
      wine_alcohol_concentration,
      wine_dungtich,
      sku,
      configurations,
    } = props.product

    const { shopLinkQRCode, qr_line } = props.currentstore

    let details = `${name}\n\n`
    details += `${DENOMINATION} ${price} ${priceUpper ? ` - ${priceUpper}` : ""
      }\n\n`
    if (description) details += `${description}\n\n`

    const tableItems = [
      { label: "Màu rượu:", value: wine_color },
      { label: "Giống nho:", value: wine_grapetype },
      { label: "Thử nếm:", value: wine_tasting },
      { label: "Quốc gia:", value: wine_country },
      { label: "Vùng:", value: wine_region },
      { label: "Nhà sản xuất:", value: wine_producer },
      { label: "Niên vụ:", value: wine_season },
      { label: "Nồng độ cồn:", value: wine_alcohol_concentration },
      { label: "Dung tích:", value: wine_dungtich },
      { label: "Mã sản phẩm:", value: sku },
      { label: "URL:", value: currentUrl },
    ]

    for (const item of tableItems) {
      if (item.value) {
        details += `${item.label} ${item.value}\n`
      }
    }

    return details
  }

  const copyToClipboard = async () => {
    try {
      const textToCopy = constructProductDetails()
      await navigator.clipboard.writeText(textToCopy)
      setCopied(true)
    } catch (error) {
      console.error("Failed to copy:", error)
    }
  }

  const validateConfigurations = () => {
    if (!product.configurations) return true

    const requiredConfigs = Object.entries(product.configurations)
      .filter(([_, config]) => config.purpose === "input" && config.required)

    return requiredConfigs.every(([key, _]) =>
      configInputs[key] && configInputs[key].trim() !== ""
    )
  }

  const addItemToCart = () => {
    if (!validateConfigurations()) {
      setIsFormValid(false)
      return
    }

    const updatedProduct = {
      ...product,
      quantity: numberOfItems,
      store: store,
      configInputs: configInputs // Add the configuration inputs to the cart item
    }
    safeAddToCart(updatedProduct)
  }

  const buyNow = () => {
    if (!validateConfigurations()) {
      setIsFormValid(false)
      return
    }

    const updatedProduct = {
      ...product,
      quantity: numberOfItems,
      store: store,
      configInputs: configInputs // Add the configuration inputs to the cart item
    }
    safeAddToCart(updatedProduct)

    // Navigate to cart page immediately
    router.push(`/${store}/cart`)
  }

  const handleConfigInput = (key, value) => {
    setConfigInputs(prev => ({
      ...prev,
      [key]: value
    }))
    setIsFormValid(true) // Reset validation state when user types
  }

  const increment = () => {
    setNumberOfItems((prevCount) => prevCount + 1)
  }

  const decrement = () => {
    if (numberOfItems === 1) return
    setNumberOfItems((prevCount) => prevCount - 1)
  }

  function shouldHideQuantity() {
    return !currentInventory
  }

  const isValidEmail = (email) => {
    // Regular expression to validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const imageSrc =
    Array.isArray(image) && image[0] && image.length > 0
      ? image[0]
      : "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp"

  /* const currentUrl = typeof window !== "undefined" ? window.location.href : "" */
  const [currentUrl, setCurrentUrl] = useState("")
  
  useEffect(() => {
    if (typeof window !== "undefined") {
      const currentUrltmp = window.location.href
      setCurrentUrl(appendOrUpdateAffiliateParam(currentUrltmp, affiliate))
    }
  }, [affiliate])

  useEffect(() => {
    if ((email && isValidEmail(email)) || (phone && phone.length > 8)) {
      recordUserDetails(email, phone)
    }
  }, [email, phone])

  useEffect(() => {
    const handleLoad = () => {
      // Code to execute after the script has loaded
      console.log("Zalo Share SDK loaded")
    }

    const zaloScript = document.createElement("script")
    zaloScript.src = "https://sp.zalo.me/plugins/sdk.js"
    zaloScript.async = true
    zaloScript.onload = handleLoad
    document.body.appendChild(zaloScript)

    return () => {
      // Clean up the script when the component unmounts
      // document.body.removeChild(zaloScript)
    }
  }, [])

  useEffect(() => {
    if (affiliate && affiliate.length > 0) {
      const newAffiliate = {
        affiliateid: affiliate,
        timestamp: Date.now(), // Current timestamp
        storeid: store,
        sku: sku,
      }
      addAffiliate(newAffiliate)

      /* addAffiliate(affiliate) */
      setAffiliateIdState(affiliate || "")
    }
  }, [affiliate])

  // Store current product in localStorage when component mounts
  useEffect(() => {
    if (product) {
      localStorage.setItem(`product_${product.sku}`, JSON.stringify(product))
    }
  }, [product])

  // Check if product is in travelsim category
  const isTravelSim = product.categories && 
    Array.isArray(product.categories) && 
    product.categories.some(cat => 
      typeof cat === 'string' && 
      cat.trim().toUpperCase() === 'TRAVELSIM'
    );

  const isTaiwanSim = product.categories && 
    Array.isArray(product.categories) && 
    product.categories.some(cat => 
      typeof cat === 'string' && 
      cat.trim().toUpperCase() === 'SIM'
    );
    
  const isTaiwanService = product.categories && 
    Array.isArray(product.categories) && 
    product.categories.some(cat => 
      typeof cat === 'string' && 
      cat.trim().toUpperCase() === 'TAIWANSERVICES'
    );

  return (
    <>
      {/* <CartLink /> */}
      <Head>
        <title>ShopMe - {name}</title>
        <meta name="description" content={description ? description : "MAG Group"} />
        <meta property="og:title" content={`ShopMe - ${name}`} key="title" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      {isClient && currentstore.layouttemplate === 'shein' && isMobile && (
        <MobileHeader
          store={store}
          currentstore={currentstore}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          inventory={inventory}
        />
      )}

      {isClient && currentstore.layouttemplate === 'shein' && !isMobile && (
        <DesktopHeader
          store={store}
          currentstore={currentstore}
          onSearch={handleSearch}
          onToggleCategories={() => setShowCategoriesPanel(!showCategoriesPanel)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}

      {/* Search Results Panel */}
      {showSearchResults && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setShowSearchResults(false)}>
          <div
            className="absolute bottom-0 left-0 right-0 bg-white rounded-t-2xl p-4 max-h-[80vh] overflow-y-auto"
            onClick={e => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Kết quả tìm kiếm</h2>
              <button
                onClick={() => setShowSearchResults(false)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-3">
              {searchResults.length > 0 ? (
                searchResults.map(product => (
                  <a
                    key={product.id}
                    href={`/${store}/product/${slugify(product.sku)}`}
                    className="flex items-start gap-3 p-2 border rounded-lg hover:shadow-sm transition-shadow"
                    onClick={() => setShowSearchResults(false)}
                  >
                    <div className="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden bg-gray-50">
                      {product.image?.[0] && (
                        <img
                          src={product.image[0]}
                          alt={product.name}
                          className="h-full w-full object-cover"
                          loading="lazy"
                        />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm text-gray-900 line-clamp-2">{product.name}</h3>
                      {product.price && (
                        <p className="text-orange-600 text-sm mt-1">
                          {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                        </p>
                      )}
                    </div>
                  </a>
                ))
              ) : (
                <p className="text-center text-gray-500 py-4">Không tìm thấy sản phẩm nào</p>
              )}
            </div>
          </div>
        </div>
      )}



      <div className="w-full px-2 sm:px-4 py-4 sm:py-12 mx-auto">
        <div className="w-full flex flex-col md:flex-row my-0 mx-auto">
          <div className="w-full flex flex-col">
            <div className="w-full max-w-md mx-auto border-2 border-gray-300 rounded-lg overflow-hidden">
              <div className="w-full flex justify-center items-center">
                <ImageCarousel imageUrls={image ? image : ["https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.1.png"]} />
              </div>
            </div>

            <div className="w-full flex flex-wrap justify-center items-center pt-4 px-2">
              {/* Copy button */}
              <div className="mr-2 pb-4">
                <Button
                  full
                  title={copied ? "COPIED!" : "COPY"}
                  onClick={copyToClipboard}
                  small={true}
                />
              </div>

              {/* Use the SocialShare component */}
              <SocialShare url={currentUrl} />
            </div>
          </div>
        </div>
        <div className="w-full pt-2 px-2 sm:px-4 md:px-10 pb-8 md:w-1/2 mx-auto">
          <h1 className="sm:mt-0 mt-2 text-3xl font-light leading-normal">
            {name}
          </h1>
          {sku && (
            <span className="text-gray-600 leading-7">
              MÃ SẢN PHẨM: {parse(sku)}
            </span>
          )}

          <h2 className="text-2xl tracking-wide sm:py-8 py-6">
            {price.toLocaleString()} {currency || DENOMINATION}{" "}
            {typeof discountrate === "number" && discountrate > 0 && (
              <span className="text-gray-600 leading-7">
                Giảm giá: {discountrate} % đến ngày: {discountenddate}
              </span>
            )}
          </h2>

          {description && (
            <span className="text-gray-600 leading-7">
              {parse(description)}
            </span>
          )}
          {/* Medium and larger screens */}
          <table className="hidden md:table text-gray-600 leading-7">
            <tbody>
              {product.validity && (
                <tr>
                  <td
                    className="border-b border-gray-200"
                    style={{ width: "120px" }}
                  >
                    Hiệu lực:
                  </td>
                  <td className="border-b border-gray-200">
                    {product.validity} ngày
                  </td>
                </tr>
              )}
              {product.territory && (
                <tr>
                  <td
                    className="border-b border-gray-200"
                    style={{ width: "120px" }}
                  >
                    Lãnh thổ:
                  </td>
                  <td className="border-b border-gray-200">
                    {product.territory}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          <div className="my-6">
            {!shouldHideQuantity() && (
              <QuantityPicker
                increment={increment}
                decrement={decrement}
                numberOfItems={numberOfItems}
              />
            )}
          </div>

          <div className="flex flex-col md:flex-row gap-2">
            <div className="w-full md:w-auto">
              <Button
                full
                title="ĐẶT HÀNG"
                onClick={addItemToCart}
              />
            </div>
            <div className="w-full md:w-auto">
              <Button
                full
                title="MUA NGAY"
                onClick={buyNow}
              />
            </div>
          </div>
        </div>
      </div>
      {/* Recently Viewed Products */}
      <div className="w-full px-2 sm:px-4">
        <RecentlyViewed
          currentProductSku={product.sku}
          store={store}
          currentProduct={product}
          products={inventory}
        />
      </div>
      
      {/* Display TravelSimGuide for products in travelsim category */}
      {isTravelSim && (
        <div className="w-full px-2 sm:px-4">
          <TravelSimGuide />
        </div>
      )}
      {isTaiwanSim && (
        <div className="w-full px-2 sm:px-4">
          <TaiwanSimGuide />
        </div>
      )}
      {isTaiwanService && (
        <div className="w-full px-2 sm:px-4">
          <TaiwanServiceGuide />
        </div>
      )}
      
      {/* QR Codes Section - More compact arrangement */}
      <div className="w-full px-2 sm:px-4 mb-4">
        <div className="max-w-5xl mx-auto">
          <div className="flex flex-col gap-2">
            <SocialMediaDisplay store={currentstore} />
            <PaymentQRDisplay paymentQRs={currentstore.paymentQRs} />
          </div>
        </div>
      </div>

      {/* Product Combo Section */}
      {product.comboproduct && product.comboproduct.length > 0 && (
        <div className="w-full px-2 sm:px-4">
          <span className="block text-gray-700 text-xs">
            BỘ SẢN PHẨM NÀY BAO GỒM CÁC SẢN PHẨM SAU. TỔNG GIÁ TIỀN KHI MUA
            RIÊNG LẺ: {DENOMINATION}{" "}
            {parseFloat(product.totalSubProductsPrice).toLocaleString()}:{" "}
          </span>
          <div>
            <div>
              <div className="flex flex-1 flex-wrap flex-row">
                {product.subProducts.map((item, index) => {
                  if (!item || !item.name) {
                    return null // Skip mapping if any attribute is null or undefined
                  }
                  // Check if item.price is null or undefined
                  if (
                    typeof item.price === "undefined" ||
                    item.price === null || item.price === 0
                  ) {
                    // Assign a new value of 100,000,000
                    item.price = 999999999
                  }
                  const imageSrc =
                    Array.isArray(item.image) && item.image.length > 0 && item.image[0]
                      ? item.image[0]
                      : "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp"

                  return (
                    <ListItem
                      key={index}
                      link={`/${store}/product/${slugify(item.sku)}`}
                      title={item.name + " ( x" + item.quantity + " )" + ((item.notincludedincombo && (Number(item.notincludedincombo) === 1)) ? " (khuyến nghị nhưng không bao gồm trong combo này)" : "")}
                      sku={item.sku}
                      price={item.price}
                      priceUpper={item.priceUpper}
                      imageSrc={imageSrc}
                      openInNewTab={true}
                    />
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="w-full px-2 sm:px-4">
        {descriptionFull && (
          <span className="text-gray-600 leading-7">
            {parse(descriptionFull)}
          </span>
        )}
      </div>
    </>
  )
}

export async function getStaticPaths() {
  const allShops = await fetchStore();
  const filteredShops = allShops || []; // Add fallback if allShops is undefined

  const paths = [];

  filteredShops.forEach((store) => {
    // Skip if store is undefined or doesn't have proper properties
    if (!store || !store.storeId) return;

    const storeId = store.storeId;
    // Make sure inventory exists and is an array
    const storeInventory = store.inventory || [];
    let inventoryForShop = storeInventory.filter((item) => item !== undefined);

    inventoryForShop.forEach((item) => {
      if (item && item.sku) { // Check if item and item.sku exist
        paths.push({
          params: {
            store: storeId,
            name: slugify(item.sku),
          },
        });
      }
    });
  });

  return {
    paths,
    fallback: false,
  }
}

export async function getStaticProps({ params }) {
  try {
    if (!params || !params.name || !params.store) {
      throw new Error("Invalid parameters provided.")
    }

    const name = params.name
    const storeId = params.store

    const inventory = await fetchInventoryForShop(storeId)

    if (!inventory || inventory.length === 0) {
      throw new Error(`Inventory not found for store with ID: ${storeId}`)
    }

    // Find the product in the inventory
    const product = inventory.find(
      (item) => slugify(item.sku) === slugify(name)
    )

    if (!product) {
      throw new Error(`Product with name ${name} not found in inventory.`)
    }

    const allstores = await fetchStore()
    const store = allstores.find((store) => store.storeId === storeId)

    if (!store) {
      throw new Error(`Store with ID ${storeId} not found.`)
    }

    let currentstore = {}

    if (store) {
      // Find LINE QR in social array for backward compatibility
      let lineQR = "/mag.logo.1.png"; // Default
      if (Array.isArray(store.social)) {
        const lineItem = store.social.find(item => item.network === "line");
        if (lineItem && lineItem.qr_image) {
          lineQR = lineItem.qr_image;
        }
      }

      currentstore = {
        shopLinkQRCode: store.shopLinkQRCode
          ? store.shopLinkQRCode
          : "/mag.logo.1.png",
        social: store.social || [],
        qr_line: lineQR, // For backward compatibility
        paymentQRs: store.paymentQRs || [],
        layouttemplate: store.layouttemplate || 'default', // Add theme information
        name: store.name || '',
        logo: store.logo || ''
      }
    } else {
      currentstore = {
        shopLinkQRCode:
          "https://upload.wikimedia.org/wikipedia/commons/d/d0/QR_code_for_mobile_English_Wikipedia.svg",
        social: [],
        qr_line: "/mag.logo.1.png",
        paymentQRs: [],
        layouttemplate: 'default',
        name: '',
        logo: ''
      }
    }

    return {
      props: {
        product,
        currentstore: currentstore,
        inventory,
      },
    }
  } catch (error) {
    // Handle errors gracefully
    console.error("Error in getStaticProps:", error.message)
    return {
      props: {
        error: error.message, // Pass the error message to the component
      },
    }
  }
}

// catgory/[category]/subcategory/[subcategory].tsx
const ItemViewWithContext = (props) => {
  return (
    <ContextProviderComponent>
      <ErrorBoundary>
        <SiteContext.Consumer>
          {(context) => <ItemView {...props} context={context} />}
        </SiteContext.Consumer>
      </ErrorBoundary>
    </ContextProviderComponent>
  )
}

export default ItemViewWithContext
