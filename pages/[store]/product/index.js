import { useRouter } from "next/router";
// catgory/[category]/subcategory/[subcategory].tsx
const SubcategoryDynamicPage = () => {
  const router = useRouter();
  const { store, name } = router.query;
  return (
    <div>
      <pre>        
        Store : {store} <br></br>
        This the the default product route, served from [store]/product/index.js <br></br>
      </pre>
    </div>
  );
};
export default SubcategoryDynamicPage;