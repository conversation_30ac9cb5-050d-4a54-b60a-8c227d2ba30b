import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import Image from '../../../components/Image'
import { FaArrowLeft, FaSpinner, FaExclamationCircle, FaCheckCircle, FaShoppingBag, FaCreditCard, FaShippingFast, FaRegClock, FaWallet, FaMoneyBill } from 'react-icons/fa'
import { fetchStore } from '../../../utils/storeProvider'
import { SiteContext, ContextProviderComponent } from '../../../context/mainContext'
import { getCustomerId, isCustomerAuthenticated } from '../../../utils/customerAuth'
import BarcodeDisplay from '../../../components/payment/BarcodeDisplay'

const OrderDetail = ({ context, storeObject }) => {
  const router = useRouter()
  const { store, id } = router.query
  const [order, setOrder] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  
  // Function to calculate subtotal from items
  const calculateSubtotal = () => {
    if (!order || !order.items || !Array.isArray(order.items)) return 'N/A';
    
    const subtotal = order.items.reduce((sum, item) => {
      const price = typeof item.price === 'number' ? item.price : 0;
      const quantity = typeof item.quantity === 'number' ? item.quantity : 0;
      return sum + (price * quantity);
    }, 0);
    
    return subtotal.toLocaleString();
  };
  
  // Function to calculate total
  const calculateTotal = () => {
    if (!order || !order.items || !Array.isArray(order.items)) return 'N/A';
    
    let total = order.items.reduce((sum, item) => {
      const price = typeof item.price === 'number' ? item.price : 0;
      const quantity = typeof item.quantity === 'number' ? item.quantity : 0;
      return sum + (price * quantity);
    }, 0);
    
    // Add shipping cost if available
    if (order.shipping && typeof order.shipping.cost === 'number') {
      total += order.shipping.cost;
    }
    
    // Add fee if available
    if (typeof order.fee === 'number') {
      total += order.fee;
    }
    
    // Subtract discount if available
    if (typeof order.discount === 'number') {
      total -= order.discount;
    }
    
    return total.toLocaleString();
  };
  
  // Function to get product image based on SKU
  const getProductImageBySku = (sku) => {
    // Create a hardcoded map for the specific products we know about
    const productImageMap = {
      'taiwan.yearsim.IF.wcBV': '/images/if-sim-card.jpg',
      'taiwan.yearsim.CHUNGHOA.xzD3': '/images/chunghoa-sim-card.jpg',
      'taiwan.yearsim.OK.dpRP': '/images/ok-sim-card.jpg'
    };
    
    // If we have a direct mapping, use it
    if (productImageMap[sku]) {
      return productImageMap[sku];
    }
    
    // First check if it's a Taiwan Yearsim product
    if (sku && sku.startsWith('taiwan.yearsim')) {
      // Extract the product code from the SKU
      const parts = sku.split('.');
      if (parts.length >= 3) {
        const productCode = parts[2].toLowerCase();
        
        // For Taiwan Yearsim products, use a generic SIM card image based on the type
        if (productCode.includes('if')) return '/images/if-sim-card.jpg';
        if (productCode.includes('chunghoa')) return '/images/chunghoa-sim-card.jpg';
        if (productCode.includes('ok')) return '/images/ok-sim-card.jpg';
        
        // Generic Taiwan SIM card image
        return '/images/taiwan-sim-card.jpg';
      }
    }
    
    // Default fallback
    return '/images/default-product-image.jpg';
  };
  
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!id) return
      
      setLoading(true)
      setError(null)
      
      try {
        // Try to fetch order by ID
        const response = await fetch(`/api/orders/${id}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch order details')
        }
        
        const data = await response.json()
        
        if (data.success && data.order) {
          setOrder(data.order)
          console.log('[ORDER_DETAIL] Order loaded:', data.order)
        } else {
          throw new Error(data.error || 'Order not found')
        }
      } catch (err) {
        console.error('[ORDER_DETAIL] Error:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }
    
    if (id) {
      fetchOrderDetails()
    }
  }, [id])
  
  // Function to format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    const date = new Date(dateString)
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
  }
  
  // Function to get order status label
  const getOrderStatusLabel = (status) => {
    if (!status) return 'Trạng thái không xác định'
    
    switch(status.toLowerCase()) {
      case 'pending':
        return 'Đang xử lý'
      case 'initiated':
        return 'Đã khởi tạo thanh toán'
      case 'paid':
      case 'completed':
        return 'Đã thanh toán'
      case 'shipped':
        return 'Đã giao hàng'
      case 'delivered':
        return 'Đã nhận hàng'
      case 'cancelled':
        return 'Đã hủy'
      case 'refunded':
        return 'Đã hoàn tiền'
      case 'not_paid':
        return 'Chưa thanh toán'
      default:
        return status
    }
  }
  
  // Function to get status color
  const getStatusColor = (status) => {
    if (!status) return 'bg-gray-100 text-gray-800'
    
    switch(status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'initiated':
        return 'bg-blue-100 text-blue-800'
      case 'paid':
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'shipped':
        return 'bg-purple-100 text-purple-800'
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'refunded':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
  
  // Function to get payment method icon
  const getPaymentMethodIcon = (method) => {
    if (!method) return <FaCreditCard className="text-gray-600" />;
    
    const methodLower = method.toLowerCase();
    
    // Check for convenience store methods
    if (methodLower.includes('convenience_store') || methodLower.includes('seven') || methodLower.includes('ibon') || methodLower.includes('711')) {
      return <FaShoppingBag className="text-green-600" />;
    }
    
    if (methodLower.includes('family') || methodLower.includes('mart')) {
      return <FaShoppingBag className="text-blue-600" />;
    }
    
    // Bank transfers
    if (methodLower.includes('bank') || methodLower.includes('transfer')) {
      return <FaWallet className="text-blue-700" />;
    }
    
    // Credit card
    if (methodLower.includes('card') || methodLower.includes('credit')) {
      return <FaCreditCard className="text-indigo-600" />;
    }
    
    // Cash on delivery
    if (methodLower.includes('cod') || methodLower.includes('cash')) {
      return <FaMoneyBill className="text-green-600" />;
    }
    
    // Mobile payment
    if (methodLower.includes('momo') || methodLower.includes('zalo') || methodLower.includes('linepay') || methodLower.includes('jko')) {
      return <FaWallet className="text-pink-600" />;
    }
    
    // Default
    return <FaCreditCard className="text-gray-600" />;
  };
  
  // Function to get payment method display name
  const getPaymentMethodName = (method, subMethod) => {
    if (!method) return 'Chưa xác định';
    
    const methodLower = method.toLowerCase();
    const subMethodLower = subMethod ? subMethod.toLowerCase() : '';
    
    // Specific combinations
    if (methodLower === 'convenience_store' && subMethodLower === 'seven_eleven_ibon') {
      return '7-11 (IBON)';
    }
    
    if (methodLower === 'convenience_store' && subMethodLower === 'family_mart') {
      return 'FamilyMart';
    }
    
    // Handle specific methods
    if (methodLower.includes('bank') || methodLower.includes('transfer')) {
      return 'Chuyển khoản ngân hàng';
    }
    
    if (methodLower.includes('card') || methodLower.includes('credit')) {
      return 'Thẻ tín dụng/ghi nợ';
    }
    
    if (methodLower.includes('cod')) {
      return 'Thanh toán khi nhận hàng';
    }
    
    if (methodLower.includes('momo')) {
      return 'Ví MoMo';
    }
    
    if (methodLower.includes('zalo')) {
      return 'ZaloPay';
    }
    
    if (methodLower.includes('linepay')) {
      return 'LINE Pay';
    }
    
    if (methodLower.includes('jko')) {
      return 'JKO Pay';
    }
    
    // Default case: just return the method with proper capitalization
    return method.charAt(0).toUpperCase() + method.slice(1);
  };
  
  // Function to get order status color
  const getOrderStatusColor = (status) => {
    if (!status) return 'gray';
    
    switch(status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'initiated':
        return 'text-blue-600 bg-blue-50';
      case 'paid':
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'shipped':
        return 'text-purple-600 bg-purple-50';
      case 'delivered':
        return 'text-green-700 bg-green-100';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      case 'refunded':
        return 'text-orange-600 bg-orange-50';
      case 'not_paid':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Get payment status style
  const getPaymentStatusStyle = (status) => {
    if (!status) return {}
    
    switch(status.toLowerCase()) {
      case 'paid':
      case 'completed':
        return { icon: <FaCheckCircle className="text-green-500" />, text: getOrderStatusLabel(status), color: 'text-green-600' }
      case 'pending':
        return { icon: <FaRegClock className="text-yellow-500" />, text: getOrderStatusLabel(status), color: 'text-yellow-600' }
      case 'initiated':
        return { icon: <FaRegClock className="text-blue-500" />, text: getOrderStatusLabel(status), color: 'text-blue-600' }
      case 'failed':
        return { icon: <FaExclamationCircle className="text-red-500" />, text: 'Thất bại', color: 'text-red-600' }
      case 'not_paid':
        return { icon: <FaExclamationCircle className="text-red-500" />, text: getOrderStatusLabel(status), color: 'text-red-600' }
      default:
        return { icon: <FaRegClock className="text-gray-500" />, text: getOrderStatusLabel(status), color: 'text-gray-600' }
    }
  }
  
  return (
    <>
      <Head>
        <title>{order ? `Đơn hàng #${order.id}` : 'Chi tiết đơn hàng'} - {storeObject?.name || store}</title>
        <meta name="description" content="Chi tiết đơn hàng" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>
      
      <div className="container mx-auto px-3 sm:px-6 py-4 sm:py-8 max-w-4xl">
        {/* Back button */}
        <div className="mb-4 sm:mb-6">
          <button 
            onClick={() => router.push(`/${store}/cart`)}
            className="flex items-center text-gray-600 hover:text-gray-900 text-sm sm:text-base"
          >
            <FaArrowLeft className="mr-2" />
            <span>Quay lại giỏ hàng</span>
          </button>
        </div>
        
        <h1 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">Chi tiết đơn hàng</h1>
        
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <FaSpinner className="animate-spin text-2xl sm:text-3xl text-gray-500 mr-3" />
            <span className="text-sm sm:text-base">Đang tải thông tin đơn hàng...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <FaExclamationCircle className="text-red-500 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm text-red-700">Không thể tải thông tin đơn hàng</p>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <div className="mt-4">
                  <button
                    onClick={() => router.push(`/${store}/cart`)}
                    className="bg-red-100 hover:bg-red-200 text-red-800 px-4 py-2 rounded text-sm"
                  >
                    Quay lại giỏ hàng
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : order ? (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            {/* Order header */}
            <div className="border-b border-gray-200 bg-gray-50 px-4 sm:px-6 py-4">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <div className="mb-2 sm:mb-0">
                  <h2 className="text-base sm:text-lg font-semibold">Đơn hàng #{order.id}</h2>
                  <p className="text-xs sm:text-sm text-gray-600">Ngày đặt: {formatDate(order.createdAt || order.date)}</p>
                </div>
                <div className="mt-2 sm:mt-0">
                  <span className={`inline-block px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${getOrderStatusColor(order.status || order.paymentStatus)}`}>
                    {getOrderStatusLabel(order.status || order.paymentStatus)}
                  </span>
                </div>
              </div>
            </div>
            
            {/* Order summary */}
            <div className="px-4 sm:px-6 py-4 grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Customer information */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Thông tin khách hàng</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><span className="font-medium">Tên:</span> {order.customerName}</p>
                  {order.customerEmail && <p><span className="font-medium">Email:</span> <span className="break-all">{order.customerEmail}</span></p>}
                  {order.customerPhone && <p><span className="font-medium">Điện thoại:</span> {order.customerPhone}</p>}
                </div>
              </div>
              
              {/* Shipping information */}
              {(order.shipping || order.shippingAddress || order.recipientInfo) && (
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Thông tin giao hàng</h3>
                  <div className="text-sm text-gray-600 space-y-1">
                    {order.shipping && order.shipping.address && (
                      <p><span className="font-medium">Địa chỉ:</span> <span className="break-words">{order.shipping.address}</span></p>
                    )}
                    {!order.shipping && order.shippingAddress && (
                      <p><span className="font-medium">Địa chỉ:</span> <span className="break-words">{order.shippingAddress}</span></p>
                    )}
                    {!order.shipping && !order.shippingAddress && order.recipientInfo && order.recipientInfo.address && (
                      <p><span className="font-medium">Địa chỉ:</span> <span className="break-words">{order.recipientInfo.address}</span></p>
                    )}
                    
                    {order.shipping && order.shipping.city && (
                      <p><span className="font-medium">Thành phố:</span> {order.shipping.city}</p>
                    )}
                    {!order.shipping && order.recipientInfo && order.recipientInfo.city && (
                      <p><span className="font-medium">Thành phố:</span> {order.recipientInfo.city}</p>
                    )}
                    
                    {order.shipping && order.shipping.postalCode && (
                      <p><span className="font-medium">Mã bưu điện:</span> {order.shipping.postalCode}</p>
                    )}
                    
                    {order.shipping && order.shipping.country && (
                      <p><span className="font-medium">Quốc gia:</span> {order.shipping.country}</p>
                    )}
                    
                    {order.deliveryInfo && order.deliveryInfo.deliveryMethod && (
                      <p><span className="font-medium">Phương thức:</span> {order.deliveryInfo.deliveryMethod === 'store' ? 'Nhận tại cửa hàng' : 'Giao hàng tận nơi'}</p>
                    )}
                    
                    {order.deliveryInfo && order.deliveryInfo.deliveryDate && (
                      <p><span className="font-medium">Ngày giao:</span> {new Date(order.deliveryInfo.deliveryDate).toLocaleDateString()}</p>
                    )}
                    
                    {order.deliveryInfo && order.deliveryInfo.location && (
                      <p><span className="font-medium">Địa điểm nhận:</span> <span className="break-words">{order.deliveryInfo.location}</span></p>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Payment information */}
            <div className="px-4 sm:px-6 py-4 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Thông tin thanh toán</h3>
              
              {/* Payment details - Mobile friendly layout */}
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 mb-4 space-y-3">
                {order.paymentMethod && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Phương thức:</span>
                    <div className="flex items-center text-sm">
                      <span className="mr-2">{getPaymentMethodIcon(order.paymentMethod)}</span>
                      <span className="break-words">{getPaymentMethodName(order.paymentMethod, order.paymentSubMethod)}</span>
                    </div>
                  </div>
                )}
                
                {order.paymentSubMethod && !order.paymentMethod?.toLowerCase().includes(order.paymentSubMethod.toLowerCase()) && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Kênh thanh toán:</span>
                    <span className="text-sm break-words">{order.paymentSubMethod.replace(/_/g, ' ')}</span>
                  </div>
                )}
                
                {order.paymentStatus && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Trạng thái:</span>
                    <div className="flex items-center text-sm">
                      <span className="mr-2">{getPaymentStatusStyle(order.paymentStatus).icon}</span>
                      <span className={getPaymentStatusStyle(order.paymentStatus).color}>
                        {getPaymentStatusStyle(order.paymentStatus).text}
                      </span>
                    </div>
                  </div>
                )}
                
                {order.paymentId && (
                  <div className="flex items-start justify-between">
                    <span className="text-sm font-medium text-gray-700">Mã giao dịch:</span>
                    <span className="font-mono text-sm text-blue-600 break-all text-right ml-2">{order.paymentId}</span>
                  </div>
                )}
                
                {/* 7-11 IBON Payment Information */}
                {order.paymentInfo && order.paymentMethod?.toLowerCase().includes('convenience') && order.paymentInfo.ibonPaymentCode && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Mã thanh toán IBON:</span>
                      <span className="font-mono text-base sm:text-lg font-bold text-green-600 bg-green-50 px-2 py-1 rounded">
                        {order.paymentInfo.ibonPaymentCode}
                      </span>
                    </div>
                    {order.paymentInfo.orderExpireDate && (
                      <div className="flex items-start justify-between">
                        <span className="text-sm font-medium text-gray-700">Hạn thanh toán:</span>
                        <span className="text-sm text-red-600 font-medium text-right ml-2">
                          {new Date(order.paymentInfo.orderExpireDate).toLocaleDateString('vi-VN')} 
                          {' '}
                          {new Date(order.paymentInfo.orderExpireDate).toLocaleTimeString('vi-VN', {hour: '2-digit', minute:'2-digit'})}
                        </span>
                      </div>
                    )}
                    {/* Display iBON barcodes if available */}
                    {order.paymentInfo.barcodes && (
                      <div className="pt-2 border-t border-gray-200">
                        <span className="text-sm font-medium text-gray-700 block mb-2">Mã vạch iBON:</span>
                        <div className="w-full">
                          <BarcodeDisplay.Inline barcodes={order.paymentInfo.barcodes} />
                        </div>
                      </div>
                    )}
                  </>
                )}
                
                {/* FamilyMart Payment Information */}
                {order.paymentInfo && order.paymentMethod?.toLowerCase().includes('convenience') && order.paymentInfo.paymentCode && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Mã thanh toán FamilyMart:</span>
                      <span className="font-mono text-base sm:text-lg font-bold text-blue-600 bg-blue-50 px-2 py-1 rounded">
                        {order.paymentInfo.paymentCode}
                      </span>
                    </div>
                    {order.paymentInfo.paymentExpiry && (
                      <div className="flex items-start justify-between">
                        <span className="text-sm font-medium text-gray-700">Hạn thanh toán:</span>
                        <span className="text-sm text-red-600 font-medium text-right ml-2">
                          {new Date(order.paymentInfo.paymentExpiry).toLocaleDateString('vi-VN')}
                          {' '}
                          {new Date(order.paymentInfo.paymentExpiry).toLocaleTimeString('vi-VN', {hour: '2-digit', minute:'2-digit'})}
                        </span>
                      </div>
                    )}
                  </>
                )}
                
                {/* Bank Transfer Information */}
                {order.paymentMethod?.toLowerCase().includes('bank') && order.bankInfo && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Ngân hàng:</span>
                      <span className="text-sm font-medium break-words text-right ml-2">{order.bankInfo.bankName}</span>
                    </div>
                    <div className="flex items-start justify-between">
                      <span className="text-sm font-medium text-gray-700">Số tài khoản:</span>
                      <span className="font-mono text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded break-all text-right ml-2">
                        {order.bankInfo.accountNumber}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Tên tài khoản:</span>
                      <span className="text-sm font-medium break-words text-right ml-2">{order.bankInfo.accountName}</span>
                    </div>
                    <div className="flex items-start justify-between">
                      <span className="text-sm font-medium text-gray-700">Nội dung chuyển khoản:</span>
                      <span className="font-mono text-sm text-green-600 bg-green-50 px-2 py-1 rounded break-all text-right ml-2">
                        {order.id}
                      </span>
                    </div>
                  </>
                )}
              </div>
              
              {/* Visual barcode display for iBON payments */}
              {order.paymentMethod?.toLowerCase().includes('convenience') && order.paymentInfo?.barcodes && (
                <div className="mt-6">
                  <BarcodeDisplay.IBON 
                    paymentInfo={order.paymentInfo}
                    orderInfo={{
                      orderId: order.id,
                      amount: order.totalAmount || order.total,
                      currency: order.currency,
                      expiry: order.paymentInfo.orderExpireDate ? new Date(order.paymentInfo.orderExpireDate).toLocaleDateString() : null
                    }}
                    displayMode="visual"
                  />
                </div>
              )}
            </div>
            
            {/* Order items - Mobile optimized */}
            <div className="px-4 sm:px-6 py-4 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-4">Danh sách sản phẩm</h3>
              
              {/* Mobile-first product list */}
              <div className="space-y-4 sm:hidden">
                {order.items && order.items.map((item, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-3 border">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 h-12 w-12 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                        {item.sku && item.sku.includes('taiwan.yearsim') ? (
                          <svg
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className={`h-6 w-6 ${
                              item.sku.includes('IF') ? 'text-blue-500' :
                              item.sku.includes('CHUNGHOA') ? 'text-red-500' :
                              item.sku.includes('OK') ? 'text-green-500' :
                              'text-gray-500'
                            }`}
                          >
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                            <line x1="8" y1="21" x2="16" y2="21" />
                            <line x1="12" y1="17" x2="12" y2="21" />
                            <path d="M7 6h.01M7 9h.01" />
                          </svg>
                        ) : (
                          <svg
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-6 w-6 text-gray-500"
                          >
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                            <circle cx="8.5" cy="8.5" r="1.5" />
                            <polyline points="21 15 16 10 5 21" />
                          </svg>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 mb-1">{item.name}</div>
                        <div className="text-xs text-gray-500 mb-1">SKU: {item.sku}</div>
                        {item.cardCode && (
                          <div className="text-xs text-gray-500 mb-2">Mã thẻ: {item.cardCode}</div>
                        )}
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-600">
                            {order.currency || ''}{typeof item.price === 'number' ? item.price.toLocaleString() : item.price} × {item.quantity}
                          </span>
                          <span className="font-medium text-gray-900">
                            {order.currency || ''}{typeof item.price === 'number' && typeof item.quantity === 'number' 
                              ? (item.price * item.quantity).toLocaleString() 
                              : 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Desktop table view */}
              <div className="hidden sm:block border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Sản phẩm
                        </th>
                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Đơn giá
                        </th>
                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Số lượng
                        </th>
                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thành tiền
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {order.items && order.items.map((item, index) => (
                        <tr key={index}>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 mr-3">
                                <div className="h-10 w-10 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                                  {item.sku && item.sku.includes('taiwan.yearsim') ? (
                                    <svg
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className={`h-6 w-6 ${
                                        item.sku.includes('IF') ? 'text-blue-500' :
                                        item.sku.includes('CHUNGHOA') ? 'text-red-500' :
                                        item.sku.includes('OK') ? 'text-green-500' :
                                        'text-gray-500'
                                      }`}
                                    >
                                      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                                      <line x1="8" y1="21" x2="16" y2="21" />
                                      <line x1="12" y1="17" x2="12" y2="21" />
                                      <path d="M7 6h.01M7 9h.01" />
                                    </svg>
                                  ) : (
                                    <svg
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="h-6 w-6 text-gray-500"
                                    >
                                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                                      <circle cx="8.5" cy="8.5" r="1.5" />
                                      <polyline points="21 15 16 10 5 21" />
                                    </svg>
                                  )}
                                </div>
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900">{item.name}</div>
                                <div className="text-xs text-gray-500">SKU: {item.sku}</div>
                                {item.cardCode && (
                                  <div className="text-xs text-gray-500">Mã thẻ: {item.cardCode}</div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                            {order.currency || ''}{typeof item.price === 'number' ? item.price.toLocaleString() : item.price}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                            {item.quantity}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                            {order.currency || ''}{typeof item.price === 'number' && typeof item.quantity === 'number' 
                              ? (item.price * item.quantity).toLocaleString() 
                              : 'N/A'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            
            {/* Order totals */}
            <div className="px-4 sm:px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex flex-col items-end">
                <div className="w-full sm:w-64 space-y-2">
                  <div className="flex justify-between py-1 text-sm text-gray-600">
                    <dt>Tạm tính</dt>
                    <dd>{order.currency || ''}{
                      typeof order.subtotal === 'number' 
                        ? order.subtotal.toLocaleString() 
                        : typeof order.amount === 'number'
                          ? order.amount.toLocaleString()
                          : typeof order.total === 'number' && order.total !== 0
                            ? order.total.toLocaleString() 
                            : calculateSubtotal()
                    }</dd>
                  </div>
                  
                  {order.shipping && typeof order.shipping.cost === 'number' && (
                    <div className="flex justify-between py-1 text-sm text-gray-600">
                      <dt>Phí vận chuyển</dt>
                      <dd>{order.currency || ''}{order.shipping.cost.toLocaleString()}</dd>
                    </div>
                  )}
                  
                  {order.fee > 0 && (
                    <div className="flex justify-between py-1 text-sm text-gray-600">
                      <dt>Phí dịch vụ</dt>
                      <dd>{order.currency || ''}{order.fee.toLocaleString()}</dd>
                    </div>
                  )}
                  
                  {order.discount && (
                    <div className="flex justify-between py-1 text-sm text-gray-600">
                      <dt>Giảm giá</dt>
                      <dd>-{order.currency || ''}{typeof order.discount === 'number' 
                        ? order.discount.toLocaleString() 
                        : order.discount}</dd>
                    </div>
                  )}
                  
                  <div className="flex justify-between py-2 text-sm font-medium border-t border-gray-200">
                    <dt>Tổng cộng</dt>
                    <dd className="text-base">{order.currency || ''}{
                      typeof order.amount === 'number' && order.amount !== 0
                        ? order.amount.toLocaleString()
                        : typeof order.totalAmount === 'number' && order.totalAmount !== 0
                          ? order.totalAmount.toLocaleString()
                          : typeof order.total === 'number' && order.total !== 0
                            ? order.total.toLocaleString()
                            : calculateTotal()
                    }</dd>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Actions */}
            <div className="px-4 sm:px-6 py-4 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row gap-3">
                <Link href={`/${store}/cart`}>
                  <button className="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded bg-white text-gray-700 hover:bg-gray-50 text-sm">
                    Quay lại giỏ hàng
                  </button>
                </Link>
                
                {order.status !== 'cancelled' && (order.paymentStatus === 'pending' || !order.paymentStatus) && (
                  <Link href={`/${store}/checkout?order=${order.id}`}>
                    <button className="w-full sm:w-auto px-4 py-2 border border-transparent rounded bg-blue-600 text-white hover:bg-blue-700 text-sm">
                      Tiếp tục thanh toán
                    </button>
                  </Link>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <FaExclamationCircle className="text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm text-yellow-700">Không tìm thấy thông tin đơn hàng</p>
                <div className="mt-4">
                  <button
                    onClick={() => router.push(`/${store}/cart`)}
                    className="bg-white hover:bg-gray-100 text-yellow-800 border border-yellow-300 px-4 py-2 rounded text-sm"
                  >
                    Quay lại giỏ hàng
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

function OrderDetailWithContext(props) {
  return (
    <ContextProviderComponent>
      <SiteContext.Consumer>
        {(context) => <OrderDetail {...props} context={context} />}
      </SiteContext.Consumer>
    </ContextProviderComponent>
  )
}

export async function getStaticPaths() {
  const allstores = await fetchStore()
  
  // We can't know all order IDs at build time, so we'll generate an empty array
  const paths = []
  
  return {
    paths,
    fallback: true, // Use fallback to generate pages on demand
  }
}

export async function getStaticProps({ params }) {
  const allstores = await fetchStore()
  const storeId = params.store
  const store = allstores.find((store) => store.storeId === storeId)
  
  return {
    props: {
      storeObject: store || null,
    },
    revalidate: 60, // Revalidate the page every 60 seconds
  }
}

export default OrderDetailWithContext 