import Head from "next/head"
import { titleIfy, appendOrUpdateAffiliateParam } from "../utils/helpers"
import { Formik, Form, Field, ErrorMessage } from "formik"
import { SiteContext, ContextProviderComponent } from "../context/mainContext"
import { useEffect, useState, useContext } from "react"
import Button from "../components/Button"

function Affiliates() {
  const [shops, setShops] = useState([
    { name: "vanggoc", shopId: "vanggoc", affiliateProgram: "Yes" },
    { name: "doraemon", shopId: "doraemon", affiliateProgram: "Yes" },
    { name: "shopid3", shopId: "shopid3", affiliateProgram: "Yes" },
  ])
  const [affiliateLink, setAffiliateLink] = useState("")
  const { addAffiliate, getPreferredAffiliate } =
    useContext(SiteContext)
  const [affiliateIdState, setAffiliateIdState] = useState(getPreferredAffiliate() || "")

  const addShop = () => {
    const newShop = {
      name: `Shop ${shops.length + 1}`,
      affiliateProgram: "Yes",
    }
    setShops([...shops, newShop])
  }

  const currentUrl = "https://shopme.top"

  const generateAffiliateLink = (values) => {
    const affiliateCode = values.myAffiliateCode
    if (affiliateCode.length < 4) {
      alert("Enter your phone number or Facebook ID, at least 4 char")
      return
    }
    const generatedLink = `${currentUrl}/?affiliate=${affiliateCode}`
    setAffiliateLink(generatedLink)
    if (affiliateCode.length > 0) {
      const newAffiliate = {
        affiliateid: affiliateCode,
        timestamp: Date.now(), // Current timestamp
        storeid: "",
        sku: "",
      }
      addAffiliate(newAffiliate)
      /* addAffiliate(affiliateCode) */
      setAffiliateIdState(affiliateCode)
    }
  }

  let contextAffiliate = getPreferredAffiliate();

  return (
    <>
      <div className="w-full">
        <Head>
          <title>ShopMe: MAG GROUP</title>
          <meta name="description" content="All Products" />
          <meta property="og:title" content="All Products" key="title" />
        </Head>
        <div className="flex flex-col items-center">
          <div className="max-w-fw flex flex-col w-full">
            <div className="pt-4 sm:pt-6 md:pt-8">
              <h3 className="text-lg md:text-xl font-light text-right">
                {titleIfy("MAG GROUP")}
              </h3>
            </div>
            <div className="flex flex-col md:flex-row">
              <div className="w-full md:w-3/4">
                <Formik
                  initialValues={{
                    currentAffiliate: "",
                    myAffiliateCode: "",
                    generateNewAffiliateCode: "",
                    myAffiliateLink: "",
                    address: "",
                    email: "",
                    mobile: "",
                    facebook: "",
                    twitter: "",
                    linkedin: "",
                    bankAccount: "",
                    bankAccountAtBank: "",
                    preferredProducts: "",
                  }}
                  onSubmit={(values, { setSubmitting }) => {
                    console.log(values)
                    generateAffiliateLink(values)
                    setSubmitting(false)
                  }}
                >
                  {({ isSubmitting }) => (
                    <Form>
                      <table className="table w-full md:table">
                        <tbody>
                          <tr>
                            <td>
                              <label htmlFor="currentAffiliate">
                                Current Affiliate
                              </label>
                            </td>
                            <td>
                              {contextAffiliate
                                ? contextAffiliate.affiliateid
                                : "NO AFFILIATE YET"}
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label
                                htmlFor="myAffiliateCode"
                                className="mr-0 md:mr-4"
                              >
                                My Affiliate Code [Phone Number or Facebook]
                              </label>
                            </td>
                            <td className="flex flex-col md:flex-row">
                              <Field
                                type="text"
                                id="myAffiliateCode"
                                name="myAffiliateCode"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your affiliate code here"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <div className="flex flex-col md:flex-row"></div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="myAffiliateLink">
                                My Affiliate Link
                              </label>
                            </td>
                            <td colSpan="2">
                              <Field
                                type="text"
                                id="myAffiliateLink"
                                name="myAffiliateLink"
                                className="mt-1 p-2 border rounded-md w-full"
                                value={affiliateLink}
                                disabled
                              />
                              <ErrorMessage
                                name="myAffiliateLink"
                                component="div"
                                className="text-red-500"
                              />
                              <div
                                style={{
                                  display: "flex",
                                  gap: "1rem",
                                  flexWrap: "wrap",
                                }}
                              >
                                <Button
                                  type="submit"
                                  disabled={isSubmitting}
                                  full
                                  title="GENERATE"
                                />
                                <Button
                                  full
                                  title="COPY"
                                  onClick={() => {
                                    navigator.clipboard.writeText(affiliateLink)
                                  }}
                                />
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td colSpan={2}>
                              <label>AFFILIATE INFO:</label>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="address">Address</label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="address"
                                name="address"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your address"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="email">Email</label>
                            </td>
                            <td>
                              <Field
                                type="email"
                                id="email"
                                name="email"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your email"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="mobile">Mobile</label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="mobile"
                                name="mobile"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your mobile number"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="facebook">Facebook</label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="facebook"
                                name="facebook"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your Facebook profile link"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="twitter">Twitter</label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="twitter"
                                name="twitter"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your Twitter profile link"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="linkedin">LinkedIn</label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="linkedin"
                                name="linkedin"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your LinkedIn profile link"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="bankAccount">Bank Account</label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="bankAccount"
                                name="bankAccount"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your bank account details"
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="bankAccountAtBank">At Bank</label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="bankAccountAtBank"
                                name="bankAccountAtBank"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Bank Name: "
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label htmlFor="preferredProducts">
                                Preferred Products
                              </label>
                            </td>
                            <td>
                              <Field
                                type="text"
                                id="preferredProducts"
                                name="preferredProducts"
                                className="mt-1 p-2 border rounded-md w-full"
                                placeholder="Enter your preferred products to affiliate"
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </Form>
                  )}
                </Formik>
              </div>
              <div className="w-full md:w-3/4">
                <table className="table w-full md:table">
                  <thead>
                    <tr>
                      <th>ShopId</th>
                      <th>Affiliate Program</th>
                      <th>Shop Link</th>
                    </tr>
                  </thead>
                  <tbody>
                    {shops.map((shop, index) => (
                      <tr key={index}>
                        <td>{shop.name}</td>
                        <td>{shop.affiliateProgram}</td>
                        <td>
                          <div className="flex items-center">
                            <span>{`${appendOrUpdateAffiliateParam(
                              "https://shopme.top/" + shop.shopId,
                              contextAffiliate? contextAffiliate.affiliateid : ""
                            )}`}</span>
                          </div>
                        </td>
                        <td>
                          <Button
                            full
                            title="COPY"
                            onClick={() => {
                              navigator.clipboard.writeText(
                                `${appendOrUpdateAffiliateParam(
                                  "https://shopme.top/" + shop.shopId,
                                  contextAffiliate.affiliateid
                                )}`
                              )
                            }}
                            small={true}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <button
                  onClick={addShop}
                  className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-4 w-full"
                >
                  Enroll New Shop
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

const AffiliatesWithContext = (props) => {
  return (
    <div>
      <ContextProviderComponent>
        <SiteContext.Consumer>
          {(context) => <Affiliates {...props} context={context} />}
        </SiteContext.Consumer>
      </ContextProviderComponent>
    </div>
  )
}

export default AffiliatesWithContext
