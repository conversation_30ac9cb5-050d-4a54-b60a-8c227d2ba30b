import React, { useEffect, useState } from 'react';
import Layout from '../../../../layouts/layout';
import { useRouter } from 'next/router';
import Link from 'next/link';
import axios from 'axios';

// Helper for logging
const log711CardReturn = (message, data = null) => {
  console.log(`[7-11 CARD RETURN] ${message}`, data ? JSON.stringify(data) : '');
};

export default function CardPaymentResult() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState('processing');
  const [orderData, setOrderData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Make sure router is ready and has query parameters
    if (!router.isReady) return;
    
    const processCallbackParams = async () => {
      try {
        // Extract all query parameters from the URL
        const params = router.query;
        log711CardReturn('Received return callback with params', params);

        // Check for required parameters
        if (!params.order_id) {
          setError('Missing order ID in callback parameters');
          setStatus('error');
          setLoading(false);
          return;
        }

        // Store the result in our backend
        const response = await axios.post('/api/payment/process-card-result', {
          orderId: params.order_id,
          resultCode: params.result_code || '',
          message: params.message || '',
          allParams: params
        });

        log711CardReturn('Processed return callback', response.data);

        if (response.data.success) {
          setOrderData(response.data.orderData);
          setStatus(response.data.paymentStatus || 'success');
        } else {
          setError(response.data.error || 'Failed to process payment result');
          setStatus('error');
        }
      } catch (err) {
        log711CardReturn('Error processing return callback', {
          message: err.message,
          response: err.response?.data
        });
        setError('An error occurred while processing your payment result. Please contact customer support.');
        setStatus('error');
      } finally {
        setLoading(false);
      }
    };

    processCallbackParams();
  }, [router.isReady, router.query]);

  const renderContent = () => {
    if (loading) {
      return (
        <div className="text-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold mb-2">Đang xử lý thanh toán...</h2>
          <p className="text-gray-600">Vui lòng đợi trong khi chúng tôi xác nhận thanh toán của bạn.</p>
        </div>
      );
    }

    if (status === 'success') {
      return (
        <div className="text-center p-8">
          <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-green-700 mb-2">Thanh toán thành công!</h2>
          <p className="text-gray-600 mb-6">
            Cảm ơn bạn đã thanh toán. Đơn hàng của bạn đã được xác nhận và đang được xử lý.
          </p>
          
          {orderData && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6 max-w-md mx-auto text-left">
              <h3 className="font-semibold mb-2">Chi tiết đơn hàng:</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <span className="text-gray-600">Mã đơn hàng:</span>
                <span className="font-medium">{orderData.orderId}</span>
                {orderData.date && (
                  <>
                    <span className="text-gray-600">Ngày đặt hàng:</span>
                    <span className="font-medium">{new Date(orderData.date).toLocaleString()}</span>
                  </>
                )}
                {orderData.totalAmount && (
                  <>
                    <span className="text-gray-600">Tổng thanh toán:</span>
                    <span className="font-medium">{orderData.totalAmount} {orderData.currency || 'NT$'}</span>
                  </>
                )}
              </div>
            </div>
          )}
          
          <div className="flex justify-center space-x-4">
            <Link href="/" className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              Quay về trang chủ
            </Link>
            <Link href="/orders" className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
              Kiểm tra đơn hàng
            </Link>
          </div>
        </div>
      );
    }

    if (status === 'pending') {
      return (
        <div className="text-center p-8">
          <div className="bg-yellow-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-yellow-700 mb-2">Thanh toán đang chờ xử lý</h2>
          <p className="text-gray-600 mb-6">
            Thanh toán của bạn đang được xử lý. Trạng thái sẽ được cập nhật trong thời gian sớm nhất.
          </p>
          
          {orderData && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6 max-w-md mx-auto text-left">
              <h3 className="font-semibold mb-2">Chi tiết đơn hàng:</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <span className="text-gray-600">Mã đơn hàng:</span>
                <span className="font-medium">{orderData.orderId}</span>
                {orderData.date && (
                  <>
                    <span className="text-gray-600">Ngày đặt hàng:</span>
                    <span className="font-medium">{new Date(orderData.date).toLocaleString()}</span>
                  </>
                )}
                {orderData.totalAmount && (
                  <>
                    <span className="text-gray-600">Tổng thanh toán:</span>
                    <span className="font-medium">{orderData.totalAmount} {orderData.currency || 'NT$'}</span>
                  </>
                )}
              </div>
            </div>
          )}
          
          <div className="flex justify-center space-x-4">
            <Link href="/" className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              Quay về trang chủ
            </Link>
            <Link href="/orders" className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
              Kiểm tra đơn hàng
            </Link>
          </div>
        </div>
      );
    }
    
    // Error or other status
    return (
      <div className="text-center p-8">
        <div className="bg-red-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-red-700 mb-2">Thanh toán thất bại</h2>
        <p className="text-gray-600 mb-6">
          {error || 'Đã xảy ra lỗi trong quá trình thanh toán. Vui lòng thử lại hoặc liên hệ với chúng tôi để được hỗ trợ.'}
        </p>
        
        <div className="flex justify-center space-x-4">
          <Link href="/" className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            Quay về trang chủ
          </Link>
          <Link href="/contact" className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            Liên hệ hỗ trợ
          </Link>
        </div>
      </div>
    );
  };

  return (
    <Layout title="Kết quả thanh toán">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
          <div className="bg-gray-50 py-3 px-6 border-b">
            <h1 className="text-xl font-semibold text-gray-800">Kết quả thanh toán</h1>
          </div>
          
          <div className="p-6">
            {renderContent()}
          </div>
          
          {/* Debug information in development */}
          {process.env.NODE_ENV !== 'production' && !loading && router.query && (
            <div className="border-t p-4">
              <details className="text-sm">
                <summary className="font-medium cursor-pointer mb-2">Debug Information</summary>
                <pre className="bg-gray-100 p-3 rounded overflow-auto text-xs">
                  {JSON.stringify({ 
                    query: router.query,
                    orderData,
                    status,
                    error
                  }, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
} 