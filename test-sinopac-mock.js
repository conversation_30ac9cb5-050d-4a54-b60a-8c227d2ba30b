const axios = require('axios');

// Test SinoPAC with mock mode
async function testSinoPacMockMode() {
  try {
    // Set environment variable for mock mode
    process.env.SINOPAC_MOCK_MODE = 'true';
    
    const testPayload = {
      OrderNo: 'MAG-MOCK-' + Date.now(),
      Amount: 2000,
      PrdtName: 'Test Product Mock',
      CustomerName: 'Test Customer',
      CustomerEmail: '<EMAIL>',
      CustomerPhone: '0912345678',
      CustomerAddress: 'Test Address'
    };

    console.log('Testing SinoPAC API in MOCK MODE...');
    console.log('Payload:', testPayload);

    const response = await axios.post('http://localhost:3000/api/payment/sinopac/order?mock=true', testPayload, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('\n=== MOCK MODE SUCCESS ===');
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.Status === 'S') {
      console.log('\n✅ Mock payment order created successfully!');
      console.log('Bank Code:', response.data.Result.ATMInfo.BankCode);
      console.log('Account Number:', response.data.Result.ATMInfo.AccountNo);
      console.log('Payment URL:', response.data.Result.PaymentURL);
      console.log('Expiry Date:', response.data.Result.ExpireDate);
    }
    
  } catch (error) {
    console.error('Test failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
}

testSinoPacMockMode(); 