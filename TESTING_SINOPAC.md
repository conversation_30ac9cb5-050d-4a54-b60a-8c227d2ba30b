# SinoPAC Payment Integration Testing Checklist

## 🎯 Overview
This document outlines testing procedures for the revised SinoPAC payment integration based on the official PHP sample code.

## 🔑 Key Changes Implemented
- ✅ Nonce-based authentication (replacing simple HMAC)
- ✅ XOR key calculations for HashID generation
- ✅ AES-CBC encryption with proper IV calculation
- ✅ Sign generation with field sorting and concatenation
- ✅ Proper API request structure (Version, ShopNo, APIService, Sign, Nonce, Message)
- ✅ Payment type specific parameters (ATMParam, CardParam, MobileParam)
- ✅ Callback handler with decryption capabilities

## 💳 **Payment Types Available**

### 1. ATM Virtual Account (`PayType: 'A'`)
**Current Implementation in UI**
- Traditional bank transfer to virtual account
- Returns: `ATMInfo.BankCode`, `ATMInfo.AccountNo`, `ExpireDate`
- Customer manually transfers to provided account
- **No QR Code involved**

### 2. 📱 Mobile Payment / QR Code (`PayType: 'M'`)
**Dynamic QR Code Payment**
- Returns: `QRCode`, `PaymentURL`, `ExpireDate`
- Customer scans QR code with mobile banking app
- **This IS the dynamic QR code flow you mentioned!**
- QR codes are unique per transaction and expire (default 10 minutes)

### 3. Credit Card (`PayType: 'C'`)
- Returns hosted payment page URL
- Customer enters card details on secure page
- Supports recurring billing

## 🧪 Testing Environments

### Sandbox Environment
- **Portal**: https://sandbox.sinopac.com/DSF.Portal/ (admin/admin)
- **API Endpoint**: https://sandbox.sinopac.com/QPay.WebAPI/api
- **Encryption Test**: https://sandbox.sinopac.com/QPay.ApiClient/Calc/Encrypt
- **Decryption Test**: https://sandbox.sinopac.com/QPay.ApiClient/Calc/Descrypt

### Test Credentials
```javascript
MERCHANT_ID: 'NA0511_001'
HASH: {
  A1: 'F342DAABD58249D8',
  A2: 'D3E28D4E9A4E4EE2', 
  B1: 'C61852BEBDA44676',
  B2: '1BD9BDB007E34418'
}
X_KEY: 'b5e6986d-8636-4aa0-8c93-441ad14b2098'
```

## 📋 Test Cases

### 1. Basic ATM Payment Flow
```bash
# Test Request
POST /api/payment/sinopac/order
{
  "OrderNo": "TEST001",
  "Amount": 100,
  "PrdtName": "Test Product",
  "PayType": "A"
}

# Expected Response
{
  "Status": "S",
  "Result": {
    "ATMInfo": {
      "BankCode": "008",
      "AccountNo": "****************"
    },
    "ExpireDate": "********",
    "QRCode": "...",
    "PaymentURL": "..."
  }
}
```

### 2. 📱 QR Code Payment Flow (NEW)
```bash
# Test Request
POST /api/payment/sinopac/qrcode
{
  "OrderNo": "TEST002",
  "Amount": 200,
  "PrdtName": "QR Code Test Product",
  "ExpMinutes": "10"
}

# Expected Response
{
  "Status": "S",
  "Result": {
    "OrderNo": "TEST002",
    "Amount": 200,
    "QRCode": "base64_encoded_qr_data_or_payment_string",
    "PaymentURL": "https://sandbox.sinopac.com/qrpay/TEST002",
    "ExpireDate": "2025-01-16T10:30:00Z",
    "ExpMinutes": "10"
  }
}
```

### 3. Credit Card Payment Flow
```bash
POST /api/payment/sinopac/order
{
  "OrderNo": "TEST003",
  "Amount": 300,
  "PrdtName": "Test Product CC", 
  "PayType": "C"
}
```

## 📱 **QR Code Payment Testing (Dynamic QR)**

### Frontend QR Code Component Testing
```javascript
// Test the new QR code component
import SinoPACQRCodePayment from '../components/taiwan/payment/methods/SinoPACQRCode';

<SinoPACQRCodePayment
  orderId="TEST_QR_001"
  amount={150}
  productName="Test QR Payment"
  onSuccess={(result) => console.log('Payment success:', result)}
  onError={(error) => console.error('Payment error:', error)}
/>
```

### QR Code Flow Testing Steps:
1. **Generate QR Code**: Component calls `/api/payment/sinopac/qrcode`
2. **Display QR Code**: Shows scannable QR code with timer countdown
3. **Customer Scanning**: Customer scans with mobile banking app
4. **Payment Processing**: SinoPAC processes the payment
5. **Callback Notification**: Backend receives payment confirmation
6. **Status Update**: Frontend shows payment success/failure

### QR Code Expiry Testing:
- ✅ QR codes expire after specified minutes (default 10)
- ✅ Timer countdown shows remaining time
- ✅ Automatic refresh option when expired
- ✅ New QR code generated with fresh expiry

### Mobile App Integration:
The QR codes work with Taiwan mobile banking apps such as:
- 永豐銀行 (Bank SinoPAC) mobile app
- Other Taiwan Pay compatible apps
- Mobile wallets supporting SinoPAC QR payments

## 🔍 Debug & Verification Steps

### 1. HashID Calculation Verification
```javascript
// Expected XOR calculation:
// XOR1 = A1 XOR A2 = F342DAABD58249D8 XOR D3E28D4E9A4E4EE2
// XOR2 = B1 XOR B2 = C61852BEBDA44676 XOR 1BD9BDB007E34418
// HashID = XOR1 + XOR2
console.log('HashID:', getHashID(SINOPAC_API.HASH));
```

### 2. Nonce & IV Verification
```javascript
// Nonce should be 16-character string from SinoPAC API
// IV should be last 16 characters of SHA256(nonce)
const nonce = await getNonce(MERCHANT_ID, API_ENDPOINT);
const iv = getIV(nonce);
console.log('Nonce:', nonce, 'IV:', iv);
```

### 3. Sign Verification
```javascript
// Sign should be SHA256 of sorted fields + nonce + hashID
const sign = getSign(orderData, nonce, hashID);
console.log('Sign:', sign);
```

### 4. Encryption Verification
```javascript
// Message should be AES-256-CBC encrypted with PKCS7 padding
const encrypted = encryptAesCBC(JSON.stringify(orderData), hashID, iv);
console.log('Encrypted Message:', encrypted);
```

## 🖥️ Frontend Testing

### 1. BankTransfer Component Integration (ATM)
- Navigate to payment page
- Select Taiwan Bank Transfer
- Fill in customer details
- Submit payment form
- Verify ATM details are displayed correctly

### 2. 📱 QR Code Component Integration (NEW)
- Navigate to payment page
- Select QR Code Payment option
- Verify QR code generates and displays
- Test timer countdown functionality
- Test QR code refresh functionality
- Test mobile payment URL link

### 3. Debug Information Panel
- Enable debug mode with `?debug=true`
- Check all API calls are logged
- Verify request/response data
- Test error handling scenarios

## 🔄 Callback Testing

### 1. PayToken Notification
```bash
POST /api/payment/sinopac-callback
{
  "ShopNo": "NA0511_001",
  "PayToken": "ABC123...",
  "OrderNo": "TEST001"
}

# Expected Response: {"Status": "S"}
```

### 2. Encrypted Message Processing
```bash
POST /api/payment/sinopac-callback  
{
  "Version": "1.0.0",
  "ShopNo": "NA0511_001",
  "APIService": "OrderNotify",
  "Sign": "...",
  "Nonce": "...",
  "Message": "..."
}
```

## ⚠️ Error Scenarios to Test

### 1. Network Errors
- API timeout (30s limit)
- Connection failures
- Invalid responses

### 2. Authentication Errors
- Wrong merchant ID
- Invalid hash keys
- Incorrect X-Key

### 3. Data Validation Errors
- Missing required fields
- Invalid amounts
- Malformed order numbers

### 4. Encryption Errors
- Decryption failures
- Invalid nonce values
- Corrupted messages

### 5. 📱 QR Code Specific Errors
- QR code generation failures
- Expired QR codes
- Invalid payment URLs
- Mobile app compatibility issues

## 📊 Performance Testing

### 1. Response Times
- Nonce API call: < 5s
- Order creation: < 10s  
- QR code generation: < 5s
- Callback processing: < 3s

### 2. Concurrent Requests
- Test multiple simultaneous orders
- Verify nonce uniqueness
- Check for race conditions
- Test QR code uniqueness

## 🛠️ Mock Mode Testing

Enable mock mode for development:
```bash
# Environment variable
SINOPAC_MOCK_MODE=true

# URL parameter for ATM payment
/api/payment/sinopac/order?mock=true

# URL parameter for QR code payment
/api/payment/sinopac/qrcode?mock=true
```

Mock responses provide:
- Consistent test data
- No external API calls
- Predictable behavior
- QR code demo data

## ✅ Production Readiness Checklist

- [ ] All test cases passing (ATM + QR code)
- [ ] Error handling working correctly
- [ ] Logging comprehensive but not exposing sensitive data
- [ ] Production credentials configured
- [ ] HTTPS enforced for all endpoints
- [ ] Rate limiting implemented
- [ ] Monitoring and alerting setup
- [ ] Callback URL accessible from SinoPAC servers
- [ ] Database properly handles concurrent updates
- [ ] QR code expiry handling working correctly
- [ ] Mobile app compatibility verified

## 📞 Support & Resources

- **SinoPAC Technical Support**: Contact through merchant portal
- **PHP Sample Code**: `/scripts/docs/Sinopac/QPay.SampleCode-php/`
- **API Documentation**: Available in merchant portal
- **Encryption Tools**: https://sandbox.sinopac.com/QPay.ApiClient/
- **QR Code Testing**: Use SinoPAC mobile app in sandbox mode

## 📝 Notes

- **ATM Payment**: Traditional bank transfer, no QR code
- **Mobile Payment**: Dynamic QR codes that expire, scannable with mobile apps
- **Credit Card**: Hosted payment pages
- Always test in sandbox before production deployment
- Keep API keys secure and rotate regularly  
- Monitor transaction success rates
- Log all failures for debugging
- Implement proper retry mechanisms
- Handle edge cases gracefully
- QR codes should be unique per transaction and expire appropriately 