# SinoPAC E3501 Error Fixes

## 🔍 **Root Cause Analysis**

The **E3501 – 訊息內容錯誤** (Message content error) was caused by several issues:

### **1. Product Name Encoding Issues**
```javascript
// PROBLEM: Vietnamese characters causing encoding errors
"PrdtName": "CHUNG HOA 1 NĂM "  // Contains diacritics (NĂM)

// SOLUTION: Clean and sanitize product names
"PrdtName": "CHUNG HOA 1 NAM"   // ASCII-only, normalized
```

### **2. Invalid Callback URLs**
```javascript
// PROBLEM: Localhost URLs rejected by SinoPAC sandbox
"ReturnURL": "http://localhost:3000/api/payment/sinopac-callback"

// SOLUTION: Use publicly accessible URLs for testing
"ReturnURL": "https://webhook.site/your-test-url"  // Or ngrok URL
```

### **3. Data Validation Issues**
```javascript
// PROBLEM: Insufficient input validation and cleaning
Amount: req.body.Amount.toString()  // May contain invalid characters

// SOLUTION: Strict validation and cleaning
Amount: validateAmount(req.body.Amount)  // Ensures numeric string
```

## 🛠️ **Implemented Fixes**

### **1. Enhanced Data Sanitization**
```javascript
const sanitizeProductName = (name) => {
  return name
    .normalize('NFD')                    // Decompose Vietnamese characters
    .replace(/[\u0300-\u036f]/g, '')    // Remove diacritics (ă, â, etc.)
    .replace(/[^a-zA-Z0-9\s]/g, '')     // Remove special characters
    .replace(/\s+/g, ' ')               // Normalize spaces
    .trim()
    .toUpperCase()
    .substring(0, 50);                  // Limit to 50 characters
};
```

### **2. URL Validation**
```javascript
const getValidCallbackUrl = (origin) => {
  // Production: use actual domain
  if (origin && !origin.includes('localhost')) {
    return `${origin}/api/payment/sinopac-callback`;
  }
  
  // Development: use publicly accessible URL
  return 'https://webhook.site/your-test-url';  // Replace with your URL
};
```

### **3. Amount Validation**
```javascript
const validateAmount = (amount) => {
  const numAmount = parseInt(amount);
  if (isNaN(numAmount) || numAmount <= 0) {
    throw new Error('Invalid amount');
  }
  return numAmount.toString();
};
```

### **4. Enhanced API Headers**
```javascript
headers: {
  'Content-Type': 'application/json; charset=utf-8',
  'Accept': 'application/json',
  'X-KeyID': SINOPAC_API.X_KEY,
  'User-Agent': 'MAG-Shop/1.0'
}
```

## 🧪 **Testing the Fixes**

### **Test Case 1: Product Name Cleaning**
```javascript
// Before fix:
"PrdtName": "CHUNG HOA 1 NĂM " → E3501 error

// After fix:
"PrdtName": "CHUNG HOA 1 NAM" → Should work

// Test command:
curl -X POST http://localhost:3000/api/payment/sinopac/qrcode \
  -H "Content-Type: application/json" \
  -d '{
    "OrderNo": "TEST-001",
    "Amount": "1000",
    "PrdtName": "CHUNG HOA 1 NĂM SPECIAL CHARS !@#$%"
  }'
```

### **Test Case 2: URL Handling**
```javascript
// Development: Will use webhook.site URL
// Production: Will use your actual domain

// Check logs for:
console.log('FIXED: Data cleaning results:', {
  callbackUrl: validCallbackUrl
});
```

### **Test Case 3: Amount Validation**
```javascript
// Test invalid amounts:
"Amount": "abc"     → Should throw validation error
"Amount": "-100"    → Should throw validation error
"Amount": "1000"    → Should pass validation
```

## 🔧 **Setup for Testing**

### **1. Update Callback URL**
Replace the webhook.site URL in the code with your test URL:

```javascript
// Option A: Use ngrok (recommended)
// 1. Install ngrok: npm install -g ngrok
// 2. Run: ngrok http 3000
// 3. Use the https URL: https://abc123.ngrok.io

// Option B: Use webhook.site
// 1. Go to webhook.site
// 2. Copy your unique URL
// 3. Replace in getValidCallbackUrl function

return 'https://webhook.site/#!/your-unique-id';
```

### **2. Monitor Logs**
The enhanced logging will show:

```bash
FIXED: Data cleaning results: {
  originalName: 'CHUNG HOA 1 NĂM ',
  cleanedName: 'CHUNG HOA 1 NAM',
  originalAmount: '3750',
  validAmount: '3750',
  callbackUrl: 'https://your-test-url.com/callback'
}
```

## 🎯 **Expected Results**

### **Before Fixes:**
```
E3501 – 訊息內容錯誤
Status: 400 Bad Request
```

### **After Fixes:**
```javascript
// Should get successful response:
{
  "Status": "S",
  "Message": "Success",
  "Result": {
    "OrderNo": "MAG-13677",
    "QRCode": "actual-qr-code-data",
    "PaymentURL": "https://...",
    "ExpireDate": "2025-06-08T..."
  }
}
```

## 🚨 **Troubleshooting**

### **If Still Getting E3501:**

1. **Check Product Name:**
   ```bash
   # Look for this in logs:
   cleanedName: 'PRODUCT NAME WITHOUT SPECIAL CHARS'
   ```

2. **Check Callback URL:**
   ```bash
   # Should NOT be localhost:
   callbackUrl: 'https://your-public-url.com/callback'
   ```

3. **Check Amount Format:**
   ```bash
   # Should be clean numeric string:
   validAmount: '3750'
   ```

4. **Check API Response:**
   ```bash
   # Look for detailed error messages:
   Raw API response: { "Status": "F", "Message": "Detailed error" }
   ```

## ✅ **Verification Steps**

1. **Run a test payment** with Vietnamese product name
2. **Check console logs** for data cleaning results
3. **Verify callback URL** is publicly accessible
4. **Monitor API response** for success/detailed errors
5. **Test fallback system** still works if API fails

The fixes address the three main causes of E3501 errors while maintaining the functional fallback system for reliability. 