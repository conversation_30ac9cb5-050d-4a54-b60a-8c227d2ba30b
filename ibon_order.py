#!/usr/bin/env python3
import requests
import json
import argparse
from datetime import datetime

class IBONOrder:
    def __init__(self):
        # Load config from file
        with open('components/taiwan/payment/methods/711/config.json', 'r') as f:
            config = json.load(f)
        
        # Get iBON configuration
        ibon_config = config['tw']['tw711']['ibon']
        
        self.base_url = "https://api.ezpay.com.tw"
        self.credentials = {
            "merchantID": ibon_config['merchantID'],
            "hashKey": ibon_config['hashKey'],
            "hashIV": ibon_config['hashIV']
        }
        self.returnURL = ibon_config['returnURL']
        self.clientBackURL = ibon_config['clientBackURL']
        self.paymentInfoURL = ibon_config['paymentInfoURL']
        self.clientRedirectURL = ibon_config['clientRedirectURL']
        self.notifyURL = ibon_config['notifyURL']
        self.customerURL = ibon_config['customerURL']
        self.language = ibon_config['language']
        self.version = ibon_config['version']
        self.respondType = ibon_config['respondType']

    def create_order(self, amount, order_id=None, buyer_name=None, buyer_phone=None, buyer_email=None):
        """Create a new iBON order"""
        if not order_id:
            order_id = f"IBON_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        order_data = {
            "merchantID": self.credentials["merchantID"],
            "hashKey": self.credentials["hashKey"],
            "hashIV": self.credentials["hashIV"],
            "returnURL": self.returnURL,
            "clientBackURL": self.clientBackURL,
            "paymentInfoURL": self.paymentInfoURL,
            "clientRedirectURL": self.clientRedirectURL,
            "notifyURL": self.notifyURL,
            "customerURL": self.customerURL,
            "language": self.language,
            "version": self.version,
            "respondType": self.respondType,
            "merchantOrderNo": order_id,
            "amt": str(amount),
            "itemDesc": f"Order #{order_id}",
            "encrypt": "1"
        }

        # Add buyer information if provided
        if buyer_name:
            order_data["buyerName"] = buyer_name
        if buyer_phone:
            order_data["buyerPhone"] = buyer_phone
        if buyer_email:
            order_data["buyerEmail"] = buyer_email

        # Make request to iBON API
        response = requests.post(f"{self.base_url}/iBon/Create", json=order_data)
        return response.json()

def main():
    parser = argparse.ArgumentParser(description="Create an order using iBON payment system")
    parser.add_argument("--amount", type=float, required=True, help="Order amount")
    parser.add_argument("--order-id", type=str, help="Custom order ID (optional)")
    parser.add_argument("--buyer-name", type=str, help="Buyer name (optional)")
    parser.add_argument("--buyer-phone", type=str, help="Buyer phone (optional)")
    parser.add_argument("--buyer-email", type=str, help="Buyer email (optional)")

    args = parser.parse_args()

    try:
        ibon = IBONOrder()
        result = ibon.create_order(
            amount=args.amount,
            order_id=args.order_id,
            buyer_name=args.buyer_name,
            buyer_phone=args.buyer_phone,
            buyer_email=args.buyer_email
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 