const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

// Initialize Next.js
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Path to logs file
const LOGS_DIR = path.join(process.cwd(), 'data', 'callbacks');
const LOGS_FILE_PATH = path.join(LOGS_DIR, 'logsapn.json');

// Ensure logs directory exists
try {
  console.log('Custom server: Checking if logs directory exists...');
  if (!fs.existsSync(LOGS_DIR)) {
    console.log('Custom server: Creating logs directory...');
    fs.mkdirSync(LOGS_DIR, { recursive: true });
    console.log('Custom server: Logs directory created successfully.');
  } else {
    console.log('Custom server: Logs directory already exists.');
  }
} catch (error) {
  console.error(`Custom server: Error creating logs directory: ${error.message}`);
}

// Middleware to log APN callbacks
function logAPNCallback(req, res, startTime, reqBody, responseStatus, responseBody) {
  try {
    // Calculate processing time
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    // Create log entry
    const logEntry = {
      timestamp: new Date().toISOString(),
      requestPayload: reqBody,
      responseStatus: responseStatus,
      responseBody: responseBody,
      processingTimeMs: processingTime,
      clientIP: req.headers['x-forwarded-for'] || req.socket.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'] || 'unknown',
      method: req.method,
      url: req.url
    };
    
    // Make sure directory exists again
    if (!fs.existsSync(LOGS_DIR)) {
      fs.mkdirSync(LOGS_DIR, { recursive: true });
    }
    
    // Read existing logs or create new logs array
    let logs = [];
    if (fs.existsSync(LOGS_FILE_PATH)) {
      const logsData = fs.readFileSync(LOGS_FILE_PATH, 'utf8');
      try {
        logs = JSON.parse(logsData);
        if (!Array.isArray(logs)) logs = [];
      } catch (e) {
        logs = [];
      }
    }
    
    // Add new log entry
    logs.push(logEntry);
    
    // Write logs to file
    fs.writeFileSync(LOGS_FILE_PATH, JSON.stringify(logs, null, 2), 'utf8');
    
    console.log(`Custom server: Logged APN callback to ${LOGS_FILE_PATH}`);
  } catch (error) {
    console.error(`Custom server: Error logging callback: ${error.message}`);
  }
}

// Start the server
app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    const { pathname } = parsedUrl;
    
    // Intercept APN callback requests for custom logging
    if (pathname === '/api/payment/apn-callback' && req.method === 'POST') {
      console.log('Custom server: Intercepted APN callback request');
      
      // Record start time
      const startTime = Date.now();
      
      // Collect request body
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      // Process the request
      req.on('end', () => {
        try {
          // Attempt to parse the body as JSON
          let reqBody;
          try {
            reqBody = JSON.parse(body);
          } catch (e) {
            reqBody = { rawBody: body };
          }
          
          // Store the original end method
          const originalEnd = res.end;
          
          // Override the end method to capture the response
          res.end = function(data) {
            // Log the request and response
            logAPNCallback(req, res, startTime, reqBody, res.statusCode, data ? data.toString() : '');
            
            // Call the original end method
            return originalEnd.apply(this, arguments);
          };
          
          // Continue with normal Next.js request handling
          req.body = reqBody;
          handle(req, res, parsedUrl);
        } catch (error) {
          console.error('Custom server: Error processing request:', error);
          res.statusCode = 500;
          res.end('Internal Server Error');
        }
      });
    } else {
      // Handle all other requests normally
      handle(req, res, parsedUrl);
    }
  }).listen(3000, (err) => {
    if (err) throw err;
    console.log('> Custom server ready on http://localhost:3000');
  });
}); 