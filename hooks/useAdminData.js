import { useState, useEffect } from 'react';
import { processOrdersWithEnhancedData, calculateSalesByCurrency } from '../utils/adminOrderUtils';
import { DEFAULT_STATS, DEFAULT_LOADING_STATE } from '../constants/adminConstants';

// Custom hook for managing admin dashboard data
export const useAdminData = () => {
  const [currentOrders, setCurrentOrders] = useState([]);
  const [stats, setStats] = useState(DEFAULT_STATS);
  const [loading, setLoading] = useState(DEFAULT_LOADING_STATE);

  // Fetch pending documents for stats
  const fetchPendingDocumentsCount = async () => {
    try {
      setLoading(prev => ({ ...prev, documents: true }));
      const response = await fetch('/api/documents/pending');
      
      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.documents) {
        // Update stats with pending approvals count
        setStats(prev => ({ ...prev, pendingApprovals: result.documents.length }));
      }
    } catch (error) {
      console.error('Error fetching pending documents count:', error);
    } finally {
      setLoading(prev => ({ ...prev, documents: false }));
    }
  };

  // Fetch orders and stats
  const fetchOrdersAndStats = async () => {
    try {
      setLoading(prev => ({ ...prev, orders: true, stats: true }));
      const response = await fetch('/api/admin/orders');
      
      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Process orders data with enhanced logic for new structure
        const ordersWithEnhancedData = processOrdersWithEnhancedData(result.orders);
        
        setCurrentOrders(ordersWithEnhancedData);
        
        // Calculate enhanced stats
        const paidOrders = ordersWithEnhancedData.filter(order => order.paymentStatus === 'paid');
        const unpaidOrders = ordersWithEnhancedData.filter(order => order.paymentStatus === 'not_paid');
        const expiredOrders = ordersWithEnhancedData.filter(order => order.isExpired);
        
        // Calculate total sales by currency
        const salesByCurrency = calculateSalesByCurrency(paidOrders);
        
        // Update stats with enhanced data
        setStats(prev => ({
          ...prev,
          totalSales: salesByCurrency, // Now an object with currency breakdowns
          totalOrders: ordersWithEnhancedData.length,
          paidOrders: paidOrders.length,
          unpaidOrders: unpaidOrders.length,
          expiredOrders: expiredOrders.length
        }));
      } else {
        console.error('Failed to fetch orders:', result.error);
        setCurrentOrders([]);
      }
    } catch (error) {
      console.error('Error fetching orders and stats:', error);
      setCurrentOrders([]);
    } finally {
      setLoading(prev => ({ ...prev, orders: false, stats: false }));
    }
  };

  useEffect(() => {
    fetchPendingDocumentsCount();
    fetchOrdersAndStats();
  }, []);

  return {
    currentOrders,
    stats,
    loading,
    refetchData: () => {
      fetchPendingDocumentsCount();
      fetchOrdersAndStats();
    }
  };
};
