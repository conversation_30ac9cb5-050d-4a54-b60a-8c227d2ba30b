<!DOCTYPE html>
<html>
<head>
    <title>Check Customer Auth</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .key {
            font-weight: bold;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button.danger {
            background-color: #f44336;
        }
        input {
            padding: 8px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Customer Authentication Status</h1>
    
    <div class="card">
        <h2>Current Authentication State</h2>
        <div id="auth-status"></div>
    </div>
    
    <div class="card">
        <h2>Cart Information</h2>
        <div id="cart-info"></div>
    </div>
    
    <div class="card">
        <h2>Set Customer ID</h2>
        <p>Use this to manually set a customer ID (simulating login):</p>
        <input type="text" id="customer-id" placeholder="Enter customer ID">
        <input type="text" id="customer-name" placeholder="Enter name (optional)">
        <input type="text" id="customer-email" placeholder="Enter email (optional)">
        <button onclick="setCustomerId()">Set Customer ID</button>
    </div>
    
    <div class="card">
        <h2>Actions</h2>
        <button onclick="refreshInfo()">Refresh Information</button>
        <button class="danger" onclick="clearCustomerId()">Clear Customer ID</button>
        <button class="danger" onclick="clearAllLocalStorage()">Clear All LocalStorage</button>
    </div>
    
    <script>
        // Function to display authentication status
        function refreshInfo() {
            // Auth status
            const authStatus = document.getElementById('auth-status');
            authStatus.innerHTML = '';
            
            const customerId = localStorage.getItem('SHOPME_CUSTOMER_ID');
            const userId = localStorage.getItem('SHOPME_USER_ID');
            
            let customerInfo = null;
            try {
                const infoString = localStorage.getItem('SHOPME_CUSTOMER_INFO');
                if (infoString) {
                    customerInfo = JSON.parse(infoString);
                }
            } catch (e) {
                console.error('Error parsing customer info:', e);
            }
            
            addInfoItem(authStatus, 'Customer ID', customerId || 'Not set');
            addInfoItem(authStatus, 'User ID', userId || 'Not set');
            
            if (customerInfo) {
                addInfoItem(authStatus, 'Customer Name', customerInfo.name || 'Not set');
                addInfoItem(authStatus, 'Customer Email', customerInfo.email || 'Not set');
                addInfoItem(authStatus, 'Customer Phone', customerInfo.phone || 'Not set');
            }
            
            // Cart info
            const cartInfo = document.getElementById('cart-info');
            cartInfo.innerHTML = '';
            
            try {
                const cartData = localStorage.getItem('SHOPME_');
                if (cartData) {
                    const cart = JSON.parse(cartData);
                    addInfoItem(cartInfo, 'Number of Items', cart.numberOfItemsInCart || 0);
                    addInfoItem(cartInfo, 'Total', cart.total || 0);
                    addInfoItem(cartInfo, 'Last Updated', cart.lastUpdated || 'Never');
                    
                    if (cart.cart && cart.cart.length > 0) {
                        const itemsList = document.createElement('div');
                        itemsList.innerHTML = '<span class="key">Items:</span>';
                        const ul = document.createElement('ul');
                        
                        cart.cart.forEach(item => {
                            const li = document.createElement('li');
                            li.textContent = `${item.name} (${item.quantity}x) - ${item.currency}${item.price}`;
                            ul.appendChild(li);
                        });
                        
                        itemsList.appendChild(ul);
                        cartInfo.appendChild(itemsList);
                    } else {
                        addInfoItem(cartInfo, 'Items', 'No items in cart');
                    }
                } else {
                    addInfoItem(cartInfo, 'Cart', 'No cart data found');
                }
            } catch (e) {
                console.error('Error parsing cart data:', e);
                addInfoItem(cartInfo, 'Error', 'Failed to parse cart data');
            }
        }
        
        // Helper function to add info items
        function addInfoItem(container, key, value) {
            const item = document.createElement('div');
            item.className = 'info-item';
            item.innerHTML = `<span class="key">${key}:</span> ${value}`;
            container.appendChild(item);
        }
        
        // Function to set customer ID
        function setCustomerId() {
            const customerId = document.getElementById('customer-id').value.trim();
            if (!customerId) {
                alert('Please enter a customer ID');
                return;
            }
            
            // Get optional info
            const name = document.getElementById('customer-name').value.trim();
            const email = document.getElementById('customer-email').value.trim();
            
            // Set customer ID
            localStorage.setItem('SHOPME_CUSTOMER_ID', customerId);
            
            // Create customer info object if we have any additional info
            if (name || email) {
                const customerInfo = { name, email };
                localStorage.setItem('SHOPME_CUSTOMER_INFO', JSON.stringify(customerInfo));
            }
            
            // Trigger the customer-identified event
            const previousId = localStorage.getItem('SHOPME_USER_ID');
            if (previousId && previousId !== customerId) {
                window.dispatchEvent(new CustomEvent('customer-identified', { 
                    detail: { customerId, previousId }
                }));
            }
            
            refreshInfo();
            alert('Customer ID set successfully!');
        }
        
        // Function to clear customer ID
        function clearCustomerId() {
            localStorage.removeItem('SHOPME_CUSTOMER_ID');
            localStorage.removeItem('SHOPME_CUSTOMER_INFO');
            refreshInfo();
            alert('Customer ID cleared!');
        }
        
        // Function to clear all localStorage
        function clearAllLocalStorage() {
            if (confirm('Are you sure you want to clear all localStorage data? This will remove your cart and all settings.')) {
                localStorage.clear();
                refreshInfo();
                alert('All localStorage data cleared!');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', refreshInfo);
    </script>
</body>
</html> 