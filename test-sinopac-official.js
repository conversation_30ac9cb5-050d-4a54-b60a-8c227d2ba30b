const axios = require('axios');
const { AES, enc, HmacSHA256, mode, pad } = require('crypto-js');

// Official SinoPAC API credentials from sandbox documentation
const SINOPAC_API = {
  MERCHANT_ID: 'NA0511_001', // Official format with underscore
  A1_KEY: 'F342DAABD58249D8',
  A2_KEY: 'D3E28D4E9A4E4EE2',
  B1_KEY: 'C61852BEBDA44676',
  B2_KEY: '1BD9BDB007E34418',
  X_KEY: 'b5e6986d-8636-4aa0-8c93-441ad14b2098',
  SANDBOX_ENDPOINT: 'https://sandbox.sinopac.com/QPay.WebAPI/api'
};

// Format date for SinoPAC API (Taiwan timezone)
const formatSinoPacDate = (date) => {
  const taiwanTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
  const year = taiwanTime.getUTCFullYear();
  const month = String(taiwanTime.getUTCMonth() + 1).padStart(2, '0');
  const day = String(taiwanTime.getUTCDate()).padStart(2, '0');
  const hours = String(taiwanTime.getUTCHours()).padStart(2, '0');
  const minutes = String(taiwanTime.getUTCMinutes()).padStart(2, '0');
  const seconds = String(taiwanTime.getUTCSeconds()).padStart(2, '0');
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
};

// Encryption function
const encryptSinoPacData = (data) => {
  const jsonData = typeof data === 'object' ? 
    JSON.stringify(data, Object.keys(data).sort()) : 
    data;
  
  console.log('Raw data to encrypt:', jsonData);
  
  const key = enc.Hex.parse(SINOPAC_API.A1_KEY);
  const iv = enc.Hex.parse(SINOPAC_API.A2_KEY);
  
  const encrypted = AES.encrypt(jsonData, key, { 
    iv: iv,
    mode: mode.CBC,
    padding: pad.Pkcs7
  });
  
  const encryptedBase64 = encrypted.toString().replace(/=+$/, '');
  console.log('Encrypted data:', encryptedBase64);
  return encryptedBase64;
};

// HMAC generation
const generateHmac = (data) => {
  console.log('Generating HMAC for:', data);
  const hmac = HmacSHA256(data, SINOPAC_API.B1_KEY);
  const hmacHex = hmac.toString(enc.Hex).toUpperCase();
  console.log('Generated HMAC:', hmacHex);
  return hmacHex;
};

// Test with official credentials
async function testOfficialSinoPacCredentials() {
  const timestamp = formatSinoPacDate(new Date());
  
  console.log('=== TESTING WITH OFFICIAL SINOPAC CREDENTIALS ===');
  console.log('Merchant ID:', SINOPAC_API.MERCHANT_ID);
  console.log('Timestamp:', timestamp);
  console.log('API Endpoint:', SINOPAC_API.SANDBOX_ENDPOINT);
  console.log('Portal Login: https://sandbox.sinopac.com/DSF.Portal/ (admin/admin)');
  console.log('');
  
  try {
    // Create test payload
    const payload = {
      Amount: 150,
      CurrencyID: "TWD",
      CustomerAddress: "Test Address, Taipei",
      CustomerEmail: "<EMAIL>",
      CustomerName: "Test Customer",
      CustomerPhone: "0912345678",
      DueDate: "",
      Memo: "Official test order",
      OrderDesc: "Testing with official credentials",
      OrderNo: `MAG-OFFICIAL-${Date.now()}`,
      PayType: "A", // ATM transfer
      PrdtName: "Test Product Official",
      ReturnURL: "http://localhost:3000/api/payment/sinopac-callback",
      ShopNo: SINOPAC_API.MERCHANT_ID,
      ShowType: "1",
      TimeStamp: timestamp
    };
    
    console.log('Payload:', JSON.stringify(payload, null, 2));
    
    // Encrypt payload
    const encryptedData = encryptSinoPacData(payload);
    
    // Create request data
    const baseRequestData = {
      APIService: "Order",
      EncryptData: encryptedData,
      MerchantID: SINOPAC_API.MERCHANT_ID,
      Message: "Payment request",
      Version: "1.0.0"
    };
    
    // Generate HMAC from encrypted data
    const hmacData = generateHmac(encryptedData);
    
    const requestData = {
      ...baseRequestData,
      HashData: hmacData
    };
    
    console.log('Final request data:', JSON.stringify(requestData, null, 2));
    console.log('');
    console.log('Making API request to SinoPAC...');
    
    const response = await axios.post(
      `${SINOPAC_API.SANDBOX_ENDPOINT}/Order`,
      requestData,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-KeyId': SINOPAC_API.X_KEY,
          'Accept': 'application/json'
        },
        timeout: 30000
      }
    );
    
    console.log('');
    console.log('🎉 SUCCESS! SinoPAC API Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.Status === 'S') {
      console.log('');
      console.log('✅ Payment order created successfully!');
      if (response.data.Result?.ATMInfo) {
        console.log('Bank Code:', response.data.Result.ATMInfo.BankCode);
        console.log('Account Number:', response.data.Result.ATMInfo.AccountNo);
      }
      if (response.data.Result?.PaymentURL) {
        console.log('Payment URL:', response.data.Result.PaymentURL);
      }
    }
    
  } catch (error) {
    console.log('');
    console.log('❌ ERROR occurred:');
    console.log('Message:', error.message);
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
      
      // Check for specific error codes
      if (error.response.data?.TSResultCode) {
        console.log('');
        console.log('SinoPAC Error Code:', error.response.data.TSResultCode);
        console.log('SinoPAC Error Message:', error.response.data.TSResultMsg);
      }
    } else {
      console.log('Network/Request Error:', error.message);
    }
  }
}

// Run the test
testOfficialSinoPacCredentials(); 