const axios = require('axios');
const { AES, enc, HmacSHA256, mode, pad } = require('crypto-js');

// SinoPAC API credentials - debug version
const SINOPAC_API = {
  MERCHANT_ID: 'NA0511001',
  A1_KEY: 'F342DAABD58249D8',
  A2_KEY: 'D3E28D4E9A4E4EE2',
  B1_KEY: 'C61852BEBDA44676',
  B2_KEY: '1BD9BDB007E34418',
  X_KEY: 'b5e6986d-8636-4aa0-8c93-441ad14b2098',
  SANDBOX_ENDPOINT: 'https://sandbox.sinopac.com/QPay.WebAPI/api'
};

// Test different credential formats
const TEST_CREDENTIALS = [
  {
    name: 'Current Format',
    merchantId: 'NA0511001'
  },
  {
    name: 'With Underscore',
    merchantId: 'NA0511_001'
  },
  {
    name: 'Uppercase',
    merchantId: 'NA0511001'.toUpperCase()
  }
];

// Format date for SinoPAC API
const formatSinoPacDate = (date) => {
  const taiwanTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
  const year = taiwanTime.getUTCFullYear();
  const month = String(taiwanTime.getUTCMonth() + 1).padStart(2, '0');
  const day = String(taiwanTime.getUTCDate()).padStart(2, '0');
  const hours = String(taiwanTime.getUTCHours()).padStart(2, '0');
  const minutes = String(taiwanTime.getUTCMinutes()).padStart(2, '0');
  const seconds = String(taiwanTime.getUTCSeconds()).padStart(2, '0');
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
};

// Encryption function
const encryptSinoPacData = (data) => {
  const jsonData = typeof data === 'object' ? 
    JSON.stringify(data, Object.keys(data).sort()) : 
    data;
  
  const key = enc.Hex.parse(SINOPAC_API.A1_KEY);
  const iv = enc.Hex.parse(SINOPAC_API.A2_KEY);
  
  const encrypted = AES.encrypt(jsonData, key, { 
    iv: iv,
    mode: mode.CBC,
    padding: pad.Pkcs7
  });
  
  return encrypted.toString().replace(/=+$/, '');
};

// HMAC generation
const generateHmac = (data) => {
  const hmac = HmacSHA256(data, SINOPAC_API.B1_KEY);
  return hmac.toString(enc.Hex).toUpperCase();
};

// Test function
async function testSinoPacCredentials() {
  const timestamp = formatSinoPacDate(new Date());
  
  console.log('=== SINOPAC API CREDENTIAL TEST ===');
  console.log('Timestamp:', timestamp);
  console.log('API Endpoint:', SINOPAC_API.SANDBOX_ENDPOINT);
  console.log('');
  
  for (const cred of TEST_CREDENTIALS) {
    console.log(`--- Testing: ${cred.name} ---`);
    console.log(`Merchant ID: ${cred.merchantId}`);
    
    try {
      // Create test payload
      const payload = {
        Amount: 100,
        CurrencyID: "TWD",
        CustomerAddress: "Test Address",
        CustomerEmail: "<EMAIL>",
        CustomerName: "Test Customer",
        CustomerPhone: "0912345678",
        DueDate: "",
        Memo: "Test order",
        OrderDesc: "Test Order",
        OrderNo: `TEST-${Date.now()}`,
        PayType: "A",
        PrdtName: "Test Product",
        ReturnURL: "http://localhost:3000/api/payment/sinopac-callback",
        ShopNo: cred.merchantId,
        ShowType: "1",
        TimeStamp: timestamp
      };
      
      console.log('Payload:', JSON.stringify(payload, null, 2));
      
      // Encrypt payload
      const encryptedData = encryptSinoPacData(payload);
      console.log('Encrypted length:', encryptedData.length);
      
      // Create request data
      const baseRequestData = {
        APIService: "Order",
        EncryptData: encryptedData,
        MerchantID: cred.merchantId,
        Message: "Payment request",
        Version: "1.0.0"
      };
      
      // Test different HMAC approaches
      const hmac1 = generateHmac(encryptedData);
      const hmac2 = generateHmac(JSON.stringify(baseRequestData, Object.keys(baseRequestData).sort()));
      const hmac3 = generateHmac(Object.keys(baseRequestData).sort().map(k => `${k}=${baseRequestData[k]}`).join('&'));
      
      console.log('HMAC 1 (encrypted):', hmac1);
      console.log('HMAC 2 (JSON):', hmac2);
      console.log('HMAC 3 (string):', hmac3);
      
      // Test with HMAC 1
      const requestData = {
        ...baseRequestData,
        HashData: hmac1
      };
      
      console.log('Making API request...');
      const response = await axios.post(
        `${SINOPAC_API.SANDBOX_ENDPOINT}/Order`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-KeyId': SINOPAC_API.X_KEY,
            'Accept': 'application/json'
          },
          timeout: 30000
        }
      );
      
      console.log('SUCCESS!', response.data);
      
    } catch (error) {
      console.log('ERROR:', {
        status: error.response?.status,
        data: error.response?.data
      });
    }
    
    console.log('');
  }
}

testSinoPacCredentials(); 