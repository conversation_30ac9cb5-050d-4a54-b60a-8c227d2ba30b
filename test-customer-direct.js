// A minimalistic test script
const axios = require('axios');

// API Key
const API_KEY = 'uX6HsVoPhmapndxrUhDn';

// Base URL
const BASE_URL = 'http://localhost:3000';

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': API_KEY
  }
});

// Main function
async function main() {
  console.log('Starting minimal API test');
  
  try {
    // Test customer creation
    const customerData = {
      name: 'Test Customer',
      email: `test${Date.now()}@example.com`,
      password: 'testPassword123',
      phone: '************'
    };
    
    console.log('Creating customer...');
    const response = await api.post('/api/customers', customerData);
    
    console.log('Response status:', response.status);
    console.log('Raw response data:', response.data);
    
    // Check if customerId exists
    if (response.data && response.data.customerId) {
      console.log('Found customerId:', response.data.customerId);
      
      // Test fetching the customer using the new endpoint
      console.log('Fetching customer using the query parameter endpoint...');
      try {
        const getResponseAlt = await api.get(`/api/customer?id=${response.data.customerId}`);
        console.log('Get response status (alt):', getResponseAlt.status);
        console.log('Get response data (alt):', getResponseAlt.data);
      } catch (error) {
        console.error('Error fetching customer with alt endpoint:', error.message);
        if (error.response) {
          console.error(`Status: ${error.response.status}`);
          console.error('Response data:', error.response.data);
        }
      }
      
      // Try to get the customer with the ID
      console.log('Fetching customer using the [id] endpoint...');
      try {
        const getResponse = await api.get(`/api/customers/${response.data.customerId}`);
        console.log('Get response status:', getResponse.status);
        console.log('Get response data:', getResponse.data);
      } catch (error) {
        console.error('Error fetching customer:', error.message);
        if (error.response) {
          console.error(`Status: ${error.response.status}`);
          console.error('Response data:', error.response.data);
        }
      }
    } else {
      console.error('No customerId found in response');
    }
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the main function
main().catch(console.error); 