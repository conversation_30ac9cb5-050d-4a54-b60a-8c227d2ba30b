// Test script for enhanced inventory management system
const {
  getCurrentInventoryCount,
  getInventoryType,
  getInventoryItems,
  filterInventoryItems,
  sortInventoryItems,
  paginateInventoryItems,
  getInventoryStatistics,
  parseBulkPasteData,
  convertInventoryType
} = require('./utils/inventoryManager.js');
const inventoryData = require('./utils/inventory.json');

console.log('🧪 Testing Enhanced Inventory Management System\n');

// Test 1: Basic inventory functions
console.log('📊 Test 1: Basic Inventory Functions');
const sampleItem = inventoryData[0];
console.log('Sample item:', sampleItem.name);
console.log('- Count:', getCurrentInventoryCount(sampleItem));
console.log('- Type:', getInventoryType(sampleItem));
console.log('- Items:', getInventoryItems(sampleItem).length, 'items\n');

// Test 2: Filtering
console.log('🔍 Test 2: Filtering Inventory');
const separateTypeItems = filterInventoryItems(inventoryData, { type: 'separate' });
console.log('- Separate type items:', separateTypeItems.length);

const inStockItems = filterInventoryItems(inventoryData, { stockStatus: 'in_stock' });
console.log('- In stock items:', inStockItems.length);

const searchResults = filterInventoryItems(inventoryData, { search: 'mobile' });
console.log('- Search "mobile" results:', searchResults.length, '\n');

// Test 3: Sorting
console.log('📈 Test 3: Sorting Inventory');
const sortedByPrice = sortInventoryItems(inventoryData.slice(0, 10), 'price', 'desc');
console.log('- Top 3 by price:');
sortedByPrice.slice(0, 3).forEach((item, index) => {
  console.log(`  ${index + 1}. ${item.name} - $${item.price}`);
});
console.log();

// Test 4: Pagination
console.log('📄 Test 4: Pagination');
const paginatedResult = paginateInventoryItems(inventoryData, 1, 5);
console.log('- Page 1 (5 items):');
console.log('  - Items returned:', paginatedResult.items.length);
console.log('  - Total pages:', paginatedResult.pagination.totalPages);
console.log('  - Total items:', paginatedResult.pagination.total, '\n');

// Test 5: Statistics
console.log('📊 Test 5: Inventory Statistics');
const stats = getInventoryStatistics(inventoryData);
console.log('- Total items:', stats.totalItems);
console.log('- Total inventory value:', `$${stats.totalValue.toFixed(2)}`);
console.log('- By type:', stats.byType);
console.log('- By status:', stats.byStatus, '\n');

// Test 6: Bulk paste parsing
console.log('📝 Test 6: Bulk Paste Data Parsing');
const bulkData = `Item 1
Item 2
Item 3`;
const parsedItems = parseBulkPasteData(bulkData);
console.log('- Parsed items from bulk paste:', parsedItems.length);
parsedItems.forEach((item, index) => {
  console.log(`  ${index + 1}. ${item.value} (ID: ${item.id})`);
});
console.log();

// Test 7: Type conversion
console.log('🔄 Test 7: Inventory Type Conversion');
const testItem = {
  inventory: {
    type: 'same',
    count: 10
  }
};
console.log('- Original type:', testItem.inventory.type);
const convertedInventory = convertInventoryType(testItem, 'separate');
console.log('- Converted type:', convertedInventory.type);
console.log('- Items array length:', convertedInventory.items.length, '\n');

console.log('✅ All tests completed successfully!');
console.log('\n🎉 Enhanced Inventory Management System is ready to use!');
console.log('\nAvailable API endpoints:');
console.log('- GET /api/inventory - List all items with filtering, sorting, pagination');
console.log('- GET /api/inventory?stats=true - Get inventory statistics');
console.log('- GET /api/inventory/manage - Enhanced inventory management');
console.log('- POST /api/inventory/manage - Bulk operations and advanced management');
console.log('\nExample API calls:');
console.log('- GET /api/inventory?type=separate&sortBy=price&sortOrder=desc&page=1&limit=10');
console.log('- GET /api/inventory?search=mobile&stockStatus=in_stock');
console.log('- GET /api/inventory?category=electronics&minPrice=10&maxPrice=100');