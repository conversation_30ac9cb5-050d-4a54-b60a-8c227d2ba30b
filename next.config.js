/** @type {import('next').NextConfig} */
const nextConfig = {
  i18n: {
    defaultLocale: 'vi',
    locales: ['en', 'vi'],
    localeDetection: false,
  },
  // Removed fallbackLng property as it's not recognized by Next.js
  // Removed "output: 'export'" to enable proper JS file generation
  images: {
    domains: [
      'cdn.jsdelivr.net',
      'barcode.tec-it.com',
      'api.qrserver.com'
    ],
    unoptimized: false, // Changed to false for better performance
  },
  headers: async () => {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              default-src 'self';
              img-src 'self' data: blob: https://*.stripe.com https://cdn-icons-png.flaticon.com https://cdn.jsdelivr.net https://barcode.tec-it.com https://api.qrserver.com https://*.basemaps.cartocdn.com;
              style-src 'self' 'unsafe-inline' https: data:;
              script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com https://checkout.stripe.com https://va.vercel-scripts.com https://sp.zalo.me https://za.zdn.vn http://za.zdn.vn;
              connect-src 'self' https://barcode.tec-it.com https://api.qrserver.com https://api.stripe.com;
              frame-src 'self' https://js.stripe.com https://hooks.stripe.com https://checkout.stripe.com https://m.stripe.network;
            `.replace(/\s+/g, ' ').trim()
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS'
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-Requested-With, Content-Type, Authorization'
          }
        ]
      },
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
        ],
      },
    ]
  },
  // Moved PWA config to a separate variable since it's not a recognized Next.js config option
  // We'll use it with next-pwa package if needed
  // Added to improve build performance for large pages
  experimental: {
    largePageDataBytes: 800 * 1024, // Increased from default 128KB to 800KB
    serverComponentsExternalPackages: ['@prisma/client']
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
  crossOrigin: 'anonymous',
};

module.exports = nextConfig;
