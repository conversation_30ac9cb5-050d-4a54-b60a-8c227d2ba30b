// Image utility functions for handling product images with fallbacks

/**
 * Get product image with fallback handling
 * @param {string|string[]} image - Image URL or array of image URLs
 * @param {string} sku - Product SKU for fallback image selection
 * @param {string} productName - Product name for alt text
 * @returns {string} - Image URL to use
 */
export const getProductImage = (image, sku = '', productName = '') => {
  // Handle array of images - use first valid one
  if (Array.isArray(image) && image.length > 0) {
    const firstImage = image[0];
    if (firstImage && typeof firstImage === 'string' && firstImage.trim()) {
      // Check if it's a CDN URL that might be failing
      if (firstImage.includes('cdn.jsdelivr.net') || firstImage.includes('abncharts')) {
        // Use local fallback for CDN URLs that are likely to fail
        return getLocalFallbackImage(sku, productName);
      }
      return firstImage;
    }
  }
  
  // Handle single image string
  if (typeof image === 'string' && image.trim()) {
    // Check if it's a CDN URL that might be failing
    if (image.includes('cdn.jsdelivr.net') || image.includes('abncharts')) {
      // Use local fallback for CDN URLs that are likely to fail
      return getLocalFallbackImage(sku, productName);
    }
    return image;
  }
  
  // No valid image provided, use fallback
  return getLocalFallbackImage(sku, productName);
};

/**
 * Get local fallback image based on SKU and product type
 * @param {string} sku - Product SKU
 * @param {string} productName - Product name
 * @returns {string} - Local fallback image path
 */
export const getLocalFallbackImage = (sku = '', productName = '') => {
  const skuLower = sku.toLowerCase();
  const nameLower = productName.toLowerCase();
  
  // Check for specific SIM card types
  if (skuLower.includes('taiwan.yearsim')) {
    if (skuLower.includes('.ok.') || nameLower.includes('ok')) {
      return '/images/products/ok-sim-card.webp';
    }
    if (skuLower.includes('.if.') || nameLower.includes('if')) {
      return '/images/products/if-sim-card.webp';
    }
    if (skuLower.includes('.chunghoa.') || skuLower.includes('.cw.') || nameLower.includes('chunghoa') || nameLower.includes('中華')) {
      return '/images/products/chunghoa-sim-card.webp';
    }
    // Default Taiwan SIM
    return '/images/products/sim-card-default.png';
  }
  
  // Check for general product types
  if (skuLower.includes('sim') || nameLower.includes('sim') || nameLower.includes('mạng')) {
    return '/images/products/sim-card-default.png';
  }
  
  if (skuLower.includes('topup') || nameLower.includes('topup') || nameLower.includes('nạp tiền')) {
    return '/images/products/sim-card-default.png';
  }
  
  // Default fallback
  return '/images/products/default-product.png';
};

/**
 * Handle image loading error by setting fallback image
 * @param {Event} event - Image error event
 * @param {string} sku - Product SKU
 * @param {string} productName - Product name
 */
export const handleImageError = (event, sku = '', productName = '') => {
  const img = event.target;
  if (img && img.src !== getLocalFallbackImage(sku, productName)) {
    img.src = getLocalFallbackImage(sku, productName);
  }
};

/**
 * Component for rendering product image with automatic fallback
 * @param {Object} props - Component props
 * @param {string|string[]} props.image - Image URL or array
 * @param {string} props.sku - Product SKU
 * @param {string} props.name - Product name
 * @param {string} props.alt - Alt text (optional, defaults to product name)
 * @param {string} props.className - CSS classes
 * @param {Object} props.style - Inline styles
 * @returns {JSX.Element} - Image element with fallback handling
 */
export const ProductImage = ({ image, sku = '', name = '', alt, className = '', style = {}, ...props }) => {
  const imageSrc = getProductImage(image, sku, name);
  const altText = alt || name || 'Product image';
  
  return (
    <img
      src={imageSrc}
      alt={altText}
      className={className}
      style={style}
      onError={(e) => handleImageError(e, sku, name)}
      {...props}
    />
  );
};

export default {
  getProductImage,
  getLocalFallbackImage,
  handleImageError,
  ProductImage
}; 