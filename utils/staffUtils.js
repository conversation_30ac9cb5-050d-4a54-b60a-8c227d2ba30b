import fs from 'fs';
import path from 'path';

const staffFilePath = path.join(process.cwd(), 'data', 'staff.json');

// Get all staff members
export const getAllStaff = () => {
  try {
    const fileData = fs.readFileSync(staffFilePath, 'utf8');
    return JSON.parse(fileData);
  } catch (error) {
    if (error.code === 'ENOENT') {
      // If file doesn't exist, return empty array
      return [];
    }
    throw error;
  }
};

// Get staff by ID
export const getStaffById = (id) => {
  const staff = getAllStaff();
  return staff.find(member => member.id === id);
};

// Get staff by store ID
export const getStaffByStore = (storeId) => {
  const staff = getAllStaff();
  return staff.filter(member => member.storeId === storeId);
};

// Save staff data to JSON file
export const saveStaff = (staffData) => {
  fs.writeFileSync(staffFilePath, JSON.stringify(staffData, null, 2), 'utf8');
  return true;
};

// Add new staff member
export const addStaff = (staffData) => {
  const staff = getAllStaff();
  
  // Generate a unique ID if not provided
  if (!staffData.id) {
    staffData.id = `staff_${Date.now()}`;
  }
  
  // Set timestamps
  const now = new Date().toISOString();
  staffData.createdAt = now;
  staffData.updatedAt = now;
  
  // Add the new staff member
  staff.push(staffData);
  
  // Save to file
  saveStaff(staff);
  
  return staffData;
};

// Update existing staff member
export const updateStaff = (id, updateData) => {
  const staff = getAllStaff();
  const index = staff.findIndex(member => member.id === id);
  
  if (index === -1) {
    return null;
  }
  
  // Update the staff member
  updateData.updatedAt = new Date().toISOString();
  staff[index] = { ...staff[index], ...updateData };
  
  // Save to file
  saveStaff(staff);
  
  return staff[index];
};

// Delete staff member
export const deleteStaff = (id) => {
  const staff = getAllStaff();
  const index = staff.findIndex(member => member.id === id);
  
  if (index === -1) {
    return false;
  }
  
  // Remove the staff member
  staff.splice(index, 1);
  
  // Save to file
  saveStaff(staff);
  
  return true;
}; 