// Checkout Management Module
const CheckoutManager = {
  // Methods map
  methods: {
    1: "CARD", // For products with CARD category
    2: "SIM",  // For products with SIM category
    3: "TRAVELSIM" // For products with Travelsim category
  },

  // Get checkout method based on product category
  getMethodByCategory: function(categories) {
    if (!categories || !Array.isArray(categories)) return null;
    
    const categoryMap = {
      "CARD": 1,
      "SIM": 2,
      "TRAVELSIM": 3
    };
    
    // Find matching category
    for (const category of categories) {
      const upperCategory = category.toUpperCase();
      if (categoryMap[upperCategory]) {
        return categoryMap[upperCategory];
      }
    }
    
    return null; // No matching method found
  },
  
  // Process checkout with specified method
  processCheckout: function(product, methodParam = null) {
    // Determine method to use
    let method = methodParam;
    
    // If no method specified, determine from product categories
    if (!method && product.categories) {
      method = this.getMethodByCategory(product.categories);
    }
    
    // Default to method 1 if still not determined
    method = method || 1;
    
    // Call appropriate checkout processor
    switch (parseInt(method)) {
      case 1:
        return this.processCardCheckout(product);
      case 2:
        return this.processSimCheckout(product);
      case 3:
        return this.processTravelSimCheckout(product);
      default:
        throw new Error("Invalid checkout method");
    }
  },
  
  // Method 1: CARD checkout process
  processCardCheckout: function(product) {
    return {
      type: "CARD",
      steps: [
        { id: "pin", title: "XUẤT MÃ PIN/NHẬP SDT NẠP TỰ ĐỘNG", required: true },
        { id: "account", title: "TÀI KHOẢN KHÁCH HÀNG", required: true },
        { id: "payment", title: "THANH TOÁN", paymentChannels: ["TAIWAN", "VIETNAM"], required: true }
      ],
      config: {
        apiConnected: true,
        autoCheck: true,
        paymentReminder: true,
        pinInventoryAlert: true,
        showPreviousTransactions: true,
        showLoyaltyPoints: true,
        shippingAvailable: false
      }
    };
  },
  
  // Method 2: SIM checkout process
  processSimCheckout: function(product) {
    return {
      type: "SIM",
      steps: [
        { id: "recipient", title: "THÔNG TIN NGƯỜI NHẬN", required: true },
        { id: "documents", title: "CUNG CẤP THÔNG TIN ĐẶT SIM", required: true, 
          documents: ["hộ chiếu", "thẻ cư trú đài loan", "thẻ khám bệnh", "VISA"] },
        { id: "delivery", title: "HÌNH THỨC NHẬN HÀNG", required: true, 
          options: ["cửa hàng tiện ích", "tại nhà"] },
        { id: "activation", title: "KÍCH HOẠT SIM", required: false }
      ],
      config: {
        requiresVerification: true,
        trackingEnabled: true,
        activationReminder: true,
        expirationReminder: true,
        shippingAvailable: true,
        paymentOptions: {
          "TAIWAN": ["COD"],
          "VIETNAM": ["PREPAID"]
        }
      }
    };
  },
  
  // Method 3: TRAVELSIM checkout process
  processTravelSimCheckout: function(product) {
    return {
      type: "TRAVELSIM",
      steps: [
        { id: "destination", title: "Chọn điểm đến", required: true },
        { id: "simType", title: "Chọn loại SIM phù hợp", required: true,
          options: ["SIM vật lý", "eSIM"] },
        { id: "duration", title: "Chọn thời gian sử dụng & dung lượng", required: true },
        { id: "orderInfo", title: "Nhập thông tin đặt hàng", required: true },
        { id: "payment", title: "Thanh toán an toàn", required: true,
          methods: ["thẻ ngân hàng", "ví điện tử", "thẻ tín dụng", "chuyển khoản"] },
        { id: "activation", title: "Kích hoạt SIM và sử dụng", required: false }
      ],
      config: {
        multiCountrySupport: true,
        esimSupport: true,
        automaticEmailConfirmation: true,
        shippingAvailable: true,
        paymentOptions: {
          "TAIWAN": ["COD"],
          "VIETNAM": ["COD", "PREPAID"]
        }
      }
    };
  },
  
  // Link a product to a specific checkout method
  linkProductToMethod: function(productSku, methodId) {
    // This would typically update a database or configuration
    // For this implementation, we'll return a configuration object
    return {
      sku: productSku,
      checkoutMethod: methodId,
      linked: true,
      timestamp: new Date().toISOString()
    };
  }
};

module.exports = CheckoutManager; 