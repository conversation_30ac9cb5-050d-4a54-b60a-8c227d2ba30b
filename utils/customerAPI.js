import axios from 'axios';
import { getCustomerInfo, getCustomerId } from './customerAuth';

// Base API URL - replace with your actual API URL in production
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

/**
 * Update customer profile information
 * @param {Object} profileData - The customer profile data to update
 * @returns {Promise} - API response
 */
export const updateCustomerProfile = async (profileData) => {
  const customerId = getCustomerId();
  if (!customerId) {
    throw new Error('No customer ID available');
  }

  try {
    const response = await fetch('/api/customer/profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customerId,
        ...profileData
      })
    });

    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating customer profile:', error);
    throw error;
  }
};

/**
 * Update order information
 * @param {string} orderId - The order ID
 * @param {Object} orderData - The order data to update
 * @returns {Promise} - API response
 */
export const updateOrderInformation = async (orderId, orderData) => {
  const customerId = getCustomerId();
  if (!customerId) {
    throw new Error('No customer ID available');
  }

  // Ensure document status is set to pending_approval if documents are being updated
  if (orderData.documentsUploaded) {
    if (!orderData.documentStatus) {
      orderData.documentStatus = {};
    }
    
    // Set default status for any document types that don't have a status
    ['idCard', 'photo', 'proofOfResidence'].forEach(docType => {
      if (orderData.documentStatus[docType] === undefined) {
        orderData.documentStatus[docType] = 'pending_approval';
      }
    });
  }

  try {
    const response = await fetch('/api/order/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customerId,
        orderId,
        ...orderData
      })
    });

    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating order information:', error);
    throw error;
  }
};

/**
 * Upload a customer document to the server
 * @param {string} documentType - The type of document (idCard, photo, proofOfResidence)
 * @param {File} file - The file to upload
 * @param {string} orderId - Optional order ID to associate with the document
 * @returns {Promise<Object>} - Response with upload status
 */
export async function uploadCustomerDocument(documentType, file, orderId = null) {
  try {
    // Create a FormData object
    const formData = new FormData();
    
    // Add the file to the form data
    formData.append('file', file);
    
    // Add document type and order ID if provided
    formData.append('documentType', documentType);
    
    // Always set status to pending_approval for new uploads
    formData.append('status', 'pending_approval');
    
    // Get customer ID from local storage or session
    const customerData = JSON.parse(localStorage.getItem('customerData') || sessionStorage.getItem('customerData') || '{}');
    const customerId = customerData.id;
    
    if (!customerId) {
      console.error('Customer ID not found in storage');
      throw new Error('Customer ID is required for document upload');
    }
    
    // Add customer ID to the form data
    formData.append('customerId', customerId);
    
    if (orderId) {
      formData.append('orderId', orderId);
    }
    
    // Add store parameter to the URL (use a default if not available)
    const currentStore = localStorage.getItem('currentStore') || 'default';
    const uploadUrl = `/api/documents/upload?store=${currentStore}`;
    
    // Debug logging for form data
    console.log('Uploading document:', {
      documentType, 
      customerId, 
      orderId,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      store: currentStore
    });
    
    // Send the upload request
    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header with FormData, browser will set it with correct boundary
    });
    
    if (!response.ok) {
      // Try to get more detailed error message
      let errorMessage = `Upload failed with status: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // If we can't parse the error response, use the default message
      }
      
      console.error('Document upload failed:', errorMessage);
      throw new Error(errorMessage);
    }
    
    // Parse and return the response
    const result = await response.json();
    
    // Ensure the response includes the pending_approval status
    if (!result.status) {
      result.status = 'pending_approval';
    }
    
    console.log('Document upload successful:', result);
    return result;
  } catch (error) {
    console.error('Error uploading document:', error);
    throw error;
  }
}

/**
 * Get customer uploaded documents
 * @returns {Promise} - API response with list of customer documents
 */
export const getCustomerDocuments = async () => {
  const customerId = getCustomerId();
  if (!customerId) {
    throw new Error('No customer ID available');
  }

  try {
    const response = await fetch(`/api/customer/documents?customerId=${customerId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
    }

    const result = await response.json();
    
    // Ensure all documents have a status field, defaulting to pending_approval if not specified
    if (result.documentsByType) {
      Object.keys(result.documentsByType).forEach(docType => {
        if (Array.isArray(result.documentsByType[docType])) {
          result.documentsByType[docType] = result.documentsByType[docType].map(doc => {
            if (!doc.status) {
              doc.status = 'pending_approval';
            }
            return doc;
          });
        }
      });
    }
    
    return result;
  } catch (error) {
    console.error('Error getting customer documents:', error);
    throw error;
  }
};

/**
 * Update document approval status
 * @param {string} documentId - The document ID
 * @param {string} status - The new status ('approved', 'rejected', 'pending_approval')
 * @param {string} adminId - The admin ID who performed the action
 * @returns {Promise<Object>} - Response with update status
 */
export const updateDocumentStatus = async (documentId, status, adminId) => {
  try {
    const response = await fetch('/api/documents/status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        documentId,
        status,
        adminId,
        updatedAt: new Date().toISOString()
      })
    });
    
    if (!response.ok) {
      throw new Error(`Update failed with status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error updating document status:', error);
    throw error;
  }
};

/**
 * Get all pending approval documents
 * @returns {Promise<Object>} - Response with pending documents
 */
export const getPendingDocuments = async () => {
  try {
    const response = await fetch('/api/documents/pending', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch pending documents: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching pending documents:', error);
    throw error;
  }
}; 