const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

/**
 * Enhanced Inventory Management Utilities
 * Provides comprehensive functions for managing inventory with support for
 * separate and same type inventories, automatic count calculation, and bulk operations
 */

// Constants
const INVENTORY_TYPES = {
  SEPARATE: 'separate',
  SAME: 'same'
};

const ITEM_STATUS = {
  AVAILABLE: 'available',
  USED: 'used',
  RESERVED: 'reserved',
  EXPIRED: 'expired'
};

/**
 * Get current inventory count from new or old structure
 * For separate type, auto-calculate from items array
 * @param {Object} item - Inventory item
 * @returns {number} Current inventory count
 */
function getCurrentInventoryCount(item) {
  if (item.inventory && typeof item.inventory === 'object') {
    // For separate type, auto-calculate count from items array
    if (item.inventory.type === INVENTORY_TYPES.SEPARATE && Array.isArray(item.inventory.items)) {
      return item.inventory.items.filter(i => i.status === ITEM_STATUS.AVAILABLE).length;
    }
    return parseInt(item.inventory.count) || 0;
  }
  return parseInt(item.currentInventory) || 0;
}

/**
 * Get inventory type
 * @param {Object} item - Inventory item
 * @returns {string} Inventory type ('separate' or 'same')
 */
function getInventoryType(item) {
  if (item.inventory && typeof item.inventory === 'object') {
    return item.inventory.type || INVENTORY_TYPES.SAME;
  }
  return INVENTORY_TYPES.SAME; // Default for legacy items
}

/**
 * Get inventory items array (for separate type)
 * @param {Object} item - Inventory item
 * @returns {Array} Array of inventory items
 */
function getInventoryItems(item) {
  if (item.inventory && typeof item.inventory === 'object' && item.inventory.type === INVENTORY_TYPES.SEPARATE) {
    return item.inventory.items || [];
  }
  return [];
}

/**
 * Get available inventory items (not used/reserved/expired)
 * @param {Object} item - Inventory item
 * @returns {Array} Array of available inventory items
 */
function getAvailableInventoryItems(item) {
  const items = getInventoryItems(item);
  return items.filter(i => i.status === ITEM_STATUS.AVAILABLE);
}

/**
 * Validate and format inventory item
 * @param {string|Object} item - Item to validate
 * @returns {Object|null} Validated item or null if invalid
 */
function validateInventoryItem(item) {
  if (typeof item === 'string' && item.trim()) {
    return {
      id: uuidv4(),
      value: item.trim(),
      status: ITEM_STATUS.AVAILABLE,
      createdAt: new Date().toISOString(),
      usedAt: null,
      reservedAt: null,
      expiresAt: null
    };
  }
  
  if (typeof item === 'object' && item.value && item.value.trim()) {
    return {
      id: item.id || uuidv4(),
      value: item.value.trim(),
      status: item.status || ITEM_STATUS.AVAILABLE,
      createdAt: item.createdAt || new Date().toISOString(),
      usedAt: item.usedAt || null,
      reservedAt: item.reservedAt || null,
      expiresAt: item.expiresAt || null,
      metadata: item.metadata || {}
    };
  }
  
  return null;
}

/**
 * Check if inventory item is in stock
 * @param {Object} item - Inventory item
 * @returns {boolean} True if in stock
 */
function isInStock(item) {
  return getCurrentInventoryCount(item) > 0;
}

/**
 * Check if inventory item is low stock
 * @param {Object} item - Inventory item
 * @param {number} threshold - Low stock threshold (default: 10)
 * @returns {boolean} True if low stock
 */
function isLowStock(item, threshold = 10) {
  const count = getCurrentInventoryCount(item);
  return count > 0 && count <= threshold;
}

/**
 * Check if inventory item is out of stock
 * @param {Object} item - Inventory item
 * @returns {boolean} True if out of stock
 */
function isOutOfStock(item) {
  return getCurrentInventoryCount(item) === 0;
}

/**
 * Parse bulk paste data into inventory items
 * Supports multiple formats: line-separated, comma-separated, JSON
 * @param {string} pasteData - Raw paste data
 * @returns {Array} Array of validated inventory items
 */
function parseBulkPasteData(pasteData) {
  if (!pasteData || typeof pasteData !== 'string') {
    return [];
  }
  
  const items = [];
  
  try {
    // Try to parse as JSON first
    const jsonData = JSON.parse(pasteData);
    if (Array.isArray(jsonData)) {
      jsonData.forEach(item => {
        const validatedItem = validateInventoryItem(item);
        if (validatedItem) items.push(validatedItem);
      });
      return items;
    }
  } catch (e) {
    // Not JSON, continue with other parsing methods
  }
  
  // Parse as line-separated or comma-separated values
  const lines = pasteData.split(/\r?\n/).filter(line => line.trim());
  
  lines.forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine) {
      // Support comma-separated values in a single line
      const values = trimmedLine.split(',').map(v => v.trim()).filter(v => v);
      values.forEach(value => {
        const validatedItem = validateInventoryItem(value);
        if (validatedItem) items.push(validatedItem);
      });
    }
  });
  
  return items;
}

/**
 * Convert inventory type from one to another
 * @param {Object} item - Inventory item
 * @param {string} newType - New inventory type
 * @returns {Object} Updated inventory structure
 */
function convertInventoryType(item, newType) {
  const currentType = getInventoryType(item);
  const currentCount = getCurrentInventoryCount(item);
  
  if (currentType === newType) {
    return item.inventory;
  }
  
  if (newType === INVENTORY_TYPES.SEPARATE) {
    // Convert to separate type
    return {
      type: INVENTORY_TYPES.SEPARATE,
      items: [],
      count: 0
    };
  } else {
    // Convert to same type
    return {
      type: INVENTORY_TYPES.SAME,
      count: currentCount
    };
  }
}

/**
 * Reserve inventory items for order processing
 * @param {Object} item - Inventory item
 * @param {number} quantity - Quantity to reserve
 * @returns {Object} Result with reserved items or error
 */
function reserveInventoryItems(item, quantity) {
  if (!isInStock(item)) {
    return { success: false, error: 'Item out of stock' };
  }
  
  const availableCount = getCurrentInventoryCount(item);
  if (availableCount < quantity) {
    return { success: false, error: `Insufficient stock. Available: ${availableCount}, Requested: ${quantity}` };
  }
  
  if (getInventoryType(item) === INVENTORY_TYPES.SEPARATE) {
    const availableItems = getAvailableInventoryItems(item);
    const itemsToReserve = availableItems.slice(0, quantity);
    
    // Mark items as reserved
    itemsToReserve.forEach(inventoryItem => {
      inventoryItem.status = ITEM_STATUS.RESERVED;
      inventoryItem.reservedAt = new Date().toISOString();
    });
    
    return {
      success: true,
      reservedItems: itemsToReserve,
      remainingCount: availableCount - quantity
    };
  } else {
    // For same type, just reduce the count
    return {
      success: true,
      reservedCount: quantity,
      remainingCount: availableCount - quantity
    };
  }
}

/**
 * Consume/use inventory items (mark as used)
 * @param {Object} item - Inventory item
 * @param {number|Array} itemsToConsume - Quantity or array of item IDs to consume
 * @returns {Object} Result with consumed items or error
 */
function consumeInventoryItems(item, itemsToConsume) {
  if (getInventoryType(item) === INVENTORY_TYPES.SEPARATE) {
    if (Array.isArray(itemsToConsume)) {
      // Consume specific items by ID
      const consumedItems = [];
      itemsToConsume.forEach(itemId => {
        const inventoryItem = item.inventory.items.find(i => i.id === itemId);
        if (inventoryItem && inventoryItem.status === ITEM_STATUS.AVAILABLE) {
          inventoryItem.status = ITEM_STATUS.USED;
          inventoryItem.usedAt = new Date().toISOString();
          consumedItems.push(inventoryItem);
        }
      });
      
      return {
        success: true,
        consumedItems,
        remainingCount: getCurrentInventoryCount(item)
      };
    } else {
      // Consume by quantity
      const quantity = parseInt(itemsToConsume);
      const availableItems = getAvailableInventoryItems(item);
      
      if (availableItems.length < quantity) {
        return { success: false, error: 'Insufficient available items' };
      }
      
      const itemsToUse = availableItems.slice(0, quantity);
      itemsToUse.forEach(inventoryItem => {
        inventoryItem.status = ITEM_STATUS.USED;
        inventoryItem.usedAt = new Date().toISOString();
      });
      
      return {
        success: true,
        consumedItems: itemsToUse,
        remainingCount: getCurrentInventoryCount(item)
      };
    }
  } else {
    // For same type, reduce count
    const quantity = parseInt(itemsToConsume);
    const currentCount = getCurrentInventoryCount(item);
    
    if (currentCount < quantity) {
      return { success: false, error: 'Insufficient inventory' };
    }
    
    const newCount = currentCount - quantity;
    return {
      success: true,
      consumedCount: quantity,
      remainingCount: newCount
    };
  }
}

/**
 * Get inventory statistics
 * @param {Array} inventoryItems - Array of inventory items
 * @returns {Object} Inventory statistics
 */
function getInventoryStatistics(inventoryItems) {
  const stats = {
    totalItems: inventoryItems.length,
    totalValue: 0,
    byType: {
      separate: 0,
      same: 0
    },
    byStatus: {
      inStock: 0,
      lowStock: 0,
      outOfStock: 0
    },
    byCategory: {},
    byProvider: {},
    totalInventoryCount: 0,
    averagePrice: 0
  };
  
  inventoryItems.forEach(item => {
    const type = getInventoryType(item);
    const count = getCurrentInventoryCount(item);
    const price = parseFloat(item.price) || 0;
    
    // Type statistics
    stats.byType[type]++;
    
    // Status statistics
    if (count === 0) {
      stats.byStatus.outOfStock++;
    } else if (isLowStock(item)) {
      stats.byStatus.lowStock++;
    } else {
      stats.byStatus.inStock++;
    }
    
    // Category statistics
    if (item.categories && Array.isArray(item.categories)) {
      item.categories.forEach(category => {
        stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;
      });
    }
    
    // Provider statistics
    const provider = item.brand || item.provider || 'Unknown';
    stats.byProvider[provider] = (stats.byProvider[provider] || 0) + 1;
    
    // Value calculations
    stats.totalValue += price * count;
    stats.totalInventoryCount += count;
  });
  
  stats.averagePrice = stats.totalItems > 0 ? stats.totalValue / stats.totalInventoryCount : 0;
  
  return stats;
}

/**
 * Filter inventory items based on criteria
 * @param {Array} inventoryItems - Array of inventory items
 * @param {Object} filters - Filter criteria
 * @returns {Array} Filtered inventory items
 */
function filterInventoryItems(inventoryItems, filters = {}) {
  return inventoryItems.filter(item => {
    // Filter by inventory type
    if (filters.type) {
      const itemType = getInventoryType(item);
      if (filters.type === 'separate' && itemType !== 'separate') return false;
      if (filters.type === 'else' && itemType === 'separate') return false;
      if (filters.type === 'same' && itemType !== 'same') return false;
    }
    
    // Filter by stock status
    if (filters.stockStatus) {
      const count = getCurrentInventoryCount(item);
      if (filters.stockStatus === 'inStock' && count === 0) return false;
      if (filters.stockStatus === 'outOfStock' && count > 0) return false;
      if (filters.stockStatus === 'lowStock' && !isLowStock(item)) return false;
    }
    
    // Filter by category
    if (filters.category) {
      if (!item.categories || !item.categories.some(cat => 
        cat.toLowerCase().includes(filters.category.toLowerCase())
      )) return false;
    }
    
    // Filter by provider
    if (filters.provider) {
      const provider = item.brand || item.provider || '';
      if (!provider.toLowerCase().includes(filters.provider.toLowerCase())) return false;
    }
    
    // Filter by SKU
    if (filters.sku) {
      if (!item.sku || !item.sku.toLowerCase().includes(filters.sku.toLowerCase())) return false;
    }
    
    // Filter by search term
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = [
        item.name,
        item.sku,
        item.description,
        ...(item.categories || [])
      ].join(' ').toLowerCase();
      
      if (!searchableText.includes(searchTerm)) return false;
    }
    
    // Filter by price range
    if (filters.minPrice !== undefined) {
      if ((item.price || 0) < filters.minPrice) return false;
    }
    if (filters.maxPrice !== undefined) {
      if ((item.price || 0) > filters.maxPrice) return false;
    }
    
    return true;
  });
}

/**
 * Sort inventory items
 * @param {Array} inventoryItems - Array of inventory items
 * @param {string} sortBy - Field to sort by
 * @param {string} sortOrder - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted inventory items
 */
function sortInventoryItems(inventoryItems, sortBy = 'name', sortOrder = 'asc') {
  return [...inventoryItems].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case 'count':
        aValue = getCurrentInventoryCount(a);
        bValue = getCurrentInventoryCount(b);
        break;
      case 'price':
        aValue = a.price || 0;
        bValue = b.price || 0;
        break;
      case 'name':
      default:
        aValue = (a.name || '').toLowerCase();
        bValue = (b.name || '').toLowerCase();
    }
    
    if (sortOrder === 'desc') {
      return aValue < bValue ? 1 : -1;
    }
    return aValue > bValue ? 1 : -1;
  });
}

/**
 * Paginate inventory items
 * @param {Array} inventoryItems - Array of inventory items
 * @param {number} page - Page number (1-based)
 * @param {number} limit - Items per page
 * @returns {Object} Paginated result
 */
function paginateInventoryItems(inventoryItems, page = 1, limit = 50) {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedItems = inventoryItems.slice(startIndex, endIndex);
  
  return {
    items: paginatedItems,
    pagination: {
      page,
      limit,
      total: inventoryItems.length,
      totalPages: Math.ceil(inventoryItems.length / limit),
      hasNext: endIndex < inventoryItems.length,
      hasPrev: page > 1
    }
  };
}

/**
 * Export inventory data to various formats
 * @param {Array} inventoryItems - Array of inventory items
 * @param {string} format - Export format ('json', 'csv')
 * @returns {string} Exported data
 */
function exportInventoryData(inventoryItems, format = 'json') {
  if (format === 'csv') {
    const headers = ['SKU', 'Name', 'Type', 'Count', 'Price', 'Currency', 'Categories', 'Provider'];
    const rows = inventoryItems.map(item => [
      item.sku || '',
      item.name || '',
      getInventoryType(item),
      getCurrentInventoryCount(item),
      item.price || 0,
      item.currency || '',
      (item.categories || []).join(';'),
      item.brand || item.provider || ''
    ]);
    
    return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  }
  
  // Default to JSON
  return JSON.stringify(inventoryItems.map(item => ({
    ...item,
    inventoryType: getInventoryType(item),
    currentCount: getCurrentInventoryCount(item),
    availableItems: getAvailableInventoryItems(item)
  })), null, 2);
}

module.exports = {
  getCurrentInventoryCount,
  getInventoryType,
  getInventoryItems,
  getAvailableInventoryItems,
  validateInventoryItem,
  isInStock,
  isLowStock,
  isOutOfStock,
  parseBulkPasteData,
  convertInventoryType,
  reserveInventoryItems,
  consumeInventoryItems,
  getInventoryStatistics,
  filterInventoryItems,
  sortInventoryItems,
  paginateInventoryItems,
  exportInventoryData,
  INVENTORY_TYPES,
  ITEM_STATUS
};