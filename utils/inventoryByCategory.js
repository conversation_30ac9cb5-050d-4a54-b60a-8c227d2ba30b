import { slugify } from "./helpers"

function inventoryByCategory (inventory) {
  return inventory.reduce((acc, next) => {
    const categories = next.categories
    categories.forEach(c => {
      /* console.log("ccccccccccccccccc:")
      console.log(c) */
      let corig = c   //here this c is already sluggified... where did I do that?
      c = slugify(c) //Son added
      if (acc[c]) {
        acc[c].items.push({ ...next, fullName: corig });
      } else {        
        acc[c] = {}
        acc[c].cFullName = corig
        acc[c].items = []
        acc[c].items.push(next)
      }
    })
    /* console.log("acc:")
    console.log(acc) */
    return acc
  }, {})
}

export {
  inventoryByCategory
}