// Detect if we're running in browser environment
const isBrowser = typeof window !== 'undefined';

let fs, path, LOG_BASE_DIR;
if (!isBrowser) {
  // Only import Node.js modules on the server
  fs = require('fs');
  path = require('path');
  // Base directory for all checkout logs
  LOG_BASE_DIR = path.join(process.cwd(), 'data', 'logs', 'checkout');
}

/**
 * Ensures that the log directory exists - server side only
 * @param {string} dirPath - Directory path to ensure
 */
const ensureDirectoryExists = (dirPath) => {
  if (isBrowser) return; // Skip in browser
  
  if (!fs.existsSync(dirPath)) {
    try {
      fs.mkdirSync(dirPath, { recursive: true });
    } catch (error) {
      console.error(`Failed to create directory ${dirPath}:`, error);
    }
  }
};

/**
 * Gets the log file path for the current date - server side only
 * @returns {string} The full path to the log file
 */
const getLogFilePath = () => {
  if (isBrowser) return null; // Skip in browser
  
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  
  // Create directory structure by date: year/month/day
  const dirPath = path.join(LOG_BASE_DIR, String(year), month);
  ensureDirectoryExists(dirPath);
  
  return path.join(dirPath, `${year}-${month}-${day}.json`);
};

/**
 * Read existing logs from the file - server side only
 * @param {string} filePath - Path to the log file
 * @returns {Array} Array of log entries
 */
const readLogsFromFile = (filePath) => {
  if (isBrowser) return []; // Skip in browser
  
  try {
    if (fs.existsSync(filePath)) {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      return fileContent ? JSON.parse(fileContent) : [];
    }
  } catch (error) {
    console.error(`Failed to read logs from ${filePath}:`, error);
  }
  return [];
};

/**
 * Write logs to the file - server side only
 * @param {string} filePath - Path to the log file
 * @param {Array} logs - Array of log entries
 */
const writeLogsToFile = (filePath, logs) => {
  if (isBrowser) return; // Skip in browser
  
  try {
    fs.writeFileSync(filePath, JSON.stringify(logs, null, 2), 'utf8');
  } catch (error) {
    console.error(`Failed to write logs to ${filePath}:`, error);
  }
};

/**
 * Logs checkout-related events to a JSON file (server) or console (browser)
 * @param {string} event - The event type or name
 * @param {Object} data - Additional data to log
 * @param {Object} options - Additional options for logging
 */
const logCheckoutEvent = (event, data = {}, options = {}) => {
  try {
    // Create the log entry
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      data,
      ...options
    };
    
    if (isBrowser) {
      // Browser implementation - just use console
      console.log(`[CHECKOUT] ${event}`, logEntry);
      return true;
    } else {
      // Server implementation - log to file
      const logFilePath = getLogFilePath();
      const existingLogs = readLogsFromFile(logFilePath);
      
      // Add to existing logs
      existingLogs.push(logEntry);
      
      // Save to file
      writeLogsToFile(logFilePath, existingLogs);
      
      // Also log to console for development
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[CHECKOUT] ${event}`, data ? data : '');
      }
      
      return true;
    }
  } catch (error) {
    console.error('Failed to log checkout event:', error);
    return false;
  }
};

/**
 * Logs a payment provider API call
 * @param {string} provider - Payment provider name
 * @param {string} endpoint - API endpoint
 * @param {Object} payload - Request payload
 * @param {Object} auth - Authentication details
 */
const logPaymentApiCall = (provider, endpoint, payload = {}, auth = {}) => {
  return logCheckoutEvent('PAYMENT_API_CALL', {
    provider,
    endpoint,
    payload,
    auth
  });
};

/**
 * Logs a payment provider API response
 * @param {string} provider - Payment provider name
 * @param {string} endpoint - API endpoint
 * @param {Object} response - Response data
 * @param {number} statusCode - HTTP status code
 */
const logPaymentApiResponse = (provider, endpoint, response = {}, statusCode) => {
  return logCheckoutEvent('PAYMENT_API_RESPONSE', {
    provider,
    endpoint,
    response,
    statusCode
  });
};

/**
 * Logs a checkout process event
 * @param {string} process - Process name or stage
 * @param {Object} data - Process data
 */
const logCheckoutProcess = (process, data = {}) => {
  return logCheckoutEvent('CHECKOUT_PROCESS', {
    process,
    data
  });
};

/**
 * Logs a transaction event
 * @param {string} orderId - Order ID
 * @param {string} status - Transaction status
 * @param {Object} details - Transaction details
 */
const logTransaction = (orderId, status, details = {}) => {
  return logCheckoutEvent('TRANSACTION', {
    orderId,
    status,
    details
  });
};

/**
 * Logs an error in the checkout process
 * @param {string} source - Error source or component
 * @param {Error|string} error - Error object or message
 * @param {Object} context - Additional context
 */
const logCheckoutError = (source, error, context = {}) => {
  const errorData = error instanceof Error 
    ? { message: error.message, stack: error.stack }
    : { message: error };
    
  return logCheckoutEvent('ERROR', {
    source,
    error: errorData,
    context
  });
};

// Export the logging functions
module.exports = {
  logCheckoutEvent,
  logPaymentApiCall,
  logPaymentApiResponse,
  logCheckoutProcess,
  logTransaction,
  logCheckoutError,
}; 