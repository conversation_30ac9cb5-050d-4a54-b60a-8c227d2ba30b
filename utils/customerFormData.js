/**
 * Utility to fetch and manage customer data for forms
 */

/**
 * Check if a user is logged in by looking for customer data in localStorage
 * @param {string} storeId - Optional store ID to check for store-specific customer data
 * @returns {boolean} True if the user is logged in
 */
export const isUserLoggedIn = (storeId = null) => {
  if (typeof window === 'undefined') return false;
  
  // Try store-specific customer data first if a storeId is provided
  if (storeId) {
    const storeCustomerData = localStorage.getItem(`${storeId}_customerData`);
    if (storeCustomerData) return true;
  }
  
  // Fall back to global customer data
  return !!localStorage.getItem('customerData') || !!localStorage.getItem('user');
};

/**
 * Get logged-in customer data from localStorage
 * @param {string} storeId - Optional store ID to get store-specific customer data
 * @returns {object|null} Customer data object or null if not logged in
 */
export const getCustomerData = (storeId = null) => {
  if (typeof window === 'undefined') return null;
  
  let customerData = null;
  
  // Try store-specific customer data first if a storeId is provided
  if (storeId) {
    const storeCustomerData = localStorage.getItem(`${storeId}_customerData`);
    if (storeCustomerData) {
      try {
        customerData = JSON.parse(storeCustomerData);
      } catch (error) {
        console.error('[CUSTOMER_FORM] Error parsing store-specific customer data:', error);
      }
    }
  }
  
  // If no store-specific data, try global customer data
  if (!customerData) {
    const globalCustomerData = localStorage.getItem('customerData') || localStorage.getItem('user');
    if (globalCustomerData) {
      try {
        customerData = JSON.parse(globalCustomerData);
      } catch (error) {
        console.error('[CUSTOMER_FORM] Error parsing global customer data:', error);
      }
    }
  }
  
  return customerData;
};

/**
 * Get customer data from API if logged in
 * @param {string} customerId - The customer ID to fetch
 * @returns {Promise<object|null>} Customer data object or null if error
 */
export const fetchCustomerData = async (customerId) => {
  if (!customerId) return null;
  
  try {
    const response = await fetch(`/api/customers/${customerId}`);
    if (!response.ok) {
      console.error('[CUSTOMER_FORM] Error fetching customer data:', response.statusText);
      return null;
    }
    
    const customerData = await response.json();
    return customerData;
  } catch (error) {
    console.error('[CUSTOMER_FORM] Error fetching customer data:', error);
    return null;
  }
};

/**
 * Get default form values from customer data
 * @param {object} customerData - The customer data object
 * @returns {object} Default form values
 */
export const getDefaultFormValues = (customerData) => {
  if (!customerData) return {};
  
  // Extract name parts (assuming name format is "First Last")
  let firstName = '';
  let lastName = '';
  
  if (customerData.name) {
    const nameParts = customerData.name.split(' ');
    if (nameParts.length > 1) {
      lastName = nameParts.pop();
      firstName = nameParts.join(' ');
    } else {
      firstName = customerData.name;
    }
  }
  
  // Get address from the first address in the addresses array if available
  const address = customerData.addresses && customerData.addresses.length > 0
    ? customerData.addresses[0]
    : {};
  
  return {
    name: customerData.name || '',
    firstName: firstName,
    lastName: lastName,
    email: customerData.email || '',
    mobile: customerData.phone || '',
    phone: customerData.phone || '',
    street: address.street || '',
    city: address.city || '',
    state: address.state || '',
    zipCode: address.zipCode || '',
    postal_code: address.zipCode || '', // For compatibility with some forms
    country: address.country || 'Vietnam',
    company: customerData.company || '',
    message: ''
  };
}; 