// API Error Handler Utility
export function handleApiError(res, error, context = 'API') {
  console.error(`${context} Error:`, error);
  
  // Determine error status and message
  let status = 500;
  let message = 'Internal Server Error';
  
  if (error.name === 'ValidationError') {
    status = 400;
    message = 'Invalid request data';
  } else if (error.name === 'UnauthorizedError') {
    status = 401;
    message = 'Unauthorized access';
  } else if (error.name === 'NotFoundError') {
    status = 404;
    message = 'Resource not found';
  }
  
  return res.status(status).json({
    error: true,
    message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
}

export function validateMethod(req, res, allowedMethods) {
  if (!allowedMethods.includes(req.method)) {
    return res.status(405).json({
      error: true,
      message: `Method ${req.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`
    });
  }
  return null;
}

export function requireAuth(req, res) {
  // Basic auth check - customize based on your auth system
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return res.status(401).json({
      error: true,
      message: 'Authorization header required'
    });
  }
  
  // Add your auth validation logic here
  return null;
}
