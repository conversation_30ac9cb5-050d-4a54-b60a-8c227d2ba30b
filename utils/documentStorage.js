// Document storage utilities
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { getCustomerId } from './customerAuth';

// Configuration
const STORAGE_BASE_PATH = process.env.DOCUMENT_STORAGE_PATH || 'data/documents/customers';
const DOCUMENT_TYPES = ['idCard', 'photo', 'proofOfResidence'];

// Convert fs.readFile to Promise-based
const readFileAsync = promisify(fs.readFile);
const statAsync = promisify(fs.stat);
const existsAsync = promisify(fs.exists);

/**
 * Get the file path for a document based on type, filename, and customer ID
 * @param {string} documentType - The type of document ('idCard', 'photo', 'proofOfResidence')
 * @param {string} filename - The filename of the document
 * @param {string} customerId - Optional customer ID (if not provided, will be retrieved from auth)
 * @returns {string} - The full file path
 */
export function getDocumentPath(documentType, filename, customerId = null) {
  // Sanitize inputs to prevent directory traversal
  const sanitizedFilename = path.basename(filename);
  
  // Get customer ID if not provided
  if (!customerId) {
    customerId = getCustomerId();
    if (!customerId) {
      throw new Error('Customer ID is required');
    }
  }
  
  // Construct the path to the document
  return path.join(process.cwd(), STORAGE_BASE_PATH, customerId, sanitizedFilename);
}

/**
 * Check if a document exists
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @param {string} customerId - Optional customer ID
 * @returns {Promise<boolean>} - Whether the document exists
 */
export async function documentExists(documentType, filename, customerId = null) {
  try {
    const filePath = getDocumentPath(documentType, filename, customerId);
    return await existsAsync(filePath);
  } catch (error) {
    console.error('Error checking document existence:', error);
    return false;
  }
}

/**
 * Get document file info
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @param {string} customerId - Optional customer ID
 * @returns {Promise<{size: number, mimeType: string}>} - File info
 */
export async function getDocumentInfo(documentType, filename, customerId = null) {
  try {
    const filePath = getDocumentPath(documentType, filename, customerId);
    const stats = await statAsync(filePath);
    
    // Determine MIME type based on file extension
    const ext = path.extname(filename).toLowerCase();
    let mimeType = 'application/octet-stream'; // Default
    
    if (ext === '.pdf') {
      mimeType = 'application/pdf';
    } else if (['.jpg', '.jpeg'].includes(ext)) {
      mimeType = 'image/jpeg';
    } else if (ext === '.png') {
      mimeType = 'image/png';
    } else if (ext === '.gif') {
      mimeType = 'image/gif';
    }
    
    return {
      size: stats.size,
      mimeType
    };
  } catch (error) {
    console.error('Error getting document info:', error);
    throw error;
  }
}

/**
 * Read a document file
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @param {string} customerId - Optional customer ID
 * @returns {Promise<Buffer>} - The document file contents
 */
export async function readDocument(documentType, filename, customerId = null) {
  try {
    const filePath = getDocumentPath(documentType, filename, customerId);
    return await readFileAsync(filePath);
  } catch (error) {
    console.error('Error reading document:', error);
    throw error;
  }
}

/**
 * Get customer ID from the filename
 * @param {string} filename - The filename which may contain customer ID
 * @returns {string|null} - The extracted customer ID or null if not found
 */
export function extractCustomerIdFromFilename(filename) {
  // Extract customer ID from filename if it follows a pattern like idCard_customerId_timestamp.jpg
  const parts = filename.split('_');
  if (parts.length >= 2) {
    return parts[1]; // This assumes the customer ID is the second part of the filename
  }
  return null;
}

export default {
  getDocumentPath,
  documentExists,
  getDocumentInfo,
  readDocument,
  extractCustomerIdFromFilename
}; 