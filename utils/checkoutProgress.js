// Checkout Progress and Payment Monitoring Utility

const CHECKOUT_STORAGE_KEY = 'checkout_progress';
const PAYMENT_STATUS_KEY = 'payment_status';

/**
 * Payment Status Management
 */
export const PaymentMonitor = {
  // Mark payment as initiated
  markPaymentInitiated: (orderId, paymentMethod, paymentData = {}) => {
    try {
      const paymentRecord = {
        orderId,
        paymentMethod,
        status: 'INITIATED',
        timestamp: new Date().toISOString(),
        sessionId: generateSessionId(),
        paymentData,
        warnings: []
      };

      // Store in localStorage
      const existingPayments = JSON.parse(localStorage.getItem(PAYMENT_STATUS_KEY) || '{}');
      
      // Check for duplicate payment attempts
      if (existingPayments[orderId]) {
        paymentRecord.warnings.push({
          type: 'DUPLICATE_ATTEMPT',
          previousMethod: existingPayments[orderId].paymentMethod,
          previousTimestamp: existingPayments[orderId].timestamp,
          message: 'Payment for this order was already initiated'
        });
        
        console.warn(`[PAYMENT_MONITOR] Duplicate payment attempt detected for order ${orderId}`);
      }

      existingPayments[orderId] = paymentRecord;
      localStorage.setItem(PAYMENT_STATUS_KEY, JSON.stringify(existingPayments));

      // Also save to centralized orders API
      this.savePaymentStatusToServer(paymentRecord);

      // Send email notification
      this.sendEmailNotification(paymentRecord);

      console.log(`[PAYMENT_MONITOR] Payment initiated for order ${orderId} using ${paymentMethod}`);
      return paymentRecord;
    } catch (error) {
      console.error('[PAYMENT_MONITOR] Error marking payment as initiated:', error);
      return null;
    }
  },

  // Check if payment was already initiated
  checkPaymentStatus: (orderId) => {
    try {
      const existingPayments = JSON.parse(localStorage.getItem(PAYMENT_STATUS_KEY) || '{}');
      return existingPayments[orderId] || null;
    } catch (error) {
      console.error('[PAYMENT_MONITOR] Error checking payment status:', error);
      return null;
    }
  },

  // Update payment status
  updatePaymentStatus: (orderId, status, additionalData = {}) => {
    try {
      const existingPayments = JSON.parse(localStorage.getItem(PAYMENT_STATUS_KEY) || '{}');
      
      if (existingPayments[orderId]) {
        existingPayments[orderId] = {
          ...existingPayments[orderId],
          status,
          lastUpdated: new Date().toISOString(),
          ...additionalData
        };
        
        localStorage.setItem(PAYMENT_STATUS_KEY, JSON.stringify(existingPayments));
        
        // Also update server
        this.savePaymentStatusToServer(existingPayments[orderId]);

        // Send email notification if payment is completed
        if (status === 'COMPLETED') {
          this.sendEmailNotification(existingPayments[orderId]);
        }
        
        console.log(`[PAYMENT_MONITOR] Payment status updated for order ${orderId}: ${status}`);
        return existingPayments[orderId];
      }
      
      return null;
    } catch (error) {
      console.error('[PAYMENT_MONITOR] Error updating payment status:', error);
      return null;
    }
  },

  // Save payment status to server
  savePaymentStatusToServer: async (paymentRecord) => {
    try {
      const response = await fetch('/api/payment/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentRecord)
      });

      if (!response.ok) {
        console.warn('[PAYMENT_MONITOR] Failed to save payment status to server');
      }
    } catch (error) {
      console.warn('[PAYMENT_MONITOR] Error saving payment status to server:', error);
    }
  },

  // Send email notification for payment events
  sendEmailNotification: async (paymentRecord) => {
    try {
      console.log('[PAYMENT_MONITOR] Sending email notification for payment:', paymentRecord.orderId);
      
      const response = await fetch('/api/notifications/payment-alert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: paymentRecord.orderId,
          paymentMethod: paymentRecord.paymentMethod,
          status: paymentRecord.status,
          totalAmount: paymentRecord.paymentData?.totalAmount,
          currency: paymentRecord.paymentData?.currency,
          timestamp: paymentRecord.timestamp,
          customerInfo: paymentRecord.paymentData?.customerInfo,
          items: paymentRecord.paymentData?.items
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('[PAYMENT_MONITOR] Email notification sent successfully:', result.messageId);
      } else {
        console.warn('[PAYMENT_MONITOR] Email notification failed:', await response.text());
      }
    } catch (error) {
      console.warn('[PAYMENT_MONITOR] Error sending email notification:', error);
      // Don't throw error - email failure shouldn't break payment flow
    }
  },

  // Get duplicate payment warning
  getDuplicatePaymentWarning: (orderId) => {
    const paymentStatus = this.checkPaymentStatus(orderId);
    
    if (paymentStatus && paymentStatus.warnings.length > 0) {
      const duplicateWarning = paymentStatus.warnings.find(w => w.type === 'DUPLICATE_ATTEMPT');
      if (duplicateWarning) {
        return {
          isDuplicate: true,
          message: `Payment for this order was already initiated on ${new Date(duplicateWarning.previousTimestamp).toLocaleString()} using ${duplicateWarning.previousMethod}. Are you sure you want to proceed with a new payment?`,
          previousMethod: duplicateWarning.previousMethod,
          previousTimestamp: duplicateWarning.previousTimestamp
        };
      }
    }
    
    return { isDuplicate: false };
  }
};

/**
 * Checkout Progress Management
 */
export const CheckoutProgress = {
  // Save current progress (both localStorage and server)
  saveProgress: async (orderId, progressData) => {
    try {
      const progress = {
        orderId,
        timestamp: new Date().toISOString(),
        sessionId: generateSessionId(),
        progress: progressData,
        url: window.location.href,
        userAgent: navigator.userAgent
      };

      // Save to localStorage first (immediate)
      const existingProgress = JSON.parse(localStorage.getItem(CHECKOUT_STORAGE_KEY) || '{}');
      existingProgress[orderId] = progress;
      localStorage.setItem(CHECKOUT_STORAGE_KEY, JSON.stringify(existingProgress));

      // Also save to server (async, don't block on failure)
      try {
        await fetch(`/api/checkout/progress?orderId=${orderId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(progress)
        });
        console.log(`[CHECKOUT_PROGRESS] Progress saved to server for order ${orderId}`);
      } catch (serverError) {
        console.warn('[CHECKOUT_PROGRESS] Failed to save to server, localStorage saved:', serverError);
      }

      console.log(`[CHECKOUT_PROGRESS] Progress saved for order ${orderId}`);
      return progress;
    } catch (error) {
      console.error('[CHECKOUT_PROGRESS] Error saving progress:', error);
      return null;
    }
  },

  // Load saved progress (try server first, fallback to localStorage)
  loadProgress: async (orderId) => {
    try {
      // First try to load from server
      try {
        const response = await fetch(`/api/checkout/progress?orderId=${orderId}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.progress) {
            console.log(`[CHECKOUT_PROGRESS] Progress loaded from server for order ${orderId}`);
            return data.progress;
          }
        }
      } catch (serverError) {
        console.warn('[CHECKOUT_PROGRESS] Failed to load from server, trying localStorage:', serverError);
      }

      // Fallback to localStorage
      const existingProgress = JSON.parse(localStorage.getItem(CHECKOUT_STORAGE_KEY) || '{}');
      const localProgress = existingProgress[orderId] || null;

      if (localProgress) {
        console.log(`[CHECKOUT_PROGRESS] Progress loaded from localStorage for order ${orderId}`);
      }

      return localProgress;
    } catch (error) {
      console.error('[CHECKOUT_PROGRESS] Error loading progress:', error);
      return null;
    }
  },

  // Check for abandoned checkouts
  getAbandonedCheckouts: () => {
    try {
      const existingProgress = JSON.parse(localStorage.getItem(CHECKOUT_STORAGE_KEY) || '{}');
      const now = new Date();
      const abandonedThreshold = 30 * 60 * 1000; // 30 minutes

      const abandoned = Object.values(existingProgress).filter(progress => {
        const progressTime = new Date(progress.timestamp);
        const timeDiff = now - progressTime;
        
        // Check if progress is older than threshold and not completed
        return timeDiff > abandonedThreshold && !progress.progress.completed;
      });

      return abandoned;
    } catch (error) {
      console.error('[CHECKOUT_PROGRESS] Error getting abandoned checkouts:', error);
      return [];
    }
  },

  // Clear progress after completion (both localStorage and server)
  clearProgress: async (orderId) => {
    try {
      // Clear from localStorage
      const existingProgress = JSON.parse(localStorage.getItem(CHECKOUT_STORAGE_KEY) || '{}');
      delete existingProgress[orderId];
      localStorage.setItem(CHECKOUT_STORAGE_KEY, JSON.stringify(existingProgress));

      // Also clear from server (async, don't block on failure)
      try {
        await fetch(`/api/checkout/progress?orderId=${orderId}`, {
          method: 'DELETE'
        });
        console.log(`[CHECKOUT_PROGRESS] Progress cleared from server for order ${orderId}`);
      } catch (serverError) {
        console.warn('[CHECKOUT_PROGRESS] Failed to clear from server, localStorage cleared:', serverError);
      }

      console.log(`[CHECKOUT_PROGRESS] Progress cleared for order ${orderId}`);
    } catch (error) {
      console.error('[CHECKOUT_PROGRESS] Error clearing progress:', error);
    }
  },

  // Mark checkout as completed
  markCompleted: (orderId) => {
    try {
      const existingProgress = JSON.parse(localStorage.getItem(CHECKOUT_STORAGE_KEY) || '{}');
      
      if (existingProgress[orderId]) {
        existingProgress[orderId].progress.completed = true;
        existingProgress[orderId].completedAt = new Date().toISOString();
        localStorage.setItem(CHECKOUT_STORAGE_KEY, JSON.stringify(existingProgress));
        
        console.log(`[CHECKOUT_PROGRESS] Checkout marked as completed for order ${orderId}`);
      }
    } catch (error) {
      console.error('[CHECKOUT_PROGRESS] Error marking checkout as completed:', error);
    }
  }
};

/**
 * Abandoned Checkout Recovery
 */
export const AbandonedCheckoutRecovery = {
  // Check for recoverable checkout on page load
  checkForRecoverableCheckout: (currentOrderId) => {
    const abandoned = CheckoutProgress.getAbandonedCheckouts();
    
    // Filter out current order from abandoned list - be more robust with ID checking
    const recoverableCheckouts = abandoned.filter(checkout => {
      // Get the order ID from the checkout data - handle legacy formats
      const checkoutOrderId = checkout.orderId || checkout.orderNumber;
      return checkoutOrderId && checkoutOrderId !== currentOrderId;
    });

    if (recoverableCheckouts.length > 0) {
      // Return the most recent abandoned checkout
      const mostRecent = recoverableCheckouts.sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
      )[0];

      // Ensure the checkout has a valid orderId field
      if (!mostRecent.orderId && mostRecent.orderNumber) {
        mostRecent.orderId = mostRecent.orderNumber;
        delete mostRecent.orderNumber;
      }

      return {
        hasRecoverable: true,
        checkout: mostRecent,
        message: `Bạn có một đơn hàng chưa hoàn thành từ ${new Date(mostRecent.timestamp).toLocaleString()}. Bạn có muốn tiếp tục đơn hàng đó không?`
      };
    }

    return { hasRecoverable: false };
  },

  // Restore abandoned checkout
  restoreCheckout: (checkoutData) => {
    try {
      // Return the progress data to be restored
      return {
        success: true,
        orderId: checkoutData.orderId,
        progress: checkoutData.progress,
        restoredAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[ABANDONED_CHECKOUT] Error restoring checkout:', error);
      return { success: false, error: error.message };
    }
  },

  // Dismiss abandoned checkout (user chose not to restore)
  dismissAbandonedCheckout: async (orderId) => {
    try {
      await CheckoutProgress.clearProgress(orderId);
      console.log(`[ABANDONED_CHECKOUT] Dismissed abandoned checkout for order ${orderId}`);
    } catch (error) {
      console.error('[ABANDONED_CHECKOUT] Error dismissing abandoned checkout:', error);
    }
  },

  // Clear all abandoned checkouts (for debugging/reset purposes)
  clearAllAbandonedCheckouts: () => {
    try {
      localStorage.removeItem(CHECKOUT_STORAGE_KEY);
      console.log('[ABANDONED_CHECKOUT] Cleared all abandoned checkout data');
      return true;
    } catch (error) {
      console.error('[ABANDONED_CHECKOUT] Error clearing all abandoned checkouts:', error);
      return false;
    }
  }
};

// Utility functions
function generateSessionId() {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Auto-save progress every 30 seconds
let autoSaveInterval = null;

export const startAutoSave = (orderId, getProgressData) => {
  if (autoSaveInterval) {
    clearInterval(autoSaveInterval);
  }

  autoSaveInterval = setInterval(async () => {
    if (typeof getProgressData === 'function') {
      const progressData = getProgressData();
      if (progressData && !progressData.completed) {
        await CheckoutProgress.saveProgress(orderId, progressData);
      }
    }
  }, 30000); // Every 30 seconds

  console.log(`[AUTO_SAVE] Started auto-save for order ${orderId}`);
};

export const stopAutoSave = () => {
  if (autoSaveInterval) {
    clearInterval(autoSaveInterval);
    autoSaveInterval = null;
    console.log('[AUTO_SAVE] Stopped auto-save');
  }
};

// Migrate old progress data to remove orderNumber references
export const migrateOldProgress = () => {
  try {
    const existingProgress = JSON.parse(localStorage.getItem(CHECKOUT_STORAGE_KEY) || '{}');
    let migrated = false;

    Object.entries(existingProgress).forEach(([orderId, progress]) => {
      if (progress && typeof progress === 'object') {
        // Remove any orderNumber references from the progress data
        if (progress.orderNumber) {
          delete progress.orderNumber;
          migrated = true;
        }
        
        // Clean up progress.progress object if it exists
        if (progress.progress && typeof progress.progress === 'object') {
          if (progress.progress.orderNumber) {
            delete progress.progress.orderNumber;
            migrated = true;
          }
        }
      }
    });

    if (migrated) {
      localStorage.setItem(CHECKOUT_STORAGE_KEY, JSON.stringify(existingProgress));
      console.log('[MIGRATION] Migrated checkout progress data to remove orderNumber references');
    }
  } catch (error) {
    console.error('[MIGRATION] Error migrating old progress:', error);
  }
};

// Clean up old progress data (older than 7 days)
export const cleanupOldProgress = () => {
  try {
    const existingProgress = JSON.parse(localStorage.getItem(CHECKOUT_STORAGE_KEY) || '{}');
    const now = new Date();
    const weekAgo = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));

    const cleaned = {};
    let removedCount = 0;

    Object.entries(existingProgress).forEach(([orderId, progress]) => {
      const progressTime = new Date(progress.timestamp);
      if (progressTime > weekAgo) {
        cleaned[orderId] = progress;
      } else {
        removedCount++;
      }
    });

    if (removedCount > 0) {
      localStorage.setItem(CHECKOUT_STORAGE_KEY, JSON.stringify(cleaned));
      console.log(`[CLEANUP] Removed ${removedCount} old progress entries`);
    }
  } catch (error) {
    console.error('[CLEANUP] Error cleaning up old progress:', error);
  }
};

// Initialize cleanup and migration on module load
if (typeof window !== 'undefined') {
  migrateOldProgress();
  cleanupOldProgress();
} 