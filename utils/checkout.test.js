// Checkout Management Module Test
const CheckoutManager = require('./checkout');
const CheckoutSystem = require('./checkout/index');
const CardCheckout = require('./checkout/Card');
const SimCheckout = require('./checkout/Sim');
const TravelSimCheckout = require('./checkout/TravelSim');

// Test products
const testProducts = [
  {
    sku: "document.license.conversion.vn2tw",
    name: "Đổi Bằng Lái Xe Việt Nam Sang Đài Loan",
    categories: ["TaiwanServices", "License", "CARD"],
    price: 1800000,
    currency: "VND"
  },
  {
    sku: "telecom.sim.data.taiwan",
    name: "SIM Đài Loan",
    categories: ["SIM", "Taiwan", "Telecom"],
    price: 350000,
    currency: "VND"
  },
  {
    sku: "telecom.esim.travel.global",
    name: "SIM Du Lịch Quốc Tế",
    categories: ["TRAVELSIM", "International", "Travel"],
    price: 450000,
    currency: "VND"
  }
];

// Test Cases
console.log('======== CHECKOUT MANAGER TEST ========');

// Test 1: Detect checkout method by category
console.log('Test 1: Detecting checkout method by category');
testProducts.forEach(product => {
  const method = CheckoutManager.getMethodByCategory(product.categories);
  console.log(`Product: ${product.name}`);
  console.log(`Categories: ${product.categories.join(', ')}`);
  console.log(`Detected Method: ${method} (${CheckoutManager.methods[method]})`);
  console.log('--------------');
});

// Test 2: Process checkout with automatic method detection
console.log('\nTest 2: Process checkout with automatic method detection');
testProducts.forEach(product => {
  console.log(`Processing checkout for: ${product.name}`);
  const checkout = CheckoutManager.processCheckout(product);
  console.log(`Checkout Type: ${checkout.type}`);
  console.log(`Steps: ${checkout.steps.length}`);
  console.log(`Shipping Available: ${checkout.config.shippingAvailable}`);
  console.log('--------------');
});

// Test 3: Process checkout with specified method
console.log('\nTest 3: Process checkout with specified method');
const product = testProducts[0];
console.log(`Processing checkout for: ${product.name} with forced method 3`);
const checkout = CheckoutManager.processCheckout(product, 3);
console.log(`Checkout Type: ${checkout.type}`);
console.log(`Expected Type: TRAVELSIM`);
console.log(`Steps: ${checkout.steps.map(s => s.id).join(', ')}`);
console.log('--------------');

// Test 4: Link a product to a specific checkout method
console.log('\nTest 4: Link product to specific checkout method');
const linkResult = CheckoutManager.linkProductToMethod('document.license.conversion.vn2tw', 2);
console.log(`Linked SKU: ${linkResult.sku}`);
console.log(`To Checkout Method: ${linkResult.checkoutMethod}`);
console.log(`Status: ${linkResult.linked ? 'Linked' : 'Failed'}`);
console.log(`Timestamp: ${linkResult.timestamp}`);

// Test 5: Using the new Checkout System with specific implementations
console.log('\nTest 5: Using the Checkout System with specific implementations');
testProducts.forEach(product => {
  console.log(`Initializing checkout for: ${product.name}`);
  try {
    const checkoutConfig = CheckoutSystem.initCheckout(product);
    console.log(`Successfully initialized ${checkoutConfig.type} checkout`);
    console.log(`Steps: ${checkoutConfig.steps.length}`);
    console.log(`First step: ${checkoutConfig.steps[0].title}`);
    console.log(`Validation rules available: ${Object.keys(checkoutConfig.validation).join(', ')}`);
  } catch (error) {
    console.error(`Error initializing checkout: ${error.message}`);
  }
  console.log('--------------');
});

// Test 6: Testing Card checkout completion
console.log('\nTest 6: Testing Card checkout completion');
const cardCheckoutData = {
  pin: {
    phoneNumber: '**********',
    amount: 200000
  },
  payment: {
    method: 'bank_transfer',
    amount: 200000,
    currency: 'VND'
  }
};

try {
  const cardResult = CheckoutSystem.completeCheckout(1, cardCheckoutData);
  console.log(`Card checkout success: ${cardResult.success}`);
  console.log(`Transaction ID: ${cardResult.transactionId}`);
  console.log(`Payment status: ${cardResult.payment.status}`);
  console.log(`Top-up status: ${cardResult.pinOrTopup.status}`);
} catch (error) {
  console.error(`Error completing Card checkout: ${error.message}`);
}
console.log('--------------');

// Test 7: Testing SIM checkout completion
console.log('\nTest 7: Testing SIM checkout completion');
const simCheckoutData = {
  recipient: {
    name: 'Nguyen Van A',
    address: '123 Đường ABC, Quận XYZ, Đài Bắc, Đài Loan',
    phone: '**********'
  },
  documents: {
    passport: true,
    residenceCard: true
  },
  delivery: {
    method: 'tại nhà'
  },
  payment: {
    method: 'COD',
    amount: 350000,
    currency: 'VND'
  }
};

try {
  const simResult = CheckoutSystem.completeCheckout(2, simCheckoutData);
  console.log(`SIM checkout success: ${simResult.success}`);
  console.log(`Transaction ID: ${simResult.transactionId}`);
  console.log(`Order ID: ${simResult.order.orderId}`);
  console.log(`SIM Number: ${simResult.order.simNumber}`);
  console.log(`Estimated delivery: ${simResult.order.estimatedDelivery}`);
  console.log(`Tracking code: ${simResult.trackingCode}`);
} catch (error) {
  console.error(`Error completing SIM checkout: ${error.message}`);
}
console.log('--------------');

// Test 8: Testing Travel SIM checkout completion
console.log('\nTest 8: Testing Travel SIM checkout completion');
const travelSimCheckoutData = {
  destination: {
    code: 'JP',
    name: 'Nhật Bản'
  },
  simType: {
    type: 'eSIM'
  },
  duration: {
    days: 7,
    data: '10GB'
  },
  orderInfo: {
    name: 'Nguyen Van B',
    email: '<EMAIL>',
    phone: '0987654321',
    country: 'Vietnam'
  },
  payment: {
    method: 'thẻ tín dụng',
    amount: 450000,
    currency: 'VND'
  }
};

try {
  const travelSimResult = CheckoutSystem.completeCheckout(3, travelSimCheckoutData);
  console.log(`Travel SIM checkout success: ${travelSimResult.success}`);
  console.log(`Transaction ID: ${travelSimResult.transactionId}`);
  console.log(`Order type: ${travelSimResult.order.type}`);
  console.log(`Destination: ${travelSimResult.order.destination}`);
  console.log(`Duration: ${travelSimResult.order.duration} days`);
  console.log(`Data amount: ${travelSimResult.order.dataAmount}`);
  console.log(`Expiration date: ${travelSimResult.order.activationInfo.expirationDate}`);
} catch (error) {
  console.error(`Error completing Travel SIM checkout: ${error.message}`);
}

console.log('\n======== TEST COMPLETED ========'); 