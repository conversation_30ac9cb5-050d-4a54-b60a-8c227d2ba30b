import { fetchCoordinates } from './multistores';
// Define a class for a point in 2D space
class Point {
  constructor(x, y) {
    this.x = x;
    this.y = y;
  }

  // Calculate the Euclidean distance between two points
  distanceTo(other) {
    const dx = this.x - other.x;
    const dy = this.y - other.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
}

// Define a class for a quadtree node
class QuadTreeNode {
  constructor(bounds, capacity) {
    this.bounds = bounds; // The bounding box of this node
    this.capacity = capacity; // Maximum number of points this node can hold
    this.points = []; // Points contained within this node
    this.isLeaf = true; // Flag indicating whether this node is a leaf
    this.children = []; // Children nodes (if not a leaf)
  }

  // Insert a point into the quadtree
  insert(point) {
    if (!this.bounds.contains(point)) {
      return false; // Point is outside the bounds of this node
    }

    if (this.points.length < this.capacity) {
      this.points.push(point); // Add the point to this node
      return true;
    }

    if (this.isLeaf) {
      this.split(); // Split the node into four children
      this.isLeaf = false;
    }

    // Recursively insert the point into one of the children
    for (const child of this.children) {
      if (child.insert(point)) {
        return true;
      }
    }

    return false;
  }

  // Split the node into four children
  split() {
    const { x, y, width, height } = this.bounds;
    const halfWidth = width / 2;
    const halfHeight = height / 2;

    // Create four child nodes
    this.children.push(new QuadTreeNode(new Rectangle(x, y, halfWidth, halfHeight), this.capacity));
    this.children.push(new QuadTreeNode(new Rectangle(x + halfWidth, y, halfWidth, halfHeight), this.capacity));
    this.children.push(new QuadTreeNode(new Rectangle(x, y + halfHeight, halfWidth, halfHeight), this.capacity));
    this.children.push(new QuadTreeNode(new Rectangle(x + halfWidth, y + halfHeight, halfWidth, halfHeight), this.capacity));
  }

  // Query the quadtree for the closest points to a given point
  queryClosest(point, count, result = []) {
    if (!this.bounds.intersectsCircle(point, result[result.length - 1]?.distance || Number.POSITIVE_INFINITY)) {
      return result; // Stop traversal if the bounding box doesn't intersect with the circle
    }

    for (const p of this.points) {
      const distance = point.distanceTo(p);
      result.push({ point, distance });
      result.sort((a, b) => a.distance - b.distance);
      if (result.length > count) {
        result.pop(); // Keep only the closest 'count' points
      }
    }

    if (!this.isLeaf) {
      for (const child of this.children) {
        child.queryClosest(point, count, result); // Recursively query children
      }
    }

    return result;
  }
}

// Define a class for a rectangle
class Rectangle {
  constructor(x, y, width, height) {
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
  }

  // Check if a point is contained within the rectangle
  contains(point) {
    return (
      point.x >= this.x &&
      point.x <= this.x + this.width &&
      point.y >= this.y &&
      point.y <= this.y + this.height
    );
  }

  // Check if the rectangle intersects with a circle (represented by a point and radius)
  intersectsCircle(point, radius) {
    const dx = Math.abs(point.x - this.x - this.width / 2);
    const dy = Math.abs(point.y - this.y - this.height / 2);

    if (dx > this.width / 2 + radius) {
      return false;
    }
    if (dy > this.height / 2 + radius) {
      return false;
    }

    if (dx <= this.width / 2 || dy <= this.height / 2) {
      return true;
    }

    return Math.pow(dx - this.width / 2, 2) + Math.pow(dy - this.height / 2, 2) <= Math.pow(radius, 2);
  }
}

// Define the quadtree class
class Quadtree {
  constructor(bounds, capacity) {
    this.root = new QuadTreeNode(bounds, capacity);
  }

  // Insert a point into the quadtree
  insert(point) {
    return this.root.insert(point);
  }

  // Query the quadtree for the closest points to a given point
  queryClosest(point, count) {
    return this.root.queryClosest(point, count);
  }
}

// Function to build the quadtree with all coordinates
function buildQuadtree(allCoordinates, capacity) {
  const minX = Math.min(...allCoordinates.map(coord => coord.lat));
  const minY = Math.min(...allCoordinates.map(coord => coord.long));
  const maxX = Math.max(...allCoordinates.map(coord => coord.lat));
  const maxY = Math.max(...allCoordinates.map(coord => coord.long));

  const boundingBox = new Rectangle(minX, minY, maxX - minX, maxY - minY);
  const quadtree = new Quadtree(boundingBox, capacity);

  allCoordinates.forEach(coord => {
    quadtree.insert(new Point(coord.lat, coord.long));
  });

  return quadtree;
}

export async function getStaticProps() {
  // Fetch all coordinates from an API or any other source
  const allCoordinates = await fetchCoordinates();

  // Build the quadtree with all coordinates
  const quadtree = buildQuadtree(allCoordinates, 4);

  return {
    props: {
      quadtree: JSON.stringify(quadtree), // Serialize the quadtree for transfer
    },
  };
}

// Export the function to find closest coordinates
export function findClosestCoordinates(lat, long, count, quadtree) {
  if (!quadtree) {
    throw new Error('Quadtree not initialized. Call buildQuadtree() first.');
  }

  const myLocation = new Point(lat, long);
  const closestPoints = quadtree.queryClosest(myLocation, count);
  return closestPoints.map(item => ({ lat: item.point.x, long: item.point.y }));
}
