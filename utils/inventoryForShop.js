import { fetchInventory } from './inventoryProvider'
import { fetchStore } from './storeProvider'

async function fetchInventoryForShop(storeIdToFind) {
  try {
    const allstores = await fetchStore();
    const storeobject = allstores.find(store => store.storeId === storeIdToFind);
    let inventory
    if (storeobject) {
      inventory = storeobject.inventory || []; // Get inventory for the specified store
    }
    else {
      inventory = await fetchInventory()
    }
    return inventory;
  } catch (error) {
    console.error(error)
    return null;
  }
}

export default fetchInventoryForShop;
