import { FaMoneyBill, FaWallet, FaCreditCard, FaPaypal, FaCcVisa, FaCcMastercard } from "react-icons/fa";
import { paymentMethodsByCurrency } from "../components/paymentFormMapping";

// Define available currencies globally
export const AVAILABLE_CURRENCIES = [
  { code: "VND", symbol: "VND", name: "Vietnamese Dong" },
  { code: "USD", symbol: "USD", name: "US Dollar" },
  { code: "NT$", symbol: "NT$", name: "New Taiwan Dollar" },
];

// Currency-specific payment methods
export const getPaymentMethodsByCurrentcy = (currency) => {
  return paymentMethodsByCurrency[currency] || paymentMethodsByCurrency['default'];
};

// Helper to get all payment methods for all currencies
export const getAllPaymentMethods = () => {
  const allMethods = {};

  // Collect methods from all currencies
  AVAILABLE_CURRENCIES.forEach(currency => {
    const methodsForCurrency = getPaymentMethodsByCurrentcy(currency.code);
    methodsForCurrency.forEach(method => {
      if (!allMethods[method.id]) {
        allMethods[method.id] = {
          ...method,
          currencies: [currency.code],
          available: method.available
        };
      } else {
        if (!allMethods[method.id].currencies.includes(currency.code)) {
          allMethods[method.id].currencies.push(currency.code);
        }
      }
    });
  });

  return Object.values(allMethods);
};

// Helper to normalize currency codes
export const normalizeCurrency = (currency) => {
  if (!currency) return currency;

  // Handle Taiwan Dollar variations
  if (currency === 'NT' || currency === 'NT$' || currency === 'NTD' || currency === 'TWD') {
    return 'NT$';
  }

  return currency.toUpperCase(); // Normalize all currency codes to uppercase
};

// Helper to filter cart items by currency
export const filterCartByCurrency = (cart, currency) => {
  return cart.filter(item => normalizeCurrency(item.currency) === normalizeCurrency(currency));
}; 