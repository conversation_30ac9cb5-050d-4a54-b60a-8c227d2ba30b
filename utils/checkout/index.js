// Checkout Module System - Main Entry Point
const CheckoutManager = require('../checkout');
const ProductCheckoutMapping = require('../productCheckoutMapping');
const CardCheckout = require('./Card');
const SimCheckout = require('./Sim');
const TravelSimCheckout = require('./TravelSim');

/**
 * Checkout system that integrates the checkout manager and product mapping
 */
const CheckoutSystem = {
  /**
   * Initialize checkout for a product
   * @param {Object} product - The product to checkout
   * @param {Object} options - Checkout options
   * @returns {Object} - Checkout configuration
   */
  initCheckout: function(product, options = {}) {
    if (!product) {
      throw new Error("No product provided for checkout");
    }
    
    // Check if method is forced
    const forcedMethod = options.methodId || null;
    
    // Get method ID based on product and possible override
    const methodId = forcedMethod || ProductCheckoutMapping.getCheckoutMethod(product);
    
    // Get detailed checkout configuration from specific implementation
    let checkoutConfig;
    
    switch (parseInt(methodId)) {
      case 1: // CARD
        checkoutConfig = CardCheckout.process(product, options);
        break;
      case 2: // SIM
        checkoutConfig = SimCheckout.process(product, options);
        break;
      case 3: // TRAVELSIM
        checkoutConfig = TravelSimCheckout.process(product, options);
        break;
      default:
        throw new Error("Invalid checkout method ID");
    }
    
    // Add session information if provided
    if (options.sessionId) {
      checkoutConfig.sessionId = options.sessionId;
    }
    
    // Add user information if provided
    if (options.userId) {
      checkoutConfig.userId = options.userId;
    }
    
    // Initialize checkout session timestamp
    checkoutConfig.timestamp = new Date().toISOString();
    
    return checkoutConfig;
  },
  
  /**
   * Complete a checkout process
   * @param {number} methodId - Checkout method ID
   * @param {Object} checkoutData - Data collected during checkout
   * @returns {Object} - Checkout result
   */
  completeCheckout: function(methodId, checkoutData) {
    if (!checkoutData) {
      throw new Error("No checkout data provided");
    }
    
    switch (parseInt(methodId)) {
      case 1: // CARD
        return CardCheckout.completeCheckout(checkoutData);
      case 2: // SIM
        return SimCheckout.completeCheckout(checkoutData);
      case 3: // TRAVELSIM
        return TravelSimCheckout.completeCheckout(checkoutData);
      default:
        throw new Error("Invalid checkout method ID");
    }
  },
  
  /**
   * Get available checkout methods
   * @returns {Object} - Available methods
   */
  getAvailableMethods: function() {
    return CheckoutManager.methods;
  },
  
  /**
   * Link a product SKU to a specific checkout method
   * @param {string} sku - Product SKU
   * @param {number} methodId - Checkout method ID
   * @returns {Object} - Result of the operation
   */
  linkProduct: function(sku, methodId) {
    return ProductCheckoutMapping.setMapping(sku, methodId);
  },
  
  /**
   * Get checkout method for a product
   * @param {Object} product - The product
   * @returns {number} - Checkout method ID
   */
  getProductMethod: function(product) {
    return ProductCheckoutMapping.getCheckoutMethod(product);
  },
  
  /**
   * Get products using a specific checkout method
   * @param {number} methodId - Checkout method ID
   * @returns {Array} - SKUs using this method
   */
  getProductsByMethod: function(methodId) {
    return ProductCheckoutMapping.getSkusByMethod(methodId);
  },
  
  /**
   * Get checkout flow steps for a product
   * @param {Object} product - The product
   * @returns {Array} - Checkout steps
   */
  getCheckoutSteps: function(product) {
    const methodId = this.getProductMethod(product);
    
    switch (parseInt(methodId)) {
      case 1: // CARD
        return CardCheckout.getCheckoutSteps();
      case 2: // SIM
        return SimCheckout.getCheckoutSteps();
      case 3: // TRAVELSIM
        return TravelSimCheckout.getCheckoutSteps();
      default:
        throw new Error("Invalid checkout method ID");
    }
  },
  
  /**
   * Get checkout implementation for a method
   * @param {number} methodId - Method ID
   * @returns {Object} - Checkout implementation
   */
  getCheckoutImplementation: function(methodId) {
    switch (parseInt(methodId)) {
      case 1: // CARD
        return CardCheckout;
      case 2: // SIM
        return SimCheckout;
      case 3: // TRAVELSIM
        return TravelSimCheckout;
      default:
        throw new Error("Invalid checkout method ID");
    }
  }
};

module.exports = CheckoutSystem; 