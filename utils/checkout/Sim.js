// SIM Checkout Method Implementation
const SimCheckout = {
  /**
   * Process a SIM checkout
   * @param {Object} product - Product to be purchased
   * @param {Object} options - Additional options
   * @returns {Object} - Checkout configuration
   */
  process: function(product, options = {}) {
    // Validate product
    if (!product || !product.sku) {
      throw new Error("Invalid product for SIM checkout");
    }
    
    // Use the orderId from options if provided
    const orderId = options.orderId || null;
    
    // Build checkout configuration
    return {
      type: "SIM",
      sku: product.sku,
      price: product.price,
      currency: product.currency || "VND",
      steps: this.getCheckoutSteps(),
      config: this.getCheckoutConfig(options),
      validation: this.getValidationRules(),
      orderId: orderId // Include the orderId in the checkout configuration
    };
  },
  
  /**
   * Get checkout steps for SIM products
   * @returns {Array} - Checkout steps
   */
  getCheckoutSteps: function() {
    return [
      { 
        id: "recipient",
        title: "THÔNG TIN NGƯỜI NHẬN", 
        required: true,
        component: "RecipientForm",
        validation: ["required", "validAddress"]
      },
      { 
        id: "documents",
        title: "CUNG CẤP THÔNG TIN ĐẶT SIM", 
        required: true,
        component: "DocumentsUploadForm",
        documents: ["hộ chiếu", "thẻ cư trú đài loan", "thẻ khám bệnh", "VISA"],
        validation: ["required", "validDocuments"]
      },
      { 
        id: "delivery",
        title: "HÌNH THỨC NHẬN HÀNG", 
        required: true,
        component: "DeliveryMethodForm",
        options: ["cửa hàng tiện ích", "tại nhà"],
        validation: ["required", "validDeliveryMethod"]
      },
      {
        id: "payment",
        title: "THANH TOÁN",
        required: true,
        component: "PaymentMethodForm",
        validation: ["required", "validPayment"]
      },
      {
        id: "activation",
        title: "KÍCH HOẠT SIM",
        required: false,
        component: "ActivationInstructionsForm",
        validation: []
      }
    ];
  },
  
  /**
   * Get configuration for SIM checkout
   * @param {Object} options - Additional options
   * @returns {Object} - Checkout configuration
   */
  getCheckoutConfig: function(options = {}) {
    return {
      requiresVerification: true,
      trackingEnabled: true,
      activationReminder: true,
      expirationReminder: true,
      shippingAvailable: true,
      paymentOptions: {
        "TAIWAN": ["COD"],
        "VIETNAM": ["PREPAID"]
      },
      // Apply any overrides from options
      ...options
    };
  },
  
  /**
   * Get validation rules for SIM checkout
   * @returns {Object} - Validation rules
   */
  getValidationRules: function() {
    return {
      recipient: {
        required: true,
        validation: ["valid_name", "valid_address", "valid_phone"]
      },
      documents: {
        required: true,
        validation: ["valid_passport_or_id", "valid_residence_card"]
      },
      delivery: {
        required: true,
        validation: ["valid_delivery_method"]
      },
      payment: {
        required: true,
        validation: ["valid_payment_method"]
      },
      activation: {
        required: false
      }
    };
  },
  
  /**
   * Complete the checkout process
   * @param {Object} checkoutData - Data collected during checkout
   * @returns {Object} - Checkout result
   */
  completeCheckout: function(checkoutData) {
    // Validate checkout data
    if (!checkoutData || !checkoutData.recipient || !checkoutData.documents || !checkoutData.delivery || !checkoutData.payment) {
      return {
        success: false,
        error: "Missing required checkout information"
      };
    }
    
    // Verify documents
    const documentResult = this.verifyDocuments(checkoutData.documents);
    if (!documentResult.success) {
      return {
        success: false,
        error: "Document verification failed",
        details: documentResult
      };
    }
    
    // Process order - ensure orderId is passed to processOrder
    const orderResult = this.processOrder({
      ...checkoutData,
      orderId: checkoutData.orderId
    });
    
    // Process payment
    const paymentResult = this.processPayment(checkoutData.payment);
    
    // Return completion result
    return {
      success: orderResult.success && paymentResult.success,
      transactionId: `SIM-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      order: orderResult,
      payment: paymentResult,
      trackingCode: this.generateTrackingCode(),
      activationInstructions: this.getActivationInstructions(),
      timestamp: new Date().toISOString()
    };
  },
  
  /**
   * Verify customer documents
   * @param {Object} documents - Document data
   * @returns {Object} - Verification result
   */
  verifyDocuments: function(documents) {
    // This would verify documents in a real implementation
    // For this example, we'll just simulate validation
    const hasRequiredDocuments = documents.passport || documents.residenceCard;
    
    return {
      success: hasRequiredDocuments,
      verified: hasRequiredDocuments ? ["identity", "residency"] : [],
      status: hasRequiredDocuments ? "VERIFIED" : "MISSING_DOCUMENTS"
    };
  },
  
  /**
   * Process SIM order
   * @param {Object} orderData - Order data
   * @returns {Object} - Order processing result
   */
  processOrder: function(orderData) {
    // This would process the order in a real implementation
    return {
      success: true,
      orderId: orderData.orderId, // Use the existing orderId
      simNumber: `+886${Math.floor(********* + Math.random() * *********)}`,
      deliveryMethod: orderData.delivery.method,
      estimatedDelivery: this.calculateEstimatedDelivery(orderData.delivery.method),
      status: "PROCESSING"
    };
  },
  
  /**
   * Process payment
   * @param {Object} paymentData - Payment data
   * @returns {Object} - Payment result
   */
  processPayment: function(paymentData) {
    // This would connect to payment processor in real implementation
    const isCOD = paymentData.method === "COD";
    
    return {
      success: true,
      paymentMethod: paymentData.method,
      amount: paymentData.amount,
      currency: paymentData.currency || "VND",
      reference: `PAY-${Date.now()}`,
      status: isCOD ? "PENDING" : "APPROVED"
    };
  },
  
  /**
   * Calculate estimated delivery date
   * @param {string} deliveryMethod - Delivery method
   * @returns {string} - Estimated delivery date
   */
  calculateEstimatedDelivery: function(deliveryMethod) {
    const now = new Date();
    let daysToAdd = 1;
    
    if (deliveryMethod === "cửa hàng tiện ích") {
      daysToAdd = 2 + Math.floor(Math.random() * 3); // 2-4 days
    } else if (deliveryMethod === "tại nhà") {
      daysToAdd = 1 + Math.floor(Math.random() * 3); // 1-3 days
    }
    
    now.setDate(now.getDate() + daysToAdd);
    return now.toISOString().split('T')[0];
  },
  
  /**
   * Generate a tracking code
   * @returns {string} - Tracking code
   */
  generateTrackingCode: function() {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    
    for (let i = 0; i < 10; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    return `TRK-${result}`;
  },
  
  /**
   * Get SIM activation instructions
   * @returns {Object} - Activation instructions
   */
  getActivationInstructions: function() {
    return {
      steps: [
        "Khi nhận được SIM, chụp ảnh người cầm SIM cùng khuôn mặt",
        "Gửi hình ảnh vào hotline để kích hoạt SIM",
        "Sau khi kích hoạt, bạn sẽ nhận được tin nhắn xác nhận",
        "Lắp SIM vào điện thoại và bật dữ liệu di động"
      ],
      hotline: "+886-9-12345678",
      supportEmail: "<EMAIL>"
    };
  }
};

module.exports = SimCheckout; 