// Travel SIM Checkout Method Implementation
const TravelSimCheckout = {
  /**
   * Process a Travel SIM checkout
   * @param {Object} product - Product to be purchased
   * @param {Object} options - Additional options
   * @returns {Object} - Checkout configuration
   */
  process: function(product, options = {}) {
    // Validate product
    if (!product || !product.sku) {
      throw new Error("Invalid product for Travel SIM checkout");
    }
    
    // Build checkout configuration
    return {
      type: "TRAVELSIM",
      sku: product.sku,
      price: product.price,
      currency: product.currency || "VND",
      steps: this.getCheckoutSteps(),
      config: this.getCheckoutConfig(options),
      validation: this.getValidationRules()
    };
  },
  
  /**
   * Get checkout steps for Travel SIM products
   * @returns {Array} - Checkout steps
   */
  getCheckoutSteps: function() {
    return [
      { 
        id: "destination",
        title: "Chọn điểm đến", 
        required: true,
        component: "DestinationSelector",
        validation: ["required"]
      },
      { 
        id: "simType",
        title: "Chọn loại SIM phù hợp", 
        required: true,
        component: "SimTypeSelector",
        options: ["SIM vật lý", "eSIM"],
        validation: ["required"]
      },
      { 
        id: "duration",
        title: "Chọn thời gian sử dụng & dung lượng", 
        required: true,
        component: "DurationAndDataSelector",
        durations: [5, 7, 10, 15, 30],
        dataOptions: ["5GB", "10GB", "15GB", "Không giới hạn"],
        validation: ["required"]
      },
      {
        id: "orderInfo",
        title: "Nhập thông tin đặt hàng",
        required: true,
        component: "OrderInfoForm",
        validation: ["required", "validEmail", "validPhone"]
      },
      {
        id: "payment",
        title: "Thanh toán an toàn",
        required: true,
        component: "PaymentMethodForm",
        methods: ["thẻ ngân hàng", "ví điện tử", "thẻ tín dụng", "chuyển khoản"],
        validation: ["required", "validPayment"]
      },
      {
        id: "activation",
        title: "Kích hoạt SIM và sử dụng",
        required: false,
        component: "ActivationInstructionsForm",
        validation: []
      }
    ];
  },
  
  /**
   * Get configuration for Travel SIM checkout
   * @param {Object} options - Additional options
   * @returns {Object} - Checkout configuration
   */
  getCheckoutConfig: function(options = {}) {
    return {
      multiCountrySupport: true,
      esimSupport: true,
      automaticEmailConfirmation: true,
      shippingAvailable: true,
      paymentOptions: {
        "TAIWAN": ["COD"],
        "VIETNAM": ["COD", "PREPAID"]
      },
      // Countries supported
      destinations: [
        { code: "TH", name: "Thái Lan", continent: "Asia" },
        { code: "KR", name: "Hàn Quốc", continent: "Asia" },
        { code: "JP", name: "Nhật Bản", continent: "Asia" },
        { code: "SG", name: "Singapore", continent: "Asia" },
        { code: "MY", name: "Malaysia", continent: "Asia" },
        { code: "HK", name: "Hồng Kông", continent: "Asia" },
        { code: "EU", name: "Châu Âu", continent: "Europe", isRegion: true },
        { code: "USA", name: "Mỹ", continent: "North America" },
        { code: "AUS", name: "Úc", continent: "Oceania" },
        { code: "GLB", name: "Toàn cầu", isGlobal: true }
      ],
      // Apply any overrides from options
      ...options
    };
  },
  
  /**
   * Get validation rules for Travel SIM checkout
   * @returns {Object} - Validation rules
   */
  getValidationRules: function() {
    return {
      destination: {
        required: true,
        validation: ["valid_country_code"]
      },
      simType: {
        required: true,
        validation: ["valid_sim_type"]
      },
      duration: {
        required: true,
        validation: ["valid_duration", "valid_data_plan"]
      },
      orderInfo: {
        required: true,
        validation: ["valid_name", "valid_email", "valid_phone"]
      },
      payment: {
        required: true,
        validation: ["valid_payment_method"]
      },
      activation: {
        required: false
      }
    };
  },
  
  /**
   * Complete the checkout process
   * @param {Object} checkoutData - Data collected during checkout
   * @returns {Object} - Checkout result
   */
  completeCheckout: function(checkoutData) {
    // Validate checkout data
    if (!checkoutData || !checkoutData.destination || !checkoutData.simType || 
        !checkoutData.duration || !checkoutData.orderInfo || !checkoutData.payment) {
      return {
        success: false,
        error: "Missing required checkout information"
      };
    }
    
    // Process order based on SIM type
    const isESim = checkoutData.simType.type === "eSIM";
    const orderResult = isESim 
      ? this.processESimOrder(checkoutData) 
      : this.processPhysicalSimOrder(checkoutData);
    
    // Process payment
    const paymentResult = this.processPayment(checkoutData.payment);
    
    // Return completion result
    return {
      success: orderResult.success && paymentResult.success,
      transactionId: `TSIM-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      order: orderResult,
      payment: paymentResult,
      trackingInfo: orderResult.trackingInfo,
      activationInfo: orderResult.activationInfo,
      timestamp: new Date().toISOString()
    };
  },
  
  /**
   * Process eSIM order
   * @param {Object} orderData - Order data
   * @returns {Object} - Order processing result
   */
  processESimOrder: function(orderData) {
    // Generate QR code data (in real implementation, this would come from provider)
    const qrCodeData = `ESIM:${Date.now()}:${orderData.destination.code}:${orderData.duration.days}:${orderData.duration.data}`;
    
    return {
      success: true,
      type: "eSIM",
      orderId: `ESIM-${Date.now()}`,
      destination: orderData.destination.code,
      duration: orderData.duration.days,
      dataAmount: orderData.duration.data,
      activationInfo: {
        qrCode: qrCodeData,
        instructions: this.getESimActivationInstructions(),
        expirationDate: this.calculateExpirationDate(orderData.duration.days)
      },
      status: "READY",
      deliveryMethod: "Email",
      emailSent: true,
      emailAddress: orderData.orderInfo.email
    };
  },
  
  /**
   * Process physical SIM order
   * @param {Object} orderData - Order data
   * @returns {Object} - Order processing result
   */
  processPhysicalSimOrder: function(orderData) {
    // In real implementation, this would create fulfillment order
    const trackingCode = this.generateTrackingCode();
    
    return {
      success: true,
      type: "Physical SIM",
      orderId: `PSIM-${Date.now()}`,
      destination: orderData.destination.code,
      duration: orderData.duration.days,
      dataAmount: orderData.duration.data,
      trackingInfo: {
        trackingCode: trackingCode,
        carrier: this.selectCarrier(orderData.orderInfo.country),
        estimatedDelivery: this.calculateEstimatedDelivery(orderData.orderInfo.country),
        deliveryStatus: "PROCESSING"
      },
      activationInfo: {
        instructions: this.getPhysicalSimActivationInstructions(),
        expirationDate: this.calculateExpirationDate(orderData.duration.days)
      },
      status: "PROCESSING",
      deliveryMethod: "Shipping",
      shippingAddress: orderData.orderInfo.address
    };
  },
  
  /**
   * Process payment
   * @param {Object} paymentData - Payment data
   * @returns {Object} - Payment result
   */
  processPayment: function(paymentData) {
    // This would connect to payment processor in real implementation
    const isCOD = paymentData.method === "COD";
    
    return {
      success: true,
      paymentMethod: paymentData.method,
      amount: paymentData.amount,
      currency: paymentData.currency || "VND",
      reference: `PAY-${Date.now()}`,
      status: isCOD ? "PENDING" : "APPROVED"
    };
  },
  
  /**
   * Calculate estimated delivery date
   * @param {string} country - Delivery country
   * @returns {string} - Estimated delivery date
   */
  calculateEstimatedDelivery: function(country) {
    const now = new Date();
    let daysToAdd = 1;
    
    // Different delivery estimates by country
    if (country === "TW" || country === "Taiwan") {
      daysToAdd = 1 + Math.floor(Math.random() * 2); // 1-2 days
    } else if (country === "VN" || country === "Vietnam") {
      daysToAdd = 2 + Math.floor(Math.random() * 3); // 2-4 days
    } else {
      daysToAdd = 5 + Math.floor(Math.random() * 5); // 5-9 days (international)
    }
    
    now.setDate(now.getDate() + daysToAdd);
    return now.toISOString().split('T')[0];
  },
  
  /**
   * Calculate service expiration date
   * @param {number} days - Duration in days
   * @returns {string} - Expiration date
   */
  calculateExpirationDate: function(days) {
    const now = new Date();
    now.setDate(now.getDate() + parseInt(days));
    return now.toISOString().split('T')[0];
  },
  
  /**
   * Select shipping carrier based on country
   * @param {string} country - Delivery country
   * @returns {string} - Selected carrier
   */
  selectCarrier: function(country) {
    // Different carriers by country
    if (country === "TW" || country === "Taiwan") {
      return "Taiwan Post";
    } else if (country === "VN" || country === "Vietnam") {
      return "Vietnam Post";
    } else {
      return "DHL International";
    }
  },
  
  /**
   * Generate a tracking code
   * @returns {string} - Tracking code
   */
  generateTrackingCode: function() {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    
    for (let i = 0; i < 12; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    return result;
  },
  
  /**
   * Get eSIM activation instructions
   * @returns {Array} - Activation instructions
   */
  getESimActivationInstructions: function() {
    return [
      "Quét mã QR đã được gửi đến email của bạn",
      "Trong phần Cài đặt > Di động > Thêm gói cước di động hoặc eSIM",
      "Làm theo hướng dẫn trên màn hình điện thoại",
      "Kích hoạt eSIM trước hoặc khi đến nơi",
      "Bật dữ liệu di động và chuyển vùng quốc tế khi đến nơi"
    ];
  },
  
  /**
   * Get physical SIM activation instructions
   * @returns {Array} - Activation instructions
   */
  getPhysicalSimActivationInstructions: function() {
    return [
      "Lắp SIM vào điện thoại khi đến nơi",
      "Bật dữ liệu di động và chuyển vùng quốc tế",
      "SIM sẽ tự động kết nối với mạng tại điểm đến",
      "Nếu không tự kết nối, vui lòng chọn mạng thủ công trong phần Cài đặt mạng"
    ];
  }
};

module.exports = TravelSimCheckout; 