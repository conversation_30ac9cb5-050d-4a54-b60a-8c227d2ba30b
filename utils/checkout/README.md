# Checkout Management Module

## Overview
This module provides a flexible checkout system with three distinct checkout methods tailored for different product categories:

1. **Card Checkout (Method 1)** - For products with CARD category
2. **SIM Checkout (Method 2)** - For products with SIM category 
3. **Travel SIM Checkout (Method 3)** - For products with TRAVELSIM category

## Structure
The checkout system consists of:
- Main checkout manager (`../checkout.js`)
- Product to checkout method mapping (`../productCheckoutMapping.js`)
- Implementation-specific checkout handlers:
  - `Card.js`: Handles card products checkout flow
  - `Sim.js`: Handles SIM products checkout flow
  - `TravelSim.js`: Handles travel SIM products checkout flow
- Main entry point (`index.js`): Integrates all components and exposes public API

## Usage

### Initialize a Checkout
```javascript
const CheckoutSystem = require('./utils/checkout');

// Product example
const product = {
  sku: "document.license.conversion.vn2tw",
  name: "<PERSON><PERSON><PERSON> Bằng <PERSON>i <PERSON>",
  categories: ["TaiwanServices", "License", "CARD"],
  price: 1800000,
  currency: "VND"
};

// Initialize checkout with automatic method detection
const checkoutConfig = CheckoutSystem.initCheckout(product);

// Initialize with specific method
const checkoutConfig = CheckoutSystem.initCheckout(product, { methodId: 1 });

// With additional options
const checkoutConfig = CheckoutSystem.initCheckout(product, { 
  sessionId: 'user-session-123', 
  userId: 'user-456'
});

// Get checkout steps
const steps = CheckoutSystem.getCheckoutSteps(product);
```

### Complete a Checkout
```javascript
// Example for Card checkout (Method 1)
const checkoutData = {
  pin: {
    phoneNumber: '**********',
    amount: 50000
  },
  payment: {
    method: 'bank_transfer',
    amount: 50000,
    currency: 'VND'
  }
};

const result = CheckoutSystem.completeCheckout(1, checkoutData);
```

### Link Products to Checkout Methods
```javascript
// Link a product SKU to a specific checkout method
CheckoutSystem.linkProduct('document.license.conversion.vn2tw', 1);

// Get method for a product
const methodId = CheckoutSystem.getProductMethod(product);

// Get products using a specific method
const products = CheckoutSystem.getProductsByMethod(1);
```

## Checkout Methods Details

### Method 1: CARD
- PIN code generation or phone top-up
- Customer account management
- Payment through multiple channels
- No shipping service available
- PIN inventory management
- Previous transactions view
- Loyalty points system

### Method 2: SIM
- Recipient information collection
- Document verification for SIM registration
- Multiple delivery options
- SIM activation process
- Order tracking
- Activation reminder
- Expiration notifications
- Shipping in Taiwan (COD) and Vietnam (Prepaid)

### Method 3: TRAVELSIM
- Destination selection
- SIM type selection (Physical or eSIM)
- Duration and data allowance selection
- Order information collection
- Multiple payment methods
- Activation instructions
- Support for multiple countries
- Automated email confirmation
- Different shipping options per country

## Extending the System
To add a new checkout method:

1. Create a new implementation file (e.g., `NewMethod.js`)
2. Update the method mappings in `../checkout.js`
3. Add the new method to the product mapping in `../productCheckoutMapping.js`
4. Update the switch statements in `index.js`

## License
Internal use only 