import { FaCheck, FaTimes, FaClock, FaShoppingCart } from 'react-icons/fa';
import { STATUS_MAPPINGS, PAYMENT_STATUS_MAPPINGS } from '../constants/adminConstants';

// Date formatting utility
export const formatDate = (dateString) => {
  try {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (err) {
    return dateString || 'N/A';
  }
};

// Currency formatting utility
export const formatCurrency = (amount, currency = 'VND') => {
  if (!amount) return '0';
  
  if (currency === 'NT$' || currency === 'NT') {
    return `${amount.toLocaleString()} NT$`;
  }
  
  return `${amount.toLocaleString('vi-VN')} ${currency}`;
};

// Status color utilities
export const getStatusColor = (status) => {
  if (!status) return 'text-gray-600 bg-gray-100';
  
  switch (status.toLowerCase()) {
    case 'paid':
      return 'text-green-600 bg-green-100';
    case 'pending':
      return 'text-yellow-600 bg-yellow-100';
    case 'expired':
    case 'cancelled':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

export const getPaymentStatusColor = (status) => {
  if (!status) return 'text-gray-600 bg-gray-100';
  
  switch (status.toLowerCase()) {
    case 'paid':
      return 'text-green-600 bg-green-100';
    case 'not_paid':
      return 'text-yellow-600 bg-yellow-100';
    case 'failed':
    case 'refunded':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

// Status icon utilities
export const getStatusIcon = (status) => {
  if (!status) return FaShoppingCart;
  
  switch (status.toLowerCase()) {
    case 'paid':
      return FaCheck;
    case 'pending':
      return FaClock;
    case 'expired':
    case 'cancelled':
      return FaTimes;
    default:
      return FaShoppingCart;
  }
};

// Status text utilities
export const getStatusText = (status) => {
  if (!status) return 'Không xác định';
  return STATUS_MAPPINGS[status.toLowerCase()] || status;
};

export const getPaymentStatusText = (status) => {
  if (!status) return 'Không xác định';
  return PAYMENT_STATUS_MAPPINGS[status.toLowerCase()] || status;
};

// Time remaining utility
export const getTimeRemaining = (validUntil) => {
  const now = new Date();
  const expiryDate = new Date(validUntil);
  
  if (expiryDate <= now) {
    return 'Đã hết hạn';
  }
  
  const diffMs = expiryDate.getTime() - now.getTime();
  const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  if (diffHrs > 24) {
    const days = Math.floor(diffHrs / 24);
    return `${days} ngày ${diffHrs % 24} giờ`;
  }
  
  return `${diffHrs} giờ ${diffMins} phút`;
};
