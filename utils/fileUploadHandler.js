import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';

// Promisify fs functions
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Configuration
let UPLOAD_DIR = process.env.DOCUMENT_STORAGE_PATH || '/data/documents/customers';
const DOCUMENT_TYPES = [
  'idCard', 
  'passport', 
  'driverLicense', 
  'birthCertificate', 
  'photo', 
  'address',
  'businessRegistration',
  'taxRegistration',
  'bankStatement',
  'insuranceCard',
  'marriageCertificate',
  'workPermit',
  'academicDegree',
  'utilityBill',
  'propertyOwnership',
  'other'
];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

/**
 * Ensure the upload directories exist
 */
async function ensureUploadDirectories() {
  try {
    console.log('Ensuring upload directories exist...');
    // Check if the base upload directory exists
    if (!await existsAsync(UPLOAD_DIR)) {
      console.log(`Creating base directory: ${UPLOAD_DIR}`);
      try {
        await mkdirAsync(UPLOAD_DIR, { recursive: true });
      } catch (error) {
        console.error(`Error creating directory ${UPLOAD_DIR}:`, error);
        // Fall back to a public directory if we can't write to the specified path
        if (!process.env.DOCUMENT_STORAGE_PATH) {
          console.log('Falling back to data directory');
          UPLOAD_DIR = '/data/documents/customers';
          await mkdirAsync(UPLOAD_DIR, { recursive: true });
        } else {
          throw error;
        }
      }
    }
  } catch (error) {
    console.error('Error ensuring upload directories:', error);
    throw error;
  }
}

// Initialize upload directories
ensureUploadDirectories().catch(console.error);

// Configure multer storage
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      // Get document type and customer ID from request
      const documentType = req.body.documentType || req.query.documentType;
      const customerId = req.body.customerId || req.query.customerId || 'unknown';
      
      console.log('Storage destination params:', { documentType, customerId });
      
      // Validate document type - we'll be more lenient for testing
      if (!documentType) {
        return cb(new Error('Missing document type'));
      }
      
      // Set the destination directory - store in customer-specific folder
      const customerDir = path.join(UPLOAD_DIR, customerId);
      
      // Ensure the customer directory exists
      if (!await existsAsync(customerDir)) {
        console.log(`Creating customer directory: ${customerDir}`);
        await mkdirAsync(customerDir, { recursive: true });
      }
      
      console.log(`Using destination directory: ${customerDir}`);
      cb(null, customerDir);
    } catch (error) {
      console.error('Error determining upload destination:', error);
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generate a unique filename with original extension
    const originalExt = path.extname(file.originalname);
    const uniqueFilename = `${uuidv4()}${originalExt}`;
    console.log(`Generated filename: ${uniqueFilename}`);
    cb(null, uniqueFilename);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Check file type (allow images and PDFs)
  const allowedMimeTypes = [
    'image/jpeg',
    'image/png', 
    'image/webp',
    'application/pdf'
  ];
  
  console.log(`Validating file type: ${file.mimetype}`);
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Invalid file type: ${file.mimetype}. Only JPEG, PNG, WEBP images and PDFs are allowed.`));
  }
};

// Configure multer upload
export const uploadMiddleware = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE
  }
});

/**
 * Delete a document file
 * @param {string} customerId - The customer ID
 * @param {string} filename - The filename of the document
 * @returns {Promise<boolean>} - Whether deletion was successful
 */
export async function deleteDocument(customerId, filename) {
  try {
    // Sanitize filename to prevent directory traversal
    const sanitizedFilename = path.basename(filename);
    const filePath = path.join(UPLOAD_DIR, customerId, sanitizedFilename);
    
    // Check if file exists
    if (!await existsAsync(filePath)) {
      return false;
    }
    
    // Delete the file
    await promisify(fs.unlink)(filePath);
    return true;
  } catch (error) {
    console.error('Error deleting document:', error);
    return false;
  }
}

export default {
  uploadMiddleware,
  deleteDocument,
  DOCUMENT_TYPES,
  UPLOAD_DIR
}; 