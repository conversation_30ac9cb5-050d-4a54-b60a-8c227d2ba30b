import { slugify } from './helpers';
import { fetchInventory } from './inventoryProvider';
import { fetchStore } from './storeProvider';
import categorygroupings from './categoryGrouping';

async function fetchCategoriesOneShop(storeIdToFind) {
  let inventory;
  let stores = await fetchStore();
  
  const storeobject = stores.find(store => store.storeId === storeIdToFind);
  if (storeobject) {            
    inventory = (storeobject.inventory || []).filter(item => item.activestatus === "1");
  } else {
    inventory = await fetchInventory();
  }
  
  const categories = inventory.reduce((acc, next) => {
    next.categories.forEach(category => {
      const slug = slugify(category);
      if (!acc.some(obj => obj.slug === slug)) {
        acc.push({ slug, name: category });   //I return the sluggified category and the original category as well
      }
    });
    return acc;
  }, []);
  
  // Define a function to get the bucket name for a given category
  function getCategoryBucketName(category) {
    for (const [bucket, values] of Object.entries(categorygroupings)) {
      if (values.includes(category)) {
        return bucket;
      }
    }
    return null; // Return null if category doesn't belong to any bucket
  }
  
  // Sort categories into buckets based on categorygroupings
  const sortedCategories = {};
  for (const [bucket, values] of Object.entries(categorygroupings)) {
    sortedCategories[bucket] = categories.filter(cat => getCategoryBucketName(cat.name) === bucket);
    sortedCategories[bucket].sort((a, b) => a.name.localeCompare(b.name)); // Sort within each bucket
  }
  
  // Get categories that don't belong to any bucket
  const uncategorizedCategories = categories.filter(cat => getCategoryBucketName(cat.name) === null);
  uncategorizedCategories.sort((a, b) => a.name.localeCompare(b.name)); // Sort uncategorized categories
  
  // Concatenate sorted categories from buckets and uncategorized categories
  const finalCategories = Object.values(sortedCategories).reduce((acc, bucketCategories) => {
    acc.push(...bucketCategories);
    return acc;
  }, []);
  finalCategories.push(...uncategorizedCategories); // Append uncategorized categories to the end

  return Promise.resolve(finalCategories);
}

export default fetchCategoriesOneShop;
