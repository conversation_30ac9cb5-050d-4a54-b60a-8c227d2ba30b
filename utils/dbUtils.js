import { MongoClient } from 'mongodb';

// Connection URL and Database Name
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = process.env.DB_NAME || 'mag-shop';

// Global variable to cache the database connection
let cachedDb = null;

/**
 * Connect to MongoDB database
 * 
 * This function manages a cached connection to reduce connection overhead
 * for serverless functions.
 */
export async function connectToDatabase() {
  // If the database connection is cached, use it
  if (cachedDb) {
    return { client: cachedDb.client, db: cachedDb.db };
  }
  
  // Connect to MongoDB
  const client = await MongoClient.connect(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
  
  // Select the database
  const db = client.db(DB_NAME);
  
  // Cache the database connection
  cachedDb = { client, db };
  
  return { client, db };
}

/**
 * Get a collection from the database
 * 
 * @param {string} collectionName - The name of the collection
 * @returns {Collection} The MongoDB collection
 */
export async function getCollection(collectionName) {
  const { db } = await connectToDatabase();
  return db.collection(collectionName);
}

/**
 * Find documents in a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} query - The query to find documents
 * @param {Object} options - Additional options like sorting, limit, etc.
 * @returns {Array} Array of documents matching the query
 */
export async function findDocuments(collectionName, query = {}, options = {}) {
  const collection = await getCollection(collectionName);
  
  let cursor = collection.find(query);
  
  // Apply options
  if (options.sort) cursor = cursor.sort(options.sort);
  if (options.limit) cursor = cursor.limit(options.limit);
  if (options.skip) cursor = cursor.skip(options.skip);
  if (options.projection) cursor = cursor.project(options.projection);
  
  return await cursor.toArray();
}

/**
 * Find a single document in a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} query - The query to find the document
 * @param {Object} options - Additional options like projection
 * @returns {Object} The document matching the query, or null if not found
 */
export async function findDocument(collectionName, query, options = {}) {
  const collection = await getCollection(collectionName);
  return await collection.findOne(query, options);
}

/**
 * Insert a document into a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} document - The document to insert
 * @returns {Object} Result of the insert operation
 */
export async function insertDocument(collectionName, document) {
  const collection = await getCollection(collectionName);
  return await collection.insertOne(document);
}

/**
 * Insert multiple documents into a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Array} documents - The documents to insert
 * @returns {Object} Result of the insert operation
 */
export async function insertDocuments(collectionName, documents) {
  const collection = await getCollection(collectionName);
  return await collection.insertMany(documents);
}

/**
 * Update a document in a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} query - The query to find the document
 * @param {Object} update - The update operations to apply
 * @param {Object} options - Additional options like upsert
 * @returns {Object} Result of the update operation
 */
export async function updateDocument(collectionName, query, update, options = {}) {
  const collection = await getCollection(collectionName);
  return await collection.updateOne(query, update, options);
}

/**
 * Update multiple documents in a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} query - The query to find documents
 * @param {Object} update - The update operations to apply
 * @param {Object} options - Additional options like upsert
 * @returns {Object} Result of the update operation
 */
export async function updateDocuments(collectionName, query, update, options = {}) {
  const collection = await getCollection(collectionName);
  return await collection.updateMany(query, update, options);
}

/**
 * Delete a document from a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} query - The query to find the document to delete
 * @returns {Object} Result of the delete operation
 */
export async function deleteDocument(collectionName, query) {
  const collection = await getCollection(collectionName);
  return await collection.deleteOne(query);
}

/**
 * Delete multiple documents from a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} query - The query to find documents to delete
 * @returns {Object} Result of the delete operation
 */
export async function deleteDocuments(collectionName, query) {
  const collection = await getCollection(collectionName);
  return await collection.deleteMany(query);
}

/**
 * Log an operation to the database
 * 
 * @param {string} action - The action being logged
 * @param {Object} details - Details about the action
 * @returns {Object} Result of the insert operation
 */
export async function logOperation(action, details = {}) {
  const logEntry = {
    action,
    details,
    timestamp: new Date(),
    environment: process.env.NODE_ENV || 'development'
  };
  
  return await insertDocument('logs', logEntry);
}

/**
 * Count documents in a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Object} query - The query to count documents
 * @returns {number} Number of documents matching the query
 */
export async function countDocuments(collectionName, query = {}) {
  const collection = await getCollection(collectionName);
  return await collection.countDocuments(query);
}

/**
 * Create indexes for a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Array} indexes - Array of index specifications
 * @returns {Array} Result of the createIndexes operation
 */
export async function createIndexes(collectionName, indexes) {
  const collection = await getCollection(collectionName);
  return await collection.createIndexes(indexes);
}

/**
 * Perform an aggregation on a collection
 * 
 * @param {string} collectionName - The name of the collection
 * @param {Array} pipeline - The aggregation pipeline
 * @param {Object} options - Additional options for the aggregation
 * @returns {Array} Result of the aggregation
 */
export async function aggregate(collectionName, pipeline, options = {}) {
  const collection = await getCollection(collectionName);
  return await collection.aggregate(pipeline, options).toArray();
}

// Default export with all database utilities
export default {
  connectToDatabase,
  getCollection,
  findDocuments,
  findDocument,
  insertDocument,
  insertDocuments,
  updateDocument,
  updateDocuments,
  deleteDocument,
  deleteDocuments,
  logOperation,
  countDocuments,
  createIndexes,
  aggregate,
}; 