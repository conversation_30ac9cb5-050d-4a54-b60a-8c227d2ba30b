import { v4 as uuid } from "uuid"
import { fetchInventory } from "./inventoryProvider"
import path from "path"
import categorygroupings from "./categoryGrouping"
import multistores from "./multistores.json"

const QRCode = require("qrcode")

// Conditionally import canvas only in Node.js environment
let createCanvas, loadImage
if (typeof window === 'undefined') {
  try {
    const canvas = require('canvas')
    createCanvas = canvas.createCanvas
    loadImage = canvas.loadImage
  } catch (error) {
    console.warn('Canvas module not available, QR codes will be generated without logos')
    createCanvas = null
    loadImage = null
  }
}


multistores.map((i) => {
  i.id = uuid()
  return i
})

async function addQRCodeToStores(multistores) {
  for (const store of multistores) {
    if (store.activestatus === "1" && store.storeId) {
      try {
        // Generate the QR code data URL
        // const qrDataURL = await QRCode.toDataURL("https://shopme.top/" + store.storeId);
        const qrDataURL = await QRCode.toDataURL(
          "https://sim.dailoanshop.net/" + store.stickystoreId
        )

        // If canvas is not available or no logo, just assign the QR code without the composite image
        if (!createCanvas || !loadImage || !store.logo || !Array.isArray(store.logo) || store.logo.length === 0) {
          store.shopLinkQRCode = qrDataURL;
          continue;
        }

        // Define canvas dimensions (wide canvas with two square boxes)
        const canvasWidth = 600
        const canvasHeight = 300
        const squareSize = canvasHeight // Square size is equal to canvas height

        // Create canvas
        const canvas = createCanvas(canvasWidth, canvasHeight)
        const ctx = canvas.getContext("2d")

        // Draw border around the canvas
        const borderWidth = 2 // Adjust border width as needed
        ctx.lineWidth = borderWidth
        ctx.strokeRect(0, 0, canvasWidth, canvasHeight)

        // Load QR code image first
        const qrImage = await loadImage(qrDataURL)

        // Calculate dimensions and position for QR code
        const qrWidth = squareSize * 0.8 // Set QR code width to 80% of square size
        const qrHeight = (qrWidth / qrImage.width) * qrImage.height // Maintain aspect ratio
        const qrX =
          (canvasWidth + squareSize) / 2 + squareSize * 0.1 - qrWidth / 2 - 20 // Center horizontally in the right half of the canvas with right margin
        const qrY = (canvasHeight - qrHeight) / 2 // Center vertically

        // Draw QR code
        ctx.drawImage(qrImage, qrX, qrY, qrWidth, qrHeight)

        // Skip logo processing if we can't load it
        try {
          // Try to load the overlay image (logo)
          let overlayImage = null;
          
          // Try CDN URL first if the logo starts with http
          if (store.logo[0].startsWith('http')) {
            try {
              overlayImage = await loadImage(store.logo[0]);
            } catch (err) {
              console.error("Failed to load logo from URL:", err);
            }
          } 
          
          // If we couldn't load the logo, just use the QR code without it
          if (!overlayImage) {
            store.shopLinkQRCode = qrDataURL;
            continue;
          }

          // Calculate position for overlay image (center both horizontally and vertically within its box)
          const overlayImageWidth = squareSize * 0.5 // Set image width to 50% of square size
          const overlayImageHeight =
            (overlayImageWidth / overlayImage.width) * overlayImage.height // Maintain aspect ratio
          const overlayImageX =
            (canvasWidth - squareSize) / 4 - overlayImageWidth / 2 + 20 // Center horizontally within the left-side box with left margin
          const overlayImageY = (canvasHeight - overlayImageHeight) / 2 // Center vertically within the left-side box

          // Draw overlay image
          ctx.drawImage(
            overlayImage,
            overlayImageX,
            overlayImageY,
            overlayImageWidth,
            overlayImageHeight
          )
        } catch (err) {
          console.error("Error processing logo:", err);
          // Continue without the logo
        }

        // Convert canvas content to data URL
        const compositeDataURL = canvas.toDataURL()

        // Assign composite data URL to the store object
        store.shopLinkQRCode = compositeDataURL
      } catch (err) {
        console.error("Error generating QR code:", err)
      }
    }
  }

  return multistores
}

function getPriceBand_smallsteps(number) {
  const index = Math.floor(number / 100000)
  return categorygroupings.pricebands[index] || "3000k+"
}

function getPriceBand(number) {
  const index = Math.floor(number / 200000)
  return categorygroupings.pricebands[index] || "+++++"
}

function mergeInventoryWithBase(multistores, baseInventory) {
  // let updatedMultistores = [...multistores];
  let updatedMultistores = multistores.filter(
    (store) => store.activestatus === "1"
  )

  // Two steps process:
  // Step 1: clean this array, to delete the inactive products
  updatedMultistores.forEach((store) => {
    if (store.activestatus === "1") {
      store.inventory = store.inventory.filter((item) => {
        // Find the product with the same SKU in baseInventory
        let product = baseInventory.find(
          (product) => product.sku === item.sku && product.activestatus === "1"
        )
        // Keep the item in store.inventory if the product is found
        return product
      })
    }
  })

  function countPriceBands(store) {
    const priceBands = new Set()
    store.inventory.forEach((item) => {
      let product = baseInventory.find(
        (product) => product.sku === item.sku && product.activestatus === "1"
      )
      if (product) {
        let priceBand = getPriceBand(product.price)
        // console.log('priceBand: ');
        // console.log(priceBand);
        priceBands.add(priceBand)
      }
    })

    // console.log(priceBands);
    return priceBands.size // Return the size of the set, which represents the count of unique price bands
  }

  updatedMultistores.forEach((store) => {
    if (store.activestatus === "1") {
    /* if (store.activestatus === "1" && (store.nopriceband && store.nopriceband !== "1")) { */
      let pricebandforthisshop = []
      if (store.nopriceband && store.nopriceband !== "1") {
        pricebandforthisshop = countPriceBands(store)
      }
      
      // console.log(pricebandforthisshop);
      store.inventory.forEach((item) => {
        let product = baseInventory.find(
          (product) => product.sku === item.sku && product.activestatus === "1"
        )
        if (product) {
          // Assign all properties from the base inventory item to the store's inventory item
          Object.assign(item, product)
          if (pricebandforthisshop > 2) {
            item.priceband = getPriceBand(item.price)
            if (Array.isArray(item.categories) && item.categories.length > 0) {
              if (!item.categories.includes(item.priceband)) {
                item.categories = [...item.categories, item.priceband]
              }
            } else {
              item.categories = [item.priceband]
            }
          }
          if (
            item.customizations &&
            Object.keys(item.customizations).length > 0
          ) {
            Object.keys(item.customizations).forEach((key) => {
              if (item.hasOwnProperty(key)) {
                item[key] = item.customizations[key]
              }
            })
          }
        }
      })
    }
  })

  // Filter out items with activestatus "0"
  updatedMultistores.forEach((store) => {
    store.inventory = store.inventory.filter(
      (item) => item.activestatus !== "0"
    )
  })

  return updatedMultistores // Return the modified multistores
}

// Function to fetch coordinates of all shops/stores
function fetchCoordinates() {
  // Extract and return coordinates from multistores array
  return updatedMultistores.map((store) => ({
    lat: parseFloat(store.locations[0].latlong.split(",")[0]),
    long: parseFloat(store.locations[0].latlong.split(",")[1]),
  }))
}

const buildStickystoreIds = (stores) => {
  return stores.map((store) => ({
    id: store.stickystoreId,
    storeId: store.storeId,
  }))
}

let stickystoreIds = buildStickystoreIds(multistores)

// console.log(stickystoreIds);

// Initialize with base multistores data
let updatedMultistores = multistores

// Add the new stores configuration
const storeConfigs = [
];

// Merge with existing stores if needed
// Instead of redefining updatedMultistores, we'll enhance it
// This code merges the new store configurations with existing stores
// For each store in updatedMultistores:
// 1. It looks for a matching store in storeConfigs by comparing store IDs
// 2. If a match is found, it merges the existing store data with the new config
// 3. If no match is found, it keeps the original store data unchanged
updatedMultistores = updatedMultistores.map(store => {
  // Try to find matching store in new config
  const newConfig = storeConfigs.find(config => config.id === store.storeId);
  if (newConfig) {
    return { ...store, ...newConfig };
  }
  return store;
});

// Async function to build complete store data
async function buildUpdatedMultistores() {
  const filteredinventory = await fetchInventory()
  let stores = mergeInventoryWithBase(multistores, filteredinventory)
  stores = await addQRCodeToStores(stores)
  
  // Apply store configs
  stores = stores.map(store => {
    const config = storeConfigs.find(config => config.storeId === store.storeId)
    if (config) {
      return { ...store, ...config }
    }
    return store
  })
  
  return stores
}

export { stickystoreIds }
export { fetchCoordinates }
export { buildUpdatedMultistores }
export default updatedMultistores
