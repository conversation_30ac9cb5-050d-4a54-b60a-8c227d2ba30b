import { slugify } from './helpers';
import inventory from './inventory';
import { fetchInventory } from './inventoryProvider';
import { fetchStore } from './storeProvider';
import categorygroupings from './categoryGrouping';
import wordsmapping from './wordsMapping';

// Helper function to normalize category names based on wordsmapping
function normalizeCategory(category) {
  // Check if this category is a child in any mapping
  for (const mapping of wordsmapping) {
    if (mapping.childs.some(child => 
      child.toLowerCase() === category.toLowerCase() ||
      slugify(child) === slugify(category)
    )) {
      return mapping.master; // Return the master term
    }
  }
  // If no mapping found, return the original category
  return category;
}

async function fetchCategoriesForOneShop(storeIdToFind) {
  let inventory;
  let stores = await fetchStore();
  
  const storeobject = stores.find(store => store.storeId === storeIdToFind);
  if (storeobject) {            
    inventory = (storeobject.inventory || []).filter(item => item.activestatus === "1" && item.price !== 0);
    
    // IMPORTANT: Normalize the categories in the inventory items
    inventory.forEach(item => {
      if (Array.isArray(item.categories)) {
        // Replace each category with its normalized version
        item.categories = item.categories.map(category => normalizeCategory(category));
      }
    });
  } else {
    inventory = await fetchInventory();
  }
  
  const categories = inventory.reduce((acc, next) => {
    // Check if next.categories is an array before using forEach
    if (Array.isArray(next.categories)) {
      next.categories.forEach(category => {
        // Category should already be normalized from the step above
        const slug = slugify(category);
        
        // Check if this category is already in our accumulator
        if (!acc.some(obj => obj.slug === slug)) {
          acc.push({ 
            slug,
            name: category,
            // We don't need originalNames anymore since we're normalizing at the source
          });
        }
      });
    } else {
      console.error("next.categories is not an array for item:", next);
    }
    return acc;
  }, []);

  // Define a function to get the bucket name for a given category
  function getCategoryBucketName(category) {
    for (const [bucket, values] of Object.entries(categorygroupings)) {
      if (values.includes(category)) {
        return bucket;
      }
    }
    return null; // Return null if category doesn't belong to any bucket
  }
  
  // Sort categories into buckets based on categorygroupings
  const sortedCategories = {};
  for (const [bucket, values] of Object.entries(categorygroupings)) {
    sortedCategories[bucket] = categories.filter(cat => getCategoryBucketName(cat.name) === bucket);
    sortedCategories[bucket].sort((a, b) => a.name.localeCompare(b.name)); // Sort within each bucket
  }
  
  // Get categories that don't belong to any bucket
  const uncategorizedCategories = categories.filter(cat => getCategoryBucketName(cat.name) === null);
  uncategorizedCategories.sort((a, b) => a.name.localeCompare(b.name)); // Sort uncategorized categories
  
  // Concatenate sorted categories from buckets and uncategorized categories
  const finalCategories = Object.values(sortedCategories).reduce((acc, bucketCategories) => {
    acc.push(...bucketCategories);
    return acc;
  }, []);
  if (storeobject.menu_grouping_strict && storeobject.menu_grouping_strict==="1") {
    // do nothing - not appending uncategoriziedCategories to the categories
  } else {
    finalCategories.push(...uncategorizedCategories); // Append uncategorized categories to the end
  }
  
  return Promise.resolve(finalCategories);
}

async function fetchCategories() {
  let inventory = await fetchInventory();
  
  // Normalize categories in inventory items
  inventory.forEach(item => {
    if (Array.isArray(item.categories)) {
      item.categories = item.categories.map(category => normalizeCategory(category));
    }
  });
  
  const categoriesByStore = {
    all: [], // Zero index for all categories
  };
  
  let stores = await fetchStore();

  // Initialize all categories array with unique categories
  inventory.forEach(item => {
    if (Array.isArray(item.categories)) {
      item.categories.forEach(category => {
        const slug = slugify(category);
        if (!categoriesByStore.all.some(obj => obj.slug === slug)) {
          categoriesByStore.all.push({ 
            slug, 
            name: category
          });
        }
      });
    }
  });

  // Initialize categories for each store
  await Promise.all(stores.map(async store => {
    const storeCategories = await fetchCategoriesForOneShop(store.storeId);
    categoriesByStore[store.storeId] = storeCategories;
  }));

  return Promise.resolve(categoriesByStore);
}

export default fetchCategories;
