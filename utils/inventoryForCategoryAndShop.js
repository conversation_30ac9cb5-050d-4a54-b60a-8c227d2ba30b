import { fetchInventory } from './inventoryProvider'
import { inventoryByCategory } from './inventoryByCategory'
import { slugify } from './helpers'
import { fetchStore } from './storeProvider'

async function fetchInventoryForCategoryAndShop(category, storeIdToFind) {
  try {
    category=slugify(category) //this is important, else ["Cặp sách"] will fail
    const allstores = await fetchStore();
    const storeobject = allstores.find(store => store.storeId === storeIdToFind);
    
    /* console.log("storeobject:")
    console.log(storeobject) */


    let inventory
    if (storeobject) {            
      //inventory = storeobject.inventory || []; // Get inventory for the specified store
      inventory = (storeobject.inventory || []).filter(item => item.activestatus === "1");
    }
    else {
      inventory = await fetchInventory()
    }        
    
    const byCategory = inventoryByCategory(inventory)
    
    if (byCategory && byCategory[category]) {
      return byCategory[category].items;
    } else {
      return null; // Return null if byCategory or its category property is null or undefined
    }
  } catch (error) {
    console.error(error)
    return null;
  }
}


export default fetchInventoryForCategoryAndShop;
