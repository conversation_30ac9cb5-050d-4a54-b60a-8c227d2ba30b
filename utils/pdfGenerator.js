// Checkout Status Report Generation utility
// Uses browser's built-in capabilities - no external dependencies required

// Helper function to format date
const formatDate = (date) => {
  return new Date(date).toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Helper function to get status text in Vietnamese
const getStatusText = (isCompleted, step) => {
  if (step.informationOnly) {
    return "HƯỚNG DẪN";
  }
  
  if (step.id === 'documents') {
    if (isCompleted) {
      return "Đã tải lên - Đang chờ xét duyệt";
    } else {
      return "Cần hoàn thành";
    }
  }
  
  if (step.id === 'payment') {
    if (isCompleted) {
      return "Hoàn thành";
    } else {
      return "Cần hoàn thành";
    }
  }
  
  return isCompleted ? "Hoàn thành" : "Cần hoàn thành";
};

// Generate HTML report as fallback
const generateHTMLReport = (data) => {
  const {
    orderId,
    storeObject,
    checkoutSteps,
    completedSections,
    sectionData,
    groupedProducts,
    selectedCurrency,
    totalForCurrentStore,
    paymentInitiated,
    paymentMethodUsed,
    addressData
  } = data;

  const html = `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trạng thái đơn hàng ${orderId}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
        .section { margin-bottom: 20px; }
        .section h3 { color: #0066cc; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        .status-completed { color: #28a745; }
        .status-pending { color: #dc3545; }
        .status-info { color: #17a2b8; }
        .product-item { margin-left: 20px; padding: 5px 0; }
        .step-item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>TRẠNG THÁI ĐƠN HÀNG</h1>
        <p><strong>Cửa hàng:</strong> ${storeObject?.storeName || storeObject?.storeId || 'N/A'}</p>
        <p><strong>Mã đơn hàng:</strong> ${orderId}</p>
        <p><strong>Ngày tạo:</strong> ${formatDate(new Date())}</p>
        <p><strong>Tổng tiền:</strong> ${selectedCurrency}${totalForCurrentStore?.toLocaleString() || '0'}</p>
    </div>

    <div class="section">
        <h3>TRẠNG THÁI THANH TOÁN</h3>
        ${paymentInitiated
          ? `<p class="status-completed">✓ Đã khởi tạo thanh toán qua: ${paymentMethodUsed}</p>`
          : `<p class="status-pending">○ Chưa khởi tạo thanh toán</p>`
        }
    </div>

    ${Object.keys(groupedProducts || {}).length > 0 ? `
    <div class="section">
        <h3>SẢN PHẨM</h3>
        ${Object.entries(groupedProducts).map(([type, products]) => `
            <div>
                <strong>${type} (${products.length} sản phẩm):</strong>
                ${products.map(product => `
                    <div class="product-item">• ${product.name} - ${product.sku} - x${product.quantity} - ${selectedCurrency}${product.price?.toLocaleString()}</div>
                `).join('')}
            </div>
        `).join('')}
    </div>
    ` : ''}

    <div class="section">
        <h3>TIẾN ĐỘ HOÀN THÀNH</h3>
        ${checkoutSteps.map(step => {
          const isCompleted = completedSections[step.id];
          const statusText = getStatusText(isCompleted, step);
          const statusClass = isCompleted ? 'status-completed' : 'status-pending';
          const statusIcon = isCompleted ? '✓' : '○';

          return `
            <div class="step-item">
                <div class="${statusClass}"><strong>${statusIcon} ${step.title}</strong> - ${statusText}</div>
                <div style="font-size: 14px; color: #666;">${step.description}</div>
                ${sectionData[step.id] ? `
                    <div style="font-size: 12px; margin-top: 5px; color: #555;">
                        ${step.id === 'recipient' && sectionData[step.id].name ? `Người nhận: ${sectionData[step.id].name}` : ''}
                        ${step.id === 'delivery' && sectionData[step.id].method ? `Phương thức: ${sectionData[step.id].method}` : ''}
                        ${step.id === 'documents' && sectionData[step.id].documentsUploaded ? `Đã tải lên: ${sectionData[step.id].documentsUploaded} tài liệu` : ''}
                    </div>
                ` : ''}
            </div>
          `;
        }).join('')}
    </div>

    ${addressData && Object.keys(addressData).length > 0 ? `
    <div class="section">
        <h3>THÔNG TIN GIAO HÀNG</h3>
        ${addressData.fullName ? `<p><strong>Họ tên:</strong> ${addressData.fullName}</p>` : ''}
        ${addressData.phone ? `<p><strong>Điện thoại:</strong> ${addressData.phone}</p>` : ''}
        ${addressData.address ? `<p><strong>Địa chỉ:</strong> ${addressData.address}</p>` : ''}
        ${addressData.city ? `<p><strong>Thành phố:</strong> ${addressData.city}</p>` : ''}
    </div>
    ` : ''}

    <div class="footer">
        <p>Tạo lúc: ${formatDate(new Date())}</p>
        <p>Báo cáo này có thể được in hoặc lưu dưới dạng PDF từ trình duyệt</p>
    </div>
</body>
</html>`;

  return html;
};

// Download HTML report
const downloadHTMLReport = (html, filename) => {
  const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Main report generation function - HTML format
export const generateCheckoutStatusPDF = ({
  orderId,
  storeObject,
  checkoutSteps,
  completedSections,
  sectionData,
  groupedProducts,
  selectedCurrency,
  totalForCurrentStore,
  paymentInitiated,
  paymentMethodUsed,
  addressData
}) => {
  try {
    // Generate HTML report
    console.log('Generating HTML checkout status report');
    const html = generateHTMLReport({
      orderId,
      storeObject,
      checkoutSteps,
      completedSections,
      sectionData,
      groupedProducts,
      selectedCurrency,
      totalForCurrentStore,
      paymentInitiated,
      paymentMethodUsed,
      addressData
    });

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `checkout-status-${orderId}-${timestamp}.html`;

    downloadHTMLReport(html, filename);

    return {
      success: true,
      filename,
      message: 'Báo cáo đã được tải xuống thành công (có thể in thành PDF từ trình duyệt)',
      type: 'html'
    };

  } catch (error) {
    console.error('Error generating report:', error);
    return {
      success: false,
      error: error.message,
      message: 'Có lỗi xảy ra khi tạo báo cáo'
    };
  }
};
