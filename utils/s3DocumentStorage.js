// S3 Document storage utilities
import AWS from 'aws-sdk';

// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1'
});

// Document types
const DOCUMENT_TYPES = ['idCard', 'photo', 'proofOfResidence'];
const S3_BUCKET = process.env.S3_DOCUMENT_BUCKET;

// Initialize S3 client
const s3 = new AWS.S3();

/**
 * Get the S3 key for a document
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @returns {string} - S3 key
 */
export function getDocumentKey(documentType, filename) {
  if (!DOCUMENT_TYPES.includes(documentType)) {
    throw new Error(`Invalid document type: ${documentType}`);
  }
  
  // Use a folder structure in S3
  return `documents/${documentType}/${filename}`;
}

/**
 * Check if a document exists in S3
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @returns {Promise<boolean>} - Whether the document exists
 */
export async function documentExists(documentType, filename) {
  try {
    const key = getDocumentKey(documentType, filename);
    
    const params = {
      Bucket: S3_BUCKET,
      Key: key
    };
    
    await s3.headObject(params).promise();
    return true;
  } catch (error) {
    if (error.code === 'NotFound') {
      return false;
    }
    console.error('Error checking document existence in S3:', error);
    throw error;
  }
}

/**
 * Get document file info from S3
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @returns {Promise<{size: number, mimeType: string}>} - File info
 */
export async function getDocumentInfo(documentType, filename) {
  try {
    const key = getDocumentKey(documentType, filename);
    
    const params = {
      Bucket: S3_BUCKET,
      Key: key
    };
    
    const data = await s3.headObject(params).promise();
    
    return {
      size: data.ContentLength,
      mimeType: data.ContentType || determineMimeType(filename)
    };
  } catch (error) {
    console.error('Error getting document info from S3:', error);
    throw error;
  }
}

/**
 * Read a document from S3
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @returns {Promise<Buffer>} - The document file contents
 */
export async function readDocument(documentType, filename) {
  try {
    const key = getDocumentKey(documentType, filename);
    
    const params = {
      Bucket: S3_BUCKET,
      Key: key
    };
    
    const data = await s3.getObject(params).promise();
    return data.Body;
  } catch (error) {
    console.error('Error reading document from S3:', error);
    throw error;
  }
}

/**
 * Generate a presigned URL for accessing the document directly
 * @param {string} documentType - The type of document
 * @param {string} filename - The filename of the document
 * @param {number} expiresIn - Expiration time in seconds (default: 60)
 * @returns {Promise<string>} - Presigned URL
 */
export async function getDocumentUrl(documentType, filename, expiresIn = 60) {
  try {
    const key = getDocumentKey(documentType, filename);
    
    const params = {
      Bucket: S3_BUCKET,
      Key: key,
      Expires: expiresIn
    };
    
    return s3.getSignedUrlPromise('getObject', params);
  } catch (error) {
    console.error('Error generating document URL:', error);
    throw error;
  }
}

/**
 * Determine MIME type based on file extension
 * @param {string} filename - The filename
 * @returns {string} - MIME type
 */
function determineMimeType(filename) {
  const ext = filename.split('.').pop().toLowerCase();
  
  const mimeTypes = {
    'pdf': 'application/pdf',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif'
  };
  
  return mimeTypes[ext] || 'application/octet-stream';
}

export default {
  getDocumentKey,
  documentExists,
  getDocumentInfo,
  readDocument,
  getDocumentUrl
}; 