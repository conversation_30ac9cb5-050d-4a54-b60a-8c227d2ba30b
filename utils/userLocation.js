import React, { useEffect, useState } from 'react';
import Button from '../components/Button'; // Assuming Button is a custom component
import { Geolocation } from '@capacitor/geolocation';

async function getLocation() {
  try {
    const position = await Geolocation.getCurrentPosition();
    return {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude
    };
  } catch (error) {
    console.error('Error getting location:', error);
    throw error;
  }
}

function calculateDistance(userLocation, targetLocation) {
  const earthRadius = 6371; // Earth's radius in kilometers
  const lat1 = userLocation.latitude;
  const lon1 = userLocation.longitude;
  const lat2 = targetLocation.latitude;
  const lon2 = targetLocation.longitude;

  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = earthRadius * c; // Distance in kilometers
  return distance;
}

function UserLocation({ targetCoordinate }) {
  const [location, setLocation] = useState(null);
  const [error, setError] = useState(null);
  const [distance, setDistance] = useState(null);

  const handleGetLocation = () => {
    getLocation()
      .then(position => {
        setLocation(position);
      })
      .catch(error => {
        setError(error.message);
      });
  };

  useEffect(() => {
    getLocation()
      .then(position => {
        setLocation(position);
      })
      .catch(error => {
        setError(error.message);
      });
  }, []);

  useEffect(() => {
    if (location && targetCoordinate) {
      const distance = calculateDistance(location, targetCoordinate);
      setDistance(distance.toFixed(2)); // Round distance to 2 decimal places
    }
  }, [location, targetCoordinate]);

  return (
    <div className="userLocationContainer">
      {/* <div className="buttonContainer">
        <Button
          onClick={handleGetLocation}          
          title={`Distance: ${distance} KM`}
          small={true}
        />
      </div> */}
      <div className="buttonContainer">
        <Button
          onClick={handleGetLocation}          
          title={location && distance !== null ? (
            <div className="flex items-center justify-center">
              <p>              
                {distance} km
              </p>
            </div>
          ) : error ? (
            <p>Error: {error}</p>
          ) : (
            <p>Loading...</p>
          )}
          small={true}
        />
      </div>      
    </div>
  );
}

export default UserLocation;
