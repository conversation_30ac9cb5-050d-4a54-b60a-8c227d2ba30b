/* function slugify(string) {
  const a = 'àáäâãåăæąçćčđďèéěėëêęğǵḧìíïîįłḿǹńňñòóöôœøṕŕřßşśšșťțùúüûǘůűūųẃẍÿýźžż·/_,:;'
  const b = 'aaaaaaaaacccddeeeeeeegghiiiiilmnnnnooooooprrsssssttuuuuuuuuuwxyyzzz------'
  const p = new RegExp(a.split('').join('|'), 'g')

  return string.toString().toLowerCase()
    .replace(/\s+/g, '-') // Replace spaces with -
    .replace(p, c => b.charAt(a.indexOf(c))) // Replace special characters
    .replace(/&/g, '-and-') // Replace & with 'and'
    .replace(/[^\w-]+/g, '') // Remove all non-word characters
    .replace(/--+/g, '-') // Replace multiple - with single -
    .replace(/^-+/, '') // Trim - from start of text
    .replace(/-+$/, ''); // Trim - from end of text
} */

function slugify(string) {
  return string
  .normalize('NFD') // separate diacritics from letters
  .replace(/[\u0300-\u036f]/g, '') // remove diacritics
  .toLowerCase() // convert to lower case
  .trim() // trim white spaces at start and end
  .replace(/\s+/g, '-') // replace spaces with -
  //.replace(/[^\w\-]+/g, '') // remove all non-word chars
  //.replace(/\-\-+/g, '-'); // replace multiple - with single -
}


function titleIfy(slug) {
  var words = slug.split('-')
  for (var i = 0; i < words.length; i++) {
    var word = words[i]
    words[i] = word.charAt(0).toUpperCase() + word.slice(1)
  }
  return words.join(' ')
}

function getTrimmedString(string, length = 8) {
  if (string.length <= length) {
    return string
  } else {
    return string.substring(0, length) + '...'
  }
}

function appendOrUpdateAffiliateParam(url, affiliate) {
  // Check if affiliate is null or empty
  if (!affiliate || affiliate.trim() === "") {
    // If affiliate is null or empty, return the original URL
    return url;
  }

  // Check if the current URL already has a query string
  const queryStringIndex = url.indexOf("?");

  let updatedUrl = url;
  if (queryStringIndex === -1) {
    // If there's no query string, add the affiliate parameter directly
    updatedUrl += `?affiliate=${affiliate}`;
  } else {
    // If there's a query string, check if it already contains an affiliate parameter
    const queryParams = new URLSearchParams(url.substring(queryStringIndex));
    if (!queryParams.has("affiliate")) {
      // If there's no affiliate parameter, append it to the existing query string
      updatedUrl += `&affiliate=${affiliate}`;
    } else {
      // If there's already an affiliate parameter, update its value to the provided affiliate
      queryParams.set("affiliate", affiliate);
      updatedUrl = `${url.split("?")[0]}?${queryParams.toString()}`;
    }
  }

  return updatedUrl;
}

export {
  slugify, titleIfy, getTrimmedString, appendOrUpdateAffiliateParam
}