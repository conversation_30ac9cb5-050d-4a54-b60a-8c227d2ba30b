// Product to Checkout Method Mapping Utility
const CheckoutManager = require('./checkout');

/**
 * This module manages the mapping between products and checkout methods
 */
const ProductCheckoutMapping = {
  // Default mappings by category
  defaultMappings: {
    "CARD": 1,
    "SIM": 2,
    "TRAVELSIM": 3
  },
  
  // Custom mappings by SKU (overrides category-based mapping)
  skuMappings: {
    // Example: Override specific SKUs to use different checkout methods
    "document.license.conversion.vn2tw": 1,
    "document.license.conversion.tw2vn": 1,
    "document.license.conversion.tw2intl": 1,
    "document.passport.renewal.tw": 1,
    "document.death.certificate.tw": 1,
    "document.maternity.certificate.tw": 1
  },
  
  /**
   * Get the appropriate checkout method for a product
   * @param {Object} product - The product object
   * @returns {number} - The checkout method ID
   */
  getCheckoutMethod: function(product) {
    if (!product) return 1; // Default to method 1
    
    // Check if this SKU has a specific mapping
    if (product.sku && this.skuMappings[product.sku]) {
      return this.skuMappings[product.sku];
    }
    
    // Otherwise determine by category
    if (product.categories && Array.isArray(product.categories)) {
      return CheckoutManager.getMethodByCategory(product.categories) || 1;
    }
    
    return 1; // Default to method 1
  },
  
  /**
   * Add or update a mapping for a specific product SKU
   * @param {string} sku - The product SKU
   * @param {number} methodId - The checkout method ID
   * @returns {Object} - The update result
   */
  setMapping: function(sku, methodId) {
    if (!sku || !methodId) {
      return { success: false, error: "Missing SKU or method ID" };
    }
    
    // Validate method ID
    if (![1, 2, 3].includes(parseInt(methodId))) {
      return { success: false, error: "Invalid method ID. Must be 1, 2, or 3" };
    }
    
    // Update the mapping
    this.skuMappings[sku] = parseInt(methodId);
    
    return { 
      success: true, 
      sku, 
      methodId: parseInt(methodId),
      methodName: CheckoutManager.methods[methodId]
    };
  },
  
  /**
   * Remove a specific SKU mapping
   * @param {string} sku - The product SKU
   * @returns {Object} - The removal result
   */
  removeMapping: function(sku) {
    if (!sku) {
      return { success: false, error: "Missing SKU" };
    }
    
    if (!this.skuMappings[sku]) {
      return { success: false, error: "SKU mapping not found" };
    }
    
    delete this.skuMappings[sku];
    
    return { success: true, sku };
  },
  
  /**
   * Initialize checkout for a product
   * @param {Object} product - The product object
   * @param {number} methodOverride - Optional method override
   * @returns {Object} - The checkout configuration
   */
  initCheckout: function(product, methodOverride = null) {
    // Determine checkout method
    const methodId = methodOverride || this.getCheckoutMethod(product);
    
    // Process checkout with the determined method
    return CheckoutManager.processCheckout(product, methodId);
  },
  
  /**
   * Get all SKUs mapped to a specific checkout method
   * @param {number} methodId - The checkout method ID
   * @returns {Array} - List of SKUs using this method
   */
  getSkusByMethod: function(methodId) {
    if (!methodId) return [];
    
    const method = parseInt(methodId);
    const skus = [];
    
    // Check custom mappings
    for (const [sku, mapMethod] of Object.entries(this.skuMappings)) {
      if (mapMethod === method) {
        skus.push(sku);
      }
    }
    
    return skus;
  }
};

module.exports = ProductCheckoutMapping; 