import axios from 'axios';
import fs from 'fs';
import path from 'path';

// Path to main data directory
const dataDir = path.join(process.cwd(), 'data');
// Path to orders.json file
const ordersFilePath = path.join(dataDir, 'orders.json');
// Path to documents.json file
const documentsFilePath = path.join(dataDir, 'documents.json');

/**
 * Client-side function to create an order
 */
export const createOrder = async (orderData) => {
  try {
    const response = await axios.post('/api/orders', orderData);
    return response.data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

/**
 * Get all orders from the central orders.json file
 * @returns {Array} Array of orders
 */
export const getAllOrders = () => {
  try {
    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Check if orders.json exists
    if (fs.existsSync(ordersFilePath)) {
      try {
        const ordersData = fs.readFileSync(ordersFilePath, 'utf8');
        
        // Handle empty file case
        if (!ordersData || ordersData.trim() === '') {
          console.log('[OrderUtils] orders.json is empty, initializing with empty array');
          fs.writeFileSync(ordersFilePath, JSON.stringify([], null, 2));
          return [];
        }
        
        const orders = JSON.parse(ordersData);
        
        // Ensure orders is an array
        if (!Array.isArray(orders)) {
          console.warn('[OrderUtils] orders.json did not contain an array, reinitializing with empty array');
          fs.writeFileSync(ordersFilePath, JSON.stringify([], null, 2));
          return [];
        }
        
        return orders;
      } catch (error) {
        console.error('[OrderUtils] Error parsing orders.json, reinitializing file:', error);
        // If there's a parsing error, recreate the file with an empty array
        fs.writeFileSync(ordersFilePath, JSON.stringify([], null, 2));
        return [];
      }
    } else {
      // If file doesn't exist, create it with an empty array
      fs.writeFileSync(ordersFilePath, JSON.stringify([], null, 2));
      return [];
    }
  } catch (error) {
    console.error('[OrderUtils] Error loading orders:', error);
    return [];
  }
};

/**
 * Updates orders in the orders.json file
 * @param {Array} orders Array of order objects to save
 * @returns {boolean} Success status
 */
export const saveOrders = (orders) => {
  try {
    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Validate input
    if (!Array.isArray(orders)) {
      console.error('[OrderUtils] saveOrders expects an array');
      return false;
    }
    
    // Write orders to file
    fs.writeFileSync(ordersFilePath, JSON.stringify(orders, null, 2));
    return true;
  } catch (error) {
    console.error('[OrderUtils] Error saving orders:', error);
    return false;
  }
};

/**
 * Gets orders for a specific customer
 * @param {string} customerId The customer ID to find orders for
 * @returns {Array} Array of orders for the customer
 */
export const getCustomerOrders = (customerId) => {
  if (!customerId) {
    console.error('[OrderUtils] Customer ID is required');
    return [];
  }
  
  try {
    const allOrders = getAllOrders();
    return allOrders.filter(order => order.customerId === customerId);
  } catch (error) {
    console.error(`[OrderUtils] Error getting orders for customer ${customerId}:`, error);
    return [];
  }
};

/**
 * Get a specific order by ID
 * @param {string} orderId The order ID to find
 * @returns {Object|null} The order object or null if not found
 */
export const getOrderById = (orderId) => {
  if (!orderId) {
    console.error('[OrderUtils] Order ID is required');
    return null;
  }
  
  try {
    const allOrders = getAllOrders();
    return allOrders.find(order => order.id === orderId || order.orderId === orderId) || null;
  } catch (error) {
    console.error(`[OrderUtils] Error getting order ${orderId}:`, error);
    return null;
  }
};

/**
 * Add an order to the central orders.json file
 * @param {Object} orderData The order data to add
 * @returns {Object} The added order with generated ID
 */
export const addOrder = (orderData) => {
  try {
    // Sanitize orderData to handle malformed data
    const sanitizedOrderData = { ...orderData };

    // Fix phone number if it's been split into individual digits
    if (sanitizedOrderData.customerId) {
      // Check if this is a numerical ID that might have been split
      const digits = {};
      let isDigitObject = false;
      
      // Look for digit properties (0, 1, 2, etc.)
      for (let i = 0; i < 15; i++) {
        if (sanitizedOrderData[i] !== undefined) {
          digits[i] = sanitizedOrderData[i];
          isDigitObject = true;
          // Remove the digit property
          delete sanitizedOrderData[i];
        }
      }
      
      // If we found digit properties, reconstruct the phone number
      if (isDigitObject) {
        console.log('[OrderUtils] Detected malformed phone number, reconstructing...');
        const phoneDigits = Object.keys(digits)
          .sort((a, b) => parseInt(a) - parseInt(b))
          .map(key => digits[key])
          .join('');
          
        // Set the reconstructed phone as the customerPhone if not already set
        if (!sanitizedOrderData.customerPhone) {
          sanitizedOrderData.customerPhone = phoneDigits;
        }
        
        console.log(`[OrderUtils] Reconstructed phone number: ${phoneDigits}`);
      }
    }
    
    const orders = getAllOrders();
    
    // Generate an order ID if not provided
    if (!sanitizedOrderData.id) {
      sanitizedOrderData.id = `ORD${String(orders.length + 1).padStart(3, '0')}`;
    }
    
    // Generate an order number if not provided
    
    
    // Add timestamps if not present
    const now = new Date().toISOString();
    if (!sanitizedOrderData.createdAt) {
      sanitizedOrderData.createdAt = now;
    }
    if (!sanitizedOrderData.updatedAt) {
      sanitizedOrderData.updatedAt = now;
    }
    
    // Ensure item SKUs are preserved if they exist
    if (sanitizedOrderData.items && Array.isArray(sanitizedOrderData.items)) {
      sanitizedOrderData.items = sanitizedOrderData.items.map(item => {
        // Create a clean item object that always includes SKU if available
        return {
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          // Include SKU if it exists in the original item
          ...(item.sku && { sku: item.sku })
        };
      });
    }
    
    // Add the order to the array
    orders.push(sanitizedOrderData);
    
    // Save the updated orders
    saveOrders(orders);
    
    return sanitizedOrderData;
  } catch (error) {
    console.error('[OrderUtils] Error adding order:', error);
    throw error;
  }
};

/**
 * Update an existing order in the central orders.json file
 * @param {string} orderId The ID of the order to update
 * @param {Object} updateData The data to update
 * @returns {Object|null} The updated order or null if not found
 */
export const updateOrder = (orderId, updateData) => {
  try {
    const orders = getAllOrders();
    const orderIndex = orders.findIndex(order => order.id === orderId || order.orderId === orderId);
    
    if (orderIndex === -1) {
      console.error(`[OrderUtils] Order ${orderId} not found`);
      return null;
    }
    
    // Get the existing order
    const existingOrder = orders[orderIndex];
    
    // Create the updated order
    const updatedOrder = {
      ...existingOrder,
      ...updateData,
      updatedAt: new Date().toISOString()
    };
    
    // Update the order in the array
    orders[orderIndex] = updatedOrder;
    
    // Save the updated orders
    saveOrders(orders);
    
    return updatedOrder;
  } catch (error) {
    console.error(`[OrderUtils] Error updating order ${orderId}:`, error);
    return null;
  }
};

/**
 * Updates or creates the individual order file for a customer
 * Used for backward compatibility with existing code
 * @param {string} customerId The customer ID
 * @param {Object} orderData The order data
 */
export const updateCustomerOrderFile = (customerId, orderData) => {
  try {
    const customerDir = path.join(dataDir, 'customers', customerId);
    const ordersDir = path.join(customerDir, 'orders');
    const orderFilePath = path.join(ordersDir, `${orderData.id}.json`);
    
    // Ensure directories exist
    if (!fs.existsSync(customerDir)) {
      fs.mkdirSync(customerDir, { recursive: true });
    }
    if (!fs.existsSync(ordersDir)) {
      fs.mkdirSync(ordersDir, { recursive: true });
    }
    
    // Write the order to the file
    fs.writeFileSync(orderFilePath, JSON.stringify(orderData, null, 2));
    
    // Update the customer's order index
    updateCustomerOrderIndex(customerId, orderData);
    
  } catch (error) {
    console.error(`[OrderUtils] Error updating customer order file for customer ${customerId}:`, error);
    // Non-critical operation, continue execution
  }
};

/**
 * Updates the order index file for a customer
 * @param {string} customerId The customer ID
 * @param {Object} orderData The order data
 */
export const updateCustomerOrderIndex = (customerId, orderData) => {
  try {
    const indexFilePath = path.join(dataDir, 'customers', customerId, 'order-index.json');
    
    // Read existing index if it exists
    let orderIndex = [];
    if (fs.existsSync(indexFilePath)) {
      try {
        const indexData = fs.readFileSync(indexFilePath, 'utf8');
        orderIndex = JSON.parse(indexData);
      } catch (error) {
        console.error(`[OrderUtils] Error reading order index for customer ${customerId}:`, error);
        // Continue with empty array
      }
    }
    
    // Create a summary of the order for the index
    const orderSummary = {
      orderId: orderData.id,
      store: orderData.store || '',
      status: orderData.status || 'Processing',
      totalAmount: orderData.total || 0,
      itemCount: orderData.items?.length || 0,
      lastUpdated: new Date().toISOString(),
      createdAt: orderData.createdAt || new Date().toISOString()
    };
    
    // Check if the order is already in the index
    const existingIndex = orderIndex.findIndex(order => order.orderId === orderData.id);
    if (existingIndex >= 0) {
      // Update existing entry
      orderIndex[existingIndex] = orderSummary;
    } else {
      // Add new entry
      orderIndex.push(orderSummary);
    }
    
    // Write the updated index back to the file
    fs.writeFileSync(indexFilePath, JSON.stringify(orderIndex, null, 2));
    
  } catch (error) {
    console.error(`[OrderUtils] Error updating order index for customer ${customerId}:`, error);
    // Non-critical operation, continue execution
  }
};

/**
 * Get all documents from the central documents.json file
 * @returns {Array} Array of documents
 */
export const getAllDocuments = () => {
  try {
    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Check if documents.json exists
    if (fs.existsSync(documentsFilePath)) {
      const documentsData = fs.readFileSync(documentsFilePath, 'utf8');
      const documents = JSON.parse(documentsData);
      
      // Ensure documents is an array
      if (!Array.isArray(documents)) {
        console.warn('[OrderUtils] documents.json did not contain an array, returning empty array');
        return [];
      }
      
      return documents;
    } else {
      // If file doesn't exist, create it with an empty array
      fs.writeFileSync(documentsFilePath, JSON.stringify([], null, 2));
      return [];
    }
  } catch (error) {
    console.error('[OrderUtils] Error loading documents:', error);
    return [];
  }
};

/**
 * Saves documents to the central documents.json file
 * @param {Array} documents Array of document objects to save
 * @returns {boolean} Success status
 */
export const saveDocuments = (documents) => {
  try {
    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Validate input
    if (!Array.isArray(documents)) {
      console.error('[OrderUtils] saveDocuments expects an array');
      return false;
    }
    
    // Write documents to file
    fs.writeFileSync(documentsFilePath, JSON.stringify(documents, null, 2));
    return true;
  } catch (error) {
    console.error('[OrderUtils] Error saving documents:', error);
    return false;
  }
};

/**
 * Add a document to the central documents.json file
 * @param {Object} documentData The document data to add
 * @returns {Object} The added document with generated ID
 */
export const addDocument = (documentData) => {
  try {
    const documents = getAllDocuments();
    
    // Generate an ID if not provided
    if (!documentData.id) {
      documentData.id = `DOC${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    }
    
    // Add timestamp if not present
    if (!documentData.uploadDate) {
      documentData.uploadDate = new Date().toISOString();
    }
    
    // Add the document to the array
    documents.push(documentData);
    
    // Save the updated documents
    saveDocuments(documents);
    
    // Also update the customer's document index for backward compatibility
    if (documentData.customerId) {
      updateCustomerDocumentIndex(documentData.customerId, documentData);
    }
    
    return documentData;
  } catch (error) {
    console.error('[OrderUtils] Error adding document:', error);
    throw error;
  }
};

/**
 * Updates the customer documents in the customers.json file
 * @param {string} customerId The customer ID
 * @param {Object} documentData The document data
 */
export const updateCustomerDocumentIndex = (customerId, documentData) => {
  try {
    const customersJsonPath = path.join(dataDir, 'customers.json');
    
    // Check if customers.json exists
    if (!fs.existsSync(customersJsonPath)) {
      console.error(`[OrderUtils] customers.json file not found`);
      return;
    }
    
    // Read customers.json
    let customers;
    try {
      const customersData = fs.readFileSync(customersJsonPath, 'utf8');
      customers = JSON.parse(customersData);
    } catch (error) {
      console.error(`[OrderUtils] Error reading customers.json:`, error);
      return;
    }
    
    // Find the customer
    const customerIndex = customers.findIndex(c => c.id === customerId);
    
    if (customerIndex === -1) {
      console.error(`[OrderUtils] Customer with ID ${customerId} not found`);
      return;
    }
    
    // Add the document to the customer's documents array
    if (!customers[customerIndex].documents) {
      customers[customerIndex].documents = [];
    }
    
    // Add the document metadata
    customers[customerIndex].documents.push({
      documentType: documentData.documentType,
      filename: documentData.filename,
      originalFilename: documentData.originalFilename,
      uploadDate: documentData.uploadDate,
      size: documentData.size,
      orderId: documentData.orderId
    });
    
    // Update the lastUpdated timestamp
    customers[customerIndex].lastUpdated = new Date().toISOString();
    
    // Write the updated customers data back to the file
    fs.writeFileSync(customersJsonPath, JSON.stringify(customers, null, 2));
    
    console.log(`[OrderUtils] Document ${documentData.filename} added to customer ${customerId}`);
    
  } catch (error) {
    console.error(`[OrderUtils] Error updating customer documents for customer ${customerId}:`, error);
    // Non-critical operation, continue execution
  }
};

/**
 * Get documents for a specific customer
 * @param {string} customerId The customer ID to find documents for
 * @returns {Array} Array of documents for the customer
 */
export const getCustomerDocuments = (customerId) => {
  if (!customerId) {
    console.error('[OrderUtils] Customer ID is required');
    return [];
  }
  
  try {
    const allDocuments = getAllDocuments();
    return allDocuments.filter(doc => doc.customerId === customerId);
  } catch (error) {
    console.error(`[OrderUtils] Error getting documents for customer ${customerId}:`, error);
    return [];
  }
};

/**
 * Get documents for a specific order
 * @param {string} orderId The order ID to find documents for
 * @returns {Array} Array of documents for the order
 */
export const getOrderDocuments = (orderId) => {
  if (!orderId) {
    console.error('[OrderUtils] Order ID is required');
    return [];
  }
  
  try {
    const allDocuments = getAllDocuments();
    return allDocuments.filter(doc => doc.orderId === orderId);
  } catch (error) {
    console.error(`[OrderUtils] Error getting documents for order ${orderId}:`, error);
    return [];
  }
};

/**
 * Get orders by customer phone number
 * @param {string} phoneNumber Customer's phone number
 * @returns {Array} Matching orders
 */
export const getOrdersByPhone = (phoneNumber) => {
  if (!phoneNumber) return [];
  
  const orders = getAllOrders();
  return orders.filter(order => 
    order.customerPhone === phoneNumber || 
    order.customerId === phoneNumber
  );
};

/**
 * Get orders for a specific customer
 * @param {string} customerId Customer ID
 * @returns {Array} Matching orders
 */
export const getOrdersByCustomerId = (customerId) => {
  if (!customerId) return [];
  
  const orders = getAllOrders();
  return orders.filter(order => order.customerId === customerId);
};

/**
 * Check for and update expired orders
 * @returns {number} Number of newly expired orders
 */
export const checkAndUpdateExpiredOrders = () => {
  const orders = getAllOrders();
  const currentTime = new Date();
  let expiredCount = 0;
  
  const updatedOrders = orders.map(order => {
    // Skip already expired or paid orders
    if (order.isExpired || order.paymentStatus === 'paid') {
      return order;
    }
    
    // Check if the order has expired
    const validUntil = new Date(order.validUntil);
    if (validUntil < currentTime) {
      expiredCount++;
      return {
        ...order,
        isExpired: true,
        expiryReason: 'payment_timeout',
        status: 'expired',
        updatedAt: currentTime.toISOString()
      };
    }
    
    return order;
  });
  
  // Only save if there were changes
  if (expiredCount > 0) {
    saveOrders(updatedOrders);
  }
  
  return expiredCount;
};

/**
 * Award loyalty points for an order
 * @param {string} orderId Order ID
 * @param {string} customerId Customer ID
 * @returns {Promise<Object>} Award result
 */
const awardLoyaltyPoints = async (orderId, customerId) => {
  try {
    // Call the loyalty points API to award points
    const response = await axios.post('/api/admin/loyalty-points', {
      action: 'award',
      orderId: orderId
    });
    
    if (response.data && response.data.message) {
      console.log(`[OrderUtils] Loyalty points awarded for order ${orderId}:`, response.data.message);
      return {
        success: true,
        points: response.data.calculation?.points || 0,
        breakdown: response.data.calculation?.breakdown || [],
        customerData: response.data.customerData
      };
    }
    
    return { success: false, error: 'No response data' };
  } catch (error) {
    console.error(`[OrderUtils] Error awarding loyalty points for order ${orderId}:`, error.message);
    return { success: false, error: error.message };
  }
};

/**
 * Update order payment status
 * @param {string} orderId Order ID to update
 * @param {string} paymentStatus New payment status ('paid', 'not_paid', 'failed', 'refunded')
 * @param {Object} paymentDetails Additional payment details to update
 * @returns {boolean} Success status
 */
export const updateOrderPaymentStatus = (orderId, paymentStatus, paymentDetails = {}) => {
  if (!orderId || !paymentStatus) return false;
  
  const orders = getAllOrders();
  const orderIndex = orders.findIndex(order => order.id === orderId);
  
  if (orderIndex === -1) {
    console.error(`[OrderUtils] Order ${orderId} not found`);
    return false;
  }
  
  const currentOrder = orders[orderIndex];
  const wasNotPaid = currentOrder.paymentStatus !== 'paid';
  const isNowPaid = paymentStatus === 'paid';
  
  const currentTime = new Date();
  orders[orderIndex] = {
    ...orders[orderIndex],
    paymentStatus,
    status: paymentStatus === 'paid' ? 'paid' : orders[orderIndex].status,
    updatedAt: currentTime.toISOString(),
    paymentInfo: {
      ...orders[orderIndex].paymentInfo,
      ...paymentDetails
    }
  };
  
  const saveResult = saveOrders(orders);
  
  // Award loyalty points if payment status changed from not paid to paid
  if (saveResult && wasNotPaid && isNowPaid && currentOrder.customerId) {
    // Award points asynchronously to avoid blocking the payment status update
    awardLoyaltyPoints(orderId, currentOrder.customerId)
      .then(result => {
        if (result.success) {
          console.log(`[OrderUtils] Successfully awarded ${result.points} loyalty points for order ${orderId}`);
          
          // Update the order with loyalty points information
          const updatedOrders = getAllOrders();
          const updatedOrderIndex = updatedOrders.findIndex(order => order.id === orderId);
          if (updatedOrderIndex !== -1) {
            updatedOrders[updatedOrderIndex].loyaltyPoints = {
              awarded: result.points,
              breakdown: result.breakdown,
              awardedAt: new Date().toISOString()
            };
            saveOrders(updatedOrders);
          }
        } else {
          console.warn(`[OrderUtils] Failed to award loyalty points for order ${orderId}:`, result.error);
        }
      })
      .catch(error => {
        console.error(`[OrderUtils] Error in loyalty points award process for order ${orderId}:`, error);
      });
  }
  
  return saveResult;
};

/**
 * Count orders by status
 * @returns {Object} Counts of orders by status
 */
export const getOrderCounts = () => {
  const orders = getAllOrders();
  
  return {
    total: orders.length,
    pending: orders.filter(order => order.status === 'pending').length,
    paid: orders.filter(order => order.status === 'paid').length,
    expired: orders.filter(order => order.status === 'expired').length,
    cancelled: orders.filter(order => order.status === 'cancelled').length
  };
};