// Client-side order calculation and processing utilities for admin dashboard

// Calculate order total with fallback logic
export const calculateOrderTotal = (order) => {
  // If totalAmount exists and is not 0, use it
  if (order.totalAmount && order.totalAmount > 0) {
    return order.totalAmount;
  }
  
  // If amount exists and is not 0, use it as fallback
  if (order.amount && order.amount > 0) {
    return order.amount;
  }
  
  // Calculate from items if available
  if (order.items && order.items.length > 0) {
    const itemsTotal = order.items.reduce((total, item) => {
      const price = Number(item.price) || 0;
      const quantity = Number(item.quantity) || 0;
      return total + (price * quantity);
    }, 0);
    
    if (itemsTotal > 0) {
      return itemsTotal;
    }
  }
  
  // Final fallback
  return order.totalAmount || order.amount || 0;
};

// Process orders with enhanced data
export const processOrdersWithEnhancedData = (orders) => {
  return orders.map(order => {
    // Use existing status if available, otherwise determine from payment status
    let status = order.status || 'pending';
    
    // Enhanced status determination
    if (order.paymentStatus === 'paid') {
      status = 'completed';
    } else if (order.isExpired) {
      status = 'expired';
    } else if (order.paymentStatus === 'not_paid' && order.status === 'pending') {
      status = 'pending';
    }
    
    // Get customer name from multiple sources
    const customerName = order.customerName || 
                       order.recipientInfo?.fullName || 
                       'Unknown Customer';
    
    // Calculate total amount if not present
    const totalAmount = order.totalAmount || 
                      order.amount ||
                      (order.items ? order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) : 0);
    
    // Add formatted dates
    const createdAt = order.createdAt;
    const updatedAt = order.updatedAt;
    
    return {
      ...order,
      status,
      customerName,
      totalAmount,
      createdAt,
      updatedAt
    };
  });
};

// Calculate sales by currency
export const calculateSalesByCurrency = (paidOrders) => {
  return paidOrders.reduce((currencyTotals, order) => {
    const amount = order.totalAmount || 0;
    const currency = order.currency || 'VND';
    
    if (!currencyTotals[currency]) {
      currencyTotals[currency] = 0;
    }
    currencyTotals[currency] += amount;
    
    return currencyTotals;
  }, {});
};

// Print order handler
export const handlePrintOrder = (order) => {
  console.log('Print order:', order.id);
  // This would implement the print functionality
};
