import inventory from './inventory';

// Helper function to get current inventory count from new or old structure
function getCurrentInventoryCount(item) {
  if (item.inventory && typeof item.inventory === 'object') {
    return parseInt(item.inventory.count) || 0;
  }
  return parseInt(item.currentInventory) || 0;
}

// Helper function to get inventory type
function getInventoryType(item) {
  if (item.inventory && typeof item.inventory === 'object') {
    return item.inventory.type || 'same';
  }
  return 'same'; // Default for legacy items
}

// Helper function to check if item is in stock
function isInStock(item) {
  return getCurrentInventoryCount(item) > 0;
}

async function fetchInventoryPre() {
  // Simulated API call, returning the inventory data
  return Promise.resolve(inventory);
}

function rebuildPrice(product) {
  /* Rebuild the price: 
      - My price: product.price
      - Bestcompetitorprice: product.competitor_best_price
      - If my price is lower than the best competitor price, then take the best competitor price
      - If my price is higher than the best competitor price, then take my price
      - If the best competitor price is not available, consider it as zero
  */

  const myPrice = parseFloat(product.price);
  const competitorBestPrice = product.competitor_best_price 
    ? parseFloat(product.competitor_best_price) 
    : 0;

  // Check if myPrice is a valid number
  if (isNaN(myPrice)) {
    console.error("Invalid price value provided.");
    return null;
  }

  // Determine the final price based on the comparison
  const finalPrice = myPrice < competitorBestPrice ? competitorBestPrice : myPrice;

  return finalPrice;
}

async function fetchInventory() {
  // Fetch the inventory data
  const allInventory = await fetchInventoryPre();

  // Filter out only the active products with a price and in stock
  const activeInventory = allInventory.filter(item => 
    item.activestatus === "1" && 
    item.price !== 0 && 
    isInStock(item)
  );

  // Rebuild the inventory array
  const rebuiltInventory = activeInventory.map(product => {
    /* Rebuild the price: 
    - My price: product.price
    - Bestcompetitorprice: product.competitor_best_price
    - If my price is lower than the best competitor price, then take the best competitor price
    - If my price is higher than the best competitor price, then take my price
    */
    
    product.price = rebuildPrice(product);

    // Check if the product is a combo product
      if (product.comboproduct && product.comboproduct.length > 0) {
        // Create an array to store the details of subproducts
        const subProducts = [];

        // Variable to store the total price of all subproducts
        let totalPrice = 0;

        // Iterate through the combo products
        product.comboproduct.forEach(comboProduct => {
          // Find the combo product in the inventory data
          const subProduct = activeInventory.find(item => item.sku === comboProduct.sku);

          // If the combo product exists, add its details to the subProducts array
          if (subProduct) {
            //subProduct.notincludedincombo = comboProduct.notincludedincombo;
            subProducts.push({
              sku: subProduct.sku,
              quantity: comboProduct.quantity,
              name: subProduct.name,
              price: subProduct.price,
              description: subProduct.description ? subProduct.description : "",
              //image: subProduct.image && subProduct.image[0] ? subProduct.image[0] : ""
              image: subProduct.image ? subProduct.image : [],
              /* notincludedincombo: subProduct.notincludedincombo ? subProduct.notincludedincombo : 0 */
              //notincludedincombo: subProduct.notincludedincombo ? (Number(subProduct.notincludedincombo) === 1 ? 1 : 0): 0
              notincludedincombo: comboProduct.notincludedincombo ? (Number(comboProduct.notincludedincombo) === 1 ? 1 : 0): 0,
              inventory: subProduct.inventory || {
                type: 'same',
                count: getCurrentInventoryCount(subProduct),
                items: []
              }
            });

            // Add the subproduct's price to the total price
            if (!comboProduct.notincludedincombo || (Number(comboProduct.notincludedincombo) != 1)) {            
              totalPrice += parseFloat(subProduct.price) * comboProduct.quantity;
            }
            /* totalPrice += parseFloat(subProduct.price) * comboProduct.quantity; */
            /* if (subProduct.sku === 'S000093') {
              console.log('S000093: ' + comboProduct.notincludedincombo);
            } */
          }
        });

        // Check if all sub-products are in stock
        const allInStock = subProducts.every(subProduct => isInStock(subProduct));
        
        if (!allInStock) {
          return null; // Skip combo products where not all sub-products are in stock
        }

        //product.price = (totalPrice* 0.95).toFixed(0) 
        //product.price = totalPrice.toFixed(0) * 0.95 //This is the combo price.

        //product.price = Math.ceil(totalPrice * 0.95); //This is the combo price.
        product.price = Math.round(Math.ceil(totalPrice * 0.95)/1000)*1000; //This is the combo price.

        // Calculate minimum inventory count from sub-products
        const minInventoryCount = Math.min(...subProducts.map(subProduct => getCurrentInventoryCount(subProduct)));
        
        // Update product inventory structure
        product.inventory = {
          type: 'same',
          count: minInventoryCount,
          items: []
        };
        
        // Remove old currentInventory field if it exists
        delete product.currentInventory;

        // Return the rebuilt product with additional subproduct details and total price
        return {
          ...product,
          subProducts: subProducts,
          //totalSubProductsPrice: Math.round(totalPrice / 100) * 100
          totalSubProductsPrice: totalPrice.toFixed(0) // Round to integer
        };
      } else {
        // If it's not a combo product, return the product as is
        return product;
      }
  });

  // Filter out null values (combo products where not all sub-products are in stock)
  return rebuiltInventory.filter(product => product !== null);
}

export {
  fetchInventory
};
