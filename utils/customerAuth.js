/**
 * Utility functions for customer authentication and cart association
 */

// Storage keys
const CUSTOMER_ID_KEY = 'SHOPME_CUSTOMER_ID';
const CUSTOMER_INFO_KEY = 'SHOPME_CUSTOMER_INFO';
const USER_ID_KEY = 'SHOPME_USER_ID';

/**
 * Set the customer ID in localStorage and associate carts
 * 
 * @param {string} customerId - The customer ID from authentication system
 * @param {object} customerInfo - Optional customer information object
 * @returns {Promise<object>} Result of the operation
 */
export async function setCustomerId(customerId, customerInfo = {}) {
  if (typeof window === 'undefined' || !customerId) return null;
  
  // Store previous ID for cart merging
  const previousId = localStorage.getItem(USER_ID_KEY);
  
  console.log('[CUSTOMER_AUTH] Setting customer ID:', customerId);
  
  // Store the customer ID
  localStorage.setItem(CUSTOMER_ID_KEY, customerId);
  
  // IMPORTANT: Also set USER_ID_KEY to be the same as customer ID
  // This ensures all cart operations use the customer ID
  localStorage.setItem(USER_ID_KEY, customerId);
  
  // Store customer info if provided
  if (customerInfo && Object.keys(customerInfo).length > 0) {
    localStorage.setItem(CUSTOMER_INFO_KEY, JSON.stringify(customerInfo));
  }
  
  // Call API to validate customer and handle cart merging
  try {
    // Get store ID from URL
    const pathSegments = window.location.pathname.split('/');
    const storeId = pathSegments.length > 1 ? pathSegments[1] : null;
    
    const response = await fetch('/api/validateCustomer', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customerId,
        previousId,
        storeId,
        customerInfo
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('[CUSTOMER_AUTH] Customer validation successful:', data);
      
      // Trigger cart merge if needed
      if (previousId && previousId !== customerId) {
        console.log('[CUSTOMER_AUTH] Triggering cart merge event');
        window.dispatchEvent(new CustomEvent('customer-identified', { 
          detail: { customerId, previousId }
        }));
      }
      
      return { success: true, data };
    } else {
      console.error('[CUSTOMER_AUTH] Customer validation failed:', await response.text());
      return { success: false, error: 'Validation failed' };
    }
  } catch (error) {
    console.error('[CUSTOMER_AUTH] Error validating customer:', error);
    
    // Even if API call fails, still trigger the event for local merge
    if (previousId && previousId !== customerId) {
      window.dispatchEvent(new CustomEvent('customer-identified', { 
        detail: { customerId, previousId }
      }));
    }
    
    return { success: false, error: error.message };
  }
}

/**
 * Create a temporary customer ID when user is not authenticated
 * 
 * @param {Object} customerData - Basic customer information
 * @returns {string} - The generated customer ID
 */
export function createTemporaryCustomer(customerData = {}) {
  if (typeof window === 'undefined') return null;
  
  // Generate a temporary ID using timestamp and random number
  const tempId = `temp_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
  
  // Create a basic customer object
  const customerInfo = {
    id: tempId,
    ...customerData,
    isTemporary: true,
    createdAt: new Date().toISOString()
  };
  
  // Save to localStorage
  localStorage.setItem(CUSTOMER_INFO_KEY, JSON.stringify(customerInfo));
  localStorage.setItem(CUSTOMER_ID_KEY, tempId);
  localStorage.setItem(USER_ID_KEY, tempId);
  
  console.log('[CUSTOMER_AUTH] Created temporary customer ID:', tempId);
  return tempId;
}

/**
 * Ensure cart is linked to customer
 * This function should be called on page load to ensure the cart is using the customer ID
 */
export function ensureCartLinkedToCustomer() {
  if (typeof window === 'undefined') return;
  
  const customerId = localStorage.getItem(CUSTOMER_ID_KEY);
  const userId = localStorage.getItem(USER_ID_KEY);
  
  // If customer is logged in but cart isn't using their ID, fix it
  if (customerId && userId !== customerId) {
    console.log('[CUSTOMER_AUTH] Fixing cart to use customer ID');
    localStorage.setItem(USER_ID_KEY, customerId);
    
    // Trigger cart merge
    window.dispatchEvent(new CustomEvent('customer-identified', { 
      detail: { customerId, previousId: userId }
    }));
  }
}

/**
 * Get the current customer ID
 * 
 * @returns {string|null} The customer ID or null if not set
 */
export function getCustomerId() {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(CUSTOMER_ID_KEY);
}

/**
 * Get customer information
 * 
 * @param {string} customerId - Optional customer ID to get specific info
 * @returns {object|null} The customer information or null if not available
 */
export function getCustomerInfo(customerId) {
  if (typeof window === 'undefined') {
    if (customerId) {
      return {
        id: customerId,
        name: `Customer ${customerId}`,
        email: `customer${customerId}@example.com`
      };
    }
    return null;
  }
  
  // Try to get customer info from localStorage
  try {
    const customerInfoJson = localStorage.getItem(CUSTOMER_INFO_KEY);
    if (customerInfoJson) {
      return JSON.parse(customerInfoJson);
    }
  } catch (error) {
    console.error('[CUSTOMER_AUTH] Error parsing customer info:', error);
  }
  
  // If customer is authenticated but no stored info, return test data
  if (isCustomerAuthenticated()) {
    const testCustomerData = {
      id: customerId || 'test_customer_123',
      name: 'Nguyễn Văn Test',
      email: '<EMAIL>',
      phone: '+84 912 345 678',
      address: '123 Đường Test, Quận 1, TP.HCM'
    };
    
    // Store the test data for future use
    localStorage.setItem(CUSTOMER_INFO_KEY, JSON.stringify(testCustomerData));
    
    return testCustomerData;
  }
  
  // If customerId is provided, return basic info
  if (customerId) {
    return {
      id: customerId,
      name: `Customer ${customerId}`,
      email: `customer${customerId}@example.com`
    };
  }
  
  return null;
}

/**
 * Clear customer authentication data
 */
export function clearCustomerAuth() {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem(CUSTOMER_ID_KEY);
  localStorage.removeItem(CUSTOMER_INFO_KEY);
}

/**
 * Check if a customer is authenticated
 * @returns {boolean} Whether the customer is authenticated
 */
export function isCustomerAuthenticated() {
  // For testing purposes, always return true
  return true;
}

export default {
  isCustomerAuthenticated,
  getCustomerInfo
};