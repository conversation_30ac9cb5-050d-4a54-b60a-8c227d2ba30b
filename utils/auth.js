import jwt from 'jsonwebtoken';
import { findDocument } from './dbUtils';

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-key-for-development';

/**
 * Verify and decode a JWT token
 * 
 * @param {string} token - The JWT token to verify
 * @returns {Object|null} The decoded token payload or null if invalid
 */
export async function verifyToken(token) {
  try {
    // Verify the token
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error.message);
    return null;
  }
}

/**
 * Generate a JWT token for a user
 * 
 * @param {Object} user - The user object
 * @param {string} expiresIn - Token expiration time (default: '7d')
 * @returns {string} The generated JWT token
 */
export async function generateToken(user, expiresIn = '7d') {
  // Create a payload with user information
  const payload = {
    id: user._id,
    email: user.email,
    role: user.role,
    isAdmin: user.isAdmin || false,
    iat: Math.floor(Date.now() / 1000)
  };
  
  // Sign the token
  const token = jwt.sign(payload, JWT_SECRET, { expiresIn });
  return token;
}

/**
 * Middleware to authenticate API routes using JWT
 * 
 * @param {Function} handler - The API route handler function
 * @returns {Function} A wrapped handler with authentication
 */
export function withAuth(handler) {
  return async (req, res) => {
    try {
      // Extract the token from authorization header
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ success: false, message: 'Authentication required' });
      }
      
      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      const decoded = await verifyToken(token);
      
      if (!decoded) {
        return res.status(401).json({ success: false, message: 'Invalid or expired token' });
      }
      
      // Find the user in the database
      const user = await findDocument('users', { email: decoded.email });
      
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      
      // Attach the user to the request object
      req.user = user;
      
      // Call the original handler
      return handler(req, res);
    } catch (error) {
      console.error('Authentication error:', error);
      return res.status(500).json({ success: false, message: 'Authentication error', error: error.message });
    }
  };
}

/**
 * Middleware to authenticate and authorize admin API routes
 * 
 * @param {Function} handler - The API route handler function
 * @returns {Function} A wrapped handler with admin authentication
 */
export function withAdminAuth(handler) {
  return async (req, res) => {
    try {
      // Extract the token from authorization header
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ success: false, message: 'Authentication required' });
      }
      
      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      const decoded = await verifyToken(token);
      
      if (!decoded) {
        return res.status(401).json({ success: false, message: 'Invalid or expired token' });
      }
      
      // Find the user in the database
      const user = await findDocument('users', { email: decoded.email });
      
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      
      // Check if the user is an admin
      if (!user.isAdmin) {
        return res.status(403).json({ success: false, message: 'Admin access required' });
      }
      
      // Attach the user to the request object
      req.user = user;
      
      // Call the original handler
      return handler(req, res);
    } catch (error) {
      console.error('Admin authentication error:', error);
      return res.status(500).json({ success: false, message: 'Authentication error', error: error.message });
    }
  };
}

/**
 * Validate an API key
 * 
 * @param {string} apiKey - The API key to validate
 * @returns {Object|null} The API key document or null if invalid
 */
export async function validateApiKey(apiKey) {
  try {
    // Find the API key in the database
    const apiKeyDoc = await findDocument('api_keys', { key: apiKey, active: true });
    
    if (!apiKeyDoc) {
      return null;
    }
    
    // Check if the API key has expired
    if (apiKeyDoc.expiresAt && new Date(apiKeyDoc.expiresAt) < new Date()) {
      return null;
    }
    
    return apiKeyDoc;
  } catch (error) {
    console.error('API key validation error:', error);
    return null;
  }
}

/**
 * Middleware to authenticate API routes using API keys
 * 
 * @param {Function} handler - The API route handler function
 * @returns {Function} A wrapped handler with API key authentication
 */
export function withApiKeyAuth(handler) {
  return async (req, res) => {
    try {
      // Extract the API key from headers or query parameters
      const apiKey = req.headers['x-api-key'] || req.query.api_key;
      
      if (!apiKey) {
        return res.status(401).json({ success: false, message: 'API key required' });
      }
      
      // Validate the API key
      const apiKeyDoc = await validateApiKey(apiKey);
      
      if (!apiKeyDoc) {
        return res.status(401).json({ success: false, message: 'Invalid or expired API key' });
      }
      
      // Attach the API key document to the request object
      req.apiKey = apiKeyDoc;
      
      // Call the original handler
      return handler(req, res);
    } catch (error) {
      console.error('API key authentication error:', error);
      return res.status(500).json({ success: false, message: 'Authentication error', error: error.message });
    }
  };
}

// Default export with all auth utilities
export default {
  verifyToken,
  generateToken,
  withAuth,
  withAdminAuth,
  validateApiKey,
  withApiKeyAuth,
}; 