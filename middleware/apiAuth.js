const API_KEY = process.env.ADMIN_API_KEY

export function validateApiKey(req) {
  const apiKey = req.headers['x-api-key']
  if (!apiKey || apiKey !== API_KEY) {
    return false
  }
  return true
}

export function apiKeyMiddleware(handler) {
  return async (req, res) => {
    if (!validateApiKey(req)) {
      return res.status(401).json({ error: 'Invalid API key' })
    }
    return handler(req, res)
  }
} 