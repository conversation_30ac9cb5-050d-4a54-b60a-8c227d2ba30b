import axios from 'axios';

/**
 * Create a new order via API
 * @param {Object} orderData - Data for the new order
 * @returns {Promise<Object>} - Response with the created order
 */
export const createOrder = async (orderData) => {
  try {
    const response = await axios.post('/api/orders', orderData);
    return response.data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

/**
 * Get all orders
 * @param {Object} options - Optional query parameters like limit, skip, etc.
 * @returns {Promise<Object>} - Response with orders
 */
export const getAllOrders = async (options = {}) => {
  try {
    // Build query string from options
    const queryParams = new URLSearchParams();
    Object.entries(options).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
    
    const query = queryParams.toString();
    const url = `/api/orders${query ? `?${query}` : ''}`;
    
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error getting orders:', error);
    throw error;
  }
};

/**
 * Get a specific order by ID
 * @param {string} orderId - The order ID
 * @returns {Promise<Object>} - Response with the order
 */
export const getOrderById = async (orderId) => {
  try {
    const response = await axios.get(`/api/orders/${orderId}`);
    return response.data;
  } catch (error) {
    console.error(`Error getting order ${orderId}:`, error);
    throw error;
  }
};

/**
 * Get orders for a specific customer
 * @param {string} customerId - The customer ID
 * @returns {Promise<Object>} - Response with customer orders
 */
export const getCustomerOrders = async (customerId) => {
  try {
    const response = await axios.get(`/api/orders?customerId=${customerId}`);
    return response.data;
  } catch (error) {
    console.error(`Error getting orders for customer ${customerId}:`, error);
    throw error;
  }
};

/**
 * Update an existing order
 * @param {string} orderId - The order ID to update
 * @param {Object} updateData - Data to update the order with
 * @returns {Promise<Object>} - Response with the updated order
 */
export const updateOrder = async (orderId, updateData) => {
  try {
    const response = await axios.put('/api/orders', {
      id: orderId,
      ...updateData
    });
    return response.data;
  } catch (error) {
    console.error(`Error updating order ${orderId}:`, error);
    throw error;
  }
};

/**
 * Update order status
 * @param {string} orderId - The order ID to update
 * @param {string} status - New status for the order
 * @returns {Promise<Object>} - Response with the updated order
 */
export const updateOrderStatus = async (orderId, status) => {
  try {
    return await updateOrder(orderId, { status });
  } catch (error) {
    console.error(`Error updating status for order ${orderId}:`, error);
    throw error;
  }
};

/**
 * Submit an order via the submitOrder API endpoint
 * @param {Object} orderData - Complete order data to submit
 * @returns {Promise<Object>} - Response with the submitted order
 */
export const submitOrder = async (orderData) => {
  try {
    const response = await axios.post('/api/submitOrder', orderData);
    return response.data;
  } catch (error) {
    console.error('Error submitting order:', error);
    throw error;
  }
};

/**
 * Upload a document related to an order
 * @param {File} file - The file to upload
 * @param {string} documentType - Type of document (idCard, photo, etc.)
 * @param {string} customerId - ID of the customer
 * @param {string} orderId - Optional order ID to associate with
 * @returns {Promise<Object>} - Response with upload status
 */
export const uploadOrderDocument = async (file, documentType, customerId, orderId = null) => {
  try {
    const formData = new FormData();
    formData.append('document', file);
    formData.append('documentType', documentType);
    formData.append('customerId', customerId);
    
    if (orderId) {
      formData.append('orderId', orderId);
    }
    
    const response = await axios.post('/api/customer/document/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error uploading document:', error);
    throw error;
  }
};

/**
 * Update order information like recipient details, shipping info, etc.
 * @param {Object} orderData - Order data to update
 * @returns {Promise<Object>} - Response with update status
 */
export const updateOrderInformation = async (orderData) => {
  try {
    if (!orderData.customerId || !orderData.orderId) {
      throw new Error('Customer ID and Order ID are required');
    }
    
    const response = await axios.post('/api/order/update', orderData);
    return response.data;
  } catch (error) {
    console.error('Error updating order information:', error);
    throw error;
  }
}; 