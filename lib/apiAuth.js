/**
 * Simple API key authentication middleware
 * Validates requests against the API_KEY in .env
 */

// Default API key for development if none defined in .env
// Try different environment variables for the API key
const API_KEY = process.env.API_KEY || 
               process.env.NEXT_PUBLIC_API_KEY ||
               process.env.ADMIN_API_KEY ||
               'magshop-dev-secret';

console.log('API Key in apiAuth.js middleware:', API_KEY);

/**
 * Validates if the API key from the request header matches the expected API key
 * @param {Object} req - The request object
 * @returns {boolean} - Whether the API key is valid
 */
export function validateApiKey(req) {
  // Check for API key in x-api-key header
  const apiKey = req.headers['x-api-key'];
  
  console.log(`Validating API key: ${apiKey} against ${API_KEY}`);
  
  // Simple equality check against the expected API key
  return apiKey === API_KEY;
}

/**
 * API authentication middleware wrapper
 * @param {Function} handler - The API route handler function
 * @returns {Function} - The wrapped handler with authentication
 */
export function withApiAuth(handler) {
  return async (req, res) => {
    try {
      console.log(`API Request: ${req.method} ${req.url}`);
      
      // For now, we'll accept all API keys to debug
      if (req.headers['x-api-key']) {
        console.log('Request has API key:', req.headers['x-api-key']);
        // return handler(req, res);
      } else {
        console.log('Request has no API key');
      }
      
      // Check API key for all requests
      if (!validateApiKey(req)) {
        console.log('API key validation failed');
        return res.status(401).json({ 
          success: false, 
          message: 'Unauthorized - Invalid API key' 
        });
      }
      
      console.log('API key validation passed');
      
      // If API key is valid, call the original handler
      return await handler(req, res);
    } catch (error) {
      console.error('API Error:', error.message, error.stack);
      return res.status(500).json({ 
        success: false,
        message: 'Internal Server Error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
}

export default withApiAuth; 