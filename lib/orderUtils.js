import axios from 'axios';

export const createOrder = async (orderData) => {
  try {
    const response = await axios.post('/api/orders', orderData);
    return response.data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

export const updateOrderStatus = async (orderId, status) => {
  try {
    const response = await axios.patch(`/api/orders/${orderId}`, { status });
    return response.data;
  } catch (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
};

export const calculateOrderTotal = (items) => {
  return items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);
};

export const formatOrderItems = (items) => {
  return items.map(item => ({
    productId: item.id,
    name: item.name,
    price: item.price,
    quantity: item.quantity,
    image: item.image,
    ...(item.sku && { sku: item.sku })
  }));
};

export const validateOrder = (order) => {
  if (!order.customerId) {
    throw new Error('Customer ID is required');
  }
  if (!order.items || order.items.length === 0) {
    throw new Error('Order must contain at least one item');
  }
  if (!order.shippingAddress) {
    throw new Error('Shipping address is required');
  }
  return true;
}; 