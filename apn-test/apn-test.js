/**
 * 7-Eleven APN Test Script
 * 
 * This script simulates the 7-Eleven APN callbacks to test
 * the integration with your system.
 */

require('dotenv').config();
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');

// Load configuration from .env
const APN_URL = process.env.APN_URL || 'https://sim.dailoanshop.net/api/payment/apn-callback';
const API_ID = process.env.API_ID || '827315300001';
let TRANS_ID = process.env.TRANS_ID || `TEST_TRANS_${Date.now()}`;
let ORDER_NO = process.env.ORDER_NO || `ORD-TEST-${Math.floor(Math.random() * 10000)}`;
const AMOUNT = process.env.AMOUNT || '299';

// Enable detailed logging and debugging
const DETAILED_DEBUG = true;

// Ensure the logs directory exists
const LOGS_DIR = './logs';
if (!fs.existsSync(LOGS_DIR)) {
  fs.mkdirSync(LOGS_DIR);
}

// Create a log file for this test run
const LOG_FILE = `${LOGS_DIR}/apn-test-${new Date().toISOString().replace(/:/g, '-')}.log`;
const logger = fs.createWriteStream(LOG_FILE, { flags: 'a' });

// Log function that writes to console and file
function log(message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  
  console.log(logMessage);
  logger.write(logMessage + '\n');
  
  if (data) {
    const dataStr = typeof data === 'object' ? JSON.stringify(data, null, 2) : data.toString();
    console.log(dataStr);
    logger.write(dataStr + '\n');
  }
}

// Generate checksum for APN validation
function generateChecksum(api_id, trans_id, amount, status, nonce) {
  const message = `${api_id}:${trans_id}:${amount}:${status}:${nonce}`;
  log(`Generating checksum with message: ${message}`);
  return crypto.createHash('md5').update(message).digest('hex');
}

// Function to simulate an APN call
async function simulateAPNCall(status, config = {}) {
  // Generate new nonce for each call to ensure uniqueness
  const nonce = Date.now().toString();
  
  // Use provided config or defaults
  const transId = config.trans_id || TRANS_ID;
  const orderNo = config.order_no || ORDER_NO;
  const amount = config.amount || AMOUNT;
  
  // Calculate checksum
  const checksum = generateChecksum(
    API_ID, 
    transId, 
    amount, 
    status, 
    nonce
  );
  
  // Prepare APN payload
  const payload = {
    api_id: API_ID,
    trans_id: transId,
    order_no: orderNo,
    amount: amount,
    status: status,  // A = waiting, B = paid, D = expired
    nonce: nonce,
    checksum: checksum,
    payment_code: 'PC' + Math.floor(Math.random() * 10000000),
    payment_detail: JSON.stringify({
      store_id: '12345',
      payment_time: new Date().toISOString(),
      receipt_no: 'R' + Math.floor(Math.random() * 1000000),
    })
  };

  log(`Sending ${getStatusName(status)} notification for order ${orderNo}...`, payload);
  
  try {
    log(`Sending request to: ${APN_URL}`);
    if (DETAILED_DEBUG) {
      log(`Request details:`, {
        method: 'POST',
        url: APN_URL,
        headers: { 'Content-Type': 'application/json' },
        payload: payload
      });
    }
    
    const response = await axios.post(APN_URL, payload);
    
    log(`Response received (${response.status}):`, response.data);
    
    if (DETAILED_DEBUG) {
      log(`Response headers:`, response.headers);
    }
    
    return { success: true, response: response.data };
  } catch (error) {
    const errorData = error.response ? error.response.data : error.message;
    const statusCode = error.response ? error.response.status : 'No HTTP status';
    
    log(`Error during APN call (${statusCode}):`, errorData);
    
    if (DETAILED_DEBUG && error.response) {
      log(`Error response headers:`, error.response.headers);
    }
    
    return { 
      success: false, 
      status: statusCode,
      error: errorData
    };
  }
}

// Helper to get status name
function getStatusName(statusCode) {
  switch(statusCode) {
    case 'A': return 'Waiting for payment';
    case 'B': return 'Payment completed';
    case 'D': return 'Payment expired';
    default: return 'Unknown status';
  }
}

// Run test scenarios in sequence
async function runAllTests() {
  log('=== 7-ELEVEN APN TEST SCENARIOS ===');
  log(`Target URL: ${APN_URL}`);
  log(`Test Order: ${ORDER_NO}`);
  log(`Transaction ID: ${TRANS_ID}`);
  log(`Amount: ${AMOUNT}`);
  log('===================================');
  
  // Scenario 1: Payment waiting
  log('\n🔄 SCENARIO 1: PAYMENT WAITING');
  const waitingResult = await simulateAPNCall('A');
  log(`Scenario 1 result: ${waitingResult.success ? 'SUCCESS' : 'FAILED'}`);
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Scenario 2: Payment completed
  log('\n✅ SCENARIO 2: PAYMENT COMPLETED');
  const completedResult = await simulateAPNCall('B');
  log(`Scenario 2 result: ${completedResult.success ? 'SUCCESS' : 'FAILED'}`);
  
  // Wait 2 seconds
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Scenario 3: Payment expired (with same transaction)
  log('\n⏱️ SCENARIO 3: PAYMENT EXPIRED (SAME TRANSACTION)');
  const expiredResult = await simulateAPNCall('D');
  log(`Scenario 3 result: ${expiredResult.success ? 'SUCCESS' : 'FAILED'}`);
  
  log('\n=== TEST COMPLETED ===');
  log(`Log file created at: ${LOG_FILE}`);
  
  // Close the log file
  logger.end();
}

// Run the tests
runAllTests();
