# 7-Eleven APN Testing Tool

This tool helps you test the 7-Eleven APN (Active Payment Notification) integration with your system.

## Configuration

Edit the `.env` file to configure your test:

```
APN_URL=https://sim.dailoanshop.net/api/payment/apn-callback
API_ID=827315300001
TRANS_ID=TEST_TRANS_123456
ORDER_NO=ORD-TEST-123456
AMOUNT=299
```

## Running the Tests

Run the test script with:

```bash
npm test
```

This will simulate three scenarios:
1. Payment waiting (Status A)
2. Payment completed (Status B)
3. Payment expired (Status D) - using a new transaction

## Logs

All test results are stored in the `logs` directory.

## Understanding APN Statuses

- Status A: Waiting for payment
- Status B: Payment completed
- Status D: Payment expired

## Troubleshooting

If you encounter issues:

1. Check the log file for detailed error information
2. Verify your server is accessible from the internet
3. Ensure the checksum validation is working correctly
4. Check that the order ID exists in your system

## Customizing Tests

Edit the `apn-test.js` file to customize the test scenarios.
