#!/bin/bash

# Backup the database each time this build script is run
yarn db:backup

# Build the project
export NODE_OPTIONS="--max-old-space-size=8192"

# Define directories
SOURCE_PUBLIC_DIR="public" # Removed search.json logic, so this is not needed
DEST_SERVE_DIR="serve/public" # Removed search.json logic
# SEARCH_JSON_FILE="search.json" # Removed search.json logic

# Parse command line arguments
FORCE_DEPLOY=false

# Parse first argument for force deploy
if [ "$1" = "--force" ]; then
    FORCE_DEPLOY=true
    shift # Consume the --force argument
fi

# Define the Docker compose file relative to the script execution directory (assuming workspace root)
DOCKER_COMPOSE_FILE="docker-compose-mag.yml"

# Check if the Docker Compose file exists
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    echo "Error: Docker compose file not found at $DOCKER_COMPOSE_FILE" >&2
    exit 1
fi

# --- Git Operations ---
# Fetch the latest changes from the remote repository
echo "Fetching latest changes..."
git fetch
if [ $? -ne 0 ]; then
  echo "Error: git fetch failed." >&2
  exit 1
fi
echo "Git fetch completed."

# Check if there are new changes or if force deploy is enabled
LOCAL=$(git rev-parse @)
REMOTE=$(git rev-parse @{u})

if [ $LOCAL = $REMOTE ] && [ "$FORCE_DEPLOY" = false ]; then
  echo "No new changes detected and force deploy is not enabled. Exiting."
  exit 0
elif [ "$FORCE_DEPLOY" = true ]; then
  echo "Force deploy enabled. Proceeding with build steps regardless of changes."
else
  echo "New changes detected. Proceeding with build steps."
fi

# Pull the latest changes
echo "Pulling latest changes..."
git pull
if [ $? -ne 0 ]; then
  echo "Error: git pull failed." >&2
  exit 1
fi
echo "Git pull completed."

# --- Build and Promote --- 
echo "Starting build process..."
yarn build
if [ $? -ne 0 ]; then
  echo "Error: yarn build failed." >&2
  exit 1
fi
echo "Build completed successfully."

echo "Promoting build..."
yarn promote
if [ $? -ne 0 ]; then
  echo "Error: yarn promote failed." >&2
  exit 1
fi

#now I need to copy the entire public folder to serve, else the run will fail. public and .next need to be at the same place
cp -R public serve/
echo "Build promoted successfully."

# --- Restart Docker --- 
echo "Restarting Docker containers using $DOCKER_COMPOSE_FILE..."
docker-compose -f "$DOCKER_COMPOSE_FILE" restart magshop # Assuming 'abngreen' is the service name
if [ $? -ne 0 ]; then
  echo "Error: Failed to restart Docker containers." >&2
  exit 1
fi
echo "Docker containers restarted successfully."

echo "Deployment finished."

exit 0
