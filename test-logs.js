const fs = require('fs');
const path = require('path');

// Path to logs directory and file
const LOGS_DIR = path.join(process.cwd(), 'data', 'callbacks');
const LOGS_FILE_PATH = path.join(LOGS_DIR, 'logsapn.json');

console.log('Current working directory:', process.cwd());
console.log('Logs directory path:', LOGS_DIR);
console.log('Logs file path:', LOGS_FILE_PATH);

try {
  console.log('Checking if logs directory exists...');
  if (!fs.existsSync(LOGS_DIR)) {
    console.log('Creating logs directory...');
    fs.mkdirSync(LOGS_DIR, { recursive: true });
    console.log('Logs directory created successfully.');
  } else {
    console.log('Logs directory already exists.');
  }

  // Create a test log entry
  const logEntry = {
    timestamp: new Date().toISOString(),
    test: 'This is a test log entry'
  };

  // Initialize logs array
  let logs = [];
  if (fs.existsSync(LOGS_FILE_PATH)) {
    console.log('Reading existing logs file...');
    const logsData = fs.readFileSync(LOGS_FILE_PATH, 'utf8');
    try {
      logs = JSON.parse(logsData);
      if (!Array.isArray(logs)) logs = [];
    } catch (e) {
      console.error('Error parsing logs file:', e.message);
      logs = [];
    }
  }

  // Add test log entry
  logs.push(logEntry);

  // Write to logs file
  console.log('Writing to logs file...');
  fs.writeFileSync(LOGS_FILE_PATH, JSON.stringify(logs, null, 2), 'utf8');
  console.log('Successfully wrote to logs file.');

} catch (error) {
  console.error('Error:', error.message);
} 