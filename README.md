# MagShop E-commerce Platform

A modern e-commerce platform with customer and order management capabilities through a simple API.

## Features

- **API-First Design**: All features accessible through RESTful API endpoints
- **Customer Management**: Create and retrieve customer data
- **Order Management**: Create orders and track order history
- **API Key Authentication**: Simple and secure API authentication

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/magshop.git
   cd magshop
   ```

2. Install dependencies:
   ```bash
   npm install
   ```
   
3. Create a `.env` file in the root directory with your API key:
   ```
   API_KEY=your-api-key
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```
   
5. The application will be available at `http://localhost:3000`

## API Documentation

See [API.md](API.md) for detailed API documentation.

### Quick API Examples

#### Create a customer:
```bash
curl -X POST "http://localhost:3000/api/customer/create" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "name": "Test Customer",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "************"
  }'
```

#### Create an order:
```bash
curl -X POST "http://localhost:3000/api/order/create" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "customerId": "CUST001",
    "items": [
      {
        "name": "Test Product 1",
        "price": 19.99,
        "quantity": 2
      },
      {
        "name": "Test Product 2",
        "price": 29.99,
        "quantity": 1
      }
    ],
    "billingAddress": {
      "street": "123 Test St",
      "city": "Test City",
      "state": "TS",
      "zip": "12345"
    },
    "paymentMethod": "credit_card",
    "currency": "USD"
  }'
```

## Testing the API

You can test the API endpoints using the provided test scripts:

```bash
cd scripts
node test-api-simplified.js
```

This will run a series of tests against the API endpoints and display the results.

## Project Structure

- `/data` - JSON data files for customers and orders
- `/lib` - Utility libraries and middleware
- `/pages` - Next.js pages and API routes
- `/scripts` - Test scripts and utilities

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.