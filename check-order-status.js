/**
 * Utility to check the current status of an order from the server
 */
const axios = require('axios');

const SERVER_URL = 'https://sim.dailoanshop.net';
const ORDER_ID = '711TEST-001';
const API_KEY = 'apikey_test_20241231'; // Example API key based on documentation

async function checkOrderStatus() {
  try {
    console.log(`Checking status for order ${ORDER_ID}...`);
    
    // Set up headers with API authentication
    const headers = {
      'X-API-Key': API_KEY,
      'Content-Type': 'application/json'
    };
    
    // First try with direct API call
    const response = await axios.get(`${SERVER_URL}/api/orders/${ORDER_ID}`, { headers });
    
    console.log('\n=== Order Details ===');
    console.log(`ID: ${response.data.id}`);
    console.log(`Order Number: ${response.data.orderNumber}`);
    console.log(`Status: ${response.data.status}`);
    console.log(`Payment Status: ${response.data.paymentStatus}`);
    console.log(`Created: ${response.data.createdAt}`);
    console.log(`Updated: ${response.data.updatedAt}`);
    console.log(`Expired: ${response.data.isExpired}`);
    
    if (response.data.isExpired) {
      console.log(`Expiry Reason: ${response.data.expiryReason}`);
    }
    
    return response.data;
  } catch (error) {
    console.error('Error checking order status:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

// Run the check
checkOrderStatus(); 