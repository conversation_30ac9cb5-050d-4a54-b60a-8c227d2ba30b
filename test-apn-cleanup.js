#!/usr/bin/env node

const { DatabaseManager } = require('./scripts/db-management.js');

async function testApnLogCleanup() {
    console.log('🔄 Testing APN Log Management\n');
    
    const dbManager = new DatabaseManager();
    await dbManager.init();
    
    try {
        // 1. Analyze APN logs
        console.log('📊 Current APN Log Analysis:');
        console.log('============================');
        const apnStats = await dbManager.analyzeApnLogs();
        
        if (apnStats) {
            console.log(`Total APN callbacks: ${apnStats.total}`);
            console.log(`🧪 Test logs: ${apnStats.testLogs}`);
            console.log(`🏭 Production logs: ${apnStats.productionLogs}`);
            console.log(`🕐 Old logs (>90 days): ${apnStats.oldLogs}`);
            console.log(`📅 Recent logs (7 days): ${apnStats.recentLogs}`);
            console.log(`✅ Successful (OK): ${apnStats.responseOK}`);
            console.log(`❌ Not Found: ${apnStats.responseNotFound}`);
            console.log(`⚡ Avg processing: ${apnStats.avgProcessingTime}ms`);
        } else {
            console.log('❌ No APN logs found or invalid format');
            return;
        }
        
        console.log('\n🧹 Cleanup Options Available:');
        console.log('==============================');
        
        // 2. Test cleanup (dry run)
        const testLogsCount = await dbManager.cleanupTestApnLogs(true);
        console.log(`- Would clean ${testLogsCount} test APN logs`);
        
        const oldLogsCount = await dbManager.cleanupOldApnLogs(true);
        console.log(`- Would clean ${oldLogsCount} old APN logs (>90 days)`);
        
        console.log('\n💡 To clean APN logs:');
        console.log('=====================');
        console.log('Option 9:  Clean old APN logs (>90 days)');
        console.log('Option 10: Clean test APN logs');
        console.log('Option 18: Wipe all logs (including APN)');
        console.log('Option 20: Wipe entire database');
        
        console.log('\n🎯 Recommended Actions:');
        console.log('=======================');
        if (testLogsCount > 0) {
            console.log(`⚠️  You have ${testLogsCount} test APN logs that could be cleaned`);
            console.log('   Run: ./db-manage.sh → Option 10 → n (no dry run)');
        }
        if (oldLogsCount > 0) {
            console.log(`⚠️  You have ${oldLogsCount} old APN logs (>90 days) that could be cleaned`);
            console.log('   Run: ./db-manage.sh → Option 9 → n (no dry run)');
        }
        if (testLogsCount === 0 && oldLogsCount === 0) {
            console.log('✅ Your APN logs are clean - no cleanup needed');
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

// Run the test
testApnLogCleanup().catch(console.error); 