*/carts.json
data/carts/carts.json
*/orders.json
*/checkout-progress.json

# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for committing if needed)
#.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

*/sw.js

/server/dist

# Generated via next-pwa
**/public/workbox**
**/public/sw**

node_modules
.next
serve

# Cart data
#/data/carts/*.json
!/data/carts/.gitkeep
