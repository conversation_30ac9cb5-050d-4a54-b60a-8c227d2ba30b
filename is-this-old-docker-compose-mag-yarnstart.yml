networks:
  web:
    external: true

services:
  # Main ABN.green application (with Pages Router API endpoints)
  magshop:
    image: node:20
    container_name: magshop
    restart: always
    #working_dir: /app
    working_dir: /fuck
    command: sh -c "yarn start"
    volumes:
      - .:/fuck
    environment:
      - NODE_ENV=production
      #- NEXTAUTH_URL=https://magshop.dailoanshop.net
      #- NEXT_PUBLIC_SITE_URL=https://magshop.dailoanshop.net
    ports:
      - 3000:3000
    labels:
      - traefik.enable=true
      - traefik.http.routers.magshop.rule=Host(`sim.dailoanshop.net`, `simserver.dailoanshop.net`, `sim.abnasia.org`, `dockersim.abnasia.org`)
      - traefik.http.routers.magshop.tls=true
      - traefik.http.routers.magshop.tls.certresolver=lets-encrypt
      - traefik.http.services.magshop.loadbalancer.server.port=3000
    networks:
      - web
