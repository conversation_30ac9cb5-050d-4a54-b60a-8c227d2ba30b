import React, { useState } from 'react';
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import PaymentFormZaloPay from './PaymentFormZaloPay';

const Input = ({ onChange, value, name, placeholder }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    type="text"
    placeholder={placeholder}
    name={name}
  />
);

const PaymentFormPayPal = ({ handleSubmit, errorMessage }) => {
  const [input, setInput] = useState({
    name: "",
    email: "",
    street: "",
    city: "",
    postal_code: "",
    state: "",
  });

  const stripe = useStripe();
  const elements = useElements();

  const onChange = e => {
    setInput({ ...input, [e.target.name]: e.target.value });
  };

  return (
    <div className="flex flex-1 pt-8 flex-col">
      <div className="mt-4 border-t pt-10">
        <form onSubmit={handleSubmit}>
          {errorMessage ? <span>{errorMessage}</span> : ""}
          <Input
            onChange={onChange}
            value={input.name}
            name="name"
            placeholder="Cardholder name"
          />
          {/* <CardElement className="mt-2 shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" /> */}
          <Input
            onChange={onChange}
            value={input.email}
            name="email"
            placeholder="PayPal Email"
          />
          <Input
            onChange={onChange}
            value={input.street}
            name="street"
            placeholder="Street"
          />
          <Input
            onChange={onChange}
            value={input.city}
            name="city"
            placeholder="City"
          />
          <Input
            onChange={onChange}
            value={input.state}
            name="state"
            placeholder="State"
          />
          <Input
            onChange={onChange}
            value={input.postal_code}
            name="postal_code"
            placeholder="Postal Code"
          />
          <button
            type="submit"
            disabled={!stripe}
            onClick={handleSubmit}
            className="hidden md:block bg-primary hover:bg-black text-white font-bold py-2 px-4 mt-4 rounded focus:outline-none focus:shadow-outline"
          >
            ĐẶT HÀNG
          </button>
        </form>
      </div>
    </div>
  );
};

export default PaymentFormPayPal;

