import React from 'react';
import {
  FacebookShareButton,
  FacebookMessengerShareButton,
  TwitterShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
  TelegramShareButton,
  ViberShareButton,
  LineShareButton,
  EmailShareButton,
} from "react-share"

import {
  FacebookIcon,
  FacebookMessengerIcon,
  TwitterIcon,
  LinkedinIcon,
  WhatsappIcon,
  TelegramIcon,
  ViberIcon,
  LineIcon,
  EmailIcon,
} from "react-share"

import { useEffect } from "react"

const SocialShare = ({ url }) => {
  useEffect(() => {
    const handleLoad = () => {
      console.log("Zalo Share SDK loaded")
    }

    const zaloScript = document.createElement("script")
    zaloScript.src = "https://sp.zalo.me/plugins/sdk.js"
    zaloScript.async = true
    zaloScript.onload = handleLoad
    document.body.appendChild(zaloScript)

    return () => {
      document.body.removeChild(zaloScript)
    }
  }, [])

  return (
    <div className="flex flex-wrap justify-center md:space-x-1">
      <div className="boxstyle_6">
        <FacebookShareButton url={url}>
          <FacebookIcon size={32} round />
        </FacebookShareButton>
        <FacebookMessengerShareButton
          url={url}
          appId="828547221491699" // Wise Facebook App
        >
          <FacebookMessengerIcon size={32} round />
        </FacebookMessengerShareButton>
        <TwitterShareButton url={url}>
          <TwitterIcon size={32} round />
        </TwitterShareButton>
        <LinkedinShareButton url={url}>
          <LinkedinIcon size={32} round />
        </LinkedinShareButton>
        
        {/* <div
          className="zalo-share-button"
          data-href={url}
          data-oaid="57863838049596727"
          data-layout="5"
          data-color="white"
          data-share-type="2"
          data-customize="false"
        ></div> */}

        <WhatsappShareButton url={url}>
          <WhatsappIcon size={32} round />
        </WhatsappShareButton>
        <TelegramShareButton url={url}>
          <TelegramIcon size={32} round />
        </TelegramShareButton>
        <ViberShareButton url={url}>
          <ViberIcon size={32} round />
        </ViberShareButton>
        <LineShareButton url={url}>
          <LineIcon size={32} round />
        </LineShareButton>
        <EmailShareButton url={url}>
          <EmailIcon size={32} round />
        </EmailShareButton>
      </div>
    </div>
  )
}

export default SocialShare 