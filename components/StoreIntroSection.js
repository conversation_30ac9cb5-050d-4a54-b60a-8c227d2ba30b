import { DisplayMediumNoBox } from "./index"
import Map from "./Map"

const StoreIntroSection = ({ currentstore, latitude, longitude }) => {
  // Function to process the intro text: convert \n to <br> and ensure HTML tags work
  const formatIntroText = (text) => {
    if (!text) return '';
    
    // Replace \n with <br> tags
    const withLineBreaks = text.replace(/\\n/g, '<br>');
    
    return withLineBreaks;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-[1.02] transition-transform duration-300">
      <div className="flex flex-col lg:flex-row">
        <div className="w-full lg:w-1/2 p-8">
          <div className="space-y-6">
            {/* Store Logo and Name */}
            <div className="flex items-center space-x-4 mb-6">
              {currentstore.logo && (
                <img 
                  src={currentstore.logo} 
                  alt={currentstore.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                />
              )}
              <h1 className="text-3xl font-bold text-gray-800">{currentstore.name}</h1>
            </div>

            {/* Store Introduction */}
            <div className="prose prose-lg">
              <div 
                className="text-gray-600 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: formatIntroText(currentstore.intro) }}
              />
            </div>

            {/* Store Address */}
            <div className="flex items-start space-x-3 bg-gray-50 p-4 rounded-lg">
              <svg className="w-6 h-6 text-gray-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <p className="text-gray-600">{currentstore.locations[0].adderss}</p>
            </div>

            {/* Store Flyer */}
            {currentstore.flyer && currentstore.flyer !== "" && (
              <div className="mt-6">
                <DisplayMediumNoBox
                  imageSrc={currentstore.flyer}
                  link={currentstore.flyer}
                  buttonText={
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      <span>XEM FLYER</span>
                    </div>
                  }
                />
              </div>
            )}
          </div>
        </div>

        {/* Map Section */}
        <div className="w-full lg:w-1/2 relative">
          <div className="h-full min-h-[400px] overflow-hidden">
            <Map
              latitude={latitude}
              longitude={longitude}
              iconurl={currentstore.logo}
            />
          </div>
          
          {/* Optional: Add a floating info card over the map */}
          <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm p-4 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <a 
                href={`https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-700 text-sm font-medium flex items-center space-x-1"
              >
                <span>Chỉ đường</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StoreIntroSection