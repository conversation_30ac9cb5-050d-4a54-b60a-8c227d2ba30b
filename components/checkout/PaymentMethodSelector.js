/**
 * Payment Method Selector Component
 * 
 * Handles payment method selection with currency-specific options
 * Integrates with PaymentRegistry service for method availability
 */

import React, { useEffect, useState } from 'react';
import { useCheckout } from '../../context/CheckoutContext';
import { PaymentRegistry } from '../../services/PaymentRegistry';
import { CurrencyManager } from '../../services/CurrencyManager';
import { logCheckoutEvent } from '../../utils/checkoutLogger';

const PaymentMethodSelector = ({ currency, onNext, onBack }) => {
  const {
    selectedPaymentMethod,
    setPaymentMethod,
    setError,
    clearError
  } = useCheckout();

  const [availableMethods, setAvailableMethods] = useState([]);
  const [selectedMethod, setSelectedMethod] = useState(selectedPaymentMethod);

  // Load available payment methods for currency
  useEffect(() => {
    if (currency) {
      const methods = PaymentRegistry.getMethodsForCurrency(currency);
      setAvailableMethods(methods);
      
      // Clear selected method if not available for this currency
      if (selectedMethod && !methods.find(m => m.id === selectedMethod)) {
        setSelectedMethod(null);
        setPaymentMethod(null);
      }
    }
  }, [currency]);

  const handleMethodSelect = (methodId) => {
    clearError();
    
    // Validate method availability
    const validation = PaymentRegistry.validateMethodSelection(methodId, currency);
    if (!validation.isValid) {
      setError(validation.errors.join(', '));
      return;
    }

    setSelectedMethod(methodId);
    setPaymentMethod(methodId);
    
    logCheckoutEvent('payment_method_selected', {
      method: methodId,
      currency,
      available: validation.isValid
    });
  };

  const handleContinue = () => {
    if (!selectedMethod) {
      setError('Please select a payment method');
      return;
    }

    clearError();
    onNext();
  };

  const renderMethodCard = (method) => {
    const isSelected = selectedMethod === method.id;
    const isAvailable = method.available;

    return (
      <div
        key={method.id}
        className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all ${
          isSelected
            ? 'border-blue-500 bg-blue-50'
            : isAvailable
            ? 'border-gray-200 hover:border-gray-300'
            : 'border-gray-100 bg-gray-50 cursor-not-allowed'
        }`}
        onClick={() => isAvailable && handleMethodSelect(method.id)}
      >
        {/* Selection Indicator */}
        <div className="absolute top-3 right-3">
          {isSelected && (
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>

        {/* Method Content */}
        <div className="flex items-center space-x-4">
          
          {/* Method Icon */}
          <div className={`text-2xl ${isAvailable ? '' : 'opacity-50'}`}>
            {typeof method.icon === 'string' ? method.icon : method.icon}
          </div>

          {/* Method Details */}
          <div className="flex-1">
            <h3 className={`font-medium ${isAvailable ? 'text-gray-900' : 'text-gray-400'}`}>
              {method.name}
            </h3>
            
            {method.description && (
              <p className={`text-sm mt-1 ${isAvailable ? 'text-gray-600' : 'text-gray-400'}`}>
                {method.description}
              </p>
            )}

            {!isAvailable && (
              <p className="text-sm text-red-500 mt-1">
                Currently unavailable
              </p>
            )}
          </div>
        </div>

        {/* Method Features */}
        {method.features && isAvailable && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex flex-wrap gap-2">
              {method.features.map((feature, index) => (
                <span 
                  key={index}
                  className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const groupMethodsByType = (methods) => {
    const groups = {
      digital: [],
      cash: [],
      card: [],
      bank: [],
      other: []
    };

    methods.forEach(method => {
      if (['cod'].includes(method.id)) {
        groups.cash.push(method);
      } else if (['stripe', '7-11-card', 'credit-card'].includes(method.id)) {
        groups.card.push(method);
      } else if (['banktransfer', 'taiwanbanktransfer', 'vietnambanktransfer'].includes(method.id)) {
        groups.bank.push(method);
      } else if (['momo', 'zalopay', 'paypal'].includes(method.id)) {
        groups.digital.push(method);
      } else {
        groups.other.push(method);
      }
    });

    return groups;
  };

  const renderMethodGroup = (title, methods, icon) => {
    if (methods.length === 0) return null;

    return (
      <div key={title} className="space-y-3">
        <h3 className="flex items-center text-lg font-medium text-gray-900">
          <span className="text-xl mr-2">{icon}</span>
          {title}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {methods.map(renderMethodCard)}
        </div>
      </div>
    );
  };

  if (!availableMethods.length) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">💳</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No payment methods available
        </h3>
        <p className="text-gray-600 mb-6">
          No payment methods are currently available for {CurrencyManager.getDisplayName(currency)}.
        </p>
        <button
          onClick={onBack}
          className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  const methodGroups = groupMethodsByType(availableMethods);

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Choose Payment Method
        </h2>
        <p className="text-gray-600 mt-1">
          Select how you'd like to pay for your order in {CurrencyManager.getDisplayName(currency)}
        </p>
      </div>

      {/* Payment Method Groups */}
      <div className="space-y-8">
        {renderMethodGroup('Digital Wallets', methodGroups.digital, '📱')}
        {renderMethodGroup('Credit & Debit Cards', methodGroups.card, '💳')}
        {renderMethodGroup('Bank Transfer', methodGroups.bank, '🏦')}
        {renderMethodGroup('Cash Payment', methodGroups.cash, '💵')}
        {renderMethodGroup('Other Methods', methodGroups.other, '🔄')}
      </div>

      {/* Selected Method Summary */}
      {selectedMethod && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-green-600 text-xl mr-3">✓</div>
            <div>
              <h4 className="font-medium text-green-900">
                Payment method selected
              </h4>
              <p className="text-green-700 text-sm">
                {availableMethods.find(m => m.id === selectedMethod)?.name}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          onClick={handleContinue}
          disabled={!selectedMethod}
          className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Continue to Payment
        </button>
      </div>
    </div>
  );
};

export default PaymentMethodSelector;