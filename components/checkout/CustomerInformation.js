/**
 * Customer Information Component
 * 
 * Handles customer information collection with auto-population from logged-in user
 * Supports both authenticated and guest checkout flows
 */

import React, { useEffect, useState } from 'react';
import { useCheckout } from '../../context/CheckoutContext';
import { getCustomerId } from '../../utils/customerAuth';
import { logCheckoutEvent, logCheckoutError } from '../../utils/checkoutLogger';

const CustomerInformation = ({ onNext, onBack }) => {
  const {
    customerInfo,
    setCustomerInfo,
    setError,
    clearError
  } = useCheckout();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
    customerId: null
  });
  
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [errors, setErrors] = useState({});

  // Load customer data on component mount
  useEffect(() => {
    loadCustomerData();
  }, []);

  // Sync with context when customerInfo changes
  useEffect(() => {
    if (customerInfo && Object.keys(customerInfo).length > 0) {
      setFormData(prev => ({
        ...prev,
        ...customerInfo
      }));
    }
  }, [customerInfo]);

  const loadCustomerData = async () => {
    try {
      setLoading(true);
      const customerId = getCustomerId();
      
      if (customerId) {
        setIsLoggedIn(true);
        
        logCheckoutEvent('loading_customer_data', { customerId });
        
        // Fetch customer data from API
        const response = await fetch(`/api/customer/data?customerId=${customerId}`);
        
        if (response.ok) {
          const customerData = await response.json();
          
          const loadedData = {
            customerId,
            name: customerData.name || '',
            email: customerData.email || '',
            phone: customerData.phone || '',
            address: customerData.address || '',
            notes: customerData.notes || ''
          };
          
          setFormData(loadedData);
          setCustomerInfo(loadedData);
          
          logCheckoutEvent('customer_data_loaded', { 
            customerId,
            hasName: !!customerData.name,
            hasEmail: !!customerData.email,
            hasPhone: !!customerData.phone
          });
        } else {
          console.warn('Failed to load customer data:', response.statusText);
          // Keep the user logged in but use empty form
          setFormData(prev => ({ ...prev, customerId }));
          setCustomerInfo({ customerId });
        }
      } else {
        // Guest checkout
        setIsLoggedIn(false);
        logCheckoutEvent('guest_checkout_started');
      }
    } catch (error) {
      logCheckoutError('customer_data_load_failed', error);
      console.error('Error loading customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear field-specific error
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }

    // Update context
    setCustomerInfo({
      ...formData,
      [field]: value
    });
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields
    if (!formData.name?.trim()) {
      newErrors.name = 'Họ và tên là bắt buộc';
    }
    
    if (!formData.email?.trim()) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }
    
    if (!formData.phone?.trim()) {
      newErrors.phone = 'Số điện thoại là bắt buộc';
    } else if (!/^[\d\s\-\+\(\)]{8,}$/.test(formData.phone)) {
      newErrors.phone = 'Số điện thoại không hợp lệ';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    clearError();
    
    if (!validateForm()) {
      setError('Vui lòng điền đầy đủ thông tin bắt buộc');
      return;
    }

    try {
      // Update customer info in context
      setCustomerInfo(formData);
      
      // If logged in, update customer profile
      if (isLoggedIn && formData.customerId) {
        await updateCustomerProfile();
      }

      logCheckoutEvent('customer_info_completed', {
        isLoggedIn,
        customerId: formData.customerId,
        hasAddress: !!formData.address,
        hasNotes: !!formData.notes
      });

      onNext();
    } catch (error) {
      logCheckoutError('customer_info_save_failed', error);
      setError('Không thể lưu thông tin khách hàng. Vui lòng thử lại.');
    }
  };

  const updateCustomerProfile = async () => {
    try {
      const response = await fetch('/api/customer/update-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customerId: formData.customerId,
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          notes: formData.notes
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update customer profile');
      }

      logCheckoutEvent('customer_profile_updated', {
        customerId: formData.customerId
      });
    } catch (error) {
      console.warn('Failed to update customer profile:', error);
      // Don't block checkout if profile update fails
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Đang tải thông tin khách hàng...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Thông tin cá nhân
        </h2>
        <p className="text-gray-600 mt-1">
          {isLoggedIn 
            ? 'Kiểm tra và cập nhật thông tin của bạn' 
            : 'Vui lòng cung cấp thông tin liên hệ để xử lý đơn hàng'
          }
        </p>
      </div>

      {/* Login Status */}
      {isLoggedIn ? (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-green-600 text-xl mr-3">✓</div>
            <div>
              <h4 className="font-medium text-green-900">
                Đã đăng nhập
              </h4>
              <p className="text-green-700 text-sm">
                Thông tin của bạn đã được tự động điền
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900">
                Thanh toán với tư cách khách
              </h4>
              <p className="text-blue-700 text-sm">
                Bạn có thể đăng nhập để lưu thông tin và theo dõi đơn hàng
              </p>
            </div>
            <button
              onClick={() => window.location.href = '/customer/login'}
              className="text-sm bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
            >
              Đăng nhập
            </button>
          </div>
        </div>
      )}

      {/* Customer Information Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        {/* Name */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Họ và Tên <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Nhập họ và tên đầy đủ"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.email ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="<EMAIL>"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Điện thoại <span className="text-red-500">*</span>
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.phone ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="0912345678 hoặc +84912345678"
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
          )}
        </div>

        {/* Address */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Địa chỉ (Tùy chọn)
          </label>
          <input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Số nhà, đường, phường/xã, quận/huyện, tỉnh/thành phố"
          />
          <p className="mt-1 text-sm text-gray-500">
            Địa chỉ này có thể được sử dụng cho giao hàng nếu cần
          </p>
        </div>

        {/* Notes */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ghi chú (Tùy chọn)
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Ghi chú thêm về đơn hàng hoặc yêu cầu đặc biệt..."
          />
        </div>
      </div>

      {/* Privacy Notice */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-start">
          <div className="text-gray-400 text-lg mr-3">🔒</div>
          <div className="text-sm text-gray-600">
            <p className="font-medium mb-1">Bảo mật thông tin</p>
            <p>
              Thông tin cá nhân của bạn sẽ được bảo mật và chỉ sử dụng để xử lý đơn hàng. 
              Chúng tôi không chia sẻ thông tin với bên thứ ba mà không có sự đồng ý của bạn.
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Quay lại
        </button>
        <button
          onClick={handleContinue}
          className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Tiếp tục
        </button>
      </div>
    </div>
  );
};

export default CustomerInformation;