/**
 * Cart Review Component
 * 
 * First step of checkout - allows user to review and modify cart items
 * Handles currency selection and item quantity updates
 */

import React from 'react';
import { FaTrash, FaPlus, FaMinus } from 'react-icons/fa';
import { useCheckout } from '../../context/CheckoutContext';
import { CurrencyManager } from '../../services/CurrencyManager';
import { getProductImage, handleImageError } from '../../utils/imageUtils';

const CartReview = ({ items, currency, onNext }) => {
  const {
    // State
    cartItems,
    availableCurrencies,
    selectedCurrency,
    
    // Actions
    updateItemQuantity,
    removeItem,
    setSelectedCurrency
  } = useCheckout();

  const handleQuantityChange = (itemId, newQuantity) => {
    if (newQuantity < 1) {
      removeItem(itemId);
    } else {
      updateItemQuantity(itemId, newQuantity);
    }
  };

  const handleCurrencyChange = (newCurrency) => {
    setSelectedCurrency(newCurrency);
  };

  const calculateItemTotal = (item) => {
    const price = parseFloat(item.price) || 0;
    const quantity = parseInt(item.quantity) || 1;
    return price * quantity;
  };

  const calculateSubtotal = () => {
    return items.reduce((total, item) => {
      return total + calculateItemTotal(item);
    }, 0);
  };

  if (!items || items.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">🛒</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Your cart is empty
        </h3>
        <p className="text-gray-600 mb-6">
          Add some items to your cart to get started.
        </p>
        <button
          onClick={() => window.history.back()}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Continue Shopping
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Review Your Cart
        </h2>
        <p className="text-gray-600 mt-1">
          Review your items and make any changes before proceeding
        </p>
      </div>

      {/* Currency Selector */}
      {availableCurrencies.length > 1 && (
        <div className="bg-blue-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            Select Currency
          </h3>
          <div className="flex flex-wrap gap-2">
            {availableCurrencies.map(curr => (
              <button
                key={curr}
                onClick={() => handleCurrencyChange(curr)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  selectedCurrency === curr
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-blue-600 border border-blue-200 hover:bg-blue-100'
                }`}
              >
                {CurrencyManager.getDisplayName(curr)}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Cart Items */}
      <div className="space-y-4">
        {items.map((item) => (
          <div key={item.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start space-x-4">
              
              {/* Product Image */}
              <div className="w-16 h-16 flex-shrink-0">
                <img
                  src={getProductImage(item)}
                  alt={item.name}
                  onError={handleImageError}
                  className="w-full h-full object-cover rounded-md"
                />
              </div>

              {/* Product Details */}
              <div className="flex-1 min-w-0">
                <h4 className="text-lg font-medium text-gray-900 truncate">
                  {item.name}
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  SKU: {item.sku}
                </p>
                
                {/* Product Configuration */}
                {item.configuration && (
                  <div className="mt-2 text-sm text-gray-700">
                    {Object.entries(item.configuration).map(([key, value]) => (
                      <div key={key} className="flex">
                        <span className="font-medium capitalize">{key}:</span>
                        <span className="ml-2">{value}</span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Price and Quantity Controls */}
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center space-x-3">
                    
                    {/* Quantity Controls */}
                    <div className="flex items-center border border-gray-300 rounded-md">
                      <button
                        onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                        className="p-2 hover:bg-gray-100 transition-colors"
                        disabled={item.quantity <= 1}
                      >
                        <FaMinus className="w-3 h-3" />
                      </button>
                      <span className="px-3 py-1 border-l border-r border-gray-300 min-w-[3rem] text-center">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                        className="p-2 hover:bg-gray-100 transition-colors"
                      >
                        <FaPlus className="w-3 h-3" />
                      </button>
                    </div>

                    {/* Unit Price */}
                    <div className="text-sm text-gray-600">
                      {CurrencyManager.formatPrice(item.price, item.currency)} each
                    </div>
                  </div>

                  {/* Item Total and Remove Button */}
                  <div className="flex items-center space-x-3">
                    <div className="text-lg font-medium text-gray-900">
                      {CurrencyManager.formatPrice(calculateItemTotal(item), item.currency)}
                    </div>
                    <button
                      onClick={() => removeItem(item.id)}
                      className="text-red-600 hover:text-red-800 p-1 transition-colors"
                      title="Remove item"
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Order Summary */}
      <div className="border-t border-gray-200 pt-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <span className="text-lg font-medium text-gray-900">
              Subtotal ({items.length} {items.length === 1 ? 'item' : 'items'})
            </span>
            <span className="text-xl font-bold text-gray-900">
              {CurrencyManager.formatPrice(calculateSubtotal(), selectedCurrency)}
            </span>
          </div>
          
          <div className="text-sm text-gray-600 mb-4">
            Shipping and taxes will be calculated at checkout
          </div>

          {/* Continue Button */}
          <div className="flex justify-between">
            <button
              onClick={() => window.history.back()}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Continue Shopping
            </button>
            <button
              onClick={onNext}
              disabled={items.length === 0}
              className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              Proceed to Checkout
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartReview;