/**
 * Delivery Address Component
 * 
 * Handles delivery address collection with validation and formatting
 * Integrates with AddressManager service for address validation
 */

import React, { useState, useEffect } from 'react';
import { useCheckout } from '../../context/CheckoutContext';
import { AddressManager } from '../../services/AddressManager';
import { ProductTypeManager } from '../../services/ProductTypeManager';
import { logCheckoutEvent } from '../../utils/checkoutLogger';

const DeliveryAddress = ({ onNext, onBack }) => {
  const {
    deliveryAddress,
    productType,
    setDeliveryAddress,
    setError,
    clearError
  } = useCheckout();

  const [formData, setFormData] = useState({
    address: '',
    city: '',
    postalCode: '',
    country: 'VN',
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [deliveryZone, setDeliveryZone] = useState(null);
  const [addressRequired, setAddressRequired] = useState(true);

  // Initialize form data
  useEffect(() => {
    if (deliveryAddress && Object.keys(deliveryAddress).length > 0) {
      setFormData(prev => ({
        ...prev,
        ...deliveryAddress
      }));
    }
  }, [deliveryAddress]);

  // Check if address is required for this product type
  useEffect(() => {
    const required = ProductTypeManager.requiresAddress(productType);
    setAddressRequired(required);
    
    if (!required) {
      // If address not required, we can skip or make it optional
      logCheckoutEvent('address_optional_for_product_type', { productType });
    }
  }, [productType]);

  // Calculate delivery zone when country changes
  useEffect(() => {
    if (formData.country) {
      const zone = AddressManager.getDeliveryZone(formData);
      setDeliveryZone(zone);
    }
  }, [formData.country]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear field-specific error
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }

    // Update context
    setDeliveryAddress({
      ...formData,
      [field]: value
    });
  };

  const validateForm = () => {
    if (!addressRequired) {
      return true; // Skip validation if address not required
    }

    const validation = AddressManager.validateAddress(formData);
    
    if (!validation.isValid) {
      const newErrors = {};
      validation.errors.forEach(error => {
        const field = error.toLowerCase().split(' ')[0];
        newErrors[field] = error;
      });
      setErrors(newErrors);
      return false;
    }

    setErrors({});
    return true;
  };

  const handleContinue = () => {
    clearError();

    if (!validateForm()) {
      setError('Vui lòng điền đầy đủ thông tin địa chỉ giao hàng');
      return;
    }

    // Normalize and save address
    const normalizedAddress = AddressManager.normalizeAddress(formData);
    setDeliveryAddress(normalizedAddress);

    logCheckoutEvent('delivery_address_completed', {
      country: formData.country,
      zone: deliveryZone?.zone,
      isRequired: addressRequired,
      deliveryTime: deliveryZone?.deliveryTime,
      cost: deliveryZone?.cost
    });

    onNext();
  };

  const handleSkip = () => {
    if (addressRequired) return;
    
    setDeliveryAddress({});
    logCheckoutEvent('delivery_address_skipped', { productType });
    onNext();
  };

  const supportedCountries = AddressManager.getSupportedCountries();

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Địa chỉ giao hàng
        </h2>
        <p className="text-gray-600 mt-1">
          {addressRequired 
            ? 'Cung cấp địa chỉ để chúng tôi có thể giao hàng cho bạn'
            : 'Địa chỉ giao hàng là tùy chọn cho loại sản phẩm này'
          }
        </p>
      </div>

      {/* Skip Option for Non-Required Address */}
      {!addressRequired && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900">
                Không cần giao hàng
              </h4>
              <p className="text-blue-700 text-sm">
                Sản phẩm này không yêu cầu địa chỉ giao hàng. Bạn có thể bỏ qua bước này.
              </p>
            </div>
            <button
              onClick={handleSkip}
              className="text-sm bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
            >
              Bỏ qua
            </button>
          </div>
        </div>
      )}

      {/* Address Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        {/* Country */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Quốc gia {addressRequired && <span className="text-red-500">*</span>}
          </label>
          <select
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.country ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            {supportedCountries.map(country => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
          {errors.country && (
            <p className="mt-1 text-sm text-red-600">{errors.country}</p>
          )}
        </div>

        {/* City */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tỉnh/Thành phố {addressRequired && <span className="text-red-500">*</span>}
          </label>
          <input
            type="text"
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.city ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Hà Nội, Hồ Chí Minh, Đà Nẵng..."
          />
          {errors.city && (
            <p className="mt-1 text-sm text-red-600">{errors.city}</p>
          )}
        </div>

        {/* Address */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Địa chỉ chi tiết {addressRequired && <span className="text-red-500">*</span>}
          </label>
          <input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.address ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Số nhà, tên đường, phường/xã, quận/huyện"
          />
          {errors.address && (
            <p className="mt-1 text-sm text-red-600">{errors.address}</p>
          )}
        </div>

        {/* Postal Code */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Mã bưu điện {addressRequired && <span className="text-red-500">*</span>}
          </label>
          <input
            type="text"
            value={formData.postalCode}
            onChange={(e) => handleInputChange('postalCode', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.postalCode ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="100000"
          />
          {errors.postalCode && (
            <p className="mt-1 text-sm text-red-600">{errors.postalCode}</p>
          )}
          {AddressManager.getCountryConfig(formData.country)?.postalCodeFormat && (
            <p className="mt-1 text-sm text-gray-500">
              Định dạng: {AddressManager.getCountryConfig(formData.country).postalCodeFormat}
            </p>
          )}
        </div>

        {/* Delivery Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ghi chú giao hàng
          </label>
          <input
            type="text"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Tầng 2, chuông cửa, giao giờ hành chính..."
          />
        </div>
      </div>

      {/* Delivery Zone Information */}
      {deliveryZone && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="text-green-600 text-xl mr-3">🚚</div>
            <div>
              <h4 className="font-medium text-green-900 mb-2">
                Thông tin giao hàng
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Vùng giao hàng:</span>
                  <p className="text-gray-600 capitalize">{deliveryZone.zone}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Thời gian:</span>
                  <p className="text-gray-600">{deliveryZone.deliveryTime}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Phí giao hàng:</span>
                  <p className="text-gray-600">
                    {deliveryZone.cost === 0 ? 'Miễn phí' : `$${deliveryZone.cost}`}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Address Preview */}
      {AddressManager.isCompleteAddress(formData) && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">
            Xem trước địa chỉ
          </h4>
          <div className="text-sm text-gray-600 whitespace-pre-line">
            {AddressManager.formatAddress(formData, 'shipping')}
          </div>
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Quay lại
        </button>
        <button
          onClick={handleContinue}
          className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Tiếp tục
        </button>
      </div>
    </div>
  );
};

export default DeliveryAddress;