/**
 * Payment Details Component
 * 
 * Handles payment form display based on selected payment method
 * Integrates with existing payment form components
 */

import React, { useState, useEffect } from 'react';
import { useCheckout } from '../../context/CheckoutContext';
import { PaymentRegistry } from '../../services/PaymentRegistry';
import MasterPaymentForms from '../taiwan/payment/MasterPaymentForms';
import PaymentFormCOD from '../PaymentFormCOD';

const PaymentDetails = ({ onNext, onBack }) => {
  const {
    selectedPaymentMethod,
    selectedCurrency,
    filteredItems,
    customerInfo,
    deliveryAddress,
    setError,
    clearError
  } = useCheckout();

  const [paymentData, setPaymentData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePaymentSuccess = (data) => {
    setPaymentData(data);
    clearError();
    onNext();
  };

  const handlePaymentError = (error) => {
    setError(error || 'Có lỗi xảy ra trong quá trình thanh toán');
    setIsProcessing(false);
  };

  const renderPaymentForm = () => {
    if (!selectedPaymentMethod) {
      return (
        <div className="text-center py-8">
          <div className="text-gray-400 text-6xl mb-4">💳</div>
          <p className="text-gray-600">Vui lòng chọn phương thức thanh toán</p>
          <button
            onClick={onBack}
            className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Chọn phương thức thanh toán
          </button>
        </div>
      );
    }

    // Prepare common props for payment forms
    const commonProps = {
      currency: selectedCurrency,
      items: filteredItems,
      customerInfo,
      deliveryAddress,
      paymentMethod: selectedPaymentMethod,
      onSuccess: handlePaymentSuccess,
      onError: handlePaymentError,
      isProcessing,
      setIsProcessing
    };

    // Render appropriate payment form based on method
    switch (selectedPaymentMethod) {
      case 'cod':
        return <PaymentFormCOD {...commonProps} />;
        
      case '7-11':
      case '7-11-card':
      case 'family-mart':
      case 'taiwanbanktransfer':
        return <MasterPaymentForms {...commonProps} />;
        
      default:
        return (
          <div className="text-center py-8">
            <div className="text-gray-400 text-6xl mb-4">⚠️</div>
            <p className="text-gray-600">
              Phương thức thanh toán này chưa được hỗ trợ
            </p>
            <button
              onClick={onBack}
              className="mt-4 bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Chọn phương thức khác
            </button>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Chi tiết thanh toán
        </h2>
        <p className="text-gray-600 mt-1">
          Hoàn tất thông tin thanh toán để xử lý đơn hàng
        </p>
      </div>

      {/* Selected Payment Method Info */}
      {selectedPaymentMethod && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-blue-600 text-xl mr-3">💳</div>
              <div>
                <h4 className="font-medium text-blue-900">
                  Phương thức thanh toán đã chọn
                </h4>
                <p className="text-blue-700 text-sm capitalize">
                  {PaymentRegistry.getMethod(selectedPaymentMethod, selectedCurrency)?.name || selectedPaymentMethod}
                </p>
              </div>
            </div>
            <button
              onClick={onBack}
              className="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Thay đổi
            </button>
          </div>
        </div>
      )}

      {/* Payment Form */}
      <div className="bg-white rounded-lg border border-gray-200">
        {renderPaymentForm()}
      </div>

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
              <div>
                <h4 className="font-medium text-gray-900">
                  Đang xử lý thanh toán...
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  Vui lòng không tắt trình duyệt
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Notice */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="text-green-600 text-lg mr-3">🔒</div>
          <div className="text-sm">
            <p className="font-medium text-green-900 mb-1">
              Thanh toán được bảo mật
            </p>
            <p className="text-green-700">
              Tất cả thông tin thanh toán được mã hóa và xử lý an toàn. 
              Chúng tôi không lưu trữ thông tin thẻ tín dụng của bạn.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentDetails;