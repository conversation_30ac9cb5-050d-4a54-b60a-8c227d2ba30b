/**
 * Product Configuration Component
 * 
 * Handles product-specific configuration options
 * Only shown for products that require configuration
 */

import React, { useState } from 'react';
import { useCheckout } from '../../context/CheckoutContext';
import { ProductTypeManager } from '../../services/ProductTypeManager';
import { logCheckoutEvent } from '../../utils/checkoutLogger';

const ProductConfiguration = ({ items, onNext, onBack }) => {
  const { setError, clearError, productType } = useCheckout();
  const [configurations, setConfigurations] = useState({});

  const handleConfigurationChange = (itemId, configKey, value) => {
    setConfigurations(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [configKey]: value
      }
    }));
    clearError();
  };

  const validateConfigurations = () => {
    // Check if all required configurations are provided
    for (const item of items) {
      if (item.requiresConfiguration && !configurations[item.id]) {
        return false;
      }
    }
    return true;
  };

  const handleContinue = () => {
    if (!validateConfigurations()) {
      setError('Vui lòng cấu hình đầy đủ cho tất cả sản phẩm');
      return;
    }

    logCheckoutEvent('product_configuration_completed', {
      productType,
      itemCount: items.length,
      configurationsCount: Object.keys(configurations).length
    });

    onNext();
  };

  // Check if any items require configuration
  const requiresConfiguration = items.some(item => 
    ProductTypeManager.requiresConfiguration(productType)
  );

  if (!requiresConfiguration) {
    // Skip this step if no configuration needed
    return (
      <div className="text-center py-8">
        <div className="text-gray-400 text-6xl mb-4">⚙️</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Không cần cấu hình
        </h3>
        <p className="text-gray-600 mb-6">
          Sản phẩm này không yêu cầu cấu hình đặc biệt.
        </p>
        <button
          onClick={onNext}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Tiếp tục
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Cấu hình sản phẩm
        </h2>
        <p className="text-gray-600 mt-1">
          Vui lòng cấu hình các tùy chọn cho sản phẩm của bạn
        </p>
      </div>

      {/* Configuration Items */}
      <div className="space-y-6">
        {items.map((item, index) => (
          <div key={item.id || index} className="border border-gray-200 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-4">
              {item.name}
            </h3>

            {/* Example configuration options - this would be dynamic based on product type */}
            <div className="space-y-4">
              {productType === ProductTypeManager.PRODUCT_TYPES.SIM && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số điện thoại muốn nạp <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="tel"
                      value={configurations[item.id]?.phoneNumber || ''}
                      onChange={(e) => handleConfigurationChange(item.id, 'phoneNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0912345678"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nhà mạng
                    </label>
                    <select
                      value={configurations[item.id]?.carrier || ''}
                      onChange={(e) => handleConfigurationChange(item.id, 'carrier', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Chọn nhà mạng</option>
                      <option value="viettel">Viettel</option>
                      <option value="mobifone">MobiFone</option>
                      <option value="vinaphone">VinaPhone</option>
                    </select>
                  </div>
                </>
              )}

              {productType === ProductTypeManager.PRODUCT_TYPES.CARD && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mệnh giá <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={configurations[item.id]?.denomination || ''}
                      onChange={(e) => handleConfigurationChange(item.id, 'denomination', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Chọn mệnh giá</option>
                      <option value="50000">50.000 VND</option>
                      <option value="100000">100.000 VND</option>
                      <option value="200000">200.000 VND</option>
                      <option value="500000">500.000 VND</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Định dạng
                    </label>
                    <div className="flex space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name={`format_${item.id}`}
                          value="digital"
                          checked={configurations[item.id]?.format === 'digital'}
                          onChange={(e) => handleConfigurationChange(item.id, 'format', e.target.value)}
                          className="mr-2"
                        />
                        Thẻ điện tử
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name={`format_${item.id}`}
                          value="physical"
                          checked={configurations[item.id]?.format === 'physical'}
                          onChange={(e) => handleConfigurationChange(item.id, 'format', e.target.value)}
                          className="mr-2"
                        />
                        Thẻ vật lý
                      </label>
                    </div>
                  </div>
                </>
              )}

              {/* Generic configuration for other product types */}
              {![ProductTypeManager.PRODUCT_TYPES.SIM, ProductTypeManager.PRODUCT_TYPES.CARD].includes(productType) && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ghi chú cấu hình
                  </label>
                  <textarea
                    value={configurations[item.id]?.notes || ''}
                    onChange={(e) => handleConfigurationChange(item.id, 'notes', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="Nhập ghi chú hoặc yêu cầu đặc biệt..."
                  />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="text-blue-600 text-xl mr-3">ℹ️</div>
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">Hướng dẫn cấu hình</p>
            <p>
              Vui lòng cung cấp thông tin chính xác để chúng tôi có thể xử lý đơn hàng của bạn một cách nhanh chóng và chính xác nhất.
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Quay lại
        </button>
        <button
          onClick={handleContinue}
          className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Tiếp tục
        </button>
      </div>
    </div>
  );
};

export default ProductConfiguration;