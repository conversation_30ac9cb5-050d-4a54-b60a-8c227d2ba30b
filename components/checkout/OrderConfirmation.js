/**
 * Order Confirmation Component
 * 
 * Final confirmation page showing order success and next steps
 * Displays order details and provides options for follow-up actions
 */

import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useCheckout } from '../../context/CheckoutContext';
import { CurrencyManager } from '../../services/CurrencyManager';
import { logCheckoutEvent } from '../../utils/checkoutLogger';

const OrderConfirmation = () => {
  const router = useRouter();
  const { 
    orderData, 
    customerInfo, 
    selectedCurrency,
    clearCart 
  } = useCheckout();

  useEffect(() => {
    if (orderData) {
      logCheckoutEvent('order_confirmation_viewed', {
        orderId: orderData.orderId,
        total: orderData.pricing?.total,
        currency: selectedCurrency
      });

      // Clear cart after successful order
      setTimeout(() => {
        clearCart();
      }, 2000);
    }
  }, [orderData]);

  if (!orderData) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">⚠️</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Không tìm thấy thông tin đơn hàng
        </h3>
        <p className="text-gray-600 mb-6">
          Có vẻ như có lỗi xảy ra. Vui lòng kiểm tra email của bạn hoặc liên hệ hỗ trợ.
        </p>
        <button
          onClick={() => router.push('/')}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Về trang chủ
        </button>
      </div>
    );
  }

  const handleViewOrder = () => {
    if (customerInfo?.customerId && orderData?.orderId) {
      router.push(`/customer/orders?orderId=${orderData.orderId}`);
    } else {
      // For guest checkout, redirect to order lookup
      router.push(`/order/${orderData.orderId}`);
    }
  };

  const handleContinueShopping = () => {
    router.push('/');
  };

  const getPaymentInstructions = () => {
    switch (orderData.paymentMethod) {
      case '7-11':
        return {
          title: 'Thanh toán tại 7-Eleven',
          instructions: [
            'Đến quầy dịch vụ tại bất kỳ cửa hàng 7-Eleven nào',
            'Cung cấp mã thanh toán cho nhân viên',
            'Thanh toán bằng tiền mặt',
            'Giữ lại biên lai làm bằng chứng thanh toán'
          ],
          icon: '🏪'
        };
      case 'family-mart':
        return {
          title: 'Thanh toán tại FamilyMart',
          instructions: [
            'Đến quầy dịch vụ tại bất kỳ cửa hàng FamilyMart nào',
            'Cung cấp mã thanh toán cho nhân viên',
            'Thanh toán bằng tiền mặt hoặc thẻ',
            'Giữ lại biên lai làm bằng chứng thanh toán'
          ],
          icon: '🛒'
        };
      case 'cod':
        return {
          title: 'Thanh toán khi nhận hàng',
          instructions: [
            'Chuẩn bị tiền mặt chính xác',
            'Kiểm tra hàng hóa khi nhận',
            'Thanh toán cho shipper',
            'Giữ lại hóa đơn giao hàng'
          ],
          icon: '💵'
        };
      case 'taiwanbanktransfer':
        return {
          title: 'Chuyển khoản ngân hàng',
          instructions: [
            'Sử dụng thông tin chuyển khoản được cung cấp',
            'Ghi rõ mã đơn hàng trong nội dung chuyển khoản',
            'Gửi ảnh chụp biên lai chuyển khoản',
            'Đơn hàng sẽ được xử lý sau khi nhận được tiền'
          ],
          icon: '🏦'
        };
      default:
        return {
          title: 'Thông tin thanh toán',
          instructions: [
            'Làm theo hướng dẫn trong email xác nhận',
            'Liên hệ hỗ trợ nếu cần trợ giúp',
            'Giữ lại mã đơn hàng để tra cứu'
          ],
          icon: '💳'
        };
    }
  };

  const paymentInstructions = getPaymentInstructions();

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      
      {/* Success Header */}
      <div className="text-center">
        <div className="text-green-600 text-8xl mb-4">✅</div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Đặt hàng thành công!
        </h2>
        <p className="text-gray-600 text-lg">
          Cảm ơn bạn đã tin tưởng và mua sắm tại cửa hàng của chúng tôi
        </p>
      </div>

      {/* Order Details Card */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        <div className="bg-green-50 px-6 py-4 border-b border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-green-900">
                Đơn hàng #{orderData.orderId}
              </h3>
              <p className="text-green-700 text-sm">
                Đặt hàng lúc: {new Date(orderData.createdAt).toLocaleString('vi-VN')}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-green-700">Tổng giá trị</p>
              <p className="text-xl font-bold text-green-900">
                {CurrencyManager.formatPrice(orderData.pricing?.total || 0, selectedCurrency)}
              </p>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Thông tin khách hàng</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><span className="font-medium">Họ tên:</span> {orderData.customer?.name}</p>
              <p><span className="font-medium">Email:</span> {orderData.customer?.email}</p>
              <p><span className="font-medium">Điện thoại:</span> {orderData.customer?.phone}</p>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">
              Sản phẩm đã đặt ({orderData.items?.length || 0} sản phẩm)
            </h4>
            <div className="space-y-2">
              {orderData.items?.map((item, index) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <div>
                    <span className="text-gray-900">{item.name}</span>
                    <span className="text-gray-500 ml-2">x{item.quantity}</span>
                  </div>
                  <span className="text-gray-900 font-medium">
                    {CurrencyManager.formatPrice(parseFloat(item.price) * parseInt(item.quantity), item.currency)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Payment Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <div className="text-blue-600 text-3xl mr-4">{paymentInstructions.icon}</div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-blue-900 mb-3">
              {paymentInstructions.title}
            </h3>
            <div className="space-y-2">
              {paymentInstructions.instructions.map((instruction, index) => (
                <div key={index} className="flex items-start text-sm text-blue-800">
                  <span className="bg-blue-200 text-blue-900 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-3 mt-0.5 flex-shrink-0">
                    {index + 1}
                  </span>
                  <span>{instruction}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Email Notification */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="text-yellow-600 text-xl mr-3">📧</div>
          <div className="text-sm">
            <p className="font-medium text-yellow-900 mb-1">
              Email xác nhận
            </p>
            <p className="text-yellow-700">
              Chúng tôi đã gửi email xác nhận đơn hàng tới <strong>{orderData.customer?.email}</strong>. 
              Vui lòng kiểm tra hộp thư (bao gồm cả thư spam) để biết thêm chi tiết.
            </p>
          </div>
        </div>
      </div>

      {/* Order Tracking */}
      {customerInfo?.customerId ? (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex">
            <div className="text-gray-600 text-xl mr-3">📱</div>
            <div className="text-sm">
              <p className="font-medium text-gray-900 mb-1">
                Theo dõi đơn hàng
              </p>
              <p className="text-gray-700 mb-3">
                Bạn có thể theo dõi trạng thái đơn hàng trong tài khoản của mình hoặc qua email thông báo.
              </p>
              <button
                onClick={handleViewOrder}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Xem chi tiết đơn hàng →
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex">
            <div className="text-gray-600 text-xl mr-3">🔍</div>
            <div className="text-sm">
              <p className="font-medium text-gray-900 mb-1">
                Tra cứu đơn hàng
              </p>
              <p className="text-gray-700 mb-3">
                Sử dụng mã đơn hàng <strong>{orderData.orderId}</strong> để tra cứu trạng thái đơn hàng.
              </p>
              <button
                onClick={handleViewOrder}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Tra cứu đơn hàng →
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Support Contact */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex">
          <div className="text-gray-600 text-xl mr-3">🆘</div>
          <div className="text-sm">
            <p className="font-medium text-gray-900 mb-1">
              Cần hỗ trợ?
            </p>
            <p className="text-gray-700">
              Nếu có bất kỳ câu hỏi nào về đơn hàng của bạn, vui lòng liên hệ với chúng tôi:
            </p>
            <div className="mt-2 space-y-1 text-gray-600">
              <p>📞 Hotline: +886903536869</p>
              <p>✉️ Email: <EMAIL></p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        <button
          onClick={handleContinueShopping}
          className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors text-center"
        >
          Tiếp tục mua sắm
        </button>
        {customerInfo?.customerId && (
          <button
            onClick={handleViewOrder}
            className="flex-1 border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors text-center"
          >
            Xem đơn hàng
          </button>
        )}
      </div>
    </div>
  );
};

export default OrderConfirmation;