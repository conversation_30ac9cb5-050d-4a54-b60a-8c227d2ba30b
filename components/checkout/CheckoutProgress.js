/**
 * Checkout Progress Component
 * 
 * Visual progress indicator showing current step and allowing navigation
 * Integrates with CheckoutFlowEngine for step information
 */

import React from 'react';
import { CheckoutFlowEngine } from '../../services/CheckoutFlowEngine';

const CheckoutProgress = ({ currentStep, steps, onStepClick }) => {
  const getCurrentStepIndex = () => {
    return steps.indexOf(currentStep);
  };

  const getStepDisplayInfo = (step) => {
    return CheckoutFlowEngine.getStepDisplayInfo(step);
  };

  const isStepAccessible = (stepIndex) => {
    const currentIndex = getCurrentStepIndex();
    // Allow going back to previous steps, but not forward beyond current + 1
    return stepIndex <= currentIndex + 1;
  };

  const isStepCompleted = (stepIndex) => {
    return stepIndex < getCurrentStepIndex();
  };

  const isStepCurrent = (stepIndex) => {
    return stepIndex === getCurrentStepIndex();
  };

  const handleStepClick = (step, stepIndex) => {
    if (isStepAccessible(stepIndex) && onStepClick) {
      onStepClick(step);
    }
  };

  const currentIndex = getCurrentStepIndex();
  const progressPercentage = ((currentIndex + 1) / steps.length) * 100;

  return (
    <div className="w-full">
      {/* Mobile Progress Bar */}
      <div className="md:hidden">
        <div className="flex justify-between text-sm font-medium text-gray-500 mb-2">
          <span>Bước {currentIndex + 1} / {steps.length}</span>
          <span>{Math.round(progressPercentage)}% hoàn thành</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <div className="mt-2 text-sm font-medium text-gray-900">
          {getStepDisplayInfo(currentStep).title}
        </div>
      </div>

      {/* Desktop Step Indicators */}
      <div className="hidden md:block">
        <nav aria-label="Progress">
          <ol className="flex items-center">
            {steps.map((step, stepIndex) => {
              const stepInfo = getStepDisplayInfo(step);
              const isCompleted = isStepCompleted(stepIndex);
              const isCurrent = isStepCurrent(stepIndex);
              const isAccessible = isStepAccessible(stepIndex);
              
              return (
                <li key={step} className="relative flex-1">
                  {/* Step Circle */}
                  <div 
                    className={`flex items-center ${
                      stepIndex !== steps.length - 1 ? '' : ''
                    }`}
                  >
                    <div className="relative flex items-center justify-center">
                      <button
                        onClick={() => handleStepClick(step, stepIndex)}
                        disabled={!isAccessible}
                        className={`
                          relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                          ${isCurrent 
                            ? 'border-blue-600 bg-blue-600 text-white shadow-lg scale-110' 
                            : isCompleted
                            ? 'border-green-600 bg-green-600 text-white hover:scale-105' 
                            : isAccessible
                            ? 'border-gray-300 bg-white text-gray-500 hover:border-gray-400 cursor-pointer'
                            : 'border-gray-200 bg-gray-100 text-gray-300 cursor-not-allowed'
                          }
                        `}
                        title={stepInfo.title}
                      >
                        {isCompleted ? (
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <span className="text-sm font-bold">
                            {stepIndex + 1}
                          </span>
                        )}
                      </button>

                      {/* Step Label */}
                      <div className="absolute top-12 left-1/2 transform -translate-x-1/2">
                        <div className={`
                          text-xs font-medium text-center whitespace-nowrap px-2 py-1 rounded
                          ${isCurrent 
                            ? 'text-blue-600 bg-blue-50' 
                            : isCompleted
                            ? 'text-green-600 bg-green-50'
                            : 'text-gray-500'
                          }
                        `}>
                          {stepInfo.title}
                        </div>
                      </div>
                    </div>

                    {/* Connector Line */}
                    {stepIndex !== steps.length - 1 && (
                      <div 
                        className={`
                          flex-1 h-0.5 mx-4 transition-colors duration-200
                          ${isCompleted || (isCurrent && stepIndex < steps.length - 1)
                            ? 'bg-blue-600' 
                            : 'bg-gray-200'
                          }
                        `}
                      />
                    )}
                  </div>
                </li>
              );
            })}
          </ol>
        </nav>

        {/* Current Step Description */}
        <div className="mt-6 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-gray-50 rounded-lg">
            <span className="text-2xl mr-3">{getStepDisplayInfo(currentStep).icon}</span>
            <div className="text-left">
              <h3 className="text-sm font-medium text-gray-900">
                {getStepDisplayInfo(currentStep).title}
              </h3>
              <p className="text-xs text-gray-600">
                {getStepDisplayInfo(currentStep).description}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Stats */}
      <div className="mt-4 flex justify-center text-xs text-gray-500">
        <div className="flex items-center space-x-4">
          <span>{currentIndex + 1} / {steps.length} bước hoàn thành</span>
          <span>•</span>
          <span>{Math.round(progressPercentage)}%</span>
          {CheckoutFlowEngine.estimateCompletionTime(currentStep, steps).estimatedMinutes > 0 && (
            <>
              <span>•</span>
              <span>
                Còn ~{CheckoutFlowEngine.estimateCompletionTime(currentStep, steps).estimatedMinutes} phút
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CheckoutProgress;