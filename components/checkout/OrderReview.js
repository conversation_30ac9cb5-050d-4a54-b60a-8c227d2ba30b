/**
 * Order Review Component
 * 
 * Final review step before order confirmation
 * Shows complete order summary and allows final confirmation
 */

import React, { useState } from 'react';
import { useCheckout } from '../../context/CheckoutContext';
import { CurrencyManager } from '../../services/CurrencyManager';
import { PaymentRegistry } from '../../services/PaymentRegistry';
import { AddressManager } from '../../services/AddressManager';
import { OrderProcessor } from '../../services/OrderProcessor';
import { getProductImage, handleImageError } from '../../utils/imageUtils';
import { logCheckoutEvent, logCheckoutError } from '../../utils/checkoutLogger';

const OrderReview = ({ onConfirm, onBack }) => {
  const {
    filteredItems,
    selectedCurrency,
    selectedPaymentMethod,
    customerInfo,
    deliveryAddress,
    setOrderData,
    setError,
    clearError
  } = useCheckout();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  const calculateSubtotal = () => {
    return filteredItems.reduce((total, item) => {
      const price = parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 1;
      return total + (price * quantity);
    }, 0);
  };

  const calculateShipping = () => {
    if (!deliveryAddress?.country) return 0;
    
    const shippingRates = {
      'VN': 0,  // Free domestic shipping
      'TW': 0,  // Free domestic shipping
      'US': 25, // International shipping
      'JP': 15, // Regional shipping
      'SG': 15  // Regional shipping
    };
    
    return shippingRates[deliveryAddress.country] || 20;
  };

  const calculateTax = () => {
    const subtotal = calculateSubtotal();
    const taxRate = selectedCurrency === 'VND' ? 0.1 : 0; // 10% VAT for VND
    return subtotal * taxRate;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping() + calculateTax();
  };

  const handleConfirmOrder = async () => {
    clearError();

    if (!termsAccepted) {
      setError('Vui lòng đồng ý với điều khoản và điều kiện');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare order data
      const orderData = {
        orderId: `MAG-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        items: filteredItems.map(item => ({
          sku: item.sku,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          currency: item.currency,
          configuration: item.configuration || {}
        })),
        currency: selectedCurrency,
        paymentMethod: selectedPaymentMethod,
        customer: customerInfo,
        deliveryAddress: deliveryAddress,
        pricing: {
          subtotal: calculateSubtotal(),
          shipping: calculateShipping(),
          tax: calculateTax(),
          total: calculateTotal()
        },
        createdAt: new Date().toISOString()
      };

      logCheckoutEvent('order_review_submitted', {
        orderId: orderData.orderId,
        itemCount: orderData.items.length,
        total: orderData.pricing.total,
        currency: selectedCurrency,
        paymentMethod: selectedPaymentMethod
      });

      // Process order
      const result = await OrderProcessor.processOrder(orderData);

      if (result.success) {
        setOrderData(orderData);
        onConfirm();
      } else {
        throw new Error(result.error || 'Không thể tạo đơn hàng');
      }

    } catch (error) {
      logCheckoutError('order_confirmation_failed', error);
      setError(error.message || 'Có lỗi xảy ra khi xử lý đơn hàng. Vui lòng thử lại.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const subtotal = calculateSubtotal();
  const shipping = calculateShipping();
  const tax = calculateTax();
  const total = calculateTotal();
  
  const paymentMethodInfo = PaymentRegistry.getMethod(selectedPaymentMethod, selectedCurrency);

  return (
    <div className="space-y-6">
      
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Xác nhận đơn hàng
        </h2>
        <p className="text-gray-600 mt-1">
          Vui lòng kiểm tra lại thông tin đơn hàng trước khi xác nhận
        </p>
      </div>

      {/* Order Items */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Sản phẩm đã đặt ({filteredItems.length} sản phẩm)
          </h3>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            {filteredItems.map((item, index) => (
              <div key={item.id || index} className="flex items-start space-x-4 pb-4 border-b border-gray-100 last:border-b-0">
                <div className="w-16 h-16 flex-shrink-0">
                  <img
                    src={getProductImage(item)}
                    alt={item.name}
                    onError={handleImageError}
                    className="w-full h-full object-cover rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{item.name}</h4>
                  <p className="text-sm text-gray-600 mt-1">SKU: {item.sku}</p>
                  {item.configuration && Object.keys(item.configuration).length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 font-medium">Cấu hình:</p>
                      {Object.entries(item.configuration).map(([key, value]) => (
                        <p key={key} className="text-xs text-gray-600">
                          {key}: {value}
                        </p>
                      ))}
                    </div>
                  )}
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-600">Số lượng: {item.quantity}</span>
                    <span className="font-medium text-gray-900">
                      {CurrencyManager.formatPrice(parseFloat(item.price) * parseInt(item.quantity), selectedCurrency)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Thông tin khách hàng
          </h3>
        </div>
        <div className="px-6 py-4 space-y-2 text-sm">
          <div><span className="font-medium">Họ tên:</span> {customerInfo.name}</div>
          <div><span className="font-medium">Email:</span> {customerInfo.email}</div>
          <div><span className="font-medium">Điện thoại:</span> {customerInfo.phone}</div>
          {customerInfo.address && (
            <div><span className="font-medium">Địa chỉ:</span> {customerInfo.address}</div>
          )}
        </div>
      </div>

      {/* Delivery Address */}
      {deliveryAddress && Object.keys(deliveryAddress).length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Địa chỉ giao hàng
            </h3>
          </div>
          <div className="px-6 py-4 text-sm text-gray-600 whitespace-pre-line">
            {AddressManager.formatAddress(deliveryAddress, 'shipping')}
          </div>
        </div>
      )}

      {/* Payment Information */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Thông tin thanh toán
          </h3>
        </div>
        <div className="px-6 py-4 space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Phương thức thanh toán:</span>
            <span className="text-gray-900">{paymentMethodInfo?.name || selectedPaymentMethod}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Tạm tính:</span>
            <span className="text-gray-900">{CurrencyManager.formatPrice(subtotal, selectedCurrency)}</span>
          </div>
          {shipping > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Phí giao hàng:</span>
              <span className="text-gray-900">{CurrencyManager.formatPrice(shipping, selectedCurrency)}</span>
            </div>
          )}
          {tax > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Thuế VAT:</span>
              <span className="text-gray-900">{CurrencyManager.formatPrice(tax, selectedCurrency)}</span>
            </div>
          )}
          <div className="pt-3 border-t border-gray-200">
            <div className="flex justify-between">
              <span className="text-lg font-medium text-gray-900">Tổng cộng:</span>
              <span className="text-xl font-bold text-gray-900">
                {CurrencyManager.formatPrice(total, selectedCurrency)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="bg-gray-50 rounded-lg p-4">
        <label className="flex items-start">
          <input
            type="checkbox"
            checked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            className="mt-1 mr-3 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          <div className="text-sm text-gray-700">
            <p>
              Tôi đồng ý với{' '}
              <a href="/terms" target="_blank" className="text-blue-600 hover:text-blue-800 underline">
                điều khoản và điều kiện
              </a>
              {' '}và{' '}
              <a href="/privacy" target="_blank" className="text-blue-600 hover:text-blue-800 underline">
                chính sách bảo mật
              </a>
              {' '}của cửa hàng. Tôi xác nhận rằng tất cả thông tin được cung cấp là chính xác.
            </p>
          </div>
        </label>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={onBack}
          disabled={isSubmitting}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Quay lại
        </button>
        <button
          onClick={handleConfirmOrder}
          disabled={!termsAccepted || isSubmitting}
          className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center"
        >
          {isSubmitting && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
          )}
          {isSubmitting ? 'Đang xử lý...' : 'Xác nhận đặt hàng'}
        </button>
      </div>
    </div>
  );
};

export default OrderReview;