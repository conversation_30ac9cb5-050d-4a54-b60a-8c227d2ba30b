/**
 * Checkout Container Component
 * 
 * Main orchestrator component that manages the overall checkout process
 * Replaces the monolithic checkout.js with a cleaner, modular structure
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { FaLongArrowAltLeft } from 'react-icons/fa';

// Context and Services
import { useCheckout } from '../../context/CheckoutContext';
import { SiteContext } from '../../context/mainContext';
import { ProductTypeManager } from '../../services/ProductTypeManager';
import { CheckoutFlowEngine } from '../../services/CheckoutFlowEngine';
import { CurrencyManager } from '../../services/CurrencyManager';

// Modular Components
import CartReview from './CartReview';
import ProductConfiguration from './ProductConfiguration';
import CustomerInformation from './CustomerInformation';
import DeliveryAddress from './DeliveryAddress';
import PaymentMethodSelector from './PaymentMethodSelector';
import PaymentDetails from './PaymentDetails';
import OrderReview from './OrderReview';
import OrderConfirmation from './OrderConfirmation';
import CheckoutProgress from './CheckoutProgress';
import CheckoutSidebar from './CheckoutSidebar';

// Layout Components
import DesktopHeader from '../../templates/shein/components/DesktopHeader';
import MobileHeader from '../../templates/shein/components/MobileHeader';

// Utils
import { logCheckoutEvent, logCheckoutError } from '../../utils/checkoutLogger';
import { getCustomerId } from '../../utils/customerAuth';

const CheckoutContainer = ({ store, inventory, cartFromURL }) => {
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);
  const [checkoutFlow, setCheckoutFlow] = useState(null);
  
  const {
    // State
    cartItems,
    filteredItems,
    currentStep,
    productType,
    selectedCurrency,
    isLoading,
    error,
    
    // Actions
    setCartItems,
    setCheckoutStep,
    setCustomerInfo,
    setError,
    clearError,
    
    // Computed
    canProceedToPayment,
    canSubmitOrder
  } = useCheckout();

  // Initialize checkout on mount
  useEffect(() => {
    initializeCheckout();
    setupResponsiveListener();
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Update checkout flow when product type changes
  useEffect(() => {
    if (productType) {
      const flow = CheckoutFlowEngine.getFlowForProductType(productType);
      setCheckoutFlow(flow);
      
      logCheckoutEvent('checkout_flow_initialized', {
        productType,
        stepCount: flow.steps.length,
        requiresAddress: flow.requiresAddress,
        requiresConfiguration: flow.requiresConfiguration
      });
    }
  }, [productType]);

  // Load customer info if logged in
  useEffect(() => {
    loadCustomerInfo();
  }, []);

  const initializeCheckout = () => {
    try {
      // Set initial cart items
      const initialCartItems = cartFromURL || [];
      if (initialCartItems.length > 0) {
        setCartItems(initialCartItems);
        logCheckoutEvent('checkout_initialized', {
          itemCount: initialCartItems.length,
          source: 'url_params'
        });
      } else {
        // Try to load from localStorage
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
          const parsedCart = JSON.parse(savedCart);
          setCartItems(parsedCart);
          logCheckoutEvent('checkout_initialized', {
            itemCount: parsedCart.length,
            source: 'local_storage'
          });
        }
      }
    } catch (error) {
      logCheckoutError('checkout_initialization_failed', error);
      setError('Failed to initialize checkout');
    }
  };

  const loadCustomerInfo = async () => {
    try {
      const customerId = getCustomerId();
      if (customerId) {
        // Load customer data
        const response = await fetch(`/api/customer/data?customerId=${customerId}`);
        if (response.ok) {
          const customerData = await response.json();
          setCustomerInfo({
            customerId,
            name: customerData.name,
            email: customerData.email,
            phone: customerData.phone
          });
        }
      }
    } catch (error) {
      console.warn('Failed to load customer info:', error);
    }
  };

  const setupResponsiveListener = () => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    handleResize(); // Initial check
    window.addEventListener('resize', handleResize);
  };

  const handleStepChange = (newStep) => {
    if (!checkoutFlow) return;
    
    const currentIndex = checkoutFlow.steps.indexOf(currentStep);
    const targetIndex = checkoutFlow.steps.indexOf(newStep);
    
    // Validate if we can move to the target step
    if (targetIndex > currentIndex + 1) {
      setError('Please complete the current step before proceeding');
      return;
    }
    
    clearError();
    setCheckoutStep(newStep);
    
    logCheckoutEvent('manual_step_change', {
      from: currentStep,
      to: newStep,
      direction: targetIndex > currentIndex ? 'forward' : 'backward'
    });
  };

  const renderStepComponent = () => {
    if (!checkoutFlow || !currentStep) {
      return <div className="text-center py-8">Loading checkout...</div>;
    }

    const stepComponents = {
      [CheckoutFlowEngine.CHECKOUT_STEPS.CART_REVIEW]: (
        <CartReview
          items={filteredItems}
          currency={selectedCurrency}
          onNext={() => handleStepChange(checkoutFlow.steps[1])}
        />
      ),
      [CheckoutFlowEngine.CHECKOUT_STEPS.PRODUCT_CONFIG]: (
        <ProductConfiguration
          items={filteredItems}
          onNext={() => handleStepChange(checkoutFlow.steps[2])}
          onBack={() => handleStepChange(checkoutFlow.steps[0])}
        />
      ),
      [CheckoutFlowEngine.CHECKOUT_STEPS.CUSTOMER_INFO]: (
        <CustomerInformation
          onNext={() => {
            const nextStepIndex = checkoutFlow.steps.indexOf(currentStep) + 1;
            if (nextStepIndex < checkoutFlow.steps.length) {
              handleStepChange(checkoutFlow.steps[nextStepIndex]);
            }
          }}
          onBack={() => {
            const prevStepIndex = checkoutFlow.steps.indexOf(currentStep) - 1;
            if (prevStepIndex >= 0) {
              handleStepChange(checkoutFlow.steps[prevStepIndex]);
            }
          }}
        />
      ),
      [CheckoutFlowEngine.CHECKOUT_STEPS.DELIVERY_ADDRESS]: (
        <DeliveryAddress
          onNext={() => {
            const nextStepIndex = checkoutFlow.steps.indexOf(currentStep) + 1;
            handleStepChange(checkoutFlow.steps[nextStepIndex]);
          }}
          onBack={() => {
            const prevStepIndex = checkoutFlow.steps.indexOf(currentStep) - 1;
            handleStepChange(checkoutFlow.steps[prevStepIndex]);
          }}
        />
      ),
      [CheckoutFlowEngine.CHECKOUT_STEPS.PAYMENT_METHOD]: (
        <PaymentMethodSelector
          currency={selectedCurrency}
          onNext={() => {
            const nextStepIndex = checkoutFlow.steps.indexOf(currentStep) + 1;
            handleStepChange(checkoutFlow.steps[nextStepIndex]);
          }}
          onBack={() => {
            const prevStepIndex = checkoutFlow.steps.indexOf(currentStep) - 1;
            handleStepChange(checkoutFlow.steps[prevStepIndex]);
          }}
        />
      ),
      [CheckoutFlowEngine.CHECKOUT_STEPS.PAYMENT_DETAILS]: (
        <PaymentDetails
          onNext={() => {
            const nextStepIndex = checkoutFlow.steps.indexOf(currentStep) + 1;
            handleStepChange(checkoutFlow.steps[nextStepIndex]);
          }}
          onBack={() => {
            const prevStepIndex = checkoutFlow.steps.indexOf(currentStep) - 1;
            handleStepChange(checkoutFlow.steps[prevStepIndex]);
          }}
        />
      ),
      [CheckoutFlowEngine.CHECKOUT_STEPS.ORDER_REVIEW]: (
        <OrderReview
          onConfirm={() => {
            const confirmationStep = CheckoutFlowEngine.CHECKOUT_STEPS.CONFIRMATION;
            handleStepChange(confirmationStep);
          }}
          onBack={() => {
            const prevStepIndex = checkoutFlow.steps.indexOf(currentStep) - 1;
            handleStepChange(checkoutFlow.steps[prevStepIndex]);
          }}
        />
      ),
      [CheckoutFlowEngine.CHECKOUT_STEPS.CONFIRMATION]: (
        <OrderConfirmation />
      )
    };

    return stepComponents[currentStep] || (
      <div className="text-center py-8 text-red-600">
        Invalid checkout step: {currentStep}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Checkout - {store.name}</title>
        <meta name="description" content={`Complete your purchase at ${store.name}`} />
      </Head>

      {/* Header */}
      <div className="sticky top-0 z-50 bg-white shadow-sm">
        {isMobile ? (
          <MobileHeader store={store} />
        ) : (
          <DesktopHeader store={store} />
        )}
      </div>

      {/* Main Content */}
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-6">
          
          {/* Back Navigation */}
          <div className="mb-6">
            <Link href={`/${store.storeId}`}>
              <a className="inline-flex items-center text-blue-600 hover:text-blue-800">
                <FaLongArrowAltLeft className="mr-2" />
                Back to Store
              </a>
            </Link>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Checkout Error
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    {error}
                  </div>
                  <div className="mt-3">
                    <button
                      onClick={clearError}
                      className="text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded"
                    >
                      Dismiss
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            {/* Main Content Area */}
            <div className="lg:col-span-2">
              
              {/* Progress Indicator */}
              {checkoutFlow && (
                <div className="mb-8">
                  <CheckoutProgress
                    currentStep={currentStep}
                    steps={checkoutFlow.steps}
                    onStepClick={handleStepChange}
                  />
                </div>
              )}

              {/* Step Content */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                {renderStepComponent()}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <CheckoutSidebar
                items={filteredItems}
                currency={selectedCurrency}
                store={store}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CheckoutContainer;