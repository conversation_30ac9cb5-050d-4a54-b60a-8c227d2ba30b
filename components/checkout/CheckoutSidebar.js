/**
 * Checkout Sidebar Component
 * 
 * Order summary sidebar showing cart items, totals, and store information
 * Provides quick overview throughout the checkout process
 */

import React from 'react';
import { useCheckout } from '../../context/CheckoutContext';
import { CurrencyManager } from '../../services/CurrencyManager';
import { getProductImage, handleImageError } from '../../utils/imageUtils';

const CheckoutSidebar = ({ items, currency, store }) => {
  const {
    selectedPaymentMethod,
    deliveryAddress
  } = useCheckout();

  const calculateSubtotal = () => {
    return items.reduce((total, item) => {
      const price = parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 1;
      return total + (price * quantity);
    }, 0);
  };

  const calculateShipping = () => {
    // Simple shipping calculation based on delivery address
    if (!deliveryAddress?.country) return 0;
    
    const shippingRates = {
      'VN': 0,  // Free domestic shipping
      'TW': 0,  // Free domestic shipping
      'US': 25, // International shipping
      'JP': 15, // Regional shipping
      'SG': 15  // Regional shipping
    };
    
    return shippingRates[deliveryAddress.country] || 20;
  };

  const calculateTax = () => {
    // Simple tax calculation (this would be more complex in real implementation)
    const subtotal = calculateSubtotal();
    const taxRate = currency === 'VND' ? 0.1 : 0; // 10% VAT for VND
    return subtotal * taxRate;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping() + calculateTax();
  };

  const subtotal = calculateSubtotal();
  const shipping = calculateShipping();
  const tax = calculateTax();
  const total = calculateTotal();

  return (
    <div className="space-y-6">
      
      {/* Order Summary Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Tóm tắt đơn hàng
          </h3>
        </div>

        {/* Items List */}
        <div className="px-6 py-4">
          <div className="space-y-4">
            {items.map((item, index) => (
              <div key={item.id || index} className="flex items-start space-x-3">
                {/* Product Image */}
                <div className="w-12 h-12 flex-shrink-0">
                  <img
                    src={getProductImage(item)}
                    alt={item.name}
                    onError={handleImageError}
                    className="w-full h-full object-cover rounded-md"
                  />
                </div>

                {/* Product Details */}
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                    {item.name}
                  </h4>
                  <p className="text-xs text-gray-500 mt-1">
                    Số lượng: {item.quantity}
                  </p>
                  {item.configuration && (
                    <div className="mt-1">
                      {Object.entries(item.configuration).map(([key, value]) => (
                        <p key={key} className="text-xs text-gray-500">
                          {key}: {value}
                        </p>
                      ))}
                    </div>
                  )}
                </div>

                {/* Price */}
                <div className="text-sm font-medium text-gray-900">
                  {CurrencyManager.formatPrice(
                    parseFloat(item.price) * parseInt(item.quantity), 
                    currency
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pricing Breakdown */}
        <div className="px-6 py-4 border-t border-gray-200 space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Tạm tính</span>
            <span className="text-gray-900">
              {CurrencyManager.formatPrice(subtotal, currency)}
            </span>
          </div>
          
          {shipping > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Phí giao hàng</span>
              <span className="text-gray-900">
                {CurrencyManager.formatPrice(shipping, currency)}
              </span>
            </div>
          )}
          
          {tax > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Thuế VAT</span>
              <span className="text-gray-900">
                {CurrencyManager.formatPrice(tax, currency)}
              </span>
            </div>
          )}
          
          <div className="pt-3 border-t border-gray-200">
            <div className="flex justify-between">
              <span className="text-base font-medium text-gray-900">Tổng cộng</span>
              <span className="text-lg font-bold text-gray-900">
                {CurrencyManager.formatPrice(total, currency)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Method */}
      {selectedPaymentMethod && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="font-medium text-gray-900 mb-2">
            Phương thức thanh toán
          </h4>
          <div className="flex items-center">
            <div className="text-lg mr-2">💳</div>
            <span className="text-sm text-gray-600 capitalize">
              {selectedPaymentMethod.replace('-', ' ')}
            </span>
          </div>
        </div>
      )}

      {/* Delivery Information */}
      {deliveryAddress && Object.keys(deliveryAddress).length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h4 className="font-medium text-gray-900 mb-2">
            Giao hàng tới
          </h4>
          <div className="text-sm text-gray-600">
            <p>{deliveryAddress.address}</p>
            <p>{deliveryAddress.city}, {deliveryAddress.country}</p>
            {deliveryAddress.postalCode && (
              <p>{deliveryAddress.postalCode}</p>
            )}
          </div>
        </div>
      )}

      {/* Store Information */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center mb-3">
          <img
            src={store.logo?.[0] || '/logo.png'}
            alt={store.name}
            className="w-8 h-8 rounded mr-3"
          />
          <div>
            <h4 className="font-medium text-gray-900">{store.name}</h4>
            <p className="text-xs text-gray-500">{store.slogan}</p>
          </div>
        </div>
        
        <div className="space-y-2 text-sm text-gray-600">
          {store.email?.[0] && (
            <div className="flex items-center">
              <span className="text-gray-400 mr-2">✉️</span>
              <span>{store.email[0]}</span>
            </div>
          )}
          {store.mobile?.[0] && (
            <div className="flex items-center">
              <span className="text-gray-400 mr-2">📞</span>
              <span>{store.mobile[0]}</span>
            </div>
          )}
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="text-green-600 text-lg mr-2">🔒</div>
          <div className="text-sm">
            <p className="font-medium text-green-900 mb-1">
              Thanh toán an toàn
            </p>
            <p className="text-green-700">
              Thông tin của bạn được bảo vệ bằng mã hóa SSL 256-bit
            </p>
          </div>
        </div>
      </div>

      {/* Support Contact */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">
          Cần hỗ trợ?
        </h4>
        <div className="space-y-2 text-sm">
          {store.mobile?.[0] && (
            <div>
              <span className="text-gray-600">Hotline: </span>
              <a 
                href={`tel:${store.mobile[0]}`}
                className="text-blue-600 hover:text-blue-800"
              >
                {store.mobile[0]}
              </a>
            </div>
          )}
          {store.email?.[0] && (
            <div>
              <span className="text-gray-600">Email: </span>
              <a 
                href={`mailto:${store.email[0]}`}
                className="text-blue-600 hover:text-blue-800"
              >
                {store.email[0]}
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CheckoutSidebar;