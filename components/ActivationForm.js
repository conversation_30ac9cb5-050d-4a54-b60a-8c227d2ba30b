import React, { useState, useEffect } from 'react';
import { FaCheckCircle, FaInfoCircle } from 'react-icons/fa';
import { isCustomerAuthenticated, getCustomerInfo } from '../utils/customerAuth';

const ActivationForm = ({ onComplete, orderId, isStandalone = false }) => {
  const [hasActivatedBefore, setHasActivatedBefore] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  
  // Check for previous activation history
  useEffect(() => {
    if (isCustomerAuthenticated()) {
      const customerInfo = getCustomerInfo();
      if (customerInfo) {
        // Check if customer has activated SIMs before
        if (customerInfo.activatedSims && customerInfo.activatedSims.length > 0) {
          setHasActivatedBefore(true);
        }
        
        // Set phone number if available (for display only)
        if (customerInfo.phone) {
          setPhoneNumber(customerInfo.phone);
        }
      }
    }
    
    // Auto-complete this section since it's informational only
    if (isStandalone && onComplete) {
      setTimeout(() => {
        onComplete({ 
          activationGuidelines: 'viewed',
          viewedAt: new Date().toISOString()
        });
      }, 100);
    }
  }, [isStandalone, onComplete]);
  
  return (
    <div className="space-y-4">
      {hasActivatedBefore && (
        <div className="bg-green-50 border-l-4 border-green-500 p-3 mb-4 text-green-700 flex items-start">
          <FaCheckCircle className="flex-shrink-0 mt-0.5 mr-2" />
          <div>
            <p className="text-sm font-medium">Bạn đã từng kích hoạt SIM trước đây</p>
            <p className="text-xs mt-1">
              Chúng tôi đã cập nhật hướng dẫn kích hoạt dành riêng cho bạn dựa trên lịch sử trước đây.
            </p>
          </div>
        </div>
      )}
      

      
      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 text-blue-700">
        <h4 className="font-medium mb-2">Hướng dẫn kích hoạt SIM</h4>
        <ol className="list-decimal ml-5 space-y-2">
          <li>Đặt SIM vào điện thoại của bạn</li>
          <li>Bật điện thoại và đợi nhận tín hiệu mạng</li>
          <li>Gọi *101# để kiểm tra thông tin thuê bao</li>
          <li>Nếu cần hỗ trợ, vui lòng gọi tổng đài CSKH: 1800 xxxx</li>
        </ol>
      </div>
      
      <div className="p-4 border border-gray-200 rounded">
        <h4 className="font-medium mb-3">Thông tin kích hoạt</h4>
        <div className="flex items-center mb-4">
          <FaInfoCircle className="text-blue-500 mr-2" />
          <span>SIM sẽ được kích hoạt sau khi nhận hàng</span>
        </div>
        
        {phoneNumber && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Số điện thoại liên hệ
            </label>
            <div className="w-full p-2 border border-gray-300 bg-gray-50 rounded text-gray-700">
              {phoneNumber}
            </div>
            <p className="text-xs text-gray-500 mt-1">Chúng tôi sẽ liên hệ với bạn qua số này nếu có vấn đề với việc kích hoạt</p>
          </div>
        )}
      </div>
      
      <div className="bg-gray-50 p-4 rounded text-sm">
        <p className="font-medium mb-2">Chú ý:</p>
        <ul className="list-disc ml-5 space-y-1 text-gray-600">
          <li>SIM sẽ tự động hủy nếu không được kích hoạt trong vòng 30 ngày</li>
          <li>Bạn cần mang theo CMND/CCCD khi nhận SIM tại cửa hàng</li>
          <li>Phí duy trì thuê bao sẽ được áp dụng theo quy định của nhà mạng</li>
        </ul>
      </div>
    </div>
  );
};

export default ActivationForm; 