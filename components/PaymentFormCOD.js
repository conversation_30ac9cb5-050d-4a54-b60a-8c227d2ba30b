import React, { useState, useEffect } from 'react';
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { isUserLoggedIn, getCustomerData } from '../utils/customerFormData';
import { getCustomerId } from '../utils/customerAuth';
import axios from 'axios';
// Import the checkout logger
import {
  logCheckoutProcess,
  logPaymentApiCall,
  logPaymentApiResponse,
  logTransaction,
  logCheckoutError
} from '../utils/checkoutLogger';

const Input = ({ onChange, value, name, placeholder }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    type="text"
    placeholder={placeholder}
    name={name}
  />
);

// Enhanced logging utility that uses the checkout logger
const logPaymentForm = (message, data = null) => {
  // Use the checkout logger to log payment form events
  return logCheckoutProcess('cod_payment', { 
    message, 
    data,
    component: 'PaymentFormCOD'
  });
};

const PaymentFormCOD = ({ handleSubmit, errorMessage, storeid, orderId, amount, currency = 'VND' }) => {
  const [input, setInput] = useState({
    name: "",
    email: "",
    street: "",
    city: "",
    district: "",
    state: "",
    postal_code: "",
    country: "Vietnam",
    phone: ""
  });
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [profileError, setProfileError] = useState(null);

  const stripe = useStripe();
  const elements = useElements();

  // Log component initialization
  useEffect(() => {
    logPaymentForm("COD Payment form initialized", { 
      storeid,
      orderId,
      amount,
      currency
    });
    
    return () => {
      logPaymentForm("COD Payment form unmounting");
    };
  }, [storeid, orderId, amount, currency]);

  // Fetch customer data from modern profile API
  useEffect(() => {
    const loadCustomerProfile = async () => {
      try {
        setIsLoadingUserData(true);
        setProfileError(null);
        
        // Get customer ID
        const customerId = getCustomerId();
        
        if (customerId) {
          logPaymentForm("Fetching customer profile", { customerId });
          
          // Log the API call
          logPaymentApiCall(
            'customer-api',
            `/api/customer/get-profile?customerId=${customerId}`,
            { customerId },
            { method: 'GET' }
          );
          
          // Call the modern profile API endpoint
          const response = await axios.get(`/api/customer/get-profile?customerId=${customerId}`);
          
          // Log the API response
          logPaymentApiResponse(
            'customer-api',
            `/api/customer/get-profile?customerId=${customerId}`,
            response.data,
            response.status
          );
          
          if (response.status === 200 && response.data.customer) {
            logPaymentForm("Successfully fetched customer profile", response.data.customer);
            
            const customerData = response.data.customer;
            
            // Find preferred address (default, most recently used, or first)
            let addressToUse = null;
            
            if (customerData.addresses && Array.isArray(customerData.addresses)) {
              // First try to find the default address
              addressToUse = customerData.addresses.find(addr => addr.isDefault === true);
              
              // If no default, try to find the most recently used one
              if (!addressToUse) {
                const sortedAddresses = [...customerData.addresses]
                  .filter(addr => addr.lastUsed)
                  .sort((a, b) => new Date(b.lastUsed) - new Date(a.lastUsed));
                
                if (sortedAddresses.length > 0) {
                  addressToUse = sortedAddresses[0];
                }
              }
              
              // If still no address, take the first one
              if (!addressToUse && customerData.addresses.length > 0) {
                addressToUse = customerData.addresses[0];
              }
            }
            
            // Support legacy address format
            const legacyAddress = customerData.addressData || {};
            
            // Combine all the data, prioritizing modern address format
            setInput({
              name: customerData.fullName || customerData.name || "",
              email: customerData.email || "",
              phone: customerData.phone || "",
              street: addressToUse?.street || legacyAddress.street || "",
              city: addressToUse?.city || legacyAddress.city || "",
              district: addressToUse?.district || legacyAddress.district || "",
              state: addressToUse?.state || legacyAddress.state || "",
              postal_code: addressToUse?.postalCode || legacyAddress.postal_code || "",
              country: addressToUse?.country || legacyAddress.country || "Vietnam",
            });
          } else {
            // Fallback to legacy methods if API fails
            logPaymentForm("API failed, using fallback methods");
            fallbackToLegacyMethods();
          }
        } else {
          // No user ID, try legacy methods
          logPaymentForm("No customer ID found, checking if logged in via legacy methods");
          fallbackToLegacyMethods();
        }
      } catch (error) {
        logPaymentForm("Error fetching customer profile", { error: error.message });
        
        // Log the error with detailed information
        logCheckoutError('cod-profile-fetch', error, { 
          customerId: getCustomerId(),
          storeid,
          orderId
        });
        
        setProfileError("Không thể tải thông tin khách hàng. Vui lòng nhập thông tin của bạn.");
        
        // Try legacy methods as fallback
        fallbackToLegacyMethods();
      } finally {
        setIsLoadingUserData(false);
      }
    };
    
    // Legacy methods as fallback
    const fallbackToLegacyMethods = () => {
      // Check if user is logged in via old method
      if (isUserLoggedIn(storeid)) {
        // Get customer data from localStorage
        const customerData = getCustomerData(storeid);
        
        if (customerData) {
          logPaymentForm("Using customer data from localStorage", { storeid });
          
          // Update input with available data
          setInput(prev => ({
            ...prev,
            name: customerData.name || customerData.fullName || prev.name,
            email: customerData.email || prev.email,
            phone: customerData.phone || prev.phone,
            street: customerData.street || (customerData.addressData?.street) || prev.street,
            city: customerData.city || (customerData.addressData?.city) || prev.city,
            district: customerData.district || (customerData.addressData?.district) || prev.district,
            state: customerData.state || (customerData.addressData?.state) || prev.state,
            postal_code: customerData.postal_code || customerData.postalCode || prev.postal_code,
            country: customerData.country || "Vietnam",
          }));
        }
      }
    };
    
    loadCustomerProfile();
  }, [storeid, orderId]);

  const onChange = e => {
    logPaymentForm(`Field updated: ${e.target.name}`, { 
      field: e.target.name,
      value: e.target.value 
    });
    setInput({ ...input, [e.target.name]: e.target.value });
  };

  // Wrap the provided handleSubmit to add logging
  const wrappedHandleSubmit = (e) => {
    e.preventDefault();
    
    // Log the form submission attempt
    logPaymentForm("COD Payment form submitted", {
      customerInfo: {
        name: input.name,
        email: input.email,
        phone: input.phone
      },
      shippingAddress: {
        street: input.street,
        city: input.city,
        district: input.district,
        country: input.country
      },
      orderId,
      storeid,
      amount,
      currency
    });
    
    // Log the transaction initiation
    logTransaction(orderId, 'INITIATED', {
      paymentMethod: 'cod',
      amount,
      currency,
      customerInfo: {
        name: input.name,
        email: input.email,
        phone: input.phone
      },
      shippingAddress: {
        street: input.street,
        city: input.city,
        district: input.district,
        country: input.country
      }
    });
    
    // Call the original handleSubmit
    handleSubmit(e);
  };

  return (
    <div className="flex flex-1 pt-4 flex-col">
      <div className="mt-4 border-t pt-6">
        {isLoadingUserData ? (
          <div className="flex justify-center items-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-800"></div>
            <span className="ml-2 text-gray-600">Đang tải thông tin khách hàng...</span>
          </div>
        ) : (
          <form onSubmit={wrappedHandleSubmit}>
            {errorMessage && (
              <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4">
                <p>{errorMessage}</p>
              </div>
            )}
            
            {profileError && (
              <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
                <p>{profileError}</p>
              </div>
            )}
            
            <div className="mb-2">
              <label className="block text-gray-700 text-xs font-bold mb-1">
                Họ tên
              </label>
              <Input
                onChange={onChange}
                value={input.name}
                name="name"
                placeholder="Họ và tên người nhận hàng"
              />
            </div>
            
            <div className="mb-2">
              <label className="block text-gray-700 text-xs font-bold mb-1">
                Email
              </label>
              <Input
                onChange={onChange}
                value={input.email}
                name="email"
                placeholder="Email liên hệ"
              />
            </div>
            
            <div className="mb-2">
              <label className="block text-gray-700 text-xs font-bold mb-1">
                Số điện thoại
              </label>
              <Input
                onChange={onChange}
                value={input.phone}
                name="phone"
                placeholder="Số điện thoại người nhận"
              />
            </div>
            
            <div className="mb-2">
              <label className="block text-gray-700 text-xs font-bold mb-1">
                Địa chỉ
              </label>
              <Input
                onChange={onChange}
                value={input.street}
                name="street"
                placeholder="Địa chỉ nhận hàng"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div className="mb-2">
                <label className="block text-gray-700 text-xs font-bold mb-1">
                  Quận/Huyện
                </label>
                <Input
                  onChange={onChange}
                  value={input.district}
                  name="district"
                  placeholder="Quận/Huyện"
                />
              </div>
              
              <div className="mb-2">
                <label className="block text-gray-700 text-xs font-bold mb-1">
                  Thành phố
                </label>
                <Input
                  onChange={onChange}
                  value={input.city}
                  name="city"
                  placeholder="Thành phố"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div className="mb-2">
                <label className="block text-gray-700 text-xs font-bold mb-1">
                  Mã bưu điện
                </label>
                <Input
                  onChange={onChange}
                  value={input.postal_code}
                  name="postal_code"
                  placeholder="Mã bưu điện"
                />
              </div>
              
              <div className="mb-2">
                <label className="block text-gray-700 text-xs font-bold mb-1">
                  Quốc gia
                </label>
                <Input
                  onChange={onChange}
                  value={input.country}
                  name="country"
                  placeholder="Quốc gia"
                />
              </div>
            </div>
            
            <button
              type="submit"
              onClick={wrappedHandleSubmit}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 mt-4 rounded focus:outline-none focus:shadow-outline"
            >
              Đặt hàng và thanh toán khi nhận hàng
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

export default PaymentFormCOD;

