import React, { useEffect } from 'react';
import dynamic from 'next/dynamic';

// Updated dynamic import with loadableGenerated to be compatible with newer Next.js versions
const LeafletMap = dynamic(
  () => import('./LeafletMap'),
  { 
    ssr: false,
    loading: () => (
      <div className="relative w-full h-full bg-gray-100 animate-pulse rounded-2xl">
        <div className="flex items-center justify-center h-full">
          <span className="text-gray-400">Loading map...</span>
        </div>
      </div>
    )
  }
);

const Map = ({ latitude, longitude, iconurl }) => {
  return (
    <div className="relative w-full h-full">
      {/* Map Container with rounded corners and shadow */}
      <div className="absolute inset-0 rounded-2xl overflow-hidden shadow-inner">
        {/* Gradient overlay at the top */}
        <div className="absolute top-0 left-0 right-0 h-16 bg-gradient-to-b from-black/20 to-transparent z-10" />
        
        {/* The actual map */}
        <div className="w-full h-full">
          <LeafletMap latitude={latitude} longitude={longitude} iconurl={iconurl}/>
        </div>
        
        {/* Gradient overlay at the bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/20 to-transparent z-10" />
        
        {/* Optional: Decorative corner accents */}
        <div className="absolute top-4 left-4 w-8 h-8 border-t-2 border-l-2 border-white/30 rounded-tl-lg" />
        <div className="absolute top-4 right-4 w-8 h-8 border-t-2 border-r-2 border-white/30 rounded-tr-lg" />
        <div className="absolute bottom-4 left-4 w-8 h-8 border-b-2 border-l-2 border-white/30 rounded-bl-lg" />
        <div className="absolute bottom-4 right-4 w-8 h-8 border-b-2 border-r-2 border-white/30 rounded-br-lg" />
      </div>
    </div>
  );
}

export default Map;
