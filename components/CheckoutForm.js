import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { updateOrderInformation } from '../utils/customerAPI';
import { logCheckoutProcess, logTransaction } from '../utils/checkoutLogger';

const CheckoutForm = ({ 
  onComplete, 
  orderId, 
  product, 
  selectedPaymentMethod, 
  selectedCurrency,
  totalForCurrentStore,
  allProducts = [],
  groupedProducts = {}
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Calculate order summary from available data
  const calculateOrderSummary = () => {
    let subtotal = 0;
    
    // Try different sources for the total amount
    if (totalForCurrentStore) {
      subtotal = totalForCurrentStore;
    } else if (product && product.price) {
      subtotal = product.price;
    } else if (allProducts && allProducts.length > 0) {
      subtotal = allProducts.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);
    } else if (Object.keys(groupedProducts).length > 0) {
      subtotal = Object.values(groupedProducts)
        .flat()
        .reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);
    }
    
    return {
      subtotal,
      shipping: 0,
      tax: 0,
      total: subtotal
    };
  };

  const [orderSummary, setOrderSummary] = useState(calculateOrderSummary());

  // Update order summary when data changes
  useEffect(() => {
    const newOrderSummary = calculateOrderSummary();
    setOrderSummary(newOrderSummary);
  }, [totalForCurrentStore, product, allProducts, groupedProducts]);

  // Log payment method selection when component initializes
  useEffect(() => {
    logCheckoutProcess('payment_method_selection', {
      message: 'Payment method selected',
      data: {
        orderId,
        paymentMethod: selectedPaymentMethod,
        currency: selectedCurrency,
        amount: orderSummary.total
      },
      component: 'CheckoutForm'
    });
    
    // Also log as a transaction event
    logTransaction(orderId, 'PAYMENT_METHOD_SELECTED', {
      paymentMethod: selectedPaymentMethod,
      currency: selectedCurrency,
      amount: orderSummary.total
    });
  }, [orderId, selectedPaymentMethod, selectedCurrency, orderSummary.total]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Update order with final checkout information
      if (orderId) {
        await updateOrderInformation(orderId, {
          checkout: {
            status: 'completed',
            completedAt: new Date().toISOString(),
            paymentMethod: selectedPaymentMethod,
            currency: selectedCurrency,
            orderSummary
          }
        });
      }
      
      // Continue with the next step
      onComplete();
    } catch (error) {
      console.error('Error completing checkout:', error);
      setError('Có lỗi xảy ra khi hoàn tất thanh toán. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 text-red-700 flex items-start">
          <FaExclamationTriangle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">{error}</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">Tổng quan đơn hàng</h3>
        
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Tạm tính:</span>
            <span>{selectedCurrency} {(orderSummary.subtotal || 0).toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Phí vận chuyển:</span>
            <span>{selectedCurrency} {(orderSummary.shipping || 0).toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Thuế:</span>
            <span>{selectedCurrency} {(orderSummary.tax || 0).toLocaleString()}</span>
          </div>
          <div className="border-t pt-2 mt-2">
            <div className="flex justify-between font-medium">
              <span>Tổng cộng:</span>
              <span>{selectedCurrency} {(orderSummary.total || 0).toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">Phương thức thanh toán</h3>
        <p className="text-gray-600 mb-2">Đã chọn: {selectedPaymentMethod}</p>
        <p className="text-sm text-gray-500">
          Bạn sẽ được chuyển đến trang thanh toán của {selectedPaymentMethod} để hoàn tất giao dịch.
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded">
        <p className="text-sm text-gray-600 mb-2">Bằng cách tiếp tục, bạn đồng ý với:</p>
        <ul className="list-disc ml-5 text-sm text-gray-600 space-y-1">
          <li>Điều khoản dịch vụ của chúng tôi</li>
          <li>Chính sách bảo mật</li>
          <li>Chính sách hoàn trả và hủy đơn hàng</li>
        </ul>
      </div>

      <button
        type="submit"
        className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center justify-center"
        disabled={loading}
      >
        {loading ? <FaSpinner className="animate-spin mr-2" /> : null}
        {loading ? 'Đang xử lý...' : 'Hoàn tất đơn hàng'}
      </button>
    </form>
  );
};

export default CheckoutForm; 