import React from 'react'
import DENOMINATION from '../utils/currencyProvider'
import Image from './Image'

const ListItem = ({ link, title, imageSrc, price, priceUpper, openInNewTab, sku }) => {
  return (
    <div className="w-full p1 sm:p-2 md:w-1/2 lg:w-1/4">
      <a href={link} {...(openInNewTab && { target: '_blank', rel: 'noopener noreferrer' })} aria-label={title}>
        <div className="h-72 flex justify-center items-center boxstyle_1 relative">
          <div className="flex flex-column justify-center items-center">
            <Image alt={title} src={imageSrc} className="w-3/5" />
          </div>
        </div>
      </a>
      <div>
        <p className="m-4 text-center mb-4">{title}</p>
        <p className="text-center text-gray-700 mb-4">
          {price !== undefined && price > 0 && `${DENOMINATION}${price.toLocaleString()}`}
          {/* {priceUpper && priceUpper > price && ` - ${DENOMINATION}${priceUpper.toLocaleString()}`} */}
          {'   '}[SKU {sku}]
        </p>
      </div>
    </div>

  );
}

export default ListItem;
