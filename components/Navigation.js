import React from 'react';
import Link from 'next/link';

/**
 * SiteNavigation component for site-wide navigation
 * 
 * @param {Object} props Component props
 * @param {string} props.store Current store identifier
 * @param {Object} props.storeobject Store object data
 * @param {Array} props.categoriesCurrent Categories to display in navigation
 * @param {function} props.isDisabledPath Function to check if path is disabled
 * @param {string} props.logoSrc Source URL for the logo image
 * @param {function} props.slugify Function to slugify category names
 */
const SiteNavigation = ({ 
  store, 
  storeobject, 
  categoriesCurrent = [], 
  isDisabledPath = () => false, 
  logoSrc = '/logo.png',
  slugify
}) => {
  return (
    <nav>
      <div className="flex justify-center">
        <div className="
          mobile:px-12 sm:flex-row sm:pt-12 sm:pb-6 desktop:px-0
          px-4 pt-8 flex flex-col w-fw
        ">
          {!isDisabledPath() && (
            <>
              <div className="mb-4 sm:mr-16 max-w-48 sm:max-w-none flex-shrink-0 flex items-center">
                {/* {store && (!storeobject.privatestore || (storeobject.privatestore && storeobject.privatestore !== '1')) && (
                  <Link legacyBehavior href={`/`} className="mr-4">
                    <a aria-label="Home">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-16 h-16">
                        <path strokeLinecap="round" strokeLinejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                      </svg>
                    </a>
                  </Link>
                )} */}
                <Link legacyBehavior href={store ? `/${store}` : `/`}>
                  <a aria-label="Home">
                    <img 
                      src={logoSrc}
                      alt="SIM ONLINE STORE" 
                      width={Math.max(60, Math.min(100, 60 * categoriesCurrent.length))}
                    />
                  </a>
                </Link>
              </div>
            </>
          )}
          
          <div className="flex flex-wrap mt-1 flex-shrink-1 justify-between w-full">
            {categoriesCurrent.length < 12 ? (
              <>
                {!isDisabledPath() && (
                  categoriesCurrent.map((category, index) => (
                    <Link
                      href={store ? `/${store}/category/${slugify(category.slug)}` : `/category/${slugify(category.slug)}`}
                      key={index}
                      aria-label={category}
                    >                    
                      <p className="
                        sm:mr-8 sm:mb-0
                        mb-4 text-left text-smaller mr-4
                        border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                      ">                      
                        {category.name.toUpperCase()}
                      </p>
                    </Link>
                  ))
                )}
              </>  
            ) : (
              <>
                {!isDisabledPath() && (
                  <div className="category-panel">
                    <div className="category-header">
                      <div className="category-toggle">
                        <img src="https://cdn-icons-png.flaticon.com/128/14337/14337898.png" style={{ width: '50px', height: '50px' }} alt="All"></img>
                      </div>
                    </div>
                    <div className="category-content">
                      {!isDisabledPath() && (
                        categoriesCurrent.map((category, index) => (
                          <Link
                            href={store ? `/${store}/category/${slugify(category.slug)}` : `/category/${slugify(category.slug)}`}
                            key={index}
                            aria-label={category}
                          >                    
                            <p className="
                              sm:mr-8 sm:mb-0
                              mb-4 text-left text-smaller mr-4
                              border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                            ">                      
                              {category.name.toUpperCase()}
                            </p>
                          </Link>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
            
            {!isDisabledPath() && (
              <>
                <Link 
                  href={store ? `/${store}/categories/` : `/categories`}
                  aria-label="All categories"
                >                
                  <p className="
                    sm:mr-8 sm:mb-0
                    mb-4 text-left text-smaller mr-4
                    border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                    ml-4
                  ">
                  DANH MỤC
                  </p>
                </Link>
                <Link 
                  href={store ? `/${store}/inventories/` : `/inventories`}
                  aria-label="All Inventories"
                >                
                  <p className="
                    sm:mr-8 sm:mb-0
                    mb-4 text-left text-smaller mr-4
                    border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                  ">
                  TOÀN BỘ CỬA HÀNG
                  </p>
                </Link>
              </>
            )}
            {!isDisabledPath() && (
              <>
                <Link
                  href={store ? `/${store}/our-story` : `/our-story`}
                >
                  <p className="
                    sm:mr-8 sm:mb-0
                    mb-4 text-left text-smaller mr-4
                    border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                  ">
                  SIM & MORE
                  </p>
                </Link>
              </>
            )}
            {!isDisabledPath() && (
              <>
                <Link
                  href={store ? `/${store}/contact` : `/contact`}
                >
                  <p className="
                    sm:mr-8 sm:mb-0
                    mb-4 text-left text-smaller mr-4
                    border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                  ">
                  LIÊN HỆ
                  </p>
                </Link>
              </>
            )}
            
            {!isDisabledPath() && (
              <>
                <Link
                  href={`/${store}/cart`}
                >
                  <p className="
                    sm:mr-8 sm:mb-0
                    mb-4 text-left text-smaller mr-4
                    border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                  ">
                  GIỎ HÀNG
                  </p>
                </Link>
              </>
            )}
            {!isDisabledPath() && store && (
              <>
                <Link
                  href={`/${store}/customer/dashboard`}
                >
                  <p className="
                    sm:mr-8 sm:mb-0
                    mb-4 text-left text-smaller mr-4
                    border-b border-gray-300 hover:border-gray-400 rounded-tl-2xl rounded-br-2xl px-1
                  ">
                  TÔI
                  </p>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default SiteNavigation; 