import React from 'react';
import Button from './Button';
import { configFunctions } from '../utils/configFunctions';

const ProductConfigurations = ({ 
  configurations, 
  configInputs, 
  handleConfigInput, 
  isFormValid 
}) => {
  const [validationMessage, setValidationMessage] = React.useState("");

  const handleFunctionRun = (functionConfig, value, isRequired) => {
    if (isRequired && !value) {
      setValidationMessage("Vui lòng điền thông tin trước khi thực hiện");
      setTimeout(() => {
        setValidationMessage("");
      }, 5000);
      return;
    }

    if (configFunctions[functionConfig.function]) {
      configFunctions[functionConfig.function](value);
    } else {
      console.warn(`Function ${functionConfig.function} not found in configFunctions`);
    }
  };

  return (
    <div className="my-6">
      {/* <h3 className="text-xl font-medium mb-4"><PERSON><PERSON><PERSON> h<PERSON><PERSON> sản phẩm:</h3> */}
      {Object.entries(configurations).map(([key, config]) => (
        <div key={key} className="mb-4">
          {config.purpose === "input" ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {key === "mobiletopup" ? "Số di động:" : key} {config.required && <span className="text-red-500">*</span>}
              </label>
              <div className="flex gap-2">
                <input
                  type={config.datatype === "number" ? "number" : "text"}
                  value={configInputs[key] || ""}
                  onChange={(e) => handleConfigInput(key, e.target.value)}
                  className={`flex-1 p-2 border rounded-md ${
                    !isFormValid && config.required && !configInputs[key]
                      ? "border-red-500"
                      : "border-gray-300"
                  }`}
                  required={config.required}
                />
                {config.run && (
                  <Button
                    title={config.run.label}
                    onClick={() => handleFunctionRun(config.run, configInputs[key], config.required)}
                    small={true}
                  />
                )}
              </div>
            </div>
          ) : (
            <div className="text-gray-600">
              <span className="font-medium">{key}:</span> {config.content}
            </div>
          )}
        </div>
      ))}
      {!isFormValid && (
        <p className="text-red-500 text-sm mt-2">
          Vui lòng điền đầy đủ thông tin bắt buộc
        </p>
      )}
      {validationMessage && (
        <p className="text-red-500 text-sm mt-2">
          {validationMessage}
        </p>
      )}
    </div>
  );
};

export default ProductConfigurations; 