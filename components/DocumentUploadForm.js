import React, { useState, useEffect } from 'react';
import { FaIn<PERSON><PERSON>ircle, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle, FaCheckCircle, FaCircle } from 'react-icons/fa';
import { isCustomerAuthenticated, getCustomerId } from '../utils/customerAuth';
import { updateOrderInformation, uploadCustomerDocument, getCustomerDocuments } from '../utils/customerAPI';
import DocumentPreviewModal from './DocumentPreviewModal';

const DocumentUploadForm = ({ onComplete, orderId }) => {
  const [documents, setDocuments] = useState({
    idCard: null,
    photo: null,
    proofOfResidence: null
  });
  const [documentStatus, setDocumentStatus] = useState({
    idCard: null,
    photo: null, 
    proofOfResidence: null
  });
  const [previouslySubmitted, setPreviouslySubmitted] = useState(false);
  const [uploadedDocs, setUploadedDocs] = useState({});
  const [loading, setLoading] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(null);
  const [error, setError] = useState(null);
  const [documentVersions, setDocumentVersions] = useState({
    idCard: [],
    photo: [],
    proofOfResidence: []
  });
  const [previewDocument, setPreviewDocument] = useState(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [missingDocuments, setMissingDocuments] = useState([]);
  
  // Check for customer documents from customers.json and API
  useEffect(() => {
    if (isCustomerAuthenticated()) {
      const customerId = getCustomerId();
      setLoading(true);
      
      // First load customer data from the server-side JSON file
      fetch(`/api/customer/data?customerId=${customerId}`)
        .then(response => response.json())
        .then(customerData => {
          const customerDocs = customerData.documents || [];
          
          // Check which documents are available in the customer data
          const availableDocs = {};
          const docStatuses = {};
          const versions = {
            idCard: [],
            photo: [],
            proofOfResidence: []
          };
          
          // Map document types from customers.json to our form's document types
          const documentTypeMapping = {
            'idCard_front': 'idCard',
            'idCard_back': 'idCard',
            'idCard': 'idCard',
            'passport': 'idCard',
            'photo': 'photo',
            'selfie': 'photo',
            'proofOfResidence': 'proofOfResidence',
            'utility_bill': 'proofOfResidence',
            'residence_certificate': 'proofOfResidence'
          };
          
          // Process documents from customer data
          customerDocs.forEach(doc => {
            const formDocType = documentTypeMapping[doc.documentType];
            if (formDocType) {
              if (!availableDocs[formDocType]) {
                availableDocs[formDocType] = doc;
                docStatuses[formDocType] = doc.status || 'pending_approval';
                versions[formDocType].push(doc);
              } else if (new Date(doc.uploadDate) > new Date(availableDocs[formDocType].uploadDate)) {
                // Replace with newer document of same type
                availableDocs[formDocType] = doc;
                docStatuses[formDocType] = doc.status || 'pending_approval';
                versions[formDocType].push(doc);
              } else {
                versions[formDocType].push(doc);
              }
            }
          });
          
          // Determine if any required documents are missing
          const missing = [];
          if (!availableDocs.idCard) missing.push('idCard');
          if (!availableDocs.photo) missing.push('photo');
          
          setMissingDocuments(missing);
          
          if (Object.keys(availableDocs).length > 0) {
            setPreviouslySubmitted(true);
            setUploadedDocs(availableDocs);
            setDocumentStatus(docStatuses);
            setDocumentVersions(versions);
          }
          
          // Also check the API for any newer documents
          return getCustomerDocuments();
        })
        .then(response => {
          if (response.success && response.documentsByType) {
            // Merge with existing documents if needed
            const apiDocs = {};
            const versions = { ...documentVersions };
            const statuses = { ...documentStatus };
            
            if (response.documentsByType.idCard && response.documentsByType.idCard.length > 0) {
              apiDocs.idCard = response.documentsByType.idCard[0];
              versions.idCard = [...versions.idCard, ...response.documentsByType.idCard];
              statuses.idCard = response.documentsByType.idCard[0].status || 'pending_approval';
            }
            
            if (response.documentsByType.photo && response.documentsByType.photo.length > 0) {
              apiDocs.photo = response.documentsByType.photo[0];
              versions.photo = [...versions.photo, ...response.documentsByType.photo];
              statuses.photo = response.documentsByType.photo[0].status || 'pending_approval';
            }
            
            if (response.documentsByType.proofOfResidence && response.documentsByType.proofOfResidence.length > 0) {
              apiDocs.proofOfResidence = response.documentsByType.proofOfResidence[0];
              versions.proofOfResidence = [...versions.proofOfResidence, ...response.documentsByType.proofOfResidence];
              statuses.proofOfResidence = response.documentsByType.proofOfResidence[0].status || 'pending_approval';
            }
            
            // Update state with any newer documents from the API
            if (Object.keys(apiDocs).length > 0) {
              setPreviouslySubmitted(true);
              
              // Merge with existing uploaded docs, preferring API docs where available
              setUploadedDocs(prev => ({
                ...prev,
                ...apiDocs
              }));
              
              setDocumentVersions(versions);
              setDocumentStatus(statuses);
              
              // Update missing documents list
              const missing = [];
              if (!uploadedDocs.idCard && !apiDocs.idCard) missing.push('idCard');
              if (!uploadedDocs.photo && !apiDocs.photo) missing.push('photo');
              setMissingDocuments(missing);
            }
          }
        })
        .catch(err => {
          console.error('Error loading documents:', err);
          setError('Không thể tải thông tin giấy tờ. Vui lòng thử lại sau.');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, []);

  // Check completion status and notify parent when documents are complete
  useEffect(() => {
    // Check if all required documents are uploaded and not rejected
    const hasRequiredDocs = uploadedDocs.idCard && uploadedDocs.photo;
    const allDocsApproved = hasRequiredDocs &&
      documentStatus.idCard === 'approved' &&
      documentStatus.photo === 'approved';
    const allDocsUploaded = hasRequiredDocs &&
      documentStatus.idCard !== 'rejected' &&
      documentStatus.photo !== 'rejected';

    if (allDocsApproved) {
      // All documents are approved - mark as completed
      onComplete({
        completed: true,
        documentsUploaded: true,
        documentsApproved: true,
        status: 'approved'
      });
    } else if (allDocsUploaded && previouslySubmitted) {
      // All documents are uploaded and pending approval
      onComplete({
        completed: true,
        documentsUploaded: true,
        documentsApproved: false,
        status: 'pending_approval'
      });
    } else if (missingDocuments.length === 0 && hasRequiredDocs) {
      // Documents exist but may need review
      onComplete({
        completed: true,
        documentsUploaded: true,
        documentsApproved: allDocsApproved,
        status: allDocsApproved ? 'approved' : 'pending_approval'
      });
    } else {
      // Documents are missing or incomplete
      onComplete({
        completed: false,
        documentsUploaded: false,
        documentsApproved: false,
        status: 'incomplete'
      });
    }
  }, [uploadedDocs, documentStatus, missingDocuments, previouslySubmitted, onComplete]);

  // Check if a document is missing or has been rejected
  const isDocumentRequiredOrRejected = (docType) => {
    // Document is missing
    if (missingDocuments.includes(docType)) return true;
    
    // Document was rejected
    if (documentStatus[docType] === 'rejected') return true;
    
    return false;
  };
  
  // Check if a document is approved
  const isDocumentApproved = (docType) => {
    return documentStatus[docType] === 'approved';
  };
  
  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setDocuments({ ...documents, [name]: files[0] });
      setDocumentStatus({ ...documentStatus, [name]: 'pending_approval' });
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Validate required files before upload
      if (!previouslySubmitted && !uploadedDocs.idCard && !documents.idCard) {
        setError('Vui lòng chọn ảnh CMND/CCCD/Hộ chiếu');
        setLoading(false);
        return;
      }
      
      if (!previouslySubmitted && !uploadedDocs.photo && !documents.photo) {
        setError('Vui lòng chọn ảnh chân dung');
        setLoading(false);
        return;
      }
      
      // Upload each document file to the server
      const uploadPromises = [];
      
      if (documents.idCard) {
        setUploadingFile('idCard');
        uploadPromises.push(
          uploadCustomerDocument('idCard', documents.idCard, orderId)
            .then(response => {
              if (response.success) {
                const newDoc = {
                  documentType: 'idCard',
                  filename: response.filename,
                  uploadDate: response.uploadDate,
                  status: 'pending_approval'
                };
                
                // Keep track of all versions
                const updatedVersions = [...(documentVersions.idCard || []), newDoc];
                
                // Update current document and versions
                setUploadedDocs(prev => ({
                  ...prev,
                  idCard: newDoc
                }));
                
                setDocumentVersions(prev => ({
                  ...prev,
                  idCard: updatedVersions
                }));
                
                setDocumentStatus(prev => ({
                  ...prev,
                  idCard: 'pending_approval'
                }));
              }
              return response;
            })
        );
      }
      
      if (documents.photo) {
        setUploadingFile('photo');
        uploadPromises.push(
          uploadCustomerDocument('photo', documents.photo, orderId)
            .then(response => {
              if (response.success) {
                const newDoc = {
                  documentType: 'photo',
                  filename: response.filename,
                  uploadDate: response.uploadDate,
                  status: 'pending_approval'
                };
                
                // Keep track of all versions
                const updatedVersions = [...(documentVersions.photo || []), newDoc];
                
                // Update current document and versions
                setUploadedDocs(prev => ({
                  ...prev,
                  photo: newDoc
                }));
                
                setDocumentVersions(prev => ({
                  ...prev,
                  photo: updatedVersions
                }));
                
                setDocumentStatus(prev => ({
                  ...prev,
                  photo: 'pending_approval'
                }));
              }
              return response;
            })
        );
      }
      
      if (documents.proofOfResidence) {
        setUploadingFile('proofOfResidence');
        uploadPromises.push(
          uploadCustomerDocument('proofOfResidence', documents.proofOfResidence, orderId)
            .then(response => {
              if (response.success) {
                const newDoc = {
                  documentType: 'proofOfResidence',
                  filename: response.filename,
                  uploadDate: response.uploadDate,
                  status: 'pending_approval'
                };
                
                // Keep track of all versions
                const updatedVersions = [...(documentVersions.proofOfResidence || []), newDoc];
                
                // Update current document and versions
                setUploadedDocs(prev => ({
                  ...prev,
                  proofOfResidence: newDoc
                }));
                
                setDocumentVersions(prev => ({
                  ...prev,
                  proofOfResidence: updatedVersions
                }));
                
                setDocumentStatus(prev => ({
                  ...prev,
                  proofOfResidence: 'pending_approval'
                }));
              }
              return response;
            })
        );
      }
      
      if (uploadPromises.length === 0 && previouslySubmitted) {
        // If no new documents to upload but we had previously uploaded docs, check completion
        const hasRequiredDocs = (uploadedDocs.idCard) && (uploadedDocs.photo);
        const allDocsApproved = hasRequiredDocs &&
          documentStatus.idCard === 'approved' &&
          documentStatus.photo === 'approved';
        setLoading(false);

        if (hasRequiredDocs) {
          onComplete({
            completed: true,
            documentsUploaded: true,
            documentsApproved: allDocsApproved,
            status: allDocsApproved ? 'approved' : 'pending_approval'
          });
        } else {
          onComplete({
            completed: false,
            documentsUploaded: false,
            documentsApproved: false,
            status: 'incomplete'
          });
        }
        return;
      }
      
      // Wait for all uploads to complete
      setUploadingFile('all');
      await Promise.all(uploadPromises);
      
      // Update order information if orderId provided
      if (orderId) {
        await updateOrderInformation(orderId, {
          documentsUploaded: true,
          documentUploadDate: new Date().toISOString(),
          documentStatus: documentStatus
        });
      }
      
      // Check if all required documents are now uploaded
      const hasRequiredDocs = (uploadedDocs.idCard || documents.idCard) &&
                             (uploadedDocs.photo || documents.photo);

      // Continue with the next step and mark as completed
      if (hasRequiredDocs) {
        onComplete({
          completed: true,
          documentsUploaded: true,
          documentsApproved: false,
          status: 'pending_approval'
        });
      } else {
        onComplete({
          completed: false,
          documentsUploaded: false,
          documentsApproved: false,
          status: 'incomplete'
        });
      }
    } catch (error) {
      console.error('Error uploading documents:', error);
      setError('Có lỗi xảy ra khi tải lên giấy tờ. Vui lòng thử lại sau. ' + error.message);
    } finally {
      setLoading(false);
      setUploadingFile(null);
    }
  };
  
  const getStatusBadge = (status) => {
    switch(status) {
      case 'pending_approval':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
            <FaCircle className="mr-1" />
            Đang chờ duyệt
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
            <FaCheckCircle className="mr-1" />
            Đã duyệt
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
            <FaExclamationTriangle className="mr-1" />
            Bị từ chối
          </span>
        );
      default:
        return null;
    }
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  // Function to open document preview modal
  const openDocumentPreview = (document) => {
    setPreviewDocument(document);
    setShowPreviewModal(true);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4" encType="multipart/form-data">
      {previouslySubmitted && (
        <div className="bg-yellow-50 border-l-4 border-yellow-500 p-3 mb-4 text-yellow-700 flex items-start">
          <FaInfoCircle className="flex-shrink-0 mt-0.5 mr-2" />
          <div>
            <p className="text-sm font-medium">Trạng thái xét duyệt giấy tờ</p>
            <p className="text-xs mt-1">
              Giấy tờ của bạn sẽ được xét duyệt trong vòng 24h làm việc.
            </p>
          </div>
        </div>
      )}
      
      {missingDocuments.length > 0 && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 text-red-700 flex items-start">
          <FaExclamationTriangle className="flex-shrink-0 mt-0.5 mr-2" />
          <div>
            <p className="text-sm font-medium">Giấy tờ còn thiếu</p>
            <p className="text-xs mt-1">
              Vui lòng cung cấp các giấy tờ còn thiếu sau: 
              {missingDocuments.includes('idCard') && ' CMND/CCCD/Hộ chiếu,'}
              {missingDocuments.includes('photo') && ' Ảnh chân dung,'}
              {missingDocuments.includes('proofOfResidence') && ' Giấy tờ chứng minh nơi cư trú'}
            </p>
          </div>
        </div>
      )}
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 text-red-700 flex items-start">
          <FaExclamationTriangle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">{error}</p>
        </div>
      )}
      
      <div className="bg-yellow-50 text-yellow-800 p-3 rounded mb-4">
        <p className="text-sm">
          Để đăng ký SIM, bạn cần cung cấp giấy tờ tùy thân hợp lệ theo quy định của nhà mạng.
        </p>
      </div>
      
      <div className={`border ${isDocumentRequiredOrRejected('idCard') ? 'border-red-300' : isDocumentApproved('idCard') ? 'border-green-300' : 'border-gray-200'} rounded p-4`}>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          CMND/CCCD/Hộ chiếu <span className="text-red-500">*</span>
          {documentStatus.idCard === 'rejected' && (
            <span className="text-red-500 text-xs ml-2">
              (Giấy tờ đã bị từ chối, vui lòng tải lên lại)
            </span>
          )}
        </label>
        
        <input
          type="file"
          name="idCard"
          id="idCard"
          className="hidden"
          onChange={handleFileChange}
          accept="image/*,.pdf"
        />
        
        {uploadedDocs.idCard ? (
          <div className={`${documentStatus.idCard === 'approved' ? 'bg-green-50 border-green-300' : documentStatus.idCard === 'rejected' ? 'bg-red-50 border-red-300' : 'bg-yellow-50 border-yellow-300'} border rounded p-3`}>
            <div className="flex justify-between items-center">
              <div>
                <div className={`text-sm font-medium ${documentStatus.idCard === 'approved' ? 'text-green-800' : documentStatus.idCard === 'rejected' ? 'text-red-800' : 'text-yellow-800'}`}>CMND/CCCD đã tải lên</div>
                <div className={`text-xs mt-1 ${documentStatus.idCard === 'approved' ? 'text-green-700' : documentStatus.idCard === 'rejected' ? 'text-red-700' : 'text-yellow-700'}`}>
                  Ngày tải lên: {formatDate(uploadedDocs.idCard.uploadDate)}
                </div>
                <div className="mt-1">
                  {getStatusBadge(documentStatus.idCard)}
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => openDocumentPreview(uploadedDocs.idCard)}
                  className="bg-gray-100 text-gray-600 hover:bg-gray-200 px-2 py-1 rounded text-sm"
                >
                  Xem tài liệu
                </button>
                <button
                  type="button"
                  onClick={() => {
                    document.getElementById('idCard').click();
                  }}
                  className={`${documentStatus.idCard === 'rejected' ? 'bg-red-100 text-red-600 hover:bg-red-200' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'} px-2 py-1 rounded text-sm`}
                >
                  {documentStatus.idCard === 'rejected' ? 'Tải lên lại (bắt buộc)' : 'Tải lên phiên bản mới'}
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <label
              htmlFor="idCard"
              className={`flex-1 cursor-pointer ${missingDocuments.includes('idCard') ? 'bg-red-50 border-red-300' : previouslySubmitted ? 'bg-green-50 border-green-300' : 'bg-white border-gray-300'} border rounded p-2 text-center hover:bg-gray-50`}
            >
              {documents.idCard ? documents.idCard.name : missingDocuments.includes('idCard') ? "Chọn file (bắt buộc)" : previouslySubmitted ? "Đã có giấy tờ tùy thân (cập nhật?)" : "Chọn file"}
            </label>
            {documents.idCard && !loading && (
              <button
                type="button"
                onClick={() => setDocuments({ ...documents, idCard: null })}
                className="text-red-500 hover:text-red-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </button>
            )}
            {loading && uploadingFile === 'idCard' && (
              <FaSpinner className="animate-spin h-5 w-5 text-blue-500" />
            )}
          </div>
        )}
        <p className="text-xs text-gray-500 mt-1">File ảnh hoặc PDF, tối đa 5MB</p>
      </div>
      
      <div className={`border ${isDocumentRequiredOrRejected('photo') ? 'border-red-300' : isDocumentApproved('photo') ? 'border-green-300' : 'border-gray-200'} rounded p-4`}>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Ảnh chân dung <span className="text-red-500">*</span>
          {documentStatus.photo === 'rejected' && (
            <span className="text-red-500 text-xs ml-2">
              (Giấy tờ đã bị từ chối, vui lòng tải lên lại)
            </span>
          )}
        </label>
        
        <input
          type="file"
          name="photo"
          id="photo"
          className="hidden"
          onChange={handleFileChange}
          accept="image/*"
        />
        
        {uploadedDocs.photo ? (
          <div className={`${documentStatus.photo === 'approved' ? 'bg-green-50 border-green-300' : documentStatus.photo === 'rejected' ? 'bg-red-50 border-red-300' : 'bg-yellow-50 border-yellow-300'} border rounded p-3`}>
            <div className="flex justify-between items-center">
              <div>
                <div className={`text-sm font-medium ${documentStatus.photo === 'approved' ? 'text-green-800' : documentStatus.photo === 'rejected' ? 'text-red-800' : 'text-yellow-800'}`}>Ảnh chân dung đã tải lên</div>
                <div className={`text-xs mt-1 ${documentStatus.photo === 'approved' ? 'text-green-700' : documentStatus.photo === 'rejected' ? 'text-red-700' : 'text-yellow-700'}`}>
                  Ngày tải lên: {formatDate(uploadedDocs.photo.uploadDate)}
                </div>
                <div className="mt-1">
                  {getStatusBadge(documentStatus.photo)}
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => openDocumentPreview(uploadedDocs.photo)}
                  className="bg-gray-100 text-gray-600 hover:bg-gray-200 px-2 py-1 rounded text-sm"
                >
                  Xem tài liệu
                </button>
                <button
                  type="button"
                  onClick={() => {
                    document.getElementById('photo').click();
                  }}
                  className={`${documentStatus.photo === 'rejected' ? 'bg-red-100 text-red-600 hover:bg-red-200' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'} px-2 py-1 rounded text-sm`}
                >
                  {documentStatus.photo === 'rejected' ? 'Tải lên lại (bắt buộc)' : 'Tải lên phiên bản mới'}
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <label
              htmlFor="photo"
              className={`flex-1 cursor-pointer ${missingDocuments.includes('photo') ? 'bg-red-50 border-red-300' : 'bg-white border-gray-300'} border rounded p-2 text-center hover:bg-gray-50`}
            >
              {documents.photo ? documents.photo.name : missingDocuments.includes('photo') ? "Chọn file (bắt buộc)" : "Chọn file"}
            </label>
            {documents.photo && !loading && (
              <button
                type="button"
                onClick={() => setDocuments({ ...documents, photo: null })}
                className="text-red-500 hover:text-red-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </button>
            )}
            {loading && uploadingFile === 'photo' && (
              <FaSpinner className="animate-spin h-5 w-5 text-blue-500" />
            )}
          </div>
        )}
        <p className="text-xs text-gray-500 mt-1">Ảnh chân dung rõ nét, không đeo kính, tối đa 2MB</p>
      </div>
      
      <div className={`border ${isDocumentApproved('proofOfResidence') ? 'border-green-300' : documentStatus.proofOfResidence === 'rejected' ? 'border-red-300' : 'border-gray-200'} rounded p-4`}>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Giấy tờ chứng minh nơi cư trú (tùy chọn)
          {documentStatus.proofOfResidence === 'rejected' && (
            <span className="text-red-500 text-xs ml-2">
              (Giấy tờ đã bị từ chối, vui lòng tải lên lại nếu cần)
            </span>
          )}
        </label>
        
        <input
          type="file"
          name="proofOfResidence"
          id="proofOfResidence"
          className="hidden"
          onChange={handleFileChange}
          accept="image/*,.pdf"
        />
        
        {uploadedDocs.proofOfResidence ? (
          <div className={`${documentStatus.proofOfResidence === 'approved' ? 'bg-green-50 border-green-300' : documentStatus.proofOfResidence === 'rejected' ? 'bg-red-50 border-red-300' : 'bg-yellow-50 border-yellow-300'} border rounded p-3`}>
            <div className="flex justify-between items-center">
              <div>
                <div className={`text-sm font-medium ${documentStatus.proofOfResidence === 'approved' ? 'text-green-800' : documentStatus.proofOfResidence === 'rejected' ? 'text-red-800' : 'text-yellow-800'}`}>Giấy tờ cư trú đã tải lên</div>
                <div className={`text-xs mt-1 ${documentStatus.proofOfResidence === 'approved' ? 'text-green-700' : documentStatus.proofOfResidence === 'rejected' ? 'text-red-700' : 'text-yellow-700'}`}>
                  Ngày tải lên: {formatDate(uploadedDocs.proofOfResidence.uploadDate)}
                </div>
                <div className="mt-1">
                  {getStatusBadge(documentStatus.proofOfResidence)}
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => openDocumentPreview(uploadedDocs.proofOfResidence)}
                  className="bg-gray-100 text-gray-600 hover:bg-gray-200 px-2 py-1 rounded text-sm"
                >
                  Xem tài liệu
                </button>
                <button
                  type="button"
                  onClick={() => {
                    document.getElementById('proofOfResidence').click();
                  }}
                  className="bg-blue-100 text-blue-600 hover:bg-blue-200 px-2 py-1 rounded text-sm"
                >
                  Tải lên phiên bản mới
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <label
              htmlFor="proofOfResidence"
              className="flex-1 cursor-pointer bg-white border border-gray-300 rounded p-2 text-center hover:bg-gray-50"
            >
              {documents.proofOfResidence ? documents.proofOfResidence.name : "Chọn file"}
            </label>
            {documents.proofOfResidence && !loading && (
              <button
                type="button"
                onClick={() => setDocuments({ ...documents, proofOfResidence: null })}
                className="text-red-500 hover:text-red-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </button>
            )}
            {loading && uploadingFile === 'proofOfResidence' && (
              <FaSpinner className="animate-spin h-5 w-5 text-blue-500" />
            )}
          </div>
        )}
        <p className="text-xs text-gray-500 mt-1">Hóa đơn tiện ích hoặc sổ tạm trú</p>
      </div>
      
      {/* Only show button if there's something to upload or missing documents */}
      {(missingDocuments.length > 0 || documents.idCard || documents.photo || documents.proofOfResidence) && (
        <button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center justify-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <FaSpinner className="animate-spin mr-2" />
              {uploadingFile === 'all' ? 'Đang xử lý...' : `Đang tải lên ${uploadingFile === 'idCard' ? 'CMND/CCCD' : uploadingFile === 'photo' ? 'ảnh chân dung' : 'giấy tờ cư trú'}...`}
            </>
          ) : (
            missingDocuments.length > 0 ? 'Tải lên giấy tờ còn thiếu' : 'Tải lên giấy tờ'
          )}
        </button>
      )}
      
      {/* Show completion status when all required documents are uploaded and approved */}
      {missingDocuments.length === 0 && !documents.idCard && !documents.photo && !documents.proofOfResidence && previouslySubmitted && (
        <div className={`w-full px-4 py-2 rounded flex items-center justify-center ${
          (documentStatus.idCard === 'approved' && documentStatus.photo === 'approved')
            ? 'bg-green-50 border border-green-300 text-green-800'
            : 'bg-yellow-50 border border-yellow-300 text-yellow-800'
        }`}>
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          {(documentStatus.idCard === 'approved' && documentStatus.photo === 'approved')
            ? 'Giấy tờ đã hoàn tất'
            : 'Giấy tờ đã tải lên - Đang chờ xét duyệt'
          }
        </div>
      )}
      
      {/* Document Preview Modal */}
      <DocumentPreviewModal 
        document={previewDocument}
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
      />
    </form>
  );
};

export default DocumentUploadForm; 