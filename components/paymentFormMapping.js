import PaymentFormCOD from "./PaymentFormCOD";
import MasterPaymentForms from "./taiwan/payment/MasterPaymentForms";
import { FaMoneyBill, FaWallet, FaCreditCard, FaCcVisa } from "react-icons/fa";

// Payment methods by currency centralized definition
export const paymentMethodsByCurrency = {
  'VND': [
    { id: 'cod', name: '<PERSON><PERSON> toán khi nhận hàng', icon: <FaMoneyBill className="text-green-600" />, bgColor: 'bg-green-50', available: true },
    { id: 'bank', name: '<PERSON>yển khoản', icon: <FaWallet className="text-gray-600" />, bgColor: 'bg-gray-50', available: true },
    { id: 'momo', name: '<PERSON><PERSON><PERSON>', icon: '💰', bgColor: 'bg-pink-50', available: true },
    { id: 'vietnambanktransfer', name: '<PERSON><PERSON><PERSON><PERSON> khoản ngân hàng VN', icon: <FaWallet className="text-blue-600" />, bgColor: 'bg-blue-50', available: true },
    { id: 'banktransfer', name: '<PERSON><PERSON><PERSON><PERSON> quốc tế', icon: <FaWallet className="text-purple-600" />, bgColor: 'bg-purple-50', available: true }
  ],
  'NT$': [
    { id: '7-11', name: '7-Eleven (iBON)', icon: '🏪', bgColor: 'bg-green-100', available: true },
    { id: '7-11-card', name: '7-Eleven Card', icon: '💳', bgColor: 'bg-blue-100', available: true },
    { id: 'family-mart', name: 'FamilyMart', icon: '🛒', bgColor: 'bg-blue-100', available: true },
    { id: 'taiwanbanktransfer', name: 'Sinopac Dynamic QR Code', icon: <FaWallet className="text-blue-600" />, bgColor: 'bg-blue-50', available: true },
    /* { id: 'ok-mart', name: 'OK Mart', icon: '🏬', bgColor: 'bg-yellow-100', available: true },
    { id: 'hi-life', name: 'Hi-Life', icon: '🏪', bgColor: 'bg-red-100', available: true } */
  ],
  'NT': [
    { id: 'taiwanbanktransfer', name: 'Chuyển khoản ngân hàng', icon: <FaWallet className="text-blue-600" />, bgColor: 'bg-blue-50', available: true },
    /* { id: 'credit-card', name: 'Thẻ tín dụng/ghi nợ', icon: <FaCreditCard className="text-indigo-600" />, bgColor: 'bg-indigo-50', available: true }, */
    { id: '7-11', name: '7-Eleven (iBON)', icon: '🏪', bgColor: 'bg-green-100', available: true },
    { id: '7-11-card', name: '7-Eleven Card', icon: '💳', bgColor: 'bg-blue-100', available: true },
    { id: 'family-mart', name: 'FamilyMart', icon: '🛒', bgColor: 'bg-blue-100', available: true }
  ],
  'NTD': [
    { id: 'taiwanbanktransfer', name: 'Chuyển khoản ngân hàng', icon: <FaWallet className="text-blue-600" />, bgColor: 'bg-blue-50', available: true },
    /* { id: 'credit-card', name: 'Thẻ tín dụng/ghi nợ', icon: <FaCreditCard className="text-indigo-600" />, bgColor: 'bg-indigo-50', available: true }, */
    { id: '7-11', name: '7-11 (iBON)', icon: '🏪', bgColor: 'bg-green-100', available: true },
    { id: '7-11-card', name: '7-11 Card', icon: '💳', bgColor: 'bg-blue-100', available: true },
    { id: 'family-mart', name: 'FamilyMart', icon: '🛒', bgColor: 'bg-blue-100', available: true }
  ],
  'TWD': [
    { id: 'taiwanbanktransfer', name: 'Chuyển khoản ngân hàng', icon: <FaWallet className="text-blue-600" />, bgColor: 'bg-blue-50', available: true },
    /* { id: 'credit-card', name: 'Thẻ tín dụng/ghi nợ', icon: <FaCreditCard className="text-indigo-600" />, bgColor: 'bg-indigo-50', available: true }, */
    { id: '7-11', name: '7-11 (iBON)', icon: '🏪', bgColor: 'bg-green-100', available: true },
    { id: '7-11-card', name: '7-11 Card', icon: '💳', bgColor: 'bg-blue-100', available: true },
    { id: 'family-mart', name: 'FamilyMart', icon: '🛒', bgColor: 'bg-blue-100', available: true }
  ],
  '$': [
    { id: 'paypal', name: 'PayPal', icon: '🌐', bgColor: 'bg-blue-50', available: true },
    { id: 'stripe', name: 'Credit Card (Stripe)', icon: <FaCreditCard className="text-indigo-600" />, bgColor: 'bg-indigo-50', available: true },
    { id: 'inquiry', name: 'Inquiry', icon: '❓', bgColor: 'bg-yellow-50', available: true }
  ],
  'USD': [
    { id: 'paypal', name: 'PayPal', icon: '🌐', bgColor: 'bg-blue-50', available: true },
    { id: 'stripe', name: 'Credit Card (Stripe)', icon: <FaCreditCard className="text-indigo-600" />, bgColor: 'bg-indigo-50', available: true },
    { id: 'inquiry', name: 'Inquiry', icon: '❓', bgColor: 'bg-yellow-50', available: true }
  ],
  'default': [
    { id: 'bank', name: 'Bank Transfer', icon: <FaWallet className="text-gray-600" />, bgColor: 'bg-gray-50', available: true },
    { id: 'card', name: 'Credit Card', icon: <FaCcVisa className="text-blue-700" />, bgColor: 'bg-blue-50', available: true },
    { id: 'inquiry', name: 'Inquiry', icon: '❓', bgColor: 'bg-yellow-50', available: true },
    { id: 'banktransfer', name: 'Bank Transfer', icon: <FaWallet className="text-purple-600" />, bgColor: 'bg-purple-50', available: true }
  ]
};

// Mapping object to associate each payment method with its corresponding component
/* const PaymentMethods = {
  'cod': PaymentFormCOD,
  '7-11': MasterPaymentForms,
  '7-11-card': MasterPaymentForms,
  'family-mart': MasterPaymentForms,
  'ok-mart': MasterPaymentForms,
  'hi-life': MasterPaymentForms
};
 */
// Payment method components mapping
const PaymentFormComponents = {
    /* "stripe": Object.assign(PaymentFormStripe, { 
      displayName: "Credit Card", 
      icon: "💳",
      currencies: ["USD", "$", "NT$", "NT", "NTD", "TWD"] 
    }),
    "momo": Object.assign(PaymentFormMomo, { 
      displayName: "MoMo", 
      icon: "💰",
      currencies: ["VND"] 
    }),
    "zalopay": Object.assign(PaymentFormZaloPay, { 
      displayName: "ZaloPay", 
      icon: "💸",
      currencies: ["VND"] 
    }), */
    /* "banktransfer": Object.assign(PaymentFormBankTransfer, { 
      displayName: "Bank Transfer", 
      icon: "🏦",
      currencies: ["VND", "USD", "$", "NT$", "NT", "NTD", "TWD"] 
    }),
    "vietnambanktransfer": Object.assign(PaymentFormVietnamBankTransfer, { 
      displayName: "Vietnam Bank Transfer", 
      icon: "🏦",
      currencies: ["VND"] 
    }),
    "cod": Object.assign(PaymentFormCOD, { 
      displayName: "Cash on Delivery", 
      icon: "💵",
      currencies: ["VND"] 
    }), */
    /* "paypal": Object.assign(PaymentFormPayPal, { 
      displayName: "PayPal", 
      icon: "🌐",
      currencies: ["USD", "$"] 
    }),
    "inquiry": Object.assign(PaymentFormInquiry, { 
      displayName: "Inquiry", 
      icon: "❓",
      currencies: ["VND", "USD", "$", "NT$", "NT", "NTD", "TWD"] 
    }),
    "timescity": Object.assign(PaymentFormTimesCity, { 
      displayName: "Times City", 
      icon: "🏙️",
      currencies: ["VND"] 
    }), */
   /*  "taiwan": Object.assign(PaymentFormTaiwan, { 
      displayName: "Taiwan Payment", 
      icon: "🇹🇼",
      currencies: ["NT$", "NT", "NTD", "TWD"] 
    }), */
    "7-11": Object.assign(MasterPaymentForms, { 
      displayName: "7-11 iBON", 
      icon: "🏪",
      logo: "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.3.png",
      currencies: ["NT$", "NT", "NTD", "TWD"] 
    }),
    "7-11-card": Object.assign(MasterPaymentForms, {
        displayName: "7-11 Card Payment",
        icon: "💳",
        logo: "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.3.png",
        currencies: ["NT$", "NT", "NTD", "TWD"]
    }),
    "familymart": Object.assign(MasterPaymentForms, {
        displayName: "Family Mart Payment",
        icon: "🏪", 
        logo: "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.3.png",
        currencies: ["NT$", "NT", "NTD", "TWD"]
    }),
    "ok-mart": Object.assign(MasterPaymentForms, {
        displayName: "OK Mart Payment",
        icon: "🏪",
        logo: "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.3.png",
        currencies: ["NT$", "NT", "NTD", "TWD"]
    })
    /* "hi-life": Object.assign(MasterPaymentForms, {
        displayName: "Hi-Life Payment",  
        icon: "🏪",
        logo: "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/mag.logo.3.png",
        currencies: ["NT$", "NT", "NTD", "TWD"]
    }) */
  };
  
  
  
  // Helper function to get currency display name
  const getCurrencyDisplayName = (currencyCode) => {
    const currencyNames = {
      "VND": "Vietnamese Dong (VND)",
      "$": "US Dollar ($)",
      "USD": "US Dollar (USD)",
      "NT$": "New Taiwan Dollar (NT$)",
      "NT": "New Taiwan Dollar (NT)",
      "NTD": "New Taiwan Dollar (NTD)",
      "TWD": "Taiwan Dollar (TWD)"
    };
    
    return currencyNames[currencyCode] || currencyCode;
  };
  
export { PaymentFormComponents, getCurrencyDisplayName };