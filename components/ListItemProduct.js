import React from 'react'
import DENOMINATION from '../utils/currencyProvider'
import Image from './Image'
import { SiteContext, ContextProviderComponent } from '../context/mainContext';

const ListItemProduct = ({ link, openInNewTab, product, storeId, compact = false }) => {
  /* const imageSrc = Array.isArray(product.image) && product.image.length > 0 ? product.image[0] : 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp'; */
  const imageSrc = Array.isArray(product.image) && product.image.length > 0 ? product.image[0] : '';
  
  if (compact) {
    return (
      <div className="flex items-center space-x-4 w-full p-1 sm:p-2">
  <div className="w-1/3">
    <a href={link} {...(openInNewTab && { target: '_blank', rel: 'noopener noreferrer' })} aria-label={product.name}>
      <div className="h-24 flex justify-center items-center boxstyle_1 relative">
        {imageSrc !== '' ? (
          <Image alt={product.name} src={imageSrc} className="max-h-20 max-w-20 object-contain" />
        ) : (
          <div>{product.name}</div>
        )}
      </div>
    </a>
  </div>
  <div className="w-1/3 text-left">
    <p className="text-sm">{product.name}</p>
  </div>
  <div className="w-1/3 text-right">
    {product.price > 0 && (
      <p className="text-gray-700 text-sm">
        {product.price.toLocaleString()} {product.currency || DENOMINATION}
      </p>
    )}
    {product.validity && (
      <p className="text-gray-600 text-xs">
        Thời hạn: {product.validity} ngày
      </p>
    )}
    {product.territory && (
      <p className="text-gray-600 text-xs">
        Lãnh thổ: {product.territory}
      </p>
    )}
  </div>
</div>
    );
  }

  return (
    <div className="w-full p1 sm:p-2 md:w-1/2 lg:w-1/4">
      {/* <a href={link} {...(openInNewTab && { target: '_blank', rel: 'noopener noreferrer' })} aria-label={title}> */}
        <div className="h-72 flex justify-center items-center boxstyle_1 relative w-95 h-95">
          <a href={link} {...(openInNewTab && { target: '_blank', rel: 'noopener noreferrer' })} aria-label={product.name}>
            <div className="flex flex-column justify-center items-center w-95 h-95">
            {imageSrc !== '' ? (
              <Image alt={product.name} src={imageSrc} className="max-h-64 max-w-64 object-contain" />
            ) : (
              <div>{product.name}</div>
            )}
            </div>
          </a>
          <ContextProviderComponent>
            <SiteContext.Consumer>
              {context => (
                <div className="absolute bottom-1 right-1">
                  <button className='text-sm font-bold text-black font-semibold py-2 px-4 border-2 border-gray hover:border-transparent hover:bg-fuchsia'
                  onClick={() => context.addToCart({ ...product, quantity: 1, store: storeId })} //is the store valid here?
                  >
                    +
                  </button>
                </div>
              )}
            </SiteContext.Consumer>
          </ContextProviderComponent>
        </div>
      {/* </a> */}
      <a href={link} {...(openInNewTab && { target: '_blank', rel: 'noopener noreferrer' })} aria-label={product.name}>
        <div>
        {imageSrc !== '' ? (
              <p className="m-4 text-center text-l mb-1">{product.name}</p>
            ) : (
              <div></div>
            )}
          {product.price > 0 && (
            <p className="text-center text-gray-700 mb-1">              
              {'   '}{product.price !== undefined && product.price > 0 && `${product.price.toLocaleString()} ${product.currency || 'NT'}`}
            </p>
          )}
          {(product.validity || product.territory) && (
            <p className="text-center text-gray-600 text-sm mb-1">
              {product.validity && `Thời hạn: ${product.validity} ngày`}
              {product.validity && product.territory && ' | '}
              {product.territory && `Lãnh thổ: ${product.territory}`}
            </p>
          )}
          {product.price > 0 && (
            <p className="text-center text-gray-500 text-xs mb-4">              
              [MÃ {product.sku}]
            </p>
          )}
          {product.price <= 0 && (
            <p className="text-center text-gray-700 mb-4">              
              SKU {product.sku}
            </p>
          )}
        </div>
      </a>
    </div>
  );
}

export default ListItemProduct;
