import React from 'react'
import fetchInventoryForShop from "../utils/inventoryForShop"
import DENOMINATION from '../utils/currencyProvider'
import Image from '../components/Image'
import Link from 'next/link'
import { slugify } from '../utils/helpers'

class ViewInventory extends React.Component {
  state = {
    currentItem: null,
    editingIndex: null,
  }

  componentDidMount() {
    // Initialize state if props are available
    if (this.props.inventory && this.props.inventory.length > 0) {
      this.setState({
        currentItem: null,
        editingIndex: null
      });
    }
  }

  componentDidUpdate(prevProps) {
    // Reset editing state when inventory prop changes
    if (prevProps.inventory !== this.props.inventory) {
      this.setState({
        currentItem: null,
        editingIndex: null
      });
    }
  }

  editItem = (item, index) => {
    const editingIndex = index;
    this.setState({ editingIndex, currentItem: item });    
  }

  saveItem = async index => {
    try {
      // Prepare the update data
      const updateData = {
        id: this.state.currentItem.id,
        price: this.state.currentItem.price
      };
      
      // Add inventory data based on the structure
      if (this.state.currentItem.inventory && typeof this.state.currentItem.inventory === 'object') {
        updateData.inventory = this.state.currentItem.inventory;
      } else {
        updateData.currentInventory = this.state.currentItem.currentInventory;
      }
      
      const response = await fetch('/api/inventory', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('Failed to update item');
      }

      const updatedItem = await response.json();
      
      // Call the parent's updateItem method to update the parent's state
      if (this.props.updateItem) {
        this.props.updateItem(updatedItem.id, updatedItem);
      }
      
      this.setState({ editingIndex: null });
    } catch (error) {
      console.error('Error updating item:', error);
      // You might want to show an error message to the user here
    }
  }

  onChange = event => {
    const { name, value } = event.target;
    let currentItem = { ...this.state.currentItem };
    
    if (name === 'inventoryCount') {
      // Handle new inventory structure
      if (currentItem.inventory && typeof currentItem.inventory === 'object') {
        currentItem.inventory = {
          ...currentItem.inventory,
          count: parseInt(value) || 0
        };
      } else {
        // Fallback to old structure
        currentItem.currentInventory = parseInt(value) || 0;
      }
    } else {
      currentItem[name] = value;
    }
    
    this.setState({ currentItem });
  }

  render() {
    const { currentItem, editingIndex } = this.state;
    const { inventory } = this.props; // Use inventory from props
    
    console.log(`ViewInventory rendering with ${inventory ? inventory.length : 0} items`);
    
    return (
      <div>
        {inventory && inventory.length > 0 ? (
          inventory.map((item, index) => {
            const imageSrc = item.image || 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp';
            const isEditing = editingIndex === index;
            const displayItem = isEditing ? currentItem : item;

            return (
              <div key={item.id} className="flex items-center p-4 border-b">
                <div className="flex-shrink-0">
                  <img className="h-16 w-16 object-cover" src={imageSrc} alt={item.name} />
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{displayItem.name}</p>
                      <p className="text-sm text-gray-500">{displayItem.brand}</p>
                      <p className="text-xs text-gray-400">SKU: {displayItem.sku || displayItem.id}</p>
                    </div>
                    <div className="flex flex-1 justify-end items-center">
                      <p className="m-0 text-sm mr-2">In stock:</p>
                      <input
                        onChange={this.onChange}
                        className="shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        value={displayItem.inventory && typeof displayItem.inventory === 'object' ? displayItem.inventory.count : (displayItem.currentInventory || 0)}
                        name="inventoryCount"
                        placeholder="Item inventory"
                        disabled={!isEditing}
                      />
                      <input
                        onChange={this.onChange}
                        className="ml-16 shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        value={displayItem.price}
                        name="price"
                        placeholder="Item price"
                        disabled={!isEditing}
                      />
                    </div>
                  </div>
                </div>
                <div className="ml-4">
                  {isEditing ? (
                    <div role="button" onClick={() => this.saveItem(index)} className="text-green-600 hover:text-green-800">
                      <p className="text-sm">Save</p>
                    </div>
                  ) : (
                    <div role="button" onClick={() => this.editItem(item, index)} className="text-blue-600 hover:text-blue-800">
                      <p className="text-sm">Edit</p>
                    </div>
                  )}
                </div>
              </div>
            );
          })
        ) : (
          <div className="p-4 text-center text-gray-500">
            No products match the selected filters
          </div>
        )}
      </div>
    );
  }
}

export default ViewInventory