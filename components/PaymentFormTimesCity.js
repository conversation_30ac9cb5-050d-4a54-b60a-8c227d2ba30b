import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Image from '../components/Image';
import DisplayMediumNoBox from '../components';
import { Button } from './';
import { useContext } from 'react';
import { SiteContext } from '../context/mainContext'

const Input = ({ onChange, value, name, placeholder, type, min }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    /* type="text" */
    type={type || "text"}
    placeholder={placeholder}
    name={name}
    min={min} // Set the minimum date
  />
);


const calculateTotalForCurrentStore = (cart, shopId) => {
  let total = 0;
  for (const item of cart) {
    if (item.store === shopId) {
      total += item.price * item.quantity;
    }
  }
  return total;
};

/* const PaymentFormTimesCity = ({ handleSubmit, errorMessage, cart, orderId, totallll, paymentimage, shopId }) => { */
const PaymentFormTimesCity = ({ handleSubmit, errorMessage, cart, orderId, paymentimage, qr_line, shopId }) => {
  console.log("[TIMES_CITY_PAYMENT] Using order ID from checkout:", orderId);
  
  const { email, phone } = useContext(SiteContext);
  /* console.log("totalll:")
  console.log(total) */
  const [input, setInput] = useState({
    topic: `[ĐƠN HÀNG MỚI: SHOP ${shopId}]`,
    shopId: shopId,
    name: "",
    toanha: "",
    floorroom: "",
    email: email,
    time: "",
    ngaygiaohang: "",
    mobile: phone,
    message: ""
  });

  const total = calculateTotalForCurrentStore(cart, shopId);

  const [showThankMessage, setShowThankMessage] = useState(false); // State to manage the visibility of the thank you message
  const tomorrowFormatted = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  useEffect(() => {
    // Set ngaygiaohang to tomorrow's date when the component mounts    
    setInput(prevInput => ({ ...prevInput, ngaygiaohang: tomorrowFormatted }));
  }, []); // Empty dependency array ensures that this effect runs only once after mounting

  const onChange = e => {
    setInput({ ...input, [e.target.name]: e.target.value });
  };

  const downloadImagePayment = () => {
    // Create a new anchor element
    const anchor = document.createElement('a');

    // Set the href attribute to the image URL
    //anchor.href = '/payment_qrcode_thanhson_tcb.jpg';
    anchor.href = paymentimage !== null && paymentimage !== undefined && paymentimage !== "" ? paymentimage : '/payment_qrcode_thanhson_tcb.jpg'

    // Set the download attribute to force download
    anchor.download = paymentimage !== null && paymentimage !== undefined && paymentimage !== "" ? paymentimage : 'payment_qrcode_thanhson_tcb.jpg'

    // Simulate a click on the anchor element to trigger the download
    anchor.click();
  }

  const downloadImageZalo = () => {
    // Create a new anchor element
    const anchor = document.createElement('a');

    // Set the href attribute to the image URL
    //anchor.href = '/payment_qrcode_thanhson_tcb.jpg';
    anchor.href = qr_line !== null && qr_line !== undefined && qr_line !== "" ? qr_line : '/mag.qr.line.1.png'

    // Set the download attribute to force download
    anchor.download = qr_line !== null && qr_line !== undefined && qr_line !== "" ? qr_line : 'mag.qr.line.1.png'

    // Simulate a click on the anchor element to trigger the download
    anchor.click();
  }

  const handleSubmitInquiry = async (event, cart, orderId, total) => {
    /* console.log("orderId:")
    console.log(orderId) */
    if (!input.name || !input.toanha || !input.floorroom || !input.mobile) {
      alert('QUÝ KHÁCH CẦN ĐIỀN ĐẦY ĐỦ CÁC THÔNG TIN ĐỂ GIAO HÀNG THÀNH CÔNG');
      return;
    }
    event.preventDefault();

    if (!paymentOption) {
      alert('XIN QUÝ KHÁCH CHỌN XÁC NHẬN ĐÃ THANH TOÁN');
      return;
    }

    let retryAttempts = 3; // Maximum number of retry attempts
    while (retryAttempts > 0) {
      try {
        await axios.post('/api/createOrder', { ...input, cart, orderId, total });
        await axios.post('/api/sendEmail', { ...input, cart, orderId, total });
        //await axios.post('/api/registerUser', { ...input, cart, orderId, total }); 
        console.log('Logged order successfully');
        setShowThankMessage(true);
        break; // Exit the loop if successful
      } catch (error) {
        console.error('Failed to log order:', error);
        retryAttempts--; // Decrement the retry attempts
        if (retryAttempts === 0) {
          console.error('Maximum retry attempts reached');
        } else {
          console.log(`Retrying... Attempts left: ${retryAttempts}`);
        }
      }
    }
  };

  const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
  const tomorrow = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
  //const tomorrowFormatted = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  /* console.log("totallllll:")
  console.log(total) */
  const paymentlinkinform = paymentimage !== null && paymentimage !== undefined && paymentimage !== "" ? paymentimage : '/payment_qrcode_thanhson_tcb.jpg'
  const zalolinkinform = qr_line !== null && qr_line !== undefined && qr_line !== "" ? qr_line : '/mag.qr.line.1.png'

  const [paymentOption, setPaymentOption] = useState('');
  return (
    <div>
    <div className="flex flex-col md:flex-row"> {/* pt-8 */}
      <div className="w-full md:w-3/4 md:pr-4">
        <div className="flex flex-1 pt-8 flex-col">
          <div className="mt-4 border-t pt-10">
            <form onSubmit={handleSubmit}>
              {errorMessage ? <span>{errorMessage}</span> : ""}
              {/* <p style={{ textTransform: 'uppercase' }}><b><u>SHOP {shopId}:</u></b> NỘI DUNG THANH TOÁN QR NGÂN HÀNG: <b>{orderId} - SỐ ĐIỆN THOẠI - TÊN QUÝ KHÁCH</b></p> */}
              <Input
                onChange={onChange}
                value={input.name}
                name="name"
                placeholder="TÊN CHỦ NHÀ [CẦN ĐIỀN]:"
                required
              />
              <Input
                onChange={onChange}
                value={input.toanha}
                name="toanha"
                placeholder="TOÀ NHÀ [CẦN ĐIỀN]:"
                required
              />
              {/* <CardElement className="mt-2 shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" /> */}
              <Input
                onChange={onChange}
                value={input.floorroom}
                name="floorroom"
                placeholder="SỐ TẦNG VÀ SỐ PHÒNG [CẦN ĐIỀN]:"
                required
              />
              <Input
                onChange={onChange}
                value={input.mobile}
                name="mobile"
                placeholder="ĐIỆN THOẠI DI ĐỘNG [CẦN ĐIỀN]: "
                required
              />
              <Input
                onChange={onChange}
                value={input.email}
                name="email"
                placeholder="EMAIL: "
              />
              <Input
                onChange={onChange}
                value={input.time}
                name="time"
                placeholder="THỜI GIAN GIAO HÀNG MONG MUỐN:"
              />
              <p>Lịch giao hàng mặc định là vào ngày tiếp theo vào các khung giờ: 10-11h, 17-18h, 20-21h</p>
              <Input
                type="date"
                onChange={onChange}
                value={input.ngaygiaohang || tomorrowFormatted}
                name="ngaygiaohang"
                //min={today} // Set the minimum date to tomorrow's date
                min={tomorrow} // Set it to tomorrow to have time to handle
              />                            
              <Input
                onChange={onChange}
                value={input.message}
                name="message"
                placeholder="LỜI DẶN DÒ KHÁC:"
              />
            </form>
          </div>
        </div>
      </div>
      <div className="w-full md:w-2/4 md:pr-4 flex items-center justify-center">
      <div className="flex flex-col items-center w-full">
          <Button
            onClick={downloadImageZalo}
            title="QR LINE"
            small={true}
          />
          <Image className="w-full h-auto" src={zalolinkinform} alt="Zalo" />
        </div>
        <div className="flex flex-col items-center w-full">
          <Image className="w-full h-auto" src={paymentlinkinform} alt="Payment" />
          <Button
            onClick={downloadImagePayment}
            title="QR CHUYỂN KHOẢN"
            small={true}
          />
        </div>
      </div>
    </div>

{showThankMessage && <div className="blinking"> <p >ĐƠN HÀNG {orderId} ĐÃ ĐƯỢC GỬI. SHOP SẼ GIAO HÀNG THEO CHỈ DẪN. XIN CẢM ƠN!</p></div>}

<div className="pt-4">
  <p style={{ textTransform: 'uppercase' }}><b><u>SHOP {shopId}:</u></b> NỘI DUNG THANH TOÁN QR NGÂN HÀNG: <b>{orderId} - SỐ ĐIỆN THOẠI - TÊN QUÝ KHÁCH</b></p>
  <div className="pt-4">
    <div style={{ marginTop: '10px' }}>
      <label style={{ display: 'inline-block', marginRight: '20px', verticalAlign: 'middle' }} className="boxstyle_1">
        <input type="radio" name="paymentOption" value="didYouPay" style={{ marginRight: '5px' }} onChange={(event) => setPaymentOption(event.target.value)} />
        CHƯA THANH TOÁN
      </label>
      <label style={{ display: 'inline-block', marginRight: '20px', verticalAlign: 'middle' }} className="boxstyle_1">
        <input type="radio" name="paymentOption" value="alreadyPaid" style={{ marginRight: '5px' }} onChange={(event) => setPaymentOption(event.target.value)} />
        ĐÃ THANH TOÁN
      </label>
      <label style={{ display: 'inline-block', marginRight: '20px', verticalAlign: 'middle' }} className="boxstyle_1">
        <input type="radio" name="paymentOption" value="payCOD" style={{ marginRight: '5px' }} onChange={(event) => setPaymentOption(event.target.value)} />
        KHI NHẬN HÀNG
      </label>
    </div>
    <div className='pt-4'>
    <Button
      onClick={(event) => handleSubmitInquiry(event, cart, orderId, total)}
      title="ĐẶT HÀNG"
      small={false}
    />
    </div>
    
  </div>
</div>

    </div>
  );
};

export default PaymentFormTimesCity;
