import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaSync } from "react-icons/fa";
import MasterPaymentForms from "./taiwan/payment/MasterPaymentForms";
import { PaymentFormComponents } from "./paymentFormMapping";
import { 
  getPaymentMethodsByCurrentcy, 
} from "../utils/paymentUtils";

// Import the checkout logger
import {
  logCheckoutProcess,
  logCheckoutError,
  logCheckoutEvent,
  logTransaction
} from "../utils/checkoutLogger";

import { getProductImage, handleImageError } from '../utils/imageUtils';

// Add function to group cart items by type
const groupCartItemsByType = (items) => {
  return items.reduce((groups, item) => {
    // Define product type based on categories
    let productType = 'DEFAULT';
    if (item.categories && Array.isArray(item.categories)) {
      if (item.categories.some(cat => cat.toUpperCase() === 'CARD')) {
        productType = 'CARD';
      } else if (item.categories.some(cat => cat.toUpperCase() === 'SIM')) {
        productType = 'SIM';
      } else if (item.categories.some(cat => cat.toUpperCase() === 'TRAVELSIM')) {
        productType = 'TRAVELSIM';
      } else {
        // Try to identify other product types here
        productType = item.categories[0]?.toUpperCase() || 'DEFAULT';
      }
    }
    
    // Create product type group if it doesn't exist
    if (!groups[productType]) {
      groups[productType] = {
        type: productType,
        items: []
      };
    }
    
    // Add item to its group
    groups[productType].items.push(item);
    
    return groups;
  }, {});
};

const PaymentSection = ({
  selectedCurrency,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  totalForCurrentStore,
  orderId,
  filteredCartItems,
  storeObject,
  saveOrderToLocalStorage,
  addressData,
  handleAddressChange,
  saveAddressToProfile,
  cart,
  productCheckoutVisible,
  handleCurrencyChange,
  context,
  orderCompleted,
  setOrderCompleted,
  allowedPaymentMethods,
  // Add new props for payment initiated status
  paymentInitiated,
  paymentMethod,
  // Additional props needed for checkout steps
  checkoutSteps,
  currentStep,
  groupedProducts
}) => {
  const [showPaymentMethodsModal, setShowPaymentMethodsModal] = useState(false);
  const [filterCurrency, setFilterCurrency] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  
  // Add state for grouped items by type
  const [groupedByType, setGroupedByType] = useState({});
  
  // Add states for handling payment channels loading
  const [loadingPaymentChannels, setLoadingPaymentChannels] = useState(false);
  const [paymentChannelsError, setPaymentChannelsError] = useState(false);
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState([]);

  // Add logging utility
  const logPayment = (message, data = null) => {
    // Use the centralized checkout logger
    return logCheckoutProcess('payment_section', { message, data });
  };

  // Function to load payment channels
  const loadPaymentChannels = () => {
    setLoadingPaymentChannels(true);
    setPaymentChannelsError(false);
    
    try {
      // Get payment methods based on currency
      const methods = getPaymentMethodsByCurrentcy(selectedCurrency);
      logPayment("Loaded payment channels", { 
        currency: selectedCurrency, 
        methodCount: methods.length,
        methods: methods.map(m => m.id)
      });
      
      // Filter by allowed methods if available
      const filtered = allowedPaymentMethods
        ? methods.filter(method => allowedPaymentMethods.includes(method.id))
        : methods;
        
      setAvailablePaymentMethods(filtered);
      
      // If previously selected method is not available in new methods, reset it
      if (selectedPaymentMethod && !filtered.some(m => m.id === selectedPaymentMethod)) {
        if (filtered.length > 0) {
          logPayment("Resetting payment method", { 
            previous: selectedPaymentMethod, 
            new: filtered[0].id 
          });
          setSelectedPaymentMethod(filtered[0].id);
        } else {
          setSelectedPaymentMethod(null);
        }
      }
      
      // If no payment method selected yet but methods are available, select first one
      if (!selectedPaymentMethod && filtered.length > 0) {
        logPayment("Setting initial payment method", { selected: filtered[0].id });
        setSelectedPaymentMethod(filtered[0].id);
      }
      
      setLoadingPaymentChannels(false);
    } catch (error) {
      logPayment("Error loading payment channels", { error: error.message });
      
      // Log the error with more details
      logCheckoutError('payment_channels', error, {
        currency: selectedCurrency,
        allowedMethods: allowedPaymentMethods
      });
      
      setPaymentChannelsError(true);
      setLoadingPaymentChannels(false);
    }
  };

  // Load payment channels on currency change
  useEffect(() => {
    loadPaymentChannels();
  }, [selectedCurrency, allowedPaymentMethods]);

  // Log payment initiated status
  useEffect(() => {
    if (paymentInitiated) {
      logPayment("Payment already initiated", { method: paymentMethod });
    }
  }, [paymentInitiated, paymentMethod]);

  // Add a warning banner at the top for payment initiated (for product checkout view)
  const PaymentInitiatedWarning = () => {
    if (!paymentInitiated) return null;
    
    return (
      <div className="mb-4 bg-yellow-50 border-l-4 border-yellow-400 p-3 md:p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              <strong>Chú ý:</strong> Đơn hàng này đã được thanh toán bằng phương thức {paymentMethod}. Vui lòng không thanh toán lại.
            </p>
            <p className="mt-1 text-sm text-yellow-700">
              Để kiểm tra trạng thái đơn hàng hoặc tạo đơn hàng mới, vui lòng liên hệ với cửa hàng.
            </p>
          </div>
        </div>
      </div>
    );
  };

  // Error component for payment channels with retry button
  const PaymentChannelsError = () => {
    return (
      <div className="my-4 bg-red-50 border border-red-200 rounded-lg p-4 text-center">
        <p className="text-red-600 mb-3">Không thể tải thông tin phương thức thanh toán. Vui lòng thử lại.</p>
        <button 
          onClick={loadPaymentChannels}
          className="flex items-center justify-center mx-auto px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          <FaSync className="mr-2" /> Thử lại
        </button>
      </div>
    );
  };

  // Group items by type when filtered items change
  useEffect(() => {
    if (filteredCartItems && filteredCartItems.length > 0) {
      const grouped = groupCartItemsByType(filteredCartItems);
      setGroupedByType(grouped);
      logPayment("Grouped items by type", { 
        types: Object.keys(grouped),
        counts: Object.entries(grouped).reduce((acc, [type, group]) => {
          acc[type] = group.items.length;
          return acc;
        }, {})
      });
    } else {
      setGroupedByType({});
    }
  }, [filteredCartItems]);

  // Get the selected payment form component based on the selected payment method
  const PaymentFormComponent = selectedPaymentMethod ? 
    (PaymentFormComponents[selectedPaymentMethod] || MasterPaymentForms) : 
    null;

  // If payment method doesn't exist, log the error
  React.useEffect(() => {
    if (!PaymentFormComponent && selectedPaymentMethod) {
      logPayment("ERROR: Selected payment method has no component", { 
        selectedMethod: selectedPaymentMethod,
        availableMethods: Object.keys(PaymentFormComponents)
      });
    }
  }, [selectedPaymentMethod, PaymentFormComponent]);

  // If in product checkout mode and it's not a payment or checkout step, return null
  if (productCheckoutVisible) {
    // Only show payment section if we're in a payment or checkout step
    const isPaymentStep = checkoutSteps && checkoutSteps[currentStep]?.id === 'payment' || checkoutSteps && checkoutSteps[currentStep]?.id === 'checkout';
    if (!isPaymentStep) return null;
  }

  // Render the product summary by type - for normal view
  const renderProductSummary = () => {
    if (!filteredCartItems || filteredCartItems.length === 0) {
      return (
        <div className="text-center py-6">
          <p className="text-gray-500">No products available for this currency</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {Object.entries(groupedByType).map(([type, group]) => {
          const typeTotal = group.items.reduce((sum, item) => 
            sum + (item.price * item.quantity), 0);
          
          return (
            <div key={type} className="border rounded-lg overflow-hidden">
              <div className="bg-gray-50 p-3 border-b flex justify-between items-center">
                <h3 className="font-medium">{type} ({group.items.length})</h3>
                <span className="text-sm font-semibold">{selectedCurrency} {typeTotal.toLocaleString()}</span>
              </div>
              
              <div className="p-4">
                <div className="space-y-4">
                  {group.items.map((item, index) => {
                    return (
                      <div className="flex items-center border-b pb-3" key={`${item.sku}-${index}`}>
                        <div className="flex-shrink-0 w-16 h-16 bg-gray-100 rounded overflow-hidden mr-3">
                          <img 
                            src={getProductImage(item.image, item.sku, item.name)} 
                            alt={item.name} 
                            className="w-full h-full object-cover"
                            onError={(e) => handleImageError(e, item.sku, item.name)}
                          />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{item.name}</h4>
                          <p className="text-sm text-gray-600">SKU: {item.sku}</p>
                          <div className="flex justify-between mt-1 text-sm">
                            <span>x{item.quantity}</span>
                            <span className="font-medium">{selectedCurrency} {item.price.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Render product summary grouped by type - for checkout steps
  const renderCheckoutProductSummary = () => {
    return (
      <div className="mt-4 mb-6">
        <h3 className="text-lg font-medium mb-3">Sản phẩm</h3>
        {Object.entries(groupedProducts).map(([type, products]) => (
          <div key={type} className="mb-4">
            <h4 className="font-medium text-gray-700 mb-2">{type} ({products.length})</h4>
            <div className="space-y-3 pl-2 md:pl-4">
              {products.map((product, index) => (
                <div key={`${product.sku}-${index}`} className="flex items-center border-b pb-3">
                  <div className="flex-shrink-0 w-12 h-12 md:w-16 md:h-16 bg-gray-100 rounded overflow-hidden mr-2 md:mr-3">
                    {product.image && (
                      <img 
                        src={Array.isArray(product.image) ? product.image[0] : product.image} 
                        alt={product.name} 
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm md:text-base truncate">{product.name}</h4>
                    <p className="text-xs md:text-sm text-gray-600">SKU: {product.sku}</p>
                    <div className="flex justify-between mt-1 text-xs md:text-sm">
                      <span>x{product.quantity}</span>
                      <span className="font-medium">{selectedCurrency}{product.price.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // If in product checkout view, render a simplified payment section
  if (productCheckoutVisible) {
    return (
      <div className="mt-4 md:mt-8">
        {/* Product Summary */}
        {renderCheckoutProductSummary()}
        
        {/* Payment Method Selection - Hide on checkout step */}
        {checkoutSteps && checkoutSteps[currentStep]?.id !== 'checkout' && (
          <div className="w-full px-2 md:px-4 mb-4 md:mb-6">
            {/* Add the warning banner */}
            <PaymentInitiatedWarning />

            <div className="bg-white border rounded-lg overflow-hidden">
              <div className="bg-gray-50 p-3 md:p-4 border-b">
                <h3 className="text-base md:text-lg font-medium">Phương thức thanh toán</h3>
              </div>
              <div className="p-3 md:p-4">
                <div className="text-gray-600 mb-3">Khả dụng cho {selectedCurrency}</div>
                
                {/* Show error or loading state */}
                {paymentChannelsError ? (
                  <PaymentChannelsError />
                ) : loadingPaymentChannels ? (
                  <div className="text-center py-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto"></div>
                    <p className="mt-2">Đang tải phương thức thanh toán...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-3">
                    {availablePaymentMethods.map(method => (
                      <button
                        key={method.id}
                        onClick={(e) => {
                          // Prevent event bubbling to parent section header
                          e.stopPropagation();
                          
                          if (!paymentInitiated) {
                            console.log('Selecting payment method:', method.id, 'Previous:', selectedPaymentMethod);
                            
                            // Log the payment method selection with transaction details
                            logPayment("Payment method selected from modal", { 
                              methodId: method.id, 
                              previousMethod: selectedPaymentMethod,
                              currency: selectedCurrency,
                              amount: totalForCurrentStore,
                              orderId
                            });
                            
                            // Also log as a transaction event
                            logCheckoutEvent('PAYMENT_METHOD_SELECTED', {
                              methodId: method.id,
                              name: method.name,
                              previousMethod: selectedPaymentMethod,
                              currency: selectedCurrency,
                              amount: totalForCurrentStore,
                              orderId,
                              location: 'payment_modal'
                            });
                            
                            // Also log as a transaction
                            if (orderId) {
                              logTransaction(orderId, 'PAYMENT_METHOD_SELECTED', {
                                paymentMethod: method.id,
                                currency: selectedCurrency,
                                amount: totalForCurrentStore
                              });
                            }
                            
                            setSelectedPaymentMethod(method.id);
                          } else {
                            // Show error message if payment already initiated
                            setErrorMessage(`Payment already initiated using ${paymentMethod}. Please check your order status.`);
                          }
                        }}
                        disabled={paymentInitiated}
                        className={`flex items-center p-2 md:p-3 border rounded-lg ${
                          selectedPaymentMethod === method.id
                            ? 'border-black bg-gray-50'
                            : 'border-gray-200 hover:bg-gray-50'
                        } ${paymentInitiated ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        <div className={`${method.bgColor} rounded-full w-8 h-8 md:w-10 md:h-10 flex items-center justify-center mr-2 md:mr-3`}>
                          {typeof method.icon === 'string' ? (
                            <span className="text-base md:text-lg">{method.icon}</span>
                          ) : method.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <span className="block font-medium text-sm md:text-base truncate">{method.name}</span>
                        </div>
                        {selectedPaymentMethod === method.id && (
                          <div className="w-3 h-3 md:w-4 md:h-4 rounded-full bg-black ml-auto flex-shrink-0"></div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
                
                {/* Payment initiated note */}
                {paymentInitiated && (
                  <div className="mt-3 text-sm text-gray-600">
                    <p>Đơn hàng này đã được thanh toán bằng {paymentMethod}. Thanh toán đã được khởi tạo và không thể thay đổi.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Order Summary */}
        <div className="mt-4 md:mt-6 bg-gray-50 p-3 md:p-4 rounded-lg">
          <h3 className="text-base md:text-lg font-medium mb-3 md:mb-4">Tổng đơn hàng</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Tạm tính:</span>
              <span>{selectedCurrency}{totalForCurrentStore}</span>
            </div>
            <div className="flex justify-between">
              <span>Phí vận chuyển:</span>
              <span>{selectedCurrency}0</span>
            </div>
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between font-medium">
                <span>Tổng cộng:</span>
                <span>{selectedCurrency}{totalForCurrentStore}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Form */}
        <div className="mt-6">
          {paymentInitiated ? (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2">Thanh toán đã được khởi tạo</h3>
              <p className="text-sm text-gray-600">
                Đơn hàng này đã được thanh toán bằng phương thức {paymentMethod}. 
                Vui lòng không thanh toán lại để tránh trùng lặp.
              </p>
            </div>
          ) : (
            <MasterPaymentForms
              paymentMethod={selectedPaymentMethod}
              amount={totalForCurrentStore}
              currency={selectedCurrency}
              orderId={orderId}
              cart={filteredCartItems.filter(item => item.store === storeObject.storeId)}
              cartItems={filteredCartItems.filter(item => item.store === storeObject.storeId)} 
              shopId={storeObject.storeId}
              saveOrderToLocalStorage={saveOrderToLocalStorage}
              key={`${selectedPaymentMethod}-${selectedCurrency}`}
            />
          )}
        </div>

        {/* Error Message */}
        {errorMessage && (
          <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{errorMessage}</span>
            <span 
              className="absolute top-0 bottom-0 right-0 px-4 py-3" 
              onClick={(e) => {
                // Prevent event bubbling to parent section header
                e.stopPropagation();
                setErrorMessage(null);
              }}
            >
              <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <title>Close</title>
                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
              </svg>
            </span>
          </div>
        )}
      </div>
    );
  }

  // Regular view (non-product checkout)
  return (
    <>
      {/* Product Summary */}
      <div className="w-full px-4 mb-6">
        <h2 className="text-lg font-medium mb-3">Sản phẩm ({filteredCartItems.length})</h2>
        {renderProductSummary()}
      </div>

      {/* Payment Method Selection */}
      <div className="w-full px-4 mb-6">
        <div className="bg-white border rounded-lg overflow-hidden">
          <div className="bg-gray-50 p-4 border-b flex justify-between items-center">
            <h2 className="text-lg font-medium">Phương thức thanh toán</h2>
            <button 
              className="flex items-center text-sm text-blue-600 hover:text-blue-800"
              onClick={(e) => {
                // Prevent event bubbling to parent section header
                e.stopPropagation();
                
                logPayment("Opening payment method selection modal");
                logCheckoutEvent('PAYMENT_SELECTION_OPENED', {
                  currency: selectedCurrency,
                  currentMethod: selectedPaymentMethod,
                  availableMethods: availablePaymentMethods.map(m => m.id)
                });
                setShowPaymentMethodsModal(true);
              }}
            >
              <FaSync className="mr-1" /> Thay đổi
            </button>
          </div>
          <div className="p-4">
            {loadingPaymentChannels ? (
              <div className="text-center py-6">
                <p className="text-gray-500">Đang tải phương thức thanh toán...</p>
              </div>
            ) : paymentChannelsError ? (
              <div className="bg-red-50 text-red-700 p-4 rounded-lg flex items-start">
                <FaExclamationTriangle className="flex-shrink-0 mr-2 mt-0.5" />
                <div>
                  <p className="font-medium">Lỗi tải phương thức thanh toán</p>
                  <p className="text-sm mt-1">Vui lòng thử lại sau hoặc chọn tiền tệ khác</p>
                  <button 
                    className="mt-2 text-sm bg-red-100 px-3 py-1 rounded hover:bg-red-200"
                    onClick={(e) => {
                      // Prevent event bubbling to parent section header
                      e.stopPropagation();
                      
                      logPayment("Retrying payment channels load");
                      loadPaymentChannels();
                    }}
                  >
                    Thử lại
                  </button>
                </div>
              </div>
            ) : (
              <>
                {availablePaymentMethods.length === 0 ? (
                  <div className="bg-yellow-50 text-yellow-700 p-4 rounded-lg">
                    <p className="font-medium">Không có phương thức thanh toán</p>
                    <p className="text-sm mt-1">Không có phương thức thanh toán nào cho tiền tệ {selectedCurrency}</p>
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                      {availablePaymentMethods.slice(0, 6).map((method) => (
                        <button
                          key={method.id}
                          className={`border rounded-lg p-3 flex flex-col items-center justify-center transition-colors ${
                            selectedPaymentMethod === method.id ? 
                            'border-black bg-gray-50' : 
                            'border-gray-200 hover:bg-gray-50'
                          }`}
                          onClick={(e) => {
                            // Prevent event bubbling to parent section header
                            e.stopPropagation();
                            
                            logPayment("Payment method selected from grid", { 
                              methodId: method.id,
                              previousMethod: selectedPaymentMethod,
                              currency: selectedCurrency,
                              amount: totalForCurrentStore,
                              orderId
                            });
                            
                            // Enhanced event logging with more details
                            logCheckoutEvent('PAYMENT_METHOD_SELECTED', {
                              methodId: method.id,
                              name: method.name,
                              previousMethod: selectedPaymentMethod,
                              currency: selectedCurrency,
                              amount: totalForCurrentStore,
                              orderId,
                              location: 'payment_grid'
                            });
                            
                            // Also log as a transaction
                            if (orderId) {
                              logTransaction(orderId, 'PAYMENT_METHOD_SELECTED', {
                                paymentMethod: method.id,
                                currency: selectedCurrency,
                                amount: totalForCurrentStore
                              });
                            }
                            
                            setSelectedPaymentMethod(method.id);
                          }}
                        >
                          {method.icon && (
                            <span className="text-2xl mb-1">{method.icon}</span>
                          )}
                          <span className="text-sm text-center">{method.name}</span>
                        </button>
                      ))}
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Order Summary */}
      <div className="w-full px-4 mb-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-medium mb-4">Tổng đơn hàng</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Tạm tính:</span>
              <span>{selectedCurrency} {totalForCurrentStore.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>Phí vận chuyển:</span>
              <span>{selectedCurrency} 0</span>
            </div>
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between font-medium">
                <span>Tổng cộng:</span>
                <span>{selectedCurrency} {totalForCurrentStore.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Selected Payment Form */}
      {PaymentFormComponent && (
        <div className="w-full px-4 mb-6">
          <div className="bg-white border rounded-lg overflow-hidden">
            <div className="bg-gray-50 p-4 border-b">
              <h2 className="text-lg font-medium">Chi tiết thanh toán</h2>
            </div>
            <div className="p-4">
              {paymentInitiated ? (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-800 mb-2">Thanh toán đã được khởi tạo</h3>
                  <p className="text-sm text-gray-600">
                    Đơn hàng này đã được thanh toán bằng phương thức {paymentMethod}. 
                    Vui lòng không thanh toán lại để tránh trùng lặp.
                  </p>
                </div>
              ) : (
                <MasterPaymentForms
                  paymentMethod={selectedPaymentMethod}
                  amount={totalForCurrentStore}
                  currency={selectedCurrency}
                  orderId={orderId}
                  cart={filteredCartItems.filter(item => item.store === storeObject.storeId)}
                  cartItems={filteredCartItems.filter(item => item.store === storeObject.storeId)}
                  shopId={storeObject.storeId}
                  saveOrderToLocalStorage={saveOrderToLocalStorage}
                  key={`${selectedPaymentMethod}-${selectedCurrency}`}
                />
              )}
            </div>
          </div>
        </div>
      )}

      {/* Payment Form for non-specific methods */}
      {!PaymentFormComponent && selectedPaymentMethod && (
        <div className="w-full px-4 mb-6">
          {paymentInitiated ? (
            <div className="bg-white border rounded-lg overflow-hidden">
              <div className="bg-gray-50 p-4 border-b">
                <h2 className="text-lg font-medium">Chi tiết thanh toán</h2>
              </div>
              <div className="p-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-800 mb-2">Thanh toán đã được khởi tạo</h3>
                  <p className="text-sm text-gray-600">
                    Đơn hàng này đã được thanh toán bằng phương thức {paymentMethod}. 
                    Vui lòng không thanh toán lại để tránh trùng lặp.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <MasterPaymentForms
              paymentMethod={selectedPaymentMethod}
              amount={totalForCurrentStore}
              currency={selectedCurrency}
              orderId={orderId}
              cart={filteredCartItems.filter(item => item.store === storeObject.storeId)}
              cartItems={filteredCartItems.filter(item => item.store === storeObject.storeId)} 
              shopId={storeObject.storeId}
              saveOrderToLocalStorage={saveOrderToLocalStorage}
              key={`${selectedPaymentMethod}-${selectedCurrency}`}
            />
          )}
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{errorMessage}</span>
          <span className="absolute top-0 bottom-0 right-0 px-4 py-3" onClick={() => setErrorMessage(null)}>
            <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <title>Close</title>
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </span>
        </div>
      )}
    </>
  );
};

export default PaymentSection; 