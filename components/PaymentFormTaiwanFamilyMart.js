import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Image from '../components/Image';
import DisplayMediumNoBox from '../components';
import { Button } from './';
import { useContext } from 'react';
import { SiteContext } from '../context/mainContext'

const Input = ({ onChange, value, name, placeholder, type, min, required }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    /* type="text" */
    type={type || "text"}
    placeholder={placeholder}
    name={name}
    min={min} // Set the minimum date
    required={required}
  />
);


const calculateTotalForCurrentStore = (cart, shopId) => {
  let total = 0;
  for (const item of cart) {
    if (item.store === shopId) {
      total += item.price * item.quantity;
    }
  }
  return total;
};


const PaymentFormTaiwanFamilyMart = ({ handleSubmit, errorMessage, cart, orderId, shopId }) => {
  const { email, phone } = useContext(SiteContext);
  /* console.log("totalll:")
  console.log(total) */
  const [input, setInput] = useState({
    topic: `[NEW ORDER: SHOP ${shopId}]`,
    shopId: shopId,
    name: "",
    familyMartStore: "",
    storeCode: "",
    email: email,
    mobile: phone,
    message: ""
  });

  const total = calculateTotalForCurrentStore(cart, shopId);

  const [showThankMessage, setShowThankMessage] = useState(false); // State to manage the visibility of the thank you message

  const onChange = e => {
    setInput({ ...input, [e.target.name]: e.target.value });
  };

  const handleSubmitFamilyMart = async (event, cart, orderId, total) => {
    if (!input.name || !input.familyMartStore || !input.storeCode || !input.mobile) {
      alert('Cần điền đầy đủ các thông tin bắt buộc.');
      return;
    }
    event.preventDefault();

    let retryAttempts = 3;
    while (retryAttempts > 0) {
      try {
        await axios.post('/api/createOrder', { ...input, cart, orderId, total });
        await axios.post('/api/sendEmail', { ...input, cart, orderId, total });
        console.log('Order logged successfully');
        setShowThankMessage(true);
        break;
      } catch (error) {
        console.error('Failed to log order:', error);
        retryAttempts--;
        if (retryAttempts === 0) {
          console.error('Maximum retry attempts reached');
        } else {
          console.log(`Retrying... Attempts left: ${retryAttempts}`);
        }
      }
    }
  };

  return (
    <div>
      <div className="flex flex-col md:flex-row"> {/* pt-8 */}
        <div className="w-full md:w-3/4 md:pr-4">
          <div className="flex flex-1 pt-8 flex-col">
            <div className="mt-4 border-t pt-10">
              <form onSubmit={handleSubmit}>
                {errorMessage ? <span>{errorMessage}</span> : ""}
                <Input
                  onChange={onChange}
                  value={input.name}
                  name="name"
                  placeholder="Họ và Tên (Bắt buộc)"
                  required={true}
                />
                <Input
                  onChange={onChange}
                  value={input.familyMartStore}
                  name="familyMartStore"
                  placeholder="Tên Cửa Hàng FamilyMart (Bắt buộc)"
                  required={true}
                />
                <Input
                  onChange={onChange}
                  value={input.storeCode}
                  name="storeCode"
                  placeholder="Mã Cửa Hàng (Bắt buộc)"
                  required={true}
                />
                <Input
                  onChange={onChange}
                  value={input.mobile}
                  name="mobile"
                  placeholder="Số Điện Thoại Di Động (Bắt buộc)"
                  required={true}
                />
                <Input
                  onChange={onChange}
                  value={input.email}
                  name="email"
                  placeholder="Email"
                />
                <Input
                  onChange={onChange}
                  value={input.message}
                  name="message"
                  placeholder="Ghi Chú Thêm"
                />
              </form>
            </div>
          </div>
        </div>
      </div>

      {showThankMessage && 
        <div className="blinking">
          <p>Đơn hàng {orderId} đã được gửi. Chúng tôi sẽ chuẩn bị đơn hàng của bạn để lấy tại FamilyMart. Cảm ơn bạn!</p>
        </div>
      }

      <div className="pt-4">
        <p style={{ textTransform: 'uppercase' }}>
          <b><u>CỬA HÀNG {shopId}:</u></b> Mã Đơn Hàng: <b>{orderId}</b>
        </p>
        <div className='pt-4'>
          <Button
            onClick={(event) => handleSubmitFamilyMart(event, cart, orderId, total)}
            title="GỬI ĐƠN HÀNG"
            small={false}
          />
        </div>
      </div>
    </div>
  );
};

export default PaymentFormTaiwanFamilyMart;
