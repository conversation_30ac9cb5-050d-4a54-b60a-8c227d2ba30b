import { isCustomerAuthenticated, getCustomerInfo } from '../utils/customerAuth';
import LoginButton from './LoginButton';

const Navbar = ({ storeObject }) => {
  const [isCustomerLoggedIn, setIsCustomerLoggedIn] = useState(false);
  const [customerInfo, setCustomerInfo] = useState(null);
  
  useEffect(() => {
    // Check if customer is logged in
    const loggedIn = isCustomerAuthenticated();
    setIsCustomerLoggedIn(loggedIn);
    
    if (loggedIn) {
      setCustomerInfo(getCustomerInfo());
    }
  }, []);
  
  const handleLoginSuccess = (customerId, info) => {
    setIsCustomerLoggedIn(true);
    setCustomerInfo(info);
  };
  
  return (
    <nav className="...">
      {/* ... existing navbar content ... */}
      
      {/* Add this near the end of your navbar, before the end of the nav tag */}
      <div className="flex items-center ml-auto">
        {isCustomerLoggedIn ? (
          <div className="flex items-center">
            <div className="text-sm mr-4">
              <span className="font-medium">Welcome, {customerInfo?.name || 'Customer'}</span>
            </div>
          </div>
        ) : (
          <LoginButton onLoginSuccess={handleLoginSuccess} />
        )}
      </div>
      
      {/* ... rest of navbar ... */}
    </nav>
  );
};

export default Navbar; 