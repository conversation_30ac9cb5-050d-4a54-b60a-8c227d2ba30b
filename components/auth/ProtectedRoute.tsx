import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import CustomerLogin from './CustomerLogin';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation();
  const isAuthenticated = localStorage.getItem('customerToken');

  if (!isAuthenticated) {
    // Redirect to login page but save the attempted URL
    return <Navigate to="/customer/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute; 