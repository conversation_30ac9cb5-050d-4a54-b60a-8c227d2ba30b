import React, { useEffect } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Import your custom marker icon image
// let customIconUrl = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abn/favicon.ico";

const LeafletMap = ({ latitude, longitude, iconurl }) => {
  useEffect(() => {
    // Create custom marker icon
    const myIcon = L.icon({
      iconUrl: iconurl || 'https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp',
      iconSize: [40, 40],
      iconAnchor: [20, 40],
      popupAnchor: [0, -40],
      className: 'rounded-full border-2 border-white shadow-lg'
    });

    // Create a map instance
    const map = L.map('map', {
      zoomControl: false, // Hide default zoom control
      attributionControl: false // Hide attribution
    }).setView([latitude, longitude], 17);

    // Add a custom tile layer with a modern style
    L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
      maxZoom: 19
    }).addTo(map);

    // Add custom zoom control to top-right
    L.control.zoom({
      position: 'topright'
    }).addTo(map);

    // Add a marker with custom icon to the map
    const marker = L.marker([latitude, longitude], { 
      icon: myIcon,
      bounceOnAdd: true
    }).addTo(map);

    // Optional: Add a subtle circle around the marker
    L.circle([latitude, longitude], {
      color: '#3B82F6',
      fillColor: '#3B82F6',
      fillOpacity: 0.1,
      radius: 50,
      weight: 1
    }).addTo(map);

    // Cleanup on unmount
    return () => {
      map.remove();
    };
  }, [latitude, longitude, iconurl]);

  return <div id="map" className="w-full h-full" />;
}

export default LeafletMap;
