import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  
  // Current language info
  const languages = {
    en: { name: 'English', flag: '🇺🇸' },
    vi: { name: 'Tiếng Việt', flag: '🇻🇳' }
  };
  
  // Get the base language code (e.g., 'en-US' becomes 'en')
  const getCurrentLang = () => {
    const fullLang = i18n.language || 'vi';
    // Extract base language code (everything before the hyphen if it exists)
    const baseLang = fullLang.split('-')[0];
    // Return the base language if it's in our list, otherwise default to 'vi'
    return languages[baseLang] ? baseLang : 'vi';
  };
  
  const currentLang = getCurrentLang();
  
  // Toggle dropdown
  const toggleDropdown = () => setIsOpen(!isOpen);
  
  // Change language and close dropdown
  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    setIsOpen(false);
  };
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button 
        onClick={toggleDropdown}
        className="flex items-center space-x-1 bg-white rounded-md px-3 py-2 border border-gray-300 hover:bg-gray-100 transition-colors"
      >
        <span>{languages[currentLang].flag}</span>
        {/* <span className="ml-1">{languages[currentLang].name}</span> */}
        <svg 
          className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 overflow-hidden border border-gray-200">
          <div className="py-1">
            {Object.entries(languages).map(([code, { name, flag }]) => (
              <button
                key={code}
                onClick={() => changeLanguage(code)}
                className={`w-full text-left px-4 py-2 flex items-center hover:bg-gray-100 transition-colors ${
                  currentLang === code ? 'bg-orange-50 text-orange-600' : ''
                }`}
              >
                <span className="mr-2">{flag}</span>
                <span>{name}</span>
                {currentLang === code && (
                  <svg className="ml-auto w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;

