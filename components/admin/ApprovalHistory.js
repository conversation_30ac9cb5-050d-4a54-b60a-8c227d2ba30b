import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faHistory, faUser, faCalendarAlt, faCheckCircle, 
  faTimesCircle, faClock, faExclamationTriangle,
  faChevronDown, faChevronUp, faFilter
} from '@fortawesome/free-solid-svg-icons';

const ApprovalHistory = ({ customerId }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);
  const [filter, setFilter] = useState('all'); // all, approved, rejected, pending

  useEffect(() => {
    const fetchApprovalHistory = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/admin/approval-history?customerId=${customerId}`);
        const data = await response.json();
        
        if (data.success) {
          setHistory(data.history || []);
        } else {
          console.error('Error fetching approval history:', data.error);
          setHistory([]);
        }
      } catch (error) {
        console.error('Error fetching approval history:', error);
        setHistory([]);
      } finally {
        setLoading(false);
      }
    };

    if (customerId) {
      fetchApprovalHistory();
    }
  }, [customerId]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <FontAwesomeIcon icon={faCheckCircle} className="text-green-600" />;
      case 'rejected':
        return <FontAwesomeIcon icon={faTimesCircle} className="text-red-600" />;
      case 'requires_update':
        return <FontAwesomeIcon icon={faExclamationTriangle} className="text-orange-600" />;
      case 'pending':
      default:
        return <FontAwesomeIcon icon={faClock} className="text-yellow-600" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'approved':
        return 'Đã duyệt';
      case 'rejected':
        return 'Từ chối';
      case 'requires_update':
        return 'Cần cập nhật';
      case 'pending':
      default:
        return 'Chờ duyệt';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'requires_update':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'pending':
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getSectionName = (section) => {
    const sectionNames = {
      fullName: 'Họ và Tên',
      dateOfBirth: 'Ngày Sinh',
      gender: 'Giới Tính',
      passportNumber: 'Số Hộ Chiếu',
      nationality: 'Quốc Tịch',
      email: 'Email',
      phone: 'Số Điện Thoại',
      currentAddress: 'Địa Chỉ Hiện Tại',
      emergencyContact: 'Liên Hệ Khẩn Cấp',
      occupation: 'Nghề Nghiệp',
      company: 'Công Ty',
      visaStatus: 'Tình Trạng Visa',
      membershipLevel: 'Hạng Thành Viên'
    };
    return sectionNames[section] || section;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredHistory = history.filter(item => {
    if (filter === 'all') return true;
    return item.newStatus === filter;
  });

  const displayedHistory = expanded ? filteredHistory : filteredHistory.slice(0, 5);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <FontAwesomeIcon icon={faHistory} className="text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Lịch Sử Duyệt</h3>
        </div>
        <div className="animate-pulse space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-100 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <FontAwesomeIcon icon={faHistory} className="text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Lịch Sử Duyệt</h3>
          <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
            {filteredHistory.length}
          </span>
        </div>
        
        {/* Filter */}
        <div className="flex items-center space-x-2">
          <FontAwesomeIcon icon={faFilter} className="text-gray-400 text-sm" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tất cả</option>
            <option value="approved">Đã duyệt</option>
            <option value="rejected">Từ chối</option>
            <option value="requires_update">Cần cập nhật</option>
            <option value="pending">Chờ duyệt</option>
          </select>
        </div>
      </div>

      {/* History List */}
      {filteredHistory.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <FontAwesomeIcon icon={faHistory} className="text-4xl mb-2 opacity-50" />
          <p>Chưa có lịch sử duyệt nào</p>
        </div>
      ) : (
        <div className="space-y-3">
          {displayedHistory.map((item, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(item.newStatus)}
                      <span className={`text-sm font-medium px-2 py-1 rounded-full border ${
                        getStatusColor(item.newStatus)
                      }`}>
                        {getStatusText(item.newStatus)}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {getSectionName(item.section)}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Admin:</span> {item.adminId || 'Hệ thống'}
                    <span className="mx-2">•</span>
                    <span>{formatDate(item.timestamp)}</span>
                  </div>
                  
                  {item.reason && (
                    <div className="text-sm text-gray-700 bg-gray-50 rounded-md p-2 mt-2">
                      <span className="font-medium">Lý do:</span> {item.reason}
                    </div>
                  )}
                  
                  {item.oldStatus && (
                    <div className="text-xs text-gray-500 mt-2">
                      Thay đổi từ: <span className="font-medium">{getStatusText(item.oldStatus)}</span>
                      {' → '}
                      <span className="font-medium">{getStatusText(item.newStatus)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {/* Show More/Less Button */}
          {filteredHistory.length > 5 && (
            <button
              onClick={() => setExpanded(!expanded)}
              className="w-full py-2 text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center space-x-1 border-t border-gray-200 mt-4 pt-4"
            >
              <span>{expanded ? 'Thu gọn' : `Xem thêm ${filteredHistory.length - 5} mục`}</span>
              <FontAwesomeIcon 
                icon={expanded ? faChevronUp : faChevronDown} 
                className="text-xs" 
              />
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ApprovalHistory;