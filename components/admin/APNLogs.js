import React, { useState, useEffect } from 'react';
import { FaCircle, FaCheck, FaTimes, FaSpinner, FaSearch, Fa<PERSON><PERSON>er, FaTimesCircle } from 'react-icons/fa';

const APNLogs = ({ channelId }) => {
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedLog, setSelectedLog] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: 'all',
    search: ''
  });
  
  useEffect(() => {
    fetchAPNLogs();
  }, [channelId]);
  
  const fetchAPNLogs = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Determine payment method filter based on channel
      const paymentMethod = channelId?.includes('familymart') ? 'familymart' : 
                          channelId?.includes('7-eleven') ? '7-eleven' : 'all';
      
      // Build query parameters
      const params = new URLSearchParams({
        paymentMethod,
        limit: '50', // Get more recent logs
        offset: '0',
        includeStats: 'true'
      });
      
      // Add date filters if needed (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      params.append('dateFrom', thirtyDaysAgo.toISOString());
      
      // Fetch real payment callback logs from the new API
      const response = await fetch(`/api/admin/payment-callbacks?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch payment callbacks');
      }
      
      // Transform the data to match the component's expected format
      const transformedLogs = result.data.callbacks.map(callback => {
        // Map payment statuses to UI-friendly statuses
        let status = 'pending';
        if (callback.responseStatus === 200) {
          status = 'success';
        } else if (callback.responseStatus >= 400) {
          status = 'failed';
        }
        
        // Handle 7-Eleven APN status codes
        if (callback.paymentMethod === '7-eleven' && callback.requestPayload?.status) {
          switch (callback.requestPayload.status) {
            case 'B': // Paid
              status = 'success';
              break;
            case 'A': // Waiting
              status = 'pending';
              break;
            case 'D': // Expired
              status = 'failed';
              break;
          }
        }
        
        // Handle FamilyMart status codes
        if (callback.paymentMethod === 'familymart' && callback.requestPayload?.Status) {
          switch (callback.requestPayload.Status) {
            case '0': // Success
              status = 'success';
              break;
            case '1': // Pending
              status = 'pending';
              break;
            case '2': // Expired
            case '3': // Cancelled
              status = 'failed';
              break;
          }
        }
        
        return {
          id: `${callback.orderId}-${callback.timestamp}`,
          timestamp: callback.timestamp,
          orderId: callback.orderId,
          transactionId: callback.requestPayload?.trans_id || 
                        callback.requestPayload?.MerchantTradeNo || 
                        callback.orderId,
          amount: callback.orderAmount || callback.requestPayload?.amount || 0,
          status,
          paymentMethod: callback.paymentMethod,
          request: {
            method: 'POST',
            url: callback.callbackType === 'apn' ? '/api/payment/apn-callback' : '/api/payment/familymart-callback',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': callback.userAgent || 'Payment-Gateway/1.0'
            },
            body: callback.requestPayload
          },
          response: {
            statusCode: callback.responseStatus,
            body: callback.responseBody
          },
          processingTime: callback.processingTimeMs || 0,
          clientIP: callback.clientIP,
          orderFound: callback.orderFound
        };
      });
      
      setLogs(transformedLogs);
    } catch (err) {
      console.error('Error fetching payment callback logs:', err);
      setError(`Failed to load callback logs: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <FaCheck className="text-green-500" />;
      case 'failed':
        return <FaTimes className="text-red-500" />;
      case 'pending':
        return <FaCircle className="text-yellow-500 text-xs" />;
      default:
        return <FaCircle className="text-gray-500 text-xs" />;
    }
  };
  
  const getStatusClass = (status) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleFilterChange = (name, value) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSearchChange = (e) => {
    setFilters(prev => ({
      ...prev,
      search: e.target.value
    }));
  };
  
  const filteredLogs = logs.filter(log => {
    // Filter by status
    if (filters.status !== 'all' && log.status !== filters.status) {
      return false;
    }
    
    // Filter by date range
    if (filters.dateRange !== 'all') {
      const logDate = new Date(log.timestamp);
      const now = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          if (logDate < today) return false;
          break;
        case 'yesterday':
          const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
          const dayBefore = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 2);
          if (logDate < dayBefore || logDate >= yesterday) return false;
          break;
        case 'week':
          const weekAgo = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
          if (logDate < weekAgo) return false;
          break;
        default:
          break;
      }
    }
    
    // Filter by search term
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return (
        log.orderId.toLowerCase().includes(searchLower) ||
        log.transactionId.toLowerCase().includes(searchLower)
      );
    }
    
    return true;
  });
  
  // Add function to handle opening log details
  const handleViewDetails = (log) => {
    setSelectedLog(log);
    setShowDetailsModal(true);
  };
  
  // Add Modal component for displaying details
  const DetailsModal = ({ log, onClose }) => {
    if (!log) return null;
    
    return (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
          <div className="flex justify-between items-center border-b pb-3 mb-4">
            <h3 className="text-lg font-medium text-gray-900">Chi tiết callback thanh toán</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
              <FaTimesCircle size={24} />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-md font-medium text-gray-700 mb-2">Thông tin chung</h4>
              <div className="bg-gray-50 p-3 rounded">
                <p className="mb-2"><span className="font-medium">ID giao dịch:</span> {log.transactionId}</p>
                <p className="mb-2"><span className="font-medium">ID đơn hàng:</span> {log.orderId}</p>
                <p className="mb-2"><span className="font-medium">Số tiền:</span> {log.amount.toLocaleString('vi-VN')}đ</p>
                <p className="mb-2"><span className="font-medium">Thời gian:</span> {new Date(log.timestamp).toLocaleString('vi-VN')}</p>
                <p className="mb-2">
                  <span className="font-medium">Trạng thái:</span> 
                  <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(log.status)}`}>
                    {log.status === 'success' ? 'Thành công' : log.status === 'pending' ? 'Đang xử lý' : 'Thất bại'}
                  </span>
                </p>
                <p className="mb-2"><span className="font-medium">Thời gian xử lý:</span> {log.processingTime}ms</p>
              </div>
            </div>
            
            <div>
              <h4 className="text-md font-medium text-gray-700 mb-2">Thông tin yêu cầu</h4>
              <div className="bg-gray-50 p-3 rounded">
                <p className="mb-2"><span className="font-medium">Phương thức:</span> {log.request.method}</p>
                <p className="mb-2"><span className="font-medium">URL:</span> {log.request.url}</p>
                <p className="mb-2"><span className="font-medium">Headers:</span></p>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(log.request.headers, null, 2)}
                </pre>
              </div>
            </div>
          </div>
          
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-md font-medium text-gray-700 mb-2">Nội dung yêu cầu</h4>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto h-40">
                {JSON.stringify(log.request.body, null, 2)}
              </pre>
            </div>
            
            <div>
              <h4 className="text-md font-medium text-gray-700 mb-2">Phản hồi từ máy chủ</h4>
              <div className="bg-gray-50 p-3 rounded">
                <p className="mb-2">
                  <span className="font-medium">Mã trạng thái:</span> 
                  <span className={`ml-2 ${log.response.statusCode >= 200 && log.response.statusCode < 300 ? 'text-green-600' : 'text-red-600'}`}>
                    {log.response.statusCode}
                  </span>
                </p>
                <p className="mb-2"><span className="font-medium">Nội dung phản hồi:</span></p>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto h-32">
                  {JSON.stringify(log.response.body, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <FaSpinner className="animate-spin text-blue-500 text-xl mr-2" />
        <span>Đang tải nhật ký cuộc gọi...</span>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <div className="flex items-center">
          <FaTimes className="mr-2" />
          <span>{error}</span>
        </div>
        <button 
          onClick={fetchAPNLogs}
          className="mt-2 text-sm text-red-700 hover:text-red-900 underline"
        >
          Thử lại
        </button>
      </div>
    );
  }
  
  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Nhật ký callback thanh toán</h3>
        
        {/* Filters */}
        <div className="flex flex-wrap gap-4 mb-4">
          <div className="flex items-center">
            <label htmlFor="status-filter" className="mr-2 text-sm text-gray-700">Trạng thái:</label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
            >
              <option value="all">Tất cả</option>
              <option value="success">Thành công</option>
              <option value="pending">Đang xử lý</option>
              <option value="failed">Thất bại</option>
            </select>
          </div>
          
          <div className="flex items-center">
            <label htmlFor="date-filter" className="mr-2 text-sm text-gray-700">Thời gian:</label>
            <select
              id="date-filter"
              value={filters.dateRange}
              onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
            >
              <option value="all">Tất cả</option>
              <option value="today">Hôm nay</option>
              <option value="yesterday">Hôm qua</option>
              <option value="week">7 ngày qua</option>
            </select>
          </div>
          
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Tìm kiếm theo ID đơn hàng hoặc ID giao dịch"
              value={filters.search}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
            />
          </div>
        </div>
        
        {/* Results count */}
        <div className="text-sm text-gray-600">
          Hiển thị {filteredLogs.length} trong số {logs.length} nhật ký
        </div>
      </div>
      
      {/* Logs Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Thời gian
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phương thức
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                ID đơn hàng
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                ID giao dịch
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Số tiền
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trạng thái
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Thời gian xử lý
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phản hồi
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredLogs.length > 0 ? (
              filteredLogs.map(log => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(log.timestamp).toLocaleString('vi-VN')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      log.paymentMethod === '7-eleven' ? 'bg-orange-100 text-orange-800' :
                      log.paymentMethod === 'familymart' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {log.paymentMethod === '7-eleven' ? '7-Eleven' :
                       log.paymentMethod === 'familymart' ? 'FamilyMart' :
                       log.paymentMethod || 'Unknown'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {log.orderId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.transactionId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.amount.toLocaleString('vi-VN')}đ
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(log.status)}
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(log.status)}`}>
                        {log.status === 'success' ? 'Thành công' : log.status === 'pending' ? 'Đang xử lý' : 'Thất bại'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.processingTime}ms
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.response.statusCode}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                      onClick={() => handleViewDetails(log)}
                      className="text-blue-600 hover:text-blue-900">
                      Chi tiết
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="9" className="px-6 py-4 text-center text-sm text-gray-500">
                  Không tìm thấy nhật ký phù hợp với bộ lọc
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      {/* Details Modal */}
      {showDetailsModal && selectedLog && (
        <DetailsModal log={selectedLog} onClose={() => setShowDetailsModal(false)} />
      )}
    </div>
  );
};

export default APNLogs; 