import React, { useState } from 'react';
import {
  FaBox,
  FaShoppingCart,
  FaFileAlt,
  FaChartBar,
  FaSignOutAlt,
  FaSpinner,
  FaUsers,
  FaCreditCard,
  FaHistory,
  FaMobile,
  FaUserShield
} from 'react-icons/fa';

import DashboardSection from './DashboardSection';
import DashboardStatsCards from './DashboardStatsCards';
import EnhancedInventory from './EnhancedInventory';
import InventoryDashboardModule from './InventoryDashboardModule';
import Orders from '../customer/Orders';
import OrderCard from '../customer/OrderCard';
import CustomersList from './CustomersList';
import CustomerDetails from './CustomerDetails';
import PaymentChannels from './PaymentChannels';
import DocumentApproval from './DocumentApproval';
import APNCallbackHistory from './APNCallbackHistory';
import IFTopup from './IFTopup';
import CustomerApproval from './CustomerApproval';

import { useAdminData } from '../../hooks/useAdminData';
import { handlePrintOrder } from '../../utils/adminOrderUtils';
import { 
  formatDate, 
  formatCurrency, 
  getStatusColor, 
  getPaymentStatusColor, 
  getStatusIcon, 
  getStatusText, 
  getPaymentStatusText, 
  getTimeRemaining 
} from '../../utils/formatUtils';
import { calculateOrderTotal } from '../../utils/adminOrderUtils';
import { DEFAULT_STORE_INFO } from '../../constants/adminConstants';

// Admin Dashboard component
const AdminDashboard = ({ signOut, initialSection }) => {
  const { currentOrders, stats, loading } = useAdminData();
  const [storeInfo, setStoreInfo] = useState(DEFAULT_STORE_INFO);
  const [isEditingStore, setIsEditingStore] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState(null);
  const [activeSection, setActiveSection] = useState(initialSection || 'overview');

  // Store Management Functions
  const handleEditStoreInfo = () => {
    setIsEditingStore(true);
  };

  const handleSaveStoreInfo = async () => {
    try {
      // In a real app, you would call an API to save the store info
      console.log('Saving store info:', storeInfo);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update UI
      setIsEditingStore(false);
      alert('Thông tin cửa hàng đã được cập nhật thành công!');
    } catch (error) {
      console.error('Error saving store info:', error);
      alert('Lỗi khi cập nhật thông tin cửa hàng.');
    }
  };

  const handleStoreInfoChange = (e) => {
    const { name, value } = e.target;
    setStoreInfo(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Function to render specific sections based on activeSection
  const renderSection = () => {
    switch(activeSection) {
      case 'if-topup':
        return <IFTopup />;
      case 'ok-topup':
        return (
          <div className="bg-gray-100 p-6 rounded-lg text-center">
            <p className="text-gray-600">OK Top-up functionality will be implemented here</p>
          </div>
        );
      case 'orders':
        return (
          <Orders
            isAdminMode={true}
            allCustomers={true}
            initialFilters={{
              status: 'all',
              time: 'all_time'
            }}
            onCustomerSelect={(customerId) => {
              console.log("Selected customer:", customerId);
            }}
          />
        );
      case 'customers':
        return (
          selectedCustomerId ? (
            <CustomerDetails 
              customerId={selectedCustomerId}
              onBack={() => setSelectedCustomerId(null)}
            />
          ) : (
            <CustomersList 
              onSelectCustomer={(customerId) => setSelectedCustomerId(customerId)}
            />
          )
        );
      case 'payments':
        return <PaymentChannels />;
      case 'analytics':
        return (
          <div className="bg-gray-100 p-6 rounded-lg text-center">
            <p className="text-gray-600">Biểu đồ thống kê sẽ hiển thị ở đây</p>
          </div>
        );
      case 'documents':
        return <DocumentApproval DashboardSection={DashboardSection} />;
      case 'history':
        return <APNCallbackHistory />;
      case 'inventory':
        return <EnhancedInventory hideHeader={true} />;
      default:
        return renderOverview();
    }
  };

  const renderOverview = () => (
    <div>
      {/* Enhanced Stats Overview */}
      <DashboardStatsCards stats={stats} loading={loading} />

      {/* Inventory Dashboard Module */}
      <div className="mb-6">
        <InventoryDashboardModule 
          onViewDetails={() => {
            const currentPath = window.location.pathname;
            const storeName = currentPath.split('/')[1];
            window.location.href = `/${storeName}/inventory-management`;
          }}
        />
      </div>

      {/* Expandable Sections */}
      <DashboardSection 
        title="QUẢN LÝ KHO CHI TIẾT" 
        icon={<FaBox className="text-blue-600" />}
      >
        <EnhancedInventory hideHeader={true} />
      </DashboardSection>

      <DashboardSection 
        title="Kênh thanh toán aaa" 
        icon={<FaCreditCard className="text-purple-600" />}
      >
        <PaymentChannels />
      </DashboardSection>

      <DashboardSection 
        title="Khách hàng" 
        icon={<FaUsers className="text-indigo-600" />}
      >
        {selectedCustomerId ? (
          <CustomerDetails 
            customerId={selectedCustomerId}
            onBack={() => setSelectedCustomerId(null)}
          />
        ) : (
          <CustomersList 
            onSelectCustomer={(customerId) => setSelectedCustomerId(customerId)}
          />
        )}
      </DashboardSection>

      <DashboardSection
        title="Duyệt thông tin khách hàng"
        icon={<FaUserShield className="text-orange-600" />}
      >
        <CustomerApproval />
      </DashboardSection>

      <DashboardSection
        title="Đơn hàng gần đây"
        icon={<FaShoppingCart className="text-green-600" />}
      >
        {loading.orders ? (
          <div className="flex justify-center items-center p-8">
            <FaSpinner className="animate-spin text-blue-500 mr-2" />
            <span>Đang tải đơn hàng...</span>
          </div>
        ) : (
          <div>
            {/* Quick Overview */}
            <div className="mb-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-green-100 p-3 rounded-lg text-center">
                  <h4 className="font-medium text-green-800">Đã thanh toán</h4>
                  <p className="text-xl font-bold text-green-900">{stats.paidOrders}</p>
                </div>
                <div className="bg-yellow-100 p-3 rounded-lg text-center">
                  <h4 className="font-medium text-yellow-800">Chưa thanh toán</h4>
                  <p className="text-xl font-bold text-yellow-900">{stats.unpaidOrders}</p>
                </div>
                <div className="bg-red-100 p-3 rounded-lg text-center">
                  <h4 className="font-medium text-red-800">Đã hết hạn</h4>
                  <p className="text-xl font-bold text-red-900">{stats.expiredOrders}</p>
                </div>
              </div>
            </div>

            {/* Recent Orders */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900 mb-2">5 đơn hàng mới nhất:</h3>
              {currentOrders.slice(0, 5).map(order => (
                <OrderCard
                  key={order.id}
                  order={order}
                  isAdminMode={true}
                  storeParam="admin"
                  onPrintOrder={handlePrintOrder}
                  formatDate={formatDate}
                  formatCurrency={formatCurrency}
                  getStatusColor={getStatusColor}
                  getPaymentStatusColor={getPaymentStatusColor}
                  getStatusIcon={getStatusIcon}
                  getStatusText={getStatusText}
                  getPaymentStatusText={getPaymentStatusText}
                  getTimeRemaining={getTimeRemaining}
                  calculateOrderTotal={calculateOrderTotal}
                />
              ))}

              {currentOrders.length > 5 && (
                <div className="text-center pt-4">
                  <p className="text-sm text-gray-500 mb-2">
                    Hiển thị 5 trong tổng số {currentOrders.length} đơn hàng
                  </p>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Xem tất cả đơn hàng →
                  </button>
                </div>
              )}

              {currentOrders.length === 0 && (
                <div className="text-center py-8">
                  <FaShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Chưa có đơn hàng nào</p>
                </div>
              )}
            </div>
          </div>
        )}
      </DashboardSection>

      <DashboardSection
        title="Tất cả đơn hàng"
        icon={<FaFileAlt className="text-blue-600" />}
      >
        <Orders
          isAdminMode={true}
          allCustomers={true}
          initialFilters={{
            status: 'all',
            time: 'all_time'
          }}
          onCustomerSelect={(customerId) => {
            console.log("Selected customer:", customerId);
            // Optionally do something with the selected customer
          }}
        />
      </DashboardSection>

      {/* Document Approval Section */}
      <DocumentApproval DashboardSection={DashboardSection} />

      <DashboardSection
        title="Lịch sử APN Callback"
        icon={<FaHistory className="text-indigo-600" />}
        defaultOpen={false}
      >
        <APNCallbackHistory />
      </DashboardSection>

      <DashboardSection
        title="IF Number Topup"
        icon={<FaMobile className="text-green-600" />}
      >
        <IFTopup />
      </DashboardSection>

      <DashboardSection
        title="Thống kê & Biểu đồ"
        icon={<FaChartBar className="text-purple-600" />}
      >
        <div className="bg-gray-100 p-6 rounded-lg text-center">
          <p className="text-gray-600">Biểu đồ thống kê sẽ hiển thị ở đây</p>
        </div>
      </DashboardSection>
    </div>
  );

  const navigationItems = [
    { id: 'overview', title: 'Tổng quan', icon: <FaChartBar /> },
    { id: 'if-topup', title: 'IF Top-up', icon: <FaMobile /> },
    { id: 'ok-topup', title: 'OK Top-up', icon: <FaCreditCard /> },
    { id: 'orders', title: 'Đơn hàng', icon: <FaShoppingCart /> },
    { id: 'customers', title: 'Khách hàng', icon: <FaUsers /> },
    { id: 'payments', title: 'Thanh toán', icon: <FaCreditCard /> },
    { id: 'inventory', title: 'Kho hàng', icon: <FaBox /> },
    { id: 'documents', title: 'Duyệt tài liệu', icon: <FaFileAlt /> },
    { id: 'history', title: 'Lịch sử', icon: <FaHistory /> },
    { id: 'analytics', title: 'Thống kê', icon: <FaChartBar /> },
  ];

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar Navigation */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-800">Admin Panel</h2>
        </div>
        <nav className="mt-4">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveSection(item.id)}
              className={`w-full flex items-center px-4 py-3 text-left hover:bg-gray-100 transition-colors ${
                (activeSection === item.id || (!activeSection && item.id === 'overview'))
                  ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600'
                  : 'text-gray-700'
              }`}
            >
              <span className="mr-3">{item.icon}</span>
              {item.title}
            </button>
          ))}
        </nav>
        <div className="absolute bottom-4 left-4 right-4">
          <button 
            onClick={signOut}
            className="w-full flex items-center justify-center px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
          >
            <FaSignOutAlt className="mr-2" />
            Đăng xuất
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-800">
              {activeSection === 'overview' || !activeSection ? 'Bảng điều khiển quản trị' : 
               activeSection === 'if-topup' ? 'IF Top-up Management' :
               activeSection === 'ok-topup' ? 'OK Top-up Management' :
               activeSection === 'orders' ? 'Orders Management' :
               activeSection === 'customers' ? 'Customer Management' :
               activeSection === 'payments' ? 'Payment Channels' :
               activeSection === 'analytics' ? 'Analytics & Reports' :
               activeSection === 'documents' ? 'Document Approval' :
               activeSection === 'history' ? 'Transaction History' :
               activeSection === 'inventory' ? 'Inventory Management' :
               'Admin Dashboard'}
            </h1>
          </div>

          {/* Render specific section or overview */}
          {activeSection && activeSection !== 'overview' ? renderSection() : renderOverview()}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
