import React, { useState, useEffect } from 'react';
import { FaArrowLeft, FaUserCircle, FaPhone, FaEnvelope, FaMapMarkerAlt, FaCalendarAlt, FaEdit, FaSpinner, FaIdCard, FaPassport, FaCar, FaHome, FaExclamationTriangle, FaShoppingBag, FaChevronRight, FaFileAlt, FaUpload, FaTimes, FaCheck, FaCreditCard, FaGift, FaStar } from 'react-icons/fa';
import Orders from '../customer/Orders';

const CustomerDetails = ({ customerId, onBack }) => {
  const [customer, setCustomer] = useState(null);
  const [customerOrders, setCustomerOrders] = useState([]);
  const [loyaltyData, setLoyaltyData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingOrders, setLoadingOrders] = useState(true);
  const [loadingLoyalty, setLoadingLoyalty] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({});
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadForm, setUploadForm] = useState({
    documentType: '',
    file: null,
    description: ''
  });
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  // Fetch loyalty data
  const fetchLoyaltyData = async () => {
    try {
      setLoadingLoyalty(true);
      const response = await fetch('/api/admin/loyalty-points');
      if (response.ok) {
        const data = await response.json();
        setLoyaltyData(data.customers[customerId] || {
          totalPoints: 0,
          availablePoints: 0,
          tier: 'Bronze',
          totalSpent: 0,
          transactions: []
        });
      }
    } catch (err) {
      console.error('Error fetching loyalty data:', err);
    } finally {
      setLoadingLoyalty(false);
    }
  };

  // Fetch customer data
  useEffect(() => {
    fetchLoyaltyData();
    const fetchCustomerDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Fetching customer data for ID:', customerId);
        
        // Fetch customer from API endpoint
        const response = await fetch(`/api/customers/${customerId}`);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`Failed to fetch customer: ${response.status} ${errorData.error || ''}`);
        }
        
        const customerData = await response.json();
        console.log('Successfully loaded customer data:', customerData);
        
        if (customerData) {
          // Format customer data for the UI
          const formattedCustomer = {
            id: customerData.id,
            fullName: customerData.personalDetails?.name || customerData.name || 
                     `${customerData.personalDetails?.firstName || ''} ${customerData.personalDetails?.middleName || ''} ${customerData.personalDetails?.lastName || ''}`.trim(),
            email: customerData.contactInfo?.email || customerData.email || '',
            phone: customerData.contactInfo?.primaryPhone || customerData.phone || '',
            secondaryPhone: customerData.contactInfo?.secondaryPhone || '',
            workPhone: customerData.contactInfo?.workPhone || '',
            registrationDate: customerData.membershipInfo?.memberSince || customerData.createdAt || new Date().toISOString(),
            status: customerData.accessControl?.accountStatus || customerData.status || 'active',
            addresses: customerData.addresses || [],
            primaryAddress: customerData.addresses && customerData.addresses.length > 0 
              ? `${customerData.addresses[0].street}, ${customerData.addresses[0].district}, ${customerData.addresses[0].city}` 
              : customerData.address || '',
            documents: customerData.documents || [],
            personalDetails: customerData.personalDetails || {},
            membershipInfo: customerData.membershipInfo || {},
            accessControl: customerData.accessControl || {},
            lastLogin: customerData.accessControl?.lastLogin || '',
            financialInfo: customerData.financialInfo || {},
            notes: customerData.notes || '',
            profileImage: customerData.profileImage || '',
            totalOrders: 0, // This will be updated when orders are fetched
            totalSpent: customerData.membershipInfo?.loyaltyPoints || 0,
            // Include the original data for access to all fields
            rawData: customerData
          };
          
          setCustomer(formattedCustomer);
          setEditForm({
            fullName: formattedCustomer.fullName,
            email: formattedCustomer.email,
            phone: formattedCustomer.phone,
            status: formattedCustomer.status,
            address: formattedCustomer.primaryAddress,
            notes: formattedCustomer.notes
          });
          setLoading(false);
        } else {
          console.error('Customer data is empty for ID:', customerId);
          setError(`Không tìm thấy khách hàng với ID: ${customerId}`);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error fetching customer details:', error);
        setError(`Lỗi khi tải dữ liệu khách hàng: ${error.message}`);
        setLoading(false);
      }
    };

    fetchCustomerDetails();
  }, [customerId]);

  // Fetch orders for this customer
  useEffect(() => {
    const fetchCustomerOrders = async () => {
      if (!customerId) return;
      
      try {
        setLoadingOrders(true);
        console.log('Fetching orders for customer ID:', customerId);
        
        // Fetch orders from the API endpoint
        const response = await fetch(`/api/orders/customer/${customerId}`);
        
        if (!response.ok) {
          console.error(`Failed to fetch orders: ${response.status}`);
          setCustomerOrders([]);
          setLoadingOrders(false);
          return;
        }
        
        const data = await response.json();
        console.log(`Found ${data.total} orders for customer ${customerId}:`, data.orders);
        
        if (data.success && data.orders && data.orders.length > 0) {
          setCustomerOrders(data.orders);
          
          // Update customer statistics based on orders
          const totalSpent = data.orders.reduce((sum, order) => {
            const orderTotal = order.totalAmount || order.amount || 0;
            return sum + orderTotal;
          }, 0);
          
          setCustomer(prev => prev ? {
            ...prev,
            totalOrders: data.total,
            totalSpent: totalSpent
          } : prev);
        } else {
          console.log('No orders found for customer ID:', customerId);
          setCustomerOrders([]);
        }
      } catch (error) {
        console.error('Error fetching customer orders:', error);
        setCustomerOrders([]);
      } finally {
        setLoadingOrders(false);
      }
    };

    if (customerId) {
      fetchCustomerOrders();
    }
  }, [customerId]);

  const handleEditFormChange = (e) => {
    const { name, value } = e.target;
    setEditForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveChanges = async () => {
    try {
      // In a real app, you would call your API to update customer data
      console.log('Saving customer changes:', editForm);
      
      // Simulate API call
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update local state with the edited data
      setCustomer(prev => ({
        ...prev,
        fullName: editForm.fullName,
        email: editForm.email,
        phone: editForm.phone,
        status: editForm.status,
        primaryAddress: editForm.address,
        notes: editForm.notes
      }));
      setIsEditing(false);
      setLoading(false);
    } catch (error) {
      console.error('Error saving customer changes:', error);
      setError(`Lỗi khi lưu thông tin khách hàng: ${error.message}`);
      setLoading(false);
    }
  };

  const handleUploadFormChange = (e) => {
    const { name, value } = e.target;
    setUploadForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    setUploadForm(prev => ({
      ...prev,
      file: e.target.files[0]
    }));
  };

  const handleUploadDocument = async (e) => {
    e.preventDefault();
    
    if (!uploadForm.documentType || !uploadForm.file) {
      alert('Vui lòng chọn loại giấy tờ và tải lên file');
      return;
    }
    
    try {
      setUploading(true);
      
      // Create form data for file upload
      const formData = new FormData();
      formData.append('customerId', customerId);
      formData.append('documentType', uploadForm.documentType);
      formData.append('file', uploadForm.file); // This field name must be 'file' as expected by the API
      if (uploadForm.description) {
        formData.append('description', uploadForm.description);
      }
      
      console.log('Uploading document:', uploadForm.documentType);
      
      // 1. UPLOAD THE ACTUAL FILE TO SERVER USING THE EXISTING API
      let uploadedDocument = null;
      try {
        // Send the file to the existing document upload API endpoint
        const uploadResponse = await fetch('/api/documents/upload', {
          method: 'POST',
          body: formData, // Contains the file and metadata
          // No need to set Content-Type header when sending FormData
        });
        
        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json().catch(() => ({}));
          throw new Error(`Failed to upload file: ${uploadResponse.status} ${errorData.error || ''}`);
        }
        
        // Get the uploaded document info from the response
        const uploadResult = await uploadResponse.json();
        if (!uploadResult.success) {
          throw new Error('Upload failed: ' + (uploadResult.error || 'Unknown error'));
        }
        
        uploadedDocument = uploadResult.document;
        console.log('File uploaded successfully:', uploadedDocument);
        
        // No need to update customers.json manually as the API already does this
        
      } catch (uploadError) {
        console.error('Error uploading file:', uploadError);
        throw new Error(`Lỗi khi tải file lên server: ${uploadError.message}`);
      }
      
      // If successful
      console.log('Document uploaded successfully');
      setUploadSuccess(true);
      
      // Reset form after delay
      setTimeout(() => {
        setUploadSuccess(false);
        setShowUploadModal(false);
        setUploadForm({
          documentType: '',
          file: null,
          description: ''
        });
        
        // Fetch updated customer data to refresh the UI
        const fetchUpdatedCustomer = async () => {
          try {
            const response = await fetch(`/api/customers/${customerId}`);
            if (response.ok) {
              const updatedCustomerData = await response.json();
              // Format customer data for the UI, same as in the initial fetch
              const formattedCustomer = {
                ...customer,
                documents: updatedCustomerData.documents || [],
                // Keep the rest of the formatted data the same
              };
              setCustomer(formattedCustomer);
            }
          } catch (error) {
            console.error('Error fetching updated customer data:', error);
          }
        };
        
        fetchUpdatedCustomer();
      }, 2000);
    } catch (error) {
      console.error('Error uploading document:', error);
      alert('Lỗi khi tải lên giấy tờ: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  // If there's an error, show error state
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <button 
            onClick={onBack}
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <FaArrowLeft />
          </button>
          <h2 className="text-xl font-semibold">Thông tin khách hàng</h2>
        </div>
        <div className="bg-red-50 p-4 rounded-md flex items-start">
          <FaExclamationTriangle className="text-red-500 mt-1 mr-3" />
          <div>
            <h3 className="text-red-800 font-medium">Đã xảy ra lỗi</h3>
            <p className="text-red-700 mt-1">{error}</p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Tải lại trang
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If loading or customer is null, show loading state
  if (loading || !customer) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-6">
          <button 
            onClick={onBack}
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <FaArrowLeft />
          </button>
          <h2 className="text-xl font-semibold">Thông tin khách hàng</h2>
        </div>
        <div className="flex flex-col items-center justify-center h-64">
          <FaSpinner className="animate-spin text-blue-500 mr-2 text-3xl mb-4" />
          <span className="text-gray-600">Đang tải thông tin khách hàng...</span>
          <p className="text-gray-500 text-sm mt-2">ID: {customerId}</p>
        </div>
      </div>
    );
  }

  // Group documents by type for better organization
  const documentsByType = customer?.documents?.reduce((acc, doc) => {
    const type = doc.associatedWith || doc.documentType;
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(doc);
    return acc;
  }, {}) || {};

  // Function to render recent orders
  const renderRecentOrders = () => {
    if (loadingOrders) {
      return (
        <div className="bg-white p-4 rounded-lg border border-gray-200 text-center">
          <FaSpinner className="animate-spin text-blue-500 inline mr-2" />
          <span className="text-gray-500">Đang tải đơn hàng...</span>
        </div>
      );
    }
    
    if (customerOrders.length === 0) {
      return (
        <div className="bg-white p-4 rounded-lg border border-gray-200 text-center text-gray-500">
          Khách hàng chưa có đơn hàng nào
        </div>
      );
    }
    
    // Show the 3 most recent orders
    const recentOrders = [...customerOrders]
      .sort((a, b) => new Date(b.orderDate || b.createdAt) - new Date(a.orderDate || a.createdAt))
      .slice(0, 3);
      
    return (
      <div className="space-y-3">
        {recentOrders.map((order, index) => (
          <div key={order.id || index} className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">Đơn hàng #{order.id || `ORDER-${index+1}`}</h4>
                <p className="text-sm text-gray-500">
                  Ngày đặt: {new Date(order.orderDate || order.createdAt).toLocaleDateString('vi-VN')}
                </p>
                <p className="text-sm font-medium mt-1">
                  {(order.totalAmount || 0).toLocaleString('vi-VN')}đ
                </p>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                order.status === 'completed' ? 'bg-green-100 text-green-800' :
                order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {order.status === 'completed' ? 'Hoàn thành' :
                 order.status === 'processing' ? 'Đang xử lý' : 'Chờ xác nhận'}
              </span>
            </div>
            {order.items && order.items.length > 0 && (
              <div className="mt-2 text-sm text-gray-600">
                <p>Sản phẩm: {order.items.map(item => `${item.name} (${item.quantity})`).join(', ')}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header with back button - Mobile-friendly */}
      <div className="p-3 sm:p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center min-w-0 flex-1">
            <button 
              onClick={onBack}
              className="mr-2 sm:mr-4 text-gray-500 hover:text-gray-700 flex-shrink-0"
            >
              <FaArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
            <h2 className="text-lg sm:text-xl font-semibold truncate">Thông tin khách hàng</h2>
          </div>
          
          {activeTab === 'overview' && !isEditing && (
            <button 
              onClick={() => setIsEditing(true)}
              className="flex items-center px-2 sm:px-3 py-1 bg-blue-50 text-blue-700 rounded hover:bg-blue-100 text-sm flex-shrink-0"
            >
              <FaEdit className="mr-1 h-3 w-3" />
              <span className="hidden sm:inline">Chỉnh sửa</span>
              <span className="sm:hidden">Sửa</span>
            </button>
          )}
        </div>
      </div>
      
      {/* Tabs - Mobile-friendly */}
      <div className="border-b">
        <nav className="flex -mb-px overflow-x-auto">
          <button
            className={`py-3 sm:py-4 px-3 sm:px-6 text-center border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            Tổng quan
          </button>
          <button
            className={`py-3 sm:py-4 px-3 sm:px-6 text-center border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${
              activeTab === 'orders'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('orders')}
          >
            Đơn hàng
          </button>
          <button
            className={`py-3 sm:py-4 px-3 sm:px-6 text-center border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${
              activeTab === 'documents'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('documents')}
          >
            Giấy tờ
          </button>
          <button
            className={`py-3 sm:py-4 px-3 sm:px-6 text-center border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap ${
              activeTab === 'personal'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('personal')}
          >
            Thông tin cá nhân
          </button>
        </nav>
      </div>
      
      {/* Tab content - Mobile-friendly padding */}
      <div className="p-3 sm:p-6">
        {activeTab === 'overview' && (
          <>
            {isEditing ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Họ và tên</label>
                    <input
                      type="text"
                      name="fullName"
                      value={editForm.fullName}
                      onChange={handleEditFormChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={editForm.email}
                      onChange={handleEditFormChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Số điện thoại</label>
                    <input
                      type="text"
                      name="phone"
                      value={editForm.phone}
                      onChange={handleEditFormChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                    <select
                      name="status"
                      value={editForm.status}
                      onChange={handleEditFormChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="active">Đang hoạt động</option>
                      <option value="inactive">Không hoạt động</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Địa chỉ</label>
                    <input
                      type="text"
                      name="address"
                      value={editForm.address}
                      onChange={handleEditFormChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      name="notes"
                      value={editForm.notes}
                      onChange={handleEditFormChange}
                      rows="3"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    ></textarea>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => {
                      setIsEditing(false);
                      setEditForm({
                        fullName: customer.fullName,
                        email: customer.email,
                        phone: customer.phone,
                        status: customer.status,
                        address: customer.primaryAddress,
                        notes: customer.notes
                      });
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Hủy
                  </button>
                  <button
                    onClick={handleSaveChanges}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    disabled={loading}
                  >
                    {loading ? <FaSpinner className="animate-spin" /> : 'Lưu thay đổi'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1">
                  <div className="flex flex-col items-center p-6 bg-gray-50 rounded-lg">
                    {customer && customer.profileImage ? (
                      <img 
                        src={customer.profileImage} 
                        alt={customer.fullName} 
                        className="w-24 h-24 rounded-full object-cover mb-4"
                      />
                    ) : (
                      <FaUserCircle className="w-24 h-24 text-gray-400 mb-4" />
                    )}
                    <h3 className="text-lg font-medium text-gray-900">{customer.fullName}</h3>
                    <p className="text-gray-500">ID: {customer.id}</p>
                    <div className="mt-4 flex items-center">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        customer.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {customer.status === 'active' ? 'Đang hoạt động' : 'Không hoạt động'}
                      </span>
                    </div>
                    {customer.membershipInfo && customer.membershipInfo.membershipLevel && (
                      <div className="mt-2 flex items-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          customer.membershipInfo.membershipLevel === 'gold' ? 'bg-yellow-100 text-yellow-800' : 
                          customer.membershipInfo.membershipLevel === 'silver' ? 'bg-gray-100 text-gray-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          Thành viên {customer.membershipInfo.membershipLevel === 'gold' ? 'Vàng' : 
                                     customer.membershipInfo.membershipLevel === 'silver' ? 'Bạc' : 'Chuẩn'}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-6 bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Thông tin liên hệ</h3>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <FaEnvelope className="mt-1 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-700">Email</p>
                          <p className="text-sm text-gray-500">{customer.email}</p>
                          {customer.contactInfo && customer.contactInfo.alternateEmail && (
                            <p className="text-sm text-gray-500">{customer.contactInfo.alternateEmail} (Phụ)</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-start">
                        <FaPhone className="mt-1 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-700">Số điện thoại</p>
                          <p className="text-sm text-gray-500">{customer.phone} (Chính)</p>
                          {customer.secondaryPhone && (
                            <p className="text-sm text-gray-500">{customer.secondaryPhone} (Phụ)</p>
                          )}
                          {customer.workPhone && (
                            <p className="text-sm text-gray-500">{customer.workPhone} (Công việc)</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-start">
                        <FaMapMarkerAlt className="mt-1 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-700">Địa chỉ</p>
                          <p className="text-sm text-gray-500">{customer.primaryAddress}</p>
                          {customer.addresses && customer.addresses.length > 1 && (
                            <button 
                              className="text-xs text-blue-600 hover:underline mt-1"
                              onClick={() => setActiveTab('personal')}
                            >
                              Xem tất cả địa chỉ ({customer.addresses.length})
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="md:col-span-2">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Tổng quan</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <p className="text-sm text-gray-500">Tổng đơn hàng</p>
                        <div className="flex items-center">
                          <p className="text-2xl font-bold">{customer.totalOrders}</p>
                          {loadingOrders && <FaSpinner className="animate-spin text-blue-500 ml-2" />}
                        </div>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <p className="text-sm text-gray-500">Tổng chi tiêu</p>
                        <div className="flex items-center">
                          <p className="text-2xl font-bold">{customer.totalSpent.toLocaleString('vi-VN')}đ</p>
                          {loadingOrders && <FaSpinner className="animate-spin text-blue-500 ml-2" />}
                        </div>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <div className="flex items-center mb-2">
                          <FaGift className="text-purple-500 mr-2" />
                          <p className="text-sm text-gray-500">Điểm Loyalty</p>
                        </div>
                        <div className="flex items-center">
                          <p className="text-2xl font-bold text-purple-600">
                            {loyaltyData ? loyaltyData.availablePoints.toLocaleString() : '0'}
                          </p>
                          {loadingLoyalty && <FaSpinner className="animate-spin text-purple-500 ml-2" />}
                        </div>
                        {loyaltyData && (
                          <div className="mt-2">
                            <div className="flex items-center">
                              <FaStar className="text-yellow-500 mr-1 text-sm" />
                              <span className="text-sm text-gray-600">{loyaltyData.tier}</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              Tổng tích lũy: {loyaltyData.totalPoints.toLocaleString()}
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <p className="text-sm text-gray-500">Ngày đăng ký</p>
                        <p className="text-lg font-medium">
                          {new Date(customer.registrationDate).toLocaleDateString('vi-VN')}
                        </p>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <p className="text-sm text-gray-500">Đăng nhập gần nhất</p>
                        <p className="text-lg font-medium">
                          {customer.lastLogin ? 
                           new Date(customer.lastLogin).toLocaleString('vi-VN') : 
                           'Chưa có thông tin'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      <h4 className="font-medium mb-2">Ghi chú</h4>
                      <p className="text-gray-700">{customer.notes || 'Không có ghi chú.'}</p>
                    </div>
                  </div>
                  
                  <div className="mt-6 bg-gray-50 rounded-lg p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900">Thẻ nạp và dịch vụ đang hoạt động</h3>
                    </div>
                    
                    {(() => {
                      // Extract all active topup cards and services
                      const now = new Date();
                      const activeItems = [];
                      
                      customerOrders.forEach(order => {
                        if (order.status === 'paid' && order.paymentStatus === 'paid' && order.items) {
                          order.items.forEach(item => {
                            if (item.type === 'topup') {
                              // For topup cards, check if they have an expiry date and haven't expired
                              if (item.expiryDate) {
                                const expiryDate = new Date(item.expiryDate);
                                if (expiryDate > now) {
                                  activeItems.push({orderId: order.id, item, orderDate: order.createdAt});
                                }
                              } else {
                                // If no expiry date, consider it active if the order is recent (last 30 days)
                                const orderDate = new Date(order.createdAt);
                                const thirtyDaysAgo = new Date();
                                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                                if (orderDate > thirtyDaysAgo) {
                                  activeItems.push({orderId: order.id, item, orderDate: order.createdAt});
                                }
                              }
                            } else if (item.type === 'service') {
                              // For services, check if current date is between start and end date
                              if (item.serviceStartDate && item.serviceEndDate) {
                                const startDate = new Date(item.serviceStartDate);
                                const endDate = new Date(item.serviceEndDate);
                                if (now >= startDate && now <= endDate) {
                                  activeItems.push({orderId: order.id, item, orderDate: order.createdAt});
                                }
                              } else if (item.serviceStartDate) {
                                // If only start date, consider active if after start date
                                const startDate = new Date(item.serviceStartDate);
                                if (now >= startDate) {
                                  activeItems.push({orderId: order.id, item, orderDate: order.createdAt});
                                }
                              } else if (item.servicePeriod) {
                                // If service period exists but no dates, consider it active
                                activeItems.push({orderId: order.id, item, orderDate: order.createdAt});
                              }
                            } else if (item.type === 'sim') {
                              // SIM cards don't expire, so include them all
                              activeItems.push({orderId: order.id, item, orderDate: order.createdAt});
                            }
                          });
                        }
                      });
                      
                      if (activeItems.length === 0) {
                        return (
                          <div className="bg-white p-4 rounded-lg border border-gray-200 text-center text-gray-500">
                            Khách hàng chưa có thẻ nạp hoặc dịch vụ đang hoạt động
                          </div>
                        );
                      }
                      
                      return (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {activeItems.map((activeItem, index) => (
                            <div key={`${activeItem.orderId}-${index}`} className="bg-white p-4 rounded-lg border border-gray-200 hover:bg-gray-50">
                              <div className="flex items-start">
                                <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                  {activeItem.item.type === 'topup' ? 
                                    <FaCreditCard className="text-gray-500" /> : 
                                    activeItem.item.type === 'sim' ? 
                                    <FaPhone className="text-gray-500" /> : 
                                    <FaFileAlt className="text-gray-500" />}
                                </div>
                                <div className="flex-grow">
                                  <p className="text-sm font-medium text-gray-800">{activeItem.item.name}</p>
                                  
                                  {activeItem.item.type === 'topup' && (
                                    <div className="mt-1 space-y-1 text-xs text-gray-600">
                                      {activeItem.item.cardSerial && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Serial:</span>
                                          <span>{activeItem.item.cardSerial}</span>
                                        </div>
                                      )}
                                      {activeItem.item.cardNumber && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Mã thẻ:</span>
                                          <span>{activeItem.item.cardNumber}</span>
                                        </div>
                                      )}
                                      {activeItem.item.cardPin && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Mã PIN:</span>
                                          <span>{activeItem.item.cardPin}</span>
                                        </div>
                                      )}
                                      {activeItem.item.topupCode && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Mã nạp:</span>
                                          <span>{activeItem.item.topupCode}</span>
                                        </div>
                                      )}
                                      {activeItem.item.expiryDate && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Hạn dùng:</span>
                                          <span>{activeItem.item.expiryDate}</span>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  
                                  {activeItem.item.type === 'service' && (
                                    <div className="mt-1 space-y-1 text-xs text-gray-600">
                                      {activeItem.item.servicePeriod && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Thời hạn:</span>
                                          <span>{activeItem.item.servicePeriod}</span>
                                        </div>
                                      )}
                                      {activeItem.item.serviceStartDate && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Ngày bắt đầu:</span>
                                          <span>{activeItem.item.serviceStartDate}</span>
                                        </div>
                                      )}
                                      {activeItem.item.serviceEndDate && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Ngày kết thúc:</span>
                                          <span>{activeItem.item.serviceEndDate}</span>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  
                                  {activeItem.item.type === 'sim' && (
                                    <div className="mt-1 space-y-1 text-xs text-gray-600">
                                      {activeItem.item.simNumber && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">SIM:</span>
                                          <span>{activeItem.item.simNumber}</span>
                                        </div>
                                      )}
                                      {activeItem.item.phoneNumber && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">SDT:</span>
                                          <span>{activeItem.item.phoneNumber}</span>
                                        </div>
                                      )}
                                      {activeItem.item.iccid && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">ICCID:</span>
                                          <span>{activeItem.item.iccid}</span>
                                        </div>
                                      )}
                                      {activeItem.item.networkProvider && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500">Nhà mạng:</span>
                                          <span>{activeItem.item.networkProvider}</span>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  
                                  <div className="mt-2 text-xs text-gray-500">
                                    Mua ngày: {new Date(activeItem.orderDate).toLocaleDateString('vi-VN')}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      );
                    })()}
                  </div>
                  
                  <div className="mt-6 bg-gray-50 rounded-lg p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900">Đơn hàng gần đây</h3>
                      <button 
                        onClick={() => setActiveTab('orders')}
                        className="text-sm text-blue-600 hover:underline flex items-center"
                      >
                        Xem tất cả
                        <FaChevronRight className="ml-1" size="0.75em" />
                      </button>
                    </div>
                    
                    {renderRecentOrders()}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        
        {activeTab === 'orders' && (
          <div>
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Đơn hàng của khách hàng</h3>
              <div className="text-sm text-gray-500">
                {loadingOrders ? (
                  <div className="flex items-center">
                    <FaSpinner className="animate-spin text-blue-500 mr-2" />
                    <span>Đang tải...</span>
                  </div>
                ) : (
                  <span>Tổng số: {customerOrders.length} đơn hàng</span>
                )}
              </div>
            </div>
            
            {loadingOrders ? (
              <div className="flex justify-center items-center py-12">
                <FaSpinner className="animate-spin text-blue-500 mr-2" />
                <span>Đang tải đơn hàng...</span>
              </div>
            ) : customerOrders.length > 0 ? (
              <div className="space-y-4">
                {customerOrders.map((order, index) => (
                  <div key={order.id || index} className="bg-white p-4 rounded-lg border border-gray-200">
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                      <div>
                        <div className="flex items-center mb-2">
                          <FaShoppingBag className="text-blue-500 mr-2" />
                          <h4 className="font-medium">Đơn hàng #{order.id || `ORDER-${index+1}`}</h4>
                        </div>
                        <p className="text-sm text-gray-500">
                          Ngày đặt: {new Date(order.orderDate || order.createdAt).toLocaleDateString('vi-VN')}
                        </p>
                        {order.items && order.items.length > 0 && (
                          <div className="mt-2">
                            <p className="text-sm font-medium">Sản phẩm:</p>
                            <ul className="mt-1 space-y-1">
                              {order.items.map((item, itemIndex) => (
                                <li key={itemIndex} className="text-sm text-gray-600 flex justify-between">
                                  <span>{item.name}</span>
                                  <span>
                                    {item.quantity} x {item.price.toLocaleString('vi-VN')}đ
                                  </span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      <div className="mt-3 md:mt-0 flex flex-col items-end">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          order.status === 'completed' ? 'bg-green-100 text-green-800' :
                          order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {order.status === 'completed' ? 'Hoàn thành' :
                           order.status === 'processing' ? 'Đang xử lý' : 'Chờ xác nhận'}
                        </span>
                        <p className="font-medium text-lg mt-2">
                          {(order.totalAmount || 0).toLocaleString('vi-VN')}đ
                        </p>
                        <button className="text-blue-600 text-sm hover:underline mt-2">
                          Xem chi tiết
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-50 p-8 rounded-lg text-center">
                <FaShoppingBag className="text-gray-400 text-4xl mx-auto mb-3" />
                <h3 className="text-lg font-medium text-gray-700 mb-1">Không có đơn hàng nào</h3>
                <p className="text-gray-500">Khách hàng này chưa có đơn hàng nào.</p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'documents' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Giấy tờ đã tải lên</h3>
              <button 
                onClick={() => setShowUploadModal(true)}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                <FaUpload className="mr-2" />
                Tải lên giấy tờ mới
              </button>
            </div>
            
            {customer.documents && customer.documents.length > 0 ? (
              <div>
                {Object.entries(documentsByType).map(([type, docs]) => (
                  <div key={type} className="mb-6">
                    <h4 className="font-medium mb-3 border-b pb-2">
                      {type === 'nationalId' ? 'CMND/CCCD' :
                       type === 'passport' ? 'Hộ chiếu' :
                       type === 'drivingLicense' ? 'Giấy phép lái xe' :
                       type === 'proofOfResidence' ? 'Giấy tờ cư trú' :
                       type === 'photo' ? 'Ảnh chân dung' : 
                       type}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {docs.map(doc => (
                        <div key={doc.filename} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="flex items-center">
                                {doc.documentType.includes('idCard') ? <FaIdCard className="mr-2 text-blue-500" /> :
                                 doc.documentType.includes('passport') ? <FaPassport className="mr-2 text-green-500" /> :
                                 doc.documentType.includes('driving') ? <FaCar className="mr-2 text-red-500" /> :
                                 doc.documentType.includes('residence') ? <FaHome className="mr-2 text-purple-500" /> :
                                 <FaFileAlt className="mr-2 text-gray-500" />}
                                <h4 className="font-medium">
                                  {doc.documentType === 'idCard' || doc.documentType === 'idCard_front' ? 'CMND/CCCD (Mặt trước)' :
                                   doc.documentType === 'idCard_back' ? 'CMND/CCCD (Mặt sau)' :
                                   doc.documentType === 'passport_main' ? 'Hộ chiếu (Trang chính)' :
                                   doc.documentType === 'passport_visa' ? 'Hộ chiếu (Trang thị thực)' :
                                   doc.documentType === 'driving_license' ? 'Giấy phép lái xe' :
                                   doc.documentType === 'proofOfResidence' ? 'Giấy tờ cư trú' :
                                   doc.documentType === 'photo' ? 'Ảnh chân dung' :
                                   doc.documentType}
                                </h4>
                              </div>
                              <p className="text-sm text-gray-500 mt-1">
                                Ngày tải lên: {new Date(doc.uploadDate).toLocaleDateString('vi-VN')}
                              </p>
                              {doc.orderId && (
                                <p className="text-sm text-gray-500">
                                  Đơn hàng: #{doc.orderId}
                                </p>
                              )}
                              <p className="text-sm text-gray-500">
                                Tên file: {doc.originalFilename || doc.filename}
                              </p>
                            </div>
                            {doc.status && (
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                doc.status === 'verified' 
                                  ? 'bg-green-100 text-green-800' 
                                  : doc.status === 'rejected'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {doc.status === 'verified' 
                                  ? 'Đã xác minh' 
                                  : doc.status === 'rejected'
                                    ? 'Đã từ chối'
                                    : 'Đang chờ'}
                              </span>
                            )}
                          </div>
                          <div className="mt-3">
                            <button className="text-blue-600 text-sm hover:underline">
                              Xem tài liệu
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-50 p-6 rounded-lg text-center text-gray-500">
                Khách hàng chưa tải lên giấy tờ nào.
              </div>
            )}
            
            {/* Document Upload Modal */}
            {showUploadModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 w-full max-w-md">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Tải lên giấy tờ mới</h3>
                    <button 
                      onClick={() => {
                        setShowUploadModal(false);
                        setUploadForm({
                          documentType: '',
                          file: null,
                          description: ''
                        });
                        setUploadSuccess(false);
                      }}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                  
                  {uploadSuccess ? (
                    <div className="text-center py-6">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <FaCheck className="text-green-600 text-2xl" />
                      </div>
                      <h4 className="text-lg font-medium text-green-600 mb-2">Tải lên thành công!</h4>
                      <p className="text-gray-600">Giấy tờ đã được tải lên và đang chờ xác minh.</p>
                    </div>
                  ) : (
                    <form onSubmit={handleUploadDocument}>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Loại giấy tờ <span className="text-red-500">*</span>
                        </label>
                        <select
                          name="documentType"
                          value={uploadForm.documentType}
                          onChange={handleUploadFormChange}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          required
                        >
                          <option value="">-- Chọn loại giấy tờ --</option>
                          <option value="idCard_front">CMND/CCCD (Mặt trước)</option>
                          <option value="idCard_back">CMND/CCCD (Mặt sau)</option>
                          <option value="passport_main">Hộ chiếu (Trang chính)</option>
                          <option value="passport_visa">Hộ chiếu (Trang thị thực)</option>
                          <option value="driving_license">Giấy phép lái xe</option>
                          <option value="proofOfResidence">Giấy tờ cư trú</option>
                          <option value="photo">Ảnh chân dung</option>
                          <option value="other">Giấy tờ khác</option>
                        </select>
                      </div>
                      
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Tải lên file <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="file"
                          name="file"
                          onChange={handleFileChange}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          required
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          Định dạng hỗ trợ: JPG, PNG, PDF. Kích thước tối đa: 5MB
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Mô tả (tùy chọn)
                        </label>
                        <textarea
                          name="description"
                          value={uploadForm.description}
                          onChange={handleUploadFormChange}
                          rows="2"
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      
                      <div className="flex justify-end space-x-3">
                        <button
                          type="button"
                          onClick={() => setShowUploadModal(false)}
                          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        >
                          Hủy
                        </button>
                        <button
                          type="submit"
                          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                          disabled={uploading}
                        >
                          {uploading ? (
                            <>
                              <FaSpinner className="animate-spin mr-2" />
                              Đang tải lên...
                            </>
                          ) : (
                            <>
                              <FaUpload className="mr-2" />
                              Tải lên
                            </>
                          )}
                        </button>
                      </div>
                    </form>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'personal' && customer && (
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Thông tin cá nhân</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {customer.personalDetails?.firstName && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Họ và tên</p>
                    <p className="text-gray-800">
                      {customer.personalDetails.lastName} {customer.personalDetails.middleName} {customer.personalDetails.firstName}
                    </p>
                  </div>
                )}
                
                {customer.personalDetails?.dateOfBirth && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Ngày sinh</p>
                    <p className="text-gray-800">
                      {new Date(customer.personalDetails.dateOfBirth).toLocaleDateString('vi-VN')}
                    </p>
                  </div>
                )}
                
                {customer.personalDetails?.gender && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Giới tính</p>
                    <p className="text-gray-800">
                      {customer.personalDetails.gender === 'male' ? 'Nam' : 
                       customer.personalDetails.gender === 'female' ? 'Nữ' : 'Khác'}
                    </p>
                  </div>
                )}
                
                {customer.personalDetails?.maritalStatus && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Tình trạng hôn nhân</p>
                    <p className="text-gray-800">
                      {customer.personalDetails.maritalStatus === 'married' ? 'Đã kết hôn' : 
                       customer.personalDetails.maritalStatus === 'single' ? 'Độc thân' : 
                       customer.personalDetails.maritalStatus}
                    </p>
                  </div>
                )}
                
                {customer.personalDetails?.occupation && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Nghề nghiệp</p>
                    <p className="text-gray-800">{customer.personalDetails.occupation}</p>
                  </div>
                )}
                
                {customer.personalDetails?.employer && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Công ty</p>
                    <p className="text-gray-800">{customer.personalDetails.employer}</p>
                  </div>
                )}
                
                {customer.rawData?.citizenship?.nationality && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Quốc tịch</p>
                    <p className="text-gray-800">{customer.rawData.citizenship.nationality}</p>
                  </div>
                )}
                
                {customer.personalDetails?.taxId && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Mã số thuế</p>
                    <p className="text-gray-800">{customer.personalDetails.taxId}</p>
                  </div>
                )}
              </div>
            </div>
            
            {customer.addresses && customer.addresses.length > 0 && (
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Địa chỉ</h3>
                
                <div className="space-y-6">
                  {customer.addresses.map((address, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">
                            {address.type === 'home' ? 'Nhà riêng' : 
                             address.type === 'work' ? 'Công việc' : 
                             `Địa chỉ ${index + 1}`}
                            {address.isDefault && ' (Mặc định)'}
                          </h4>
                          <p className="text-gray-800 mt-1">
                            {address.street}, {address.ward}, {address.district}, {address.city}, {address.country}
                          </p>
                          {address.postalCode && (
                            <p className="text-gray-600 text-sm">Mã bưu điện: {address.postalCode}</p>
                          )}
                        </div>
                        {address.lastUsed && (
                          <div className="text-sm text-gray-500">
                            Sử dụng gần đây: {new Date(address.lastUsed).toLocaleDateString('vi-VN')}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {customer.rawData?.familyMembers && customer.rawData.familyMembers.length > 0 && (
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Thành viên gia đình</h3>
                
                <div className="space-y-4">
                  {customer.rawData.familyMembers.map((member, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <h4 className="font-medium">
                        {member.relationship === 'spouse' ? 'Vợ/Chồng' : 
                         member.relationship === 'child' ? 'Con' : 
                         member.relationship === 'parent' ? 'Cha/Mẹ' : 
                         member.relationship}
                      </h4>
                      <p className="text-gray-800 mt-1">Họ tên: {member.name}</p>
                      {member.dateOfBirth && (
                        <p className="text-gray-600 text-sm">
                          Ngày sinh: {new Date(member.dateOfBirth).toLocaleDateString('vi-VN')}
                        </p>
                      )}
                      {member.nationalId && (
                        <p className="text-gray-600 text-sm">CMND/CCCD: {member.nationalId}</p>
                      )}
                      {member.contactNumber && (
                        <p className="text-gray-600 text-sm">Số điện thoại: {member.contactNumber}</p>
                      )}
                      <p className="text-gray-600 text-sm">
                        Địa chỉ: {member.sameAddress ? 'Cùng địa chỉ với khách hàng' : 
                                 (member.address ? `${member.address.street}, ${member.address.district}, ${member.address.city}, ${member.address.country}` : 'Không có thông tin')}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {customer.rawData?.financialInfo?.bankAccounts && (
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Thông tin tài chính</h3>
                
                {customer.rawData.financialInfo.bankAccounts.map((account, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg mb-4">
                    <h4 className="font-medium">Tài khoản ngân hàng {index + 1}</h4>
                    <p className="text-gray-800 mt-1">Ngân hàng: {account.bankName}</p>
                    <p className="text-gray-600 text-sm">Số tài khoản: {account.accountNumber}</p>
                    <p className="text-gray-600 text-sm">Chi nhánh: {account.branch}</p>
                    {account.accountType && (
                      <p className="text-gray-600 text-sm">
                        Loại tài khoản: {account.accountType === 'savings' ? 'Tiết kiệm' : 
                                        account.accountType === 'checking' ? 'Vãng lai' : 
                                        account.accountType}
                      </p>
                    )}
                  </div>
                ))}
                
                {customer.rawData.financialInfo.creditScore && (
                  <div className="mt-4">
                    <p className="text-sm font-medium text-gray-700">Điểm tín dụng</p>
                    <p className="text-gray-800">{customer.rawData.financialInfo.creditScore}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerDetails;