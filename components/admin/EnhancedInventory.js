import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  FaSearch, 
  FaFilter, 
  FaSort, 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaBox, 
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaChevronLeft,
  FaChevronRight,
  FaDownload,
  FaUpload,
  FaEye,
  FaCopy,
  FaTimes,
  FaGoogle,
  FaSync,
  FaCloudUploadAlt,
  FaCloudDownloadAlt,
  FaCog
} from 'react-icons/fa';

const EnhancedInventory = ({ hideHeader = false }) => {
  const [inventory, setInventory] = useState([]);
  const [filteredInventory, setFilteredInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statistics, setStatistics] = useState(null);
  
  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    type: '',
    stockStatus: '',
    category: '',
    provider: '',
    priceMin: '',
    priceMax: ''
  });
  
  // Sorting and pagination states
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  
  // UI states
  const [showFilters, setShowFilters] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [bulkAction, setBulkAction] = useState('');
  const [showBulkPaste, setShowBulkPaste] = useState(false);
  const [bulkPasteData, setBulkPasteData] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [showItemDetails, setShowItemDetails] = useState(false);
  
  // Inline editing states
  const [editingItems, setEditingItems] = useState({});
  const [editLoading, setEditLoading] = useState({});
  
  // SKU display states
  const [expandedSkus, setExpandedSkus] = useState({});
  
  // Add stock modal states
  const [showAddStock, setShowAddStock] = useState(false);
  const [stockToAdd, setStockToAdd] = useState(0);
  const [itemDetails, setItemDetails] = useState('');
  
  // Google Sheets sync states
  const [showSheetsSync, setShowSheetsSync] = useState(false);
  const [sheetsConfig, setSheetsConfig] = useState(null);
  const [syncLoading, setSyncLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState('');
  const [lastSyncTime, setLastSyncTime] = useState(null);
  
  // Search input ref to maintain focus
  const searchInputRef = useRef(null);

  // Fetch inventory data
  const fetchInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage,
        limit: itemsPerPage,
        sortBy,
        sortOrder,
        ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
        ...(filters.type && { type: filters.type }),
        ...(filters.stockStatus && { stockStatus: filters.stockStatus }),
        ...(filters.category && { category: filters.category }),
        ...(filters.provider && { provider: filters.provider }),
        ...(filters.priceMin && { priceMin: filters.priceMin }),
        ...(filters.priceMax && { priceMax: filters.priceMax }),
        includeStats: 'true'
      });
      
      const response = await fetch(`/api/inventory?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch inventory');
      }
      
      const data = await response.json();
      
      if (data.pagination) {
        setInventory(data.items);
        setFilteredInventory(data.items);
        setTotalPages(Math.max(1, parseInt(data.pagination.totalPages) || 1));
        setTotalItems(Math.max(0, parseInt(data.pagination.totalItems) || 0));
        setStatistics(data.statistics);
      } else {
        // Fallback for simple array response
        setInventory(data);
        setFilteredInventory(data);
        setTotalItems(Math.max(0, parseInt(data.length) || 0));
        setTotalPages(Math.max(1, Math.ceil((data.length || 0) / itemsPerPage)));
      }
    } catch (err) {
      setError(err.message);
      console.error('Error fetching inventory:', err);
    } finally {
      setLoading(false);
    }
  };

  // Format SKU for display
  const formatSku = (sku, itemId, isExpanded) => {
    if (!sku || sku.length <= 12) return sku;
    
    if (isExpanded) {
      return (
        <span className="break-all">
          {sku}
          <button
            onClick={() => setExpandedSkus(prev => ({ ...prev, [itemId]: false }))}
            className="ml-1 text-blue-600 hover:text-blue-800 text-xs"
            title="Thu gọn"
          >
            ↑
          </button>
        </span>
      );
    }
    
    const firstPart = sku.substring(0, 4);
    const lastPart = sku.substring(sku.length - 4);
    
    return (
      <span>
        {firstPart}...{lastPart}
        <button
          onClick={() => setExpandedSkus(prev => ({ ...prev, [itemId]: true }))}
          className="ml-1 text-blue-600 hover:text-blue-800 text-xs"
          title="Xem đầy đủ"
        >
          ↓
        </button>
      </span>
    );
  };

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Effect to fetch data when filters change
  useEffect(() => {
    fetchInventory();
  }, [currentPage, itemsPerPage, sortBy, sortOrder, debouncedSearchTerm, filters]);

  // Effect to fetch Google Sheets configuration on component mount
  useEffect(() => {
    fetchSheetsConfig();
  }, []);

  // Handle search with useCallback to prevent re-renders
  const handleSearch = useCallback((e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page
  }, []);

  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({ ...prev, [filterName]: value }));
    setCurrentPage(1); // Reset to first page
  };

  // Handle sorting
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle item selection
  const handleItemSelect = (itemId) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.length === filteredInventory.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredInventory.map(item => item.id || item.sku));
    }
  };

  // Get inventory count for display
  const getInventoryCount = (item) => {
    if (item.inventory && typeof item.inventory === 'object') {
      if (item.inventory.type === 'separate' && Array.isArray(item.inventory.items)) {
        return item.inventory.items.length;
      }
      return parseInt(item.inventory.count) || 0;
    }
    return parseInt(item.currentInventory) || 0;
  };

  // Get inventory type
  const getInventoryType = (item) => {
    if (!item || !item.inventory) return 'same';
    return item.inventory.type || 'same';
  };

  // Get stock status
  const getStockStatus = (item) => {
    const count = getInventoryCount(item);
    const inventoryType = getInventoryType(item);
    
    // For separate inventory, check available items or count
    if (inventoryType === 'separate') {
      if (item.inventory?.items && item.inventory.items.length > 0) {
        // If items array exists and has items, check available items
        const availableItems = item.inventory.items.filter(i => 
          !i.status || i.status === 'available'
        ).length;
        if (availableItems === 0) return 'out_of_stock';
        if (availableItems <= 5) return 'low_stock';
        return 'in_stock';
      } else {
        // If items array is empty or doesn't exist, use count
        if (count === 0) return 'out_of_stock';
        if (count <= 10) return 'low_stock';
        return 'in_stock';
      }
    }
    
    // For same inventory type
    if (count === 0) return 'out_of_stock';
    if (count <= 10) return 'low_stock';
    return 'in_stock';
  };

  // Get stock status color
  const getStockStatusColor = (status) => {
    switch (status) {
      case 'in_stock': return 'text-green-600 bg-green-100';
      case 'low_stock': return 'text-yellow-600 bg-yellow-100';
      case 'out_of_stock': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get stock status text
  const getStockStatusText = (status) => {
    switch (status) {
      case 'in_stock': return 'Còn hàng';
      case 'low_stock': return 'Sắp hết';
      case 'out_of_stock': return 'Hết hàng';
      default: return 'Không xác định';
    }
  };

  // Format currency
  const formatCurrency = (amount, currency = 'VND') => {
    try {
      // List of valid currency codes
      const validCurrencies = ['VND', 'USD', 'EUR', 'JPY', 'CNY', 'KRW', 'THB', 'SGD'];
      
      // Use VND as fallback for invalid currencies
      const safeCurrency = validCurrencies.includes(currency) ? currency : 'VND';
      
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: safeCurrency,
        minimumFractionDigits: 0
      }).format(amount);
    } catch (error) {
      // Fallback to simple number formatting if currency formatting fails
      return new Intl.NumberFormat('vi-VN').format(amount) + ' ' + (currency || 'VND');
    }
  };

  // Handle bulk paste
  const handleBulkPaste = async (sku) => {
    try {
      const response = await fetch('/api/inventory', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulkPaste',
          sku,
          pasteData: bulkPasteData
        })
      });
      
      if (response.ok) {
        setBulkPasteData('');
        setShowBulkPaste(false);
        fetchInventory();
      }
    } catch (error) {
      console.error('Error in bulk paste:', error);
    }
  };

  // Handle inline edit toggle
  const handleEditToggle = (item) => {
    const itemId = item.id || item.sku;
    if (editingItems[itemId]) {
      // Cancel editing
      setEditingItems(prev => {
        const newState = { ...prev };
        delete newState[itemId];
        return newState;
      });
    } else {
      // Start editing
      setEditingItems(prev => ({
        ...prev,
        [itemId]: {
          name: item.name || '',
          sku: item.sku || '',
          price: item.price || '',
          currency: item.currency || 'VND',
          description: item.description || '',
          brand: item.brand || '',
          provider: item.provider || '',
          category: item.category || '',
          inventory: {
            type: item.inventory?.type || 'same',
            count: item.inventory?.count || item.currentInventory || 0,
            items: item.inventory?.items || []
          }
        }
      }));
    }
  };

  // Handle inline form change
  const handleInlineFormChange = (itemId, field, value) => {
    setEditingItems(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [field]: value
      }
    }));
  };

  // Handle inventory form change (for nested inventory object)
  const handleInventoryFormChange = (itemId, inventoryField, value) => {
    setEditingItems(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        inventory: {
          ...prev[itemId].inventory,
          [inventoryField]: value
        }
      }
    }));
  };

  // Handle save inline edit
  const handleSaveInlineEdit = async (item) => {
    const itemId = item.id || item.sku;
    const editData = editingItems[itemId];
    if (!editData) return;
    
    setEditLoading(prev => ({ ...prev, [itemId]: true }));
    try {
      const response = await fetch('/api/inventory', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: item.id,
          sku: item.sku,
          ...editData
        })
      });
      
      if (response.ok) {
        // Remove from editing state
        setEditingItems(prev => {
          const newState = { ...prev };
          delete newState[itemId];
          return newState;
        });
        fetchInventory();
      } else {
        const errorData = await response.json();
        alert('Lỗi khi lưu: ' + (errorData.message || 'Không thể lưu thay đổi'));
      }
    } catch (error) {
      console.error('Error saving edit:', error);
      alert('Lỗi khi lưu thay đổi');
    } finally {
      setEditLoading(prev => ({ ...prev, [itemId]: false }));
    }
  };

  // Handle delete item
  const handleDeleteItem = async (item) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa sản phẩm "${item.name}"?`)) {
      return;
    }
    
    try {
      const response = await fetch('/api/inventory', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: item.id,
          sku: item.sku
        })
      });
      
      if (response.ok) {
        fetchInventory();
      } else {
        const errorData = await response.json();
        alert('Lỗi khi xóa: ' + (errorData.message || 'Không thể xóa sản phẩm'));
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert('Lỗi khi xóa sản phẩm');
    }
  };

  // Google Sheets sync functions
  const fetchSheetsConfig = async () => {
    try {
      const response = await fetch('/api/inventory/sheets-sync?action=status');
      if (response.ok) {
        const config = await response.json();
        setSheetsConfig(config);
      }
    } catch (error) {
      console.error('Error fetching sheets config:', error);
    }
  };

  const handleSyncToSheets = async () => {
    try {
      setSyncLoading(true);
      setSyncStatus('Đang đồng bộ lên Google Sheets...');
      
      const response = await fetch('/api/inventory/sheets-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'to-sheets' })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        setSyncStatus(`Thành công! Đã đồng bộ ${result.count} sản phẩm lên Google Sheets`);
        setLastSyncTime(new Date());
        setTimeout(() => setSyncStatus(''), 3000);
      } else {
        setSyncStatus(`Lỗi: ${result.message || 'Không thể đồng bộ'}`);
        setTimeout(() => setSyncStatus(''), 5000);
      }
    } catch (error) {
      console.error('Error syncing to sheets:', error);
      setSyncStatus('Lỗi kết nối khi đồng bộ');
      setTimeout(() => setSyncStatus(''), 5000);
    } finally {
      setSyncLoading(false);
    }
  };

  const handleSyncFromSheets = async () => {
    try {
      setSyncLoading(true);
      setSyncStatus('Đang đồng bộ từ Google Sheets...');
      
      const response = await fetch('/api/inventory/sheets-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'from-sheets' })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        setSyncStatus(`Thành công! Đã đồng bộ ${result.count} sản phẩm từ Google Sheets`);
        setLastSyncTime(new Date());
        fetchInventory(); // Refresh the inventory display
        setTimeout(() => setSyncStatus(''), 3000);
      } else {
        setSyncStatus(`Lỗi: ${result.message || 'Không thể đồng bộ'}`);
        setTimeout(() => setSyncStatus(''), 5000);
      }
    } catch (error) {
      console.error('Error syncing from sheets:', error);
      setSyncStatus('Lỗi kết nối khi đồng bộ');
      setTimeout(() => setSyncStatus(''), 5000);
    } finally {
      setSyncLoading(false);
    }
  };

  const handleBidirectionalSync = async () => {
    try {
      setSyncLoading(true);
      setSyncStatus('Đang thực hiện đồng bộ hai chiều...');
      
      const response = await fetch('/api/inventory/sheets-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'bidirectional' })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        setSyncStatus(`Thành công! Đã hợp nhất ${result.mergedItems} sản phẩm`);
        setLastSyncTime(new Date());
        fetchInventory(); // Refresh the inventory display
        setTimeout(() => setSyncStatus(''), 3000);
      } else {
        setSyncStatus(`Lỗi: ${result.message || 'Không thể đồng bộ'}`);
        setTimeout(() => setSyncStatus(''), 5000);
      }
    } catch (error) {
      console.error('Error in bidirectional sync:', error);
      setSyncStatus('Lỗi kết nối khi đồng bộ');
      setTimeout(() => setSyncStatus(''), 5000);
    } finally {
      setSyncLoading(false);
    }
  };

  // Load sheets config on component mount
  useEffect(() => {
    fetchSheetsConfig();
  }, []);

  // Render loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">Đang tải kho hàng...</span>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>Lỗi:</strong> {error}
        <button 
          onClick={fetchInventory}
          className="ml-4 bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
        >
          Thử lại
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      {!hideHeader && (
        <div className="border-b border-gray-200 p-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Quản lý kho hàng</h2>
              <p className="text-sm text-gray-600 mt-1">
                Tổng cộng {totalItems} sản phẩm
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  showFilters 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <FaFilter className="inline mr-2" />
                Bộ lọc
              </button>
              <button
                onClick={() => setShowBulkPaste(!showBulkPaste)}
                className="px-4 py-2 bg-green-100 text-green-700 rounded-md text-sm font-medium hover:bg-green-200"
              >
                <FaUpload className="inline mr-2" />
                Nhập hàng loạt
              </button>
              <button
                onClick={() => setShowSheetsSync(!showSheetsSync)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  showSheetsSync 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } ${!sheetsConfig?.configured ? 'opacity-50' : ''}`}
                disabled={!sheetsConfig?.configured}
                title={!sheetsConfig?.configured ? 'Cần cấu hình Google Sheets trước' : 'Đồng bộ với Google Sheets'}
              >
                <FaGoogle className="inline mr-2" />
                Google Sheets
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Statistics */}
      {statistics && (
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{statistics.totalItems}</div>
              <div className="text-sm text-blue-600">Tổng sản phẩm</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{statistics.inStock}</div>
              <div className="text-sm text-green-600">Còn hàng</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{statistics.lowStock}</div>
              <div className="text-sm text-yellow-600">Sắp hết</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{statistics.outOfStock}</div>
              <div className="text-sm text-red-600">Hết hàng</div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="p-6 border-b border-gray-200">
        {/* Search Bar */}
        <div className="mb-4">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              key="search-input"
              ref={searchInputRef}
              type="text"
              placeholder="Tìm kiếm sản phẩm..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="">Tất cả loại</option>
              <option value="separate">Riêng biệt</option>
              <option value="same">Cùng loại</option>
            </select>

            <select
              value={filters.stockStatus}
              onChange={(e) => handleFilterChange('stockStatus', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="in_stock">Còn hàng</option>
              <option value="low_stock">Sắp hết</option>
              <option value="out_of_stock">Hết hàng</option>
            </select>

            <input
              type="text"
              placeholder="Danh mục"
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />

            <input
              type="text"
              placeholder="Nhà cung cấp"
              value={filters.provider}
              onChange={(e) => handleFilterChange('provider', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />

            <input
              type="number"
              placeholder="Giá tối thiểu"
              value={filters.priceMin}
              onChange={(e) => handleFilterChange('priceMin', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />

            <input
              type="number"
              placeholder="Giá tối đa"
              value={filters.priceMax}
              onChange={(e) => handleFilterChange('priceMax', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
          </div>
        )}
      </div>

      {/* Bulk Paste Modal */}
      {showBulkPaste && (
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-medium mb-4">Nhập hàng loạt</h3>
          <textarea
            value={bulkPasteData}
            onChange={(e) => setBulkPasteData(e.target.value)}
            placeholder="Dán dữ liệu hàng loạt ở đây (mỗi dòng một mục)..."
            className="w-full h-32 border border-gray-300 rounded-md px-3 py-2 text-sm"
          />
          <div className="flex justify-end space-x-2 mt-4">
            <button
              onClick={() => setShowBulkPaste(false)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Hủy
            </button>
            <button
              onClick={() => handleBulkPaste('default')}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              Xử lý
            </button>
          </div>
        </div>
      )}

      {/* Inventory Cards */}
      <div className="p-6">
        {/* Bulk Actions Bar */}
        {selectedItems.length > 0 && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-700">
                Đã chọn {selectedItems.length} sản phẩm
              </span>
              <div className="flex space-x-2">
                <button className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
                  Xuất Excel
                </button>
                <button className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600">
                  Xóa
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Sort Controls */}
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={selectedItems.length === filteredInventory.length && filteredInventory.length > 0}
              onChange={handleSelectAll}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-gray-600">Chọn tất cả</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Sắp xếp:</span>
            <button
              onClick={() => handleSort('name')}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 flex items-center"
            >
              Tên <FaSort className="ml-1" />
            </button>
            <button
              onClick={() => handleSort('price')}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 flex items-center"
            >
              Giá <FaSort className="ml-1" />
            </button>
          </div>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredInventory.map((item) => {
            const stockStatus = getStockStatus(item);
            const inventoryType = getInventoryType(item);
            const inventoryCount = getInventoryCount(item);
            const itemId = item.id || item.sku;
            const isEditing = editingItems[itemId];
            const editData = editingItems[itemId];
            const isLoading = editLoading[itemId];
            
            return (
              <div key={itemId} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                {/* Card Header with Checkbox */}
                <div className="p-4 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(itemId)}
                      onChange={() => handleItemSelect(itemId)}
                      className="rounded border-gray-300"
                    />
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      getStockStatusColor(stockStatus)
                    }`}>
                      {getStockStatusText(stockStatus)}
                    </span>
                  </div>
                </div>

                {/* Product Image */}
                <div className="p-4 flex justify-center">
                  {item.image ? (
                    <img 
                      className="h-24 w-24 rounded-lg object-cover" 
                      src={item.image} 
                      alt={item.name}
                    />
                  ) : (
                    <div className="h-24 w-24 rounded-lg bg-gray-100 flex items-center justify-center">
                      <FaBox className="text-gray-400 text-2xl" />
                    </div>
                  )}
                </div>

                {/* Product Info */}
                <div className="p-4 space-y-3">
                  {/* Product Name */}
                  <div>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editData.name}
                        onChange={(e) => handleInlineFormChange(itemId, 'name', e.target.value)}
                        className="w-full text-sm font-medium border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Tên sản phẩm"
                      />
                    ) : (
                      <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                        {item.name}
                      </h3>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      {item.brand || item.provider}
                    </p>
                  </div>

                  <div className="space-y-2">
                    {/* SKU */}
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">SKU:</span>
                      {isEditing ? (
                        <input
                          type="text"
                          value={editData.sku}
                          onChange={(e) => handleInlineFormChange(itemId, 'sku', e.target.value)}
                          className="text-xs font-mono border border-gray-300 rounded px-1 py-0.5 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-24"
                          placeholder="SKU"
                        />
                      ) : (
                        <span className="text-xs font-mono text-gray-900">
                          {formatSku(item.sku, itemId, expandedSkus[itemId])}
                        </span>
                      )}
                    </div>
                    
                    {/* Price */}
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Giá:</span>
                      {isEditing ? (
                        <input
                          type="number"
                          value={editData.price}
                          onChange={(e) => handleInlineFormChange(itemId, 'price', e.target.value)}
                          className="text-sm font-semibold border border-gray-300 rounded px-1 py-0.5 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-20"
                          placeholder="Giá"
                        />
                      ) : (
                        <span className="text-sm font-semibold text-gray-900">
                          {formatCurrency(item.price, item.currency)}
                        </span>
                      )}
                    </div>

                    {/* Category */}
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Danh mục:</span>
                      {isEditing ? (
                        <input
                          type="text"
                          value={editData.category}
                          onChange={(e) => handleInlineFormChange(itemId, 'category', e.target.value)}
                          className="text-xs border border-gray-300 rounded px-1 py-0.5 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-20"
                          placeholder="Danh mục"
                        />
                      ) : (
                        <span className="text-xs text-gray-900">{item.category || '-'}</span>
                      )}
                    </div>

                    {/* Inventory Type */}
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Loại:</span>
                      {isEditing ? (
                        <select
                          value={editData.inventory.type}
                          onChange={(e) => handleInventoryFormChange(itemId, 'type', e.target.value)}
                          className="text-xs border border-gray-300 rounded px-1 py-0.5 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="same">Cùng loại</option>
                          <option value="separate">Riêng biệt</option>
                        </select>
                      ) : (
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          inventoryType === 'separate' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {inventoryType === 'separate' ? 'Riêng biệt' : 'Cùng loại'}
                        </span>
                      )}
                    </div>

                    {/* Quantity */}
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Số lượng:</span>
                      {isEditing ? (
                        <input
                          type="number"
                          value={editData.inventory.count}
                          onChange={(e) => handleInventoryFormChange(itemId, 'count', parseInt(e.target.value) || 0)}
                          className="text-sm font-medium border border-gray-300 rounded px-1 py-0.5 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-16"
                          placeholder="Số lượng"
                        />
                      ) : (
                        <span className="text-sm font-medium text-gray-900">
                          {inventoryCount}
                          {inventoryType === 'separate' && (
                            <span className="text-gray-500 ml-1">mục</span>
                          )}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Card Actions */}
                <div className="p-4 border-t border-gray-100">
                  <div className="flex justify-center space-x-3">
                    {isEditing ? (
                      <>
                        <button
                          onClick={() => handleSaveInlineEdit(item)}
                          disabled={isLoading}
                          className="flex items-center px-3 py-1 text-xs text-green-600 hover:text-green-800 hover:bg-green-50 rounded transition-colors disabled:opacity-50"
                          title="Lưu"
                        >
                          {isLoading ? (
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-600 mr-1"></div>
                          ) : (
                            <FaCheckCircle className="mr-1" />
                          )}
                          Lưu
                        </button>
                        <button
                          onClick={() => handleEditToggle(item)}
                          disabled={isLoading}
                          className="flex items-center px-3 py-1 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded transition-colors disabled:opacity-50"
                          title="Hủy"
                        >
                          <FaTimesCircle className="mr-1" />
                          Hủy
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => {
                            setSelectedItem(item);
                            setShowItemDetails(true);
                          }}
                          className="flex items-center px-3 py-1 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                          title="Xem chi tiết"
                        >
                          <FaEye className="mr-1" />
                          Chi tiết
                        </button>
                        <button
                          onClick={() => handleEditToggle(item)}
                          className="flex items-center px-3 py-1 text-xs text-green-600 hover:text-green-800 hover:bg-green-50 rounded transition-colors"
                          title="Chỉnh sửa"
                        >
                          <FaEdit className="mr-1" />
                          Sửa
                        </button>
                        <button
                          onClick={() => {
                            setSelectedItem(item);
                            setShowAddStock(true);
                          }}
                          className="flex items-center px-3 py-1 text-xs text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded transition-colors"
                          title="Thêm kho"
                        >
                          <FaPlus className="mr-1" />
                          Thêm kho
                        </button>
                        <button
                          onClick={() => handleDeleteItem(item)}
                          className="flex items-center px-3 py-1 text-xs text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
                          title="Xóa"
                        >
                          <FaTrash className="mr-1" />
                          Xóa
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Empty State */}
        {filteredInventory.length === 0 && (
          <div className="text-center py-12">
            <FaBox className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Không có sản phẩm</h3>
            <p className="mt-1 text-sm text-gray-500">Không tìm thấy sản phẩm nào phù hợp với bộ lọc hiện tại.</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Hiển thị {Math.max(1, ((currentPage - 1) * itemsPerPage) + 1)} đến {Math.min(currentPage * itemsPerPage, totalItems || 0)} trong tổng số {totalItems || 0} kết quả
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                <FaChevronLeft />
              </button>
              
              {[...Array(Math.min(5, totalPages))].map((_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-1 border rounded-md text-sm ${
                      currentPage === page
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                <FaChevronRight />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Item Details Modal */}
      {showItemDetails && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Chi tiết sản phẩm</h3>
              <button
                onClick={() => setShowItemDetails(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimesCircle />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Tên sản phẩm</label>
                <p className="text-sm text-gray-900">{selectedItem.name}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">SKU</label>
                  <p className="text-sm text-gray-900">{selectedItem.sku}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Giá</label>
                  <p className="text-sm text-gray-900">{formatCurrency(selectedItem.price, selectedItem.currency)}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Loại kho</label>
                  <p className="text-sm text-gray-900">{getInventoryType(selectedItem) === 'separate' ? 'Riêng biệt' : 'Cùng loại'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Số lượng</label>
                  <p className="text-sm text-gray-900">{getInventoryCount(selectedItem)}</p>
                </div>
              </div>
              
              {selectedItem.description && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Mô tả</label>
                  <p className="text-sm text-gray-900">{selectedItem.description}</p>
                </div>
              )}
              
              {getInventoryType(selectedItem) === 'separate' && selectedItem.inventory?.items && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Danh sách mục ({selectedItem.inventory.items.length})</label>
                  <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-md">
                    {selectedItem.inventory.items.slice(0, 10).map((item, index) => (
                      <div key={index} className="px-3 py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-sm text-gray-900">{item.value || item.name || `Mục ${index + 1}`}</span>
                        {item.status && (
                          <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                            item.status === 'available' ? 'bg-green-100 text-green-800' :
                            item.status === 'used' ? 'bg-gray-100 text-gray-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {item.status === 'available' ? 'Có sẵn' : 
                             item.status === 'used' ? 'Đã dùng' : 'Không khả dụng'}
                          </span>
                        )}
                      </div>
                    ))}
                    {selectedItem.inventory.items.length > 10 && (
                      <div className="px-3 py-2 text-sm text-gray-500 text-center">
                        ... và {selectedItem.inventory.items.length - 10} mục khác
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Add Stock Modal */}
      {showAddStock && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-90vw">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Thêm kho hàng</h3>
              <button
                onClick={() => {
                  setShowAddStock(false);
                  setSelectedItem(null);
                  setStockToAdd(0);
                  setItemDetails('');
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <FaTimes />
              </button>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Sản phẩm: {selectedItem.name}</p>
              <p className="text-sm text-gray-600 mb-2">SKU: {selectedItem.sku}</p>
              <p className="text-sm text-gray-600 mb-2">Loại kho: {getInventoryType(selectedItem) === 'same' ? 'Chung' : 'Riêng biệt'}</p>
              <p className="text-sm text-gray-600 mb-4">Kho hiện tại: {getInventoryCount(selectedItem)}</p>
            </div>
            
            {getInventoryType(selectedItem) === 'same' ? (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Số lượng thêm vào kho
                </label>
                <input
                  type="number"
                  value={stockToAdd}
                  onChange={(e) => setStockToAdd(parseInt(e.target.value) || 0)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Nhập số lượng"
                  min="1"
                />
              </div>
            ) : (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Chi tiết sản phẩm (mã topup, serial, v.v.)
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Nhập từng item trên một dòng hoặc phân cách bằng dấu phẩy
                </p>
                <textarea
                  value={itemDetails}
                  onChange={(e) => setItemDetails(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ví dụ:\nTOPUP001\nTOPUP002\nhoặc: TOPUP001, TOPUP002, TOPUP003"
                  rows="4"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Số lượng sẽ được tính tự động: {itemDetails.trim() ? (itemDetails.includes(',') ? itemDetails.split(',').filter(item => item.trim()).length : itemDetails.split('\n').filter(item => item.trim()).length) : 0} items
                </p>
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowAddStock(false);
                  setSelectedItem(null);
                  setStockToAdd(0);
                }}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Hủy
              </button>
              <button
                onClick={async () => {
                  try {
                    let updateData = {
                      id: selectedItem.id,
                      sku: selectedItem.sku,
                      inventory: { ...selectedItem.inventory }
                    };
                    
                    if (getInventoryType(selectedItem) === 'same') {
                      if (stockToAdd <= 0) {
                        alert('Vui lòng nhập số lượng hợp lệ');
                        return;
                      }
                      const currentCount = getInventoryCount(selectedItem);
                      updateData.inventory.count = currentCount + stockToAdd;
                    } else {
                      // Handle separate inventory type
                      if (!itemDetails.trim()) {
                        alert('Vui lòng nhập chi tiết sản phẩm');
                        return;
                      }
                      
                      // Parse item details - support both comma-separated and line-separated
                      let newItems = [];
                      if (itemDetails.includes(',')) {
                        newItems = itemDetails.split(',').map(item => item.trim()).filter(item => item);
                      } else {
                        newItems = itemDetails.split('\n').map(item => item.trim()).filter(item => item);
                      }
                      
                      if (newItems.length === 0) {
                        alert('Không tìm thấy item hợp lệ');
                        return;
                      }
                      
                      // Create new items array with existing items plus new ones
                      const existingItems = selectedItem.inventory.items || [];
                      const itemsToAdd = newItems.map(detail => ({
                        id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        detail: detail,
                        available: true,
                        createdAt: new Date().toISOString()
                      }));
                      
                      updateData.inventory.items = [...existingItems, ...itemsToAdd];
                      updateData.inventory.count = (selectedItem.inventory.count || 0) + newItems.length;
                    }
                    
                    const response = await fetch('/api/inventory', {
                      method: 'PUT',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify(updateData),
                    });
                    
                    if (response.ok) {
                      await fetchInventory();
                      setShowAddStock(false);
                      setSelectedItem(null);
                      setStockToAdd(0);
                      setItemDetails('');
                    } else {
                      alert('Có lỗi xảy ra khi cập nhật kho hàng');
                    }
                  } catch (error) {
                    console.error('Error updating stock:', error);
                    alert('Có lỗi xảy ra khi cập nhật kho hàng');
                  }
                }}
                className="px-4 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                disabled={getInventoryType(selectedItem) === 'same' ? stockToAdd <= 0 : !itemDetails.trim()}
              >
                Thêm vào kho
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Google Sheets Sync Modal */}
      {showSheetsSync && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-[600px] max-w-90vw max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold flex items-center">
                <FaGoogle className="text-blue-500 mr-2" />
                Đồng bộ Google Sheets
              </h3>
              <button
                onClick={() => setShowSheetsSync(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <FaTimes />
              </button>
            </div>

            {/* Sync Status */}
            {syncStatus && (
              <div className={`mb-4 p-3 rounded-md ${
                syncStatus.includes('Thành công') 
                  ? 'bg-green-100 text-green-700 border border-green-200' 
                  : syncStatus.includes('Lỗi') 
                  ? 'bg-red-100 text-red-700 border border-red-200'
                  : 'bg-blue-100 text-blue-700 border border-blue-200'
              }`}>
                {syncLoading && <FaSync className="animate-spin inline mr-2" />}
                {syncStatus}
              </div>
            )}

            {/* Configuration Status */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center">
                <FaCog className="mr-2" />
                Trạng thái cấu hình
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  {sheetsConfig?.configured ? (
                    <FaCheckCircle className="text-green-500 mr-2" />
                  ) : (
                    <FaTimesCircle className="text-red-500 mr-2" />
                  )}
                  <span>Google Sheets API: {sheetsConfig?.configured ? 'Đã cấu hình' : 'Chưa cấu hình'}</span>
                </div>
                {sheetsConfig?.sheetUrl && (
                  <div className="flex items-center">
                    <FaCheckCircle className="text-green-500 mr-2" />
                    <span>Sheet URL: </span>
                    <a 
                      href={sheetsConfig.sheetUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 ml-1 underline"
                    >
                      Mở Google Sheets
                    </a>
                  </div>
                )}
                {lastSyncTime && (
                  <div className="flex items-center">
                    <FaCheckCircle className="text-green-500 mr-2" />
                    <span>Lần đồng bộ cuối: {lastSyncTime.toLocaleString('vi-VN')}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Sync Actions */}
            {sheetsConfig?.configured ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Sync to Sheets */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h5 className="font-medium mb-2 flex items-center">
                      <FaCloudUploadAlt className="text-blue-500 mr-2" />
                      Đồng bộ lên Sheets
                    </h5>
                    <p className="text-sm text-gray-600 mb-3">
                      Gửi dữ liệu kho hàng từ hệ thống lên Google Sheets. Sẽ ghi đè dữ liệu hiện có trên Sheets.
                    </p>
                    <button
                      onClick={handleSyncToSheets}
                      disabled={syncLoading}
                      className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      {syncLoading ? (
                        <FaSync className="animate-spin mr-2" />
                      ) : (
                        <FaCloudUploadAlt className="mr-2" />
                      )}
                      Đồng bộ lên Sheets
                    </button>
                  </div>

                  {/* Sync from Sheets */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h5 className="font-medium mb-2 flex items-center">
                      <FaCloudDownloadAlt className="text-green-500 mr-2" />
                      Đồng bộ từ Sheets
                    </h5>
                    <p className="text-sm text-gray-600 mb-3">
                      Lấy dữ liệu từ Google Sheets về hệ thống. Sẽ ghi đè dữ liệu hiện có trong hệ thống.
                    </p>
                    <button
                      onClick={handleSyncFromSheets}
                      disabled={syncLoading}
                      className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      {syncLoading ? (
                        <FaSync className="animate-spin mr-2" />
                      ) : (
                        <FaCloudDownloadAlt className="mr-2" />
                      )}
                      Đồng bộ từ Sheets
                    </button>
                  </div>
                </div>

                {/* Bidirectional Sync */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h5 className="font-medium mb-2 flex items-center">
                    <FaSync className="text-purple-500 mr-2" />
                    Đồng bộ hai chiều (Khuyến nghị)
                  </h5>
                  <p className="text-sm text-gray-600 mb-3">
                    Hợp nhất dữ liệu từ cả hai nguồn. Dữ liệu mới nhất sẽ được ưu tiên, các sản phẩm mới sẽ được thêm vào cả hai bên.
                  </p>
                  <button
                    onClick={handleBidirectionalSync}
                    disabled={syncLoading}
                    className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {syncLoading ? (
                      <FaSync className="animate-spin mr-2" />
                    ) : (
                      <FaSync className="mr-2" />
                    )}
                    Đồng bộ hai chiều
                  </button>
                </div>

                {/* Sheet Structure Info */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h5 className="font-medium mb-2 text-blue-800">Cấu trúc Google Sheets</h5>
                  <p className="text-sm text-blue-700 mb-2">
                    Google Sheets sẽ có các cột sau:
                  </p>
                  <div className="text-xs text-blue-600 space-y-1">
                    <div><strong>SKU:</strong> Mã sản phẩm</div>
                    <div><strong>Name:</strong> Tên sản phẩm</div>
                    <div><strong>Brand:</strong> Thương hiệu</div>
                    <div><strong>Price:</strong> Giá</div>
                    <div><strong>Currency:</strong> Đơn vị tiền tệ</div>
                    <div><strong>Categories:</strong> Danh mục (phân cách bằng dấu phẩy)</div>
                    <div><strong>Inventory_Type:</strong> Loại kho (separate/same)</div>
                    <div><strong>Inventory_Count:</strong> Số lượng kho</div>
                    <div><strong>Inventory_Items:</strong> Chi tiết items (JSON cho loại separate)</div>
                    <div><strong>Description:</strong> Mô tả</div>
                    <div><strong>Last_Updated:</strong> Thời gian cập nhật cuối</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <FaExclamationTriangle className="text-yellow-500 text-4xl mx-auto mb-4" />
                <h4 className="text-lg font-medium mb-2">Chưa cấu hình Google Sheets</h4>
                <p className="text-gray-600 mb-4">
                  Để sử dụng tính năng đồng bộ Google Sheets, bạn cần cấu hình các biến môi trường sau:
                </p>
                <div className="text-left bg-gray-100 p-4 rounded-lg text-sm font-mono">
                  <div>GOOGLE_SHEETS_INVENTORY_ID=your_sheet_id</div>
                  <div>GOOGLE_SERVICE_ACCOUNT_EMAIL=your_service_account_email</div>
                  <div>GOOGLE_PRIVATE_KEY=your_private_key</div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

    </div>
  );
};

export default EnhancedInventory;