import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { FaUserPlus, FaUserEdit, FaTrash, FaSpinner, FaTimes, FaSave, FaCheck, FaUndo } from 'react-icons/fa';

const roleOptions = [
  { value: 'admin', label: 'Admin', color: 'bg-green-100 text-green-800' },
  { value: 'manager', label: 'Quản lý', color: 'bg-purple-100 text-purple-800' },
  { value: 'staff', label: 'Nhân viên', color: 'bg-blue-100 text-blue-800' }
];

const StaffManagement = () => {
  const router = useRouter();
  const { store } = router.query;
  
  const [staff, setStaff] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'staff',
    isActive: true
  });
  
  const [editingStaffId, setEditingStaffId] = useState(null);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  
  // Fetch staff data
  useEffect(() => {
    const fetchStaff = async () => {
      if (!store) return;
      
      try {
        setLoading(true);
        const response = await fetch(`/api/staff/list?storeId=${store}`);
        
        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
          setStaff(data.staff || []);
        } else {
          throw new Error(data.message || 'Failed to fetch staff data');
        }
      } catch (error) {
        console.error('Error fetching staff:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchStaff();
  }, [store]);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  
  // Handle form submission for adding new staff
  const handleAddStaff = async (e) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const staffData = {
        ...formData,
        storeId: store
      };
      
      const response = await fetch('/api/staff/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(staffData)
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Add the new staff member to the list
        setStaff([...staff, data.staff]);
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          role: 'staff',
          isActive: true
        });
        
        setShowAddForm(false);
      } else {
        throw new Error(data.message || 'Failed to add staff member');
      }
    } catch (error) {
      console.error('Error adding staff:', error);
      alert(`Error adding staff: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };
  
  // Start editing a staff member
  const handleEditStart = (staffMember) => {
    setEditingStaffId(staffMember.id);
    setFormData({
      name: staffMember.name,
      email: staffMember.email,
      phone: staffMember.phone || '',
      role: staffMember.role,
      isActive: staffMember.isActive
    });
  };
  
  // Cancel editing
  const handleEditCancel = () => {
    setEditingStaffId(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'staff',
      isActive: true
    });
  };
  
  // Save edited staff member
  const handleUpdateStaff = async (id) => {
    try {
      setSaving(true);
      
      const response = await fetch(`/api/staff/update?id=${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Update the staff list
        setStaff(staff.map(member => 
          member.id === id ? data.staff : member
        ));
        
        setEditingStaffId(null);
      } else {
        throw new Error(data.message || 'Failed to update staff member');
      }
    } catch (error) {
      console.error('Error updating staff:', error);
      alert(`Error updating staff: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };
  
  // Delete staff member
  const handleDeleteStaff = async (id) => {
    if (!confirm('Bạn có chắc chắn muốn xóa nhân viên này?')) {
      return;
    }
    
    try {
      setDeleting(true);
      
      const response = await fetch(`/api/staff/delete?id=${id}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Remove from staff list
        setStaff(staff.filter(member => member.id !== id));
      } else {
        throw new Error(data.message || 'Failed to delete staff member');
      }
    } catch (error) {
      console.error('Error deleting staff:', error);
      alert(`Error deleting staff: ${error.message}`);
    } finally {
      setDeleting(false);
    }
  };
  
  // Get the background color for a role
  const getRoleColor = (role) => {
    const roleOption = roleOptions.find(option => option.value === role);
    return roleOption ? roleOption.color : 'bg-gray-100 text-gray-800';
  };
  
  // Get the label for a role
  const getRoleLabel = (role) => {
    const roleOption = roleOptions.find(option => option.value === role);
    return roleOption ? roleOption.label : role;
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-blue-500 mr-2" />
        <span>Đang tải danh sách nhân viên...</span>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <p>Lỗi: {error}</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Header with add button */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-800">Quản lý nhân viên</h3>
        <button
          onClick={() => setShowAddForm(true)}
          className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center"
          disabled={showAddForm}
        >
          <FaUserPlus className="mr-1" />
          Thêm nhân viên
        </button>
      </div>
      
      {/* Add staff form */}
      {showAddForm && (
        <div className="bg-blue-50 p-4 rounded-md mb-4">
          <h4 className="text-md font-medium mb-3 flex justify-between">
            <span>Thêm nhân viên mới</span>
            <button 
              onClick={() => setShowAddForm(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <FaTimes />
            </button>
          </h4>
          
          <form onSubmit={handleAddStaff} className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Tên nhân viên <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Số điện thoại
                </label>
                <input
                  type="text"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                  Vai trò <span className="text-red-500">*</span>
                </label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                >
                  {roleOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 rounded border-gray-300"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                Kích hoạt tài khoản
              </label>
            </div>
            
            <div className="flex justify-end space-x-2 pt-2">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                Hủy
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                disabled={saving}
              >
                {saving ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <FaSave className="mr-2" />
                    Lưu
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Staff list */}
      <div className="overflow-x-auto">
        {staff.length === 0 ? (
          <div className="text-center p-4 bg-gray-50 rounded-md">
            <p className="text-gray-500">Chưa có nhân viên nào</p>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tên
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vai trò
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {staff.map(member => (
                <tr key={member.id}>
                  {editingStaffId === member.id ? (
                    // Editing mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="w-full p-1 border border-gray-300 rounded-md text-sm"
                          required
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full p-1 border border-gray-300 rounded-md text-sm"
                          required
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          name="role"
                          value={formData.role}
                          onChange={handleInputChange}
                          className="w-full p-1 border border-gray-300 rounded-md text-sm"
                          required
                        >
                          {roleOptions.map(option => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <label className="inline-flex items-center">
                          <input
                            type="checkbox"
                            name="isActive"
                            checked={formData.isActive}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-blue-600 rounded border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">Kích hoạt</span>
                        </label>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex space-x-2">
                          <button 
                            onClick={() => handleUpdateStaff(member.id)}
                            className="text-green-600 hover:text-green-900 flex items-center"
                            disabled={saving}
                          >
                            {saving ? <FaSpinner className="animate-spin" /> : <FaCheck />}
                          </button>
                          <button 
                            onClick={handleEditCancel}
                            className="text-gray-600 hover:text-gray-900"
                          >
                            <FaUndo />
                          </button>
                        </div>
                      </td>
                    </>
                  ) : (
                    // Display mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {member.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {member.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getRoleColor(member.role)}`}>
                          {getRoleLabel(member.role)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          member.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {member.isActive ? 'Hoạt động' : 'Đã khóa'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex space-x-3">
                          <button 
                            onClick={() => handleEditStart(member)} 
                            className="text-blue-600 hover:text-blue-900"
                            disabled={editingStaffId !== null}
                          >
                            <FaUserEdit />
                          </button>
                          <button 
                            onClick={() => handleDeleteStaff(member.id)}
                            className="text-red-600 hover:text-red-900"
                            disabled={deleting}
                          >
                            {deleting ? <FaSpinner className="animate-spin" /> : <FaTrash />}
                          </button>
                        </div>
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default StaffManagement; 