import React, { useState, useEffect } from 'react';
import axios from 'axios';

const IFTopupWidget = ({ orderId, phoneNumber, planId, onTopupComplete, currentUser }) => {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [inventoryProduct, setInventoryProduct] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [orderStatus, setOrderStatus] = useState(null);
  const [isTopupDisabled, setIsTopupDisabled] = useState(false);

  useEffect(() => {
    // Load products and check order status
    const loadData = async () => {
      setIsLoadingProducts(true);
      try {
        // First, look up the product in inventory by SKU (planId is actually a SKU)
        const inventoryResponse = await fetch(`/api/inventory?id=${planId}`);
        const inventoryData = await inventoryResponse.json();
        
        if (inventoryData && inventoryData.sku) {
          setInventoryProduct(inventoryData);
          
          // Check if this product has a sku_manufacturer field
          if (inventoryData.sku_manufacturer) {
            // Load IF products to get the specific plan details
            const productsResponse = await fetch('/api/payment/if/products');
            const productsData = await productsResponse.json();
            
            if (productsData.success && Array.isArray(productsData.products)) {
              setProducts(productsData.products);
              
              // Find the specific product using the sku_manufacturer as the plan ID
              const product = productsData.products.find(p => p.id === inventoryData.sku_manufacturer);
              setSelectedProduct(product);
              
              if (!product) {
                setError(`IF plan not found for manufacturer SKU: ${inventoryData.sku_manufacturer}`);
              }
            } else {
              setError('Failed to load IF products');
            }
          } else {
            setError(`Product ${planId} does not have a manufacturer SKU (sku_manufacturer field)`);
          }
        } else {
          setError(`Product not found for SKU: ${planId}`);
        }

        // Check if this order has already been topped up
        const orderResponse = await fetch(`/api/orders/${orderId}/topup-status`);
        const orderData = await orderResponse.json();
        
        if (orderData.success) {
          setOrderStatus(orderData.status);
          setIsTopupDisabled(orderData.status?.hasTopup || false);
          if (orderData.status?.hasTopup) {
            setSuccess(`Order already topped up by ${orderData.status.toppedUpBy} on ${new Date(orderData.status.toppedUpAt).toLocaleString()}`);
          }
        }
      } catch (err) {
        setError('Failed to load data: ' + err.message);
      } finally {
        setIsLoadingProducts(false);
      }
    };
    
    if (orderId && planId) {
      loadData();
    }
  }, [orderId, planId]);

  const handleTopup = async () => {
    if (!phoneNumber || !selectedProduct || !orderId) {
      setError('Missing required information');
      return;
    }

    // Validate phone number format
    if (!/^\d{10}$/.test(phoneNumber.replace(/\D/g, ''))) {
      setError('Invalid phone number format');
      return;
    }

    if (isTopupDisabled) {
      setError('This order has already been topped up');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await axios.post('/api/payment/if/activate-order', {
        orderId,
        phoneNumber: phoneNumber.replace(/\D/g, ''),
        productId: selectedProduct.id,
        adminUser: currentUser || 'unknown',
        timestamp: new Date().toISOString()
      });

      if (response.data.success) {
        const transactionId = response.data.data?.transactionId || 'N/A';
        setSuccess(`Topup successful! Transaction ID: ${transactionId}`);
        setIsTopupDisabled(true);
        
        // Call callback if provided
        if (onTopupComplete) {
          onTopupComplete({
            success: true,
            transactionId,
            orderId,
            productId: selectedProduct.id,
            adminUser: currentUser || 'unknown'
          });
        }
      } else {
        setError(response.data.error || 'Nạp thất bại');
      }
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Đã có lỗi xảy ra';
      setError(errorMessage);
      
      // Call callback with error if provided
      if (onTopupComplete) {
        onTopupComplete({
          success: false,
          error: errorMessage,
          orderId
        });
      }
    } finally {
      setLoading(false);
    }
  };

  if (isLoadingProducts) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
          <div className="h-8 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    );
  }

  if (!selectedProduct) {
    return (
      <div className="p-4 bg-red-50 rounded-lg">
        <p className="text-red-600">
          {inventoryProduct 
            ? `IF plan not found for SKU: ${planId} (manufacturer SKU: ${inventoryProduct.sku_manufacturer || 'not set'})`
            : `Product not found for SKU: ${planId}`
          }
        </p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white rounded-lg border">
      <h3 className="text-lg font-semibold mb-3">Nạp gói IF - Order #{orderId}</h3>
      
      {error && (
        <div className="mb-3 p-3 bg-red-100 text-red-700 rounded text-sm">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-3 p-3 bg-green-100 text-green-700 rounded text-sm">
          {success}
        </div>
      )}

      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number
          </label>
          <input
            type="text"
            value={phoneNumber}
            readOnly
            className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Gói nạp
          </label>
          <div className="p-3 bg-gray-50 rounded-md">
            {inventoryProduct && (
              <>
                <p className="text-sm font-medium">{inventoryProduct.name}</p>
                <p className="text-xs text-gray-500 mb-2">SKU: {inventoryProduct.sku}</p>
              </>
            )}
            <p className="text-sm font-medium">{selectedProduct.name}</p>
            <p className="text-sm text-gray-600">NT${selectedProduct.price}</p>
            <p className="text-xs text-gray-500">IF Plan ID: {selectedProduct.id}</p>
          </div>
        </div>

        {currentUser && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Người dùng hiện tại: {currentUser}
            </label>
          </div>
        )}

        <button
          onClick={handleTopup}
          disabled={loading || isTopupDisabled}
          className={`w-full py-2 px-4 rounded-md text-white font-medium text-sm ${
            loading || isTopupDisabled
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {loading ? 'Đang xử lý...' : isTopupDisabled ? 'Đã nạp' : 'Nạp'}
        </button>
      </div>

      {orderStatus?.hasTopup && (
        <div className="mt-3 p-3 bg-yellow-50 rounded-md">
          <h4 className="text-sm font-medium text-yellow-800">Lịch sử nạp</h4>
          <p className="text-xs text-yellow-700">
            Topped up by: {orderStatus.toppedUpBy}
          </p>
          <p className="text-xs text-yellow-700">
            Date: {new Date(orderStatus.toppedUpAt).toLocaleString()}
          </p>
          {orderStatus.transactionId && (
            <p className="text-xs text-yellow-700">
              Transaction ID: {orderStatus.transactionId}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default IFTopupWidget; 