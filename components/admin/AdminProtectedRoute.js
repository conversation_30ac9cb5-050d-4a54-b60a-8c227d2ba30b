import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAdminAuth } from '../../context/adminAuthContext';
import { FiLoader } from 'react-icons/fi';

const AdminProtectedRoute = ({ children }) => {
  const { user, loading, isAuthenticated } = useAdminAuth();
  const router = useRouter();
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    // Only redirect if we're sure the user is not authenticated and not loading
    if (!loading && !isAuthenticated && !redirecting) {
      setRedirecting(true);
      router.push('/admin/login');
    }
  }, [loading, isAuthenticated, router, redirecting]);

  // Show loading spinner while checking authentication or redirecting
  if (loading || redirecting) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">
            {redirecting ? 'Redirecting to login...' : 'Verifying authentication...'}
          </p>
        </div>
      </div>
    );
  }

  // If not authenticated, show loading (redirect should be happening)
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // If authenticated, render the protected content
  return children;
};

export default AdminProtectedRoute;
