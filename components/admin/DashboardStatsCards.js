import React from 'react';
import { 
  FaCreditCard, 
  FaShoppingCart, 
  FaFileAlt, 
  FaExclamationTriangle, 
  FaSpinner 
} from 'react-icons/fa';
import { formatCurrency } from '../../utils/formatUtils';

const DashboardStatsCards = ({ stats, loading }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6">
      {/* Total Sales Card */}
      <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
        <div className="flex items-center">
          <FaCreditCard className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 mr-2 flex-shrink-0" />
          <h3 className="text-xs sm:text-sm font-medium text-blue-800 truncate">Tổng doanh số</h3>
        </div>
        {loading.stats ? (
          <div className="flex items-center space-x-2 mt-2">
            <FaSpinner className="animate-spin text-blue-500" />
            <span className="text-sm text-gray-500">Đang tải...</span>
          </div>
        ) : (
          <div className="mt-2">
            {Object.keys(stats.totalSales || {}).length === 0 ? (
              <p className="text-2xl font-bold">0</p>
            ) : (
              Object.entries(stats.totalSales).map(([currency, amount]) => (
                <p key={currency} className="text-lg font-bold mb-1">
                  {formatCurrency(amount, currency)}
                </p>
              ))
            )}
          </div>
        )}
      </div>

      {/* Total Orders Card */}
      <div className="bg-green-50 p-3 sm:p-4 rounded-lg">
        <div className="flex items-center">
          <FaShoppingCart className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2 flex-shrink-0" />
          <h3 className="text-xs sm:text-sm font-medium text-green-800 truncate">Tổng đơn hàng</h3>
        </div>
        {loading.stats ? (
          <div className="flex items-center space-x-2 mt-2">
            <FaSpinner className="animate-spin text-green-500" />
            <span className="text-sm text-gray-500">Đang tải...</span>
          </div>
        ) : (
          <div>
            <p className="text-xl sm:text-2xl font-bold">{stats.totalOrders}</p>
            <p className="text-xs text-green-600 mt-1">
              <span className="block sm:inline">Đã thanh toán: {stats.paidOrders}</span>
              <span className="block sm:inline sm:ml-2">Chưa thanh toán: {stats.unpaidOrders}</span>
            </p>
          </div>
        )}
      </div>

      {/* Pending Approvals Card */}
      <div className="bg-yellow-50 p-3 sm:p-4 rounded-lg">
        <div className="flex items-center">
          <FaFileAlt className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600 mr-2 flex-shrink-0" />
          <h3 className="text-xs sm:text-sm font-medium text-yellow-800 truncate">Chờ phê duyệt</h3>
        </div>
        {loading.documents ? (
          <div className="flex items-center space-x-2 mt-2">
            <FaSpinner className="animate-spin text-yellow-500" />
            <span className="text-sm text-gray-500">Đang tải...</span>
          </div>
        ) : (
          <p className="text-xl sm:text-2xl font-bold">{stats.pendingApprovals}</p>
        )}
      </div>

      {/* Expired Orders Card */}
      <div className="bg-red-50 p-3 sm:p-4 rounded-lg">
        <div className="flex items-center">
          <FaExclamationTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-red-600 mr-2 flex-shrink-0" />
          <h3 className="text-xs sm:text-sm font-medium text-red-800 truncate">Đã hết hạn</h3>
        </div>
        {loading.stats ? (
          <div className="flex items-center space-x-2 mt-2">
            <FaSpinner className="animate-spin text-red-500" />
            <span className="text-sm text-gray-500">Đang tải...</span>
          </div>
        ) : (
          <div>
            <p className="text-xl sm:text-2xl font-bold">{stats.expiredOrders}</p>
            <p className="text-xs text-red-600 mt-1">
              Cần xử lý ngay lập tức
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardStatsCards;
