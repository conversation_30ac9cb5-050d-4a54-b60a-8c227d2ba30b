import React, { useState, useEffect } from 'react';
import axios from 'axios';

const IFTopup = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [products, setProducts] = useState([]);
  const [categorizedProducts, setCategorizedProducts] = useState({ topup: [], wireless: [] });
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);

  useEffect(() => {
    // Load products from config
    const loadProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const response = await fetch('/api/payment/if/products');
        const data = await response.json();
        if (data.success && Array.isArray(data.products)) {
          setProducts(data.products);
          setCategorizedProducts(data.categorized || { topup: [], wireless: [] });
        } else {
          setError('Invalid products data received');
          setProducts([]);
          setCategorizedProducts({ topup: [], wireless: [] });
        }
      } catch (err) {
        setError('Failed to load products: ' + err.message);
        setProducts([]);
        setCategorizedProducts({ topup: [], wireless: [] });
      } finally {
        setIsLoadingProducts(false);
      }
    };
    loadProducts();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!phoneNumber || !selectedProduct) {
      setError('Please fill in all fields');
      return;
    }

    // Validate phone number format
    if (!/^\d{10}$/.test(phoneNumber)) {
      setError('Phone number must be exactly 10 digits');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await axios.post('/api/payment/if/activate', {
        phoneNumber,
        productId: selectedProduct.id,
        orderId: `order_${Date.now()}`
      });

      if (response.data.success) {
        setSuccess(`Topup successful! Transaction ID: ${response.data.data?.transactionId || 'N/A'}`);
        setPhoneNumber('');
        setSelectedProduct(null);
      } else {
        setError(response.data.error || 'Topup failed');
      }
    } catch (err) {
      setError(err.response?.data?.error || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-4 sm:p-6">
      <h2 className="text-xl sm:text-2xl font-bold text-center mb-4 sm:mb-6 text-gray-800">Nạp tiền IF</h2>
      
      {error && (
        <div className="mb-3 sm:mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-3 sm:mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded text-sm">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
        <div>
          <label htmlFor="phoneNumber" className="block text-xs sm:text-sm font-medium text-gray-700 mb-2">
            Số điện thoại (10 chữ số)
          </label>
          <input
            type="tel"
            id="phoneNumber"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, ''))}
            placeholder="Ví dụ: 0901234567"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
            required
            pattern="[0-9]{10}"
            maxLength="10"
          />
        </div>

        <div>
          <label htmlFor="product" className="block text-xs sm:text-sm font-medium text-gray-700 mb-2">
            Chọn sản phẩm
          </label>
          {isLoadingProducts ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-5 w-5 sm:h-6 sm:w-6 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600 text-sm">Đang tải sản phẩm...</span>
            </div>
          ) : (
            <select
              id="product"
              value={selectedProduct?.id || ''}
              onChange={(e) => {
                const product = products.find(p => p.id === e.target.value);
                setSelectedProduct(product);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
              required
            >
              <option value="">Chọn sản phẩm...</option>
              
              {/* Topup Products */}
              {categorizedProducts.topup && categorizedProducts.topup.length > 0 && (
                <optgroup label="Nạp tiền ngay">
                  {categorizedProducts.topup.map((product) => (
                    <option key={product.id} value={product.id}>
                      {product.name} - {product.price.toLocaleString('vi-VN')}đ
                    </option>
                  ))}
                </optgroup>
              )}
              
              {/* Wireless Products */}
              {categorizedProducts.wireless && categorizedProducts.wireless.length > 0 && (
                <optgroup label="Gói cước di động">
                  {categorizedProducts.wireless.map((product) => (
                    <option key={product.id} value={product.id}>
                      {product.name} - {product.price.toLocaleString('vi-VN')}đ
                    </option>
                  ))}
                </optgroup>
              )}
            </select>
          )}
        </div>

        <button
          type="submit"
          disabled={loading || !phoneNumber || !selectedProduct}
          className="w-full bg-blue-500 text-white py-2 sm:py-3 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-sm sm:text-base font-medium"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              <span>Đang xử lý...</span>
            </>
          ) : (
            'Nạp tiền'
          )}
        </button>
      </form>

      {selectedProduct && (
        <div className="mt-4 p-4 bg-gray-50 rounded-md">
          <h3 className="font-medium">Chi tiết sản phẩm đã chọn:</h3>
          <p><strong>Tên:</strong> {selectedProduct.name}</p>
          <p><strong>Giá:</strong> NT${selectedProduct.price}</p>
          <p><strong>Mã sản phẩm:</strong> {selectedProduct.id}</p>
        </div>
      )}

      {!isLoadingProducts && products.length > 0 && (
        <div className="mt-4 text-sm text-gray-600">
          <p>Số sản phẩm có sẵn: {products.length}</p>
          <p>Gói nạp tiền: {categorizedProducts.topup?.length || 0}</p>
          <p>Gói cước di động: {categorizedProducts.wireless?.length || 0}</p>
        </div>
      )}
    </div>
  );
};

export default IFTopup;