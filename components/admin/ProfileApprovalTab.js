import { useState, useEffect } from 'react';
import { FiUser, FiPhone, FiMail, FiMapPin, FiCalendar, FiCheck, FiX, FiClock, FiEdit3 } from 'react-icons/fi';
import ApprovalStatus from '../profile/ApprovalStatus';

const ProfileApprovalTab = ({ customerId, customerData }) => {
  const [pendingApprovals, setPendingApprovals] = useState([]);
  const [approvalHistory, setApprovalHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedField, setSelectedField] = useState(null);

  // Profile fields that can be edited by customers
  const profileFields = [
    { key: 'fullName', label: 'Họ và tên', icon: FiUser, type: 'text' },
    { key: 'dateOfBirth', label: '<PERSON><PERSON><PERSON> sinh', icon: Fi<PERSON>alendar, type: 'date' },
    { key: 'gender', label: 'Giới tính', icon: FiUser, type: 'select' },
    { key: 'passportNumber', label: 'Số hộ chiếu', icon: FiUser, type: 'text' },
    { key: 'email', label: 'Email', icon: FiMail, type: 'email' },
    { key: 'phone', label: 'Số điện thoại', icon: FiPhone, type: 'tel' },
    { key: 'address', label: 'Địa chỉ', icon: FiMapPin, type: 'text' }
  ];

  useEffect(() => {
    if (customerId) {
      fetchPendingApprovals();
      fetchApprovalHistory();
    }
  }, [customerId]);

  const fetchPendingApprovals = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/customer-approval?customerId=${customerId}&status=pending`);
      if (response.ok) {
        const data = await response.json();
        setPendingApprovals(data.approvals || []);
      } else {
        console.error('Failed to fetch pending approvals');
        setPendingApprovals([]);
      }
    } catch (error) {
      console.error('Error fetching pending approvals:', error);
      setPendingApprovals([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchApprovalHistory = async () => {
    try {
      const response = await fetch(`/api/admin/approval-history?customerId=${customerId}`);
      if (response.ok) {
        const data = await response.json();
        setApprovalHistory(data.history || []);
      } else {
        console.error('Failed to fetch approval history');
        setApprovalHistory([]);
      }
    } catch (error) {
      console.error('Error fetching approval history:', error);
      setApprovalHistory([]);
    }
  };

  const handleApproval = async (approvalId, action, reason = '') => {
    try {
      const response = await fetch('/api/admin/customer-approval', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approvalId,
          action,
          reason,
          adminId: 'current-admin' // This should come from auth context
        })
      });

      if (response.ok) {
        // Refresh data after successful action
        await fetchPendingApprovals();
        await fetchApprovalHistory();
        
        // Reset modal state
        setShowRejectModal(false);
        setSelectedField(null);
        setRejectionReason('');
      } else {
        console.error('Failed to process approval');
      }
    } catch (error) {
      console.error('Error processing approval:', error);
    }
  };

  const handleReject = (approval) => {
    setSelectedField(approval);
    setShowRejectModal(true);
  };

  const confirmReject = () => {
    if (selectedField && rejectionReason.trim()) {
      handleApproval(selectedField.id, 'reject', rejectionReason);
    }
  };

  const getFieldIcon = (fieldKey) => {
    const field = profileFields.find(f => f.key === fieldKey);
    return field ? field.icon : FiEdit3;
  };

  const getFieldLabel = (fieldKey) => {
    const field = profileFields.find(f => f.key === fieldKey);
    return field ? field.label : fieldKey;
  };

  const formatValue = (value, fieldKey) => {
    if (!value) return 'N/A';
    
    switch (fieldKey) {
      case 'dateOfBirth':
        return new Date(value).toLocaleDateString('vi-VN');
      case 'gender':
        return value === 'male' ? 'Nam' : value === 'female' ? 'Nữ' : value;
      default:
        return value;
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Đang tải dữ liệu duyệt hồ sơ...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Pending Approvals Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <FiClock className="w-5 h-5 mr-2 text-yellow-500" />
            Chờ duyệt ({pendingApprovals.length})
          </h3>
        </div>

        {pendingApprovals.length === 0 ? (
          <div className="text-center py-8">
            <FiCheck className="w-12 h-12 mx-auto text-green-500 mb-4" />
            <p className="text-gray-500">Không có thay đổi nào cần duyệt</p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingApprovals.map((approval) => {
              const IconComponent = getFieldIcon(approval.field);
              
              return (
                <div key={approval.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <IconComponent className="w-5 h-5 text-blue-600" />
                      </div>
                      
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900">
                          {getFieldLabel(approval.field)}
                        </h4>
                        
                        <div className="mt-2 grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Giá trị hiện tại:</p>
                            <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                              {formatValue(approval.currentValue, approval.field)}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Giá trị mới:</p>
                            <p className="text-sm text-gray-900 bg-blue-50 p-2 rounded border border-blue-200">
                              {formatValue(approval.newValue, approval.field)}
                            </p>
                          </div>
                        </div>
                        
                        <div className="mt-2">
                          <p className="text-xs text-gray-500">
                            Yêu cầu lúc: {new Date(approval.requestedAt).toLocaleString('vi-VN')}
                          </p>
                          {approval.reason && (
                            <p className="text-xs text-gray-600 mt-1">
                              Lý do: {approval.reason}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleApproval(approval.id, 'approve')}
                        className="flex items-center px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                      >
                        <FiCheck className="w-4 h-4 mr-1" />
                        Duyệt
                      </button>
                      <button
                        onClick={() => handleReject(approval)}
                        className="flex items-center px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700"
                      >
                        <FiX className="w-4 h-4 mr-1" />
                        Từ chối
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Approval History Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Lịch sử duyệt</h3>
        
        {approvalHistory.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Chưa có lịch sử duyệt</p>
          </div>
        ) : (
          <div className="space-y-4">
            {approvalHistory.map((history, index) => {
              const IconComponent = getFieldIcon(history.field);
              
              return (
                <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <IconComponent className="w-4 h-4 text-gray-600" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">
                        {getFieldLabel(history.field)}
                      </h4>
                      <ApprovalStatus 
                        status={history.status}
                        date={history.processedAt}
                        tooltip={history.reason}
                      />
                    </div>
                    
                    <div className="mt-2 text-xs text-gray-600">
                      <p>Từ: {formatValue(history.oldValue, history.field)}</p>
                      <p>Thành: {formatValue(history.newValue, history.field)}</p>
                      <p>Xử lý lúc: {new Date(history.processedAt).toLocaleString('vi-VN')}</p>
                      {history.processedBy && (
                        <p>Bởi: {history.processedBy}</p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Rejection Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Từ chối thay đổi</h3>
            
            <p className="text-sm text-gray-600 mb-4">
              Bạn đang từ chối thay đổi cho trường: <strong>{getFieldLabel(selectedField?.field)}</strong>
            </p>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lý do từ chối *
              </label>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                rows={3}
                placeholder="Nhập lý do từ chối..."
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setSelectedField(null);
                  setRejectionReason('');
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Hủy
              </button>
              <button
                onClick={confirmReject}
                disabled={!rejectionReason.trim()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Xác nhận từ chối
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileApprovalTab;