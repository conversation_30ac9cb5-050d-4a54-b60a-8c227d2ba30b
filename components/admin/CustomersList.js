import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaUserCircle } from 'react-icons/fa';

const CustomersList = ({ onSelectCustomer }) => {
const [customers, setCustomers] = useState([]);
const [loading, setLoading] = useState(true);
const [searchTerm, setSearchTerm] = useState('');
const [filters, setFilters] = useState({
  status: 'all',
  registrationDate: 'all',
  hasOrders: 'all',
});
const [showFilters, setShowFilters] = useState(false);

useEffect(() => {
  const fetchCustomers = async () => {
    try {
      setLoading(true);
      
      // Fetch customers from API endpoint instead of directly from JSON file
      const response = await fetch('/api/customers');
      const data = await response.json();
      const customersData = data.customers || data;
      
      // Transform the data structure to match our UI needs
      const formattedCustomers = customersData.map(customer => {
        // Initialize with default values
        return {
          id: customer.id,
          fullName: customer.personalDetails?.name || customer.name || `${customer.personalDetails?.firstName || ''} ${customer.personalDetails?.lastName || ''}`,
          email: customer.contactInfo?.email || customer.email || '',
          phone: customer.contactInfo?.primaryPhone || customer.phone || '',
          registrationDate: customer.membershipInfo?.memberSince || customer.createdAt || new Date().toISOString(),
          status: customer.accessControl?.accountStatus || 'active',
          totalOrders: 0, // Will be updated from orders API
          totalSpent: 0, // Will be updated from orders API
          address: customer.addresses && customer.addresses.length > 0 
            ? `${customer.addresses[0].street}, ${customer.addresses[0].district}, ${customer.addresses[0].city}` 
            : customer.address || '',
          membershipLevel: customer.membershipInfo?.membershipLevel || 'standard'
        };
      });
      
      // Get order data for each customer
      const customersWithOrders = await Promise.all(
        formattedCustomers.map(async (customer) => {
          try {
            const ordersResponse = await fetch(`/api/orders/customer/${customer.id}`);
            if (ordersResponse.ok) {
              const orderData = await ordersResponse.json();
              
              if (orderData.success && orderData.orders) {
                // Calculate total spent from actual orders
                const totalSpent = orderData.orders.reduce((sum, order) => {
                  return sum + (order.totalAmount || order.amount || 0);
                }, 0);
                
                return {
                  ...customer,
                  totalOrders: orderData.total || orderData.orders.length,
                  totalSpent: totalSpent
                };
              }
            }
            return customer; // Return customer with default values if order fetch fails
          } catch (error) {
            console.error(`Error fetching orders for customer ${customer.id}:`, error);
            return customer; // Return customer with default values if order fetch fails
          }
        })
      );
      
      setCustomers(customersWithOrders);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching customers:', error);
      // Set empty array if there's an error fetching customers
      setCustomers([]);
      setLoading(false);
    }
  };

  fetchCustomers();
}, []);

const applyFilters = (customer) => {
  // Filter by search term
  const matchesSearch = 
    customer.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm);
  
  // Filter by status
  const matchesStatus = 
    filters.status === 'all' || customer.status === filters.status;
  
  // Filter by registration date
  let matchesDate = true;
  const today = new Date();
  const registrationDate = new Date(customer.registrationDate);
  const daysDifference = Math.floor((today - registrationDate) / (1000 * 60 * 60 * 24));
  
  if (filters.registrationDate === 'last_week') {
    matchesDate = daysDifference <= 7;
  } else if (filters.registrationDate === 'last_month') {
    matchesDate = daysDifference <= 30;
  } else if (filters.registrationDate === 'last_year') {
    matchesDate = daysDifference <= 365;
  }
  
  // Filter by orders
  let matchesOrders = true;
  if (filters.hasOrders === 'with_orders') {
    matchesOrders = customer.totalOrders > 0;
  } else if (filters.hasOrders === 'no_orders') {
    matchesOrders = customer.totalOrders === 0;
  }
  
  return matchesSearch && matchesStatus && matchesDate && matchesOrders;
};

const filteredCustomers = customers.filter(applyFilters);

return (
  <div className="bg-white rounded-lg shadow">
    <div className="p-3 sm:p-4 border-b">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
            placeholder="Tìm kiếm khách hàng theo tên, email, số điện thoại..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button
          className="flex items-center justify-center px-3 sm:px-4 py-2 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 text-sm sm:text-base"
          onClick={() => setShowFilters(!showFilters)}
        >
          <FaFilter className="mr-2" />
          <span className="hidden sm:inline">Bộ lọc</span>
          <span className="sm:hidden">Lọc</span>
        </button>
      </div>

      {showFilters && (
        <div className="mt-3 sm:mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
            <select
              className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            >
              <option value="all">Tất cả</option>
              <option value="active">Đang hoạt động</option>
              <option value="inactive">Không hoạt động</option>
            </select>
          </div>
          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Ngày đăng ký</label>
            <select
              className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              value={filters.registrationDate}
              onChange={(e) => setFilters({ ...filters, registrationDate: e.target.value })}
            >
              <option value="all">Tất cả thời gian</option>
              <option value="last_week">7 ngày qua</option>
              <option value="last_month">30 ngày qua</option>
              <option value="last_year">365 ngày qua</option>
            </select>
          </div>
          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Đơn hàng</label>
            <select
              className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              value={filters.hasOrders}
              onChange={(e) => setFilters({ ...filters, hasOrders: e.target.value })}
            >
              <option value="all">Tất cả</option>
              <option value="with_orders">Có đơn hàng</option>
              <option value="no_orders">Chưa có đơn hàng</option>
            </select>
          </div>
        </div>
      )}
    </div>

    {loading ? (
      <div className="flex justify-center items-center p-8 sm:p-12">
        <FaSpinner className="animate-spin text-blue-500 mr-2" />
        <span className="text-sm sm:text-base">Đang tải danh sách khách hàng...</span>
      </div>
    ) : filteredCustomers.length > 0 ? (
      <>
        {/* Responsive Card Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
          {filteredCustomers.map((customer) => (
            <div 
              key={customer.id} 
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onSelectCustomer(customer.id)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <FaUserCircle className="h-8 w-8 text-gray-400 mr-3" />
                  <div>
                    <h3 className="font-medium text-gray-900 text-sm">{customer.fullName}</h3>
                    <p className="text-xs text-gray-500">ID: {customer.id}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  customer.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {customer.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center text-gray-600">
                  <span className="w-4 text-center mr-2">📧</span>
                  <span className="truncate">{customer.email}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <span className="w-4 text-center mr-2">📱</span>
                  <span>{customer.phone}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-gray-600">
                    <span className="w-4 text-center mr-2">🏆</span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      customer.membershipLevel === 'gold' ? 'bg-yellow-100 text-yellow-800' : 
                      customer.membershipLevel === 'silver' ? 'bg-gray-100 text-gray-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {customer.membershipLevel === 'gold' ? 'Vàng' : 
                        customer.membershipLevel === 'silver' ? 'Bạc' : 'Chuẩn'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center text-gray-600">
                  <span className="w-4 text-center mr-2">📦</span>
                  <span>{customer.totalOrders} đơn hàng</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <span className="w-4 text-center mr-2">💰</span>
                  <span className="font-medium text-gray-900">{customer.totalSpent.toLocaleString('vi-VN')}đ</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <span className="w-4 text-center mr-2">📅</span>
                  <span>{new Date(customer.registrationDate).toLocaleDateString('vi-VN')}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </>
    ) : (
      <div className="p-6 sm:p-8 text-center text-gray-500 text-sm sm:text-base">
        Không tìm thấy khách hàng nào phù hợp với bộ lọc đã chọn.
      </div>
    )}
  </div>
);
};

export default CustomersList;