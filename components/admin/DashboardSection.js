import React, { useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';

// Admin Dashboard Section component
const DashboardSection = ({ title, icon, children, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="mb-4 border border-gray-200 rounded-lg shadow-sm">
      <div 
        className={`flex items-center justify-between p-3 sm:p-4 cursor-pointer ${isOpen ? 'bg-blue-50' : 'bg-white'} transition-colors duration-200`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center min-w-0 flex-1">
          <div className="flex-shrink-0">
            {icon}
          </div>
          <h2 className="ml-2 text-base sm:text-lg font-medium text-gray-800 truncate">{title}</h2>
        </div>
        <div className="flex-shrink-0 ml-2">
          {isOpen ? <FaChevronUp className="text-gray-500 h-4 w-4" /> : <FaChevronDown className="text-gray-500 h-4 w-4" />}
        </div>
      </div>
      
      {isOpen && (
        <div className="p-3 sm:p-4 border-t border-gray-200">
          {children}
        </div>
      )}
    </div>
  );
};

export default DashboardSection;
