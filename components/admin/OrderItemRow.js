import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash, faUser } from '@fortawesome/free-solid-svg-icons';
import IFTopupWidget from './IFTopupWidget';
import { getProductImage, handleImageError } from '../../utils/imageUtils';

const OrderItemRow = ({ 
  item, 
  index, 
  order, 
  formatCurrency, 
  editingItemIndex, 
  setEditingItemIndex,
  selectedFieldType,
  setSelectedFieldType,
  fieldValue,
  setFieldValue,
  fieldTypes,
  handleSaveItemField,
  updateLoading,
  handleDeleteItemField,
  deleteLoading,
  currentUser
}) => {
  // Helper function to get field metadata
  const getFieldMetadata = (fieldKey) => {
    return item._fieldMetadata?.[fieldKey];
  };

  // Helper function to check if current user can delete a field
  const canDeleteField = (fieldKey) => {
    if (!currentUser) return true; // Admin can delete anything if no user context
    
    const metadata = getFieldMetadata(fieldKey);
    if (!metadata) return true; // System fields can be deleted by admin
    
    // Admin can delete their own fields, or fields without ownership info
    return !metadata.addedBy || metadata.addedBy === currentUser.id;
  };

  // Helper function to render field with delete button and ownership indicator
  const renderFieldWithDelete = (label, value, fieldKey) => {
    const metadata = getFieldMetadata(fieldKey);
    const canDelete = canDeleteField(fieldKey);
    const isUserAdded = metadata?.isUserAdded || false;
    const addedByUser = metadata?.addedByName;

    return (
      <div className="flex justify-between items-center group">
        <div className="flex-1">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-900">{label}:</span>
            <span className="text-sm text-gray-700 ml-1">{value}</span>
            {isUserAdded && addedByUser && (
              <div className="ml-2 flex items-center" title={`Được thêm bởi: ${addedByUser}`}>
                <FontAwesomeIcon icon={faUser} className="h-2 w-2 text-blue-500" />
                <span className="text-xs text-blue-500 ml-1">({addedByUser})</span>
              </div>
            )}
          </div>
        </div>
        {canDelete && (
          <button
            onClick={() => handleDeleteItemField(index, fieldKey)}
            disabled={deleteLoading === `${index}-${fieldKey}` || updateLoading}
            className="opacity-0 group-hover:opacity-100 transition-opacity p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded ml-2"
            title={`Xóa ${label.toLowerCase()}`}
          >
            {deleteLoading === `${index}-${fieldKey}` ? (
              <span className="text-xs">...</span>
            ) : (
              <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
            )}
          </button>
        )}
        {!canDelete && (
          <div className="ml-2 text-xs text-gray-400" title="Bạn không thể xóa trường này">
            🔒
          </div>
        )}
      </div>
    );
  };

  // Helper function to render regular fields (system fields without delete functionality)
  const renderField = (label, value) => (
    <div>
      <span className="text-sm font-medium text-gray-900">{label}:</span>
      <span className="text-sm text-gray-700 ml-1 capitalize">{value}</span>
    </div>
  );

  return (
    <tr key={index}>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-md overflow-hidden mr-3">
            <img 
              src={getProductImage(item.image, item.sku, item.name)} 
              alt={item.name || item.productName || 'Sản phẩm'} 
              className="h-full w-full object-cover"
              onError={(e) => handleImageError(e, item.sku, item.name)}
            />
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">{item.name || item.productName || 'Sản phẩm'}</div>
            {item.productId && <div className="text-sm text-gray-500">Mã: {item.productId}</div>}
            {item.sku && <div className="text-sm text-gray-500">SKU: {item.sku}</div>}
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {formatCurrency(item.price, order.currency)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {item.quantity}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        {formatCurrency(item.price * item.quantity, order.currency)}
      </td>
      <td className="px-6 py-4 whitespace-normal">
        <div className="space-y-1">
          {/* Item type indicator - system field, no delete option */}
          {item.type && renderField("Loại", item.type)}
          
          {/* SIM specific fields */}
          {(item.simNumber || item.phoneNumber || item.iccid || item.networkProvider) && (
            <>
              {item.simNumber && renderFieldWithDelete("Số SIM", item.simNumber, "simNumber")}
              {item.phoneNumber && renderFieldWithDelete("Số điện thoại", item.phoneNumber, "phoneNumber")}
              {item.iccid && renderFieldWithDelete("ICCID", item.iccid, "iccid")}
              {item.networkProvider && renderFieldWithDelete("Nhà mạng", item.networkProvider, "networkProvider")}
            </>
          )}
          
          {/* Topup card specific fields */}
          {(item.cardNumber || item.cardPin || item.cardSerial || item.topupCode || item.expiryDate) && (
            <>
              {item.cardSerial && renderFieldWithDelete("Serial thẻ", item.cardSerial, "cardSerial")}
              {item.cardNumber && renderFieldWithDelete("Mã thẻ", item.cardNumber, "cardNumber")}
              {item.cardPin && renderFieldWithDelete("Mã PIN", item.cardPin, "cardPin")}
              {item.topupCode && renderFieldWithDelete("Mã nạp", item.topupCode, "topupCode")}
              {item.expiryDate && renderFieldWithDelete("Hạn dùng", item.expiryDate, "expiryDate")}
            </>
          )}
          
          {/* Device specific fields */}
          {(item.imei || item.model || item.color) && (
            <>
              {item.model && renderFieldWithDelete("Model", item.model, "model")}
              {item.imei && renderFieldWithDelete("IMEI", item.imei, "imei")}
              {item.color && renderFieldWithDelete("Màu sắc", item.color, "color")}
            </>
          )}
          
          {/* Service specific fields */}
          {(item.servicePeriod || item.serviceStartDate || item.serviceEndDate) && (
            <>
              {item.servicePeriod && renderFieldWithDelete("Thời hạn", item.servicePeriod, "servicePeriod")}
              {item.serviceStartDate && renderFieldWithDelete("Ngày bắt đầu", item.serviceStartDate, "serviceStartDate")}
              {item.serviceEndDate && renderFieldWithDelete("Ngày kết thúc", item.serviceEndDate, "serviceEndDate")}
            </>
          )}

          {/* Legacy fields for backward compatibility */}
          {item.topupNumber && renderFieldWithDelete("Số nạp tiền", item.topupNumber, "topupNumber")}
          {item.cardCode && renderFieldWithDelete("Mã thẻ (legacy)", item.cardCode, "cardCode")}
          {item.activationCode && renderFieldWithDelete("Mã kích hoạt", item.activationCode, "activationCode")}
          {item.pinCode && renderFieldWithDelete("Mã PIN (legacy)", item.pinCode, "pinCode")}
          {item.account && renderFieldWithDelete("Tài khoản", item.account, "account")}
          {item.password && renderFieldWithDelete("Mật khẩu", item.password, "password")}
          {item.productDetails && renderFieldWithDelete("Thông tin khác", item.productDetails, "productDetails")}
          
          {/* IF Auto Topup Widget */}
          {/* {item.auto_activate === true && ( */}
          {true && (
            <div className="mt-3 border-t pt-3">
              <IFTopupWidget
                orderId={order.id}
                phoneNumber={item.phoneNumber || item.simNumber || order.customerPhone || ''}
                planId={item.productId || item.sku || ''}
                currentUser={currentUser?.id || currentUser?.name || currentUser || 'admin'}
                onTopupComplete={(result) => {
                  if (result.success) {
                    console.log('IF Topup completed for order:', order.id, 'Transaction:', result.transactionId);
                    // You can add additional logic here like refreshing the order data
                  } else {
                    console.error('IF Topup failed for order:', order.id, 'Error:', result.error);
                  }
                }}
              />
            </div>
          )}
          
          {editingItemIndex === index ? (
            <div className="mt-2 border border-gray-200 p-2 rounded-md bg-gray-50">
              <div className="mb-2">
                <label className="text-xs text-gray-600 block mb-1">Loại thông tin:</label>
                <select 
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                  value={selectedFieldType}
                  onChange={(e) => setSelectedFieldType(e.target.value)}
                >
                  <option value="">-- Chọn loại thông tin --</option>
                  {fieldTypes.map((type) => (
                    <option key={type.key} value={type.key}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="mb-2">
                <label className="text-xs text-gray-600 block mb-1">Giá trị:</label>
                <input 
                  type="text"
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                  value={fieldValue}
                  onChange={(e) => setFieldValue(e.target.value)}
                  placeholder={`Nhập ${fieldTypes.find(t => t.key === selectedFieldType)?.label || 'giá trị'}`}
                />
              </div>
              
              <div className="flex space-x-2">
                <button 
                  className="px-2 py-1 text-xs text-white bg-blue-600 rounded hover:bg-blue-700 flex-1"
                  onClick={() => handleSaveItemField(index)}
                  disabled={updateLoading || !selectedFieldType || !fieldValue || deleteLoading !== ''}
                >
                  {updateLoading ? 'Đang lưu...' : 'Lưu'}
                </button>
                <button 
                  className="px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded hover:bg-gray-200 flex-1"
                  onClick={() => {
                    setEditingItemIndex(null);
                    setSelectedFieldType('');
                    setFieldValue('');
                  }}
                  disabled={deleteLoading !== ''}
                >
                  Hủy
                </button>
              </div>
            </div>
          ) : (
            <div>
              <button 
                className="px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 border border-blue-600 rounded hover:bg-blue-50"
                onClick={() => {
                  setEditingItemIndex(index);
                  setSelectedFieldType('');
                  setFieldValue('');
                }}
                disabled={deleteLoading !== ''}
              >
                + Thêm thông tin
              </button>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

export default OrderItemRow; 