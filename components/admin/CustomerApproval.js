import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser, faCalendar, faVenusMars, faPassport, faBuilding,
  faPlaneArrival, faMapMarkerAlt, faGlobe, faIdCard, faStar,
  faEnvelope, faPhone, faHome, faCheck, faTimes, faUserShield,
  faClock, faCheckCircle, faTimesCircle, faEdit, faSave,
  faExclamationTriangle, faSpinner
} from '@fortawesome/free-solid-svg-icons';
import ApprovalHistory from './ApprovalHistory';

const CustomerApproval = () => {
  const [pendingCustomers, setPendingCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [filter, setFilter] = useState('all'); // all, pending, approved, rejected

  useEffect(() => {
    fetchPendingCustomers();
  }, [filter]);

  const fetchPendingCustomers = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/customer-approval?filter=${filter}`);
      const data = await response.json();
      
      if (data.success) {
        setPendingCustomers(data.customers || []);
      } else {
        console.error('Error fetching customers:', data.error);
        setPendingCustomers([]);
      }
    } catch (error) {
      console.error('Error fetching pending customers:', error);
      setPendingCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleApproval = async (customerId, section, status, reason = '') => {
    try {
      setProcessing(true);
      
      const response = await fetch('/api/admin/customer-approval', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId,
          section,
          status,
          reason,
          adminId: 'admin' // TODO: Get actual admin ID from auth context
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Log approval history
        await fetch('/api/admin/approval-history', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            customerId,
            section,
            oldStatus: selectedCustomer?.approvalStatus?.[section] || 'pending',
            newStatus: status,
            reason,
            adminId: 'admin' // TODO: Get actual admin ID from auth context
          })
        });
        
        // Update local state
        setPendingCustomers(prev => 
          prev.map(customer => {
            if (customer.id === customerId) {
              return data.customer;
            }
            return customer;
          })
        );
        
        // Update selected customer if it's the one being modified
        if (selectedCustomer && selectedCustomer.id === customerId) {
          setSelectedCustomer(data.customer);
        }
      } else {
        console.error('Error updating approval status:', data.error);
        alert('Có lỗi xảy ra khi cập nhật trạng thái duyệt');
      }
      
    } catch (error) {
      console.error('Error updating approval status:', error);
      alert('Có lỗi xảy ra khi cập nhật trạng thái duyệt');
    } finally {
      setProcessing(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <FontAwesomeIcon icon={faCheckCircle} className="text-green-500" />;
      case 'rejected':
        return <FontAwesomeIcon icon={faTimesCircle} className="text-red-500" />;
      case 'pending':
      default:
        return <FontAwesomeIcon icon={faClock} className="text-yellow-500" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'approved':
        return 'Đã duyệt';
      case 'rejected':
        return 'Từ chối';
      case 'pending':
      default:
        return 'Chờ duyệt';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const filteredCustomers = pendingCustomers.filter(customer => {
    if (filter === 'all') return true;
    const statuses = Object.values(customer.approvalStatus);
    if (filter === 'pending') return statuses.includes('pending');
    if (filter === 'approved') return statuses.every(status => status === 'approved');
    if (filter === 'rejected') return statuses.includes('rejected');
    return true;
  });

  const ApprovalSection = ({ title, data, section, customer }) => {
    const [reason, setReason] = useState('');
    const [showReasonInput, setShowReasonInput] = useState(false);
    
    const status = customer.approvalStatus[section];
    const existingReason = customer[`${section}Reason`] || '';

    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            {getStatusIcon(status)}
            <span className="ml-2">{title}</span>
            <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
              {getStatusText(status)}
            </span>
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {Object.entries(data).map(([key, value]) => (
            <div key={key} className="flex justify-between py-2 border-b border-gray-100">
              <span className="font-medium text-gray-600 capitalize">
                {key.replace(/([A-Z])/g, ' $1').trim()}:
              </span>
              <span className="text-gray-900">{value || 'N/A'}</span>
            </div>
          ))}
        </div>

        {existingReason && (
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <p className="text-sm text-gray-600">
              <strong>Lý do:</strong> {existingReason}
            </p>
          </div>
        )}

        {status === 'pending' && (
          <div className="space-y-3">
            {showReasonInput && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lý do (tùy chọn):
                </label>
                <textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="2"
                  placeholder="Nhập lý do approve/reject..."
                />
              </div>
            )}
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  if (!showReasonInput) {
                    setShowReasonInput(true);
                  } else {
                    handleApproval(customer.id, section, 'approved', reason);
                    setReason('');
                    setShowReasonInput(false);
                  }
                }}
                disabled={processing}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {processing ? (
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                ) : (
                  <FontAwesomeIcon icon={faCheck} className="mr-2" />
                )}
                Duyệt
              </button>
              
              <button
                onClick={() => {
                  if (!showReasonInput) {
                    setShowReasonInput(true);
                  } else {
                    handleApproval(customer.id, section, 'rejected', reason);
                    setReason('');
                    setShowReasonInput(false);
                  }
                }}
                disabled={processing}
                className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {processing ? (
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                ) : (
                  <FontAwesomeIcon icon={faTimes} className="mr-2" />
                )}
                Từ chối
              </button>
              
              {showReasonInput && (
                <button
                  onClick={() => {
                    setReason('');
                    setShowReasonInput(false);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Hủy
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FontAwesomeIcon icon={faSpinner} className="animate-spin text-blue-500 mr-2" />
        <span>Đang tải danh sách khách hàng...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <FontAwesomeIcon icon={faUserShield} className="mr-3 text-blue-600" />
            Duyệt thông tin khách hàng
          </h2>
          <p className="text-gray-600 mt-1">Xem xét và duyệt thông tin khách hàng đã gửi</p>
        </div>
        
        <div className="mt-4 sm:mt-0">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Tất cả</option>
            <option value="pending">Chờ duyệt</option>
            <option value="approved">Đã duyệt</option>
            <option value="rejected">Đã từ chối</option>
          </select>
        </div>
      </div>

      {/* Customer List */}
      {!selectedCustomer ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Danh sách khách hàng ({filteredCustomers.length})
            </h3>
          </div>
          
          <div className="divide-y divide-gray-200">
            {filteredCustomers.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                Không có khách hàng nào cần duyệt
              </div>
            ) : (
              filteredCustomers.map((customer) => {
                const pendingCount = Object.values(customer.approvalStatus).filter(status => status === 'pending').length;
                const approvedCount = Object.values(customer.approvalStatus).filter(status => status === 'approved').length;
                const rejectedCount = Object.values(customer.approvalStatus).filter(status => status === 'rejected').length;
                
                return (
                  <div
                    key={customer.id}
                    className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => setSelectedCustomer(customer)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <FontAwesomeIcon icon={faUser} className="text-blue-600" />
                        </div>
                        <div>
                          <h4 className="text-lg font-medium text-gray-900">{customer.name}</h4>
                          <p className="text-sm text-gray-500">{customer.email}</p>
                          <p className="text-sm text-gray-500">ID: {customer.memberID}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="flex space-x-2 mb-1">
                            {pendingCount > 0 && (
                              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                {pendingCount} chờ duyệt
                              </span>
                            )}
                            {approvedCount > 0 && (
                              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                {approvedCount} đã duyệt
                              </span>
                            )}
                            {rejectedCount > 0 && (
                              <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                                {rejectedCount} từ chối
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-400">
                            Gửi: {new Date(customer.submittedAt).toLocaleDateString('vi-VN')}
                          </p>
                        </div>
                        <FontAwesomeIcon icon={faEdit} className="text-gray-400" />
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      ) : (
        /* Customer Detail View */
        <div>
          <div className="mb-6">
            <button
              onClick={() => setSelectedCustomer(null)}
              className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              ← Quay lại danh sách
            </button>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <FontAwesomeIcon icon={faUser} className="text-blue-600 text-xl" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{selectedCustomer.name}</h2>
                <p className="text-gray-600">{selectedCustomer.email}</p>
                <p className="text-sm text-gray-500">Mã thành viên: {selectedCustomer.memberID}</p>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Customer Information - 2/3 width */}
            <div className="lg:col-span-2 space-y-6">
              <ApprovalSection
                title="Thông tin cá nhân"
                data={selectedCustomer.personalDetails}
                section="personalDetails"
                customer={selectedCustomer}
              />
              
              <ApprovalSection
                title="Thông tin liên hệ"
                data={selectedCustomer.contactInfo}
                section="contactInfo"
                customer={selectedCustomer}
              />
              
              <ApprovalSection
                title="Thông tin du lịch"
                data={selectedCustomer.travelInfo}
                section="travelInfo"
                customer={selectedCustomer}
              />
            </div>
            
            {/* Approval History - 1/3 width */}
            <div className="lg:col-span-1">
              <ApprovalHistory customerId={selectedCustomer.id} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerApproval;