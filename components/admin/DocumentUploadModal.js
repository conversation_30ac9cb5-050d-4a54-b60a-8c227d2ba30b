import React, { useState } from 'react';
import { FaSpinner, FaUpload, FaTimesCircle } from 'react-icons/fa';
import { DOCUMENT_TYPES } from './DocumentApproval';

const DocumentUploadModal = ({ customerId, customerName, documentType, isOpen, onClose, onUpload, allowTypeSelection = false }) => {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [selectedDocType, setSelectedDocType] = useState(documentType || 'idCard');

  if (!isOpen) return null;

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;
    
    // Check file type (jpeg, jpg, png, webp, pdf)
    const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'];
    if (!validTypes.includes(selectedFile.type)) {
      setError('Chỉ chấp nhận các file có định dạng JPEG, PNG, WEBP hoặc PDF');
      return;
    }
    
    // Check file size (max 5MB)
    if (selectedFile.size > 5 * 1024 * 1024) {
      setError('File không được vượt quá 5MB');
      return;
    }
    
    setFile(selectedFile);
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('🔍 Upload button clicked - handleSubmit started');
    
    if (!file) {
      console.log('❌ Error: No file selected');
      setError('Vui lòng chọn file');
      return;
    }
    
    console.log('📁 File details:', {
      name: file.name,
      type: file.type,
      size: `${(file.size / 1024).toFixed(2)} KB`,
      lastModified: new Date(file.lastModified).toISOString()
    });
    
    setUploading(true);
    setError('');
    
    try {
      console.log('📝 Preparing form data...');
      const formData = new FormData();
      formData.append('file', file);
      formData.append('customerId', customerId);
      formData.append('documentType', allowTypeSelection ? selectedDocType : documentType);
      
      console.log('📤 Form data prepared:', {
        customerId,
        documentType: allowTypeSelection ? selectedDocType : documentType,
        fileType: file.type,
        fileName: file.name
      });
      
      // Log the actual FormData contents for debugging
      for (let pair of formData.entries()) {
        console.log('FormData content:', pair[0], pair[1]);
      }
      
      // Get the store from the URL path
      const store = window.location.pathname.split('/')[1] || 'magshop';
      
      console.log('🌐 Calling API: /api/documents/upload with store:', store);
      const response = await fetch(`/api/documents/upload?store=${encodeURIComponent(store)}`, {
        method: 'POST',
        body: formData,
      });
      
      console.log('🔄 API response status:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Upload failed with server error:', errorText);
        setError(`Lỗi máy chủ (${response.status}): ${errorText || response.statusText}`);
        setUploading(false);
        return;
      }
      
      const result = await response.json();
      console.log('📥 API response data:', result);
      
      if (result.success) {
        console.log('✅ Upload successful:', result.document);
        onUpload(result.document);
        onClose();
      } else {
        console.error('❌ Upload failed with error:', result.error);
        setError(result.error || 'Đã xảy ra lỗi khi tải lên tài liệu');
      }
    } catch (error) {
      console.error('❌ Exception during upload:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      setError(`Đã xảy ra lỗi khi tải lên tài liệu: ${error.message}`);
    } finally {
      console.log('🏁 Upload process completed');
      setUploading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg max-w-md w-full">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="font-medium text-lg">
            Tải lên {allowTypeSelection ? 'tài liệu' : DOCUMENT_TYPES[documentType]?.label} - {customerName}
          </h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <FaTimesCircle size={24} />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-4" encType="multipart/form-data">
          {allowTypeSelection && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loại giấy tờ
              </label>
              <select
                value={selectedDocType}
                onChange={(e) => setSelectedDocType(e.target.value)}
                className="w-full border border-gray-300 rounded p-2"
              >
                {Object.entries(DOCUMENT_TYPES).map(([key, { label, description }]) => (
                  <option key={key} value={key} title={description}>
                    {label}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                {DOCUMENT_TYPES[selectedDocType]?.description}
              </p>
            </div>
          )}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Chọn file
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              className="w-full border border-gray-300 rounded p-2"
              accept=".jpg,.jpeg,.png,.webp,.pdf"
            />
            {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
          </div>
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              disabled={uploading}
            >
              Hủy
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
              disabled={uploading}
            >
              {uploading ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  Đang tải...
                </>
              ) : (
                <>
                  <FaUpload className="mr-2" />
                  Tải lên
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DocumentUploadModal; 