import React, { useState, useEffect } from 'react';
import { FaSync, FaChartLine, FaHistory, FaExclamationTriangle, FaCheckCircle, FaSpinner } from 'react-icons/fa';
import APNLogs from './APNLogs';

const PaymentChannels = () => {
  const [channels, setChannels] = useState([]);
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [showAPNLogs, setShowAPNLogs] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalTransactions: 0,
    totalVolume: 0
  });

  useEffect(() => {
    fetchPaymentChannels();
  }, []);

  const fetchPaymentChannels = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Fetch real payment channel statistics from the API
      const response = await fetch('/api/admin/payment-stats');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch payment statistics');
      }
      
      const { summary, channels: channelData } = result.data;
      
      // Update summary stats
      setStats({
        totalTransactions: summary.totalTransactions,
        totalVolume: summary.totalVolume
      });
      
      // Transform channel data for display
      const transformedChannels = channelData.map(channel => ({
        id: channel.id,
        name: channel.name,
        icon: getChannelIcon(channel.id),
        status: channel.status,
        transactions: channel.transactions,
        volume: channel.volume,
        lastUpdated: channel.lastTransaction || new Date().toISOString(),
        hasCallback: ['seven_eleven_ibon', '7_eleven_card', 'family_mart'].includes(channel.id),
        logo: getChannelLogo(channel.id),
        currencies: channel.currencies || []
      }));
      
      setChannels(transformedChannels);
    } catch (err) {
      console.error('Error fetching payment channels:', err);
      setError('Không thể tải dữ liệu kênh thanh toán: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get channel icon
  const getChannelIcon = (channelId) => {
    const iconMap = {
      'family_mart': '🏪',
      'seven_eleven_ibon': '🏪',
      '7_eleven_card': '💳',
      'sinopac_mobile': '📱'
    };
    return iconMap[channelId] || '💲';
  };

  // Helper function to get channel logo
  const getChannelLogo = (channelId) => {
    const logoMap = {
      'family_mart': '/MAG/LOGO/bank-logo/familymart-logo.webp',
      'familymart': '/MAG/LOGO/bank-logo/familymart-logo.webp',
      'seven_eleven_ibon': '/MAG/LOGO/bank-logo/7-11-logo.webp',
      '7_eleven_card': '/MAG/LOGO/bank-logo/7-11-logo.webp',
      '7-11': '/MAG/LOGO/bank-logo/7-11-logo.webp',
      'seven_eleven': '/MAG/LOGO/bank-logo/7-11-logo.webp',
      'sinopac_mobile': '/MAG/LOGO/bank-logo/sinopac.webp',
      'sinopac': '/MAG/LOGO/bank-logo/sinopac.webp',
      'hilife': '/MAG/LOGO/bank-logo/hi-life-logo.webp',
      'hi_life': '/MAG/LOGO/bank-logo/hi-life-logo.webp',
      'okmart': '/MAG/LOGO/bank-logo/ok-mart-logo.webp',
      'ok_mart': '/MAG/LOGO/bank-logo/ok-mart-logo.webp',
      'bank_transfer': '/MAG/LOGO/bank-logo/bank-transfer-logo.webp',
      'credit_card': '/MAG/LOGO/bank-logo/logo-visa-card.webp',
      'visa': '/MAG/LOGO/bank-logo/logo-visa-card.webp',
      'test': '/MAG/LOGO/bank-logo/bank-transfer-logo.webp'
    };
    return logoMap[channelId] || '/shopme.logo.1.webp';
  };

  const handleChannelSelect = (channel) => {
    setSelectedChannel(channel);
    if (channel.hasCallback) {
      setShowAPNLogs(true);
    } else {
      setShowAPNLogs(false);
    }
  };

  const StatusBadge = ({ status }) => {
    let colorClass = '';
    let label = '';
    
    switch (status) {
      case 'active':
        colorClass = 'bg-green-100 text-green-800';
        label = 'Hoạt động';
        break;
      case 'inactive':
        colorClass = 'bg-gray-100 text-gray-800';
        label = 'Không hoạt động';
        break;
      case 'issue':
        colorClass = 'bg-red-100 text-red-800';
        label = 'Có vấn đề';
        break;
      default:
        colorClass = 'bg-blue-100 text-blue-800';
        label = status;
    }
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
        {label}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-blue-500 text-xl sm:text-2xl mr-2" />
        <span className="text-sm sm:text-base">Đang tải kênh thanh toán...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-3 sm:px-4 py-3 rounded mx-2 sm:mx-0">
        <div className="flex items-center">
          <FaExclamationTriangle className="mr-2 flex-shrink-0" />
          <span className="text-sm sm:text-base">{error}</span>
        </div>
        <button 
          onClick={fetchPaymentChannels}
          className="mt-2 text-sm text-red-700 hover:text-red-900 underline"
        >
          Thử lại
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm mx-2 sm:mx-0">
      {/* Summary Stats - Mobile-friendly */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 p-3 sm:p-4 border-b border-gray-200">
        <div className="bg-blue-50 p-3 rounded-lg">
          <h4 className="text-xs sm:text-sm font-medium text-blue-800">Tổng giao dịch</h4>
          <p className="text-xl sm:text-2xl font-bold">{stats.totalTransactions.toLocaleString()}</p>
        </div>
        <div className="bg-green-50 p-3 rounded-lg">
          <h4 className="text-xs sm:text-sm font-medium text-green-800">Tổng giá trị</h4>
          <p className="text-xl sm:text-2xl font-bold">{stats.totalVolume.toLocaleString('vi-VN')}đ</p>
        </div>
      </div>

      {/* Controls - Mobile-friendly */}
      <div className="flex justify-between items-center p-3 sm:p-4">
        <h3 className="text-base sm:text-lg font-medium text-gray-800">Kênh thanh toán</h3>
        <button 
          onClick={fetchPaymentChannels}
          className="flex items-center px-2 sm:px-3 py-1.5 bg-blue-50 text-blue-700 rounded hover:bg-blue-100 text-sm"
        >
          <FaSync className="mr-1 h-3 w-3" />
          <span className="hidden sm:inline">Làm mới</span>
          <span className="sm:hidden">Mới</span>
        </button>
      </div>

      {/* Channels List - Responsive: Table for desktop, Cards for mobile */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Kênh
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trạng thái
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Giao dịch
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Giá trị
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Cập nhật lần cuối
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {channels.map(channel => (
              <tr key={channel.id} className={selectedChannel?.id === channel.id ? 'bg-blue-50' : ''}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <img className="h-10 w-10 rounded-full object-contain" src={channel.logo} alt={channel.name} />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{channel.name}</div>
                      {channel.hasCallback && (
                        <div className="text-xs text-green-600">Hỗ trợ callback</div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <StatusBadge status={channel.status} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {channel.transactions.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {channel.volume.toLocaleString('vi-VN')}đ
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(channel.lastUpdated).toLocaleString('vi-VN')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button 
                    onClick={() => handleChannelSelect(channel)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    Chi tiết
                  </button>
                  {channel.hasCallback && (
                    <button 
                      onClick={() => {
                        setSelectedChannel(channel);
                        setShowAPNLogs(true);
                      }}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      <FaHistory className="inline mr-1" />
                      Nhật ký
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-3 p-3">
        {channels.map(channel => (
          <div key={channel.id} className={`border rounded-lg p-3 ${selectedChannel?.id === channel.id ? 'bg-blue-50 border-blue-200' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-8 w-8">
                  <img className="h-8 w-8 rounded-full object-contain" src={channel.logo} alt={channel.name} />
                </div>
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900">{channel.name}</div>
                  {channel.hasCallback && (
                    <div className="text-xs text-green-600">Hỗ trợ callback</div>
                  )}
                </div>
              </div>
              <StatusBadge status={channel.status} />
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-3">
              <div>
                <span className="font-medium">Giao dịch:</span> {channel.transactions.toLocaleString()}
              </div>
              <div>
                <span className="font-medium">Giá trị:</span> {channel.volume.toLocaleString('vi-VN')}đ
              </div>
              <div className="col-span-2">
                <span className="font-medium">Cập nhật:</span> {new Date(channel.lastUpdated).toLocaleString('vi-VN')}
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button 
                onClick={() => handleChannelSelect(channel)}
                className="flex-1 text-center px-3 py-1.5 bg-blue-50 text-blue-700 rounded text-sm hover:bg-blue-100"
              >
                Chi tiết
              </button>
              {channel.hasCallback && (
                <button 
                  onClick={() => {
                    setSelectedChannel(channel);
                    setShowAPNLogs(true);
                  }}
                  className="flex-1 text-center px-3 py-1.5 bg-indigo-50 text-indigo-700 rounded text-sm hover:bg-indigo-100"
                >
                  <FaHistory className="inline mr-1" />
                  Nhật ký
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Show message if no channels found */}
      {channels.length === 0 && !isLoading && !error && (
        <div className="text-center py-8">
          <p className="text-gray-500">Không tìm thấy kênh thanh toán nào có dữ liệu.</p>
          <p className="text-sm text-gray-400 mt-2">Dữ liệu sẽ hiển thị khi có đơn hàng với thông tin thanh toán.</p>
        </div>
      )}

      {/* Channel Details - Mobile-friendly */}
      {selectedChannel && (
        <div className="mt-6 p-3 sm:p-4 border-t border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-base sm:text-lg font-medium text-gray-800">
              Chi tiết {selectedChannel.name}
            </h3>
            <button 
              onClick={() => setSelectedChannel(null)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Đóng
            </button>
          </div>
          
          {/* Details content */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
              <div className="space-y-2">
                <p className="text-sm"><strong>ID phương thức thanh toán:</strong> {selectedChannel.id}</p>
                <p className="text-sm"><strong>Số giao dịch:</strong> {selectedChannel.transactions.toLocaleString()}</p>
                <p className="text-sm"><strong>Tổng giá trị:</strong> {selectedChannel.volume.toLocaleString('vi-VN')}đ</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm"><strong>Tiền tệ được hỗ trợ:</strong> {selectedChannel.currencies?.join(', ') || 'Tất cả'}</p>
                <p className="text-sm"><strong>Trạng thái tích hợp:</strong> {selectedChannel.status === 'active' ? 'Hoạt động' : selectedChannel.status === 'inactive' ? 'Không hoạt động' : selectedChannel.status === 'issue' ? 'Có vấn đề' : selectedChannel.status}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* APN Logs */}
      {showAPNLogs && selectedChannel && (
        <div className="mt-6">
          <APNLogs channelId={selectedChannel.id} />
        </div>
      )}
    </div>
  );
};

export default PaymentChannels;