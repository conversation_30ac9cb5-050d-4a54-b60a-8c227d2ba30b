import React, { useState, useEffect } from 'react';
import { 
  FaHistory, 
  FaCheckCircle, 
  FaTimesCircle, 
  FaEx<PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON><PERSON>er,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaEye,
  FaCalendarAlt,
  FaCreditCard,
  FaExternalLinkAlt,
  FaSync
} from 'react-icons/fa';

interface PaymentCallback {
  id: string;
  orderId: string;
  timestamp: string;
  method: string;
  submethod?: string;
  status: 'success' | 'failed' | 'pending' | 'error';
  httpStatus: number;
  response?: any;
  details?: string;
  paymentProvider: string;
  transactionId?: string;
  amount?: number;
  currency?: string;
  customerName?: string;
  attempts?: number;
  lastAttempt?: string;
}

interface CallbackHistoryProps {
  className?: string;
}

const APNCallbackHistory: React.FC<CallbackHistoryProps> = ({ className = '' }) => {
  const [callbacks, setCallbacks] = useState<PaymentCallback[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters and sorting
  const [filters, setFilters] = useState({
    status: 'all',
    paymentProvider: 'all',
    dateRange: '7d',
    searchQuery: '',
    source: 'all'
  });
  
  const [sortConfig, setSortConfig] = useState({
    key: 'timestamp',
    direction: 'desc' as 'asc' | 'desc'
  });
  
  const [selectedCallback, setSelectedCallback] = useState<PaymentCallback | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Fetch callback data
  const fetchCallbacks = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/admin/apn-callbacks');
      
      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        setCallbacks(result.callbacks || []);
      } else {
        throw new Error(result.error || 'Failed to fetch callbacks');
      }
    } catch (err) {
      console.error('Error fetching APN callbacks:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCallbacks();
  }, []);

  // Helper functions
  const getStatusIcon = (status: string, httpStatus?: number) => {
    if (httpStatus && httpStatus >= 200 && httpStatus < 300) {
      return <FaCheckCircle className="text-green-500" />;
    }
    
    switch (status?.toLowerCase()) {
      case 'success':
        return <FaCheckCircle className="text-green-500" />;
      case 'failed':
      case 'error':
        return <FaTimesCircle className="text-red-500" />;
      case 'pending':
        return <FaSpinner className="text-yellow-500 animate-spin" />;
      default:
        return <FaExclamationTriangle className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: string, httpStatus?: number) => {
    if (httpStatus && httpStatus >= 200 && httpStatus < 300) {
      return 'text-green-600 bg-green-100';
    }
    
    switch (status?.toLowerCase()) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'failed':
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider?.toLowerCase()) {
      case '7-eleven':
      case 'seven_eleven':
      case '711':
        return '🏪';
      case 'familymart':
      case 'family_mart':
        return '🏪';
      case 'sinopac':
      case 'sinopac_bank':
        return '🏦';
      default:
        return '💳';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateString || 'N/A';
    }
  };

  const formatCurrency = (amount?: number, currency = 'VND') => {
    if (!amount) return 'N/A';
    
    if (currency === 'NT$' || currency === 'NT') {
      return `${amount.toLocaleString()} NT$`;
    }
    
    return `${amount.toLocaleString('vi-VN')} ${currency}`;
  };

  // Filter and sort callbacks
  const filteredAndSortedCallbacks = React.useMemo(() => {
    let filtered = [...callbacks];

    // Apply filters
    if (filters.status !== 'all') {
      filtered = filtered.filter(callback => callback.status === filters.status);
    }

    if (filters.paymentProvider !== 'all') {
      filtered = filtered.filter(callback => 
        callback.paymentProvider?.toLowerCase().includes(filters.paymentProvider.toLowerCase())
      );
    }

    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(callback =>
        callback.orderId?.toLowerCase().includes(query) ||
        callback.transactionId?.toLowerCase().includes(query) ||
        callback.customerName?.toLowerCase().includes(query) ||
        callback.paymentProvider?.toLowerCase().includes(query)
      );
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const cutoff = new Date();
      
      switch (filters.dateRange) {
        case '1d':
          cutoff.setDate(now.getDate() - 1);
          break;
        case '7d':
          cutoff.setDate(now.getDate() - 7);
          break;
        case '30d':
          cutoff.setDate(now.getDate() - 30);
          break;
        case '90d':
          cutoff.setDate(now.getDate() - 90);
          break;
      }
      
      filtered = filtered.filter(callback => 
        new Date(callback.timestamp) >= cutoff
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortConfig.key as keyof PaymentCallback];
      let bValue = b[sortConfig.key as keyof PaymentCallback];

      // Handle different data types
      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });

    return filtered;
  }, [callbacks, filters, sortConfig]);

  // Handle sorting
  const handleSort = (key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getSortIcon = (key: string) => {
    if (sortConfig.key !== key) return <FaSort className="text-gray-400" />;
    return sortConfig.direction === 'asc' ? 
      <FaSortUp className="text-blue-500" /> : 
      <FaSortDown className="text-blue-500" />;
  };

  // View callback details
  const viewCallbackDetails = (callback: PaymentCallback) => {
    setSelectedCallback(callback);
    setShowDetails(true);
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-3 sm:p-6 mx-2 sm:mx-0 ${className}`}>
        <div className="flex justify-center items-center py-12">
          <FaSpinner className="animate-spin text-blue-500 mr-3 text-lg sm:text-xl" />
          <span className="text-gray-600 text-sm sm:text-base">Đang tải lịch sử callback...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-3 sm:p-6 mx-2 sm:mx-0 ${className}`}>
        <div className="text-center py-12">
          <FaTimesCircle className="text-red-500 text-2xl sm:text-3xl mx-auto mb-4" />
          <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">Lỗi tải dữ liệu</h3>
          <p className="text-gray-600 mb-4 text-sm sm:text-base">{error}</p>
          <button
            onClick={fetchCallbacks}
            className="inline-flex items-center px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm sm:text-base"
          >
            <FaSync className="mr-2" />
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border mx-2 sm:mx-0 ${className}`}>
      {/* Header - Mobile-friendly */}
      <div className="border-b border-gray-200 px-3 sm:px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center min-w-0 flex-1">
            <FaHistory className="text-blue-600 mr-2 sm:mr-3 text-lg sm:text-xl flex-shrink-0" />
            <div className="min-w-0">
              <h2 className="text-base sm:text-lg font-medium text-gray-900 truncate">Lịch sử APN Callback</h2>
              <p className="text-xs sm:text-sm text-gray-500">
                {filteredAndSortedCallbacks.length} callback trong tổng số {callbacks.length}
              </p>
            </div>
          </div>
          <button
            onClick={fetchCallbacks}
            className="inline-flex items-center px-2 sm:px-3 py-2 text-xs sm:text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex-shrink-0"
          >
            <FaSync className="mr-1 sm:mr-2 h-3 w-3" />
            <span className="hidden sm:inline">Làm mới</span>
            <span className="sm:hidden">Mới</span>
          </button>
        </div>
      </div>

      {/* Filters - Mobile-friendly */}
      <div className="border-b border-gray-200 px-3 sm:px-6 py-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {/* Status Filter */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
              <FaFilter className="inline mr-1" />
              Trạng thái
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md text-xs sm:text-sm"
            >
              <option value="all">Tất cả</option>
              <option value="success">Thành công</option>
              <option value="failed">Thất bại</option>
              <option value="pending">Đang chờ</option>
              <option value="error">Lỗi</option>
            </select>
          </div>

          {/* Provider Filter */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
              <FaCreditCard className="inline mr-1" />
              Nhà cung cấp
            </label>
            <select
              value={filters.paymentProvider}
              onChange={(e) => setFilters(prev => ({ ...prev, paymentProvider: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md text-xs sm:text-sm"
            >
              <option value="all">Tất cả</option>
              <option value="7-eleven">7-Eleven</option>
              <option value="familymart">FamilyMart</option>
              <option value="sinopac">Sinopac Bank</option>
            </select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
              <FaCalendarAlt className="inline mr-1" />
              Thời gian
            </label>
            <select
              value={filters.dateRange}
              onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md text-xs sm:text-sm"
            >
              <option value="1d">24 giờ qua</option>
              <option value="7d">7 ngày qua</option>
              <option value="30d">30 ngày qua</option>
              <option value="90d">90 ngày qua</option>
              <option value="all">Tất cả</option>
            </select>
          </div>

          {/* Search */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
              Tìm kiếm
            </label>
            <input
              type="text"
              placeholder="ID đơn hàng, giao dịch..."
              value={filters.searchQuery}
              onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md text-xs sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Responsive Card View */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {filteredAndSortedCallbacks.map((callback, index) => (
          <div key={callback.id || index} className="border border-gray-200 rounded-lg p-3 bg-white">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <span className="mr-2">{getProviderIcon(callback.paymentProvider)}</span>
                <div>
                  <div className="text-sm font-medium text-gray-900">{callback.orderId}</div>
                  {callback.customerName && (
                    <div className="text-xs text-gray-500">{callback.customerName}</div>
                  )}
                </div>
              </div>
              <div className="flex items-center">
                {getStatusIcon(callback.status, callback.httpStatus)}
                <span className={`ml-1 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(callback.status, callback.httpStatus)}`}>
                  {callback.status}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-3">
              <div>
                <span className="font-medium">Thời gian:</span>
                <div>{formatDate(callback.timestamp)}</div>
              </div>
              <div>
                <span className="font-medium">Nhà cung cấp:</span>
                <div>{callback.paymentProvider}</div>
              </div>
              <div>
                <span className="font-medium">HTTP:</span>
                <span className={`px-1 py-0.5 text-xs font-medium rounded ${
                  callback.httpStatus >= 200 && callback.httpStatus < 300 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {callback.httpStatus || 'N/A'}
                </span>
              </div>
              <div>
                <span className="font-medium">Số tiền:</span>
                <div>{formatCurrency(callback.amount, callback.currency)}</div>
              </div>
            </div>
            
            <button
              onClick={() => viewCallbackDetails(callback)}
              className="w-full flex items-center justify-center px-3 py-2 bg-blue-50 text-blue-700 rounded text-sm hover:bg-blue-100"
            >
              <FaEye className="mr-2" />
              Xem chi tiết
            </button>
          </div>
        ))}
      </div>

      {/* No results message */}
      {filteredAndSortedCallbacks.length === 0 && (
        <div className="text-center py-12">
          <FaHistory className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không có callback nào</h3>
          <p className="text-gray-500">
            {callbacks.length === 0 
              ? 'Chưa có callback nào được ghi nhận.'
              : 'Không tìm thấy callback nào phù hợp với bộ lọc hiện tại.'
            }
          </p>
        </div>
      )}

      {/* Callback Details Modal */}
      {showDetails && selectedCallback && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="border-b border-gray-200 px-3 sm:px-6 py-3 sm:py-4">
              <div className="flex items-center justify-between">
                <h3 className="text-base sm:text-lg font-medium text-gray-900">Chi tiết Callback</h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FaTimesCircle className="h-5 w-5 sm:h-6 sm:w-6" />
                </button>
              </div>
            </div>
            
            <div className="px-3 sm:px-6 py-3 sm:py-4 space-y-3 sm:space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">ID Đơn hàng</label>
                  <p className="text-sm text-gray-900">{selectedCallback.orderId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Mã giao dịch</label>
                  <p className="text-sm text-gray-900">{selectedCallback.transactionId || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Thời gian</label>
                  <p className="text-sm text-gray-900">{formatDate(selectedCallback.timestamp)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nhà cung cấp</label>
                  <p className="text-sm text-gray-900 flex items-center">
                    <span className="mr-2">{getProviderIcon(selectedCallback.paymentProvider)}</span>
                    {selectedCallback.paymentProvider}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Trạng thái</label>
                  <div className="flex items-center">
                    {getStatusIcon(selectedCallback.status, selectedCallback.httpStatus)}
                    <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(selectedCallback.status, selectedCallback.httpStatus)}`}>
                      {selectedCallback.status}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">HTTP Status</label>
                  <span className={`px-2 py-1 text-xs font-medium rounded ${
                    selectedCallback.httpStatus >= 200 && selectedCallback.httpStatus < 300 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {selectedCallback.httpStatus || 'N/A'}
                  </span>
                </div>
              </div>

              {/* Response Details */}
              {selectedCallback.response && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Response Data</label>
                  <pre className="bg-gray-100 p-3 rounded-md text-xs overflow-x-auto">
                    {JSON.stringify(selectedCallback.response, null, 2)}
                  </pre>
                </div>
              )}

              {/* Additional Details */}
              {selectedCallback.details && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Chi tiết</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {selectedCallback.details}
                  </p>
                </div>
              )}

              {/* Attempts Info */}
              {selectedCallback.attempts && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Số lần thử</label>
                    <p className="text-sm text-gray-900">{selectedCallback.attempts}</p>
                  </div>
                  {selectedCallback.lastAttempt && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Lần thử cuối</label>
                      <p className="text-sm text-gray-900">{formatDate(selectedCallback.lastAttempt)}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <div className="border-t border-gray-200 px-3 sm:px-6 py-3 sm:py-4">
              <button
                onClick={() => setShowDetails(false)}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 text-sm sm:text-base"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default APNCallbackHistory;