import React, { useState, useEffect } from 'react';
import {
  FaBox,
  FaChartLine,
  FaExclamationTriangle,
  FaArrowUp,
  FaArrowDown,
  FaEye,
  FaPlus,
  FaDownload
} from 'react-icons/fa';

const InventoryDashboardModule = ({ onViewDetails }) => {
  const [inventoryStats, setInventoryStats] = useState({
    totalItems: 0,
    inStock: 0,
    lowStock: 0,
    outOfStock: 0,
    totalValue: 0,
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);
  const [lowStockItems, setLowStockItems] = useState([]);

  useEffect(() => {
    fetchInventoryStats();
  }, []);

  const fetchInventoryStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/inventory?action=stats');
      if (response.ok) {
        const data = await response.json();
        setInventoryStats(data.stats || {
          totalItems: 0,
          inStock: 0,
          lowStock: 0,
          outOfStock: 0,
          totalValue: 0,
          recentActivity: []
        });
        setLowStockItems(data.lowStockItems || []);
      }
    } catch (error) {
      console.error('Error fetching inventory stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount, currency = 'VND') => {
    try {
      const validCurrencies = ['VND', 'USD', 'EUR', 'JPY', 'CNY', 'KRW', 'THB', 'SGD'];
      const safeCurrency = validCurrencies.includes(currency) ? currency : 'VND';
      
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: safeCurrency,
        minimumFractionDigits: 0
      }).format(amount);
    } catch (error) {
      return new Intl.NumberFormat('vi-VN').format(amount) + ' ' + (currency || 'VND');
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FaBox className="text-blue-600 text-xl mr-3" />
            <h2 className="text-lg font-semibold text-gray-900">Tổng quan kho hàng</h2>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => {
                if (onViewDetails) {
                  onViewDetails();
                } else {
                  window.location.href = window.location.pathname.replace('/admin-dashboard', '/inventory-management');
                }
              }}
              className="flex items-center px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
            >
              <FaEye className="mr-2" />
              Xem chi tiết
            </button>
            <button className="flex items-center px-3 py-2 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors">
              <FaPlus className="mr-2" />
              Thêm sản phẩm
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">Tổng sản phẩm</p>
                <p className="text-2xl font-bold text-blue-700">{inventoryStats.totalItems}</p>
              </div>
              <FaBox className="text-blue-500 text-xl" />
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 font-medium">Còn hàng</p>
                <p className="text-2xl font-bold text-green-700">{inventoryStats.inStock}</p>
              </div>
              <FaArrowUp className="text-green-500 text-xl" />
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-600 font-medium">Sắp hết</p>
                <p className="text-2xl font-bold text-yellow-700">{inventoryStats.lowStock}</p>
              </div>
              <FaExclamationTriangle className="text-yellow-500 text-xl" />
            </div>
          </div>

          <div className="bg-red-50 p-4 rounded-lg border border-red-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-600 font-medium">Hết hàng</p>
                <p className="text-2xl font-bold text-red-700">{inventoryStats.outOfStock}</p>
              </div>
              <FaArrowDown className="text-red-500 text-xl" />
            </div>
          </div>
        </div>

        {/* Total Value */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border border-purple-100 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-purple-600 font-medium">Tổng giá trị kho</p>
              <p className="text-2xl font-bold text-purple-700">
                {formatCurrency(inventoryStats.totalValue)}
              </p>
            </div>
            <FaChartLine className="text-purple-500 text-2xl" />
          </div>
        </div>

        {/* Low Stock Alert */}
        {lowStockItems.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center mb-3">
              <FaExclamationTriangle className="text-yellow-500 mr-2" />
              <h3 className="text-sm font-medium text-yellow-800">
                Cảnh báo: {lowStockItems.length} sản phẩm sắp hết hàng
              </h3>
            </div>
            <div className="space-y-2">
              {lowStockItems.slice(0, 3).map((item, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-yellow-700 font-medium">{item.name}</span>
                  <span className="text-yellow-600">Còn {item.quantity} {item.unit || 'sản phẩm'}</span>
                </div>
              ))}
              {lowStockItems.length > 3 && (
                <p className="text-xs text-yellow-600 mt-2">
                  ... và {lowStockItems.length - 3} sản phẩm khác
                </p>
              )}
            </div>
          </div>
        )}

        {/* Recent Activity */}
        <div className="border border-gray-200 rounded-lg">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900">Hoạt động gần đây</h3>
          </div>
          <div className="divide-y divide-gray-200">
            {inventoryStats.recentActivity.length > 0 ? (
              inventoryStats.recentActivity.slice(0, 5).map((activity, index) => (
                <div key={index} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-3 ${
                        activity.type === 'add' ? 'bg-green-500' :
                        activity.type === 'update' ? 'bg-blue-500' :
                        activity.type === 'remove' ? 'bg-red-500' :
                        'bg-gray-500'
                      }`}></div>
                      <div>
                        <p className="text-sm text-gray-900">{activity.description}</p>
                        <p className="text-xs text-gray-500">{activity.timestamp}</p>
                      </div>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      activity.type === 'add' ? 'bg-green-100 text-green-800' :
                      activity.type === 'update' ? 'bg-blue-100 text-blue-800' :
                      activity.type === 'remove' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {activity.type === 'add' ? 'Thêm' :
                       activity.type === 'update' ? 'Cập nhật' :
                       activity.type === 'remove' ? 'Xóa' : 'Khác'}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-8 text-center">
                <FaBox className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Chưa có hoạt động nào</p>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-6 flex flex-wrap gap-2">
          <button className="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
            <FaDownload className="mr-2" />
            Xuất báo cáo
          </button>
          <button className="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
            <FaChartLine className="mr-2" />
            Xem thống kê
          </button>
        </div>
      </div>
    </div>
  );
};

export default InventoryDashboardModule;