import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAdminAuth } from '../../context/adminAuthContext';
import { FiLoader } from 'react-icons/fi';

const AdminLayout = ({ children }) => {
  const { loading, isAuthenticated, initialized } = useAdminAuth();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [redirecting, setRedirecting] = useState(false);

  // Ensure component is mounted before checking auth
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle redirect when not authenticated
  useEffect(() => {
    if (mounted && initialized && !loading && !isAuthenticated && !redirecting) {
      setRedirecting(true);
      window.location.href = '/admin/login';
    }
  }, [mounted, initialized, loading, isAuthenticated, redirecting]);

  // Show loading state while mounting or checking auth
  if (!mounted || !initialized || loading || redirecting) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">
            {!mounted ? 'Đang khởi tạo...' :
             !initialized ? 'Đang thiết lập xác thực...' :
             redirecting ? 'Đang chuyển hướng đến trang đăng nhập...' :
             'Đang xác thực...'}
          </p>
        </div>
      </div>
    );
  }

  // If not authenticated, show loading state
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Đang kiểm tra xác thực...</p>
        </div>
      </div>
    );
  }

  // Render the admin content
  return children;
};

export default AdminLayout;
