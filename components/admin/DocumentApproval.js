import React, { useState, useEffect } from 'react';
import { FaFileAlt, FaCheck, FaTimes, FaSpinner, FaTimesCircle, FaUpload, FaFilter, FaUser, FaCalendarAlt, FaPlus } from 'react-icons/fa';
import CustomersList from './CustomersList';
import DocumentUploadModal from './DocumentUploadModal';

// Document types dictionary
const DOCUMENT_TYPES = {
  idCard: { label: 'CMND/CCCD', description: 'Chứng minh nhân dân hoặc căn cước công dân' },
  passport: { label: '<PERSON>ộ chiếu', description: 'Hộ chiếu quốc tế' },
  driverLicense: { label: 'Bằng lái xe', description: 'Giấy phép lái xe' },
  birthCertificate: { label: 'Giấy khai sinh', description: 'Gi<PERSON>y khai sinh' },
  photo: { label: 'Ảnh chân dung', description: 'Ảnh chân dung chính chủ' },
  address: { label: '<PERSON><PERSON><PERSON><PERSON> tờ cư trú', description: '<PERSON><PERSON> hộ khẩu, tạm trú, KT3, x<PERSON><PERSON> nhận cư trú' },
  businessRegistration: { label: 'Đăng ký kinh doanh', description: 'Giấy phép đăng ký kinh doanh' },
  taxRegistration: { label: 'Đăng ký thuế', description: 'Giấy đăng ký mã số thuế' },
  bankStatement: { label: 'Sao kê ngân hàng', description: 'Sao kê tài khoản ngân hàng' },
  insuranceCard: { label: 'Thẻ bảo hiểm', description: 'Thẻ bảo hiểm y tế/xã hội' },
  marriageCertificate: { label: 'Giấy kết hôn', description: 'Giấy chứng nhận kết hôn' },
  workPermit: { label: 'Giấy phép lao động', description: 'Giấy phép lao động cho người nước ngoài' },
  academicDegree: { label: 'Bằng cấp học thuật', description: 'Bằng đại học, cao đẳng, chứng chỉ' },
  utilityBill: { label: 'Hóa đơn tiện ích', description: 'Hóa đơn điện, nước, internet' },
  propertyOwnership: { label: 'Giấy tờ sở hữu', description: 'Giấy chứng nhận quyền sử dụng đất, sở hữu nhà' },
  other: { label: 'Khác', description: 'Các giấy tờ khác' }
};

// Export DOCUMENT_TYPES for use in DocumentUploadModal
export { DOCUMENT_TYPES };

// Document Modal component for previewing documents
const DocumentPreviewModal = ({ document, isOpen, onClose }) => {
  if (!isOpen) return null;
  
  // Construct the document URL with the correct path
  const documentUrl = `/data/documents/customers/${document.customerId}/${document.filename}`;
  const isPdf = document.filename.toLowerCase().endsWith('.pdf');
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg max-w-5xl w-full max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="font-medium text-lg">
            {DOCUMENT_TYPES[document.documentType]?.label || 'Tài liệu'} - {document.customerName}
          </h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <FaTimesCircle size={24} />
          </button>
        </div>
        <div className="p-4 overflow-auto flex-grow">
          <div className="mb-4">
            <p className="text-sm text-gray-500">ID: {document.id}</p>
            <p className="text-sm text-gray-500">Customer ID: {document.customerId}</p>
            <p className="text-sm text-gray-500">Ngày tải lên: {new Date(document.uploadDate).toLocaleDateString('vi-VN')}</p>
            <p className="text-sm text-gray-500">Trạng thái: {
              document.status === 'pending' ? 'Đang chờ phê duyệt' :
              document.status === 'approved' ? 'Đã phê duyệt' :
              'Đã từ chối'
            }</p>
            {document.orderId && (
              <p className="text-sm text-gray-500">Đơn hàng: #{document.orderId}</p>
            )}
          </div>
          <div className="flex justify-center">
            <div className="border border-gray-200 rounded overflow-hidden max-h-[60vh]">
              {isPdf ? (
                <iframe 
                  src={`${documentUrl}#view=FitH`} 
                  className="w-full h-[60vh]"
                  title={`${document.documentType} document`}
                />
              ) : (
                <img 
                  src={documentUrl} 
                  alt={`${document.documentType} document`} 
                  className="max-w-full h-auto object-contain"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = '/images/document-placeholder.png'; // Fallback image
                  }}
                />
              )}
            </div>
          </div>
        </div>
        <div className="flex justify-end p-4 border-t">
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  );
};

// Document Approval Card component
const DocumentApprovalCard = ({ document, onApprove, onReject, isProcessing, onViewDocument, onReupload }) => {
  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 mb-3">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium">{DOCUMENT_TYPES[document.documentType]?.label || 'Tài liệu không xác định'}</h3>
          <p className="text-sm text-gray-500">Ngày tải lên: {new Date(document.uploadDate).toLocaleDateString('vi-VN')}</p>
          <p className="text-sm text-gray-500">Khách hàng: {document.customerName}</p>
          <p className="text-sm text-gray-500">ID: {document.id}</p>
          <p className="text-sm text-gray-500">Customer ID: {document.customerId}</p>
          {document.orderId && (
            <p className="text-sm text-gray-500">Đơn hàng: #{document.orderId}</p>
          )}
          <div className={`inline-block px-2 py-1 rounded-full text-xs mt-2 ${statusColors[document.status]}`}>
            {document.status === 'pending' ? 'Đang chờ phê duyệt' : 
             document.status === 'approved' ? 'Đã phê duyệt' : 
             'Đã từ chối'}
          </div>
        </div>
        <div className="flex space-x-2">
          {isProcessing ? (
            <FaSpinner className="animate-spin text-blue-500" />
          ) : (
            <>
              {document.status !== 'approved' && (
                <button 
                  onClick={() => onApprove(document.id)}
                  className="bg-green-100 text-green-700 p-2 rounded-full hover:bg-green-200"
                  disabled={isProcessing}
                  title="Phê duyệt"
                >
                  <FaCheck />
                </button>
              )}
              {document.status !== 'rejected' && (
                <button 
                  onClick={() => onReject(document.id)}
                  className="bg-red-100 text-red-700 p-2 rounded-full hover:bg-red-200"
                  disabled={isProcessing}
                  title="Từ chối"
                >
                  <FaTimes />
                </button>
              )}
              <button 
                onClick={() => onReupload(document)}
                className="bg-blue-100 text-blue-700 p-2 rounded-full hover:bg-blue-200"
                disabled={isProcessing}
                title="Tải lại"
              >
                <FaUpload />
              </button>
            </>
          )}
        </div>
      </div>
      <div className="mt-3">
        <button 
          className="text-blue-600 text-sm hover:underline"
          disabled={isProcessing}
          onClick={() => onViewDocument(document)}
        >
          Xem tài liệu
        </button>
      </div>
    </div>
  );
};

// Customer Selection for Upload Component
const CustomerSelectionUpload = ({ customers, onSelectCustomer }) => {
  const [selectedCustomerId, setSelectedCustomerId] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (selectedCustomerId) {
      const customer = customers.find(c => c.id === selectedCustomerId);
      onSelectCustomer(customer);
    }
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-md max-w-md mx-auto">
      <h3 className="text-lg font-medium mb-4">Tải lên tài liệu cho khách hàng</h3>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Chọn khách hàng
          </label>
          <select
            value={selectedCustomerId}
            onChange={(e) => setSelectedCustomerId(e.target.value)}
            className="w-full border border-gray-300 rounded p-2"
            required
          >
            <option value="">-- Chọn khách hàng --</option>
            {customers.map(customer => (
              <option key={customer.id} value={customer.id}>
                {customer.name} (ID: {customer.id})
              </option>
            ))}
          </select>
        </div>
        <button
          type="submit"
          className="w-full bg-blue-600 text-white rounded p-2 hover:bg-blue-700 flex items-center justify-center"
          disabled={!selectedCustomerId}
        >
          <FaUpload className="mr-2" />
          Tải lên giấy tờ
        </button>
      </form>
    </div>
  );
};

const DocumentApproval = ({ DashboardSection }) => {
  const [documents, setDocuments] = useState([]);
  const [filteredDocuments, setFilteredDocuments] = useState([]);
  const [processingDoc, setProcessingDoc] = useState(null);
  const [loading, setLoading] = useState(true);
  const [previewDocument, setPreviewDocument] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [uploadInfo, setUploadInfo] = useState(null);
  const [customers, setCustomers] = useState([]);
  
  // Filter states
  const [filters, setFilters] = useState({
    customerId: '',
    status: '',
    documentType: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    // Fetch documents
    fetchDocuments();

    // Fetch customers
    const fetchCustomers = async () => {
      try {
        const response = await fetch('/api/customers');
        if (response.ok) {
          const data = await response.json();
          setCustomers(data.customers || []);
        }
      } catch (error) {
        console.error('Error fetching customers:', error);
      }
    };
    
    fetchCustomers();
  }, []);

  useEffect(() => {
    // Apply filters
    applyFilters();
  }, [documents, filters]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/documents/all');
      
      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.documents) {
        setDocuments(result.documents);
        setFilteredDocuments(result.documents);
      } else {
        console.error('Failed to fetch documents:', result.error);
        setDocuments([]);
        setFilteredDocuments([]);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      setDocuments([]);
      setFilteredDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...documents];
    
    // Filter by customer ID
    if (filters.customerId) {
      filtered = filtered.filter(doc => doc.customerId === filters.customerId);
    }
    
    // Filter by status
    if (filters.status) {
      filtered = filtered.filter(doc => doc.status === filters.status);
    }
    
    // Filter by document type
    if (filters.documentType) {
      filtered = filtered.filter(doc => doc.documentType === filters.documentType);
    }
    
    // Filter by date range
    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(doc => new Date(doc.uploadDate) >= fromDate);
    }
    
    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of the day
      filtered = filtered.filter(doc => new Date(doc.uploadDate) <= toDate);
    }
    
    setFilteredDocuments(filtered);
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetFilters = () => {
    setFilters({
      customerId: '',
      status: '',
      documentType: '',
      dateFrom: '',
      dateTo: ''
    });
  };

  const handleApproveDocument = async (docId) => {
    try {
      setProcessingDoc(docId);
      
      // Call API to update document status
      const response = await fetch('/api/documents/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: docId,
          status: 'approved',
          adminId: 'admin1' // In a real app, this would be the logged-in admin's ID
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Update document status in state
        setDocuments(documents.map(doc => 
          doc.id === docId ? { ...doc, status: 'approved' } : doc
        ));
      } else {
        console.error('Failed to approve document:', result.error);
        alert(`Failed to approve document: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error approving document:', error);
      alert(`Error approving document: ${error.message}`);
    } finally {
      setProcessingDoc(null);
    }
  };

  const handleRejectDocument = async (docId) => {
    try {
      setProcessingDoc(docId);
      
      // Call API to update document status
      const response = await fetch('/api/documents/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: docId,
          status: 'rejected',
          adminId: 'admin1' // In a real app, this would be the logged-in admin's ID
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Update document status in state
        setDocuments(documents.map(doc => 
          doc.id === docId ? { ...doc, status: 'rejected' } : doc
        ));
      } else {
        console.error('Failed to reject document:', result.error);
        alert(`Failed to reject document: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error rejecting document:', error);
      alert(`Error rejecting document: ${error.message}`);
    } finally {
      setProcessingDoc(null);
    }
  };

  const handleViewDocument = (document) => {
    setPreviewDocument(document);
    setIsPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setPreviewDocument(null);
  };

  const handleReuploadDocument = (document) => {
    setUploadInfo({
      customerId: document.customerId,
      customerName: document.customerName,
      documentType: document.documentType
    });
    setIsUploadOpen(true);
  };

  const handleNewDocumentUpload = async (customer) => {
    // If we only have the ID, fetch the full customer details
    let customerDetails = customer;
    
    if (customer && customer.id && !customer.fullName) {
      try {
        const response = await fetch(`/api/customers/${customer.id}`);
        if (response.ok) {
          const data = await response.json();
          const customerData = data.customer || data;
          
          customerDetails = {
            id: customerData.id,
            fullName: customerData.personalDetails?.name || customerData.name || 
                    `${customerData.personalDetails?.firstName || ''} ${customerData.personalDetails?.lastName || ''}`,
          };
        }
      } catch (error) {
        console.error('Error fetching customer details:', error);
      }
    }
    
    setUploadInfo({
      customerId: customerDetails.id,
      customerName: customerDetails.fullName || customerDetails.name || 'Khách hàng',
      documentType: null,
      allowTypeSelection: true
    });
    setIsUploadOpen(true);
  };

  const handleCloseUpload = () => {
    setIsUploadOpen(false);
    setUploadInfo(null);
  };

  const handleDocumentUploaded = (newDocument) => {
    // Add the new document to our list and refresh
    fetchDocuments();
  };

  return (
    <>
      <DashboardSection 
        title="Quản lý giấy tờ" 
        icon={<FaFileAlt className="text-blue-600" />}
        defaultOpen={true}
      >
        {/* Customer Selection for Document Upload */}
        <div className="bg-white p-4 rounded-lg shadow mb-4">
          <div className="flex items-center mb-2">
            <FaUser className="text-gray-500 mr-2" />
            <h3 className="font-medium">Tải lên giấy tờ cho khách hàng</h3>
          </div>
          <CustomersList onSelectCustomer={(customerId) => {
            // Create a temporary customer object with just the ID
            const tempCustomer = { id: customerId };
            handleNewDocumentUpload(tempCustomer);
          }} />
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <FaFilter className="text-gray-500 mr-2" />
              <h3 className="font-medium">Bộ lọc</h3>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <FaUser className="inline mr-1" /> Khách hàng
              </label>
              <CustomersList 
                onSelectCustomer={(customerId) => {
                  handleFilterChange({
                    target: { name: 'customerId', value: customerId || '' }
                  });
                }}
                selectedCustomerId={filters.customerId}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Trạng thái
              </label>
              <select
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded p-2"
              >
                <option value="">Tất cả trạng thái</option>
                <option value="pending">Đang chờ phê duyệt</option>
                <option value="approved">Đã phê duyệt</option>
                <option value="rejected">Đã từ chối</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Loại giấy tờ
              </label>
              <select
                name="documentType"
                value={filters.documentType}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded p-2"
              >
                <option value="">Tất cả loại</option>
                {Object.entries(DOCUMENT_TYPES).map(([key, { label }]) => (
                  <option key={key} value={key}>{label}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <FaCalendarAlt className="inline mr-1" /> Từ ngày
              </label>
              <input
                type="date"
                name="dateFrom"
                value={filters.dateFrom}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <FaCalendarAlt className="inline mr-1" /> Đến ngày
              </label>
              <input
                type="date"
                name="dateTo"
                value={filters.dateTo}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={resetFilters}
                className="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300"
              >
                Đặt lại bộ lọc
              </button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <FaSpinner className="animate-spin text-blue-500 mr-2" />
            <span>Đang tải giấy tờ...</span>
          </div>
        ) : filteredDocuments.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredDocuments.map(doc => (
              <DocumentApprovalCard 
                key={doc.id} 
                document={doc} 
                onApprove={handleApproveDocument}
                onReject={handleRejectDocument}
                onViewDocument={handleViewDocument}
                onReupload={handleReuploadDocument}
                isProcessing={processingDoc === doc.id}
              />
            ))}
          </div>
        ) : (
          <div>
            <p className="text-gray-500 mb-6">Không tìm thấy giấy tờ nào phù hợp với bộ lọc</p>
            
            {!documents.length && (
              <div className="mb-4">
                <h4 className="font-medium mb-2">Tải lên giấy tờ mới</h4>
                <CustomersList onSelectCustomer={(customerId) => {
                  // Create a temporary customer object with just the ID
                  const tempCustomer = { id: customerId };
                  handleNewDocumentUpload(tempCustomer);
                }} />
              </div>
            )}
          </div>
        )}
      </DashboardSection>

      {/* Document Preview Modal */}
      {previewDocument && (
        <DocumentPreviewModal 
          document={previewDocument}
          isOpen={isPreviewOpen}
          onClose={handleClosePreview}
        />
      )}

      {/* Document Upload Modal */}
      {uploadInfo && (
        <DocumentUploadModal 
          customerId={uploadInfo.customerId}
          customerName={uploadInfo.customerName}
          documentType={uploadInfo.documentType}
          isOpen={isUploadOpen}
          onClose={handleCloseUpload}
          onUpload={handleDocumentUploaded}
          allowTypeSelection={uploadInfo.allowTypeSelection}
        />
      )}
    </>
  );
};

export default DocumentApproval; 