import { useState, useContext } from 'react'
import { SiteContext } from '../context/mainContext'
import emailjs from 'emailjs-com'
import axios from 'axios'
import Button from './Button'
import { FaBell } from 'react-icons/fa'

const SubscriptionPanel = ({ shopId, sku }) => {
  const [subscribed, setSubscribed] = useState(false)
  const [status, setStatus] = useState('')
  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext)
  const [email, setEmail] = useState(contextEmail || '')
  const [phone, setPhone] = useState(contextPhone || '')
  const [isFocused, setIsFocused] = useState(false)

  const handleFocus = () => setIsFocused(true)
  const handleBlur = () => setIsFocused(false)

  const handleEmailChange = (event) => {
    setEmail(event.target.value)
  }

  const handlePhoneChange = (event) => {
    setPhone(event.target.value)
  }

  const handleSubscription = async () => {
    if (!(email || phone)) {
      setStatus('Xin điền email hoặc điện thoại để nhận thông báo.')
      setTimeout(() => setStatus(''), 3000)
      return
    }

    try {
      if (!subscribed) {
        setSubscribed(true)
        
        await emailjs.send(
          "shopme.aws.emailjs",
          "voaa_email_template_1",
          {
            message: `${email}\n${phone}\n${sku}\n\nURL: ${window.location.href}`,
          },
          "QFig8Xb2RUx8ujepR"
        )

        const payload = {
          active: "1",
          userid: email,
          email: email,
          mobile: phone,
          shop: shopId,
          product: sku,
          notes: "",
        }

        await axios.post("/api/registerUser", payload)
      } else {
        setSubscribed(false)
      }
    } catch (error) {
      console.error("An error occurred while subscribing user:", error)
      setSubscribed(false)
      setStatus('Có lỗi xảy ra. Vui lòng thử lại sau.')
      setTimeout(() => setStatus(''), 3000)
    }
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex items-center space-x-2">
        <Button
          title={subscribed ? "ĐÃ ĐĂNG KÝ" : "BÁO CHO TÔI"}
          onClick={handleSubscription}
          disabled={subscribed}
          small={true}
        />
        {!subscribed && (
          <div className="flex items-center">
            <FaBell className="w-6 h-6 text-yellow-500" />
            <span className="text-gray-600">
              Báo cho tôi khi sản phẩm này có voucher
            </span>
          </div>
        )}
      </div>

      {status && (
        <div className="text-red-600 text-sm">
          {status}
        </div>
      )}

      {subscribed && (
        <div className="text-green-600">
          [Chúng tôi sẽ báo quý khách khi shop này có chương trình.]
        </div>
      )}

      <div style={{ maxWidth: "100%", overflowX: "auto" }}>
        <table style={{ width: "100%" }}>
          <tbody>
            <tr>
              <td style={{ width: "30%" }}>
                <label htmlFor="email">Email:</label>
              </td>
              <td style={{ width: "70%" }}>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={handleEmailChange}
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  required
                  style={{
                    borderBottom: "1px dotted black",
                    width: "100%",
                  }}
                />
              </td>
            </tr>
            <tr>
              <td>
                <label htmlFor="phone">Phone:</label>
              </td>
              <td>
                <input
                  type="tel"
                  id="phone"
                  value={phone}
                  onChange={handlePhoneChange}
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  required
                  style={{
                    borderBottom: "1px dotted black",
                    width: "100%",
                  }}
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default SubscriptionPanel 