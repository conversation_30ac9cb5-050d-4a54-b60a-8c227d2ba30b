import React from 'react';
import DENOMINAT<PERSON> from '../utils/currencyProvider';
import Image from './Image';

const ListItemShop = ({ link, title, imageSrc, price, priceUpper, openInNewTab, sku }) => {
  return (    
    <div className="w-full p1 sm:p-2 sm:w-1/4 md:w-1/6 lg:w-1/8 mb-12 aspect-w-1 aspect-h-1">
      {/* Added mb-4 class to add bottom margin */}
      <a href={link} {...(openInNewTab && { target: '_blank', rel: 'noopener noreferrer' })} aria-label={title}>
        <div className="h-full flex justify-center items-center boxstyle_1 relative">
          <div className="flex flex-column justify-center items-center">
            <Image alt={title} src={imageSrc} className="w-3/5 h-full" />
          </div>
        </div>
      </a>
      <div>
        <p className="m-4 text-center text-l mb-1">{title}</p>
        {price && (
          <p className="text-center text-gray-700 mb-4">SKU: {sku} {`${DENOMINATION} ${price}`} {priceUpper && ` - ${DENOMINATION}${priceUpper}`} </p>
        )}
      </div>
    </div>
  );
};

export default ListItemShop;
