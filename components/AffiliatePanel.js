import Button from "./Button"

const AffiliatePanel = ({
  storeId,
  isAdvanceMode,
  emptyAffiliateArray,
  applyAffiliate,
  affiliateCodeInput,
  handleAffiliateDropdownChange,
  handleAffiliateCodeInputChange,
  affiliate,
  affiliateIds
}) => {
  return (
    <div className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-4 mb-4 md:mb-0 boxstyle_6">
      <h2 className="text-lg font-bold mb-4">
        BẠN BIẾT ĐẾN {storeId} QUA:
      </h2>
      <div className="flex flex-wrap items-center md:flex-row">
        {isAdvanceMode && (
          <div className="mb-4 md:mb-0 md:mr-4">
            <Button
              title="EMPTY!"
              onClick={emptyAffiliateArray}
              small={true}
            />
          </div>
        )}

        
        <select
          value={affiliateCodeInput}
          onChange={handleAffiliateDropdownChange}
          className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-3 mb-2 md:mb-0 md:mr-2 border border-gray-300 rounded-md select-dropdown"
        >
          <option value="">--INTERNET--</option>
          {affiliateIds.map((id) => (
            <option key={id} value={id}>
              {id}
            </option>
          ))}
        </select>
        <div className="mb-4 md:mb-0 md:mr-4">
          <Button
            title="ÁP DỤNG"
            onClick={applyAffiliate}
            small={true}
          />
        </div>

        <div className="flex flex-col md:flex-row items-center">
          {isAdvanceMode && (
            <input
              type="text"
              id="affiliateCodeInput"
              name="affiliateCodeInput"
              value={affiliateCodeInput}
              onChange={handleAffiliateCodeInputChange}
              className="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-3 mb-2 md:mb-0 md:mr-2 border border-gray-300 rounded-md"
              placeholder={affiliate ? affiliate : "MÃ CSKH"}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default AffiliatePanel 