import React, { useState, useEffect } from 'react';
import { Fa<PERSON>pinner, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';
import { isCustomerAuthenticated, getCustomerInfo, createTemporaryCustomer } from '../utils/customerAuth';

const RecipientInfoForm = ({ onComplete, orderId }) => {
  const [formData, setFormData] = useState({
    fullName: '',
    phoneNumber: '',
    email: '',
    address: '',
    city: '',
    district: ''
  });
  const [loading, setLoading] = useState(false);
  const [autofilled, setAutofilled] = useState(false);
  const [error, setError] = useState(null);
  
  // Check for customer data when component mounts
  useEffect(() => {
    if (isCustomerAuthenticated()) {
      const customerInfo = getCustomerInfo();
      if (customerInfo) {
        const updatedFormData = { ...formData };
        let wasUpdated = false;

        // Map customer information to form fields
        if (customerInfo.name) {
          updatedFormData.fullName = customerInfo.name;
          wasUpdated = true;
        }
        
        if (customerInfo.phone) {
          updatedFormData.phoneNumber = customerInfo.phone;
          wasUpdated = true;
        }
        
        if (customerInfo.email) {
          updatedFormData.email = customerInfo.email;
          wasUpdated = true;
        }
        
        if (customerInfo.address) {
          updatedFormData.address = customerInfo.address;
          wasUpdated = true;
        }
        
        if (customerInfo.city) {
          updatedFormData.city = customerInfo.city;
          wasUpdated = true;
        }
        
        if (customerInfo.district) {
          updatedFormData.district = customerInfo.district;
          wasUpdated = true;
        }
        
        if (wasUpdated) {
          setFormData(updatedFormData);
          setAutofilled(true);
        }
      }
    }
  }, []);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Create a temporary customer ID if no authentication exists
      let customerId = null;
      
      if (isCustomerAuthenticated()) {
        customerId = getCustomerInfo()?.id;
      } else {
        // Create a temporary customer
        customerId = createTemporaryCustomer({
          name: formData.fullName,
          phone: formData.phoneNumber,
          email: formData.email,
          address: formData.address,
          city: formData.city,
          district: formData.district
        });
      }
      
      // Create the data to send
      const profileData = {
        customerId, // Include the customer ID explicitly
        name: formData.fullName,
        phone: formData.phoneNumber,
        email: formData.email,
        address: formData.address,
        city: formData.city,
        district: formData.district
      };
      
      // Send data directly to the API using fetch
      const response = await fetch('/api/customer/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData)
      });
      
      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
      }
      
      // Update order information if orderId provided
      if (orderId) {
        await fetch('/api/order/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            customerId,
            orderId,
            recipientInfo: {
              ...formData,
              lastUpdated: new Date().toISOString()
            }
          })
        });
      }
      
      // Continue with the next step
      onComplete();
    } catch (error) {
      console.error('Error saving recipient information:', error);
      setError('Có lỗi xảy ra khi lưu thông tin. Vui lòng thử lại sau. Chi tiết: ' + error.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {autofilled && (
        <div className="bg-blue-50 border-l-4 border-blue-500 p-3 mb-4 text-blue-700 flex items-start">
          <FaInfoCircle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">
            Một số thông tin đã được điền tự động từ hồ sơ của bạn. Vui lòng kiểm tra lại và cập nhật nếu cần.
          </p>
        </div>
      )}
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 text-red-700 flex items-start">
          <FaExclamationTriangle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">{error}</p>
        </div>
      )}
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Họ và tên
        </label>
        <input
          type="text"
          name="fullName"
          value={formData.fullName}
          onChange={handleChange}
          className={`w-full p-2 border ${autofilled && formData.fullName ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
          placeholder="Nhập họ và tên người nhận"
          required
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Số điện thoại
          </label>
          <input
            type="tel"
            name="phoneNumber"
            value={formData.phoneNumber}
            onChange={handleChange}
            className={`w-full p-2 border ${autofilled && formData.phoneNumber ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
            placeholder="Nhập số điện thoại"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full p-2 border ${autofilled && formData.email ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
            placeholder="Nhập địa chỉ email"
            required
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Địa chỉ
        </label>
        <input
          type="text"
          name="address"
          value={formData.address}
          onChange={handleChange}
          className={`w-full p-2 border ${autofilled && formData.address ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
          placeholder="Nhập địa chỉ nhận hàng"
          required
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Thành phố
          </label>
          <input
            type="text"
            name="city"
            value={formData.city}
            onChange={handleChange}
            className={`w-full p-2 border ${autofilled && formData.city ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
            placeholder="Nhập thành phố"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Quận/Huyện
          </label>
          <input
            type="text"
            name="district"
            value={formData.district}
            onChange={handleChange}
            className={`w-full p-2 border ${autofilled && formData.district ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
            placeholder="Nhập quận/huyện"
            required
          />
        </div>
      </div>
      
      <button
        type="submit"
        className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center justify-center"
        disabled={loading}
      >
        {loading ? <FaSpinner className="animate-spin mr-2" /> : null}
        {loading ? 'Đang lưu...' : 'Lưu thông tin'}
      </button>
    </form>
  );
};

export default RecipientInfoForm; 