import React, { useState, useEffect } from 'react';
import { Fa<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { isCustomerAuthenticated, getCustomerInfo } from '../utils/customerAuth';
import { updateOrderInformation } from '../utils/customerAPI';

const PaymentForm = ({ onComplete, orderId }) => {
  const [paymentMethod, setPaymentMethod] = useState('');
  const [cardInfo, setCardInfo] = useState({
    cardNumber: '',
    cardName: '',
    expiryDate: '',
    cvv: ''
  });
  const [loading, setLoading] = useState(false);
  const [savedMethods, setSavedMethods] = useState([]);
  const [error, setError] = useState(null);
  
  // Check for saved payment methods
  useEffect(() => {
    if (isCustomerAuthenticated()) {
      const customerInfo = getCustomerInfo();
      if (customerInfo && customerInfo.paymentMethods) {
        // Get saved payment methods
        setSavedMethods(customerInfo.paymentMethods);
      }
    }
  }, []);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setCardInfo({ ...cardInfo, [name]: value });
  };
  
  const handleSelectSavedMethod = (methodId) => {
    setPaymentMethod(`saved_${methodId}`);
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Save payment method in the order
      if (orderId) {
        const paymentData = {
          method: paymentMethod,
          status: 'pending',
          lastUpdated: new Date().toISOString()
        };
        
        // Add card details if using a new card
        if (paymentMethod === 'card') {
          // Only store the last 4 digits of the card for security
          const lastFour = cardInfo.cardNumber.slice(-4);
          paymentData.card = {
            lastFour,
            cardName: cardInfo.cardName,
            expiryDate: cardInfo.expiryDate,
            // Don't store CVV
          };
        }
        
        await updateOrderInformation(orderId, {
          payment: paymentData
        });
      }
      
      // Continue with the next step
      onComplete();
    } catch (error) {
      console.error('Error processing payment:', error);
      setError('Có lỗi xảy ra khi xử lý thanh toán. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {savedMethods.length > 0 && (
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Phương thức thanh toán đã lưu</h4>
          <div className="space-y-2">
            {savedMethods.map((method, index) => (
              <div 
                key={index} 
                className={`border rounded p-3 cursor-pointer ${paymentMethod === `saved_${method.id}` ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                onClick={() => handleSelectSavedMethod(method.id)}
              >
                <div className="flex items-center">
                  <input
                    type="radio"
                    checked={paymentMethod === `saved_${method.id}`}
                    onChange={() => handleSelectSavedMethod(method.id)}
                    className="h-4 w-4 text-blue-600 border-gray-300"
                  />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-700">{method.name}</p>
                    <p className="text-xs text-gray-500">
                      {method.type === 'card' ? `**** **** **** ${method.lastFour}` : method.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 mb-2">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Hoặc sử dụng phương thức mới</span>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 gap-4">
        <div 
          className={`border rounded p-4 cursor-pointer ${paymentMethod === 'card' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
          onClick={() => setPaymentMethod('card')}
        >
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              className="h-4 w-4 text-blue-600 border-gray-300"
              checked={paymentMethod === 'card'}
              onChange={() => setPaymentMethod('card')}
              required
            />
            <div className="ml-3">
              <span className="block font-medium text-gray-700">Thẻ tín dụng/ghi nợ</span>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-2xl text-blue-500">💳</span>
                <span className="text-xs text-gray-500">Visa, MasterCard, JCB</span>
              </div>
            </div>
          </label>
          
          {paymentMethod === 'card' && (
            <div className="mt-4 ml-7 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Số thẻ
                </label>
                <input
                  type="text"
                  name="cardNumber"
                  value={cardInfo.cardNumber}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="1234 5678 9012 3456"
                  required={paymentMethod === 'card'}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tên chủ thẻ
                </label>
                <input
                  type="text"
                  name="cardName"
                  value={cardInfo.cardName}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="NGUYEN VAN A"
                  required={paymentMethod === 'card'}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hạn sử dụng
                  </label>
                  <input
                    type="text"
                    name="expiryDate"
                    value={cardInfo.expiryDate}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded"
                    placeholder="MM/YY"
                    required={paymentMethod === 'card'}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    CVV
                  </label>
                  <input
                    type="text"
                    name="cvv"
                    value={cardInfo.cvv}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 rounded"
                    placeholder="123"
                    required={paymentMethod === 'card'}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div 
          className={`border rounded p-4 cursor-pointer ${paymentMethod === 'bank' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
          onClick={() => setPaymentMethod('bank')}
        >
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              className="h-4 w-4 text-blue-600 border-gray-300"
              checked={paymentMethod === 'bank'}
              onChange={() => setPaymentMethod('bank')}
            />
            <div className="ml-3">
              <span className="block font-medium text-gray-700">Chuyển khoản ngân hàng</span>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-2xl text-blue-500">🏦</span>
                <span className="text-xs text-gray-500">Nhận hướng dẫn chuyển khoản qua email</span>
              </div>
            </div>
          </label>
        </div>
      </div>
      
      <div className="flex items-center mt-4">
        <input
          type="checkbox"
          id="savePaymentMethod"
          name="savePaymentMethod"
          className="h-4 w-4 text-blue-600 border-gray-300 rounded"
        />
        <label htmlFor="savePaymentMethod" className="ml-2 block text-sm text-gray-700">
          Lưu phương thức thanh toán này cho lần sau
        </label>
      </div>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 text-red-700 flex items-start">
          <FaExclamationTriangle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">{error}</p>
        </div>
      )}
      
      <button
        type="submit"
        className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center justify-center mt-4"
        disabled={!paymentMethod || loading}
      >
        {loading ? <FaSpinner className="animate-spin mr-2" /> : null}
        {loading ? 'Đang xử lý thanh toán...' : 'Thanh toán'}
      </button>
    </form>
  );
};

export default PaymentForm; 