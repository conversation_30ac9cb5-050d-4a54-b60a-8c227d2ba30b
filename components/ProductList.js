import React from 'react'
import ListItemProduct from './ListItemProduct'
import { slugify } from '../utils/helpers'

const ProductList = ({ 
  products = [], 
  store, 
  title,
  expandable = false,
  initialDisplayCount = 20,
  centerVertically = true,
  compact = false
}) => {
  const [expanded, setExpanded] = React.useState(false)
  const displayedProducts = expanded ? products : products.slice(0, initialDisplayCount)

  return (
    <div className="mb-8">
      {title && (
        <h2 className="text-xl font-bold mb-4">{title}</h2>
      )}

      {/* Product Grid - Added justify-center */}
      <div className={`
        flex flex-wrap -mx-2 
        ${centerVertically ? 'justify-center' : ''}
      `}>
        {displayedProducts.map((product, index) => {
          if (
            !product ||
            !product.image ||
            product.image.length === 0
          ) {
            return null
          }
          return (
            <ListItemProduct
              key={`${product.sku}-${index}`}
              link={`/${store}/product/${slugify(product.sku)}`}
              product={product}
              storeId={store}
              openInNewTab={false}
              compact={compact}
            />
          )
        })}
      </div>

      {/* Expand/Collapse Button */}
      {expandable && products.length > initialDisplayCount && (
        <div className="text-center mt-6">
          <button
            onClick={() => setExpanded(!expanded)}
            className="
              bg-orange-500 
              hover:bg-orange-600 
              text-white 
              px-6 
              py-2.5 
              rounded-lg
              font-medium
              transition-colors
              duration-200
              shadow-sm
              hover:shadow-md
            "
          >
            {expanded ? 'Thu gọn' : 'Xem thêm'}
          </button>
        </div>
      )}
    </div>
  )
}

export default ProductList 