import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { isUserLoggedIn, getCustomerData, fetchCustomerData, getDefaultFormValues } from '../utils/customerFormData';

const Input = ({ onChange, value, name, placeholder }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    type="text"
    placeholder={placeholder}
    name={name}
  />
);

/* const PaymentFormInquiry = ({ handleSubmit, errorMessage }) => { */
const PaymentFormInquiry = ({ handleSubmit, errorMessage, cart, orderId, paymentimage, shopId }) => {
  console.log("[INQUIRY_PAYMENT] Using order ID from checkout:", orderId);
  
  const [input, setInput] = useState({
    topic: `[NEW ORDER: SHOP ${shopId}]`,
    shopId: shopId,
    name: "",
    email: "",
    mobile: "",
    message: ""
  });

  const [showThankMessage, setShowThankMessage] = useState(false); // State to manage the visibility of the thank you message
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);

  // Fetch customer data if logged in
  useEffect(() => {
    const loadCustomerData = async () => {
      try {
        setIsLoadingUserData(true);
        
        // Check if user is logged in
        if (isUserLoggedIn(shopId)) {
          console.log('[PAYMENT_FORM] User is logged in, fetching data');
          
          // Get customer data from localStorage
          const customerData = getCustomerData(shopId);
          
          if (customerData) {
            console.log('[PAYMENT_FORM] Using customer data from localStorage');
            
            // Try to get the latest data from API if we have an ID
            let freshData = customerData;
            if (customerData.id) {
              const apiData = await fetchCustomerData(customerData.id);
              if (apiData) {
                console.log('[PAYMENT_FORM] Updated customer data from API');
                freshData = apiData;
              }
            }
            
            // Get default form values from customer data
            const defaultValues = getDefaultFormValues(freshData);
            
            // Update input state with customer data
            setInput(prevInput => ({
              ...prevInput,
              name: defaultValues.name,
              email: defaultValues.email,
              mobile: defaultValues.mobile || defaultValues.phone
            }));
          }
        }
      } catch (error) {
        console.error('[PAYMENT_FORM] Error loading customer data:', error);
      } finally {
        setIsLoadingUserData(false);
      }
    };
    
    loadCustomerData();
  }, [shopId]);

  const onChange = e => {
    setInput({ ...input, [e.target.name]: e.target.value });
  };

  const handleSubmitInquiry = async (event, cart) => {
    event.preventDefault();
  
    try {
      await axios.post('/api/createOrder', { ...input, cart }); // Include cart data in the request body
      await axios.post('/api/sendEmail', { ...input, cart }); // Include cart data in the request body
      console.log('Email sent successfully');
      setShowThankMessage(true); // Show the thank you message after successful email submission
    } catch (error) {
      console.error('Failed to send email:', error);
    }
  };
  
  
  return (
    <div className="flex flex-1 pt-8 flex-col">
      <div className="mt-4 border-t pt-10">
        {isLoadingUserData ? (
          <div className="flex justify-center items-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-800"></div>
            <span className="ml-2 text-gray-600">Đang tải thông tin...</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            {errorMessage ? <span>{errorMessage}</span> : ""}
            <Input
              onChange={onChange}
              value={input.name}
              name="name"
              placeholder="Your name"
            />
            <Input
              onChange={onChange}
              value={input.email}
              name="email"
              placeholder="Email"
            />
            <Input
              onChange={onChange}
              value={input.mobile}
              name="mobile"
              placeholder="Mobile"
            />          
            <Input
              onChange={onChange}
              value={input.message}
              name="message"
              placeholder="Message"
            />
            {showThankMessage && <p>The order request has been sent. We shall get back to you soon. Thank you for your business!</p>} {/* Display the thank you message if showThankMessage is true */}
            <button
              type="submit"            
              /* onClick={handleSubmitInquiry} */
              onClick={(event) => handleSubmitInquiry(event, cart)}
              className="hidden md:block bg-primary hover:bg-black text-white font-bold py-2 px-4 mt-4 rounded focus:outline-none focus:shadow-outline"            
            >
              ĐẶT HÀNG
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

export default PaymentFormInquiry;
