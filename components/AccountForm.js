import React, { useState, useEffect } from 'react';
import { FaInfoCircle, <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { isCustomerAuthenticated, getCustomerInfo } from '../utils/customerAuth';
import { updateCustomerProfile, updateOrderInformation } from '../utils/customerAPI';

const AccountForm = ({ onComplete, orderId }) => {
  const [formData, setFormData] = useState({
    fullName: '',
    accountId: '',
    email: '',
    phone: '',
    saveInfo: true
  });
  const [loading, setLoading] = useState(false);
  const [autofilled, setAutofilled] = useState(false);
  const [error, setError] = useState(null);
  
  // Check for customer data when component mounts
  useEffect(() => {
    if (isCustomerAuthenticated()) {
      const customerInfo = getCustomerInfo();
      if (customerInfo) {
        const updatedFormData = { ...formData };
        let wasUpdated = false;

        // Map customer information to form fields
        if (customerInfo.name) {
          updatedFormData.fullName = customerInfo.name;
          wasUpdated = true;
        }
        
        if (customerInfo.accountId) {
          updatedFormData.accountId = customerInfo.accountId;
          wasUpdated = true;
        }
        
        if (customerInfo.email) {
          updatedFormData.email = customerInfo.email;
          wasUpdated = true;
        }
        
        if (customerInfo.phone) {
          updatedFormData.phone = customerInfo.phone;
          wasUpdated = true;
        }
        
        if (wasUpdated) {
          setFormData(updatedFormData);
          setAutofilled(true);
        }
      }
    }
  }, []);
  
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Update account information in customer profile if opted in
      if (formData.saveInfo && isCustomerAuthenticated()) {
        await updateCustomerProfile({
          name: formData.fullName,
          accountId: formData.accountId,
          email: formData.email,
          phone: formData.phone,
          lastUpdated: new Date().toISOString()
        });
      }
      
      // Update order information if orderId provided
      if (orderId) {
        await updateOrderInformation(orderId, {
          accountInfo: {
            fullName: formData.fullName,
            accountId: formData.accountId,
            email: formData.email,
            phone: formData.phone,
            lastUpdated: new Date().toISOString()
          }
        });
      }
      
      // Continue with the next step
      onComplete();
    } catch (error) {
      console.error('Error saving account information:', error);
      setError('Có lỗi xảy ra khi lưu thông tin tài khoản. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {autofilled && (
        <div className="bg-blue-50 border-l-4 border-blue-500 p-3 mb-4 text-blue-700 flex items-start">
          <FaInfoCircle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">
            Thông tin tài khoản đã được điền từ hồ sơ của bạn. Vui lòng kiểm tra và cập nhật nếu cần.
          </p>
        </div>
      )}
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 text-red-700 flex items-start">
          <FaExclamationTriangle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">{error}</p>
        </div>
      )}
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Họ và tên chủ tài khoản
        </label>
        <input
          type="text"
          name="fullName"
          value={formData.fullName}
          onChange={handleChange}
          className={`w-full p-2 border ${autofilled && formData.fullName ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
          placeholder="Nhập họ và tên chủ tài khoản"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Mã tài khoản (nếu có)
        </label>
        <input
          type="text"
          name="accountId"
          value={formData.accountId}
          onChange={handleChange}
          className={`w-full p-2 border ${autofilled && formData.accountId ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
          placeholder="Nhập mã tài khoản nếu bạn đã có"
        />
        <p className="text-xs text-gray-500 mt-1">Để trống nếu bạn chưa có tài khoản, chúng tôi sẽ tạo tài khoản mới cho bạn</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full p-2 border ${autofilled && formData.email ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
            placeholder="Nhập địa chỉ email"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Số điện thoại
          </label>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            className={`w-full p-2 border ${autofilled && formData.phone ? 'border-green-300 bg-green-50' : 'border-gray-300'} rounded`}
            placeholder="Nhập số điện thoại"
            required
          />
        </div>
      </div>
      
      <div className="flex items-center">
        <input
          type="checkbox"
          id="saveInfo"
          name="saveInfo"
          checked={formData.saveInfo}
          onChange={handleChange}
          className="h-4 w-4 text-blue-600 border-gray-300 rounded"
        />
        <label htmlFor="saveInfo" className="ml-2 block text-sm text-gray-700">
          Lưu thông tin này cho lần mua hàng tiếp theo
        </label>
      </div>
      
      <button
        type="submit"
        className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center justify-center"
        disabled={loading}
      >
        {loading ? <FaSpinner className="animate-spin mr-2" /> : null}
        {loading ? 'Đang xác thực tài khoản...' : 'Xác nhận thông tin tài khoản'}
      </button>
    </form>
  );
};

export default AccountForm; 