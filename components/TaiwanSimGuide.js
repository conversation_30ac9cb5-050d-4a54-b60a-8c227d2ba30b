import React from 'react';

const TaiwanSimGuide = () => {
  return (
    <div className="my-4 px-4 md:px-0 md:my-8">
      <div className="bg-gray-400 py-4 md:py-6 px-4 mb-4 md:mb-6 rounded-lg shadow-md">
        <h2 className="text-white text-2xl md:text-3xl font-bold text-center">SIM ĐÀI LOAN</h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Step 1 */}
        <div className="relative">
          <div className="bg-yellow-400 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">CUNG CẤP THÔNG TIN ĐẶT SIM</h3>
          </div>
          <div className="bg-white rounded-b-lg p-4 shadow-md border-2 border-yellow-400">
            <ul className="list-disc pl-5 space-y-2">
              <li>
                Kh<PERSON>ch hàng chụp ảnh 2 loại giấy tờ: hộ chiếu / thẻ cư trú đài loan/ thẻ khám bệnh / VISA (đối với khách mới nhập cảnh)
              </li>
              <li>
                NHẬN hotline để nhân viên check số hộ chiếu trước khi đặt
              </li>
            </ul>
          </div>
          {/* Arrow pointing right - only visible on medium screens and up */}
          <div className="hidden md:block absolute -right-8 top-1/2 transform -translate-y-1/2 z-10">
            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="#F59E0B" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>

        {/* Step 2 */}
        <div className="relative">
          <div className="bg-green-500 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">THÔNG TIN NGƯỜI NHẬN</h3>
          </div>
          <div className="bg-white rounded-b-lg p-4 shadow-md border-2 border-green-500">
            <ul className="list-disc pl-5 space-y-2">
              <li>
                Cho địa chỉ, số điện thoại, tên người nhận.
              </li>
              <li>
                Hình thức thanh toán: Nhận hàng thanh toán tại Taiwan (<span className="font-bold">nhận tại cửa hàng tiện ích</span> (7-11/FAMILY/HI-LIFE/OL MARK) tầm <span className="font-bold">2-4</span> hôm nhận được & <span className="font-bold">TẠI NHÀ</span> (tầm <span className="font-bold">1-3</span> hôm nhận được)
              </li>
              <li>
                KHÁCH HÀNG CÓ THỂ THEO DÕI ĐƠN TRÊN HỆ THỐNG
              </li>
            </ul>
          </div>
          {/* Arrow pointing right - only visible on medium screens and up */}
          <div className="hidden md:block absolute -right-8 top-1/2 transform -translate-y-1/2 z-10">
            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="#3B82F6" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>

        {/* Step 3 */}
        <div>
          <div className="bg-blue-500 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">KÍCH HOẠT SIM</h3>
          </div>
          <div className="bg-white rounded-b-lg p-4 shadow-md border-2 border-blue-500">
            <ul className="list-disc pl-5 space-y-2">
              <li>
                Khách hàng nhận vào hotline, chụp ảnh người cầm sim cùng mặt để kích hoạt sim
              </li>
              <li>
                Hệ thống sẽ có tin nhắn nhắc ngày sắp hết hạn mạng
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Mobile help button */}
      <div className="fixed bottom-6 right-6 md:hidden">
        <button className="bg-blue-500 text-white p-4 rounded-full shadow-lg transform transition-transform hover:scale-110">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default TaiwanSimGuide; 