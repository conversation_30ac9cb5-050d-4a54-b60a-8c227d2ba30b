import React, { useState } from 'react';
import { Send, User, Mail, MessageSquare, CheckCircle, AlertCircle } from 'lucide-react';

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [status, setStatus] = useState({
    loading: false,
    error: null,
    success: false
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setStatus({ loading: true, error: null, success: false });

    try {
      const response = await fetch('/api/contactFormApi', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      setStatus({ loading: false, error: null, success: true });
      setFormData({ name: '', email: '', message: '' });

      // Reset success message after 5 seconds
      setTimeout(() => {
        setStatus(prev => ({ ...prev, success: false }));
      }, 5000);

    } catch (error) {
      setStatus({
        loading: false,
        error: error.message,
        success: false
      });
    }
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-6">
        
        {/* Name input */}
        <div className="group">
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <User className="w-4 h-4 mr-2 text-gray-500" />
            Họ và tên <span className="text-red-500 ml-1">*</span>
          </label>
          <div className="relative">
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Nhập họ và tên của bạn"
              className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-all duration-300 bg-white hover:border-gray-400"
              required
            />
            <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 transition-colors duration-300 group-focus-within:text-gray-600" />
          </div>
        </div>

        {/* Email input */}
        <div className="group">
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <Mail className="w-4 h-4 mr-2 text-gray-500" />
            Email <span className="text-red-500 ml-1">*</span>
          </label>
          <div className="relative">
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-all duration-300 bg-white hover:border-gray-400"
              required
            />
            <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 transition-colors duration-300 group-focus-within:text-gray-600" />
          </div>
        </div>

        {/* Message textarea */}
        <div className="group">
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <MessageSquare className="w-4 h-4 mr-2 text-gray-500" />
            Tin nhắn <span className="text-red-500 ml-1">*</span>
          </label>
          <div className="relative">
            <textarea
              name="message"
              value={formData.message}
              onChange={handleChange}
              placeholder="Bạn cần hỗ trợ gì? Hãy mô tả chi tiết để chúng tôi có thể hỗ trợ bạn tốt nhất..."
              className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-all duration-300 bg-white hover:border-gray-400 min-h-[120px] resize-none"
              required
            />
            <MessageSquare className="absolute left-4 top-4 w-4 h-4 text-gray-400 transition-colors duration-300 group-focus-within:text-gray-600" />
          </div>
          <p className="text-xs text-gray-500 mt-1 flex items-center">
            <span className="mr-1">💡</span>
            Càng chi tiết càng giúp chúng tôi hỗ trợ bạn tốt hơn
          </p>
        </div>

        {/* Error message */}
        {status.error && (
          <div className="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 animate-fadeIn">
            <AlertCircle className="w-5 h-5 mr-3 flex-shrink-0" />
            <div>
              <p className="font-medium">Có lỗi xảy ra</p>
              <p className="text-sm">{status.error}</p>
            </div>
          </div>
        )}

        {/* Success message */}
        {status.success && (
          <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg text-green-700 animate-fadeIn">
            <CheckCircle className="w-5 h-5 mr-3 flex-shrink-0" />
            <div>
              <p className="font-medium">Gửi tin nhắn thành công! 🎉</p>
              <p className="text-sm">Chúng tôi sẽ phản hồi bạn trong vòng 24 giờ</p>
            </div>
          </div>
        )}

        {/* Submit button */}
        <button
          type="submit"
          disabled={status.loading}
          className={`w-full bg-gray-900 hover:bg-gray-800 text-white px-6 py-4 rounded-lg font-medium transition-all duration-300 flex items-center justify-center space-x-3 ${
            status.loading ? 'opacity-70 cursor-not-allowed' : 'hover:shadow-lg'
          }`}
        >
          
          {status.loading ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Đang gửi...</span>
            </>
          ) : (
            <>
              <span>Gửi tin nhắn</span>
              <Send className="w-5 h-5" />
            </>
          )}
        </button>

        {/* Additional info */}
        <div className="text-center">
                      <p className="text-xs text-gray-500 leading-relaxed">
            Bằng cách gửi tin nhắn, bạn đồng ý với{' '}
            <a href="#" className="text-gray-700 hover:text-gray-900 hover:underline transition-colors duration-200">
              chính sách bảo mật
            </a>{' '}
            của chúng tôi. Thông tin của bạn sẽ được bảo mật tuyệt đối.
          </p>
        </div>
      </form>

      {/* Custom styles for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default ContactForm;
