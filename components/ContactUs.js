import React from 'react'
import Link from 'next/link'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faEnvelope, faPhone } from '@fortawesome/free-solid-svg-icons'
import { 
  faLine,
  faWhatsapp,
  faFacebook
} from '@fortawesome/free-brands-svg-icons'
import ContactForm from './ContactForm'
import FAQSection from './faq/FAQSection'

// Mock FAQ data
const faqData = [
  {
    id: '1',
    category: 'SIM Online Store',
    question: "Làm thế nào để theo dõi đơn hàng của tôi?",
    answer: "Bạn có thể theo dõi đơn hàng bằng cách đăng nhập vào tài khoản và kiểm tra trong phần 'Đơn hàng của tôi' hoặc sử dụng mã đơn hàng được gửi qua email."
  },
  {
    id: '2',
    category: 'SIM Online Store',
    question: "<PERSON><PERSON>h sách đổi trả như thế nào?",
    answer: "<PERSON>úng tôi chấp nhận đổi trả trong vòng 7 ngày kể từ ngày nhận hàng, với điều kiện sản phẩm còn nguyên vẹn và có đầy đủ hóa đơn mua hàng."
  },
  {
    id: '3',
    category: 'SIM Online Store',
    question: "Các phương thức thanh toán được chấp nhận?",
    answer: "Chúng tôi chấp nhận thanh toán qua thẻ tín dụng/ghi nợ, chuyển khoản ngân hàng, và thanh toán khi nhận hàng (COD)."
  }
]

const ContactUs = ({ storeData = {} }) => {
  // Helper function to check if a contact method should be displayed
  const shouldDisplayContact = (value) => {
    return value && value !== "" && value !== "0" && value !== null && value !== undefined;
  };

  // Helper function to check if a specific social network exists in the social array
  const hasSocialNetwork = (socialArray, networkName) => {
    if (!Array.isArray(socialArray)) return false;
    return socialArray.some(item => item.network === networkName && item.handle);
  };

  // Get social network data
  const getSocialNetwork = (socialArray, networkName) => {
    if (!Array.isArray(socialArray)) return null;
    return socialArray.find(item => item.network === networkName && item.handle);
  };

  return (
    <div className="space-y-8">
      {/* Contact Form */}
      <div>
        <ContactForm />
      </div>

      {/* Contact Methods Grid */}
      {(hasSocialNetwork(storeData?.social, 'line') || 
        hasSocialNetwork(storeData?.social, 'whatsapp') || 
        shouldDisplayContact(storeData?.mobile?.[0]) || 
        hasSocialNetwork(storeData?.social, 'facebook') || 
        shouldDisplayContact(storeData?.email?.[0])) && (
        <div className="border-t border-gray-200 pt-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">
            Liên Hệ Trực Tiếp
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            
            {/* LINE Contact */}
            {hasSocialNetwork(storeData?.social, 'line') && (
              <a 
                href={`https://line.me/ti/p/${getSocialNetwork(storeData.social, 'line')?.handle}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-300"
              >
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <FontAwesomeIcon icon={faLine} className="text-white text-lg" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="font-semibold text-gray-900">LINE</p>
                  <p className="text-sm text-gray-600">@{getSocialNetwork(storeData.social, 'line')?.handle}</p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-300">→</div>
              </a>
            )}

            {/* WhatsApp Contact */}
            {hasSocialNetwork(storeData?.social, 'whatsapp') && (
              <a 
                href={`https://wa.me/${getSocialNetwork(storeData.social, 'whatsapp')?.handle}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-300"
              >
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <FontAwesomeIcon icon={faWhatsapp} className="text-white text-lg" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="font-semibold text-gray-900">WhatsApp</p>
                  <p className="text-sm text-gray-600">{getSocialNetwork(storeData.social, 'whatsapp')?.handle}</p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-300">→</div>
              </a>
            )}

            {/* Facebook Contact */}
            {hasSocialNetwork(storeData?.social, 'facebook') && (
              <a 
                href={`https://facebook.com/${getSocialNetwork(storeData.social, 'facebook')?.handle}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-300"
              >
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <FontAwesomeIcon icon={faFacebook} className="text-white text-lg" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="font-semibold text-gray-900">Facebook</p>
                  <p className="text-sm text-gray-600">@{getSocialNetwork(storeData.social, 'facebook')?.handle}</p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-300">→</div>
              </a>
            )}

            {/* Phone Contact */}
            {shouldDisplayContact(storeData?.mobile?.[0]) && (
              <a 
                href={`tel:${storeData.mobile[0]}`}
                className="group flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-300"
              >
                <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <FontAwesomeIcon icon={faPhone} className="text-white text-lg" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="font-semibold text-gray-900">Điện Thoại</p>
                  <p className="text-sm text-gray-600">{storeData.mobile[0]}</p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-300">→</div>
              </a>
            )}

            {/* Email Contact */}
            {shouldDisplayContact(storeData?.email?.[0]) && (
              <a 
                href={`mailto:${storeData.email[0]}`}
                className="group flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-300"
              >
                <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                  <FontAwesomeIcon icon={faEnvelope} className="text-white text-lg" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="font-semibold text-gray-900">Email</p>
                  <p className="text-sm text-gray-600 truncate">{storeData.email[0]}</p>
                </div>
                <div className="text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-300">→</div>
              </a>
            )}
          </div>

          {/* Contact Tips */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                <span className="text-gray-600 text-sm">💡</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">Mẹo liên hệ hiệu quả</h4>
                <p className="text-sm text-gray-700 leading-relaxed">
                  Để được hỗ trợ nhanh nhất, vui lòng cung cấp thông tin chi tiết về vấn đề của bạn. 
                  Chúng tôi cam kết phản hồi trong vòng 24 giờ.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContactUs 