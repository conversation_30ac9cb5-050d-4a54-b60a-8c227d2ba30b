import Link from 'next/link'
import { getTrimmedString } from '../../utils/helpers'
import Image from '../Image'

const DisplaySmall = ({ link, title, subtitle, imageSrc, validity, territory }) =>  (
  <div className="lg:w-flex-fourth 
  px-6 pt-10 pb-2 lg:p-6 lg:pb-0
  lg:mb-0 mb-4
  boxstyle_1">
    <Link href={link} aria-label={title}>

      <div className="flex flex-column justify-center items-center h-32">
        {/* <Image alt={title} src={imageSrc} className="w-3/5" /> */}
        <Image alt={title} src={imageSrc} className="max-w-full h-full" />
      </div>
      <div className="">
        <p className="text-xl mb-1">{title}</p>
        <p className="text-xs text-gray-700 mb-2">{getTrimmedString(subtitle, 150)}</p>
        {validity && <p className="text-xs text-gray-600 mb-1">Validity: {validity}</p>}
        {territory && <p className="text-xs text-gray-600 mb-4">Territory: {territory}</p>}
      </div>

    </Link>
  </div>
)

export default DisplaySmall