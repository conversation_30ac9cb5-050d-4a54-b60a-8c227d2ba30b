import Image from '../Image';
import Link from 'next/link';
import Button from '../Button';


/* const downloadImage = (imageLink) => {
  // Create a new anchor element
  const anchor = document.createElement('a');
  
  // Set the href attribute to the image URL
  //anchor.href = '/payment_qrcode_thanhson_tcb.jpg';
  anchor.href = imageLink !== null && imageLink !== undefined && imageLink !== "" ? imageLink : '/payment_qrcode_thanhson_tcb.jpg'
  
  // Set the download attribute to force download
  anchor.download = imageLink !== null && imageLink !== undefined && imageLink !== "" ? imageLink : 'payment_qrcode_thanhson_tcb.jpg'
  
  // Simulate a click on the anchor element to trigger the download
  anchor.click();
} */

const downloadImage = (imageLink) => {
  // Create a new anchor element
  const anchor = document.createElement('a');

  // Set the href attribute to the image URL
  anchor.href = imageLink;

  // Generate a filename based on the image link
  let filename;
  if (imageLink.startsWith('data:image')) {
    // If the image link is a data URL, generate a unique filename
    const timestamp = new Date().getTime();
    filename = `image_${timestamp}.jpg`;
  } else {
    // Extract the filename from the image link
    filename = imageLink.substring(imageLink.lastIndexOf('/') + 1);
  }

  // Set the download attribute to force download with the filename
  anchor.download = filename;

  // Simulate a click on the anchor element to trigger the download
  anchor.click();
}




const DisplayMediumNoBox = ({ imageSrc, title, subtitle, link, buttonText }) => {
  return (
    <>
      <div className="mb-4 lg:mb-0 p-8 pb-0 flex flex-col justify-center items-center">
        {link !== "" ? ( // Check if link is not empty
          <Link href={link} aria-label={title}>
            {/* Mobile Layout */}
            <div className="aspect-w-16 aspect-h-9 lg:hidden">
              <Image src={imageSrc} alt={title} className="object-cover w-full h-full" />
            </div>
            {/* Desktop Layout */}
            <div className="h-80 flex justify-center items-center hidden lg:flex">
              <Image src={imageSrc} alt={title} className="max-w-full h-full" />
            </div>
            <div className="mb-8 text-center">
              <p className="text-3xl font-semibold mb-1">{title}</p>
              <p className="text-xs text-gray-700">{subtitle}</p>
            </div>
          </Link>
        ) : ( // If link is empty, only display the image
          <div>
            {/* Mobile Layout */}
            <div className="aspect-w-16 aspect-h-9 lg:hidden">
              {/* <Image src={imageSrc} alt={title} className="object-cover w-full h-full" /> */}
              <Image src={imageSrc} alt={title} className="object-cover w-full" />
            </div>
            {/* Desktop Layout */}
            <div className="h-80 flex justify-center items-center hidden lg:flex">
              <Image src={imageSrc} alt={title} className="max-w-full" />
              {/* <Image src={imageSrc} alt={title} className="max-w-full h-full" /> */}
            </div>
            <div className="mb-8 text-center">
              <p className="text-3xl font-semibold mb-1">{title}</p>
              <p className="text-xs text-gray-700">{subtitle}</p>
            </div>
          </div>
        )}
        <Button
          /* onClick={downloadImage} */
          onClick={() => downloadImage(imageSrc)}
          title={buttonText ? buttonText : "QR DOWNLOAD"}
        />
      </div>
    </>
  );
}


export default DisplayMediumNoBox;
