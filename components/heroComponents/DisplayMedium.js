import Image from '../Image'
import Link from 'next/link'

const DisplayMedium = ({ imageSrc, title, subtitle, link }) => {
  return (
    <div className="
    mb-4 lg:mb-0
    p-8 pb-0 
    boxstyle_1">
      <Link href={`${link}`} aria-label={title}>

        <div className="h-56 flex justify-center items-center ">
          <Image src={imageSrc} alt={title} className="max-w-full h-full"/>
        </div>
        <div className="mb-8">
          <p className="text-xl mb-1">{title}</p>
          <p className="text-xs text-gray-700">{subtitle}</p>
        </div>

      </Link>
    </div>
  );
}

export default DisplayMedium;