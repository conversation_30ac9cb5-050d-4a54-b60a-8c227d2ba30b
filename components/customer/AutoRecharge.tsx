import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSync, faCreditCard, faBell, faExclamationTriangle, faSimCard, faToggleOn, faToggleOff } from '@fortawesome/free-solid-svg-icons';

interface AutoRechargeSetting {
  isEnabled: boolean;
  threshold: number;
  amount: number;
}

interface Sim {
  id: string;
  phoneNumber: string;
  autoRecharge: {
    data: AutoRechargeSetting;
    voice: AutoRechargeSetting;
    sms: AutoRechargeSetting;
  };
}

type AutoRechargeCategory = 'data' | 'voice' | 'sms';

const AutoRecharge: React.FC = () => {
  const [sims, setSims] = useState<Sim[]>([
    {
      id: '1',
      phoneNumber: '+1234567890',
      autoRecharge: {
        data: {
          isEnabled: false,
          threshold: 100,
          amount: 500,
        },
        voice: {
          isEnabled: true,
          threshold: 50,
          amount: 200,
        },
        sms: {
          isEnabled: false,
          threshold: 10,
          amount: 100,
        },
      },
    },
    {
      id: '2',
      phoneNumber: '+9876543210',
      autoRecharge: {
        data: {
          isEnabled: true,
          threshold: 200,
          amount: 1000,
        },
        voice: {
          isEnabled: false,
          threshold: 100,
          amount: 500,
        },
        sms: {
          isEnabled: true,
          threshold: 20,
          amount: 200,
        },
      },
    },
  ]);

  const handleToggle = (simId: string, category: AutoRechargeCategory, option: keyof AutoRechargeSetting, value: boolean | number) => {
    setSims((prevSims) =>
      prevSims.map((sim) => {
        if (sim.id === simId) {
          return {
            ...sim,
            autoRecharge: {
              ...sim.autoRecharge,
              [category]: {
                ...sim.autoRecharge[category],
                [option]: value,
              },
            },
          };
        }
        return sim;
      })
    );
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Auto Recharge Settings</h2>
        <div className="space-y-6">
          {sims.map((sim) => (
            <div key={sim.id} className="border rounded-lg p-4">
              <div className="flex items-center mb-4">
                <FontAwesomeIcon icon={faSimCard} className="text-indigo-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">{sim.phoneNumber}</h3>
              </div>
              <div className="space-y-4">
                {Object.entries(sim.autoRecharge).map(([category, settings]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 capitalize">{category}</h4>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => handleToggle(sim.id, category as AutoRechargeCategory, 'isEnabled', !settings.isEnabled)}
                          className="text-indigo-600 hover:text-indigo-800"
                        >
                          <FontAwesomeIcon
                            icon={settings.isEnabled ? faToggleOn : faToggleOff}
                            className="text-2xl"
                          />
                        </button>
                        <span className="text-sm text-gray-500">
                          {settings.isEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                      {settings.isEnabled && (
                        <div className="flex items-center space-x-4">
                          <div>
                            <label className="text-xs text-gray-500">Threshold</label>
                            <input
                              type="number"
                              value={settings.threshold}
                              onChange={(e) =>
                                handleToggle(
                                  sim.id,
                                  category as AutoRechargeCategory,
                                  'threshold',
                                  parseInt(e.target.value)
                                )
                              }
                              className="ml-2 w-20 px-2 py-1 border rounded text-sm"
                            />
                          </div>
                          <div>
                            <label className="text-xs text-gray-500">Amount</label>
                            <input
                              type="number"
                              value={settings.amount}
                              onChange={(e) =>
                                handleToggle(
                                  sim.id,
                                  category as AutoRechargeCategory,
                                  'amount',
                                  parseInt(e.target.value)
                                )
                              }
                              className="ml-2 w-20 px-2 py-1 border rounded text-sm"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AutoRecharge; 