import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser,
  faSimCard,
  faWallet,
  faShoppingBag,
  faCreditCard,
  faGift,
  faQuestionCircle,
  faStore,
} from '@fortawesome/free-solid-svg-icons';

// Import all components
import Profile from './Profile';
import SimManagement from './SimManagement';
import KycVerification from './KycVerification';
import DataUsage from './DataUsage';
import SimSettings from './SimSettings';
import TopUpHistory from './TopUpHistory';
import RechargePlans from './RechargePlans';
import AutoRecharge from './AutoRecharge';
import PaymentMethods from './PaymentMethods';
import RewardsProgram from './RewardsProgram';
import SupportCenter from './SupportCenter';

interface MenuItem {
  id: string;
  title: string;
  icon: any;
  component: React.FC;
}

const CustomerDashboard: React.FC = () => {
  const [activeSection, setActiveSection] = useState('profile');

  const menuItems: MenuItem[] = [
    {
      id: 'profile',
      title: 'Hồ Sơ & Thông Tin Cá Nhân',
      icon: faUser,
      component: Profile,
    },
  ];

  const ActiveComponent = menuItems.find(item => item.id === activeSection)?.component || Profile;

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      {/* Top Header with Shop Icon */}
      <div className="w-full bg-white shadow-sm py-3">
        <div className="flex justify-center items-center">
          <FontAwesomeIcon icon={faStore} className="text-3xl text-indigo-600" />
        </div>
      </div>
      
      {/* Main Content Area */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-md">
          <div className="p-4">
            <h2 className="text-xl font-bold text-gray-800">Cổng Khách Hàng</h2>
          </div>
          <nav className="mt-4">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center px-4 py-3 text-left ${
                  activeSection === item.id
                    ? 'bg-indigo-50 text-indigo-600'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <FontAwesomeIcon icon={item.icon} className="mr-3" />
                <span>{item.title}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          <ActiveComponent />
        </div>
      </div>
    </div>
  );
};

export default CustomerDashboard; 