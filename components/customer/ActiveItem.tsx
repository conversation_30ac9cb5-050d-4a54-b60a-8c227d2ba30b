import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCreditCard, faFileInvoice, faEdit, faSave, faTimes, faTrash, faUser } from '@fortawesome/free-solid-svg-icons';

// Field metadata to track who added/modified fields
interface FieldMetadata {
  addedBy?: string; // user ID or identifier
  addedByName?: string; // user display name
  addedAt?: string; // timestamp
  isUserAdded?: boolean; // flag to identify user-added fields
}

// Assigned column value interface
interface AssignedColumnValue {
  columnIndex: number;
  columnLetter: string;
  value: string;
}

// Order item interface with field metadata
interface OrderItem {
  name: string;
  price: number | string;
  quantity: number;
  image?: string;
  sku?: string;
  type?: 'sim' | 'topup' | 'device' | 'service' | 'other';
  // SIM specific fields
  simNumber?: string;
  phoneNumber?: string;
  iccid?: string;
  networkProvider?: string;
  simStatus?: string;
  // Topup card specific fields
  cardNumber?: string;
  cardPin?: string;
  cardSerial?: string;
  topupCode?: string;
  expiryDate?: string;
  // Device specific fields
  imei?: string;
  model?: string;
  color?: string;
  // Service specific fields
  servicePeriod?: string;
  serviceStartDate?: string;
  serviceEndDate?: string;
  // Field metadata for tracking ownership
  _fieldMetadata?: {
    [key: string]: FieldMetadata;
  };
}

interface ActiveItemProps {
  orderId: string;
  item: OrderItem;
  orderDate: string;
  formatDate: (dateString: string) => string;
  onItemUpdate?: (orderId: string, itemIndex: number, updatedItem: OrderItem) => void;
  itemIndex?: number;
  currentUser?: {
    id: string;
    name: string;
  };
  assignedColumnValues?: AssignedColumnValue[];
}

// Customer-relevant field types (fewer than admin)
const customerFieldTypes = [
  // SIM fields
  { label: "Số SIM", key: "simNumber" },
  { label: "Số điện thoại", key: "phoneNumber" },
  { label: "ICCID", key: "iccid" },
  { label: "Nhà mạng", key: "networkProvider" },
  // Topup card fields
  { label: "Mã thẻ", key: "cardNumber" },
  { label: "Mã PIN", key: "cardPin" },
  { label: "Serial thẻ", key: "cardSerial" },
  { label: "Mã nạp", key: "topupCode" },
  { label: "Hạn dùng", key: "expiryDate" },
  // Device fields
  { label: "IMEI", key: "imei" },
  { label: "Model", key: "model" },
  { label: "Màu sắc", key: "color" },
  // Service fields
  { label: "Thời hạn dịch vụ", key: "servicePeriod" },
  { label: "Ngày bắt đầu", key: "serviceStartDate" },
  { label: "Ngày kết thúc", key: "serviceEndDate" },
];

const ActiveItem: React.FC<ActiveItemProps> = ({ 
  orderId, 
  item, 
  orderDate, 
  formatDate, 
  onItemUpdate,
  itemIndex = 0,
  currentUser,
  assignedColumnValues
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedFieldType, setSelectedFieldType] = useState('');
  const [fieldValue, setFieldValue] = useState('');
  const [updateLoading, setUpdateLoading] = useState(false);
  const [localItem, setLocalItem] = useState<OrderItem>(item);
  const [deleteLoading, setDeleteLoading] = useState<string>('');

  // Helper function to check if current user can delete a field
  const canDeleteField = (fieldKey: string): boolean => {
    if (!currentUser) return false;
    
    const metadata = localItem._fieldMetadata?.[fieldKey];
    if (!metadata) return false; // Only user-added fields have metadata
    
    return metadata.addedBy === currentUser.id;
  };

  // Helper function to get field metadata
  const getFieldMetadata = (fieldKey: string): FieldMetadata | undefined => {
    return localItem._fieldMetadata?.[fieldKey];
  };

  const handleDeleteField = async (fieldKey: string) => {
    if (!canDeleteField(fieldKey)) {
      alert('Bạn chỉ có thể xóa những thông tin do bạn thêm vào.');
      return;
    }

    try {
      setDeleteLoading(fieldKey);

      // Update local item by removing the field and its metadata
      const updatedItem = { ...localItem };
      delete updatedItem[fieldKey as keyof OrderItem];
      
      // Also remove metadata for this field
      if (updatedItem._fieldMetadata) {
        const newMetadata = { ...updatedItem._fieldMetadata };
        delete newMetadata[fieldKey];
        updatedItem._fieldMetadata = newMetadata;
      }
      
      setLocalItem(updatedItem);
      
      // Save to the server with null/empty value to delete the field
      const response = await fetch(`/api/orders/item-update?orderId=${encodeURIComponent(orderId)}&itemId=${encodeURIComponent(itemIndex)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          [fieldKey]: null, // or empty string depending on your API preference
          _fieldMetadata: updatedItem._fieldMetadata
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Server responded with ${response.status}`);
      }
      
      // Notify parent component if callback provided
      if (onItemUpdate) {
        onItemUpdate(orderId, itemIndex, updatedItem);
      }
      
    } catch (error) {
      console.error('Error deleting field:', error);
      alert('Lỗi khi xóa thông tin: ' + error.message);
      // Restore original item on error
      setLocalItem(item);
    } finally {
      setDeleteLoading('');
    }
  };

  const handleSaveField = async () => {
    if (!selectedFieldType || !fieldValue || !currentUser) {
      return;
    }

    try {
      setUpdateLoading(true);

      // Create metadata for the new field
      const fieldMetadata: FieldMetadata = {
        addedBy: currentUser.id,
        addedByName: currentUser.name,
        addedAt: new Date().toISOString(),
        isUserAdded: true
      };

      // Update local item with new field and metadata
      const updatedItem = {
        ...localItem,
        [selectedFieldType]: fieldValue,
        _fieldMetadata: {
          ...localItem._fieldMetadata,
          [selectedFieldType]: fieldMetadata
        }
      };
      
      setLocalItem(updatedItem);
      
      // Save to the server including metadata
      const response = await fetch(`/api/orders/item-update?orderId=${encodeURIComponent(orderId)}&itemId=${encodeURIComponent(itemIndex)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          [selectedFieldType]: fieldValue,
          _fieldMetadata: updatedItem._fieldMetadata
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Server responded with ${response.status}`);
      }
      
      // Notify parent component if callback provided
      if (onItemUpdate) {
        onItemUpdate(orderId, itemIndex, updatedItem);
      }
      
      // Reset the editing state
      setIsEditing(false);
      setSelectedFieldType('');
      setFieldValue('');
      
    } catch (error) {
      console.error('Error updating item details:', error);
      alert('Lỗi khi cập nhật thông tin: ' + error.message);
      // Restore original item on error
      setLocalItem(item);
    } finally {
      setUpdateLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setSelectedFieldType('');
    setFieldValue('');
    setLocalItem(item); // Reset to original
  };

  // Helper function to render field with conditional delete button and ownership indicator
  const renderFieldWithDelete = (label: string, value: string | number, fieldKey: string) => {
    const metadata = getFieldMetadata(fieldKey);
    const canDelete = canDeleteField(fieldKey);
    const isUserAdded = metadata?.isUserAdded || false;

    return (
      <div className="flex justify-between items-center group">
        <div className="flex justify-between flex-1 mr-2">
          <div className="flex items-center">
            <span className="text-gray-500">{label}:</span>
            {isUserAdded && (
              <div className="ml-1 flex items-center" title={`Được thêm bởi: ${metadata?.addedByName || 'Người dùng'}`}>
                <FontAwesomeIcon icon={faUser} className="h-2 w-2 text-blue-500" />
                <span className="text-xs text-blue-500 ml-1">({metadata?.addedByName || 'User'})</span>
              </div>
            )}
          </div>
          <span>{value}</span>
        </div>
        {canDelete && (
          <button
            onClick={() => handleDeleteField(fieldKey)}
            disabled={deleteLoading === fieldKey || updateLoading}
            className="opacity-0 group-hover:opacity-100 transition-opacity p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
            title={`Xóa ${label.toLowerCase()}`}
          >
            {deleteLoading === fieldKey ? (
              <span className="text-xs">...</span>
            ) : (
              <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
            )}
          </button>
        )}
      </div>
    );
  };

  // Helper function to render regular fields (without delete functionality)
  const renderField = (label: string, value: string | number) => (
    <div className="flex justify-between">
      <span className="text-gray-500">{label}:</span>
      <span>{value}</span>
    </div>
  );

  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
      <div className="flex items-start">
        <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
          <FontAwesomeIcon 
            icon={localItem.type === 'topup' ? faCreditCard : faFileInvoice} 
            className="h-5 w-5 text-gray-500" 
          />
        </div>
        <div className="flex-grow">
          <p className="text-sm font-medium text-gray-800">{localItem.name}</p>
        
          {/* Display all item data dynamically */}
          <div className="mt-1 space-y-1 text-xs text-gray-600">
            {/* Basic item information - these are system fields, no delete option */}
            {localItem.price && renderField("Giá", typeof localItem.price === 'number' ? localItem.price.toLocaleString() : localItem.price)}
            {localItem.quantity && renderField("Số lượng", localItem.quantity)}
            {localItem.type && renderField("Loại", localItem.type)}
            
            {/* Fields that can potentially be user-added or system fields */}
            {localItem.sku && renderFieldWithDelete("SKU", localItem.sku, "sku")}
            
            {/* SIM specific fields */}
            {localItem.simNumber && renderFieldWithDelete("SIM", localItem.simNumber, "simNumber")}
            {localItem.phoneNumber && renderFieldWithDelete("Số điện thoại", localItem.phoneNumber, "phoneNumber")}
            {localItem.iccid && renderFieldWithDelete("ICCID", localItem.iccid, "iccid")}
            {localItem.networkProvider && renderFieldWithDelete("Nhà mạng", localItem.networkProvider, "networkProvider")}
            {localItem.simStatus && renderFieldWithDelete("Trạng thái SIM", localItem.simStatus, "simStatus")}
            
            {/* Topup card specific fields */}
            {localItem.cardSerial && renderFieldWithDelete("Serial", localItem.cardSerial, "cardSerial")}
            {localItem.cardNumber && renderFieldWithDelete("Mã thẻ", localItem.cardNumber, "cardNumber")}
            {localItem.cardPin && renderFieldWithDelete("Mã PIN", localItem.cardPin, "cardPin")}
            {localItem.topupCode && renderFieldWithDelete("Mã nạp", localItem.topupCode, "topupCode")}
            {localItem.expiryDate && renderFieldWithDelete("Hạn dùng", localItem.expiryDate, "expiryDate")}
            
            {/* Device specific fields */}
            {localItem.imei && renderFieldWithDelete("IMEI", localItem.imei, "imei")}
            {localItem.model && renderFieldWithDelete("Model", localItem.model, "model")}
            {localItem.color && renderFieldWithDelete("Màu sắc", localItem.color, "color")}
            
            {/* Service specific fields */}
            {localItem.servicePeriod && renderFieldWithDelete("Thời hạn dịch vụ", localItem.servicePeriod, "servicePeriod")}
            {localItem.serviceStartDate && renderFieldWithDelete("Ngày bắt đầu", localItem.serviceStartDate, "serviceStartDate")}
            {localItem.serviceEndDate && renderFieldWithDelete("Ngày kết thúc", localItem.serviceEndDate, "serviceEndDate")}
            
            {/* Assigned Column Values */}
            {assignedColumnValues && assignedColumnValues.length > 0 && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="text-xs font-medium text-gray-700 mb-1">Giá trị được gán:</div>
                <div className="grid grid-cols-2 gap-1">
                  {assignedColumnValues.map((columnValue, index) => (
                    <div key={index} className="flex justify-between text-xs">
                      <span className="text-gray-500">Cột {columnValue.columnLetter}:</span>
                      <span className="font-mono text-gray-800">{columnValue.value || '(trống)'}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Add information button - always show, but functionality requires user */}
            {!isEditing && (
              <div className="mt-2">
                <button 
                  className="px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 border border-blue-600 rounded hover:bg-blue-50"
                  onClick={() => {
                    if (!currentUser) {
                      alert('Bạn cần đăng nhập để thêm thông tin.');
                      return;
                    }
                    setIsEditing(true);
                    setSelectedFieldType('');
                    setFieldValue('');
                  }}
                  disabled={updateLoading || deleteLoading !== ''}
                >
                  + Thêm thông tin
                </button>
              </div>
            )}
          </div>

          {/* Editing Form */}
          {isEditing && (
            <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <h4 className="text-sm font-medium text-blue-800 mb-2">Thêm/cập nhật thông tin</h4>
              
              <div className="mb-2">
                <label className="text-xs text-blue-700 block mb-1">Loại thông tin:</label>
                <select 
                  className="w-full p-2 text-xs border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={selectedFieldType}
                  onChange={(e) => setSelectedFieldType(e.target.value)}
                >
                  <option value="">-- Chọn loại thông tin --</option>
                  {customerFieldTypes.map((type) => (
                    <option key={type.key} value={type.key}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="mb-3">
                <label className="text-xs text-blue-700 block mb-1">Giá trị:</label>
                <input 
                  type="text"
                  className="w-full p-2 text-xs border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={fieldValue}
                  onChange={(e) => setFieldValue(e.target.value)}
                  placeholder={`Nhập ${customerFieldTypes.find(t => t.key === selectedFieldType)?.label || 'giá trị'}`}
                />
              </div>
              
              <div className="flex space-x-2">
                <button 
                  className="px-3 py-1 text-xs text-white bg-blue-600 rounded-md hover:bg-blue-700 flex items-center flex-1 justify-center"
                  onClick={handleSaveField}
                  disabled={updateLoading || !selectedFieldType || !fieldValue || deleteLoading !== ''}
                >
                  <FontAwesomeIcon icon={faSave} className="mr-1" />
                  {updateLoading ? 'Đang lưu...' : 'Lưu'}
                </button>
                <button 
                  className="px-3 py-1 text-xs text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 flex items-center flex-1 justify-center"
                  onClick={handleCancelEdit}
                  disabled={updateLoading || deleteLoading !== ''}
                >
                  <FontAwesomeIcon icon={faTimes} className="mr-1" />
                  Hủy
                </button>
              </div>
            </div>
          )}
          
          <div className="mt-2 text-xs text-gray-500">
            <div>Đơn hàng: #{orderId}</div>
            <div>Mua ngày: {formatDate(orderDate)}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActiveItem;