import React, { useState, useEffect, useMemo, useRef } from 'react';
import axios from 'axios';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShoppingBag, faTruck, faCheckCircle, faClock, faTimesCircle, faFileInvoice, faExclamationTriangle, faRedo, faChevronDown, faChevronUp, faMapMarkerAlt, faMoneyBill, faCreditCard, faCalendarAlt, faUser, faPhone, faEnvelope, faInfoCircle, faExternalLinkAlt, faFilter, faTimes } from '@fortawesome/free-solid-svg-icons';
import JsBarcode from 'jsbarcode';
import PaymentBarcodeDisplay from '../payment/BarcodeDisplay';
import OrderCard from './OrderCard';
import ActiveItem from './ActiveItem';

// Interfaces for order data
interface OrderItem {
  name: string;
  price: number | string;
  quantity: number;
  image?: string;
  sku?: string;
  type?: 'sim' | 'topup' | 'device' | 'service' | 'other';
  // SIM specific fields
  simNumber?: string;
  phoneNumber?: string;
  iccid?: string;
  networkProvider?: string;
  simStatus?: string;
  // Topup card specific fields
  cardNumber?: string;
  cardPin?: string;
  cardSerial?: string;
  topupCode?: string;
  expiryDate?: string;
  // Device specific fields
  imei?: string;
  model?: string;
  color?: string;
  // Service specific fields
  servicePeriod?: string;
  serviceStartDate?: string;
  serviceEndDate?: string;
  // Usage tracking fields
  usedFor?: string; // Description of how the item was used (e.g., phone number topped up, person given to)
  usageNotes?: string; // Additional notes about usage
  usageDate?: string; // When the item was used
  usageUpdatedAt?: string; // When usage info was last updated
}

interface OrderNote {
  id: string;
  text: string;
  createdAt: string;
  updatedAt: string;
}

interface Order {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  items: OrderItem[];
  totalAmount: number;
  amount?: number;
  currency: string;
  status: string;
  paymentStatus: string;
  paymentMethod: string;
  paymentSubMethod?: string;
  createdAt: string;
  updatedAt: string;
  validUntil: string;
  isExpired: boolean;
  storeId?: string;
  paymentInfo?: any;
  shippingAddress?: string;
  fee: number;
  notes?: OrderNote[];
  // Google Sheets metadata
  metadata?: {
    type: string;
    sheetsId?: string;
    assignedColumns?: any;
    assignedColumnValues?: {
      columnIndex: number;
      columnLetter: string;
      value: string;
    }[];
    assignedSku?: string;
  };
}

// Filter types
type StatusFilter = 'all' | 'paid' | 'pending' | 'expired' | 'cancelled' | 'not_yet_paid';
type TimeFilter = 'all_time' | 'today' | 'yesterday' | 'this_week' | 'last_week' | 'this_month' | 'last_month' | 'this_year' | 'last_year';

interface OrdersProps {
  customerId?: string | null;
  isAdminMode?: boolean;
  allCustomers?: boolean;
  onCustomerSelect?: (customerId: string) => void;
  initialFilters?: {
    status?: StatusFilter;
    time?: TimeFilter;
    searchTerm?: string;
  };
}

// Removed BarcodeDisplay component - now using consolidated PaymentBarcodeDisplay

const Orders: React.FC<OrdersProps> = ({ 
  customerId, 
  isAdminMode = false, 
  allCustomers = false,
  onCustomerSelect,
  initialFilters = {}
}) => {
  const router = useRouter();
  const { store: storeParam } = router.query;
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customerPhone, setCustomerPhone] = useState<string | null>(null);
  
  // Filter states
  /* const [statusFilter, setStatusFilter] = useState<StatusFilter>(initialFilters.status || 'not_yet_paid'); */
  const [statusFilter, setStatusFilter] = useState<StatusFilter>(initialFilters.status || 'all');
  const [timeFilter, setTimeFilter] = useState<TimeFilter>(initialFilters.time || 'all_time');
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState(initialFilters.searchTerm || '');
  const [customerList, setCustomerList] = useState<{id: string; name: string; phone: string}[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(customerId || null);
  const [currentUser, setCurrentUser] = useState<{ id: string; name: string } | null>(null);

  const fetchOrders = async (customerIdentifier: string) => {
    try {
      setLoading(true);
      
      // Use the same admin orders endpoint and filter client-side
      const response = await axios.get(`/api/admin/orders${storeParam ? `?storeId=${storeParam}` : ''}`);
      
      if (response.data.success) {
        const allOrders = response.data.orders || [];
        
        // Filter orders for the specific customer
        // Determine if the identifier is a phone number or customer ID
        const isPhoneNumber = /^\d+$/.test(customerIdentifier);
        
        const filteredOrders = allOrders.filter(order => {
          if (isPhoneNumber) {
            // Match by phone number
            return order.customerPhone === customerIdentifier || 
                   order.customerId === customerIdentifier;
          } else {
            // Match by customer ID or name
            return order.customerId === customerIdentifier ||
                   order.customerName === customerIdentifier ||
                   order.customerPhone === customerIdentifier;
          }
        });
        
        setOrders(filteredOrders);
      } else {
        throw new Error(response.data.message || 'Không thể lấy dữ liệu đơn hàng');
      }
    } catch (err: any) {
      console.error('Lỗi khi tải dữ liệu đơn hàng:', err);
      setError(err.message || 'Có lỗi xảy ra khi tải đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  // New function to fetch all orders (for admin mode)
  const fetchAllOrders = async () => {
    try {
      setLoading(true);
      
      const response = await axios.get(`/api/admin/orders${storeParam ? `?storeId=${storeParam}` : ''}`);
      
      if (response.data.success) {
        setOrders(response.data.orders || []);
      } else {
        throw new Error(response.data.message || 'Không thể lấy dữ liệu đơn hàng');
      }
    } catch (err: any) {
      console.error('Lỗi khi tải dữ liệu đơn hàng:', err);
      setError(err.message || 'Có lỗi xảy ra khi tải đơn hàng');
    } finally {
      setLoading(false);
    }
  };
  
  // New function to fetch customer list (for admin mode) - Extract from orders.json
  const fetchCustomerList = async () => {
    try {
      // Use the same admin orders endpoint to get all orders, then extract unique customers
      const response = await axios.get(`/api/admin/orders${storeParam ? `?storeId=${storeParam}` : ''}`);
      
      if (response.data.success) {
        const allOrders = response.data.orders || [];
        
        // Extract unique customers from orders
        const uniqueCustomers = new Map();
        
        allOrders.forEach(order => {
          const customerId = order.customerId || order.customerPhone || order.id;
          if (customerId && !uniqueCustomers.has(customerId)) {
            uniqueCustomers.set(customerId, {
              id: customerId,
              name: order.customerName || 'Unknown Customer',
              phone: order.customerPhone || 'No Phone',
              email: order.customerEmail || ''
            });
          }
        });
        
        // Convert map to array
        const customerList = Array.from(uniqueCustomers.values());
        setCustomerList(customerList);
      }
    } catch (err) {
      console.error('Error fetching customer list:', err);
    }
  };

  useEffect(() => {
    if (isAdminMode) {
      // In admin mode, set a generic admin user
      setCurrentUser({
        id: 'admin',
        name: 'Admin'
      });
      
      // In admin mode, fetch customer list for filtering
      fetchCustomerList();
      
      if (allCustomers) {
        // Fetch all orders
        fetchAllOrders();
      } else if (selectedCustomerId) {
        // Fetch orders for a specific customer
        fetchOrders(selectedCustomerId);
      } else {
        setOrders([]);
        setLoading(false);
      }
    } else {
      // Original behavior for customer view
      const getCustomerData = () => {
        // First check store-specific customer data
        const storeIdParam = storeParam as string;
        const storeSpecificData = storeIdParam ? localStorage.getItem(`${storeIdParam}_customerData`) : null;
        
        // Then check the generic customer data as fallback
        const genericData = localStorage.getItem('customerData');
        
        const customerData = storeSpecificData || genericData;
        
        if (customerData) {
          try {
            const customer = JSON.parse(customerData);
            return customer;
          } catch (err) {
            console.error('Lỗi khi đọc dữ liệu khách hàng:', err);
            return null;
          }
        }
        return null;
      };

      const loadOrders = async () => {
        // Get customer data and set current user
        const customer = getCustomerData();
        if (customer) {
          setCurrentUser({
            id: customer.id || customer.phone || customer.email || 'user',
            name: customer.name || customer.customerName || 'User'
          });
        }

        // First try to use the customerId prop if provided
        if (customerId) {
          setCustomerPhone(customerId);
          await fetchOrders(customerId);
          return;
        }
        
        // Otherwise fall back to localStorage data
        if (customer && customer.phone) {
          setCustomerPhone(customer.phone);
          await fetchOrders(customer.phone);
        } else if (customer && customer.id) {
          // Try using id as phone number fallback
          setCustomerPhone(customer.id);
          await fetchOrders(customer.id);
        } else {
          setError('Không tìm thấy thông tin liên hệ của khách hàng');
          setLoading(false);
        }
      };

      loadOrders();
    }
  }, [storeParam, customerId, isAdminMode, allCustomers, selectedCustomerId]);

  // Function to calculate order total
  const calculateOrderTotal = (order: Order) => {
    // If totalAmount exists and is not 0, use it
    if (order.totalAmount && order.totalAmount > 0) {
      return order.totalAmount;
    }
    
    // If amount exists and is not 0, use it as fallback
    if (order.amount && order.amount > 0) {
      return order.amount;
    }
    
    // Calculate from items if available
    if (order.items && order.items.length > 0) {
      const itemsTotal = order.items.reduce((total, item) => {
        const price = Number(item.price) || 0;
        const quantity = Number(item.quantity) || 0;
        return total + (price * quantity);
      }, 0);
      
      if (itemsTotal > 0) {
        return itemsTotal;
      }
    }
    
    // Final fallback
    return order.totalAmount || order.amount || 0;
  };

  // Utility functions
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (err) {
      return dateString || 'N/A';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'VND') => {
    if (!amount) return '0';
    
    if (!currency) {
      currency = 'VND';
    }
    
    if (currency === 'NT$' || currency === 'NT') {
      return `${amount.toLocaleString()} NT$`;
    }
    
    try {
      return amount.toLocaleString('vi-VN') + (currency ? ` ${currency}` : '');
    } catch (err) {
      console.error('Error formatting currency:', err);
      // Fallback formatting
      return `${amount} ${currency}`;
    }
  };

  const getStatusColor = (status: string) => {
    if (!status) return 'text-gray-600 bg-gray-100';
    
    switch (status.toLowerCase()) {
      case 'paid':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'expired':
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    if (!status) return 'text-gray-600 bg-gray-100';
    
    switch (status.toLowerCase()) {
      case 'paid':
        return 'text-green-600 bg-green-100';
      case 'not_paid':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
      case 'refunded':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    if (!status) return faShoppingBag;
    
    switch (status.toLowerCase()) {
      case 'paid':
        return faCheckCircle;
      case 'pending':
        return faClock;
      case 'expired':
      case 'cancelled':
        return faTimesCircle;
      default:
        return faShoppingBag;
    }
  };

  const getStatusText = (status: string) => {
    if (!status) return 'Không xác định';
    
    const statusMap: Record<string, string> = {
      'pending': 'Đang Chờ',
      'paid': 'Đã Thanh Toán',
      'expired': 'Hết Hạn',
      'cancelled': 'Đã Hủy',
      'not_yet_paid': 'Chờ Thanh Toán',
    };
    
    return statusMap[status.toLowerCase()] || status;
  };

  const getPaymentStatusText = (status: string) => {
    if (!status) return 'Không xác định';
    
    const statusMap: Record<string, string> = {
      'not_paid': 'Chưa Thanh Toán',
      'paid': 'Đã Thanh Toán',
      'failed': 'Thanh Toán Thất Bại',
      'refunded': 'Đã Hoàn Tiền',
    };
    
    return statusMap[status.toLowerCase()] || status;
  };

  const getTimeRemaining = (validUntil: string) => {
    const now = new Date();
    const expiryDate = new Date(validUntil);
    
    if (expiryDate <= now) {
      return 'Đã hết hạn';
    }
    
    const diffMs = expiryDate.getTime() - now.getTime();
    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHrs > 24) {
      const days = Math.floor(diffHrs / 24);
      return `${days} ngày ${diffHrs % 24} giờ`;
    }
    
    return `${diffHrs} giờ ${diffMins} phút`;
  };

  const handleRetryPayment = (order: Order) => {
    // Always use the current store from URL instead of order.storeId
    if (storeParam) {
      router.push(`/${storeParam}/cart`);
    }
  };

  // Generate barcode as base64 image for printing
  const generateBarcodeImage = (code: string): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      try {
        JsBarcode(canvas, code, {
          format: "CODE128",
          lineColor: "#000",
          width: 2,
          height: 60,
          displayValue: true,
          margin: 10,
          fontSize: 12,
          textAlign: "center",
          textPosition: "bottom",
          background: "#ffffff"
        });
        resolve(canvas.toDataURL('image/png'));
      } catch (error) {
        console.error('Error generating barcode image:', error);
        resolve('');
      }
    });
  };

  // Handle printing only the specific order
  const handlePrintOrder = async (order: Order) => {
    // Give time for any state update to render
    setTimeout(async () => {
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      
      if (!printWindow) {
        alert('Vui lòng cho phép trình duyệt mở cửa sổ mới để in hóa đơn');
        return;
      }
      
      // Get the current order element
      const orderElement = document.getElementById(`order-${order.id}`);
      
      if (!orderElement) {
        printWindow.close();
        return;
      }

      // Generate barcode images if this is an iBON payment
      let barcodeImages = { barcode1: '', barcode2: '', barcode3: '' };
      if (order.paymentMethod === 'convenience_store' && 
          order.paymentSubMethod === 'seven_eleven_ibon' && 
          order.paymentInfo?.barcodes) {
        try {
          const promises: Promise<string>[] = [];
          if (order.paymentInfo.barcodes.barcode1) {
            promises.push(generateBarcodeImage(order.paymentInfo.barcodes.barcode1));
          }
          if (order.paymentInfo.barcodes.barcode2) {
            promises.push(generateBarcodeImage(order.paymentInfo.barcodes.barcode2));
          }
          if (order.paymentInfo.barcodes.barcode3) {
            promises.push(generateBarcodeImage(order.paymentInfo.barcodes.barcode3));
          }
          
          const images = await Promise.all(promises);
          let imageIndex = 0;
          if (order.paymentInfo.barcodes.barcode1) {
            barcodeImages.barcode1 = images[imageIndex++];
          }
          if (order.paymentInfo.barcodes.barcode2) {
            barcodeImages.barcode2 = images[imageIndex++];
          }
          if (order.paymentInfo.barcodes.barcode3) {
            barcodeImages.barcode3 = images[imageIndex++];
          }
        } catch (error) {
          console.error('Error generating barcode images for print:', error);
        }
      }
      
      // Calculate payment method text
      let paymentText = '';
      if (order.paymentMethod === 'card') paymentText = 'Thẻ tín dụng/ghi nợ';
      else if (order.paymentMethod === 'cash') paymentText = 'Tiền mặt';
      else if (order.paymentMethod === 'transfer') paymentText = 'Chuyển khoản';
      else if (order.paymentMethod === 'convenience_store') paymentText = 'Cửa hàng tiện lợi';
      else paymentText = order.paymentMethod || 'Không có thông tin';
      
      if (order.paymentSubMethod) {
        if (order.paymentSubMethod === 'seven_eleven_ibon') paymentText += ' (7-Eleven ibon)';
        else if (order.paymentSubMethod === 'family_mart') paymentText += ' (FamilyMart)';
        else paymentText += ' (' + order.paymentSubMethod + ')';
      }
      
      // Create HTML content for the print window
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Đơn hàng #${order.id}</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
              line-height: 1.5;
              color: #374151;
              padding: 2rem;
            }
            .print-header {
              text-align: center;
              margin-bottom: 2rem;
              padding-bottom: 1rem;
              border-bottom: 1px solid #e5e7eb;
            }
            .print-header h1 {
              font-size: 1.5rem;
              font-weight: 600;
              margin-bottom: 0.5rem;
            }
            .print-header p {
              font-size: 0.875rem;
              color: #6b7280;
            }
            .print-section {
              margin-bottom: 1.5rem;
            }
            .print-section h2 {
              font-size: 1.125rem;
              font-weight: 500;
              margin-bottom: 0.75rem;
              padding-bottom: 0.5rem;
              border-bottom: 1px solid #e5e7eb;
            }
            .detail-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 1rem;
            }
            .detail-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 0.5rem;
            }
            .detail-label {
              font-size: 0.875rem;
              color: #6b7280;
            }
            .detail-value {
              font-size: 0.875rem;
              font-weight: 500;
            }
            table {
              width: 100%;
              border-collapse: collapse;
            }
            th {
              text-align: left;
              font-size: 0.75rem;
              text-transform: uppercase;
              padding: 0.75rem 0.5rem;
              background-color: #f3f4f6;
              border-bottom: 1px solid #e5e7eb;
            }
            td {
              padding: 0.75rem 0.5rem;
              border-bottom: 1px solid #e5e7eb;
              font-size: 0.875rem;
            }
            .text-right {
              text-align: right;
            }
            .text-center {
              text-align: center;
            }
            .item-details {
              font-size: 0.75rem;
              color: #6b7280;
              margin-top: 0.25rem;
            }
            .footer {
              text-align: center;
              margin-top: 2rem;
              padding-top: 1rem;
              border-top: 1px solid #e5e7eb;
              font-size: 0.75rem;
              color: #9ca3af;
            }
            @media print {
              body {
                padding: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Đơn hàng #${order.id}</h1>
            <p>Ngày đặt hàng: ${formatDate(order.createdAt)}</p>
          </div>
          
          <div class="print-section">
            <h2>Thông tin khách hàng</h2>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">Tên:</span>
                <span class="detail-value">${order.customerName || 'Không có thông tin'}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Điện thoại:</span>
                <span class="detail-value">${order.customerPhone || 'Không có thông tin'}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Email:</span>
                <span class="detail-value">${order.customerEmail || 'Không có thông tin'}</span>
              </div>
              ${order.shippingAddress ? '<div class="detail-item"><span class="detail-label">Địa chỉ giao hàng:</span><span class="detail-value">' + order.shippingAddress + '</span></div>' : ''}
            </div>
          </div>
          
          <div class="print-section">
            <h2>Thông tin thanh toán</h2>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">Phương thức thanh toán:</span>
                <span class="detail-value">
                  ${paymentText}
                </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Trạng thái thanh toán:</span>
                <span class="detail-value">${getPaymentStatusText(order.paymentStatus)}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Trạng thái đơn hàng:</span>
                <span class="detail-value">${getStatusText(order.status)}</span>
              </div>
              ${order.validUntil ? '<div class="detail-item"><span class="detail-label">Thời hạn thanh toán:</span><span class="detail-value">' + formatDate(order.validUntil) + '</span></div>' : ''}
            </div>
          </div>
          
          <div class="print-section">
            <h2>Chi tiết đơn hàng</h2>
            ${(() => {
              if (order.items && order.items.length > 0) {
                const itemsHtml = order.items.map(item => {
                  let itemDetails = '';
                  if (item.sku) itemDetails += `<div class="item-details">SKU: ${item.sku}</div>`;
                  
                  if (item.type === 'sim') {
                    let simDetails = '<div class="item-details">';
                    if (item.simNumber) simDetails += `SIM: ${item.simNumber}<br>`;
                    if (item.phoneNumber) simDetails += `SDT: ${item.phoneNumber}<br>`;
                    if (item.iccid) simDetails += `ICCID: ${item.iccid}<br>`;
                    if (item.networkProvider) simDetails += `Nhà mạng: ${item.networkProvider}`;
                    simDetails += '</div>';
                    itemDetails += simDetails;
                  }
                  
                  if (item.type === 'topup') {
                    let topupDetails = '<div class="item-details">';
                    if (item.cardSerial) topupDetails += `Serial: ${item.cardSerial}<br>`;
                    if (item.cardNumber) topupDetails += `Mã thẻ: ${item.cardNumber}<br>`;
                    if (item.cardPin) topupDetails += `Mã PIN: ${item.cardPin}<br>`;
                    if (item.topupCode) topupDetails += `Mã nạp: ${item.topupCode}<br>`;
                    if (item.expiryDate) topupDetails += `Hạn dùng: ${item.expiryDate}`;
                    topupDetails += '</div>';
                    itemDetails += topupDetails;
                  }
                  
                  if (item.type === 'device') {
                    let deviceDetails = '<div class="item-details">';
                    if (item.model) deviceDetails += `Model: ${item.model}<br>`;
                    if (item.imei) deviceDetails += `IMEI: ${item.imei}<br>`;
                    if (item.color) deviceDetails += `Màu sắc: ${item.color}`;
                    deviceDetails += '</div>';
                    itemDetails += deviceDetails;
                  }
                  
                  if (item.type === 'service') {
                    let serviceDetails = '<div class="item-details">';
                    if (item.servicePeriod) serviceDetails += `Thời hạn: ${item.servicePeriod}<br>`;
                    if (item.serviceStartDate) serviceDetails += `Ngày bắt đầu: ${item.serviceStartDate}<br>`;
                    if (item.serviceEndDate) serviceDetails += `Ngày kết thúc: ${item.serviceEndDate}`;
                    serviceDetails += '</div>';
                    itemDetails += serviceDetails;
                  }
                  
                  return `<tr><td>${item.name}${itemDetails}</td><td class="text-right">${formatCurrency(Number(item.price), order.currency)}</td><td class="text-center">${item.quantity}</td><td class="text-right">${formatCurrency(Number(item.price) * item.quantity, order.currency)}</td></tr>`;
                }).join('');
                
                const feeRow = order.fee > 0 ? `<tr><td colspan="3" class="text-right">Phí:</td><td class="text-right">${formatCurrency(order.fee, order.currency)}</td></tr>` : '';
                
                return `<table><thead><tr><th style="width: 50%;">Sản phẩm</th><th class="text-right">Giá</th><th class="text-center">SL</th><th class="text-right">Tổng</th></tr></thead><tbody>${itemsHtml}</tbody><tfoot><tr><td colspan="3" class="text-right" style="font-weight: 500;">Tổng cộng:</td><td class="text-right" style="font-weight: 600;">${formatCurrency(calculateOrderTotal(order), order.currency)}</td></tr>${feeRow}</tfoot></table>`;
              } else {
                const feeSection = order.fee > 0 ? `<div style="display: flex; justify-content: space-between;"><span>Phí:</span><span>${formatCurrency(order.fee, order.currency)}</span></div>` : '';
                return `<p>Không có thông tin chi tiết về sản phẩm</p><div style="margin-top: 1rem;"><div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;"><span style="font-weight: 500;">Tổng thanh toán:</span><span style="font-weight: 600;">${formatCurrency(calculateOrderTotal(order), order.currency)}</span></div>${feeSection}</div>`;
              }
            })()}
          </div>
          
          ${order.paymentMethod === 'convenience_store' && order.paymentInfo ? '<div class="print-section"><h2>Thông tin thanh toán tại cửa hàng tiện lợi</h2>' + (order.paymentSubMethod === 'seven_eleven_ibon' && order.paymentInfo.ibonPaymentCode ? '<div class="detail-grid"><div class="detail-item"><span class="detail-label">Mã thanh toán ibon:</span><span class="detail-value">' + order.paymentInfo.ibonPaymentCode + '</span></div>' + (order.paymentInfo.ibonShopId ? '<div class="detail-item"><span class="detail-label">Mã cửa hàng:</span><span class="detail-value">' + order.paymentInfo.ibonShopId + '</span></div>' : '') + '</div>' + ((order.paymentInfo.barcodes && (order.paymentInfo.barcodes.barcode1 || order.paymentInfo.barcodes.barcode2 || order.paymentInfo.barcodes.barcode3)) ? '<div style="margin-top: 1.5rem;"><h3 style="font-size: 1rem; font-weight: 500; margin-bottom: 1rem;">Mã vạch thanh toán:</h3><div style="display: grid; grid-template-columns: 1fr; gap: 1rem;">' + (order.paymentInfo.barcodes.barcode1 ? '<div style="border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 1rem; text-align: center;"><p style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem;">Mã vạch 1</p>' + (barcodeImages.barcode1 ? '<img src="' + barcodeImages.barcode1 + '" alt="Mã vạch 1" style="max-height: 80px; margin: 0 auto 0.5rem;">' : '<div style="border: 1px solid #d1d5db; padding: 0.75rem; margin: 0.5rem 0; font-family: monospace; font-size: 0.875rem; background-color: #f9fafb;">' + order.paymentInfo.barcodes.barcode1 + '</div>') + '<p style="font-family: monospace; font-size: 0.75rem; color: #6b7280;">' + order.paymentInfo.barcodes.barcode1 + '</p></div>' : '') + (order.paymentInfo.barcodes.barcode2 ? '<div style="border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 1rem; text-align: center;"><p style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem;">Mã vạch 2</p>' + (barcodeImages.barcode2 ? '<img src="' + barcodeImages.barcode2 + '" alt="Mã vạch 2" style="max-height: 80px; margin: 0 auto 0.5rem;">' : '<div style="border: 1px solid #d1d5db; padding: 0.75rem; margin: 0.5rem 0; font-family: monospace; font-size: 0.875rem; background-color: #f9fafb;">' + order.paymentInfo.barcodes.barcode2 + '</div>') + '<p style="font-family: monospace; font-size: 0.75rem; color: #6b7280;">' + order.paymentInfo.barcodes.barcode2 + '</p></div>' : '') + (order.paymentInfo.barcodes.barcode3 ? '<div style="border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 1rem; text-align: center;"><p style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem;">Mã vạch 3</p>' + (barcodeImages.barcode3 ? '<img src="' + barcodeImages.barcode3 + '" alt="Mã vạch 3" style="max-height: 80px; margin: 0 auto 0.5rem;">' : '<div style="border: 1px solid #d1d5db; padding: 0.75rem; margin: 0.5rem 0; font-family: monospace; font-size: 0.875rem; background-color: #f9fafb;">' + order.paymentInfo.barcodes.barcode3 + '</div>') + '<p style="font-family: monospace; font-size: 0.75rem; color: #6b7280;">' + order.paymentInfo.barcodes.barcode3 + '</p></div>' : '') + '</div><div style="margin-top: 1rem; padding: 0.75rem; background-color: #eff6ff; border-radius: 0.5rem; border: 1px solid #bfdbfe;"><p style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.5rem;">Hướng dẫn thanh toán:</p><ol style="font-size: 0.75rem; margin-left: 1rem;"><li>Đến máy iBON tại cửa hàng 7-Eleven</li><li>Chọn "代碼繳費" (Thanh toán bằng mã)</li><li>Quét các mã vạch trên hoặc nhập mã thanh toán</li><li>Xác nhận thông tin và thanh toán tại quầy</li></ol></div></div>' : '') : '') + (order.paymentSubMethod === 'family_mart' && order.paymentInfo.pinCode ? '<div class="detail-grid"><div class="detail-item"><span class="detail-label">Mã FamiPort:</span><span class="detail-value">' + order.paymentInfo.pinCode + '</span></div>' + (order.paymentInfo.paymentNo ? '<div class="detail-item"><span class="detail-label">Số thanh toán:</span><span class="detail-value">' + order.paymentInfo.paymentNo + '</span></div>' : '') + '</div>' : '') + '</div>' : ''}
          
          <div class="footer">
              <p>Đơn hàng được tạo lúc: ${formatDate(order.createdAt)}</p>
              <p>Mã đơn hàng: ${order.id}</p>
            </div>
          
          <div class="no-print" style="text-align: center; margin-top: 2rem;">
            <button onclick="window.print();" style="padding: 0.5rem 1rem; background-color: #2563eb; color: white; border: none; border-radius: 0.375rem; cursor: pointer;">
              In hóa đơn
            </button>
            <button onclick="window.close();" style="padding: 0.5rem 1rem; background-color: #e5e7eb; color: #374151; border: none; border-radius: 0.375rem; margin-left: 0.5rem; cursor: pointer;">
              Đóng
            </button>
          </div>
        </body>
        </html>
      `);
      
      // Close the document
      printWindow.document.close();
    }, 100);
  };

  // Time filter utility functions
  const isToday = (date: Date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  const isYesterday = (date: Date) => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return date.getDate() === yesterday.getDate() &&
      date.getMonth() === yesterday.getMonth() &&
      date.getFullYear() === yesterday.getFullYear();
  };

  const isThisWeek = (date: Date) => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
    startOfWeek.setHours(0, 0, 0, 0);
    return date >= startOfWeek;
  };

  const isLastWeek = (date: Date) => {
    const now = new Date();
    const startOfLastWeek = new Date(now);
    startOfLastWeek.setDate(now.getDate() - now.getDay() - 7); // Start of last week
    startOfLastWeek.setHours(0, 0, 0, 0);
    const endOfLastWeek = new Date(startOfLastWeek);
    endOfLastWeek.setDate(startOfLastWeek.getDate() + 7);
    return date >= startOfLastWeek && date < endOfLastWeek;
  };

  const isThisMonth = (date: Date) => {
    const now = new Date();
    return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
  };

  const isLastMonth = (date: Date) => {
    const now = new Date();
    const lastMonth = new Date(now);
    lastMonth.setMonth(now.getMonth() - 1);
    return date.getMonth() === lastMonth.getMonth() && date.getFullYear() === lastMonth.getFullYear();
  };

  const isThisYear = (date: Date) => {
    return date.getFullYear() === new Date().getFullYear();
  };

  const isLastYear = (date: Date) => {
    return date.getFullYear() === new Date().getFullYear() - 1;
  };

  // Handle customer selection in admin mode
  const handleCustomerChange = (customerId: string) => {
    setSelectedCustomerId(customerId);
    if (onCustomerSelect) {
      onCustomerSelect(customerId);
    }
  };

  // Filtered orders based on selected filters
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      // Apply status filter
      if (statusFilter !== 'all') {
        if (statusFilter === 'not_yet_paid') {
          // Show only orders that are pending, not expired, and not paid
          if (!(order.status === 'pending' && 
                 order.paymentStatus === 'not_paid' && 
                 !order.isExpired)) {
            return false;
          }
        } else if (order.status !== statusFilter) {
          return false;
        }
      }

      // Apply time filter
      if (timeFilter !== 'all_time') {
        const orderDate = new Date(order.createdAt);
        
        switch (timeFilter) {
          case 'today':
            if (!isToday(orderDate)) return false;
            break;
          case 'yesterday':
            if (!isYesterday(orderDate)) return false;
            break;
          case 'this_week':
            if (!isThisWeek(orderDate)) return false;
            break;
          case 'last_week':
            if (!isLastWeek(orderDate)) return false;
            break;
          case 'this_month':
            if (!isThisMonth(orderDate)) return false;
            break;
          case 'last_month':
            if (!isLastMonth(orderDate)) return false;
            break;
          case 'this_year':
            if (!isThisYear(orderDate)) return false;
            break;
          case 'last_year':
            if (!isLastYear(orderDate)) return false;
            break;
        }
      }

      // Apply search term filter (if in admin mode)
      if (searchTerm && searchTerm.trim() !== '') {
        const term = searchTerm.toLowerCase().trim();
        const matchesId = order.id.toLowerCase().includes(term);
        const matchesCustomerName = order.customerName && order.customerName.toLowerCase().includes(term);
        const matchesCustomerPhone = order.customerPhone && order.customerPhone.toLowerCase().includes(term);
        const matchesCustomerEmail = order.customerEmail && order.customerEmail.toLowerCase().includes(term);
        
        if (!(matchesId || matchesCustomerName || matchesCustomerPhone || matchesCustomerEmail)) {
          return false;
        }
      }

      return true;
    });
  }, [orders, statusFilter, timeFilter, searchTerm]);

  // Reset filters
  const resetFilters = () => {
    setStatusFilter('all');
    setTimeFilter('all_time');
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <p className="text-gray-500">Đang tải dữ liệu đơn hàng...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-4 sm:p-6">
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex flex-col sm:flex-row">
            <div className="flex-shrink-0 sm:mr-3 mb-2 sm:mb-0">
              <FontAwesomeIcon icon={faExclamationTriangle} className="h-5 w-5 text-red-400" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-red-800">Có lỗi xảy ra khi tải đơn hàng</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <FontAwesomeIcon icon={faShoppingBag} className="h-12 w-12 text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-1">Bạn chưa có đơn hàng nào</h3>
          <p className="text-gray-500">Khi bạn đặt hàng, các đơn hàng sẽ xuất hiện tại đây</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Admin mode customer selector */}
      {isAdminMode && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 p-4">
          <div className="mb-4">
            <label htmlFor="customerSelect" className="block text-sm font-medium text-gray-700 mb-1">
              Chọn khách hàng
            </label>
            <div className="flex">
              <select
                id="customerSelect"
                value={selectedCustomerId || ''}
                onChange={(e) => handleCustomerChange(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="">Tất cả khách hàng</option>
                {customerList.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} ({customer.phone})
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div>
            <label htmlFor="searchInput" className="block text-sm font-medium text-gray-700 mb-1">
              Tìm kiếm
            </label>
            <input
              type="text"
              id="searchInput"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Tìm theo ID, tên, SĐT, email..."
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
        </div>
      )}

      {/* Filters Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 overflow-hidden">
        <div className="p-4 flex items-center justify-between">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faFilter} className="h-4 w-4 text-gray-500 mr-2" />
            <h3 className="text-sm font-medium text-gray-700">Lọc đơn hàng</h3>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            {showFilters ? 'Ẩn bộ lọc' : 'Hiện bộ lọc'}
            <FontAwesomeIcon icon={showFilters ? faChevronUp : faChevronDown} className="ml-1 h-3 w-3" />
          </button>
        </div>

        {showFilters && (
          <div className="px-4 pb-4 border-t border-gray-100 pt-3 animate-fadeIn">
            <div className="space-y-4">
              {/* Status Filter Buttons */}
              <div>
                <p className="block text-xs font-medium text-gray-700 mb-2">
                  Trạng thái
                </p>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setStatusFilter('all')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      statusFilter === 'all' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Tất cả
                  </button>
                  <button
                    onClick={() => setStatusFilter('not_yet_paid')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      statusFilter === 'not_yet_paid' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Chờ thanh toán
                  </button>
                  <button
                    onClick={() => setStatusFilter('paid')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      statusFilter === 'paid' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Đã thanh toán
                  </button>
                  <button
                    onClick={() => setStatusFilter('pending')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      statusFilter === 'pending' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Đang chờ
                  </button>
                  <button
                    onClick={() => setStatusFilter('expired')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      statusFilter === 'expired' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Hết hạn
                  </button>
                  <button
                    onClick={() => setStatusFilter('cancelled')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      statusFilter === 'cancelled' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Đã hủy
                  </button>
                </div>
              </div>

              {/* Time Filter Buttons */}
              <div>
                <p className="block text-xs font-medium text-gray-700 mb-2">
                  Thời gian
                </p>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setTimeFilter('all_time')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'all_time' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Tất cả
                  </button>
                  <button
                    onClick={() => setTimeFilter('today')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'today' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Hôm nay
                  </button>
                  <button
                    onClick={() => setTimeFilter('yesterday')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'yesterday' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Hôm qua
                  </button>
                  <button
                    onClick={() => setTimeFilter('this_week')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'this_week' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Tuần này
                  </button>
                  <button
                    onClick={() => setTimeFilter('last_week')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'last_week' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Tuần trước
                  </button>
                  <button
                    onClick={() => setTimeFilter('this_month')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'this_month' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Tháng này
                  </button>
                  <button
                    onClick={() => setTimeFilter('last_month')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'last_month' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Tháng trước
                  </button>
                  <button
                    onClick={() => setTimeFilter('this_year')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'this_year' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Năm nay
                  </button>
                  <button
                    onClick={() => setTimeFilter('last_year')}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                      timeFilter === 'last_year' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Năm trước
                  </button>
                </div>
              </div>
            </div>

            {/* Active Filters & Reset */}
            {(statusFilter !== 'all' || timeFilter !== 'all_time') && (
              <div className="mt-4 pt-3 border-t border-gray-100 flex justify-end">
                <button 
                  onClick={resetFilters}
                  className="text-sm text-red-600 hover:text-red-800 flex items-center"
                >
                  <FontAwesomeIcon icon={faTimes} className="mr-1 h-3 w-3" />
                  Xóa tất cả bộ lọc
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Filter results count */}
      {orders.length > 0 && (
        <div className="text-sm text-gray-500 mb-4">
          {filteredOrders.length === 0 
            ? 'Không tìm thấy đơn hàng nào khớp với bộ lọc'
            : filteredOrders.length === orders.length 
              ? `Hiển thị tất cả ${orders.length} đơn hàng`
              : `Hiển thị ${filteredOrders.length} / ${orders.length} đơn hàng`
          }
        </div>
      )}
      
      {filteredOrders.length > 0 && (() => {
        // Categorize orders
        const paidActiveOrders = filteredOrders.filter(order => 
          (order.status === 'paid' || order.status === 'completed') && order.paymentStatus === 'paid'
        );
        
        const ordersNeedingAttention = filteredOrders.filter(order => 
          order.paymentStatus === 'pending' || 
          order.paymentStatus === 'awaiting_payment' || 
          (order.status === 'pending' && order.paymentStatus !== 'paid') ||
          (order.status === 'processing' && order.paymentStatus !== 'paid')
        );
        
        const expiredOrders = filteredOrders.filter(order => 
          order.status === 'expired' || 
          order.status === 'cancelled' || 
          order.paymentStatus === 'failed' ||
          order.paymentStatus === 'expired'
        );

            return (
              <>
                {/* Paid/Active Orders Section */}
                {paidActiveOrders.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 overflow-hidden">
                    <div className="p-4 border-b border-gray-200">
                      <h3 className="text-sm font-medium text-gray-700">Thẻ nạp và dịch vụ đang hoạt động</h3>
                    </div>
                    
                    {(() => {
                      // Extract all items from paid and completed orders
                      const activeItems: Array<{orderId: string; item: OrderItem; orderDate: string; assignedColumnValues?: any[]}> = [];
                      
                      paidActiveOrders.forEach(order => {
                        if (order.items) {
                          order.items.forEach(item => {
                            activeItems.push({
                              orderId: order.id, 
                              item, 
                              orderDate: order.createdAt,
                              assignedColumnValues: order.metadata?.assignedColumnValues
                            });
                          });
                        }
                      });
                      
                      if (activeItems.length === 0) {
                        return (
                          <div className="p-6 text-center">
                            <p className="text-gray-500">Không có thẻ nạp hoặc dịch vụ đang hoạt động.</p>
                          </div>
                        );
                      }
                      
                      return (
                        <div className="p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {activeItems.map((activeItem, index) => (
                              <ActiveItem
                                key={`${activeItem.orderId}-${index}`}
                                orderId={activeItem.orderId}
                                item={activeItem.item}
                                orderDate={activeItem.orderDate}
                                formatDate={formatDate}
                                itemIndex={index}
                                currentUser={currentUser || undefined}
                                assignedColumnValues={activeItem.assignedColumnValues}
                                onItemUpdate={(orderId, itemIndex, updatedItem) => {
                                  setOrders(prevOrders => 
                                    prevOrders.map(order => {
                                      if (order.id === orderId && order.items) {
                                        const updatedItems = [...order.items];
                                        updatedItems[itemIndex] = updatedItem;
                                        return { ...order, items: updatedItems };
                                      }
                                      return order;
                                    })
                                  );
                                  console.log('Updated item:', orderId, itemIndex, updatedItem);
                                }}
                              />
                            ))}
                          </div>
                        </div>
                      );
                    })()}
                    
                    {/* Show paid orders as well */}
                    {paidActiveOrders.length > 0 && (
                      <div className="border-t border-gray-200">
                        <div className="p-4">
                          <div className="space-y-4">
                            {paidActiveOrders.map((order) => (
                              <OrderCard
                                key={order.id}
                                order={order}
                                defaultMode={isAdminMode ? 'admin' : 'customer'}
                                storeParam={storeParam}
                                onPrintOrder={handlePrintOrder}
                                formatDate={formatDate}
                                formatCurrency={formatCurrency}
                                getStatusColor={getStatusColor}
                                getPaymentStatusColor={getPaymentStatusColor}
                                getStatusIcon={getStatusIcon}
                                getStatusText={getStatusText}
                                getPaymentStatusText={getPaymentStatusText}
                                getTimeRemaining={getTimeRemaining}
                                calculateOrderTotal={calculateOrderTotal}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Orders Needing Attention Section */}
                {ordersNeedingAttention.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm border border-orange-200 mb-6 overflow-hidden">
                    <div className="p-4 border-b border-orange-200 bg-orange-50">
                      <h3 className="text-sm font-medium text-orange-800">Đơn hàng cần thanh toán</h3>
                      <p className="text-xs text-orange-600 mt-1">Các đơn hàng đang chờ thanh toán hoặc xử lý</p>
                    </div>
                    <div className="p-4">
                      <div className="space-y-4">
                        {ordersNeedingAttention.map((order) => (
                          <OrderCard
                            key={order.id}
                            order={order}
                            defaultMode={isAdminMode ? 'admin' : 'customer'}
                            storeParam={storeParam}
                            onPrintOrder={handlePrintOrder}
                            formatDate={formatDate}
                            formatCurrency={formatCurrency}
                            getStatusColor={getStatusColor}
                            getPaymentStatusColor={getPaymentStatusColor}
                            getStatusIcon={getStatusIcon}
                            getStatusText={getStatusText}
                            getPaymentStatusText={getPaymentStatusText}
                            getTimeRemaining={getTimeRemaining}
                            calculateOrderTotal={calculateOrderTotal}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Expired Orders Section */}
                {expiredOrders.length > 0 && (
                  <div className="bg-white rounded-lg shadow-sm border border-red-200 mb-6 overflow-hidden">
                    <div className="p-4 border-b border-red-200 bg-red-50">
                      <h3 className="text-sm font-medium text-red-800">Đơn hàng đã hết hạn</h3>
                      <p className="text-xs text-red-600 mt-1">Các đơn hàng đã hết hạn hoặc bị hủy</p>
                    </div>
                    <div className="p-4">
                      <div className="space-y-4">
                        {expiredOrders.map((order) => (
                          <OrderCard
                            key={order.id}
                            order={order}
                            defaultMode={isAdminMode ? 'admin' : 'customer'}
                            storeParam={storeParam}
                            onPrintOrder={handlePrintOrder}
                            formatDate={formatDate}
                            formatCurrency={formatCurrency}
                            getStatusColor={getStatusColor}
                            getPaymentStatusColor={getPaymentStatusColor}
                            getStatusIcon={getStatusIcon}
                            getStatusText={getStatusText}
                            getPaymentStatusText={getPaymentStatusText}
                            getTimeRemaining={getTimeRemaining}
                            calculateOrderTotal={calculateOrderTotal}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* No orders found */}
                {filteredOrders.length === 0 && orders.length > 0 && (
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="text-center py-8">
                      <FontAwesomeIcon icon={faFilter} className="h-12 w-12 text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-1">Không tìm thấy đơn hàng nào</h3>
                      <p className="text-gray-500 mb-4">Không có đơn hàng nào khớp với bộ lọc đã chọn</p>
                      <button 
                        onClick={resetFilters}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
                      >
                        Xóa bộ lọc
                      </button>
                    </div>
                  </div>
                )}

                {/* Empty state when no orders at all */}
                {filteredOrders.length === 0 && orders.length === 0 && (
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="text-center py-8">
                      <h3 className="text-lg font-medium text-gray-900 mb-1">Chưa có đơn hàng nào</h3>
                      <p className="text-gray-500 mb-4">Bạn chưa có đơn hàng nào. Hãy bắt đầu mua sắm!</p>
                    </div>
                  </div>
                )}
              </>
            );
          })()}
      
      {/* Style for animation */}
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

export default Orders;