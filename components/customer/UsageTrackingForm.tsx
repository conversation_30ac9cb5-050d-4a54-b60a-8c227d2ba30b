import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSave, faEdit, faCheck, faTimes, faSpinner } from '@fortawesome/free-solid-svg-icons';

interface UsageTrackingFormProps {
  orderId: string;
  itemIndex: number;
  itemType: 'sim' | 'topup';
  itemName: string;
  initialUsedFor?: string;
  initialUsageNotes?: string;
  usageDate?: string;
  usageUpdatedAt?: string;
  customerEmail?: string;
  customerPhone?: string;
  onUsageUpdate?: (usageInfo: any) => void;
  readOnly?: boolean;
}

const UsageTrackingForm: React.FC<UsageTrackingFormProps> = ({
  orderId,
  itemIndex,
  itemType,
  itemName,
  initialUsedFor = '',
  initialUsageNotes = '',
  usageDate,
  usageUpdatedAt,
  customerEmail,
  customerPhone,
  onUsageUpdate,
  readOnly = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [usedFor, setUsedFor] = useState(initialUsedFor);
  const [usageNotes, setUsageNotes] = useState(initialUsageNotes);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Update local state when props change
  useEffect(() => {
    setUsedFor(initialUsedFor);
    setUsageNotes(initialUsageNotes);
  }, [initialUsedFor, initialUsageNotes]);

  const handleSave = async () => {
    if (!customerEmail && !customerPhone) {
      setError('Customer information is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/customer/order-usage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          itemIndex,
          usedFor: usedFor.trim(),
          usageNotes: usageNotes.trim(),
          customerEmail,
          customerPhone
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to save usage information');
      }

      setSuccess(true);
      setIsEditing(false);
      
      // Call callback if provided
      if (onUsageUpdate) {
        onUsageUpdate(data.data.usageInfo);
      }

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setUsedFor(initialUsedFor);
    setUsageNotes(initialUsageNotes);
    setIsEditing(false);
    setError(null);
  };

  const getPlaceholderText = () => {
    if (itemType === 'sim') {
      return 'e.g., Used for phone number +886-912-345-678';
    } else if (itemType === 'topup') {
      return 'e.g., Topped up phone number +886-912-345-678';
    }
    return 'Describe how you used this item';
  };

  const hasUsageInfo = initialUsedFor || initialUsageNotes;

  return (
    <div className="usage-tracking-form bg-gray-50 p-4 rounded-lg border border-gray-200 mt-3">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-semibold text-gray-700 flex items-center">
          <FontAwesomeIcon icon={faEdit} className="mr-2 text-blue-500" />
          Usage Tracking - {itemName}
        </h4>
        {!readOnly && !isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="text-blue-500 hover:text-blue-700 text-sm font-medium"
          >
            {hasUsageInfo ? 'Edit' : 'Add Usage Info'}
          </button>
        )}
      </div>

      {/* Display Mode */}
      {!isEditing && (
        <div className="space-y-2">
          {hasUsageInfo ? (
            <>
              {initialUsedFor && (
                <div>
                  <span className="text-xs font-medium text-gray-600">Used For:</span>
                  <p className="text-sm text-gray-800 mt-1">{initialUsedFor}</p>
                </div>
              )}
              {initialUsageNotes && (
                <div>
                  <span className="text-xs font-medium text-gray-600">Notes:</span>
                  <p className="text-sm text-gray-800 mt-1">{initialUsageNotes}</p>
                </div>
              )}
              {usageDate && (
                <div className="text-xs text-gray-500">
                  First used: {new Date(usageDate).toLocaleDateString()}
                </div>
              )}
              {usageUpdatedAt && (
                <div className="text-xs text-gray-500">
                  Last updated: {new Date(usageUpdatedAt).toLocaleDateString()}
                </div>
              )}
            </>
          ) : (
            <p className="text-sm text-gray-500 italic">
              No usage information recorded yet. Click "Add Usage Info" to track how you used this {itemType}.
            </p>
          )}
        </div>
      )}

      {/* Edit Mode */}
      {isEditing && (
        <div className="space-y-3">
          <div>
            <label className="block text-xs font-medium text-gray-600 mb-1">
              Used For *
            </label>
            <input
              type="text"
              value={usedFor}
              onChange={(e) => setUsedFor(e.target.value)}
              placeholder={getPlaceholderText()}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-600 mb-1">
              Additional Notes
            </label>
            <textarea
              value={usageNotes}
              onChange={(e) => setUsageNotes(e.target.value)}
              placeholder="Any additional notes about usage..."
              rows={2}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                disabled={isLoading || !usedFor.trim()}
                className="flex items-center px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-1" />
                ) : (
                  <FontAwesomeIcon icon={faSave} className="mr-1" />
                )}
                Save
              </button>
              <button
                onClick={handleCancel}
                disabled={isLoading}
                className="flex items-center px-3 py-1.5 bg-gray-500 text-white text-sm rounded-md hover:bg-gray-600 disabled:bg-gray-400"
              >
                <FontAwesomeIcon icon={faTimes} className="mr-1" />
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="mt-3 p-2 bg-green-100 border border-green-300 rounded-md">
          <div className="flex items-center text-green-700 text-sm">
            <FontAwesomeIcon icon={faCheck} className="mr-2" />
            Usage information saved successfully!
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mt-3 p-2 bg-red-100 border border-red-300 rounded-md">
          <div className="flex items-center text-red-700 text-sm">
            <FontAwesomeIcon icon={faTimes} className="mr-2" />
            {error}
          </div>
        </div>
      )}
    </div>
  );
};

export default UsageTrackingForm;