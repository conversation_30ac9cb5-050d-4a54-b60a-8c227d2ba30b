import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSimCard, faToggleOn, faToggleOff } from '@fortawesome/free-solid-svg-icons';

interface SimSetting {
  isEnabled: boolean;
  value: string | number;
}

interface Sim {
  id: string;
  phoneNumber: string;
  settings: {
    roaming: SimSetting;
    dataRoaming: SimSetting;
    internationalCalls: SimSetting;
    voicemail: SimSetting;
  };
}

type SimSettingCategory = 'roaming' | 'dataRoaming' | 'internationalCalls' | 'voicemail';

const SimSettings: React.FC = () => {
  const [sims, setSims] = useState<Sim[]>([
    {
      id: '1',
      phoneNumber: '+1234567890',
      settings: {
        roaming: {
          isEnabled: false,
          value: 'disabled',
        },
        dataRoaming: {
          isEnabled: false,
          value: 'disabled',
        },
        internationalCalls: {
          isEnabled: true,
          value: 'enabled',
        },
        voicemail: {
          isEnabled: true,
          value: 'enabled',
        },
      },
    },
    {
      id: '2',
      phoneNumber: '+9876543210',
      settings: {
        roaming: {
          isEnabled: true,
          value: 'enabled',
        },
        dataRoaming: {
          isEnabled: true,
          value: 'enabled',
        },
        internationalCalls: {
          isEnabled: false,
          value: 'disabled',
        },
        voicemail: {
          isEnabled: false,
          value: 'disabled',
        },
      },
    },
  ]);

  const handleToggle = (simId: string, category: SimSettingCategory, option: keyof SimSetting, value: boolean | string) => {
    setSims((prevSims) =>
      prevSims.map((sim) => {
        if (sim.id === simId) {
          return {
            ...sim,
            settings: {
              ...sim.settings,
              [category]: {
                ...sim.settings[category],
                [option]: value,
              },
            },
          };
        }
        return sim;
      })
    );
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">SIM Settings</h2>
        <div className="space-y-6">
          {sims.map((sim) => (
            <div key={sim.id} className="border rounded-lg p-4">
              <div className="flex items-center mb-4">
                <FontAwesomeIcon icon={faSimCard} className="text-indigo-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">{sim.phoneNumber}</h3>
              </div>
              <div className="space-y-4">
                {Object.entries(sim.settings).map(([category, setting]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700 capitalize">
                      {category.replace(/([A-Z])/g, ' $1').trim()}
                    </h4>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => handleToggle(sim.id, category as SimSettingCategory, 'isEnabled', !setting.isEnabled)}
                          className="text-indigo-600 hover:text-indigo-800"
                        >
                          <FontAwesomeIcon
                            icon={setting.isEnabled ? faToggleOn : faToggleOff}
                            className="text-2xl"
                          />
                        </button>
                        <span className="text-sm text-gray-500">
                          {setting.isEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                      {setting.isEnabled && (
                        <div className="flex items-center space-x-4">
                          <div>
                            <label className="text-xs text-gray-500">Status</label>
                            <span className="ml-2 text-sm text-gray-900 capitalize">{setting.value}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SimSettings; 