import React from 'react';
import Orders from './Orders';

interface OrdersSectionProps {
  isActive: boolean;
  toggleSection: () => void;
}

const OrdersSection: React.FC<OrdersSectionProps> = ({ isActive, toggleSection }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div 
        onClick={toggleSection}
        className="p-4 border-b border-gray-100 flex justify-between items-center cursor-pointer hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
            <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
          </svg>
          <h2 className="text-xl font-medium text-gray-800">Đơn hàng của tôi</h2>
        </div>
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isActive ? 'transform rotate-180' : ''}`} 
          viewBox="0 0 20 20" 
          fill="currentColor"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </div>
      <div 
        className={`transition-all duration-300 ease-in-out overflow-hidden ${
          isActive ? 'max-h-full opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="p-1 sm:p-4 md:p-6">
          <Orders />
        </div>
      </div>
    </div>
  );
};

export default OrdersSection; 