import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkerAlt, faHome, faBuilding, faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';

interface Address {
  id: string;
  type: 'billing' | 'shipping';
  street: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  isDefault: boolean;
}

const validationSchema = Yup.object({
  street: Yup.string().required('Vui lòng nhập địa chỉ đường'),
  city: Yup.string().required('Vui lòng nhập thành phố'),
  state: Yup.string().required('Vui lòng nhập tỉnh/thành'),
  country: Yup.string().required('Vui lòng nhập quốc gia'),
  zipCode: Yup.string().required('<PERSON>ui lòng nhập mã bưu điện'),
});

const AddressBook: React.FC = () => {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [showForm, setShowForm] = useState(false);

  const formik = useFormik({
    initialValues: {
      type: 'shipping' as 'billing' | 'shipping',
      street: '',
      city: '',
      state: '',
      country: '',
      zipCode: '',
      isDefault: false,
    },
    validationSchema,
    onSubmit: (values) => {
      const newAddress: Address = {
        id: Math.random().toString(36).substr(2, 9),
        ...values,
      };
      setAddresses([...addresses, newAddress]);
      setShowForm(false);
      formik.resetForm();
    },
  });

  const handleDeleteAddress = (id: string) => {
    setAddresses(addresses.filter(addr => addr.id !== id));
  };

  const handleSetDefault = (id: string, type: 'billing' | 'shipping') => {
    setAddresses(addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === id && addr.type === type,
    })));
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Sổ Địa Chỉ</h2>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Thêm Địa Chỉ Mới
        </button>
      </div>

      {showForm && (
        <form onSubmit={formik.handleSubmit} className="mb-6 p-4 border rounded-md">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Loại Địa Chỉ</label>
              <select
                name="type"
                onChange={formik.handleChange}
                value={formik.values.type}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="shipping">Địa Chỉ Giao Hàng</option>
                <option value="billing">Địa Chỉ Thanh Toán</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Địa Chỉ Đường</label>
              <input
                type="text"
                name="street"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.street}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {formik.touched.street && formik.errors.street && (
                <div className="text-red-500 text-sm mt-1">{formik.errors.street}</div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Thành Phố</label>
              <input
                type="text"
                name="city"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.city}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {formik.touched.city && formik.errors.city && (
                <div className="text-red-500 text-sm mt-1">{formik.errors.city}</div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Tỉnh/Thành</label>
              <input
                type="text"
                name="state"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.state}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {formik.touched.state && formik.errors.state && (
                <div className="text-red-500 text-sm mt-1">{formik.errors.state}</div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Quốc Gia</label>
              <input
                type="text"
                name="country"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.country}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {formik.touched.country && formik.errors.country && (
                <div className="text-red-500 text-sm mt-1">{formik.errors.country}</div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Mã Bưu Điện</label>
              <input
                type="text"
                name="zipCode"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.zipCode}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              {formik.touched.zipCode && formik.errors.zipCode && (
                <div className="text-red-500 text-sm mt-1">{formik.errors.zipCode}</div>
              )}
            </div>
          </div>

          <div className="mt-4 flex items-center">
            <input
              type="checkbox"
              name="isDefault"
              onChange={formik.handleChange}
              checked={formik.values.isDefault}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">
              Đặt làm địa chỉ {formik.values.type === 'shipping' ? 'giao hàng' : 'thanh toán'} mặc định
            </label>
          </div>

          <div className="mt-4 flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => setShowForm(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Hủy
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              Lưu Địa Chỉ
            </button>
          </div>
        </form>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {addresses.length === 0 ? (
          <div className="col-span-2 text-center py-8 text-gray-500">
            Bạn chưa có địa chỉ nào. Vui lòng thêm địa chỉ mới.
          </div>
        ) : (
          addresses.map((address) => (
            <div key={address.id} className="p-4 border rounded-md">
              <div className="flex justify-between items-start">
                <div className="flex items-center">
                  <FontAwesomeIcon 
                    icon={address.type === 'shipping' ? faHome : faBuilding} 
                    className="text-indigo-600 mr-2" 
                  />
                  <div>
                    <h3 className="font-medium">
                      {address.type === 'shipping' ? 'Địa Chỉ Giao Hàng' : 'Địa Chỉ Thanh Toán'}
                      {address.isDefault && <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">Mặc Định</span>}
                    </h3>
                  </div>
                </div>
                <button
                  onClick={() => handleDeleteAddress(address.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <FontAwesomeIcon icon={faTrash} />
                </button>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                <p>{address.street}</p>
                <p>{address.city}, {address.state}</p>
                <p>{address.country}, {address.zipCode}</p>
              </div>
              {!address.isDefault && (
                <button
                  onClick={() => handleSetDefault(address.id, address.type)}
                  className="mt-2 text-sm text-indigo-600 hover:text-indigo-800"
                >
                  Đặt làm mặc định
                </button>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default AddressBook; 