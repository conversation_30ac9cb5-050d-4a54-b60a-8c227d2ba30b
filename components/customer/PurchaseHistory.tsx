import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface PurchaseItem {
  id: string;
  date: string;
  amount: number;
  status: string;
  products?: string[];
}

interface PurchaseHistoryProps {
  customerId?: string | null;
}

const PurchaseHistory: React.FC<PurchaseHistoryProps> = ({ customerId }) => {
  const [purchases, setPurchases] = useState<PurchaseItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPurchaseHistory = async () => {
      // If no customer ID is provided, try to get it from localStorage
      let id = customerId;
      if (!id) {
        const customerData = localStorage.getItem('customerData');
        if (customerData) {
          try {
            const customer = JSON.parse(customerData);
            id = customer.id;
          } catch (err) {
            console.error('Lỗi khi đọc dữ liệu khách hàng:', err);
            setError('Không thể đọc thông tin khách hàng. Vui lòng đăng nhập lại.');
          }
        }
      }

      if (!id) {
        setError('Không tìm thấy ID khách hàng. Vui lòng đăng nhập lại.');
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch customer data to get purchase history
        const response = await axios.get(`/api/customers/${id}`);
        const customerData = response.data;
        
        if (customerData.purchaseHistory && Array.isArray(customerData.purchaseHistory)) {
          setPurchases(customerData.purchaseHistory);
        } else {
          setPurchases([]);
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('Lỗi khi lấy lịch sử mua hàng:', err);
        setError('Không thể kết nối đến máy chủ. Vui lòng thử lại sau.');
        setIsLoading(false);
      }
    };

    fetchPurchaseHistory();
  }, [customerId]);

  // Format date to Vietnamese format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  // Format currency to Vietnamese format
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Map status to Vietnamese
  const getStatusInVietnamese = (status: string) => {
    const statusMap: Record<string, string> = {
      'processing': 'Đang xử lý',
      'shipped': 'Đang giao hàng',
      'delivered': 'Đã giao hàng',
      'completed': 'Hoàn thành',
      'cancelled': 'Đã hủy',
      'refunded': 'Đã hoàn tiền'
    };
    
    return statusMap[status.toLowerCase()] || status;
  };

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <span className="ml-3">Đang tải...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="p-4 bg-red-50 border-l-4 border-red-500 text-red-700">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (purchases.length === 0) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="p-4 bg-gray-50 rounded-lg text-center">
          <p className="text-gray-500">Bạn chưa có đơn hàng nào.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      
      <div className="space-y-4">
        {purchases.map((purchase) => (
          <div key={purchase.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <p className="text-sm text-gray-500">Đơn hàng #{purchase.id}</p>
                <p className="text-sm text-gray-500">{formatDate(purchase.date)}</p>
              </div>
              <div className="text-right">
                <p className="font-semibold">{formatCurrency(purchase.amount)}</p>
                <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                  purchase.status === 'completed' ? 'bg-green-100 text-green-800' :
                  purchase.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                  purchase.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                  purchase.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {getStatusInVietnamese(purchase.status)}
                </span>
              </div>
            </div>
            
            {purchase.products && purchase.products.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <p className="text-sm font-medium mb-1">Sản phẩm:</p>
                <ul className="text-sm text-gray-600">
                  {purchase.products.map((product, index) => (
                    <li key={index} className="py-1">• {product}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PurchaseHistory; 