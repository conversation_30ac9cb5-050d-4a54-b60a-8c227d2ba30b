import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMobileAlt, faChartLine, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';

interface DataUsage {
  total: number;
  used: number;
  remaining: number;
  lastUpdated: string;
}

interface SimData {
  id: string;
  phoneNumber: string;
  dataUsage: DataUsage;
  plan: {
    name: string;
    limit: number;
    validity: string;
  };
}

const DataUsage: React.FC = () => {
  // Mock data
  const sims: SimData[] = [
    {
      id: '1',
      phoneNumber: '+1234567890',
      dataUsage: {
        total: 10,
        used: 3.5,
        remaining: 6.5,
        lastUpdated: '2024-03-20 15:30',
      },
      plan: {
        name: 'Premium 10GB',
        limit: 10,
        validity: '30 days',
      },
    },
    {
      id: '2',
      phoneNumber: '+9876543210',
      dataUsage: {
        total: 5,
        used: 4.2,
        remaining: 0.8,
        lastUpdated: '2024-03-20 15:30',
      },
      plan: {
        name: 'Basic 5GB',
        limit: 5,
        validity: '30 days',
      },
    },
  ];

  const getUsagePercentage = (used: number, total: number) => {
    return (used / total) * 100;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Data Usage</h2>
        <div className="space-y-6">
          {sims.map((sim) => (
            <div key={sim.id} className="border rounded-lg p-4">
              <div className="flex items-center mb-4">
                <FontAwesomeIcon icon={faMobileAlt} className="text-indigo-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">{sim.phoneNumber}</h3>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Data Usage</span>
                    <span>
                      {sim.dataUsage.used}GB / {sim.dataUsage.total}GB
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className={`${getUsageColor(
                        getUsagePercentage(sim.dataUsage.used, sim.dataUsage.total)
                      )} h-2.5 rounded-full`}
                      style={{
                        width: `${getUsagePercentage(sim.dataUsage.used, sim.dataUsage.total)}%`,
                      }}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Remaining</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {sim.dataUsage.remaining}GB
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Plan</p>
                    <p className="text-lg font-semibold text-gray-900">{sim.plan.name}</p>
                  </div>
                </div>

                {getUsagePercentage(sim.dataUsage.used, sim.dataUsage.total) >= 70 && (
                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-400" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">
                          You are running low on data. Consider upgrading your plan or purchasing additional data.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="text-sm text-gray-500">
                  Last updated: {sim.dataUsage.lastUpdated}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DataUsage; 