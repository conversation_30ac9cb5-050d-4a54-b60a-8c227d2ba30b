import React, { useState } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faShoppingBag, 
  faChevronDown, 
  faExternalLinkAlt, 
  faFileInvoice, 
  faTimes, 
  faUser, 
  faPhone, 
  faEnvelope, 
  faMapMarkerAlt, 
  faCreditCard, 
  faMoneyBill, 
  faCalendarAlt, 
  faClock, 
  faInfoCircle,
  faGift,
  faStickyNote
} from '@fortawesome/free-solid-svg-icons';
import JsBarcode from 'jsbarcode';
import BarcodeDisplay from '../taiwan/payment/methods/711/BarcodeDisplay';
import FamilyMartBarcodeDisplay from '../taiwan/payment/methods/FamilyMart/FamilyMartBarcodeDisplay';
import { getProductImage, handleImageError } from '../../utils/imageUtils';
import UsageTrackingForm from './UsageTrackingForm';
import OrderNotesManager from './OrderNotesManager';
import GoogleSheetsDetails from './GoogleSheetsDetails';

// Order interfaces
interface OrderItem {
  name: string;
  price: number | string;
  quantity: number;
  image?: string;
  sku?: string;
  type?: 'sim' | 'topup' | 'device' | 'service' | 'other';
  // SIM specific fields
  simNumber?: string;
  phoneNumber?: string;
  iccid?: string;
  networkProvider?: string;
  simStatus?: string;
  // Topup card specific fields
  cardNumber?: string;
  cardPin?: string;
  cardSerial?: string;
  topupCode?: string;
  expiryDate?: string;
  // Device specific fields
  imei?: string;
  model?: string;
  color?: string;
  // Service specific fields
  servicePeriod?: string;
  serviceStartDate?: string;
  serviceEndDate?: string;
  // Usage tracking fields
  usedFor?: string; // Description of how the item was used (e.g., phone number topped up, person given to)
  usageNotes?: string; // Additional notes about usage
  usageDate?: string; // When the item was used
  usageUpdatedAt?: string; // When usage info was last updated
}

// APN Callback interfaces
interface PaymentCallback {
  timestamp: string;
  paymentMethod: '7-eleven' | 'familymart' | 'sinopac' | '711-card';
  callbackType: 'apn' | 'payment_notification' | 'status_update';
  requestPayload: any;
  responseStatus: number;
  responseBody: string;
  responseHeaders?: any;
  processingTimeMs: number;
  clientIP: string;
  userAgent: string;
  orderFound: boolean;
}

interface OrderNote {
  id: string;
  text: string;
  createdAt: string;
  updatedAt: string;
}

interface Order {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  items: OrderItem[];
  totalAmount: number;
  amount?: number;
  currency: string;
  status: string;
  paymentStatus: string;
  paymentMethod: string;
  paymentSubMethod?: string;
  createdAt: string;
  updatedAt: string;
  validUntil: string;
  isExpired: boolean;
  storeId?: string;
  paymentInfo?: any;
  shippingAddress?: string;
  fee: number;
  // APN Callback data
  paymentCallbacks?: PaymentCallback[];
  trans_id?: string;
  // Loyalty points data
  loyaltyPoints?: {
    awarded: number;
    breakdown: {
      basePoints: number;
      tierBonus: number;
      specialEventBonus: number;
    };
    awardedAt: string;
  };
  // Customer notes
  notes?: OrderNote[];
  // Google Sheets metadata
  metadata?: {
    type: string;
    sheetsId?: string;
    assignedColumns?: any;
    assignedColumnValues?: {
      columnIndex: number;
      columnLetter: string;
      value: string;
    }[];
    assignedSku?: string;
  };
}

interface OrderCardProps {
  order: Order;
  defaultMode?: 'customer' | 'admin';
  storeParam?: string | string[];
  onPrintOrder: (order: Order) => void;
  formatDate: (dateString: string) => string;
  formatCurrency: (amount: number, currency?: string) => string;
  getStatusColor: (status: string) => string;
  getPaymentStatusColor: (status: string) => string;
  getStatusIcon: (status: string) => any;
  getStatusText: (status: string) => string;
  getPaymentStatusText: (status: string) => string;
  getTimeRemaining: (validUntil: string) => string;
  calculateOrderTotal: (order: Order) => number;
}

const OrderCard: React.FC<OrderCardProps> = ({
  order,
  defaultMode = 'customer',
  storeParam,
  onPrintOrder,
  formatDate,
  formatCurrency,
  getStatusColor,
  getPaymentStatusColor,
  getStatusIcon,
  getStatusText,
  getPaymentStatusText,
  getTimeRemaining,
  calculateOrderTotal
}) => {
  const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'customer' | 'admin'>(defaultMode);

  const toggleOrderDetails = (orderId: string) => {
    setExpandedOrderId(expandedOrderId === orderId ? null : orderId);
  };

  // Generate barcode as base64 image for printing
  const generateBarcodeImage = (code: string): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      try {
        JsBarcode(canvas, code, {
          format: "CODE128",
          lineColor: "#000",
          width: 2,
          height: 60,
          displayValue: true,
          margin: 10,
          fontSize: 12,
          textAlign: "center",
          textPosition: "bottom",
          background: "#ffffff"
        });
        resolve(canvas.toDataURL('image/png'));
      } catch (error) {
        console.error('Error generating barcode image:', error);
        resolve('');
      }
    });
  };

  // APN Callback Status Functions
  const getCallbackStatusIcon = (callback: PaymentCallback) => {
    if (callback.responseStatus >= 200 && callback.responseStatus < 300) {
      return <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4 text-green-500" />;
    } else if (callback.responseStatus >= 400) {
      return <FontAwesomeIcon icon={faTimes} className="h-4 w-4 text-red-500" />;
    } else {
      return <FontAwesomeIcon icon={faClock} className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getCallbackStatusText = (callback: PaymentCallback) => {
    if (callback.responseStatus >= 200 && callback.responseStatus < 300) {
      return 'Thành công';
    } else if (callback.responseStatus >= 400) {
      return 'Thất bại';
    } else {
      return 'Đang xử lý';
    }
  };

  const getPaymentChannelName = (paymentMethod: string, paymentSubMethod?: string) => {
    switch (paymentMethod) {
      case '7-eleven':
        return '7-Eleven iBON';
      case 'familymart':
        return 'FamilyMart';
      case 'sinopac':
        return 'SinoPac Bank';
      case '711-card':
        return '7-Eleven Card';
      case 'convenience_store':
        if (paymentSubMethod === 'seven_eleven_ibon') return '7-Eleven iBON';
        if (paymentSubMethod === 'family_mart') return 'FamilyMart';
        return 'Cửa hàng tiện lợi';
      case 'card':
        return 'Thẻ tín dụng/ghi nợ';
      default:
        return paymentMethod || 'Không xác định';
    }
  };

  const getAPNStatusFromCallback = (callback: PaymentCallback) => {
    // Parse APN status based on payment method
    if (callback.paymentMethod === '7-eleven') {
      const status = callback.requestPayload?.status;
      switch (status) {
        case 'A': return { text: 'Chờ thanh toán', color: 'text-yellow-600' };
        case 'B': return { text: 'Đã thanh toán', color: 'text-green-600' };
        case 'D': return { text: 'Hết hạn', color: 'text-red-600' };
        case 'C': return { text: 'Đã hủy', color: 'text-red-600' };
        default: return { text: status || 'Không xác định', color: 'text-gray-600' };
      }
    } else if (callback.paymentMethod === 'familymart') {
      const statusCode = callback.requestPayload?.STATUS_CODE;
      switch (statusCode) {
        case '0': return { text: 'Hoàn thành', color: 'text-green-600' };
        case '1': return { text: 'Chờ thanh toán', color: 'text-yellow-600' };
        case '2': return { text: 'Hết hạn', color: 'text-red-600' };
        case '3': return { text: 'Đã hủy', color: 'text-red-600' };
        default: return { text: statusCode || 'Không xác định', color: 'text-gray-600' };
      }
    } else if (callback.paymentMethod === 'sinopac') {
      const status = callback.requestPayload?.Status;
      switch (status) {
        case 'S': return { text: 'Thành công', color: 'text-green-600' };
        case 'F': return { text: 'Thất bại', color: 'text-red-600' };
        default: return { text: status || 'Không xác định', color: 'text-gray-600' };
      }
    }
    return { text: 'Không xác định', color: 'text-gray-600' };
  };

  // Render APN Callback Status Section
  const renderAPNCallbackStatus = () => {
    if (!order.paymentCallbacks || order.paymentCallbacks.length === 0) {
      return (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200 mb-3">
            Trạng thái callback từ nhà cung cấp thanh toán
          </h4>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center text-sm text-gray-500">
              <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4 text-gray-400 mr-2" />
              <span>Chưa nhận được callback từ {getPaymentChannelName(order.paymentMethod, order.paymentSubMethod)}</span>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              Server sẽ nhận callback tự động khi có thay đổi trạng thái thanh toán
            </p>
          </div>
        </div>
      );
    }

    // Sort callbacks by timestamp (newest first)
    const sortedCallbacks = [...order.paymentCallbacks].sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    return (
      <div className="mt-6">
        <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200 mb-3">
          Lịch sử callback từ nhà cung cấp thanh toán
        </h4>
        
        {/* Summary */}
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-blue-800">
                Đã nhận {sortedCallbacks.length} callback từ {getPaymentChannelName(order.paymentMethod, order.paymentSubMethod)}
              </span>
            </div>
            <div className="text-xs text-blue-600">
              Callback cuối: {formatDate(sortedCallbacks[0].timestamp)}
            </div>
          </div>
        </div>

        {/* Callback List */}
        <div className="space-y-3">
          {sortedCallbacks.map((callback, index) => {
            const apnStatus = getAPNStatusFromCallback(callback);
            return (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getCallbackStatusIcon(callback)}
                    <span className="text-sm font-medium text-gray-800">
                      {getCallbackStatusText(callback)}
                    </span>
                    <span className={`text-sm font-medium ${apnStatus.color}`}>
                      ({apnStatus.text})
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatDate(callback.timestamp)}
                  </div>
                </div>

                {/* Callback Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                  <div>
                    <span className="font-medium text-gray-600">Phương thức:</span>
                    <span className="ml-1 text-gray-800">
                      {getPaymentChannelName(callback.paymentMethod)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Mã phản hồi:</span>
                    <span className={`ml-1 ${callback.responseStatus >= 200 && callback.responseStatus < 300 ? 'text-green-600' : 'text-red-600'}`}>
                      {callback.responseStatus}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Thời gian xử lý:</span>
                    <span className="ml-1 text-gray-800">{callback.processingTimeMs}ms</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">IP gửi:</span>
                    <span className="ml-1 text-gray-800">{callback.clientIP}</span>
                  </div>
                </div>

                {/* Payment-specific details */}
                {callback.paymentMethod === '7-eleven' && callback.requestPayload && (
                  <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                    <div className="font-medium text-gray-600 mb-1">Chi tiết APN 7-Eleven:</div>
                    <div className="grid grid-cols-2 gap-2">
                      {callback.requestPayload.trans_id && (
                        <div><span className="font-medium">Trans ID:</span> {callback.requestPayload.trans_id}</div>
                      )}
                      {callback.requestPayload.payment_code && (
                        <div><span className="font-medium">Loại thanh toán:</span> {callback.requestPayload.payment_code === '1' ? 'Thẻ tín dụng' : 'CVS'}</div>
                      )}
                      {callback.requestPayload.amount && (
                        <div><span className="font-medium">Số tiền:</span> {callback.requestPayload.amount}</div>
                      )}
                      {callback.requestPayload.nonce && (
                        <div><span className="font-medium">Nonce:</span> {callback.requestPayload.nonce}</div>
                      )}
                    </div>
                  </div>
                )}

                {callback.paymentMethod === 'familymart' && callback.requestPayload && (
                  <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                    <div className="font-medium text-gray-600 mb-1">Chi tiết FamilyMart:</div>
                    <div className="grid grid-cols-2 gap-2">
                      {callback.requestPayload.PIN_CODE && (
                        <div><span className="font-medium">PIN Code:</span> {callback.requestPayload.PIN_CODE}</div>
                      )}
                      {callback.requestPayload.PAYMENT_NO && (
                        <div><span className="font-medium">Payment No:</span> {callback.requestPayload.PAYMENT_NO}</div>
                      )}
                      {callback.requestPayload.STORE_ID && (
                        <div><span className="font-medium">Store ID:</span> {callback.requestPayload.STORE_ID}</div>
                      )}
                      {callback.requestPayload.PAYMENT_DATE && (
                        <div><span className="font-medium">Ngày thanh toán:</span> {callback.requestPayload.PAYMENT_DATE}</div>
                      )}
                    </div>
                  </div>
                )}

                {callback.paymentMethod === 'sinopac' && callback.requestPayload && (
                  <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                    <div className="font-medium text-gray-600 mb-1">Chi tiết SinoPac:</div>
                    <div className="grid grid-cols-2 gap-2">
                      {callback.requestPayload.PayToken && (
                        <div><span className="font-medium">Pay Token:</span> {callback.requestPayload.PayToken}</div>
                      )}
                      {callback.requestPayload.TransactionNo && (
                        <div><span className="font-medium">Transaction No:</span> {callback.requestPayload.TransactionNo}</div>
                      )}
                      {callback.requestPayload.Amount && (
                        <div><span className="font-medium">Số tiền:</span> {callback.requestPayload.Amount}</div>
                      )}
                      {callback.requestPayload.ErrorCode && (
                        <div><span className="font-medium">Error Code:</span> {callback.requestPayload.ErrorCode}</div>
                      )}
                    </div>
                  </div>
                )}

                {/* Server Response */}
                <div className="mt-2">
                  <details className="text-xs">
                    <summary className="cursor-pointer font-medium text-gray-600 hover:text-gray-800">
                      Xem phản hồi server ({callback.responseBody?.length || 0} ký tự)
                    </summary>
                    <div className="mt-1 p-2 bg-gray-100 rounded font-mono text-xs max-h-20 overflow-y-auto">
                      {callback.responseBody || 'Không có dữ liệu'}
                    </div>
                  </details>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer Info */}
        <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
          <div className="flex items-start">
            <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4 text-yellow-500 mt-0.5 mr-2" />
            <div className="text-xs text-yellow-800">
              <p className="font-medium mb-1">Thông tin callback:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>Callback được gửi tự động từ nhà cung cấp thanh toán khi có thay đổi trạng thái</li>
                <li>Mỗi callback được xác thực bằng checksum hoặc token bảo mật</li>
                <li>Thời gian xử lý thể hiện hiệu suất phản hồi của server</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div 
      key={order.id} 
      id={`order-${order.id}`}
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
    >
      {/* Order Header - Mobile-friendly layout */}
      <div className="p-3 sm:p-4">
        <div className="flex flex-col space-y-3">
          {/* Mode Toggle */}
          <div className="flex justify-end">
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('customer')}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  viewMode === 'customer'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Customer
              </button>
              <button
                onClick={() => setViewMode('admin')}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  viewMode === 'admin'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Admin
              </button>
            </div>
          </div>
          
          {/* Order Info */}
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faShoppingBag} className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500 mr-2 sm:mr-3 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-800 truncate">Đơn hàng #{order.id}</p>
                <p className="text-xs text-gray-500">{formatDate(order.createdAt)}</p>
              </div>
            </div>
            
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              <div className="flex flex-wrap gap-1 sm:gap-2">
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                  <FontAwesomeIcon icon={getStatusIcon(order.status)} className="mr-1 h-3 w-3" />
                  <span className="hidden sm:inline">{getStatusText(order.status)}</span>
                  <span className="sm:hidden">{getStatusText(order.status).substring(0, 8)}</span>
                </span>
                <span className={`px-2 py-1 text-xs rounded-full ${getPaymentStatusColor(order.paymentStatus)}`}>
                  <span className="hidden sm:inline">{getPaymentStatusText(order.paymentStatus)}</span>
                  <span className="sm:hidden">{getPaymentStatusText(order.paymentStatus).substring(0, 8)}</span>
                </span>
              </div>
              <p className="text-sm sm:text-base font-medium text-gray-900">
                {formatCurrency(calculateOrderTotal(order), order.currency)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Order Content - Conditional rendering based on viewMode */}
      <div className="px-3 sm:px-4 pb-3 sm:pb-4 border-t border-gray-100">
        {viewMode === 'customer' ? (
          /* Customer Mode - Simple View */
          <div className="space-y-3">
            {/* Simple Product List */}
            <div className="flex items-center space-x-3">
              <div className="flex -space-x-2">
                {order.items && order.items.length > 0 ? (
                  <>
                    {order.items.slice(0, 2).map((item, idx) => (
                      <div key={idx} className="h-8 w-8 rounded-full border-2 border-white bg-gray-200 overflow-hidden">
                        <img 
                          src={getProductImage(item.image || '', item.sku || '', item.name)} 
                          alt={item.name} 
                          className="h-full w-full object-cover"
                          onError={(e) => handleImageError(e.nativeEvent, item.sku || '', item.name)}
                        />
                      </div>
                    ))}
                    {order.items.length > 2 && (
                      <div className="h-8 w-8 rounded-full border-2 border-white bg-gray-100 flex items-center justify-center text-xs font-medium text-gray-500">
                        +{order.items.length - 2}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="h-8 w-8 rounded-full border-2 border-white bg-gray-200 flex items-center justify-center">
                    <FontAwesomeIcon icon={faShoppingBag} className="h-4 w-4 text-gray-400" />
                  </div>
                )}
              </div>
              <span className="text-sm text-gray-600">
                {order.items && order.items.length > 0 
                  ? `${order.items.length} sản phẩm` 
                  : "Chi tiết thanh toán"}
              </span>
            </div>
            
            {/* Simple Customer & Payment Info */}
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                <FontAwesomeIcon icon={faUser} className="h-4 w-4 text-gray-400 mr-2" />
                {order.customerName || 'Khách hàng'}
              </div>
              <div className="text-sm text-gray-600">
                <FontAwesomeIcon 
                  icon={order.paymentMethod === 'card' ? faCreditCard : faMoneyBill} 
                  className="h-4 w-4 text-gray-400 mr-2" 
                />
                {order.paymentMethod === 'card' ? 'Thẻ' : 
                 order.paymentMethod === 'cash' ? 'Tiền mặt' : 
                 order.paymentMethod === 'transfer' ? 'Chuyển khoản' : 
                 order.paymentMethod === 'convenience_store' ? 'Cửa hàng' :
                 'Khác'}
              </div>
            </div>
            
            {/* Customer Notes Section - Customer Mode */}
            {order.notes && order.notes.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <h5 className="text-xs font-medium text-gray-700 mb-2 flex items-center">
                  <FontAwesomeIcon icon={faStickyNote} className="h-3 w-3 text-gray-400 mr-1" />
                  Ghi chú của bạn
                </h5>
                <div className="space-y-1">
                  {order.notes.slice(0, 2).map((note: any, index: number) => (
                    <div key={note.id || index} className="text-xs text-gray-600 bg-yellow-50 p-2 rounded border-l-2 border-yellow-200">
                      {note.text}
                    </div>
                  ))}
                  {order.notes.length > 2 && (
                    <div className="text-xs text-gray-500 italic">
                      +{order.notes.length - 2} ghi chú khác...
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Admin Mode - Full Details View */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 sm:gap-4 items-start">
            {/* Left: Product info */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
                <div className="flex -space-x-1 sm:-space-x-2">
                  {order.items && order.items.length > 0 ? (
                    <>
                      {order.items.slice(0, 3).map((item, idx) => (
                        <div key={idx} className="h-6 w-6 sm:h-8 sm:w-8 rounded-full border border-white bg-gray-200 overflow-hidden">
                          <img 
                            src={getProductImage(item.image || '', item.sku || '', item.name)} 
                            alt={item.name} 
                            className="h-full w-full object-cover"
                            onError={(e) => handleImageError(e.nativeEvent, item.sku || '', item.name)}
                          />
                        </div>
                      ))}
                      {order.items.length > 3 && (
                        <div className="h-6 w-6 sm:h-8 sm:w-8 rounded-full border border-white bg-gray-100 flex items-center justify-center text-xs font-medium text-gray-500">
                          +{order.items.length - 3}
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="h-6 w-6 sm:h-8 sm:w-8 rounded-full border border-white bg-gray-200 flex items-center justify-center">
                      <FontAwesomeIcon icon={faShoppingBag} className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
                    </div>
                  )}
                </div>
                <span className="text-xs sm:text-sm text-gray-600 truncate">
                  {order.items && order.items.length > 0 
                    ? `${order.items.length} sản phẩm` 
                    : "Chi tiết thanh toán"}
                </span>
              </div>
              
              {/* Payment method */}
              <div className="text-xs text-gray-600">
                <span className="inline-flex items-center">
                  <FontAwesomeIcon 
                    icon={order.paymentMethod === 'card' ? faCreditCard : faMoneyBill} 
                    className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" 
                  />
                  <span className="truncate">
                    {order.paymentMethod === 'card' ? 'Thẻ tín dụng/ghi nợ' : 
                     order.paymentMethod === 'cash' ? 'Tiền mặt' : 
                     order.paymentMethod === 'transfer' ? 'Chuyển khoản' : 
                     order.paymentMethod === 'convenience_store' ? 'Cửa hàng tiện lợi' :
                     order.paymentMethod || 'Không có thông tin'}
                  </span>
                </span>
              </div>
            </div>
            
            {/* Center: Customer info */}
            <div className="lg:col-span-1">
              <div className="text-xs bg-blue-50 p-2 rounded">
                <div className="flex items-center mb-1">
                  <FontAwesomeIcon icon={faUser} className="h-3 w-3 text-blue-500 mr-1 flex-shrink-0" />
                  <span className="truncate">{order.customerName || 'Không có tên'}</span>
                </div>
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faPhone} className="h-3 w-3 text-blue-500 mr-1 flex-shrink-0" />
                  <span className="truncate">{order.customerPhone || 'Không có SĐT'}</span>
                </div>
                {order.customerEmail && (
                  <div className="flex items-center mt-1">
                    <FontAwesomeIcon icon={faEnvelope} className="h-3 w-3 text-blue-500 mr-1 flex-shrink-0" />
                    <span className="truncate">{order.customerEmail}</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Right: Status info and special notices */}
            <div className="lg:col-span-1">
              {/* Time remaining if applicable */}
              {order.validUntil && !order.isExpired && order.status === 'pending' && (
                <div className="text-xs text-blue-600 mb-1">
                  <FontAwesomeIcon icon={faClock} className="h-3 w-3 mr-1" />
                  Còn lại: {getTimeRemaining(order.validUntil)}
                </div>
              )}
              
              {/* Loyalty Points Display */}
              {order.loyaltyPoints && order.loyaltyPoints.awarded > 0 && (
                <div className="text-xs mb-1">
                  <FontAwesomeIcon icon={faGift} className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-green-600 font-medium">
                    +{order.loyaltyPoints.awarded} điểm thưởng
                  </span>
                </div>
              )}

              {/* APN Callback Status Indicator */}
              {order.paymentCallbacks && order.paymentCallbacks.length > 0 && (
                <div className="text-xs mb-1">
                  <FontAwesomeIcon icon={faInfoCircle} className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-blue-600 font-medium">
                    {order.paymentCallbacks.length} callback{order.paymentCallbacks.length > 1 ? 's' : ''}
                  </span>
                  <span className="text-gray-500 ml-1">
                    từ {getPaymentChannelName(order.paymentMethod, order.paymentSubMethod)}
                  </span>
                </div>
              )}

              {/* Quick iBON access for pending payments */}
              {order.paymentMethod === 'convenience_store' && 
               order.paymentSubMethod === 'seven_eleven_ibon' && 
               order.paymentInfo?.barcodes && 
               (order.paymentInfo.barcodes.barcode1 || order.paymentInfo.barcodes.barcode2 || order.paymentInfo.barcodes.barcode3) && 
               order.status === 'pending' && (
                <div className="text-xs text-green-600">
                  <FontAwesomeIcon icon={faCreditCard} className="h-3 w-3 mr-1" />
                  Có {[order.paymentInfo.barcodes.barcode1, order.paymentInfo.barcodes.barcode2, order.paymentInfo.barcodes.barcode3].filter(Boolean).length} mã vạch iBON
                </div>
              )}
            </div>
            
            {/* Customer Notes Section - Admin Mode */}
            {order.notes && order.notes.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-100 lg:col-span-3">
                <h5 className="text-xs font-medium text-gray-700 mb-2 flex items-center">
                  <FontAwesomeIcon icon={faStickyNote} className="h-3 w-3 text-gray-400 mr-1" />
                  Ghi chú của bạn ({order.notes.length})
                </h5>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                  {order.notes.map((note: any, index: number) => (
                    <div key={note.id || index} className="text-xs text-gray-600 bg-yellow-50 p-2 rounded border-l-2 border-yellow-200">
                      <div className="font-medium mb-1">{note.text}</div>
                      {note.createdAt && (
                        <div className="text-gray-400 text-xs">
                          {formatDate(note.createdAt)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Order Actions */}
      <div className="p-4 border-t border-gray-200 bg-gray-50 flex flex-col sm:flex-row sm:justify-between gap-3">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => toggleOrderDetails(order.id)}
            className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-xs flex items-center transition-colors"
          >
            <span>Chi tiết</span>
            <FontAwesomeIcon icon={faChevronDown} className="ml-1 h-3 w-3" />
          </button>
        </div>
        
        <div className="flex items-center space-x-2">
          {viewMode === 'admin' && (
            <Link 
              href={`/${storeParam}/admin/orders/${order.id}`}
              className="px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-xs flex items-center"
            >
              <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-1 h-3 w-3" />
              <span>Quản lý</span>
            </Link>
          )}
          
          <button 
            onClick={() => onPrintOrder(order)}
            className="px-3 py-1.5 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md text-xs flex items-center"
          >
            <FontAwesomeIcon icon={faFileInvoice} className="mr-1 h-3 w-3" />
            <span>Hóa đơn</span>
          </button>
        </div>
      </div>
      
      {/* Modal for Order Details - Only shown when expanded */}
      {expandedOrderId === order.id && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-gray-900 bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Chi tiết đơn hàng #{order.id}
                </h3>
                <div className="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('customer')}
                    className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                      viewMode === 'customer'
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    Customer
                  </button>
                  <button
                    onClick={() => setViewMode('admin')}
                    className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                      viewMode === 'admin'
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    Admin
                  </button>
                </div>
              </div>
              <button 
                onClick={() => setExpandedOrderId(null)}
                className="text-gray-400 hover:text-gray-500"
              >
                <FontAwesomeIcon icon={faTimes} className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-4">
              {/* Order detail content from the original expanded view */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Customer Information */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200">
                    Thông tin khách hàng
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <FontAwesomeIcon icon={faUser} className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                      <div>
                        <p className="text-xs text-gray-500">Tên:</p>
                        <p className="text-sm text-gray-800">{order.customerName || 'Không có thông tin'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <FontAwesomeIcon icon={faPhone} className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                      <div>
                        <p className="text-xs text-gray-500">Điện thoại:</p>
                        <p className="text-sm text-gray-800">{order.customerPhone || 'Không có SĐT'}</p>
                      </div>
                    </div>
                    
                    {viewMode === 'admin' && (
                      <div className="flex items-start">
                        <FontAwesomeIcon icon={faEnvelope} className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                        <div>
                          <p className="text-xs text-gray-500">Email:</p>
                          <p className="text-sm text-gray-800">{order.customerEmail || 'Không có thông tin'}</p>
                        </div>
                      </div>
                    )}
                    
                    {viewMode === 'admin' && order.shippingAddress && (
                      <div className="flex items-start">
                        <FontAwesomeIcon icon={faMapMarkerAlt} className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                        <div>
                          <p className="text-xs text-gray-500">Địa chỉ giao hàng:</p>
                          <p className="text-sm text-gray-800">{order.shippingAddress}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Payment Information */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200">
                    Thông tin thanh toán
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <FontAwesomeIcon icon={order.paymentMethod === 'card' ? faCreditCard : faMoneyBill} className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                      <div>
                        <p className="text-xs text-gray-500">Phương thức:</p>
                        <p className="text-sm text-gray-800">
                          {order.paymentMethod === 'card' ? 'Thẻ tín dụng/ghi nợ' : 
                           order.paymentMethod === 'cash' ? 'Tiền mặt' : 
                           order.paymentMethod === 'transfer' ? 'Chuyển khoản' : 
                           order.paymentMethod === 'convenience_store' ? 'Cửa hàng tiện lợi' :
                           order.paymentMethod || 'Không có thông tin'}
                        </p>
                        {viewMode === 'admin' && order.paymentSubMethod && (
                          <p className="text-xs text-gray-500 mt-1">
                            {order.paymentSubMethod === 'seven_eleven_ibon' ? '7-Eleven ibon' :
                             order.paymentSubMethod === 'family_mart' ? 'FamilyMart' :
                             order.paymentSubMethod}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {viewMode === 'admin' && (
                      <div className="flex items-start">
                        <FontAwesomeIcon icon={faCalendarAlt} className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                        <div>
                          <p className="text-xs text-gray-500">Thời gian đặt hàng:</p>
                          <p className="text-sm text-gray-800">{formatDate(order.createdAt)}</p>
                        </div>
                      </div>
                    )}
                    
                    {viewMode === 'admin' && order.validUntil && (
                      <div className="flex items-start">
                        <FontAwesomeIcon icon={faClock} className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                        <div>
                          <p className="text-xs text-gray-500">Thời hạn thanh toán:</p>
                          <p className="text-sm text-gray-800">
                            {formatDate(order.validUntil)}
                            {!order.isExpired && (
                              <span className="ml-2 text-xs text-blue-600">({getTimeRemaining(order.validUntil)})</span>
                            )}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Order Items Table */}
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200 mb-3">
                  Chi tiết đơn hàng
                </h4>
                
                {order.items && order.items.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-100">
                        <tr>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Sản phẩm
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Giá
                          </th>
                          <th scope="col" className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            SL
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tổng
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {order.items.map((item, index) => (
                          <tr 
                            key={index} 
                            className="hover:bg-gray-50"
                          >
                            <td className="px-3 py-3 whitespace-normal">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded overflow-hidden mr-3">
                                  <img 
                                    src={getProductImage(item.image || '', item.sku || '', item.name)} 
                                    alt={item.name} 
                                    className="h-full w-full object-cover"
                                    onError={(e) => handleImageError(e.nativeEvent, item.sku || '', item.name)}
                                  />
                                </div>
                                <div>
                                  <div className="text-sm text-gray-900">{item.name}</div>
                                  {viewMode === 'admin' && item.sku && (
                                    <div className="text-xs text-gray-500 mt-0.5">SKU: {item.sku}</div>
                                  )}
                                  {viewMode === 'admin' && item.type === 'topup' && (
                                    <div className="text-xs text-gray-500 mt-0.5">
                                      {item.cardSerial && <div>Serial: {item.cardSerial}</div>}
                                      {item.cardNumber && <div>Mã thẻ: {item.cardNumber}</div>}
                                      {item.cardPin && <div>Mã PIN: {item.cardPin}</div>}
                                      {item.topupCode && <div>Mã nạp: {item.topupCode}</div>}
                                      {item.expiryDate && <div>Hạn dùng: {item.expiryDate}</div>}
                                    </div>
                                  )}
                                  {viewMode === 'admin' && item.type === 'sim' && (
                                    <div className="text-xs text-gray-500 mt-0.5">
                                      {item.simNumber && <div>SIM: {item.simNumber}</div>}
                                      {item.phoneNumber && <div>SDT: {item.phoneNumber}</div>}
                                      {item.iccid && <div>ICCID: {item.iccid}</div>}
                                      {item.networkProvider && <div>Nhà mạng: {item.networkProvider}</div>}
                                    </div>
                                  )}
                                  {viewMode === 'admin' && item.type === 'device' && (
                                    <div className="text-xs text-gray-500 mt-0.5">
                                      {item.model && <div>Model: {item.model}</div>}
                                      {item.imei && <div>IMEI: {item.imei}</div>}
                                      {item.color && <div>Màu sắc: {item.color}</div>}
                                    </div>
                                  )}
                                  {viewMode === 'admin' && item.type === 'service' && (
                                    <div className="text-xs text-gray-500 mt-0.5">
                                      {item.servicePeriod && <div>Thời hạn: {item.servicePeriod}</div>}
                                      {item.serviceStartDate && <div>Ngày bắt đầu: {item.serviceStartDate}</div>}
                                      {item.serviceEndDate && <div>Ngày kết thúc: {item.serviceEndDate}</div>}
                                    </div>
                                  )}
                                  
                                  {/* Usage Tracking for SIM/Card items - Admin only */}
                                  {viewMode === 'admin' && (item.type === 'sim' || item.type === 'topup') && (
                                    <div className="mt-3">
                                      <UsageTrackingForm
                                        orderId={order.id}
                                        itemIndex={index}
                                        itemType={item.type}
                                        itemName={item.name}
                                        initialUsedFor={item.usedFor}
                                        initialUsageNotes={item.usageNotes}
                                        usageDate={item.usageDate}
                                        usageUpdatedAt={item.usageUpdatedAt}
                                        customerEmail={order.customerEmail}
                                        customerPhone={order.customerPhone}
                                        onUsageUpdate={(usageInfo) => {
                                          // Handle usage update - this would typically trigger a parent component update
                                          console.log('Usage updated for item:', usageInfo);
                                        }}
                                      />
                                    </div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-3 py-3 whitespace-nowrap text-right text-sm text-gray-500">
                              {formatCurrency(Number(item.price), order.currency)}
                            </td>
                            <td className="px-3 py-3 whitespace-nowrap text-center text-sm text-gray-500">
                              {item.quantity}
                            </td>
                            <td className="px-3 py-3 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                              {formatCurrency(Number(item.price) * item.quantity, order.currency)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot className="bg-gray-50">
                        <tr>
                          <td colSpan={3} className="px-3 py-3 text-right text-sm font-medium text-gray-900">
                            Tổng cộng:
                          </td>
                          <td className="px-3 py-3 text-right text-sm font-bold text-gray-900">
                            {formatCurrency(calculateOrderTotal(order), order.currency)}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                ) : (
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4 text-blue-400 mr-2" />
                      <span>Chi tiết sản phẩm không khả dụng</span>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Google Sheets Details */}
              {order.metadata && order.metadata.type === 'google_sheets_assignment' && (
                <div className="mt-6">
                  <GoogleSheetsDetails
                    metadata={order.metadata}
                  />
                </div>
              )}
              
              {/* Assigned Column Values */}
              {order.metadata && order.metadata.assignedColumnValues && order.metadata.assignedColumnValues.length > 0 && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200 mb-3">
                    Giá trị cột được gán
                  </h4>
                  <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {order.metadata.assignedColumnValues.map((columnValue: any, index: number) => (
                        <div key={index} className="bg-white rounded p-3 border border-gray-100">
                          <div className="text-xs text-gray-500 mb-1">Cột {columnValue.columnLetter}</div>
                          <div className="font-medium text-gray-900 break-all">{columnValue.value}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
              
              {/* Loyalty Points Details - Admin only */}
              {viewMode === 'admin' && order.loyaltyPoints && order.loyaltyPoints.awarded > 0 && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200 mb-3">
                    Điểm thưởng đã nhận
                  </h4>
                  <div className="bg-green-50 rounded-lg border border-green-200 p-4">
                    <div className="flex items-center mb-3">
                      <FontAwesomeIcon icon={faGift} className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-lg font-semibold text-green-700">
                        +{order.loyaltyPoints.awarded} điểm thưởng
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
                      <div className="bg-white rounded p-3">
                        <div className="text-xs text-gray-500 mb-1">Điểm cơ bản</div>
                        <div className="font-medium text-gray-900">+{order.loyaltyPoints.breakdown.basePoints}</div>
                      </div>
                      
                      {order.loyaltyPoints.breakdown.tierBonus > 0 && (
                        <div className="bg-white rounded p-3">
                          <div className="text-xs text-gray-500 mb-1">Thưởng hạng thành viên</div>
                          <div className="font-medium text-blue-600">+{order.loyaltyPoints.breakdown.tierBonus}</div>
                        </div>
                      )}
                      
                      {order.loyaltyPoints.breakdown.specialEventBonus > 0 && (
                        <div className="bg-white rounded p-3">
                          <div className="text-xs text-gray-500 mb-1">Thưởng sự kiện đặc biệt</div>
                          <div className="font-medium text-purple-600">+{order.loyaltyPoints.breakdown.specialEventBonus}</div>
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-3 text-xs text-gray-600">
                      Điểm được trao vào: {formatDate(order.loyaltyPoints.awardedAt)}
                    </div>
                  </div>
                </div>
              )}

              {/* Customer Notes Section - Available in both modes */}
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200 mb-3">
                  Ghi chú của bạn
                </h4>
                <OrderNotesManager 
                  orderId={order.id}
                  initialNotes={order.notes || []}
                />
              </div>

              {/* APN Callback Status - Admin only */}
              {viewMode === 'admin' && renderAPNCallbackStatus()}
              
              {/* Payment Info for convenience store orders - Admin only */}
              {viewMode === 'admin' && order.paymentMethod === 'convenience_store' && order.paymentInfo && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-700 pb-2 border-b border-gray-200 mb-3">
                    Thông tin thanh toán tại cửa hàng tiện lợi
                  </h4>
                  
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    {order.paymentSubMethod === 'seven_eleven_ibon' && order.paymentInfo.ibonPaymentCode && (
                      <div className="space-y-4">
                        <div className="flex flex-col space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Mã thanh toán ibon:</span>
                            <span className="text-sm font-medium">{order.paymentInfo.ibonPaymentCode}</span>
                          </div>
                          {order.paymentInfo.ibonShopId && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Mã cửa hàng:</span>
                              <span className="text-sm font-medium">{order.paymentInfo.ibonShopId}</span>
                            </div>
                          )}
                        </div>
                        
                        {/* iBON Barcodes */}
                        {order.paymentInfo.barcodes && (order.paymentInfo.barcodes.barcode1 || order.paymentInfo.barcodes.barcode2 || order.paymentInfo.barcodes.barcode3) && (
                          <div className="mt-4">
                            <p className="text-sm font-medium text-gray-700 mb-3">Mã vạch thanh toán (in hoặc hiển thị tại máy iBON):</p>
                            <BarcodeDisplay
                              ibonCode1={order.paymentInfo.barcodes.barcode1}
                              ibonCode2={order.paymentInfo.barcodes.barcode2}
                              ibonCode3={order.paymentInfo.barcodes.barcode3}
                              orderId={order.id}
                              totalWithFee={calculateOrderTotal(order)}
                              currency={order.currency}
                              ibonExpiryMinutes={order.paymentInfo.orderExpireDate ? 
                                (new Date(order.paymentInfo.orderExpireDate).getTime() - Date.now()) / (1000 * 60) : 
                                7 * 24 * 60 // Default 7 days in minutes
                              }
                              setOrderCompleted={() => {}} // No-op for display mode
                              onSaveAndCopy={(data: any) => {
                                console.log('Barcode data saved:', data);
                              }}
                              showCompleteButton={false}
                              className="mt-2"
                            />
                            
                            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                              <div className="flex items-start">
                                <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4 text-blue-400 mt-0.5 mr-2" />
                                <div className="text-sm text-blue-800">
                                  <p className="font-medium mb-1">Hướng dẫn thanh toán:</p>
                                  <ol className="list-decimal list-inside space-y-1 text-xs">
                                    <li>Đến máy iBON tại cửa hàng 7-Eleven</li>
                                    <li>Chọn "代碼繳費" (Thanh toán bằng mã)</li>
                                    <li>Quét các mã vạch trên hoặc nhập mã thanh toán</li>
                                    <li>Xác nhận thông tin và thanh toán tại quầy</li>
                                  </ol>
                                </div>
                              </div>
                            </div>

                            {/* Download/Print buttons */}
                            <div className="mt-4 flex space-x-2">
                              <button 
                                onClick={async () => {
                                  const barcodeWindow = window.open('', '_blank');
                                  if (barcodeWindow) {
                                    // Generate barcode images for the popup
                                    let barcodeImages = { barcode1: '', barcode2: '', barcode3: '' };
                                    try {
                                      const promises: Promise<string>[] = [];
                                      if (order.paymentInfo.barcodes.barcode1) {
                                        promises.push(generateBarcodeImage(order.paymentInfo.barcodes.barcode1));
                                      }
                                      if (order.paymentInfo.barcodes.barcode2) {
                                        promises.push(generateBarcodeImage(order.paymentInfo.barcodes.barcode2));
                                      }
                                      if (order.paymentInfo.barcodes.barcode3) {
                                        promises.push(generateBarcodeImage(order.paymentInfo.barcodes.barcode3));
                                      }
                                      
                                      const images = await Promise.all(promises);
                                      let imageIndex = 0;
                                      if (order.paymentInfo.barcodes.barcode1) {
                                        barcodeImages.barcode1 = images[imageIndex++];
                                      }
                                      if (order.paymentInfo.barcodes.barcode2) {
                                        barcodeImages.barcode2 = images[imageIndex++];
                                      }
                                      if (order.paymentInfo.barcodes.barcode3) {
                                        barcodeImages.barcode3 = images[imageIndex++];
                                      }
                                    } catch (error) {
                                      console.error('Error generating barcode images:', error);
                                    }

                                    barcodeWindow.document.write(`
                                      <!DOCTYPE html>
                                      <html>
                                      <head>
                                        <title>iBON Barcodes - Đơn hàng #${order.id}</title>
                                        <style>
                                          body { font-family: Arial, sans-serif; padding: 20px; }
                                          .barcode-container { margin-bottom: 30px; text-align: center; }
                                          .barcode-title { font-weight: bold; margin-bottom: 10px; }
                                          .barcode-code { font-family: monospace; margin-top: 10px; }
                                          @media print { 
                                            .no-print { display: none; }
                                            body { margin: 0; padding: 10px; }
                                          }
                                        </style>
                                      </head>
                                      <body>
                                        <h2>Mã vạch thanh toán iBON</h2>
                                        <p>Đơn hàng: #${order.id}</p>
                                        <p>Mã thanh toán: ${order.paymentInfo.ibonPaymentCode}</p>
                                        <hr>
                                        ${[
                                          { code: order.paymentInfo.barcodes.barcode1, label: 'Mã vạch 1', image: barcodeImages.barcode1 },
                                          { code: order.paymentInfo.barcodes.barcode2, label: 'Mã vạch 2', image: barcodeImages.barcode2 },
                                          { code: order.paymentInfo.barcodes.barcode3, label: 'Mã vạch 3', image: barcodeImages.barcode3 }
                                        ].filter(barcode => barcode.code).map((barcode, index) => `
                                          <div class="barcode-container">
                                            <div class="barcode-title">${barcode.label}</div>
                                            ${barcode.image ? 
                                              `<img src="${barcode.image}" alt="${barcode.label}" style="max-height: 100px;">` : 
                                              `<div style="border: 1px solid #ccc; padding: 20px; font-family: monospace; font-size: 16px;">${barcode.code}</div>`
                                            }
                                            <div class="barcode-code">${barcode.code}</div>
                                          </div>
                                        `).join('')}
                                        <div class="no-print" style="margin-top: 30px; text-align: center;">
                                          <button onclick="window.print();" style="padding: 10px 20px; margin-right: 10px;">In mã vạch</button>
                                          <button onclick="window.close();" style="padding: 10px 20px;">Đóng</button>
                                        </div>
                                      </body>
                                      </html>
                                    `);
                                    barcodeWindow.document.close();
                                  }
                                }}
                                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm flex items-center"
                              >
                                <FontAwesomeIcon icon={faFileInvoice} className="mr-2 h-4 w-4" />
                                In mã vạch
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {order.paymentSubMethod === 'family_mart' && order.paymentInfo.pinCode && (
                      <div className="space-y-4">
                        <div className="flex flex-col space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Mã FamiPort:</span>
                            <span className="text-sm font-medium">{order.paymentInfo.pinCode}</span>
                          </div>
                          {order.paymentInfo.paymentNo && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Số thanh toán:</span>
                              <span className="text-sm font-medium">{order.paymentInfo.paymentNo}</span>
                            </div>
                          )}
                        </div>
                        
                        {/* FamilyMart Barcodes */}
                        {order.paymentInfo.barcodes && (order.paymentInfo.barcodes.barcode1 || order.paymentInfo.barcodes.barcode2 || order.paymentInfo.barcodes.barcode3) && (
                          <div className="mt-4">
                            <p className="text-sm font-medium text-gray-700 mb-3">Mã vạch thanh toán FamilyMart:</p>
                            <FamilyMartBarcodeDisplay
                              paymentBarcode={[
                                order.paymentInfo.barcodes.barcode1,
                                order.paymentInfo.barcodes.barcode2,
                                order.paymentInfo.barcodes.barcode3
                              ].filter(Boolean).join(',')}
                              orderId={order.id}
                              totalWithFee={calculateOrderTotal(order)}
                              currency={order.currency}
                              paymentCode={order.paymentInfo.pinCode}
                              paymentExpiry={order.paymentInfo.orderExpireDate || order.validUntil}
                              onSaveAndCopy={(data: any) => {
                                console.log('FamilyMart barcode data saved:', data);
                              }}
                              showDownloadButton={true}
                              className="mt-2"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
              <button 
                onClick={() => setExpandedOrderId(null)}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Đóng
              </button>
              
              <button 
                onClick={() => onPrintOrder(order)}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <FontAwesomeIcon icon={faFileInvoice} className="mr-2 h-4 w-4" />
                In hóa đơn
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderCard;