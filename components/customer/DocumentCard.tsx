import React, { useState } from 'react';
import { 
  FaFile, FaFileAlt, FaFilePdf, FaFileImage, 
  FaFileDownload, FaEye, FaCalendarAlt, FaTrash,
  FaSpinner, FaExclamationTriangle
} from 'react-icons/fa';

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: string;
  relatedOrder?: string;
  url: string;
  documentType?: string;
}

interface DocumentCardProps {
  document: Document;
  onPreview: (doc: Document) => void;
  onDelete: (doc: Document) => void;
  getFileIcon: (type: string) => React.ReactElement;
  formatFileSize: (bytes: number) => string;
  formatDate: (dateString: string) => string;
  getDocumentTypeLabel: (type?: string) => string;
  isImageDocument: (doc: Document) => boolean;
}

// Delete Confirmation Modal
const DeleteConfirmationModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  documentName: string;
  isDeleting: boolean;
}> = ({ isOpen, onClose, onConfirm, documentName, isDeleting }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg max-w-md w-full">
        <div className="flex items-center p-4 border-b">
          <FaExclamationTriangle className="text-red-500 mr-3" size={24} />
          <h3 className="font-medium text-lg">Xác nhận xóa tài liệu</h3>
        </div>
        
        <div className="p-4">
          <p className="text-gray-700 mb-4">
            Bạn có chắc chắn muốn xóa tài liệu "{documentName}"? 
            Hành động này không thể hoàn tác.
          </p>
        </div>
        
        <div className="flex justify-end space-x-2 p-4 border-t">
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            disabled={isDeleting}
          >
            Hủy
          </button>
          <button 
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <FaSpinner className="animate-spin mr-2" />
                Đang xóa...
              </>
            ) : (
              <>
                <FaTrash className="mr-2" />
                Xóa
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

const DocumentCard: React.FC<DocumentCardProps> = ({ 
  document, 
  onPreview, 
  onDelete,
  getFileIcon, 
  formatFileSize, 
  formatDate, 
  getDocumentTypeLabel, 
  isImageDocument 
}) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const isImage = isImageDocument(document);

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      // Get the store parameter from the URL
      const store = window.location.pathname.split('/')[1] || 'magshop';
      
      const response = await fetch(`/api/documents/delete?store=${encodeURIComponent(store)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId: document.id,
          fileName: document.name,
          url: document.url
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        onDelete(document);
        setShowDeleteModal(false);
      } else {
        throw new Error(result.message || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete error:', error);
      alert(`Có lỗi xảy ra khi xóa tài liệu: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <div 
        className={`bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 ${isImage ? 'cursor-pointer group' : ''}`}
        onClick={isImage ? () => onPreview(document) : undefined}
      >
        <div className="flex items-center p-4">
          {/* Document Icon/Thumbnail */}
          <div className="flex-shrink-0 mr-4">
            {isImage ? (
              <div className="relative w-16 h-16 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg overflow-hidden">
                <img 
                  src={document.url} 
                  alt={document.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  loading="lazy"
                />
                {/* Overlay with preview icon */}
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                  <FaEye className="text-white text-sm" />
                </div>
              </div>
            ) : (
              <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg flex items-center justify-center">
                {getFileIcon(document.type)}
              </div>
            )}
          </div>
          
          {/* Document Information */}
          <div className="flex-grow">
            <div className="flex items-start justify-between">
              <div className="flex-grow pr-4">
                <h3 className="font-semibold text-gray-900 text-base mb-1 line-clamp-1" title={document.name}>
                  {document.name}
                </h3>
                
                <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                  {/* Document Type */}
                  {document.documentType && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs font-medium">
                      {getDocumentTypeLabel(document.documentType)}
                    </span>
                  )}
                  
                  {/* Related Order */}
                  {document.relatedOrder && (
                    <div className="flex items-center text-blue-600">
                      <svg className="w-3 h-3 mr-1 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                      <span className="font-medium">#{document.relatedOrder}</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  {/* Upload Date */}
                  <div className="flex items-center">
                    <FaCalendarAlt className="mr-1 text-gray-400" />
                    <span>{formatDate(document.uploadDate)}</span>
                  </div>
                  
                  {/* File Size */}
                  <div className="flex items-center">
                    <svg className="w-3 h-3 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                    <span className="font-medium">{formatFileSize(document.size)}</span>
                  </div>
                </div>
              </div>
              
              {/* Action buttons */}
              <div className="flex space-x-2 flex-shrink-0">
                {isImage && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onPreview(document);
                    }}
                    className="flex items-center px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                  >
                    <FaEye className="mr-1.5" />
                    Xem
                  </button>
                )}
                <a 
                  href={document.url} 
                  download 
                  className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={(e) => e.stopPropagation()}
                >
                  <FaFileDownload className="mr-1.5" />
                  Tải về
                </a>
                <button
                  onClick={handleDeleteClick}
                  className="flex items-center px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 text-sm font-medium rounded-lg transition-colors duration-200"
                  title="Xóa tài liệu"
                >
                  <FaTrash className="mr-1.5" />
                  Xóa
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        documentName={document.name}
        isDeleting={isDeleting}
      />
    </>
  );
};

export default DocumentCard; 