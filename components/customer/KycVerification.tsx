import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faIdCard, faPassport, faFileUpload, faCheckCircle, faTimesCircle } from '@fortawesome/free-solid-svg-icons';

interface Document {
  id: string;
  type: 'passport' | 'nationalId' | 'driversLicense';
  name: string;
  status: 'pending' | 'verified' | 'rejected';
  uploadDate: string;
  expiryDate?: string;
}

const KycVerification: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      type: 'passport',
      name: 'Passport_123456.pdf',
      status: 'verified',
      uploadDate: '2024-03-15',
      expiryDate: '2025-03-15',
    },
    {
      id: '2',
      type: 'nationalId',
      name: 'ID_Card_789012.pdf',
      status: 'pending',
      uploadDate: '2024-03-20',
    },
  ]);

  const getStatusColor = (status: Document['status']) => {
    switch (status) {
      case 'verified':
        return 'text-green-600 bg-green-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'verified':
        return faCheckCircle;
      case 'rejected':
        return faTimesCircle;
      case 'pending':
        return faFileUpload;
      default:
        return faIdCard;
    }
  };

  const getDocumentIcon = (type: Document['type']) => {
    switch (type) {
      case 'passport':
        return faPassport;
      case 'nationalId':
        return faIdCard;
      case 'driversLicense':
        return faIdCard;
      default:
        return faIdCard;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">KYC Verification</h2>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          Upload New Document
        </button>
      </div>

      <div className="space-y-4">
        {documents.map((doc) => (
          <div key={doc.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <FontAwesomeIcon icon={getDocumentIcon(doc.type)} className="text-gray-400 text-xl" />
                <div>
                  <h3 className="text-lg font-medium">
                    {doc.type === 'passport' ? 'Passport' : 
                     doc.type === 'nationalId' ? 'National ID' : 'Driver\'s License'}
                  </h3>
                  <p className="text-gray-600">{doc.name}</p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded-full text-sm font-medium ${getStatusColor(doc.status)}`}>
                <FontAwesomeIcon icon={getStatusIcon(doc.status)} className="mr-1" />
                {doc.status.charAt(0).toUpperCase() + doc.status.slice(1)}
              </span>
            </div>

            <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500">Upload Date</p>
                <p className="font-medium">{doc.uploadDate}</p>
              </div>
              {doc.expiryDate && (
                <div>
                  <p className="text-sm text-gray-500">Expiry Date</p>
                  <p className="font-medium">{doc.expiryDate}</p>
                </div>
              )}
              <div>
                <p className="text-sm text-gray-500">Actions</p>
                <div className="flex space-x-2">
                  <button className="text-indigo-600 hover:text-indigo-800">
                    View
                  </button>
                  <button className="text-red-600 hover:text-red-800">
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6">
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FontAwesomeIcon icon={faIdCard} className="text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Please ensure all documents are clear and legible. Accepted formats: PDF, JPG, PNG
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KycVerification; 