import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHistory, faSync, faGift, faWallet } from '@fortawesome/free-solid-svg-icons';

interface TopUp {
  id: string;
  amount: number;
  date: string;
  status: 'success' | 'pending' | 'failed';
  type: 'manual' | 'auto';
  balance: number;
}

const TopUpHistory: React.FC = () => {
  const [topUps, setTopUps] = useState<TopUp[]>([
    {
      id: '1',
      amount: 50,
      date: '2024-03-20',
      status: 'success',
      type: 'manual',
      balance: 75,
    },
    {
      id: '2',
      amount: 30,
      date: '2024-03-15',
      status: 'success',
      type: 'auto',
      balance: 25,
    },
  ]);

  const [autoRechargeEnabled, setAutoRechargeEnabled] = useState(false);
  const [currentBalance, setCurrentBalance] = useState(75);

  const getStatusColor = (status: TopUp['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Top-Up & Recharge</h2>
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faWallet} className="text-gray-400 mr-2" />
            <span className="text-lg font-medium">${currentBalance}</span>
          </div>
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
            Top Up Now
          </button>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faSync} className="text-gray-400 mr-2" />
            <div>
              <h3 className="font-medium">Auto-Recharge</h3>
              <p className="text-sm text-gray-500">Automatically top up when balance is low</p>
            </div>
          </div>
          <button
            onClick={() => setAutoRechargeEnabled(!autoRechargeEnabled)}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
              autoRechargeEnabled ? 'bg-indigo-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                autoRechargeEnabled ? 'translate-x-5' : 'translate-x-0'
              }`}
            />
          </button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Top-Up History</h3>
          <button className="text-indigo-600 hover:text-indigo-800">
            <FontAwesomeIcon icon={faGift} className="mr-2" />
            Gift Balance
          </button>
        </div>

        {topUps.map((topUp) => (
          <div key={topUp.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <FontAwesomeIcon icon={faHistory} className="text-gray-400" />
                <div>
                  <h4 className="font-medium">${topUp.amount} Top-Up</h4>
                  <p className="text-sm text-gray-500">
                    {topUp.type === 'auto' ? 'Auto Recharge' : 'Manual Top-Up'} • {topUp.date}
                  </p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded-full text-sm font-medium ${getStatusColor(topUp.status)}`}>
                {topUp.status.charAt(0).toUpperCase() + topUp.status.slice(1)}
              </span>
            </div>

            <div className="mt-2 text-sm text-gray-500">
              Balance after top-up: ${topUp.balance}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TopUpHistory; 