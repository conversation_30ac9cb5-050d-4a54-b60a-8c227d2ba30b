import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, faCalendar, faVenusMars, faPassport, faBuilding, 
  faPlaneArrival, faMapMarkerAlt, faGlobe, faIdCard, faStar,
  faEnvelope, faPhone, faHome, faCheck, faTimes, faUserShield,
  faClock, faCheckCircle, faTimesCircle, faEdit, faSave
} from '@fortawesome/free-solid-svg-icons';
import ApprovalStatus from '../../profile/ApprovalStatus';

interface CombinedProfileTabProps {
  customerData: any;
}

interface VerificationState {
  [key: string]: boolean;
}

interface AdminApprovalState {
  [key: string]: {
    status: 'pending' | 'approved' | 'rejected' | 'requires_update';
    reason?: string;
    lastUpdated?: string;
  };
}

const CombinedProfileTab: React.FC<CombinedProfileTabProps> = ({ customerData }) => {
  const [verificationState, setVerificationState] = useState<VerificationState>({
    fullName: true,
    dateOfBirth: false,
    gender: true,
    passportNumber: false,
    nationality: true,
    email: true,
    phone: false,
    currentAddress: true,
    emergencyContact: false,
    occupation: true,
    company: false,
    visaStatus: true,
    membershipLevel: true
  });

  const [adminApprovalState, setAdminApprovalState] = useState<AdminApprovalState>({});
  const [loading, setLoading] = useState(true);

  // Fetch approval status from API
  useEffect(() => {
    const fetchApprovalStatus = async () => {
      try {
        const response = await fetch(`/api/admin/customer-approval?customerId=${customerData.id || customerData.memberID}`);
        const data = await response.json();
        
        if (data.success && data.customer) {
          setAdminApprovalState(data.customer.approvalStatus || {});
        } else {
          // Default approval state if no data found
          setAdminApprovalState({
            fullName: { status: 'pending' },
            dateOfBirth: { status: 'pending' },
            gender: { status: 'pending' },
            passportNumber: { status: 'pending' },
            nationality: { status: 'pending' },
            email: { status: 'pending' },
            phone: { status: 'pending' },
            currentAddress: { status: 'pending' },
            emergencyContact: { status: 'pending' },
            occupation: { status: 'pending' },
            company: { status: 'pending' },
            visaStatus: { status: 'pending' },
            membershipLevel: { status: 'pending' }
          });
        }
      } catch (error) {
        console.error('Error fetching approval status:', error);
        // Set default state on error
        setAdminApprovalState({
          fullName: { status: 'pending' },
          dateOfBirth: { status: 'pending' },
          gender: { status: 'pending' },
          passportNumber: { status: 'pending' },
          nationality: { status: 'pending' },
          email: { status: 'pending' },
          phone: { status: 'pending' },
          currentAddress: { status: 'pending' },
          emergencyContact: { status: 'pending' },
          occupation: { status: 'pending' },
          company: { status: 'pending' },
          visaStatus: { status: 'pending' },
          membershipLevel: { status: 'pending' }
        });
      } finally {
        setLoading(false);
      }
    };

    if (customerData.id || customerData.memberID) {
      fetchApprovalStatus();
    } else {
      setLoading(false);
    }
  }, [customerData.id, customerData.memberID]);

  const [isEditMode, setIsEditMode] = useState(false);
  const [editedData, setEditedData] = useState(customerData);

  const handleEditToggle = () => {
    if (isEditMode) {
      // Cancel edit - reset data
      setEditedData(customerData);
    }
    setIsEditMode(!isEditMode);
  };

  const handleSave = () => {
    // Here you would typically send the data to your API
    console.log('Saving data:', editedData);
    setIsEditMode(false);
    // You can add API call here to save the data
  };

  const handleInputChange = (field: string, value: string) => {
    setEditedData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Chưa cập nhật';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Ngày không hợp lệ';
    }
  };

  const toggleVerification = (fieldKey: string) => {
    setVerificationState(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }));
  };



  const InfoRow: React.FC<{
    icon: any;
    label: string;
    value: string;
    color: string;
    fieldKey: string;
    isVerified?: boolean;
  }> = ({ icon, label, value, color, fieldKey, isVerified }) => {
    const adminApproval = adminApprovalState[fieldKey] || { status: 'pending' };
    
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      red: 'bg-red-50 text-red-600',
      orange: 'bg-orange-50 text-orange-600',
      gray: 'bg-gray-50 text-gray-600',
      yellow: 'bg-yellow-50 text-yellow-600'
    };

    const verified = isVerified !== undefined ? isVerified : verificationState[fieldKey];

    return (
      <>
        {/* Desktop Table Row */}
        <tr className="hidden md:table-row border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150">
          <td className="py-4 pl-6 pr-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${colorClasses[color as keyof typeof colorClasses]}`}>
              <FontAwesomeIcon icon={icon} className="text-base" />
            </div>
          </td>
          <td className="py-4 px-3 font-medium text-gray-700 text-base">{label}</td>
          <td className="py-4 px-3 text-gray-900 text-base">
            {isEditMode ? (
              <input
                type="text"
                value={editedData[fieldKey] || value || ''}
                onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={label}
              />
            ) : (
              value || <span className="text-gray-400 font-normal italic">Chưa cập nhật</span>
            )}
          </td>
          <td className="py-4 px-3 text-center">
            <button
              onClick={() => toggleVerification(fieldKey)}
              className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
                verified 
                  ? 'bg-green-100 text-green-600 hover:bg-green-200' 
                  : 'bg-red-100 text-red-600 hover:bg-red-200'
              }`}
              title={verified ? 'Đã xác thực' : 'Chưa xác thực'}
            >
              <FontAwesomeIcon 
                icon={verified ? faCheck : faTimes} 
                className="text-sm" 
              />
            </button>
          </td>
          <td className="py-4 px-6 pr-6 text-center">
            <ApprovalStatus 
              status={adminApproval.status}
              reason={adminApproval.reason}
              section={fieldKey}
              lastUpdated={adminApproval.lastUpdated}
            />
          </td>
        </tr>

        {/* Mobile Card Layout */}
        <div className="md:hidden bg-white border border-gray-200 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow duration-150">
          <div className="flex items-start space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${colorClasses[color as keyof typeof colorClasses]}`}>
              <FontAwesomeIcon icon={icon} className="text-base" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h3 className="font-medium text-gray-700 text-base">{label}</h3>
              </div>
              <div className="text-gray-900 text-base break-words">
                {isEditMode ? (
                  <input
                    type="text"
                    value={editedData[fieldKey] || value || ''}
                    onChange={(e) => handleInputChange(fieldKey, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    placeholder={label}
                  />
                ) : (
                  value || <span className="text-gray-400 font-normal italic">Chưa cập nhật</span>
                )}
              </div>
              <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => toggleVerification(fieldKey)}
                    className={`flex items-center space-x-1 text-xs font-medium ${
                      verified 
                        ? 'text-green-600' 
                        : 'text-red-600'
                    }`}
                  >
                    <FontAwesomeIcon 
                      icon={verified ? faCheck : faTimes} 
                      className="text-xs" 
                    />
                    <span>{verified ? 'Đã xác thực' : 'Chưa xác thực'}</span>
                  </button>
                </div>
                <div className="flex justify-end">
                  <ApprovalStatus 
                    status={adminApproval.status}
                    reason={adminApproval.reason}
                    section={fieldKey}
                    lastUpdated={adminApproval.lastUpdated}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  const TravelHistoryRow: React.FC<{ trip: any; index: number }> = ({ trip, index }) => (
    <tr className="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150">
      <td className="py-3 pl-4 pr-2">
        <div className="w-8 h-8 bg-blue-50 text-blue-600 rounded-lg flex items-center justify-center">
          <FontAwesomeIcon icon={faMapMarkerAlt} className="text-sm" />
        </div>
      </td>
      <td className="py-3 px-2">
        <div className="font-medium text-gray-900">{trip.destination}</div>
        <div className="text-xs text-gray-500 mt-1">
          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 mr-2">
            {trip.purpose === 'business' ? 'Công tác' : 
             trip.purpose === 'vacation' ? 'Du lịch' : 
             trip.purpose === 'study' ? 'Học tập' :
             trip.purpose === 'medical' ? 'Y tế' : trip.purpose || 'Khác'}
          </span>
        </div>
      </td>
      <td className="py-3 px-2 text-sm text-gray-600">
        {formatDate(trip.departureDate)}
      </td>
      <td className="py-3 px-2 text-sm text-gray-600">
        {trip.entryDate ? formatDate(trip.entryDate) : '-'}
      </td>
      <td className="py-3 px-2 text-sm text-gray-600">
        {trip.duration || '-'}
      </td>
    </tr>
  );

  const personalInfo = [
    {
      icon: faUser,
      label: "Họ và Tên",
      value: customerData.personalDetails?.name || customerData.name,
      color: "blue",
      fieldKey: "fullName"
    },
    {
      icon: faCalendar,
      label: "Ngày Sinh",
      value: formatDate(customerData.personalDetails?.dateOfBirth),
      color: "green",
      fieldKey: "dateOfBirth"
    },
    {
      icon: faVenusMars,
      label: "Giới Tính",
      value: customerData.personalDetails?.gender === 'male' ? 'Nam' : 
             customerData.personalDetails?.gender === 'female' ? 'Nữ' : 
             customerData.personalDetails?.gender === 'other' ? 'Khác' : 'Nam',
      color: "red",
      fieldKey: "gender"
    },
    {
      icon: faPassport,
      label: "Số Hộ Chiếu",
      value: customerData.personalDetails?.passportNumber,
      color: "orange",
      fieldKey: "passportNumber"
    },
    {
      icon: faGlobe,
      label: "Quốc Tịch",
      value: customerData.citizenship?.nationality || 'Việt Nam',
      color: "gray",
      fieldKey: "nationality"
    },
    {
      icon: faEnvelope,
      label: "Email",
      value: customerData.contactInfo?.email,
      color: "blue",
      fieldKey: "email"
    },
    {
      icon: faPhone,
      label: "Số Điện Thoại",
      value: customerData.contactInfo?.phone,
      color: "green",
      fieldKey: "phone"
    },
    {
      icon: faHome,
      label: "Địa Chỉ Hiện Tại",
      value: customerData.contactInfo?.currentAddress,
      color: "red",
      fieldKey: "currentAddress"
    },
    {
      icon: faUserShield,
      label: "Liên Hệ Khẩn Cấp",
      value: customerData.contactInfo?.emergencyContact,
      color: "orange",
      fieldKey: "emergencyContact"
    },
    {
      icon: faBuilding,
      label: "Nghề Nghiệp",
      value: customerData.professionalInfo?.occupation,
      color: "gray",
      fieldKey: "occupation"
    },
    {
      icon: faBuilding,
      label: "Công Ty",
      value: customerData.professionalInfo?.company,
      color: "blue",
      fieldKey: "company"
    },
    {
      icon: faPassport,
      label: "Tình Trạng Visa",
      value: customerData.citizenship?.visaStatus === 'valid' ? 'Còn hiệu lực' : 
             customerData.citizenship?.visaStatus === 'expired' ? 'Hết hạn' : 
             customerData.citizenship?.visaStatus === 'pending' ? 'Đang xử lý' : 
             'Còn hiệu lực',
      color: "yellow",
      fieldKey: "visaStatus"
    },
    {
      icon: faIdCard,
      label: "Tình Trạng Cư Trú",
      value: customerData.citizenship?.residencyStatus === 'citizen' ? 'Công dân' : 
             customerData.citizenship?.residencyStatus === 'permanent_resident' ? 'Thường trú nhân' : 
             customerData.citizenship?.residencyStatus === 'temporary_resident' ? 'Tạm trú' : 
             customerData.citizenship?.residencyStatus === 'tourist' ? 'Du lịch' : 
             'Công dân',
      color: "green",
      fieldKey: "residencyStatus"
    },
    {
      icon: faStar,
      label: "Hạng Thành Viên",
      value: customerData.membershipInfo?.membershipLevel || 'Cơ bản',
      color: "orange",
      fieldKey: "membershipLevel"
    },
    {
      icon: faUser,
      label: "Thành Viên Từ",
      value: formatDate(customerData.membershipInfo?.memberSince),
      color: "orange",
      fieldKey: "memberSince"
    },
    {
      icon: faPassport,
      label: "Ngày Nhập Cảnh",
      value: formatDate(customerData.entryDate),
      color: "yellow",
      fieldKey: "entryDate"
    },
    {
      icon: faIdCard,
      label: "Mã Thành Viên",
      value: customerData.memberID,
      color: "blue",
      fieldKey: "memberID"
    }
  ];

  return (
    <div className="space-y-6 sm:space-y-8 max-w-6xl mx-auto">
      {/* Header */}
      <div className="text-center sm:text-left bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 sm:p-8">
        <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
          <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white shadow-lg">
            <FontAwesomeIcon icon={faUser} className="text-2xl sm:text-3xl" />
          </div>
          <div className="flex-1 text-center sm:text-left">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                  {customerData.personalDetails?.name || customerData.name || 'Khách hàng'}
                </h1>
                <p className="text-gray-600 text-base sm:text-lg">Hồ sơ khách hàng tổng hợp</p>
                <div className="mt-3 flex flex-wrap justify-center sm:justify-start gap-2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <FontAwesomeIcon icon={faIdCard} className="mr-1" />
                    {customerData.memberID || 'N/A'}
                  </span>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <FontAwesomeIcon icon={faStar} className="mr-1" />
                    {customerData.membershipInfo?.membershipLevel || 'Cơ bản'}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-2 mt-4 sm:mt-0">
                {isEditMode ? (
                  <>
                    <button
                      onClick={handleSave}
                      className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
                    >
                      <FontAwesomeIcon icon={faSave} className="mr-2" />
                      Lưu
                    </button>
                    <button
                      onClick={handleEditToggle}
                      className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
                    >
                      <FontAwesomeIcon icon={faTimes} className="mr-2" />
                      Hủy
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleEditToggle}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                  >
                    <FontAwesomeIcon icon={faEdit} className="mr-2" />
                    Chỉnh sửa
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Personal Information Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {/* Desktop Table */}
        <div className="hidden md:block overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="py-3 pl-6 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Biểu tượng</th>
                <th className="py-3 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thông tin</th>
                <th className="py-3 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giá trị</th>
                <th className="py-3 px-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Xác thực</th>
                <th className="py-3 px-6 pr-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Admin duyệt</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {personalInfo.map((info, index) => (
                <InfoRow key={index} {...info} />
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Cards */}
        <div className="md:hidden p-4 space-y-3">
          {personalInfo.map((info, index) => (
            <InfoRow key={index} {...info} />
          ))}
        </div>
      </div>

      {/* Travel History */}
      {customerData.travelHistory && customerData.travelHistory.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              Lịch Sử Du Lịch
            </h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="py-3 pl-4 pr-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Điểm đến</th>
                  <th className="py-3 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thông tin</th>
                  <th className="py-3 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày khởi hành</th>
                  <th className="py-3 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày nhập cảnh</th>
                  <th className="py-3 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {customerData.travelHistory.map((trip: any, index: number) => (
                  <TravelHistoryRow key={index} trip={trip} index={index} />
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default CombinedProfileTab;