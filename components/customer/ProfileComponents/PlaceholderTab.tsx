import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faEnvelope, faPlaneArrival, faGlobe, faUsers, faHeart, 
  faCreditCard, faStar, faFile, faCog
} from '@fortawesome/free-solid-svg-icons';

interface PlaceholderTabProps {
  tabName: string;
}

const PlaceholderTab: React.FC<PlaceholderTabProps> = ({ tabName }) => {
  const getTabIcon = (tab: string) => {
    const iconMap: { [key: string]: any } = {
      contact: faEnvelope,
      travel: faPlaneArrival,
      citizenship: faGlobe,
      family: faUsers,
      health: faHeart,
      finance: faCreditCard,
      preferences: faStar,
      documents: faFile,
    };
    return iconMap[tab] || faCog;
  };

  const getTabTitle = (tab: string) => {
    const titleMap: { [key: string]: string } = {
      contact: '<PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> chỉ',
      travel: 'Lịch sử du lịch',
      /* citizenship: 'Thông tin quốc tịch',
      family: 'Thành viên gia đình',
      health: 'Thông tin sức khỏe',
      finance: 'Thông tin tài chính',
      preferences: 'Tùy chọn cá nhân',
      documents: 'Tài liệu & Chứng từ', */
    };
    return titleMap[tab] || 'Nội dung tab';
  };

  const getTabDescription = (tab: string) => {
    const descMap: { [key: string]: string } = {
      contact: 'Quản lý thông tin liên hệ, địa chỉ và mạng xã hội',
      travel: 'Theo dõi lịch sử du lịch và các chuyến đi',
      /* citizenship: 'Thông tin về quốc tịch và tình trạng cư trú',
      family: 'Quản lý thông tin các thành viên trong gia đình',
      health: 'Thông tin sức khỏe, bảo hiểm và liên hệ khẩn cấp',
      finance: 'Thông tin tài chính và các tài khoản ngân hàng',
      preferences: 'Cài đặt ngôn ngữ, tiền tệ và tùy chọn liên lạc',
      documents: 'Quản lý các tài liệu và chứng từ quan trọng', */
    };
    return descMap[tab] || 'Nội dung của tab này đang được phát triển';
  };

  const getGradientColor = (tab: string) => {
    const colorMap: { [key: string]: string } = {
      contact: 'from-green-400 to-emerald-500',
      travel: 'from-purple-400 to-indigo-500', 
      citizenship: 'from-blue-400 to-cyan-500',
      family: 'from-pink-400 to-rose-500',
      health: 'from-red-400 to-pink-500',
      finance: 'from-yellow-400 to-orange-500',
      preferences: 'from-indigo-400 to-purple-500',
      documents: 'from-gray-400 to-slate-500',
    };
    return colorMap[tab] || 'from-blue-400 to-indigo-500';
  };

  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center max-w-md mx-auto">
        {/* Animated Icon */}
        <div className={`w-24 h-24 bg-gradient-to-br ${getGradientColor(tabName)} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg transform transition-all duration-500 hover:scale-110 hover:rotate-3`}>
          <FontAwesomeIcon 
            icon={getTabIcon(tabName)} 
            className="text-3xl text-white animate-pulse" 
          />
        </div>

        {/* Title */}
        <h3 className="text-2xl font-bold text-gray-800 mb-3">
          {getTabTitle(tabName)}
        </h3>

        {/* Description */}
        <p className="text-gray-600 mb-6 leading-relaxed">
          {getTabDescription(tabName)}
        </p>

        {/* Coming Soon Badge */}
        <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full">
          <div className="w-2 h-2 bg-indigo-500 rounded-full mr-2 animate-pulse"></div>
          <span className="text-sm font-medium text-indigo-700">Đang phát triển</span>
        </div>

        {/* Progress Indicator */}
        <div className="mt-8">
          <div className="flex justify-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          <p className="text-xs text-gray-500">Tính năng này sẽ có sẵn trong bản cập nhật tiếp theo</p>
        </div>
      </div>
    </div>
  );
};

export default PlaceholderTab;