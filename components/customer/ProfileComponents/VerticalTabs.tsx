import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, faEnvelope, faPlaneArrival, faGlobe, faUsers, 
  faHeart, faCreditCard, faStar, faFile, faChevronLeft, faChevronRight 
} from '@fortawesome/free-solid-svg-icons';

interface VerticalTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const VerticalTabs: React.FC<VerticalTabsProps> = ({ 
  activeTab, 
  onTabChange, 
  isCollapsed = false, 
  onToggleCollapse 
}) => {
  const tabs = [
    { id: 'personal', label: 'Thông tin cá nhân', icon: faUser, color: 'blue' },
    { id: 'contact', label: '<PERSON><PERSON><PERSON> <PERSON><PERSON> & <PERSON>ịa chỉ', icon: faEnvelope, color: 'green' },
    { id: 'travel', label: '<PERSON>ịch sử du lịch', icon: faPlaneArrival, color: 'purple' },
    /* { id: 'citizenship', label: 'Quốc tịch', icon: faGlobe, color: 'indigo' },
    { id: 'family', label: 'Gia đình', icon: faUsers, color: 'pink' },
    { id: 'health', label: 'Sức khỏe', icon: faHeart, color: 'red' },
    { id: 'finance', label: 'Tài chính', icon: faCreditCard, color: 'yellow' },
    { id: 'preferences', label: 'Tùy chọn', icon: faStar, color: 'orange' },
    { id: 'documents', label: 'Tài liệu', icon: faFile, color: 'gray' }, */
  ];

  const getTabStyles = (tab: any) => {
    const isActive = activeTab === tab.id;
    
    const colorClasses = {
      blue: isActive ? 'bg-blue-50 border-l-4 border-blue-500 text-blue-600' : 'hover:bg-blue-50 hover:text-blue-600',
      green: isActive ? 'bg-green-50 border-l-4 border-green-500 text-green-600' : 'hover:bg-green-50 hover:text-green-600',
      purple: isActive ? 'bg-purple-50 border-l-4 border-purple-500 text-purple-600' : 'hover:bg-purple-50 hover:text-purple-600',
      indigo: isActive ? 'bg-indigo-50 border-l-4 border-indigo-500 text-indigo-600' : 'hover:bg-indigo-50 hover:text-indigo-600',
      pink: isActive ? 'bg-pink-50 border-l-4 border-pink-500 text-pink-600' : 'hover:bg-pink-50 hover:text-pink-600',
      red: isActive ? 'bg-red-50 border-l-4 border-red-500 text-red-600' : 'hover:bg-red-50 hover:text-red-600',
      yellow: isActive ? 'bg-yellow-50 border-l-4 border-yellow-500 text-yellow-600' : 'hover:bg-yellow-50 hover:text-yellow-600',
      orange: isActive ? 'bg-orange-50 border-l-4 border-orange-500 text-orange-600' : 'hover:bg-orange-50 hover:text-orange-600',
      gray: isActive ? 'bg-gray-50 border-l-4 border-gray-500 text-gray-600' : 'hover:bg-gray-50 hover:text-gray-600',
    };

    return `${colorClasses[tab.color as keyof typeof colorClasses]} ${
      isActive ? '' : 'text-gray-600 border-l-4 border-transparent'
    }`;
  };

  return (
    <div className={`bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    }`}>
      {/* Header with Toggle Button */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        {!isCollapsed && (
          <h2 className="text-lg font-semibold text-gray-800">Menu</h2>
        )}
        {onToggleCollapse && (
          <button
            onClick={onToggleCollapse}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200 text-gray-500 hover:text-gray-700"
            title={isCollapsed ? 'Mở rộng menu' : 'Thu gọn menu'}
          >
            <FontAwesomeIcon 
              icon={isCollapsed ? faChevronRight : faChevronLeft} 
              className="h-4 w-4"
            />
          </button>
        )}
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 py-2">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            className={`${getTabStyles(tab)} w-full text-left px-4 py-3 transition-all duration-200 flex items-center group relative overflow-hidden ${
              isCollapsed ? 'justify-center' : ''
            }`}
            onClick={() => onTabChange(tab.id)}
            title={isCollapsed ? tab.label : ''}
          >
            {/* Background animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            
            <div className={`relative z-10 flex items-center ${isCollapsed ? 'justify-center' : ''}`}>
              <FontAwesomeIcon 
                icon={tab.icon} 
                className={`text-sm transition-transform duration-200 ${
                  activeTab === tab.id ? 'scale-110' : 'group-hover:scale-110'
                } ${isCollapsed ? '' : 'mr-3'}`} 
              />
              {!isCollapsed && (
                <span className="font-medium text-sm">
                  {tab.label}
                </span>
              )}
            </div>
          </button>
        ))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className={`text-xs text-gray-500 ${isCollapsed ? 'text-center' : 'text-center'}`}>
          {isCollapsed ? '📋' : 'Hồ sơ khách hàng'}
        </div>
      </div>
    </div>
  );
};

export default VerticalTabs;