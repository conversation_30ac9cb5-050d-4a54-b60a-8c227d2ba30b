import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faTimesCircle } from '@fortawesome/free-solid-svg-icons';

interface MessageAlertProps {
  message: { text: string; type: string };
  onClose?: () => void;
}

const MessageAlert: React.FC<MessageAlertProps> = ({ message, onClose }) => (
  <div className={`mb-6 p-4 text-sm rounded-xl flex items-center shadow-sm border-l-4 ${
    message.type === 'success' 
      ? 'bg-emerald-50 text-emerald-800 border-emerald-400' 
      : 'bg-red-50 text-red-800 border-red-400'
  } transition-all duration-300 transform hover:scale-[1.02]`}>
    <div className={`w-10 h-10 flex-shrink-0 rounded-full flex items-center justify-center mr-4 ${
      message.type === 'success' ? 'bg-emerald-100' : 'bg-red-100'
    }`}>
      <FontAwesomeIcon 
        icon={message.type === 'success' ? faCheckCircle : faTimesCircle} 
        className={`${message.type === 'success' ? 'text-emerald-600' : 'text-red-600'} text-lg`} 
      />
    </div>
    <div className="flex-1">
      <p className="font-medium">{message.text}</p>
    </div>
    {onClose && (
      <button 
        onClick={onClose}
        className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
      >
        <FontAwesomeIcon icon={faTimesCircle} />
      </button>
    )}
  </div>
);

export default MessageAlert; 