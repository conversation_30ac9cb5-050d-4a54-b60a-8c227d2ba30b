import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, faCalendar, faVenusMars, faPassport, faBuilding, 
  faPlaneArrival, faMapMarkerAlt, faGlobe, faIdCard, faStar
} from '@fortawesome/free-solid-svg-icons';

interface PersonalInfoTabProps {
  customerData: any;
}

const PersonalInfoTab: React.FC<PersonalInfoTabProps> = ({ customerData }) => {
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Chưa cập nhật';
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch {
      return 'Định dạng không hợp lệ';
    }
  };

  const InfoRow: React.FC<{
    icon: any;
    label: string;
    value: any;
    color?: string;
  }> = ({ icon, label, value, color = 'blue' }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      purple: 'bg-purple-50 text-purple-600',
      indigo: 'bg-indigo-50 text-indigo-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      green: 'bg-green-50 text-green-600',
      red: 'bg-red-50 text-red-600',
      orange: 'bg-orange-50 text-orange-600',
      gray: 'bg-gray-50 text-gray-600'
    };

    return (
      <>
        {/* Desktop Table Row */}
        <tr className="hidden md:table-row border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150">
          <td className="py-4 pl-6 pr-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${colorClasses[color as keyof typeof colorClasses]}`}>
              <FontAwesomeIcon icon={icon} className="text-base" />
            </div>
          </td>
          <td className="py-4 px-3 font-medium text-gray-700 text-base">{label}</td>
          <td className="py-4 px-6 text-gray-900 text-base">
            {value || <span className="text-gray-400 font-normal italic">Chưa cập nhật</span>}
          </td>
        </tr>

        {/* Mobile Card Layout */}
        <div className="md:hidden bg-white border border-gray-200 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow duration-150">
          <div className="flex items-start space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${colorClasses[color as keyof typeof colorClasses]}`}>
              <FontAwesomeIcon icon={icon} className="text-base" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-gray-700 text-sm mb-1">{label}</div>
              <div className="text-gray-900 text-base break-words">
                {value || <span className="text-gray-400 font-normal italic">Chưa cập nhật</span>}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  const TravelHistoryRow: React.FC<{ trip: any; index: number }> = ({ trip, index }) => (
    <tr className="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150">
      <td className="py-3 pl-4 pr-2">
        <div className="w-8 h-8 bg-blue-50 text-blue-600 rounded-lg flex items-center justify-center">
          <FontAwesomeIcon icon={faMapMarkerAlt} className="text-sm" />
        </div>
      </td>
      <td className="py-3 px-2">
        <div className="font-medium text-gray-900">{trip.destination}</div>
        <div className="text-xs text-gray-500 mt-1">
          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 mr-2">
            {trip.purpose === 'business' ? 'Công tác' : 
             trip.purpose === 'vacation' ? 'Du lịch' : 
             trip.purpose === 'study' ? 'Học tập' :
             trip.purpose === 'medical' ? 'Y tế' : trip.purpose || 'Khác'}
          </span>
          {trip.duration && (
            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-50 text-gray-700">
              {trip.duration} ngày
            </span>
          )}
        </div>
      </td>
      <td className="py-3 px-4">
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faCalendar} className="text-gray-400 mr-2 w-4" />
            <span>
              {trip.arrivalDate && trip.departureDate 
                ? `${formatDate(trip.arrivalDate)} - ${formatDate(trip.departureDate)}`
                : 'Chưa có thông tin ngày'
              }
            </span>
          </div>
          
          {trip.visaNumber && (
            <div className="flex items-center">
              <FontAwesomeIcon icon={faPassport} className="text-gray-400 mr-2 w-4" />
              <span><strong>Visa:</strong> {trip.visaNumber}</span>
            </div>
          )}
          
          {trip.entryDate && (
            <div className="flex items-center">
              <FontAwesomeIcon icon={faPlaneArrival} className="text-gray-400 mr-2 w-4" />
              <span><strong>Ngày nhập cảnh:</strong> {formatDate(trip.entryDate)}</span>
            </div>
          )}
        </div>
      </td>
    </tr>
  );

  const personalInfo = [
    { 
      icon: faUser, 
      label: 'Họ và Tên', 
      value: customerData.personalDetails?.name || customerData.name,
      color: 'blue'
    },
    { 
      icon: faCalendar, 
      label: 'Ngày Sinh', 
      value: formatDate(customerData.personalDetails?.dateOfBirth || customerData.dateOfBirth),
      color: 'purple'
    },
    { 
      icon: faVenusMars, 
      label: 'Giới Tính', 
      value: customerData.personalDetails?.gender === 'male' || customerData.gender === 'male' ? 'Nam' : 
             customerData.personalDetails?.gender === 'female' || customerData.gender === 'female' ? 'Nữ' : 
             customerData.personalDetails?.gender || customerData.gender,
      color: 'indigo'
    },
    { 
      icon: faPassport, 
      label: 'Số Hộ Chiếu', 
      value: customerData.passportNumber,
      color: 'yellow'
    },
    { 
      icon: faBuilding, 
      label: 'Nghề Nghiệp', 
      value: customerData.personalDetails?.occupation,
      color: 'green'
    },
    { 
      icon: faBuilding, 
      label: 'Nơi Làm Việc', 
      value: customerData.personalDetails?.employer,
      color: 'red'
    },
  ];

  const additionalInfo = [
    {
      icon: faGlobe,
      label: "Quốc Tịch",
      value: customerData.citizenship?.nationality || 'Vietnamese',
      color: "green"
    },
    {
      icon: faIdCard,
      label: "Tình Trạng Cư Trú",
      value: customerData.citizenship?.residencyStatus === 'citizen' ? 'Công dân' : 
             customerData.citizenship?.residencyStatus === 'permanent_resident' ? 'Thường trú nhân' : 
             customerData.citizenship?.residencyStatus === 'temporary_resident' ? 'Tạm trú' : 
             customerData.citizenship?.residencyStatus === 'tourist' ? 'Du lịch' : 
             'Công dân',
      color: "green"
    },
    {
      icon: faStar,
      label: "Hạng Thành Viên",
      value: customerData.membershipInfo?.membershipLevel || 'Cơ bản',
      color: "orange"
    },
    {
      icon: faUser,
      label: "Thành Viên Từ",
      value: formatDate(customerData.membershipInfo?.memberSince),
      color: "orange"
    },
    {
      icon: faPassport,
      label: "Ngày Nhập Cảnh",
      value: formatDate(customerData.entryDate),
      color: "yellow"
    },
    {
      icon: faIdCard,
      label: "Mã Thành Viên",
      value: customerData.memberID,
      color: "blue"
    }
  ];

  return (
    <div className="space-y-6 sm:space-y-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center sm:text-left bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 sm:p-8">
        <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
          <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white shadow-lg">
            <FontAwesomeIcon icon={faUser} className="text-2xl sm:text-3xl" />
          </div>
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
              {customerData.personalDetails?.name || customerData.name || 'Khách hàng'}
            </h1>
            <p className="text-gray-600 text-base sm:text-lg">Thông tin hồ sơ chi tiết</p>
            <div className="mt-3 flex flex-wrap justify-center sm:justify-start gap-2">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Thành viên
              </span>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Đã xác thực
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Personal Information Section */}
      <div>
        <div className="flex items-center mb-6">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
            <FontAwesomeIcon icon={faUser} className="text-blue-600 text-lg" />
          </div>
          <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">Thông Tin Cá Nhân</h2>
        </div>

        {/* Desktop Table */}
        <div className="hidden md:block overflow-hidden rounded-xl border border-gray-200 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200">
            <tbody className="bg-white divide-y divide-gray-100">
              {personalInfo.map((info, index) => (
                <InfoRow
                  key={index}
                  icon={info.icon}
                  label={info.label}
                  value={info.value}
                  color={info.color}
                />
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Cards */}
        <div className="md:hidden space-y-3">
          {personalInfo.map((info, index) => (
            <InfoRow
              key={index}
              icon={info.icon}
              label={info.label}
              value={info.value}
              color={info.color}
            />
          ))}
        </div>
      </div>
      
      {/* Travel History Section */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
              <FontAwesomeIcon icon={faPlaneArrival} className="text-purple-600 text-lg" />
            </div>
            <div>
              <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">Lịch Sử Du Lịch</h2>
              <p className="text-sm text-gray-500">
                {customerData.travelHistory?.length || 0} chuyến đi đã thực hiện
              </p>
            </div>
          </div>
        </div>

        {customerData.travelHistory && customerData.travelHistory.length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block overflow-hidden rounded-xl border border-gray-200 shadow-sm">
              <table className="min-w-full divide-y divide-gray-200">
                <tbody className="bg-white divide-y divide-gray-100">
                  {customerData.travelHistory.map((trip: any, index: number) => (
                    <TravelHistoryRow key={`trip-${index}`} trip={trip} index={index} />
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {customerData.travelHistory.map((trip: any, index: number) => (
                <div key={`trip-mobile-${index}`} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-purple-50 text-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <FontAwesomeIcon icon={faMapMarkerAlt} className="text-base" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-semibold text-gray-900 text-lg mb-2">{trip.destination}</div>
                      <div className="space-y-2">
                        <div className="flex flex-wrap gap-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700">
                            {trip.purpose === 'business' ? 'Công tác' :
                             trip.purpose === 'vacation' ? 'Du lịch' :
                             trip.purpose === 'study' ? 'Học tập' :
                             trip.purpose === 'medical' ? 'Y tế' : trip.purpose || 'Khác'}
                          </span>
                          {trip.duration && (
                            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-50 text-gray-700">
                              {trip.duration} ngày
                            </span>
                          )}
                        </div>
                        <div className="space-y-1 text-sm text-gray-600">
                          <div className="flex items-center">
                            <FontAwesomeIcon icon={faCalendar} className="text-gray-400 mr-2 w-4" />
                            <span>
                              {trip.arrivalDate && trip.departureDate
                                ? `${formatDate(trip.arrivalDate)} - ${formatDate(trip.departureDate)}`
                                : 'Chưa có thông tin ngày'
                              }
                            </span>
                          </div>
                          {trip.visaNumber && (
                            <div className="flex items-center">
                              <FontAwesomeIcon icon={faPassport} className="text-gray-400 mr-2 w-4" />
                              <span><strong>Visa:</strong> {trip.visaNumber}</span>
                            </div>
                          )}
                          {trip.entryDate && (
                            <div className="flex items-center">
                              <FontAwesomeIcon icon={faPlaneArrival} className="text-gray-400 mr-2 w-4" />
                              <span><strong>Ngày nhập cảnh:</strong> {formatDate(trip.entryDate)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <FontAwesomeIcon icon={faPlaneArrival} className="text-2xl text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-600 mb-2">Chưa có lịch sử du lịch</h3>
            <p className="text-sm text-gray-500 max-w-md mx-auto">
              Thông tin về các chuyến đi của bạn sẽ xuất hiện ở đây khi có dữ liệu
            </p>
          </div>
        )}
      </div>

      {/* Additional Information Section */}
      <div>
        <div className="flex items-center mb-6">
          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
            <FontAwesomeIcon icon={faGlobe} className="text-green-600 text-lg" />
          </div>
          <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">Thông Tin Bổ Sung</h2>
        </div>

        {/* Desktop Table */}
        <div className="hidden md:block overflow-hidden rounded-xl border border-gray-200 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200">
            <tbody className="bg-white divide-y divide-gray-100">
              {additionalInfo.map((info, index) => (
                <InfoRow
                  key={index}
                  icon={info.icon}
                  label={info.label}
                  value={info.value}
                  color={info.color}
                />
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Cards */}
        <div className="md:hidden space-y-3">
          {additionalInfo.map((info, index) => (
            <InfoRow
              key={index}
              icon={info.icon}
              label={info.label}
              value={info.value}
              color={info.color}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoTab; 
