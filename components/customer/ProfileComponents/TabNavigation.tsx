import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, faEnvelope, faPlaneArrival, faGlobe, faUsers, 
  faHeart, faCreditCard, faStar, faFile 
} from '@fortawesome/free-solid-svg-icons';

interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'personal', label: 'Thông tin cá nhân', icon: faUser, color: 'blue' },
    { id: 'contact', label: '<PERSON><PERSON>n hệ & Địa chỉ', icon: faEnvelope, color: 'green' },
    { id: 'travel', label: 'Lịch sử du lịch', icon: faPlaneArrival, color: 'purple' },
    /* { id: 'citizenship', label: '<PERSON>uốc tịch', icon: faGlobe, color: 'indigo' },
    { id: 'family', label: '<PERSON><PERSON> đình', icon: faUsers, color: 'pink' },
    { id: 'health', label: 'Sức khỏe', icon: faHeart, color: 'red' },
    { id: 'finance', label: 'Tài chính', icon: faCreditCard, color: 'yellow' },
    { id: 'preferences', label: 'Tùy chọn', icon: faStar, color: 'orange' },
    { id: 'documents', label: 'Tài liệu', icon: faFile, color: 'gray' }, */
  ];

  const getTabStyles = (tab: any) => {
    const isActive = activeTab === tab.id;
    
    const colorClasses = {
      blue: isActive ? 'bg-blue-50 border-blue-500 text-blue-600' : 'hover:bg-blue-50 hover:text-blue-600',
      green: isActive ? 'bg-green-50 border-green-500 text-green-600' : 'hover:bg-green-50 hover:text-green-600',
      purple: isActive ? 'bg-purple-50 border-purple-500 text-purple-600' : 'hover:bg-purple-50 hover:text-purple-600',
      indigo: isActive ? 'bg-indigo-50 border-indigo-500 text-indigo-600' : 'hover:bg-indigo-50 hover:text-indigo-600',
      pink: isActive ? 'bg-pink-50 border-pink-500 text-pink-600' : 'hover:bg-pink-50 hover:text-pink-600',
      red: isActive ? 'bg-red-50 border-red-500 text-red-600' : 'hover:bg-red-50 hover:text-red-600',
      yellow: isActive ? 'bg-yellow-50 border-yellow-500 text-yellow-600' : 'hover:bg-yellow-50 hover:text-yellow-600',
      orange: isActive ? 'bg-orange-50 border-orange-500 text-orange-600' : 'hover:bg-orange-50 hover:text-orange-600',
      gray: isActive ? 'bg-gray-50 border-gray-500 text-gray-600' : 'hover:bg-gray-50 hover:text-gray-600',
    };

    return `${isActive ? 'border-b-2' : 'border-transparent border-b-2'} ${
      colorClasses[tab.color as keyof typeof colorClasses]
    } ${isActive ? '' : 'text-gray-500'}`;
  };

  return (
    <div className="border-b border-gray-200 mb-8">
      <nav className="-mb-px flex space-x-2 overflow-x-auto scrollbar-hide pb-2">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            className={`${getTabStyles(tab)} whitespace-nowrap py-3 px-4 font-medium text-sm transition-all duration-300 rounded-t-lg flex items-center group relative overflow-hidden`}
            onClick={() => onTabChange(tab.id)}
          >
            {/* Background animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            
            <div className="relative z-10 flex items-center">
              <FontAwesomeIcon 
                icon={tab.icon} 
                className={`mr-2 text-sm transition-transform duration-300 ${
                  activeTab === tab.id ? 'scale-110' : 'group-hover:scale-110'
                }`} 
              />
              <span className="font-medium">{tab.label}</span>
            </div>
            
            {/* Active indicator */}
            {activeTab === tab.id && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-current to-transparent"></div>
            )}
          </button>
        ))}
      </nav>
    </div>
  );
};

export default TabNavigation;