import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface InfoCardProps {
  icon: any;
  label: string;
  value: string;
  iconColor?: string;
  onClick?: () => void;
}

const InfoCard: React.FC<InfoCardProps> = ({ 
  icon, 
  label, 
  value, 
  iconColor = 'blue',
  onClick 
}) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600 group-hover:bg-blue-200',
    purple: 'bg-purple-100 text-purple-600 group-hover:bg-purple-200',
    indigo: 'bg-indigo-100 text-indigo-600 group-hover:bg-indigo-200',
    green: 'bg-green-100 text-green-600 group-hover:bg-green-200',
    yellow: 'bg-yellow-100 text-yellow-600 group-hover:bg-yellow-200',
    red: 'bg-red-100 text-red-600 group-hover:bg-red-200',
    pink: 'bg-pink-100 text-pink-600 group-hover:bg-pink-200',
    orange: 'bg-orange-100 text-orange-600 group-hover:bg-orange-200',
    gray: 'bg-gray-100 text-gray-600 group-hover:bg-gray-200',
    emerald: 'bg-emerald-100 text-emerald-600 group-hover:bg-emerald-200',
    teal: 'bg-teal-100 text-teal-600 group-hover:bg-teal-200',
  };

  return (
    <div 
      className={`group bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-lg hover:border-indigo-200 transition-all duration-300 transform hover:-translate-y-2 ${
        onClick ? 'cursor-pointer' : ''
      } relative overflow-hidden`}
      onClick={onClick}
    >
      {/* Background gradient on hover */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-gray-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      
      <div className="relative z-10 flex items-start space-x-4">
        <div className={`w-12 h-12 ${colorClasses[iconColor as keyof typeof colorClasses]} rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-sm`}>
          <FontAwesomeIcon icon={icon} className="text-lg" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 group-hover:text-gray-600 transition-colors">
            {label}
          </p>
          <p className="text-lg font-bold text-gray-900 break-words group-hover:text-gray-800 transition-colors leading-tight">
            {value || (
              <span className="text-gray-400 font-normal italic">Chưa cập nhật</span>
            )}
          </p>
        </div>
      </div>
      
      {/* Hover indicator */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-400 to-purple-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
    </div>
  );
};

export default InfoCard; 