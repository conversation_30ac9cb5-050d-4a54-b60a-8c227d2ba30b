import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPencilAlt, faIdCard, faStar, faCopy } from '@fortawesome/free-solid-svg-icons';

interface ProfileHeaderProps {
  customerData: any;
  onEdit: () => void;
  onCopyId: (id: string) => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ customerData, onEdit, onCopyId }) => {
  const memberId = customerData.memberID || customerData.id || 'N/A';
  const points = customerData.membershipInfo?.loyaltyPoints || customerData.points || '0';
  const customerName = customerData.personalDetails?.name || customerData.name || 'Khách hàng';

  return (
    <div className="relative bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 rounded-2xl p-6 text-white shadow-2xl overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-white/10 backdrop-blur-3xl"></div>
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
      
      <div className="relative z-10">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              Xin chào, {customerName}
            </h1>
            <p className="text-indigo-100 opacity-90 text-lg">Quản lý và cập nhật thông tin của bạn</p>
          </div>
          <button
            onClick={onEdit}
            className="group flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm rounded-xl text-white hover:bg-white/30 transition-all duration-300 transform hover:scale-105 shadow-lg border border-white/20"
          >
            <FontAwesomeIcon icon={faPencilAlt} className="mr-2 h-4 w-4 group-hover:rotate-12 transition-transform" />
            Chỉnh sửa
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Member ID Card */}
          <div className="group bg-white/20 backdrop-blur-sm rounded-xl p-6 hover:bg-white/30 transition-all duration-300 border border-white/20">
            <div className="flex items-center">
              <div className="w-14 h-14 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                <FontAwesomeIcon icon={faIdCard} className="text-white text-xl" />
              </div>
              <div className="flex-1">
                <p className="text-xs font-medium text-white/70 uppercase tracking-wider mb-2">
                  Mã ID Thành Viên
                </p>
                <div className="flex items-center">
                  <p className="text-xl font-bold mr-3 font-mono">{memberId}</p>
                  <button 
                    onClick={() => onCopyId(memberId)}
                    className="group/copy text-white/70 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
                    title="Sao chép mã ID"
                  >
                    <FontAwesomeIcon icon={faCopy} className="text-sm group-hover/copy:scale-110 transition-transform" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Points Card */}
          <div className="group bg-white/20 backdrop-blur-sm rounded-xl p-6 hover:bg-white/30 transition-all duration-300 border border-white/20">
            <div className="flex items-center">
              <div className="w-14 h-14 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                <FontAwesomeIcon icon={faStar} className="text-white text-xl" />
              </div>
              <div>
                <p className="text-xs font-medium text-white/70 uppercase tracking-wider mb-2">
                  Điểm Tích Lũy
                </p>
                <p className="text-xl font-bold">
                  {new Intl.NumberFormat('vi-VN').format(parseInt(points))} điểm
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t border-white/20">
          <div className="text-center">
            <p className="text-2xl font-bold">
              {customerData.travelHistory?.length || 0}
            </p>
            <p className="text-xs text-white/70">Chuyến đi</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold">
              {customerData.documents?.length || 0}
            </p>
            <p className="text-xs text-white/70">Tài liệu</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold">
              {customerData.familyMembers?.length || 0}
            </p>
            <p className="text-xs text-white/70">Thành viên gia đình</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold">
              {customerData.membershipInfo?.membershipLevel || 'Cơ bản'}
            </p>
            <p className="text-xs text-white/70">Hạng thành viên</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader; 