import React, { useState, useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, faEnvelope, faPhone, faMapMarkerAlt, faCalendar, faVenusMars, 
  faPencilAlt, faLock, faPassport, faPlaneArrival, faIdCard, faStar, 
  faBuilding, faCreditCard, faUsers, faHeart, faGlobe, faFile, faUpload, 
  faTrash, faCheckCircle, faTimesCircle, faCopy, faEye, faDownload
} from '@fortawesome/free-solid-svg-icons';

// Import beautiful components
import LoadingSpinner from './ProfileComponents/LoadingSpinner';
import MessageAlert from './ProfileComponents/MessageAlert';
import ProfileHeader from './ProfileComponents/ProfileHeader';
import CombinedProfileTab from './ProfileComponents/CombinedProfileTab';

// Types and Interfaces
interface DocumentItem {
  documentType: string;
  filename?: string;
  originalFilename?: string;
  uploadDate: string;
  size?: number;
  status: string;
  path?: string;
}

interface ProfileSectionProps {}

// Enhanced Security Section Component - Fully Mobile Optimized
const SecuritySection: React.FC<{ store: string }> = ({ store }) => (
  <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-all duration-300">
    <div className="bg-gradient-to-r from-teal-500 to-emerald-600 p-3 sm:p-4 lg:p-6">
      <div className="flex flex-col space-y-3 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
        <div className="text-center sm:text-left">
          <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-1">Bảo Mật Tài Khoản</h2>
          {/* <p className="text-teal-100 text-xs sm:text-sm">Quản lý bảo mật và quyền truy cập</p> */}
        </div>
        <a
          href={`/${store}/customer/change-password`}
          className="group flex items-center justify-center px-4 py-2 sm:px-5 sm:py-2.5 lg:px-6 lg:py-3 bg-white/20 backdrop-blur-sm rounded-lg text-white hover:bg-white/30 transition-all duration-300 transform hover:scale-105 shadow-lg border border-white/20 text-sm sm:text-base font-medium"
        >
          <FontAwesomeIcon icon={faLock} className="mr-2 h-3 w-3 sm:h-4 sm:w-4 group-hover:rotate-12 transition-transform" />
          <span>Đổi mật khẩu</span>
        </a>
      </div>
    </div>
    
    {/* <div className="p-3 sm:p-4 lg:p-6">
      <div className="flex flex-col sm:flex-row items-start p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-teal-50 to-emerald-50 rounded-lg border border-teal-200">
        <div className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0 sm:mr-3 lg:mr-4">
          <FontAwesomeIcon icon={faLock} className="text-teal-600 text-sm sm:text-base lg:text-lg" />
        </div>
        <div className="text-center sm:text-left">
          <h3 className="font-semibold text-teal-800 mb-1 sm:mb-2 text-sm sm:text-base">Khuyến nghị bảo mật</h3>
          <p className="text-xs sm:text-sm text-teal-700 leading-relaxed">
            Để bảo vệ tài khoản của bạn, vui lòng không chia sẻ mật khẩu với người khác. 
            Thay đổi mật khẩu thường xuyên để tăng tính bảo mật và sử dụng mật khẩu mạnh.
          </p>
        </div>
      </div>
    </div> */}
  </div>
);

// Main Component
const ProfileSection: React.FC<ProfileSectionProps> = () => {
  const router = useRouter();
  const { store } = router.query;
  const [customerData, setCustomerData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [showEditForm, setShowEditForm] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  const [isUploading, setIsUploading] = useState(false);

  // Generate member ID based on name and phone
  const generateMemberID = (name: string, phone: string) => {
    if (!name || !phone) return '';
    const namePrefix = name.replace(/\s+/g, '').substring(0, 3).toUpperCase();
    const phoneSuffix = phone.replace(/\D/g, '').slice(-4);
    return `${namePrefix}${phoneSuffix}`;
  };

  const validationSchema = Yup.object({
    "personalDetails.name": Yup.string().required('Họ và tên là bắt buộc'),
    "contactInfo.email": Yup.string().email('Email không hợp lệ').required('Email là bắt buộc'),
    "contactInfo.primaryPhone": Yup.string().required('Số điện thoại là bắt buộc')
  });
  
  const formik = useFormik({
    initialValues: {
      id: '',
      personalDetails: {
        name: '',
        firstName: '',
        lastName: '',
        middleName: '',
        dateOfBirth: '',
        gender: '',
        maritalStatus: '',
        occupation: '',
        employer: '',
        taxId: ''
      },
      contactInfo: {
        primaryPhone: '',
        secondaryPhone: '',
        workPhone: '',
        email: '',
        alternateEmail: '',
        socialProfiles: {
          facebook: '',
          linkedin: ''
        }
      },
      addresses: [{
        street: '',
        city: '',
        district: '',
        ward: '',
        postalCode: '',
        country: 'Vietnam',
        isDefault: true,
        type: 'home'
      }],
      citizenship: {
        currentCountry: 'Vietnam',
        nationality: 'Vietnamese',
        otherNationalities: [],
        residencyStatus: 'citizen'
      },
      documents: [] as DocumentItem[],
      financialInfo: {
        bankAccounts: [],
        taxResidency: '',
        creditScore: 0
      },
      familyMembers: [],
      healthInfo: {
        bloodType: '',
        allergies: [],
        insuranceProvider: '',
        insurancePolicyNumber: '',
        emergencyContact: {
          name: '',
          relationship: '',
          phone: ''
        }
      },
      travelHistory: [],
      membershipInfo: {
        memberSince: '',
        membershipLevel: '',
        loyaltyPoints: 0,
        referralCode: ''
      },
      memberID: '',
      points: '0',
      passportNumber: '',
      entryDate: '',
      preferences: {
        language: 'Vietnamese',
        currency: 'VND',
        communicationPreferences: {
          email: true,
          sms: true,
          phone: false,
          promotions: true
        }
      },
      consent: {
        marketingConsent: true,
        dataProcessingConsent: true,
        consentDate: '',
        privacyPolicyVersion: ''
      },
      profileImage: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      if (!customerData || !customerData.id) {
        setMessage({ text: 'Không tìm thấy thông tin khách hàng', type: 'error' });
        return;
      }

      setIsLoading(true);
      setMessage({ text: '', type: '' });

      try {
        const updatedCustomer = {
          ...customerData,
          personalDetails: {
            ...customerData.personalDetails,
            ...values.personalDetails
          },
          contactInfo: {
            ...customerData.contactInfo,
            ...values.contactInfo
          },
          addresses: values.addresses,
          citizenship: values.citizenship,
          financialInfo: values.financialInfo,
          familyMembers: values.familyMembers,
          healthInfo: values.healthInfo,
          travelHistory: values.travelHistory,
          membershipInfo: {
            ...customerData.membershipInfo,
            loyaltyPoints: parseInt(values.points) || customerData.membershipInfo?.loyaltyPoints || 0
          },
          passportNumber: values.passportNumber,
          entryDate: values.entryDate,
          memberID: values.memberID || generateMemberID(values.personalDetails.name, values.contactInfo.primaryPhone),
          preferences: values.preferences,
          consent: values.consent,
          profileImage: values.profileImage,
          lastUpdated: new Date().toISOString()
        };

        const response = await fetch(`/api/customers/${customerData.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updatedCustomer)
        });

        if (response.ok) {
          const storageKey = `${store}_customerData`;
          localStorage.setItem(storageKey, JSON.stringify(updatedCustomer));
          setCustomerData(updatedCustomer);
          setMessage({ text: 'Thông tin đã được cập nhật thành công', type: 'success' });
          setShowEditForm(false);
        } else {
          const errorData = await response.json();
          setMessage({ text: errorData.message || 'Lỗi khi cập nhật thông tin', type: 'error' });
        }
      } catch (error) {
        console.error('Error updating profile:', error);
        setMessage({ text: 'Có lỗi xảy ra, vui lòng thử lại sau', type: 'error' });
      } finally {
        setIsLoading(false);
      }
    }
  });

  // Load customer data
  useEffect(() => {
    if (!store) return;

    const loadCustomerData = async () => {
      const storageKey = `${store}_customerData`;
      const data = localStorage.getItem(storageKey) || localStorage.getItem('customerData');
      
      if (data) {
        try {
          const parsedData = JSON.parse(data);
          
          if (parsedData.id) {
            try {
              const response = await fetch(`/api/customers/${parsedData.id}`);
              if (response.ok) {
                const fullCustomerData = await response.json();
                setCustomerData(fullCustomerData);
                setFormikValues(fullCustomerData);
                return;
              }
            } catch (err) {
              console.error('Error fetching customer data from API:', err);
            }
          }
          
          setCustomerData(parsedData);
          setFormikValues(parsedData);
        } catch (e) {
          console.error('Error parsing customer data:', e);
        }
      }
    };

    loadCustomerData();
  }, [store]);

  // Helper function to set formik values from customer data
  const setFormikValues = (data: any) => {
    const formikData = {
      id: data.id || '',
      personalDetails: data.personalDetails || {
        name: data.name || '',
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        middleName: data.middleName || '',
        dateOfBirth: data.dateOfBirth || '',
        gender: data.gender || '',
        maritalStatus: data.maritalStatus || '',
        occupation: data.occupation || '',
        employer: data.employer || '',
        taxId: data.taxId || ''
      },
      contactInfo: data.contactInfo || {
        primaryPhone: data.phone || '',
        secondaryPhone: '',
        workPhone: '',
        email: data.email || '',
        alternateEmail: '',
        socialProfiles: {
          facebook: '',
          linkedin: ''
        }
      },
      addresses: data.addresses || [{
        street: data.address || '',
        city: '',
        district: '',
        ward: '',
        postalCode: '',
        country: 'Vietnam',
        isDefault: true,
        type: 'home'
      }],
      citizenship: data.citizenship || {
        currentCountry: 'Vietnam',
        nationality: 'Vietnamese',
        otherNationalities: [],
        residencyStatus: 'citizen'
      },
      documents: data.documents || [] as DocumentItem[],
      financialInfo: data.financialInfo || {
        bankAccounts: [],
        taxResidency: '',
        creditScore: 0
      },
      familyMembers: data.familyMembers || [],
      healthInfo: data.healthInfo || {
        bloodType: '',
        allergies: [],
        insuranceProvider: '',
        insurancePolicyNumber: '',
        emergencyContact: {
          name: '',
          relationship: '',
          phone: ''
        }
      },
      travelHistory: data.travelHistory || [],
      membershipInfo: data.membershipInfo || {
        memberSince: '',
        membershipLevel: '',
        loyaltyPoints: 0,
        referralCode: ''
      },
      memberID: data.memberID || generateMemberID(
        data.personalDetails?.name || data.name || '', 
        data.contactInfo?.primaryPhone || data.phone || ''
      ),
      points: data.membershipInfo?.loyaltyPoints?.toString() || data.points || '0',
      passportNumber: data.passportNumber || '',
      entryDate: data.entryDate || '',
      preferences: data.preferences || {
        language: 'Vietnamese',
        currency: 'VND',
        communicationPreferences: {
          email: true,
          sms: true,
          phone: false,
          promotions: true
        }
      },
      consent: data.consent || {
        marketingConsent: false,
        dataProcessingConsent: false,
        consentDate: '',
        privacyPolicyVersion: ''
      },
      profileImage: data.profileImage || ''
    };
    
    formik.setValues(formikData);
  };

  const handleCopyId = (id: string) => {
    navigator.clipboard.writeText(id);
    setMessage({ text: 'Đã sao chép mã ID thành công', type: 'success' });
    setTimeout(() => setMessage({ text: '', type: '' }), 3000);
  };

  const clearMessage = () => setMessage({ text: '', type: '' });

  if (!customerData) {
    return <LoadingSpinner />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Background decorations - Hidden on mobile for better performance */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none hidden lg:block">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute top-1/2 -left-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-indigo-200 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 right-1/4 w-80 h-80 bg-gradient-to-br from-indigo-200 to-purple-200 rounded-full opacity-20 blur-3xl"></div>
      </div>

      <div className="relative z-10 space-y-3 sm:space-y-4 lg:space-y-8 p-3 sm:p-4 lg:p-6 max-w-7xl mx-auto">
        {/* Message Alert */}
        {message.text && (
          <div className="px-1">
            <MessageAlert message={message} onClose={clearMessage} />
          </div>
        )}

        {/* Profile Header - Hidden for now but can be uncommented */}
        {/* <ProfileHeader 
          customerData={customerData} 
          onEdit={() => setShowEditForm(true)}
          onCopyId={handleCopyId}
        /> */}

        {/* Main Content */}
        {!showEditForm ? (
          <div className="bg-white/90 backdrop-blur-sm rounded-xl lg:rounded-2xl shadow-lg border border-white/20 overflow-hidden">
            {/* Combined Profile Content */}
            <div className="p-3 sm:p-4 lg:p-8">
              <CombinedProfileTab customerData={customerData} />
            </div>
          </div>
        ) : (
          /* Edit Form - Mobile Optimized */
          <div className="bg-white/90 backdrop-blur-sm rounded-xl lg:rounded-2xl shadow-lg border border-white/20 overflow-hidden">
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-3 sm:p-4 lg:p-8">
              <h2 className="text-lg sm:text-xl lg:text-3xl font-bold text-white mb-1 sm:mb-2">Chỉnh Sửa Thông Tin</h2>
              <p className="text-indigo-100 text-sm sm:text-base lg:text-lg">Cập nhật thông tin cá nhân của bạn</p>
            </div>
            
            <div className="p-3 sm:p-4 lg:p-8">
              <div className="text-center py-6 sm:py-8 lg:py-16">
                <div className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-xl lg:rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 lg:mb-6 shadow-lg">
                  <FontAwesomeIcon icon={faPencilAlt} className="text-lg sm:text-xl lg:text-2xl text-white" />
                </div>
                <h3 className="text-base sm:text-lg lg:text-xl font-semibold text-gray-800 mb-2 lg:mb-3">Chức năng chỉnh sửa</h3>
                <p className="text-sm sm:text-base text-gray-600 mb-4 lg:mb-6 px-4 max-w-md mx-auto">Form chỉnh sửa thông tin đang được phát triển</p>
                <div className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full mb-6 lg:mb-8">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-xs sm:text-sm font-medium text-indigo-700">Sắp ra mắt</span>
                </div>
              </div>
              
              <div className="flex justify-center">
                <button
                  onClick={() => setShowEditForm(false)}
                  className="group flex items-center px-4 py-2 sm:px-5 sm:py-2.5 lg:px-6 lg:py-3 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg lg:rounded-xl hover:from-gray-200 hover:to-gray-300 transition-all duration-300 transform hover:scale-105 shadow-sm text-sm sm:text-base font-medium"
                >
                  <svg className="w-4 h-4 mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span>Quay lại</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Security Section - Mobile Optimized */}
        {!showEditForm && (
          <div className="px-1">
            <SecuritySection store={store as string} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileSection;