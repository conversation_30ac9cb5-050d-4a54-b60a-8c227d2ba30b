import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSimCard, faQrcode, faExclamationTriangle, faCheckCircle, faTimesCircle } from '@fortawesome/free-solid-svg-icons';

interface Sim {
  id: string;
  type: 'physical' | 'esim';
  number: string;
  status: 'active' | 'inactive' | 'pending';
  plan: string;
  data: string;
  validity: string;
  qrCode?: string;
}

const SimManagement: React.FC = () => {
  const [sims, setSims] = useState<Sim[]>([
    {
      id: '1',
      type: 'physical',
      number: '+1234567890',
      status: 'active',
      plan: 'Premium',
      data: '10GB',
      validity: '30 days',
    },
    {
      id: '2',
      type: 'esim',
      number: '+1987654321',
      status: 'pending',
      plan: 'Basic',
      data: '5GB',
      validity: '15 days',
      qrCode: 'https://example.com/qr-code.png',
    },
  ]);

  const getStatusColor = (status: Sim['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'inactive':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: Sim['status']) => {
    switch (status) {
      case 'active':
        return faCheckCircle;
      case 'inactive':
        return faTimesCircle;
      case 'pending':
        return faExclamationTriangle;
      default:
        return faSimCard;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">SIM & eSIM Management</h2>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          Add New SIM
        </button>
      </div>

      <div className="space-y-4">
        {sims.map((sim) => (
          <div key={sim.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div className="flex items-center space-x-3">
                <FontAwesomeIcon icon={faSimCard} className="text-gray-400 text-xl" />
                <div>
                  <h3 className="text-lg font-medium">
                    {sim.type === 'physical' ? 'Physical SIM' : 'eSIM'}
                  </h3>
                  <p className="text-gray-600">{sim.number}</p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded-full text-sm font-medium ${getStatusColor(sim.status)}`}>
                <FontAwesomeIcon icon={getStatusIcon(sim.status)} className="mr-1" />
                {sim.status.charAt(0).toUpperCase() + sim.status.slice(1)}
              </span>
            </div>

            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-500">Plan</p>
                <p className="font-medium">{sim.plan}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Data</p>
                <p className="font-medium">{sim.data}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Validity</p>
                <p className="font-medium">{sim.validity}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Actions</p>
                <div className="flex space-x-2">
                  {sim.type === 'esim' && sim.qrCode && (
                    <button className="text-indigo-600 hover:text-indigo-800">
                      <FontAwesomeIcon icon={faQrcode} />
                    </button>
                  )}
                  <button className="text-red-600 hover:text-red-800">
                    <FontAwesomeIcon icon={faExclamationTriangle} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <button className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
          Report Lost/Stolen SIM
        </button>
        <button className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
          Order Replacement
        </button>
      </div>
    </div>
  );
};

export default SimManagement; 