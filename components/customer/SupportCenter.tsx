import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faQuestionCircle, faTicketAlt, faComments, faBook, faPhone, faEnvelope } from '@fortawesome/free-solid-svg-icons';

interface Ticket {
  id: string;
  subject: string;
  status: 'open' | 'in_progress' | 'resolved';
  date: string;
  priority: 'low' | 'medium' | 'high';
}

interface Faq {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const SupportCenter: React.FC = () => {
  const [tickets, setTickets] = useState<Ticket[]>([
    {
      id: 'TKT001',
      subject: 'SIM Activation Issue',
      status: 'in_progress',
      date: '2024-03-20',
      priority: 'high',
    },
    {
      id: 'TKT002',
      subject: 'Billing Query',
      status: 'open',
      date: '2024-03-19',
      priority: 'medium',
    },
  ]);

  const [faqs] = useState<Faq[]>([
    {
      id: 'FAQ001',
      question: 'How do I activate my eSIM?',
      answer: 'To activate your eSIM, scan the QR code provided in your email or account dashboard using your device\'s camera.',
      category: 'SIM & eSIM',
    },
    {
      id: 'FAQ002',
      question: 'How can I check my data usage?',
      answer: 'You can check your data usage in the "My SIMs & eSIMs" section of your account dashboard.',
      category: 'Usage & Billing',
    },
  ]);

  const getStatusColor = (status: Ticket['status']) => {
    switch (status) {
      case 'resolved':
        return 'text-green-600 bg-green-100';
      case 'in_progress':
        return 'text-yellow-600 bg-yellow-100';
      case 'open':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: Ticket['priority']) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Support & Assistance</h2>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          <FontAwesomeIcon icon={faTicketAlt} className="mr-2" />
          New Ticket
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faComments} className="text-blue-400 text-xl mr-3" />
            <div>
              <h3 className="font-medium">Live Chat</h3>
              <p className="text-sm text-gray-600">Chat with our support team</p>
            </div>
          </div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faPhone} className="text-green-400 text-xl mr-3" />
            <div>
              <h3 className="font-medium">Phone Support</h3>
              <p className="text-sm text-gray-600">Call us at **************</p>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Support Tickets</h3>
        <div className="space-y-4">
          {tickets.map((ticket) => (
            <div key={ticket.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{ticket.subject}</h4>
                  <p className="text-sm text-gray-500 mt-1">Created: {ticket.date}</p>
                </div>
                <div className="flex space-x-2">
                  <span className={`px-2 py-1 rounded-full text-sm font-medium ${getStatusColor(ticket.status)}`}>
                    {ticket.status.replace('_', ' ')}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-sm font-medium ${getPriorityColor(ticket.priority)}`}>
                    {ticket.priority}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Frequently Asked Questions</h3>
        <div className="space-y-4">
          {faqs.map((faq) => (
            <div key={faq.id} className="border rounded-lg p-4">
              <h4 className="font-medium">{faq.question}</h4>
              <p className="text-sm text-gray-500 mt-1">{faq.answer}</p>
              <span className="inline-block mt-2 text-xs text-gray-400">
                Category: {faq.category}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6">
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FontAwesomeIcon icon={faEnvelope} className="text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Need more help? Email <NAME_EMAIL> or check our comprehensive help center.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportCenter; 