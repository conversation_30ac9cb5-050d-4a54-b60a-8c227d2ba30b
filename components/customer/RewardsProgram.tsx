import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar, faGift, faTicketAlt, faPercent, faUserFriends } from '@fortawesome/free-solid-svg-icons';

interface Reward {
  id: string;
  title: string;
  description: string;
  points: number;
  expiryDate?: string;
  isRedeemed: boolean;
}

interface Offer {
  id: string;
  title: string;
  description: string;
  discount: string;
  validUntil: string;
  isActive: boolean;
}

const RewardsProgram: React.FC = () => {
  const [points, setPoints] = useState(2500);
  const [rewards, setRewards] = useState<Reward[]>([
    {
      id: '1',
      title: '10% Off Next Top-up',
      description: 'Get 10% off on your next top-up recharge',
      points: 500,
      expiryDate: '2024-04-20',
      isRedeemed: false,
    },
    {
      id: '2',
      title: 'Free Data Day',
      description: 'Enjoy unlimited data for 24 hours',
      points: 1000,
      expiryDate: '2024-04-15',
      isRedeemed: false,
    },
  ]);

  const [offers, setOffers] = useState<Offer[]>([
    {
      id: '1',
      title: 'Birthday Special',
      description: 'Get 20% off on all plans this month',
      discount: '20% OFF',
      validUntil: '2024-03-31',
      isActive: true,
    },
    {
      id: '2',
      title: 'Referral Bonus',
      description: 'Earn 500 points for each friend you refer',
      discount: '500 PTS',
      validUntil: '2024-04-30',
      isActive: true,
    },
  ]);

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Rewards & Offers</h2>
        <div className="flex items-center">
          <FontAwesomeIcon icon={faStar} className="text-yellow-400 mr-2" />
          <span className="text-lg font-medium">{points} Points</span>
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Available Rewards</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {rewards.map((reward) => (
            <div key={reward.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{reward.title}</h4>
                  <p className="text-sm text-gray-500 mt-1">{reward.description}</p>
                  {reward.expiryDate && (
                    <p className="text-sm text-red-600 mt-1">
                      Expires: {reward.expiryDate}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <div className="text-lg font-medium text-indigo-600">
                    {reward.points} pts
                  </div>
                  <button
                    className={`mt-2 px-3 py-1 text-sm rounded-md ${
                      reward.isRedeemed
                        ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                        : 'bg-indigo-600 text-white hover:bg-indigo-700'
                    }`}
                    disabled={reward.isRedeemed}
                  >
                    {reward.isRedeemed ? 'Redeemed' : 'Redeem'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Special Offers</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {offers.map((offer) => (
            <div key={offer.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{offer.title}</h4>
                  <p className="text-sm text-gray-500 mt-1">{offer.description}</p>
                  <p className="text-sm text-red-600 mt-1">
                    Valid until: {offer.validUntil}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-medium text-green-600">
                    {offer.discount}
                  </div>
                  <button
                    className={`mt-2 px-3 py-1 text-sm rounded-md ${
                      offer.isActive
                        ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                        : 'bg-gray-100 text-gray-500 cursor-not-allowed'
                    }`}
                    disabled={!offer.isActive}
                  >
                    {offer.isActive ? 'Activate' : 'Expired'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6">
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FontAwesomeIcon icon={faUserFriends} className="text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Invite friends and earn bonus points! Share your referral code to get started.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RewardsProgram; 