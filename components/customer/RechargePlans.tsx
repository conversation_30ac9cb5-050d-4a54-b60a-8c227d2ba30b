import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faInfinity, faMobileAlt, faClock } from '@fortawesome/free-solid-svg-icons';

interface RechargePlan {
  id: string;
  name: string;
  description: string;
  price: number;
  validity: string;
  data: string;
  features: string[];
  isPopular?: boolean;
}

const RechargePlans: React.FC = () => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [plans] = useState<RechargePlan[]>([
    {
      id: '1',
      name: 'Basic',
      description: 'Perfect for light users',
      price: 10,
      validity: '7 days',
      data: '5GB',
      features: [
        '5GB High-speed data',
        '7 days validity',
        'Standard support',
        'Basic streaming quality',
      ],
    },
    {
      id: '2',
      name: 'Premium',
      description: 'Most popular choice',
      price: 25,
      validity: '30 days',
      data: '15GB',
      features: [
        '15GB High-speed data',
        '30 days validity',
        'Priority support',
        'HD streaming quality',
        'Unlimited social media',
      ],
      isPopular: true,
    },
    {
      id: '3',
      name: 'Unlimited',
      description: 'For heavy data users',
      price: 50,
      validity: '30 days',
      data: 'Unlimited',
      features: [
        'Unlimited High-speed data',
        '30 days validity',
        '24/7 Priority support',
        '4K streaming quality',
        'Unlimited social media',
        'Free roaming in 50+ countries',
      ],
    },
  ]);

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold">Choose Your Recharge Plan</h2>
        <p className="text-gray-600 mt-2">Select the perfect plan for your needs</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`relative border rounded-lg p-6 ${
              plan.isPopular
                ? 'border-indigo-500 shadow-lg'
                : 'border-gray-200'
            }`}
          >
            {plan.isPopular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-indigo-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
            )}

            <div className="text-center mb-6">
              <h3 className="text-xl font-bold">{plan.name}</h3>
              <p className="text-gray-600 mt-2">{plan.description}</p>
              <div className="mt-4">
                <span className="text-3xl font-bold">${plan.price}</span>
                <span className="text-gray-600">/ {plan.validity}</span>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faMobileAlt} className="text-gray-400 mr-3" />
                <span className="text-gray-600">{plan.data}</span>
              </div>
              <div className="flex items-center">
                <FontAwesomeIcon icon={faClock} className="text-gray-400 mr-3" />
                <span className="text-gray-600">{plan.validity} validity</span>
              </div>
            </div>

            <div className="space-y-3 mb-6">
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <FontAwesomeIcon icon={faCheck} className="text-green-500 mr-3" />
                  <span className="text-gray-600">{feature}</span>
                </div>
              ))}
            </div>

            <button
              onClick={() => setSelectedPlan(plan.id)}
              className={`w-full py-2 px-4 rounded-md font-medium ${
                selectedPlan === plan.id
                  ? 'bg-indigo-600 text-white'
                  : plan.isPopular
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                  : 'border border-indigo-600 text-indigo-600 hover:bg-indigo-50'
              }`}
            >
              {selectedPlan === plan.id ? 'Selected' : 'Select Plan'}
            </button>
          </div>
        ))}
      </div>

      <div className="mt-8">
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FontAwesomeIcon icon={faInfinity} className="text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Need more data? Upgrade your plan anytime. Unused data will be carried forward to the next billing cycle.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RechargePlans; 