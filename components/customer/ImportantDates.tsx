import React from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBirthdayCake, faHeart, faBell } from '@fortawesome/free-solid-svg-icons';

const validationSchema = Yup.object({
  birthday: Yup.date().required('Vui lòng nhập ngày sinh'),
  anniversary: Yup.date().nullable(),
  receiveNotifications: Yup.boolean(),
});

const ImportantDates: React.FC = () => {
  const formik = useFormik({
    initialValues: {
      birthday: '',
      anniversary: '',
      receiveNotifications: true,
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form submitted:', values);
      // Handle form submission
    },
  });

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6"><PERSON><PERSON><PERSON>uan Trọng</h2>
      <form onSubmit={formik.handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">Ngày Sinh</label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faBirthdayCake} className="text-gray-400" />
            </div>
            <input
              type="date"
              name="birthday"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.birthday}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          {formik.touched.birthday && formik.errors.birthday && (
            <div className="text-red-500 text-sm mt-1">{formik.errors.birthday}</div>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Kỷ Niệm (Tùy Chọn)</label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faHeart} className="text-gray-400" />
            </div>
            <input
              type="date"
              name="anniversary"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.anniversary}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          {formik.touched.anniversary && formik.errors.anniversary && (
            <div className="text-red-500 text-sm mt-1">{formik.errors.anniversary}</div>
          )}
        </div>

        <div className="flex items-center">
          <div className="relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faBell} className="text-gray-400" />
            </div>
            <div className="flex items-center h-10 pl-10">
              <input
                type="checkbox"
                name="receiveNotifications"
                onChange={formik.handleChange}
                checked={formik.values.receiveNotifications}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Nhận ưu đãi đặc biệt và thông báo cho các ngày quan trọng của tôi
              </label>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Lưu Ngày
          </button>
        </div>
      </form>
    </div>
  );
};

export default ImportantDates; 