import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faPlus, 
  faEdit, 
  faTrash, 
  faSave, 
  faTimes, 
  faStickyNote,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import axios from 'axios';

interface OrderNote {
  id: string;
  text: string;
  createdAt: string;
  updatedAt: string;
}

interface OrderNotesManagerProps {
  orderId: string;
  initialNotes?: OrderNote[];
  onNotesUpdate?: (notes: OrderNote[]) => void;
}

const OrderNotesManager: React.FC<OrderNotesManagerProps> = ({
  orderId,
  initialNotes = [],
  onNotesUpdate
}) => {
  const [notes, setNotes] = useState<OrderNote[]>(initialNotes);
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [newNoteText, setNewNoteText] = useState('');
  const [editNoteText, setEditNoteText] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get customer data for API authentication
  const getCustomerData = () => {
    try {
      const customerData = localStorage.getItem('customerData');
      return customerData ? JSON.parse(customerData) : null;
    } catch (error) {
      console.error('Error parsing customer data:', error);
      return null;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Load notes from API
  const loadNotes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const customerData = getCustomerData();
      if (!customerData) {
        throw new Error('Customer authentication required');
      }

      const response = await axios.post(`/api/orders/${orderId}/notes/get`, {
        customerData
      }, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      });

      if (response.data.success) {
        setNotes(response.data.notes || []);
        onNotesUpdate?.(response.data.notes || []);
      } else {
        throw new Error(response.data.message || 'Failed to load notes');
      }
    } catch (err: any) {
      console.error('Error loading notes:', err);
      setError(err.response?.data?.message || err.message || 'Failed to load notes');
    } finally {
      setLoading(false);
    }
  };

  // Add a new note
  const addNote = async () => {
    if (!newNoteText.trim()) return;

    try {
      setLoading(true);
      setError(null);
      
      const customerData = getCustomerData();
      if (!customerData) {
        throw new Error('Customer authentication required');
      }

      const response = await axios.post(`/api/orders/${orderId}/notes`, {
        text: newNoteText.trim(),
        customerData
      }, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      });

      if (response.data.success) {
        const updatedNotes = [...notes, response.data.note];
        setNotes(updatedNotes);
        onNotesUpdate?.(updatedNotes);
        setNewNoteText('');
        setIsAddingNote(false);
      } else {
        throw new Error(response.data.message || 'Failed to add note');
      }
    } catch (err: any) {
      console.error('Error adding note:', err);
      setError(err.response?.data?.message || err.message || 'Failed to add note');
    } finally {
      setLoading(false);
    }
  };

  // Update an existing note
  const updateNote = async (noteId: string) => {
    if (!editNoteText.trim()) return;

    try {
      setLoading(true);
      setError(null);
      
      const customerData = getCustomerData();
      if (!customerData) {
        throw new Error('Customer authentication required');
      }

      const response = await axios.put(`/api/orders/${orderId}/notes`, {
        noteId,
        text: editNoteText.trim(),
        customerData
      }, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      });

      if (response.data.success) {
        const updatedNotes = notes.map(note => 
          note.id === noteId ? response.data.note : note
        );
        setNotes(updatedNotes);
        onNotesUpdate?.(updatedNotes);
        setEditingNoteId(null);
        setEditNoteText('');
      } else {
        throw new Error(response.data.message || 'Failed to update note');
      }
    } catch (err: any) {
      console.error('Error updating note:', err);
      setError(err.response?.data?.message || err.message || 'Failed to update note');
    } finally {
      setLoading(false);
    }
  };

  // Delete a note
  const deleteNote = async (noteId: string) => {
    if (!confirm('Bạn có chắc chắn muốn xóa ghi chú này?')) return;

    try {
      setLoading(true);
      setError(null);
      
      const customerData = getCustomerData();
      if (!customerData) {
        throw new Error('Customer authentication required');
      }

      const response = await axios.delete(`/api/orders/${orderId}/notes`, {
        data: {
          noteId,
          customerData
        },
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      });

      if (response.data.success) {
        const updatedNotes = notes.filter(note => note.id !== noteId);
        setNotes(updatedNotes);
        onNotesUpdate?.(updatedNotes);
      } else {
        throw new Error(response.data.message || 'Failed to delete note');
      }
    } catch (err: any) {
      console.error('Error deleting note:', err);
      setError(err.response?.data?.message || err.message || 'Failed to delete note');
    } finally {
      setLoading(false);
    }
  };

  // Start editing a note
  const startEditing = (note: OrderNote) => {
    setEditingNoteId(note.id);
    setEditNoteText(note.text);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingNoteId(null);
    setEditNoteText('');
  };

  // Cancel adding
  const cancelAdding = () => {
    setIsAddingNote(false);
    setNewNoteText('');
  };

  // Load notes on component mount
  useEffect(() => {
    if (initialNotes.length === 0) {
      loadNotes();
    }
  }, [orderId]);

  return (
    <div className="mt-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-sm font-medium text-gray-700 flex items-center">
          <FontAwesomeIcon icon={faStickyNote} className="h-4 w-4 text-blue-500 mr-2" />
          Ghi chú của tôi ({notes.length})
        </h4>
        {!isAddingNote && (
          <button
            onClick={() => setIsAddingNote(true)}
            disabled={loading}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FontAwesomeIcon icon={faPlus} className="h-3 w-3 mr-1" />
            Thêm ghi chú
          </button>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Add new note form */}
      {isAddingNote && (
        <div className="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <textarea
            value={newNoteText}
            onChange={(e) => setNewNoteText(e.target.value)}
            placeholder="Nhập ghi chú của bạn..."
            className="w-full p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            rows={3}
            disabled={loading}
          />
          <div className="flex justify-end space-x-2 mt-3">
            <button
              onClick={cancelAdding}
              disabled={loading}
              className="px-3 py-1.5 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FontAwesomeIcon icon={faTimes} className="h-3 w-3 mr-1" />
              Hủy
            </button>
            <button
              onClick={addNote}
              disabled={loading || !newNoteText.trim()}
              className="px-3 py-1.5 text-sm text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 mr-1 animate-spin" />
              ) : (
                <FontAwesomeIcon icon={faSave} className="h-3 w-3 mr-1" />
              )}
              Lưu
            </button>
          </div>
        </div>
      )}

      {/* Notes list */}
      <div className="space-y-3">
        {notes.length === 0 && !loading ? (
          <div className="text-center py-8 text-gray-500">
            <FontAwesomeIcon icon={faStickyNote} className="h-8 w-8 text-gray-300 mb-2" />
            <p className="text-sm">Chưa có ghi chú nào</p>
            <p className="text-xs text-gray-400">Thêm ghi chú để theo dõi đơn hàng của bạn</p>
          </div>
        ) : (
          notes.map((note) => (
            <div key={note.id} className="bg-white border border-gray-200 rounded-lg p-4">
              {editingNoteId === note.id ? (
                // Edit mode
                <div>
                  <textarea
                    value={editNoteText}
                    onChange={(e) => setEditNoteText(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                    disabled={loading}
                  />
                  <div className="flex justify-between items-center mt-3">
                    <p className="text-xs text-gray-500">
                      Tạo: {formatDate(note.createdAt)}
                      {note.updatedAt !== note.createdAt && (
                        <span> • Sửa: {formatDate(note.updatedAt)}</span>
                      )}
                    </p>
                    <div className="flex space-x-2">
                      <button
                        onClick={cancelEditing}
                        disabled={loading}
                        className="px-2 py-1 text-xs text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => updateNote(note.id)}
                        disabled={loading || !editNoteText.trim()}
                        className="px-2 py-1 text-xs text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? (
                          <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 animate-spin" />
                        ) : (
                          <FontAwesomeIcon icon={faSave} className="h-3 w-3" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                // View mode
                <div>
                  <p className="text-sm text-gray-800 whitespace-pre-wrap mb-3">{note.text}</p>
                  <div className="flex justify-between items-center">
                    <p className="text-xs text-gray-500">
                      Tạo: {formatDate(note.createdAt)}
                      {note.updatedAt !== note.createdAt && (
                        <span> • Sửa: {formatDate(note.updatedAt)}</span>
                      )}
                    </p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => startEditing(note)}
                        disabled={loading}
                        className="px-2 py-1 text-xs text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <FontAwesomeIcon icon={faEdit} className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => deleteNote(note.id)}
                        disabled={loading}
                        className="px-2 py-1 text-xs text-red-600 bg-red-50 border border-red-200 rounded hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {loading && notes.length === 0 && (
        <div className="text-center py-4">
          <FontAwesomeIcon icon={faSpinner} className="h-5 w-5 text-blue-500 animate-spin" />
          <p className="text-sm text-gray-500 mt-2">Đang tải ghi chú...</p>
        </div>
      )}
    </div>
  );
};

export default OrderNotesManager;