import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/router';
import { 
  FaFile, FaFileAlt, FaFilePdf, FaFileImage, 
  FaFileDownload, FaSpinner, FaExclamationTriangle, 
  FaEye, FaTimes, FaRegFileAlt, FaCalendarAlt, FaTag,
  FaExpand, FaChevronLeft, FaChevronRight, FaSearchPlus,
  FaSearchMinus, FaCompress, FaExternalLinkAlt, FaUpload, FaPlus
} from 'react-icons/fa';
import { getCustomerInfo, isCustomerAuthenticated } from '../../utils/customerAuth';
import { getCustomerDocuments } from '../../utils/customerAPI';
import DocumentCard from './DocumentCard';

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: string;
  relatedOrder?: string;
  url: string;
  documentType?: string;
}

// Document types dictionary
const DOCUMENT_TYPES = {
  idCard: { label: 'CMND/CCCD', description: 'Chứng minh nhân dân hoặc căn cước công dân' },
  passport: { label: 'Hộ chiếu', description: 'Hộ chiếu quốc tế' },
  driverLicense: { label: 'Bằng lái xe', description: 'Giấy phép lái xe' },
  birthCertificate: { label: 'Giấy khai sinh', description: 'Giấy khai sinh' },
  photo: { label: 'Ảnh chân dung', description: 'Ảnh chân dung chính chủ' },
  address: { label: 'Giấy tờ cư trú', description: 'Sổ hộ khẩu, tạm trú, KT3, xác nhận cư trú' },
  businessRegistration: { label: 'Đăng ký kinh doanh', description: 'Giấy phép đăng ký kinh doanh' },
  taxRegistration: { label: 'Đăng ký thuế', description: 'Giấy đăng ký mã số thuế' },
  bankStatement: { label: 'Sao kê ngân hàng', description: 'Sao kê tài khoản ngân hàng' },
  insuranceCard: { label: 'Thẻ bảo hiểm', description: 'Thẻ bảo hiểm y tế/xã hội' },
  marriageCertificate: { label: 'Giấy kết hôn', description: 'Giấy chứng nhận kết hôn' },
  workPermit: { label: 'Giấy phép lao động', description: 'Giấy phép lao động cho người nước ngoài' },
  academicDegree: { label: 'Bằng cấp học thuật', description: 'Bằng đại học, cao đẳng, chứng chỉ' },
  utilityBill: { label: 'Hóa đơn tiện ích', description: 'Hóa đơn điện, nước, internet' },
  propertyOwnership: { label: 'Giấy tờ sở hữu', description: 'Giấy chứng nhận quyền sử dụng đất, sở hữu nhà' },
  other: { label: 'Khác', description: 'Các giấy tờ khác' }
};

// DocumentUploadRow component for table-based upload
const DocumentUploadRow: React.FC<{
  documentType: string;
  documentInfo: { label: string; description: string };
  onUpload: (newDocument: Document) => void;
  customerId: string;
  customerName: string;
  existingDocuments: Document[];
}> = ({ documentType, documentInfo, onUpload, customerId, customerName, existingDocuments }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if this document type already exists
  const existingDoc = existingDocuments.find(doc => doc.documentType === documentType);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      alert('Chỉ chấp nhận file JPG, PNG hoặc PDF');
      return;
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      alert('File không được vượt quá 10MB');
      return;
    }

    setUploadStatus('uploading');
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentType', documentType);
      formData.append('customerId', customerId);
      formData.append('customerName', customerName);

      const response = await fetch('/api/customer/upload-document', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();
      setUploadProgress(100);
      setUploadStatus('success');
      
      // Call the onUpload callback with the new document
      onUpload({
        id: result.id || Date.now().toString(),
        name: file.name,
        type: file.type,
        size: file.size,
        uploadDate: new Date().toISOString(),
        url: result.url || '',
        documentType: documentType
      });

      // Reset status after 3 seconds
      setTimeout(() => {
        setUploadStatus('idle');
        setUploadProgress(0);
      }, 3000);

    } catch (error) {
      console.error('Upload error:', error);
      setUploadStatus('error');
      setTimeout(() => {
        setUploadStatus('idle');
        setUploadProgress(0);
      }, 3000);
    }
  };

  const getStatusIcon = () => {
    switch (uploadStatus) {
      case 'uploading':
        return <FaSpinner className="animate-spin text-blue-500" />;
      case 'success':
        return <div className="text-green-500">✓</div>;
      case 'error':
        return <div className="text-red-500">✗</div>;
      default:
        return existingDoc ? <div className="text-green-500">✓</div> : <FaUpload className="text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (uploadStatus) {
      case 'uploading':
        return `Đang tải lên... ${uploadProgress}%`;
      case 'success':
        return 'Tải lên thành công!';
      case 'error':
        return 'Lỗi tải lên';
      default:
        return existingDoc ? 'Đã có tài liệu' : 'Kéo thả file hoặc click để chọn';
    }
  };

  return (
    <tr className="border-b border-gray-100 hover:bg-gray-50">
      <td className="px-4 py-4 border-r border-gray-200">
        <div>
          <div className="font-medium text-gray-900">{documentInfo.label}</div>
          <div className="text-sm text-gray-500">{documentInfo.description}</div>
        </div>
      </td>
      <td className="px-4 py-4">
        <div
          className={`relative border-2 border-dashed rounded-lg p-4 transition-all duration-200 cursor-pointer ${
            isDragOver
              ? 'border-blue-400 bg-blue-50'
              : uploadStatus === 'success' || existingDoc
              ? 'border-green-300 bg-green-50'
              : uploadStatus === 'error'
              ? 'border-red-300 bg-red-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".jpg,.jpeg,.png,.pdf"
            onChange={handleFileSelect}
            className="hidden"
          />
          <div className="flex items-center justify-center space-x-2">
            {getStatusIcon()}
            <span className="text-sm text-gray-600">{getStatusText()}</span>
          </div>
          {uploadStatus === 'uploading' && (
            <div className="mt-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};



const Documents: React.FC = () => {
  const router = useRouter();
  const { store } = router.query;
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [previewDocument, setPreviewDocument] = useState<Document | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<any>(null);

  useEffect(() => {
    if (!store) return;
    
    const fetchDocuments = async () => {
      setLoading(true);
      setError('');
      
      try {
        // Check if user is authenticated
        if (!isCustomerAuthenticated()) {
          throw new Error('Bạn cần đăng nhập để xem tài liệu');
        }
        
        const customerData = getCustomerInfo("");
        if (!customerData) {
          throw new Error('Không tìm thấy thông tin khách hàng');
        }
        
        setCustomerInfo(customerData);
        
        // Get customer ID
        const customerId = customerData.id || customerData._id;
        if (!customerId) {
          throw new Error('Không tìm thấy ID khách hàng');
        }
        
        console.log('Customer data for upload:', {
          id: customerId,
          name: customerData.name || customerData.fullName,
          hasPersonalDetails: !!customerData.personalDetails
        });
        
        // Fetch documents from API
        const response = await getCustomerDocuments();
        
        if (!response.success) {
          throw new Error(response.message || 'Không thể tải tài liệu');
        }
        
        // Process documents from API response
        const allDocuments: Document[] = [];
        
        // Process documents by type
        if (response.documentsByType) {
          const documentTypes = Object.keys(response.documentsByType);
          
          documentTypes.forEach(docType => {
            const typeDocuments = response.documentsByType[docType];
            if (Array.isArray(typeDocuments)) {
              typeDocuments.forEach(doc => {
                allDocuments.push({
                  id: doc._id || doc.id || `doc-${Math.random().toString(36).substring(2, 11)}`,
                  name: doc.name || doc.fileName || `${docType} Document`,
                  type: doc.type || doc.mimeType || 'application/octet-stream',
                  size: doc.size || 0,
                  uploadDate: doc.uploadDate || doc.createdAt || new Date().toISOString(),
                  relatedOrder: doc.orderId,
                  url: getDocumentUrl(doc, customerId),
                  documentType: docType
                });
              });
            }
          });
        }
        
        // Process documents from orders
        if (response.orders && Array.isArray(response.orders)) {
          response.orders.forEach(order => {
            // Process order documents
            if (order.documents && Array.isArray(order.documents)) {
              order.documents.forEach(doc => {
                allDocuments.push({
                  id: doc._id || doc.id || `order-doc-${order.orderId}-${Math.random().toString(36).substring(2, 11)}`,
                  name: doc.name || doc.fileName || 'Tài liệu đơn hàng',
                  type: doc.type || doc.mimeType || 'application/octet-stream',
                  size: doc.size || 0,
                  uploadDate: doc.uploadDate || order.timestamp || order.lastUpdated || new Date().toISOString(),
                  relatedOrder: order.orderId,
                  url: getDocumentUrl(doc, customerId)
                });
              });
            }
            
            // Process uploaded files from order items
            if (order.items && Array.isArray(order.items)) {
              order.items.forEach(item => {
                // Check for uploaded files
                if (item.uploadedFiles && Array.isArray(item.uploadedFiles)) {
                  item.uploadedFiles.forEach(file => {
                    allDocuments.push({
                      id: file._id || file.id || `item-file-${order.orderId}-${item.id}-${Math.random().toString(36).substring(2, 11)}`,
                      name: file.name || file.fileName || 'Tài liệu sản phẩm',
                      type: file.type || file.mimeType || 'application/octet-stream',
                      size: file.size || 0,
                      uploadDate: file.uploadDate || order.timestamp || order.lastUpdated || new Date().toISOString(),
                      relatedOrder: order.orderId,
                      url: getDocumentUrl(file, customerId)
                    });
                  });
                }
                
                // Check for config files
                if (item.configFiles && Array.isArray(item.configFiles)) {
                  item.configFiles.forEach(file => {
                    allDocuments.push({
                      id: file._id || file.id || `config-file-${order.orderId}-${item.id}-${Math.random().toString(36).substring(2, 11)}`,
                      name: file.name || file.fileName || 'Tài liệu cấu hình',
                      type: file.type || file.mimeType || 'application/octet-stream',
                      size: file.size || 0,
                      uploadDate: file.uploadDate || order.timestamp || order.lastUpdated || new Date().toISOString(),
                      relatedOrder: order.orderId,
                      url: getDocumentUrl(file, customerId)
                    });
                  });
                }
              });
            }
          });
        }
        
        // Sort documents by date, newest first
        allDocuments.sort((a, b) => {
          const dateA = new Date(a.uploadDate).getTime();
          const dateB = new Date(b.uploadDate).getTime();
          return dateB - dateA;
        });
        
        // Remove duplicates (by url)
        const uniqueDocuments = allDocuments.filter((doc, index, self) => 
          index === self.findIndex(d => d.url === doc.url)
        );
        
        setDocuments(uniqueDocuments);
      } catch (error) {
        console.error('Error fetching documents:', error);
        setError(error instanceof Error ? error.message : 'Có lỗi xảy ra khi tải tài liệu. Vui lòng thử lại sau.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchDocuments();
  }, [store]);

  // Reset zoom when changing images
  useEffect(() => {
    if (previewDocument) {
      setZoomLevel(1);
    }
  }, [previewDocument]);

  // Helper function to get icon based on file type
  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return <FaFilePdf className="text-red-500" size={24} />;
    if (type.includes('image')) return <FaFileImage className="text-blue-500" size={24} />;
    if (type.includes('text')) return <FaFileAlt className="text-gray-500" size={24} />;
    return <FaFile className="text-gray-500" size={24} />;
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };
  
  // Helper function to generate correct document URL
  const getDocumentUrl = (doc: any, customerId: string) => {
    if (!doc.url) return '';
    
    // If it's already a proper API URL, return as is
    if (doc.url.includes('/api/customer/') && doc.url.includes('/document/')) {
      return doc.url;
    }
    
    // If it's a /data/ URL, extract the filename and use the correct API
    if (doc.url.startsWith('/data/documents/customers/')) {
      const pathParts = doc.url.split('/');
      const filename = pathParts[pathParts.length - 1];
      return `/api/customer/${customerId}/document/${filename}`;
    }
    
    // If it has a filename property, use that directly
    if (doc.filename) {
      return `/api/customer/${customerId}/document/${doc.filename}`;
    }
    
    // If it's a direct filename, use it
    if (doc.url && !doc.url.includes('/')) {
      return `/api/customer/${customerId}/document/${doc.url}`;
    }
    
    // Default fallback
    return doc.url;
  };

  // Helper function to get document type label
  const getDocumentTypeLabel = (type?: string) => {
    if (!type) return '';
    
    // Check if type exists in DOCUMENT_TYPES
    if (DOCUMENT_TYPES[type as keyof typeof DOCUMENT_TYPES]) {
      return DOCUMENT_TYPES[type as keyof typeof DOCUMENT_TYPES].label;
    }
    
    switch (type.toLowerCase()) {
      case 'idcard':
        return 'CMND/CCCD';
      case 'photo':
        return 'Ảnh chân dung';
      case 'proofofresidence':
        return 'Xác minh cư trú';
      default:
        return type;
    }
  };

  // Handle document upload success
  const handleDocumentUploaded = async (newDocument: Document) => {
    // Optimistically add the new document to the list
    setDocuments(prev => [newDocument, ...prev]);
    
    // Refetch documents from server to ensure consistency
    setTimeout(() => {
      refetchDocuments();
    }, 1000); // Wait a second for the server to process
  };

  // Refetch documents after upload
  const refetchDocuments = async () => {
    if (!store || !customerInfo) return;
    
    try {
      const response = await getCustomerDocuments();
      
      if (!response.success) {
        throw new Error(response.message || 'Không thể tải tài liệu');
      }
      
      // Process documents from API response (same logic as in useEffect)
      const allDocuments: Document[] = [];
      
      // Process documents by type
      if (response.documentsByType) {
        const documentTypes = Object.keys(response.documentsByType);
        
        documentTypes.forEach(docType => {
          const typeDocuments = response.documentsByType[docType];
          if (Array.isArray(typeDocuments)) {
            typeDocuments.forEach(doc => {
              allDocuments.push({
                id: doc._id || doc.id || `doc-${Math.random().toString(36).substring(2, 11)}`,
                name: doc.name || doc.fileName || `${docType} Document`,
                type: doc.type || doc.mimeType || 'application/octet-stream',
                size: doc.size || 0,
                uploadDate: doc.uploadDate || doc.createdAt || new Date().toISOString(),
                relatedOrder: doc.orderId,
                url: getDocumentUrl(doc, customerInfo.id || customerInfo._id),
                documentType: docType
              });
            });
          }
        });
      }
      
      // Process documents from orders
      if (response.orders && Array.isArray(response.orders)) {
        response.orders.forEach(order => {
          // Process order documents
          if (order.documents && Array.isArray(order.documents)) {
            order.documents.forEach(doc => {
              allDocuments.push({
                id: doc._id || doc.id || `order-doc-${order.orderId}-${Math.random().toString(36).substring(2, 11)}`,
                name: doc.name || doc.fileName || 'Tài liệu đơn hàng',
                type: doc.type || doc.mimeType || 'application/octet-stream',
                size: doc.size || 0,
                uploadDate: doc.uploadDate || order.timestamp || order.lastUpdated || new Date().toISOString(),
                relatedOrder: order.orderId,
                url: getDocumentUrl(doc, customerInfo.id || customerInfo._id)
              });
            });
          }
          
          // Process uploaded files from order items
          if (order.items && Array.isArray(order.items)) {
            order.items.forEach(item => {
              // Check for uploaded files
              if (item.uploadedFiles && Array.isArray(item.uploadedFiles)) {
                item.uploadedFiles.forEach(file => {
                  allDocuments.push({
                    id: file._id || file.id || `item-file-${order.orderId}-${item.id}-${Math.random().toString(36).substring(2, 11)}`,
                    name: file.name || file.fileName || 'Tài liệu sản phẩm',
                    type: file.type || file.mimeType || 'application/octet-stream',
                    size: file.size || 0,
                    uploadDate: file.uploadDate || order.timestamp || order.lastUpdated || new Date().toISOString(),
                    relatedOrder: order.orderId,
                    url: getDocumentUrl(file, customerInfo.id || customerInfo._id)
                  });
                });
              }
              
              // Check for config files
              if (item.configFiles && Array.isArray(item.configFiles)) {
                item.configFiles.forEach(file => {
                  allDocuments.push({
                    id: file._id || file.id || `config-file-${order.orderId}-${item.id}-${Math.random().toString(36).substring(2, 11)}`,
                    name: file.name || file.fileName || 'Tài liệu cấu hình',
                    type: file.type || file.mimeType || 'application/octet-stream',
                    size: file.size || 0,
                    uploadDate: file.uploadDate || order.timestamp || order.lastUpdated || new Date().toISOString(),
                    relatedOrder: order.orderId,
                    url: getDocumentUrl(file, customerInfo.id || customerInfo._id)
                  });
                });
              }
            });
          }
        });
      }
      
      // Sort documents by date, newest first
      allDocuments.sort((a, b) => {
        const dateA = new Date(a.uploadDate).getTime();
        const dateB = new Date(b.uploadDate).getTime();
        return dateB - dateA;
      });
      
      // Remove duplicates (by url)
      const uniqueDocuments = allDocuments.filter((doc, index, self) => 
        index === self.findIndex(d => d.url === doc.url)
      );
      
      setDocuments(uniqueDocuments);
    } catch (error) {
      console.error('Error refetching documents:', error);
    }
  };
  
  // Check if a document is an image
  const isImageDocument = (doc: Document) => {
    return !!(doc.type && (
      doc.type.includes('image') || 
      doc.type.includes('jpg') || 
      doc.type.includes('jpeg') || 
      doc.type.includes('png') || 
      doc.type.includes('gif') || 
      doc.type.includes('webp') ||
      doc.type.includes('bmp')
    ));
  };
  
  // Get all image documents
  const getImageDocuments = () => {
    return documents.filter(doc => isImageDocument(doc));
  };
  
  // Open document preview
  const openPreview = (doc: Document) => {
    setPreviewDocument(doc);
    const imageDocuments = getImageDocuments();
    const index = imageDocuments.findIndex(d => d.id === doc.id);
    setCurrentImageIndex(index);
    setZoomLevel(1);
  };
  
  // Close document preview
  const closePreview = () => {
    setPreviewDocument(null);
    setZoomLevel(1);
    setIsFullscreen(false);
  };

  // Navigate to next image in preview
  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    const imageDocuments = getImageDocuments();
    if (currentImageIndex < imageDocuments.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
      setPreviewDocument(imageDocuments[currentImageIndex + 1]);
      setZoomLevel(1);
    }
  };

  // Navigate to previous image in preview
  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    const imageDocuments = getImageDocuments();
    if (currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
      setPreviewDocument(imageDocuments[currentImageIndex - 1]);
      setZoomLevel(1);
    }
  };

  // Zoom in
  const zoomIn = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (zoomLevel < 3) {
      setZoomLevel(prev => Math.min(prev + 0.25, 3));
    }
  };

  // Zoom out
  const zoomOut = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (zoomLevel > 0.5) {
      setZoomLevel(prev => Math.max(prev - 0.25, 0.5));
    }
  };

  // Reset zoom
  const resetZoom = (e: React.MouseEvent) => {
    e.stopPropagation();
    setZoomLevel(1);
  };

  // Toggle fullscreen
  const toggleFullscreen = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsFullscreen(!isFullscreen);
  };

  // Open image in new tab
  const openInNewTab = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (previewDocument) {
      window.open(previewDocument.url, '_blank');
    }
  };

  // Format date nicely
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle document deletion
  const handleDocumentDelete = (deletedDoc: Document) => {
    setDocuments(prev => prev.filter(doc => doc.id !== deletedDoc.id));
  };

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!previewDocument) return;
    
    e.preventDefault();
    
    switch (e.key) {
      case 'ArrowRight':
        const nextImageDocs = getImageDocuments();
        if (currentImageIndex < nextImageDocs.length - 1) {
          setCurrentImageIndex(currentImageIndex + 1);
          setPreviewDocument(nextImageDocs[currentImageIndex + 1]);
          setZoomLevel(1);
        }
        break;
      case 'ArrowLeft':
        const prevImageDocs = getImageDocuments();
        if (currentImageIndex > 0) {
          setCurrentImageIndex(currentImageIndex - 1);
          setPreviewDocument(prevImageDocs[currentImageIndex - 1]);
          setZoomLevel(1);
        }
        break;
      case 'Escape':
        closePreview();
        break;
      case '+':
        if (zoomLevel < 3) {
          setZoomLevel(prev => Math.min(prev + 0.25, 3));
        }
        break;
      case '-':
        if (zoomLevel > 0.5) {
          setZoomLevel(prev => Math.max(prev - 0.25, 0.5));
        }
        break;
      case '0':
        setZoomLevel(1);
        break;
      case 'f':
        setIsFullscreen(!isFullscreen);
        break;
    }
  }, [previewDocument, currentImageIndex, zoomLevel, isFullscreen]);

  // Add keyboard event listener when preview is open
  useEffect(() => {
    if (previewDocument) {
      window.addEventListener('keydown', handleKeyDown);
    }
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [previewDocument, handleKeyDown]);

  // If documents are still loading
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <div className="relative w-20 h-20 mb-6">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-4 shadow-lg">
              <FaRegFileAlt className="text-3xl text-blue-200" />
            </div>
          </div>
          <FaSpinner className="animate-spin text-5xl text-blue-500 absolute inset-0" />
        </div>
        <div className="text-center">
          <p className="text-xl font-semibold text-gray-700 mb-2">Đang tải tài liệu...</p>
          <p className="text-sm text-gray-500">Vui lòng đợi trong giây lát</p>
        </div>
      </div>
    );
  }

  // If there was an error
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-20 px-4">
        <div className="bg-gradient-to-br from-red-50 to-orange-50 rounded-3xl p-6 mb-6 shadow-lg">
          <FaExclamationTriangle className="text-4xl text-red-500" />
        </div>
        <h3 className="text-xl font-semibold text-gray-800 mb-3">Không thể tải tài liệu</h3>
        <p className="text-base text-gray-600 text-center max-w-lg mb-6 leading-relaxed">{error}</p>
        <button 
          onClick={() => window.location.reload()}
          className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          Thử lại
        </button>
      </div>
    );
  }

  // If no documents found
  if (documents.length === 0) {
    return (
      <div className="w-full max-w-5xl mx-auto">
        <div className="mb-8">
          <div className="flex justify-between items-start mb-4">

          </div>
        </div>
        
        <div className="py-20 text-center px-4">
          <div className="inline-flex p-8 rounded-3xl bg-gradient-to-br from-blue-50 to-indigo-100 mb-8 shadow-lg">
            <FaRegFileAlt className="text-5xl text-blue-500" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-800 mb-4">Chưa có tài liệu</h3>
          <p className="text-lg text-gray-500 max-w-lg mx-auto mb-8 leading-relaxed">
            Bạn chưa có tài liệu hoặc tệp tin nào được tải lên từ các đơn hàng trước đây. 
            Hãy bắt đầu bằng cách tải lên tài liệu đầu tiên của bạn.
          </p>

        </div>


      </div>
    );
  }

  return (
    <div className="w-full max-w-5xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-2xl font-semibold mb-2 text-gray-800">Tài liệu của tôi</h2>
            <p className="text-base text-gray-500">
              Xem, tải xuống và tải lên các tài liệu của bạn.
            </p>
          </div>

        </div>
      </div>
      
      {/* Document Upload Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">
                Loại tài liệu
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                Tải lên tài liệu
              </th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(DOCUMENT_TYPES).map(([docType, docInfo]) => (
              <DocumentUploadRow
                key={docType}
                documentType={docType}
                documentInfo={docInfo}
                onUpload={handleDocumentUploaded}
                customerId={customerInfo?.id || customerInfo?._id || ''}
                customerName={
                  customerInfo?.name || 
                  customerInfo?.fullName || 
                  (customerInfo?.personalDetails?.name) ||
                  (customerInfo?.personalDetails?.firstName && customerInfo?.personalDetails?.lastName 
                    ? `${customerInfo.personalDetails.firstName} ${customerInfo.personalDetails.lastName}`
                    : 'Khách hàng')
                }
                existingDocuments={documents}
              />
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Existing Documents Section */}
      {documents.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Tài liệu đã tải lên</h3>
          <div className="space-y-4">
            {documents.map((doc) => (
              <DocumentCard
                key={doc.id}
                document={doc}
                onPreview={openPreview}
                onDelete={handleDocumentDelete}
                getFileIcon={getFileIcon}
                formatFileSize={formatFileSize}
                formatDate={formatDate}
                getDocumentTypeLabel={getDocumentTypeLabel}
                isImageDocument={isImageDocument}
              />
            ))}
          </div>
        </div>
      )}
      
      {/* Image Preview Modal */}
      {previewDocument && isImageDocument(previewDocument) && (
        <div 
          className={`fixed inset-0 bg-black ${isFullscreen ? 'bg-opacity-100' : 'bg-opacity-90'} z-50 flex items-center justify-center p-0 animate-fadeIn`}
          onClick={closePreview}
        >
          {/* Image Navigation Controls */}
          <div className="fixed top-4 right-4 z-20 flex space-x-2">
            <button 
              onClick={openInNewTab}
              className="bg-white/80 hover:bg-white rounded-full p-2 shadow-lg text-gray-600 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Open in new tab"
              title="Mở trong tab mới"
            >
              <FaExternalLinkAlt size={16} />
            </button>
            <button 
              onClick={toggleFullscreen}
              className="bg-white/80 hover:bg-white rounded-full p-2 shadow-lg text-gray-600 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Toggle fullscreen"
              title={isFullscreen ? "Thoát toàn màn hình" : "Toàn màn hình"}
            >
              {isFullscreen ? <FaCompress size={16} /> : <FaExpand size={16} />}
            </button>
            <button 
              onClick={closePreview}
              className="bg-white/80 hover:bg-white rounded-full p-2 shadow-lg text-gray-600 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Close preview"
              title="Đóng"
            >
              <FaTimes size={16} />
            </button>
          </div>
          
          {/* Zoom Controls */}
          <div className="fixed bottom-4 right-4 z-20 flex items-center bg-white/80 p-1 rounded-full shadow-lg">
            <button 
              onClick={zoomOut}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:text-gray-400"
              disabled={zoomLevel <= 0.5}
              aria-label="Zoom out"
              title="Thu nhỏ"
            >
              <FaSearchMinus size={16} />
            </button>
            <button 
              onClick={resetZoom}
              className="px-2 py-1 mx-1 text-xs font-medium bg-gray-200 rounded-md hover:bg-gray-300"
              aria-label="Reset zoom"
              title="Khôi phục kích thước"
            >
              {Math.round(zoomLevel * 100)}%
            </button>
            <button 
              onClick={zoomIn}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:text-gray-400"
              disabled={zoomLevel >= 3}
              aria-label="Zoom in"
              title="Phóng to"
            >
              <FaSearchPlus size={16} />
            </button>
          </div>
          
          {/* Image Counter */}
          <div className="fixed bottom-4 left-4 z-20 bg-white/80 px-3 py-1 rounded-full text-sm font-medium text-gray-700 shadow-lg">
            {currentImageIndex + 1}/{getImageDocuments().length}
          </div>
          
          {!isFullscreen && (
            <div 
              className={`relative w-full max-w-6xl max-h-[90vh] flex items-center justify-center`}
              onClick={e => e.stopPropagation()}
            >
              {/* Image Navigation Buttons */}
              {currentImageIndex > 0 && (
                <button 
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg text-gray-600 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 z-10"
                  aria-label="Previous image"
                  title="Ảnh trước"
                >
                  <FaChevronLeft size={18} />
                </button>
              )}
              
              {currentImageIndex < getImageDocuments().length - 1 && (
                <button 
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg text-gray-600 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 z-10"
                  aria-label="Next image"
                  title="Ảnh tiếp theo"
                >
                  <FaChevronRight size={18} />
                </button>
              )}
              
              <div className="relative overflow-auto max-h-[90vh] max-w-full flex items-center justify-center p-4">
                <div className="transform-gpu" style={{ transform: `scale(${zoomLevel})`, transition: 'transform 0.2s ease-out' }}>
                  <img 
                    src={previewDocument.url} 
                    alt={previewDocument.name}
                    className="max-w-full object-contain select-none"
                    style={{ 
                      maxHeight: '80vh',
                      cursor: zoomLevel > 1 ? 'move' : 'default' 
                    }}
                  />
                </div>
              </div>
              
              {/* Document Info Panel */}
              <div className="absolute bottom-0 left-0 right-0 bg-white/90 backdrop-blur-sm py-3 px-4 flex flex-wrap items-center justify-between border-t border-gray-200">
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-1">{previewDocument.name}</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                      </svg>
                      <span>{formatFileSize(previewDocument.size)}</span>
                    </div>
                    
                    <div className="flex items-center">
                      <FaCalendarAlt className="mr-1 text-gray-400" />
                      <span>{formatDate(previewDocument.uploadDate)}</span>
                    </div>
                  </div>
                </div>
                
                <a 
                  href={previewDocument.url} 
                  download 
                  className="flex items-center px-4 py-2 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-md transition-colors duration-200"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <FaFileDownload className="mr-2" />
                  Tải xuống
                </a>
              </div>
            </div>
          )}
          
          {isFullscreen && (
            <div 
              className="w-full h-full flex items-center justify-center"
              onClick={e => e.stopPropagation()}
            >
              {/* Image Navigation Buttons */}
              {currentImageIndex > 0 && (
                <button 
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg text-gray-600 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 z-10"
                  aria-label="Previous image"
                  title="Ảnh trước"
                >
                  <FaChevronLeft size={18} />
                </button>
              )}
              
              {currentImageIndex < getImageDocuments().length - 1 && (
                <button 
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg text-gray-600 hover:text-gray-900 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 z-10"
                  aria-label="Next image"
                  title="Ảnh tiếp theo"
                >
                  <FaChevronRight size={18} />
                </button>
              )}
              
              <div className="relative overflow-auto w-full h-full flex items-center justify-center">
                <div className="transform-gpu" style={{ transform: `scale(${zoomLevel})`, transition: 'transform 0.2s ease-out' }}>
                  <img 
                    src={previewDocument.url} 
                    alt={previewDocument.name}
                    className="max-w-full max-h-full object-contain select-none"
                    style={{ cursor: zoomLevel > 1 ? 'move' : 'default' }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}



      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        .animate-fadeIn {
          animation: fadeIn 0.2s ease-in-out;
        }
        .line-clamp-1 {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        /* Hide scrollbar for image preview */
        .overflow-auto::-webkit-scrollbar {
          display: none;
        }
        .overflow-auto {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        
        /* Enhanced card animations */
        .document-card {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .document-card:hover {
          transform: translateY(-4px) scale(1.02);
        }
        
        /* Backdrop blur support */
        .backdrop-blur-sm {
          backdrop-filter: blur(4px);
          -webkit-backdrop-filter: blur(4px);
        }
      `}</style>
    </div>
  );
};

export default Documents;