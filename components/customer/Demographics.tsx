import React from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faGlobe, faLanguage } from '@fortawesome/free-solid-svg-icons';

const validationSchema = Yup.object({
  nationality: Yup.string().required('Vui lòng chọn quốc tịch'),
  preferredLanguage: Yup.string().required('Vui lòng chọn ngôn ngữ ưa thích'),
});

const Demographics: React.FC = () => {
  const formik = useFormik({
    initialValues: {
      nationality: '',
      preferredLanguage: '',
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form submitted:', values);
      // Handle form submission
    },
  });

  const languages = [
    { code: 'en', name: 'Tiếng An<PERSON>' },
    { code: 'es', name: '<PERSON>iế<PERSON> Tâ<PERSON>' },
    { code: 'fr', name: 'Tiếng <PERSON>' },
    { code: 'de', name: 'Tiếng <PERSON>' },
    { code: 'it', name: 'Tiếng Ý' },
    { code: 'pt', name: 'Tiếng Bồ Đào Nha' },
    { code: 'ru', name: 'Tiếng Nga' },
    { code: 'zh', name: 'Tiếng Trung Quốc' },
    { code: 'ja', name: 'Tiếng Nhật' },
    { code: 'ko', name: 'Tiếng Hàn' },
    { code: 'vi', name: 'Tiếng Việt' },
  ];

  const countries = [
    { code: 'VN', name: 'Việt Nam' },
    { code: 'US', name: 'Hoa Kỳ' },
    { code: 'GB', name: 'Vương Quốc Anh' },
    { code: 'CA', name: 'Canada' },
    { code: 'AU', name: 'Úc' },
    { code: 'DE', name: 'Đức' },
    { code: 'FR', name: 'Pháp' },
    { code: 'IT', name: 'Ý' },
    { code: 'ES', name: 'Tây Ban Nha' },
    { code: 'JP', name: 'Nhật Bản' },
    { code: 'CN', name: 'Trung Quốc' },
  ];

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Thông Tin Nhân Khẩu</h2>
      <form onSubmit={formik.handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">Quốc Tịch</label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faGlobe} className="text-gray-400" />
            </div>
            <select
              name="nationality"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.nationality}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Chọn quốc tịch của bạn</option>
              {countries.map((country) => (
                <option key={country.code} value={country.code}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>
          {formik.touched.nationality && formik.errors.nationality && (
            <div className="text-red-500 text-sm mt-1">{formik.errors.nationality}</div>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Ngôn Ngữ Ưa Thích</label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faLanguage} className="text-gray-400" />
            </div>
            <select
              name="preferredLanguage"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.preferredLanguage}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Chọn ngôn ngữ ưa thích của bạn</option>
              {languages.map((language) => (
                <option key={language.code} value={language.code}>
                  {language.name}
                </option>
              ))}
            </select>
          </div>
          {formik.touched.preferredLanguage && formik.errors.preferredLanguage && (
            <div className="text-red-500 text-sm mt-1">{formik.errors.preferredLanguage}</div>
          )}
        </div>

        <div className="mt-6">
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Lưu Tùy Chọn
          </button>
        </div>
      </form>
    </div>
  );
};

export default Demographics; 