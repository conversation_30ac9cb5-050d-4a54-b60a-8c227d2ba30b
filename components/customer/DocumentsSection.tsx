import React, { useState, useEffect } from 'react';
import {
  FaCheck,
  FaTimes,
  FaClock,
  FaExclamationTriangle,
  FaFileAlt,
  FaUpload,
  FaSpinner
} from 'react-icons/fa';
import Documents from './Documents';
import { getCustomerInfo, isCustomerAuthenticated } from '../../utils/customerAuth';
import { getCustomerDocuments } from '../../utils/customerAPI';

// Import document requirements configuration
import {
  DOCUMENT_STATUS,
  getRequiredDocumentsForCustomer,
  getDocumentComplianceStatus
} from '../../config/documentRequirements';

interface DocumentsSectionProps {
  isActive: boolean;
  toggleSection: () => void;
}

interface DocumentStatus {
  type: string;
  label: string;
  description: string;
  required: boolean;
  status: 'missing' | 'pending' | 'approved' | 'rejected' | 'expired';
  uploadDate?: string;
  expiryDate?: string;
  hasDocument: boolean;
  priority: 'critical' | 'high' | 'medium' | 'low';
}

// Document Status Card Component
const DocumentStatusCard: React.FC<{ document: DocumentStatus }> = ({ document }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <FaCheck className="text-green-500" />;
      case 'rejected':
        return <FaTimes className="text-red-500" />;
      case 'pending':
        return <FaClock className="text-yellow-500" />;
      case 'expired':
        return <FaExclamationTriangle className="text-orange-500" />;
      case 'missing':
        return <FaFileAlt className="text-gray-400" />;
      default:
        return <FaFileAlt className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'rejected':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'pending':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'expired':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'missing':
        return 'bg-gray-50 border-gray-200 text-gray-600';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    const statusLabels = DOCUMENT_STATUS[status];
    return statusLabels ? statusLabels.label : status;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  return (
    <div className={`border rounded-lg p-4 transition-all duration-200 hover:shadow-md ${
      document.required ? 'border-l-4 border-l-red-400' : 'border-l-4 border-l-blue-400'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <div className="flex items-center space-x-2">
              {getStatusIcon(document.status)}
              <h5 className="font-semibold text-gray-900">{document.label}</h5>
            </div>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(document.priority)}`}>
              {document.priority === 'critical' ? 'Quan trọng' :
               document.priority === 'high' ? 'Cao' :
               document.priority === 'medium' ? 'Trung bình' : 'Thấp'}
            </span>
          </div>

          <p className="text-sm text-gray-600 mb-3">{document.description}</p>

          <div className="flex items-center justify-between">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(document.status)}`}>
              {getStatusText(document.status)}
            </span>

            {document.uploadDate && (
              <span className="text-xs text-gray-500">
                Tải lên: {formatDate(document.uploadDate)}
              </span>
            )}
          </div>

          {document.status === 'expired' && document.expiryDate && (
            <div className="mt-2 text-xs text-orange-600 bg-orange-50 p-2 rounded">
              Hết hạn: {formatDate(document.expiryDate)}
            </div>
          )}

          {document.status === 'rejected' && (
            <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
              Tài liệu bị từ chối. Vui lòng tải lên lại với thông tin chính xác.
            </div>
          )}

          {document.status === 'missing' && document.required && (
            <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
              Tài liệu bắt buộc chưa được tải lên.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const DocumentsSection: React.FC<DocumentsSectionProps> = ({ isActive, toggleSection }) => {
  const [documentStatuses, setDocumentStatuses] = useState<DocumentStatus[]>([]);
  const [complianceStatus, setComplianceStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [customerInfo, setCustomerInfo] = useState<any>(null);
  const [showUploadInterface, setShowUploadInterface] = useState(false);

  useEffect(() => {
    if (isActive) {
      fetchDocumentStatus();
    }
  }, [isActive]);

  const fetchDocumentStatus = async () => {
    setLoading(true);
    try {
      // Check if user is authenticated
      if (!isCustomerAuthenticated()) {
        setLoading(false);
        return;
      }

      const customerData = getCustomerInfo("");
      if (!customerData) {
        setLoading(false);
        return;
      }

      setCustomerInfo(customerData);

      // Get customer documents
      const response = await getCustomerDocuments();
      const customerDocs = response.success ? (response.documents || []) : [];

      // Get required documents for this customer
      const requirements = getRequiredDocumentsForCustomer(customerData);
      const compliance = getDocumentComplianceStatus(customerData);

      setComplianceStatus(compliance);

      // Create document status array
      const statuses: DocumentStatus[] = [];

      // Process required documents
      requirements.required.forEach(req => {
        const existingDoc = customerDocs.find((doc: any) => doc.documentType === req.type);
        const status = getDocumentStatus(existingDoc, req);

        statuses.push({
          type: req.type,
          label: req.label,
          description: req.description,
          required: true,
          status,
          uploadDate: existingDoc?.uploadDate,
          expiryDate: existingDoc?.expiryDate,
          hasDocument: !!existingDoc,
          priority: req.priority
        });
      });

      // Process recommended documents
      requirements.recommended.forEach(req => {
        const existingDoc = customerDocs.find((doc: any) => doc.documentType === req.type);
        const status = getDocumentStatus(existingDoc, req);

        statuses.push({
          type: req.type,
          label: req.label,
          description: req.description,
          required: false,
          status,
          uploadDate: existingDoc?.uploadDate,
          expiryDate: existingDoc?.expiryDate,
          hasDocument: !!existingDoc,
          priority: req.priority
        });
      });

      // Sort by priority and requirement status
      statuses.sort((a, b) => {
        if (a.required !== b.required) return a.required ? -1 : 1;

        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

      setDocumentStatuses(statuses);
    } catch (error) {
      console.error('Error fetching document status:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDocumentStatus = (existingDoc: any, requirement: any) => {
    if (!existingDoc) return 'missing';

    // Check if document is expired
    if (requirement.expirationTracking && requirement.validityPeriod) {
      const uploadDate = new Date(existingDoc.uploadDate);
      const expirationDate = new Date(uploadDate.getTime() + (requirement.validityPeriod * 24 * 60 * 60 * 1000));
      if (expirationDate < new Date()) return 'expired';
    }

    return existingDoc.status || 'pending';
  };

  const getComplianceColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div
        onClick={toggleSection}
        className="p-4 border-b border-gray-100 flex justify-between items-center cursor-pointer hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
          </svg>
          <h2 className="text-xl font-medium text-gray-800">Yêu cầu tài liệu</h2>
          {complianceStatus && (
            <span className={`ml-3 text-sm font-medium ${getComplianceColor(complianceStatus.percentage)}`}>
              {complianceStatus.percentage}%
            </span>
          )}
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isActive ? 'transform rotate-180' : ''}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </div>
      <div
        className={`transition-all duration-300 ease-in-out ${
          isActive ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'
        }`}
        style={{
          overflow: isActive ? 'visible' : 'hidden'
        }}
      >
        <div className="p-1 sm:p-4 md:p-6">
          {!showUploadInterface ? (
            <div className="space-y-6">
              {/* Compliance Overview */}
              {complianceStatus && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">Tình trạng hoàn thành</h3>
                      <p className="text-sm text-gray-600">
                        {complianceStatus.completed}/{complianceStatus.total} tài liệu bắt buộc đã hoàn thành
                      </p>
                    </div>
                    <div className="text-right">
                      <div className={`text-2xl font-bold ${getComplianceColor(complianceStatus.percentage)}`}>
                        {complianceStatus.percentage}%
                      </div>
                      <div className="w-24 bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            complianceStatus.percentage >= 90 ? 'bg-green-500' :
                            complianceStatus.percentage >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${complianceStatus.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Document Status List */}
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <FaSpinner className="animate-spin text-2xl text-blue-500 mr-3" />
                  <span className="text-gray-600">Đang tải trạng thái tài liệu...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  {documentStatuses.length > 0 ? (
                    <>
                      {/* Required Documents */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                          <FaExclamationTriangle className="text-red-500 mr-2" />
                          Tài liệu bắt buộc
                        </h4>
                        <div className="grid gap-3">
                          {documentStatuses.filter(doc => doc.required).map((doc) => (
                            <DocumentStatusCard key={doc.type} document={doc} />
                          ))}
                        </div>
                      </div>

                      {/* Recommended Documents */}
                      {documentStatuses.some(doc => !doc.required) && (
                        <div>
                          <h4 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                            <FaFileAlt className="text-blue-500 mr-2" />
                            Tài liệu khuyến nghị
                          </h4>
                          <div className="grid gap-3">
                            {documentStatuses.filter(doc => !doc.required).map((doc) => (
                              <DocumentStatusCard key={doc.type} document={doc} />
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <FaFileAlt className="text-4xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-600">Không có yêu cầu tài liệu nào</p>
                    </div>
                  )}
                </div>
              )}

              {/* Upload Interface Toggle */}
              <div className="border-t pt-4">
                <button
                  onClick={() => setShowUploadInterface(true)}
                  className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <FaUpload className="mr-2" />
                  Tải lên và quản lý tài liệu
                </button>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">Quản lý tài liệu</h3>
                <button
                  onClick={() => setShowUploadInterface(false)}
                  className="px-4 py-2 text-blue-600 hover:text-blue-800 transition-colors"
                >
                  ← Quay lại trạng thái
                </button>
              </div>
              <Documents />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentsSection;