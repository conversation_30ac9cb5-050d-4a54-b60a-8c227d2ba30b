import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp, faExternalLinkAlt } from '@fortawesome/free-solid-svg-icons';

interface GoogleSheetsDetailsProps {
  metadata: {
    type: string;
    sheetsId?: string;
    assignedColumns?: number[];
    assignedColumnValues?: {
      columnIndex: number;
      columnLetter: string;
      value: string;
    }[];
    assignedSku?: string;
  };
}

const GoogleSheetsDetails: React.FC<GoogleSheetsDetailsProps> = ({ metadata }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!metadata || metadata.type !== 'google_sheets_assignment') {
    return null;
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
      <button
        onClick={toggleExpanded}
        className="flex items-center justify-between w-full text-left focus:outline-none"
      >
        <div className="flex items-center">
          <FontAwesomeIcon 
            icon={faExternalLinkAlt} 
            className="text-blue-600 mr-2" 
          />
          <span className="font-medium text-blue-800">
            Product Details
          </span>
        </div>
        <FontAwesomeIcon
          icon={isExpanded ? faChevronUp : faChevronDown}
          className="text-blue-600"
        />
      </button>

      {isExpanded && (
        <div className="mt-4 space-y-3">
          {/* SKU Information */}
          {metadata.assignedSku && (
            <div className="bg-white rounded-md p-3 border border-blue-100">
              <div className="text-sm font-medium text-gray-700 mb-1">
                Product SKU
              </div>
              <div className="text-gray-900 font-mono text-sm">
                {metadata.assignedSku}
              </div>
            </div>
          )}

          {/* Assigned Values */}
          {metadata.assignedColumnValues && metadata.assignedColumnValues.length > 0 && (
            <div className="bg-white rounded-md p-3 border border-blue-100">
              <div className="text-sm font-medium text-gray-700 mb-2">
                Product Information
              </div>
              <div className="space-y-2">
                {metadata.assignedColumnValues.map((item, index) => (
                  <div key={index} className="flex flex-col sm:flex-row sm:items-center">
                    <div className="text-xs text-gray-500 sm:w-16 mb-1 sm:mb-0">
                      Column {item.columnLetter}
                    </div>
                    <div className="font-mono text-sm text-gray-900 bg-gray-50 px-2 py-1 rounded border">
                      {item.value}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Sheet Reference */}
          {metadata.sheetsId && (
            <div className="bg-white rounded-md p-3 border border-blue-100">
              <div className="text-sm font-medium text-gray-700 mb-1">
                Reference ID
              </div>
              <div className="text-xs text-gray-500 font-mono break-all">
                {metadata.sheetsId}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GoogleSheetsDetails;