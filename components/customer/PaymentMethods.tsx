import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCreditCard, faWallet, faHistory, faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';

interface PaymentMethod {
  id: string;
  type: 'card' | 'wallet';
  last4: string;
  expiryDate?: string;
  isDefault: boolean;
}

interface Transaction {
  id: string;
  date: string;
  amount: number;
  description: string;
  status: 'completed' | 'pending' | 'failed';
}

const PaymentMethods: React.FC = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'card',
      last4: '4242',
      expiryDate: '12/25',
      isDefault: true,
    },
    {
      id: '2',
      type: 'wallet',
      last4: '1234',
      isDefault: false,
    },
  ]);

  const [transactions, setTransactions] = useState<Transaction[]>([
    {
      id: 'TXN001',
      date: '2024-03-20',
      amount: 50,
      description: 'Top-up recharge',
      status: 'completed',
    },
    {
      id: 'TXN002',
      date: '2024-03-18',
      amount: 25,
      description: 'Data plan purchase',
      status: 'completed',
    },
  ]);

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Payment & Billing</h2>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Add Payment Method
        </button>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-medium mb-4">Saved Payment Methods</h3>
        <div className="space-y-4">
          {paymentMethods.map((method) => (
            <div key={method.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex items-center space-x-3">
                  <FontAwesomeIcon
                    icon={method.type === 'card' ? faCreditCard : faWallet}
                    className="text-gray-400 text-xl"
                  />
                  <div>
                    <h4 className="font-medium">
                      {method.type === 'card' ? 'Credit Card' : 'Digital Wallet'}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {method.type === 'card' ? `•••• ${method.last4}` : `Wallet ${method.last4}`}
                      {method.expiryDate && ` • Expires ${method.expiryDate}`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  {method.isDefault && (
                    <span className="px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full">
                      Default
                    </span>
                  )}
                  <button className="text-red-600 hover:text-red-800">
                    <FontAwesomeIcon icon={faTrash} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Transaction History</h3>
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div key={transaction.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex items-center space-x-3">
                  <FontAwesomeIcon icon={faHistory} className="text-gray-400" />
                  <div>
                    <h4 className="font-medium">${transaction.amount}</h4>
                    <p className="text-sm text-gray-500">
                      {transaction.description} • {transaction.date}
                    </p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-sm font-medium ${getStatusColor(transaction.status)}`}>
                  {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6">
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FontAwesomeIcon icon={faCreditCard} className="text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Your payment information is encrypted and secure. We never store your full card details.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethods; 