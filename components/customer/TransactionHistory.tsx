import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShoppingBag, faTruck, faCheckCircle, faClock, faTimesCircle, faFileInvoice, faShoppingCart } from '@fortawesome/free-solid-svg-icons';
import SocialMediaDisplay from '../SocialMediaDisplay';

// Interface for purchase data
interface PurchaseItem {
  id: string;
  date: string;
  amount: number;
  status: string;
  products?: string[];
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface TransactionHistoryProps {
  customerId?: string | null;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ customerId }) => {
  const router = useRouter();
  const { store: storeParam } = router.query;
  const [purchases, setPurchases] = useState<PurchaseItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);

  useEffect(() => {
    const fetchCustomerData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get customer ID from props or localStorage
        let id = customerId;
        if (!id) {
          // First check store-specific customer data
          const storeIdParam = storeParam as string;
          const storeSpecificData = storeIdParam ? localStorage.getItem(`${storeIdParam}_customerData`) : null;
          
          // Then check the generic customer data as fallback
          const genericData = localStorage.getItem('customerData');
          
          const customerData = storeSpecificData || genericData;
          
          if (customerData) {
            try {
              const customer = JSON.parse(customerData);
              id = customer.id;
            } catch (err) {
              console.error('Lỗi khi đọc dữ liệu khách hàng:', err);
              throw new Error('Không thể đọc thông tin khách hàng. Vui lòng đăng nhập lại.');
            }
          }
        }

        if (!id) {
          throw new Error('Không tìm thấy ID khách hàng. Vui lòng đăng nhập lại.');
        }

        console.log(`Fetching purchase history for user: ${id} in store context: ${storeParam || 'all stores'}`);

        // Fetch customer data to get purchase history
        const customerResponse = await axios.get(`/api/customers/${id}`);

        // Process purchase history data
        const customerData = customerResponse.data;
        if (customerData.purchaseHistory && Array.isArray(customerData.purchaseHistory)) {
          setPurchases(customerData.purchaseHistory);
        } else {
          setPurchases([]);
        }
      } catch (err) {
        console.error('Lỗi khi tải dữ liệu:', err);
        setError((err as Error).message || 'Có lỗi xảy ra. Vui lòng thử lại sau.');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomerData();
  }, [customerId, storeParam]);

  useEffect(() => {
    const fetchCartData = async () => {
      try {
        // Get cart data from localStorage using the SHOPME_ key
        const storageData = localStorage.getItem('SHOPME_');
        if (storageData) {
          const data = JSON.parse(storageData);
          if (data.cart && Array.isArray(data.cart)) {
            setCartItems(data.cart);
          }
        }
      } catch (error) {
        console.error('Error fetching cart data:', error);
      }
    };

    fetchCartData();
  }, []);  // Keep dependency array empty since we're using a constant key

  // Utility functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  const formatCurrency = (amount: number, currency: string = 'VND') => {
    if (!currency) {
      currency = 'VND';
    }
    
    if (currency === 'NT') {
      return `${amount} NT$`;
    }
    
    try {
      const formatter = new Intl.NumberFormat(
        currency === 'VND' ? 'vi-VN' : 'en-US', 
        { 
          style: 'currency', 
          currency: currency,
          maximumFractionDigits: 2
        }
      );
      return formatter.format(amount);
    } catch (err) {
      console.error('Error formatting currency:', err);
      // Fallback formatting
      return `${amount.toLocaleString()} ${currency}`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'processing':
        return 'text-yellow-600 bg-yellow-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      case 'shipped':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
      case 'completed':
        return faCheckCircle;
      case 'processing':
        return faClock;
      case 'cancelled':
        return faTimesCircle;
      case 'shipped':
        return faTruck;
      default:
        return faShoppingBag;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'processing': 'Đang Xử Lý',
      'shipped': 'Đang Giao Hàng',
      'delivered': 'Đã Giao',
      'completed': 'Hoàn Thành',
      'cancelled': 'Đã Hủy',
      'refunded': 'Đã Hoàn Tiền'
    };
    
    return statusMap[status.toLowerCase()] || 'Không Xác Định';
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <p className="text-gray-500">Đang tải dữ liệu mua hàng...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FontAwesomeIcon icon={faTimesCircle} className="text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">
                <strong>Lỗi: </strong>{error}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (purchases.length === 0) {
    return (
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <p className="text-gray-500 mb-6">Bạn chưa có giao dịch mua hàng nào.</p>
          
          {cartItems.length > 0 && (
            <div className="mt-4">
              <div className="bg-blue-50 rounded-lg p-4 mb-4">
                <h3 className="text-blue-700 font-medium mb-3 flex items-center justify-center">
                  Giỏ hàng hiện tại của bạn:
                </h3>
                <ul className="space-y-2 text-left">
                  {cartItems.map((item) => (
                    <li key={item.id} className="flex justify-between items-center text-gray-600">
                      <span>{item.name} x{item.quantity}</span>
                      <span className="font-medium">{(item.price * item.quantity)}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-4 pt-3 border-t border-blue-200">
                  <p className="text-blue-700 font-medium mb-3">
                    Tổng cộng: {(cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0))}
                  </p>
                  <a
                    href={`/${storeParam}/cart`}
                    className="inline-block px-6 py-2 border-2 border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 transition-colors"
                  >
                    Tiến hành thanh toán
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
      
      <div className="space-y-4">
        {purchases.map((purchase) => (
          <div key={purchase.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <p className="text-sm text-gray-500">Mua hàng #{purchase.id}</p>
                <p className="text-sm text-gray-500">{formatDate(purchase.date)}</p>
              </div>
              <div className="text-right">
                <p className="font-semibold">{formatCurrency(purchase.amount)}</p>
                <span className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusColor(purchase.status)}`}>
                  <FontAwesomeIcon icon={getStatusIcon(purchase.status)} className="mr-1" />
                  {getStatusText(purchase.status)}
                </span>
              </div>
            </div>
            
            {purchase.products && purchase.products.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <p className="text-sm font-medium mb-1">Sản phẩm:</p>
                <ul className="text-sm text-gray-600">
                  {purchase.products.map((product, index) => (
                    <li key={index} className="py-1">• {product}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6">
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FontAwesomeIcon icon={faShoppingBag} className="text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Cần trợ giúp với đơn hàng của bạn? Liên hệ với đội hỗ trợ khách hàng của chúng tôi.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionHistory;