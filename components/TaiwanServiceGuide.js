import React from 'react';

const TaiwanServiceGuide = () => {
  return (
    <div className="my-4 px-4 md:px-0 md:my-8">
      <div className="bg-blue-500 py-4 md:py-6 px-4 mb-4 md:mb-6 rounded-lg shadow-md">
        <h2 className="text-white text-2xl md:text-3xl font-bold text-center">DỊCH VỤ TẠI ĐÀI LOAN</h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Service 1 */}
        <div>
          <div className="bg-yellow-400 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">ĐỔI BẰNG LÁI XE VIỆT NAM SANG ĐÀI LOAN</h3>
          </div>
          <div className="bg-yellow-100 rounded-b-lg p-4 shadow-md">
            <p className="mb-2"><PERSON><PERSON><PERSON> chụp trước cho công ty mình bằng lái để công ty mình kiểm tra có đổi được không nhé.</p>
            <p className="font-bold mb-2">■Hồ sơ bằng lái:</p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Bản gốc hộ chiếu VN</li>
              <li>Bằng lái xe bản gốc</li>
              <li>Thẻ cư trú</li>
              <li>Giấy khám sức khỏe</li>
              <li>Ảnh thẻ 3*4: 3 cái</li>
              <li>Ảnh trên giấy khám sức khỏe phải giống ảnh làm hồ sơ.</li>
            </ol>
          </div>
        </div>

        {/* Service 2 */}
        <div>
          <div className="bg-green-400 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">ĐỔI BẰNG LÁI XE ĐÀI LOAN SANG VIỆT NAM</h3>
          </div>
          <div className="bg-green-100 rounded-b-lg p-4 shadow-md">
            <ul className="list-disc pl-5 space-y-1">
              <li>Bằng đài gốc</li>
              <li>Thẻ cư trú photo</li>
              <li>Hộ chiếu photo</li>
            </ul>
          </div>
        </div>

        {/* Service 3 */}
        <div>
          <div className="bg-green-500 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">BẰNG LÁI ĐẠI ĐỘI BẰNG QUỐC TẾ</h3>
          </div>
          <div className="bg-green-100 rounded-b-lg p-4 shadow-md">
            <ol className="list-decimal pl-5 space-y-1">
              <li>1-thẻ cư trú (bản gốc)</li>
              <li>2- ảnh 2 năm gần đây: 2 ảnh</li>
              <li>3-bằng gốc</li>
              <li>4- hộ chiếu gốc</li>
            </ol>
          </div>
        </div>

        {/* Service 4 */}
        <div>
          <div className="bg-cyan-500 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">HỘ CHIẾU</h3>
          </div>
          <div className="bg-cyan-100 rounded-b-lg p-4 shadow-md">
            <ol className="list-decimal pl-5 space-y-1">
              <li>Hộ chiếu cũ (gốc)</li>
              <li>Giấy phép làm việc (bản hợp đồng - chụp ảnh hoặc photo)/thẻ học sinh</li>
              <li>Thẻ cư trú (photo)</li>
              <li>3 ảnh thẻ 4*6</li>
              <li>Thông tin ngày tháng năm sinh và tên bố mẹ</li>
              <li>Thông tin giao nhận của bạn (tên, địa chỉ, Sđt) để công ty gửi hộ chiếu mới khi hồ sơ hoàn tất.</li>
            </ol>
          </div>
        </div>

        {/* Service 5 */}
        <div>
          <div className="bg-cyan-400 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">HỘ SƠ TƯ TUẤT ĐÀI LOAN</h3>
          </div>
          <div className="bg-cyan-100 rounded-b-lg p-4 shadow-md">
            <ol className="list-decimal pl-5 space-y-1">
              <li>1 - giấy khai sinh gốc</li>
              <li>2- giấy chứng tử gốc</li>
              <li>3 - giấy xóa cư trú gốc</li>
              <li>4- ảnh chụp cccd 2 mặt (ng mất và lao động)</li>
              <li>5- ảnh chụp hộ chiếu, visa có dấu nhập cảnh</li>
              <li>6- số đt của ng nhà tại VN</li>
            </ol>
          </div>
        </div>

        {/* Service 6 */}
        <div>
          <div className="bg-blue-400 text-white rounded-t-lg p-4 shadow-md">
            <h3 className="font-bold text-lg text-center">HỘ SƠ THAI SẢN ĐÀI LOAN</h3>
          </div>
          <div className="bg-blue-100 rounded-b-lg p-4 shadow-md">
            <ol className="list-decimal pl-5 space-y-1">
              <li>Hộ chiếu của mẹ (bản gốc)</li>
              <li>Thẻ cư trú của mẹ (bản gốc)</li>
              <li>Giấy khai sinh em bé</li>
              <li>Số tài khoản của mẹ</li>
              <li>Cmnd của mẹ (photo)</li>
              <li>Cmnd hoặc hộ chiếu của bố (photo)</li>
              <li>Giấy khám thai tại đl (giấy chẩn đoán ghi rõ tuần thai)</li>
              <li>Địa chỉ và sđt tại vn hoặc đl</li>
            </ol>
          </div>
        </div>
      </div>
      
      {/* Mobile help button */}
      <div className="fixed bottom-6 right-6 md:hidden">
        <button className="bg-blue-500 text-white p-4 rounded-full shadow-lg transform transition-transform hover:scale-110">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default TaiwanServiceGuide; 