import React, { useState } from 'react';
import { DisplayMediumNoBox } from "../components";

const PaymentQRDisplay = ({ paymentQRs = [] }) => {
  const [enlargedQR, setEnlargedQR] = useState(null);

  const handleQRClick = (imageSrc, name) => {
    setEnlargedQR({ src: imageSrc, name });
  };

  const closeEnlargedQR = () => {
    setEnlargedQR(null);
  };

  const downloadQR = (imageSrc, name) => {
    // Create a temporary anchor element
    const link = document.createElement('a');
    link.href = imageSrc;
    link.download = `${name.toLowerCase().replace(/\s+/g, '-')}-payment-qr.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // If no payment QRs, return nothing
  if (!Array.isArray(paymentQRs) || paymentQRs.length === 0) {
    return null;
  }

  return (
    <div className="my-4">
      <h3 className="text-lg font-semibold text-center mb-3">Thanh toán</h3>
      
      {paymentQRs.map((countryGroup, countryIndex) => (
        <div key={`country-group-${countryIndex}`} className="mb-3">
          <h4 className="text-sm font-medium mb-2 text-center capitalize">
            {countryGroup.country || "Other"}
          </h4>
          <div className="flex flex-wrap justify-center gap-3 p-3 bg-gray-50 rounded-lg">
            {Array.isArray(countryGroup.qrs) && countryGroup.qrs.map((qrItem, qrIndex) => (
              <div 
                key={`qr-${countryIndex}-${qrIndex}`}
                className="flex flex-col items-center text-center bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden border border-gray-200"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="w-20 h-20 relative p-2">
                  <img 
                    src={qrItem.qr_image || "/shopme.logo.2.webp"} 
                    alt={`Payment QR ${qrIndex + 1}`} 
                    className="object-contain w-full h-full"
                    onClick={() => handleQRClick(qrItem.qr_image || "/shopme.logo.2.webp", qrItem.qr_text || `Thanh toán ${countryGroup.country} ${qrIndex + 1}`)}
                  />
                </div>
                <div className="w-full bg-gradient-to-r from-green-500 to-teal-600 p-1.5">
                  <p className="text-xs font-medium text-white truncate">
                    {qrItem.qr_text || `Thanh toán ${qrIndex + 1}`}
                  </p>
                </div>
                <div className="flex justify-center w-full divide-x divide-gray-200">
                  <button 
                    className="flex-1 py-1 text-xs text-gray-600 hover:bg-gray-50 transition-colors"
                    onClick={() => handleQRClick(qrItem.qr_image || "/shopme.logo.2.webp", qrItem.qr_text || `Thanh toán ${countryGroup.country} ${qrIndex + 1}`)}
                  >
                    <span className="flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      Xem
                    </span>
                  </button>
                  <button 
                    className="flex-1 py-1 text-xs text-gray-600 hover:bg-gray-50 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation(); 
                      downloadQR(qrItem.qr_image || "/shopme.logo.2.webp", qrItem.qr_text || `Thanh toán ${countryGroup.country} ${qrIndex + 1}`);
                    }}
                  >
                    <span className="flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Tải
                    </span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Enlarged QR Modal */}
      {enlargedQR && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50" onClick={closeEnlargedQR}>
          <div className="bg-white p-4 rounded-lg max-w-sm w-full mx-4" onClick={(e) => e.stopPropagation()}>
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-lg font-semibold">{enlargedQR.name}</h4>
              <button 
                className="text-gray-500 hover:text-gray-800" 
                onClick={closeEnlargedQR}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex justify-center">
              <img 
                src={enlargedQR.src} 
                alt={`${enlargedQR.name} QR Code`} 
                className="max-w-full max-h-80 object-contain" 
              />
            </div>
            <div className="flex justify-center mt-4">
              <button 
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 flex items-center"
                onClick={(e) => {
                  e.stopPropagation(); 
                  downloadQR(enlargedQR.src, enlargedQR.name);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Tải mã QR
              </button>
            </div>
            <div className="text-center mt-3">
              <p className="text-sm text-gray-600">Quét mã QR để thanh toán</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentQRDisplay; 