import React, { useState } from 'react';
import { DisplayMediumNoBox } from "../components";
import Image from 'next/image';

const SocialMediaDisplay = ({ store = {} }) => {
  const social = store.social || [];
  const shopLinkQRCode = store.shopLinkQRCode || null;
  const [enlargedQR, setEnlargedQR] = useState(null);

  // If no social media accounts and no shop QR code, return nothing
  if ((!Array.isArray(social) || social.length === 0) && !shopLinkQRCode) {
    return null;
  }

  const handleQRClick = (imageSrc, name) => {
    setEnlargedQR({ src: imageSrc, name });
  };

  const closeEnlargedQR = () => {
    setEnlargedQR(null);
  };

  const downloadQR = (imageSrc, name) => {
    // Create a temporary anchor element
    const link = document.createElement('a');
    link.href = imageSrc;
    link.download = `${name.toLowerCase().replace(/\s+/g, '-')}-qr-code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Get social media icon based on network name
  const getSocialIcon = (network) => {
    switch (network?.toLowerCase()) {
      case 'facebook':
        return 'fa-facebook-f';
      case 'whatsapp':
        return 'fa-whatsapp';
      case 'line':
        return 'fa-line';
      case 'zalo':
        return 'fa-comment-dots'; // Using a similar icon for Zalo
      default:
        return 'fa-share-alt';
    }
  };

  return (
    <div className="my-4">
      <h3 className="text-lg font-semibold text-center mb-3">Liên hệ</h3>
      <div className="flex flex-wrap justify-center gap-3 p-3 bg-gray-50 rounded-lg">
        {/* Shop QR Code */}
        {shopLinkQRCode && (
          <div className="flex flex-col items-center text-center bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden border border-gray-200" onClick={(e) => e.stopPropagation()}>
            <div className="w-20 h-20 relative p-2">
              <img 
                src={shopLinkQRCode} 
                alt="Website QR" 
                className="object-contain w-full h-full"
                onClick={() => handleQRClick(shopLinkQRCode, "Website")}
              />
            </div>
            <div className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 p-1.5">
              <p className="text-xs font-medium text-white truncate">Website</p>
            </div>
            <div className="flex justify-center w-full divide-x divide-gray-200">
              <button 
                className="flex-1 py-1 text-xs text-gray-600 hover:bg-gray-50 transition-colors"
                onClick={() => handleQRClick(shopLinkQRCode, "Website")}
              >
                <span className="flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Xem
                </span>
              </button>
              <button 
                className="flex-1 py-1 text-xs text-gray-600 hover:bg-gray-50 transition-colors"
                onClick={(e) => {
                  e.stopPropagation(); 
                  downloadQR(shopLinkQRCode, "Website");
                }}
              >
                <span className="flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Tải
                </span>
              </button>
            </div>
          </div>
        )}
        
        {/* Social Media QRs */}
        {Array.isArray(social) && social.map((socialItem, index) => (
          <div 
            key={`social-${index}`}
            className="flex flex-col items-center text-center bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden border border-gray-200" 
            onClick={(e) => e.stopPropagation()}
          >
            {socialItem.qr_image ? (
              <>
                <div className="w-20 h-20 relative p-2">
                  <img 
                    src={socialItem.qr_image} 
                    alt={`${socialItem.network || "Contact"} QR`} 
                    className="object-contain w-full h-full"
                    onClick={() => handleQRClick(socialItem.qr_image, socialItem.network || "Contact")}
                  />
                </div>
                <div className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 p-1.5">
                  <p className="text-xs font-medium text-white truncate capitalize">
                    {socialItem.network || "Contact"}
                    {socialItem.handle ? `: ${socialItem.handle}` : ''}
                  </p>
                </div>
                <div className="flex justify-center w-full divide-x divide-gray-200">
                  <button 
                    className="flex-1 py-1 text-xs text-gray-600 hover:bg-gray-50 transition-colors"
                    onClick={() => handleQRClick(socialItem.qr_image, socialItem.network || "Contact")}
                  >
                    <span className="flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      Xem
                    </span>
                  </button>
                  <button 
                    className="flex-1 py-1 text-xs text-gray-600 hover:bg-gray-50 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation(); 
                      downloadQR(socialItem.qr_image, socialItem.network || "Contact");
                    }}
                  >
                    <span className="flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Tải
                    </span>
                  </button>
                </div>
              </>
            ) : (
              <div className="w-20 h-20 flex items-center justify-center bg-gray-100 rounded">
                <i className={`fas ${getSocialIcon(socialItem.network)} text-xl`}></i>
                <p className="text-xs mt-1 capitalize">{socialItem.network || "Contact"}</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Enlarged QR Modal */}
      {enlargedQR && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50" onClick={closeEnlargedQR}>
          <div className="bg-white p-4 rounded-lg max-w-sm w-full mx-4" onClick={(e) => e.stopPropagation()}>
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-lg font-semibold capitalize">{enlargedQR.name}</h4>
              <button 
                className="text-gray-500 hover:text-gray-800" 
                onClick={closeEnlargedQR}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex justify-center">
              <img 
                src={enlargedQR.src} 
                alt={`${enlargedQR.name} QR Code`} 
                className="max-w-full max-h-80 object-contain" 
              />
            </div>
            <div className="flex justify-center mt-4">
              <button 
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center"
                onClick={(e) => {
                  e.stopPropagation(); 
                  downloadQR(enlargedQR.src, enlargedQR.name);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Tải mã QR
              </button>
            </div>
            <div className="text-center mt-3">
              <p className="text-sm text-gray-600">Quét mã QR để kết nối</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SocialMediaDisplay; 