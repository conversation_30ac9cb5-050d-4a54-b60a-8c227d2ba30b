import React, { useState, useEffect } from 'react';
import { FaCheckCircle, FaCircle, FaArrowRight, FaSpinner, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';
import { getCustomerInfo, isCustomerAuthenticated, createTemporaryCustomer } from '../utils/customerAuth';
import { 
  updateCustomerProfile, 
  updateOrderInformation, 
  uploadCustomerDocument,
  getCustomerDocuments
} from '../utils/customerAPI';
import DocumentPreviewModal from './DocumentPreviewModal';
import { 
  AVAILABLE_CURRENCIES, 
  getPaymentMethodsByCurrentcy, 
  getAllPaymentMethods,
  filterCartByCurrency 
} from '../utils/paymentUtils';
import DeliveryMethodForm from './DeliveryMethodForm';
import ActivationForm from './ActivationForm';
import AccountForm from './AccountForm';
import DocumentUploadForm from './DocumentUploadForm';
import RecipientInfoForm from './RecipientInfoForm';
import CheckoutForm from './CheckoutForm';
import PinEntryForm from './PinEntryForm';
import PaymentSection from './PaymentSection';

// Utility for logging
const logProductCheckout = (message, data = null) => {
  console.log(`[PRODUCT_CHECKOUT] ${message}`, data ? data : '');
};

// Card checkout process steps
const cardCheckoutSteps = [
  { 
    id: "pin", 
    title: "XUẤT MÃ PIN/NHẬP SDT NẠP TỰ ĐỘNG", 
    description: "Nhập SĐT để nhận mã PIN hoặc nạp trực tiếp",
    type: "CARD"
  },
  { 
    id: "account", 
    title: "TÀI KHOẢN KHÁCH HÀNG", 
    description: "Kiểm tra thông tin tài khoản",
    type: "CARD"
  }
];

// SIM checkout process steps
const simCheckoutSteps = [
  { 
    id: "recipient", 
    title: "THÔNG TIN NGƯỜI NHẬN", 
    description: "Cung cấp thông tin người nhận SIM",
    type: "SIM"
  },
  { 
    id: "documents", 
    title: "CUNG CẤP THÔNG TIN ĐẶT SIM", 
    description: "Tải lên giấy tờ cần thiết để đăng ký SIM",
    type: "SIM"
  },
  { 
    id: "delivery", 
    title: "HÌNH THỨC NHẬN HÀNG", 
    description: "Chọn cách thức nhận SIM",
    type: "SIM"
  },
  {
    id: "payment",
    title: "THANH TOÁN",
    description: "Hoàn tất thanh toán cho giao dịch",
    type: "SIM"
  },
  { 
    id: "activation", 
    title: "KÍCH HOẠT SIM", 
    description: "Hướng dẫn kích hoạt SIM sau khi nhận",
    type: "SIM"
  }
];

// TravelSIM checkout process steps
const travelSimCheckoutSteps = [
  { 
    id: "destination", 
    title: "Chọn điểm đến", 
    description: "Chọn quốc gia/khu vực sử dụng SIM",
    type: "TRAVELSIM"
  },
  { 
    id: "simType", 
    title: "Chọn loại SIM phù hợp", 
    description: "Chọn SIM vật lý hoặc eSIM",
    type: "TRAVELSIM"
  },
  { 
    id: "duration", 
    title: "Chọn thời gian sử dụng & dung lượng", 
    description: "Chọn gói cước phù hợp",
    type: "TRAVELSIM"
  },
  { 
    id: "orderInfo", 
    title: "Nhập thông tin đặt hàng", 
    description: "Cung cấp thông tin liên hệ",
    type: "TRAVELSIM"
  }
];

// Common checkout steps for all product types
const commonCheckoutSteps = [
  {
    id: "payment",
    title: "THANH TOÁN",
    description: "Hoàn tất thanh toán cho giao dịch",
    type: "COMMON"
  },
  {
    id: "checkout",
    title: "XÁC NHẬN ĐƠN HÀNG",
    description: "Kiểm tra và xác nhận thông tin đơn hàng",
    type: "COMMON"
  }
];

// Default checkout steps
const defaultCheckoutSteps = [
  { 
    id: "details", 
    title: "Chi tiết sản phẩm", 
    description: "Xem thông tin sản phẩm",
    type: "DEFAULT"
  },
  { 
    id: "confirm", 
    title: "Xác nhận đơn hàng", 
    description: "Xác nhận thông tin đơn hàng",
    type: "DEFAULT"
  },
  { 
    id: "payment", 
    title: "Thanh toán", 
    description: "Hoàn tất thanh toán",
    type: "DEFAULT"
  }
];

// Map of step IDs to form components
const stepFormComponents = {
  "pin": PinEntryForm,
  "recipient": RecipientInfoForm,
  "documents": DocumentUploadForm,
  "delivery": DeliveryMethodForm,
  "activation": ActivationForm,
  "account": AccountForm,
  "payment": PaymentSection,
  "checkout": CheckoutForm
};

// Function to build combined checkout steps for multiple product types
const buildCombinedCheckoutSteps = (productTypes) => {
  let allSteps = [];
  
  // Add steps for each product type
  if (productTypes.includes('CARD')) {
    allSteps = [...allSteps, ...cardCheckoutSteps];
  }
  
  if (productTypes.includes('SIM')) {
    allSteps = [...allSteps, ...simCheckoutSteps];
  }
  
  if (productTypes.includes('TRAVELSIM')) {
    allSteps = [...allSteps, ...travelSimCheckoutSteps];
  }
  
  // Add only common steps that don't already exist
  commonCheckoutSteps.forEach(commonStep => {
    // Check if this step ID already exists in allSteps
    const stepExists = allSteps.some(step => step.id === commonStep.id);
    if (!stepExists) {
      allSteps.push(commonStep);
    }
  });
  
  // If no specific product types were found, use default steps
  if (allSteps.length === 0) {
    allSteps = defaultCheckoutSteps;
  }
  
  return allSteps;
};

// Main ProductCheckoutProcess component - Restructured to show all sections on one page
const ProductCheckoutProcess = ({ 
  allProductTypes = [], // All product types in the cart
  allProducts = [], // All products for the current currency
  groupedProducts = {}, // Products grouped by type
  orderId, 
  storeObject, 
  addressData, 
  onAddressChange,
  saveAddressToProfile,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  selectedCurrency,
  totalForCurrentStore,
  filteredCartItems,
  cart,
  context,
  orderCompleted,
  setOrderCompleted,
  allowedPaymentMethods,
  saveOrderToLocalStorage,
  // Support for backward compatibility
  products,
  productType,
  product
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [checkoutSteps, setCheckoutSteps] = useState([]);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [currentProductType, setCurrentProductType] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [showPaymentMethodsModal, setShowPaymentMethodsModal] = useState(false);
  const [useMultipleRows, setUseMultipleRows] = useState(false);
  const [showScrollHint, setShowScrollHint] = useState(true);

  // Add effect to hide scroll hint after 5 seconds
  useEffect(() => {
    if (showScrollHint) {
      const timer = setTimeout(() => {
        setShowScrollHint(false);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [showScrollHint]);

  // Initialize checkout steps based on all product types in the cart
  useEffect(() => {
    // For backward compatibility
    if (productType) {
      allProductTypes = [productType];
    }
    if (products && products.length > 0) {
      allProducts = products;
      // Create simple grouped products object
      if (productType) {
        groupedProducts = { [productType]: products };
      }
    }
    if (product) {
      // Use single product if that's all we have
      allProducts = [product];
      if (product.categories && Array.isArray(product.categories)) {
        /* let type = 'DEFAULT'; */
        let type = 'DEFAULT';
        
        // Add detailed logging for product categorization
        console.log('[PRODUCT_CATEGORIZATION] Product:', product.name || product.sku);
        console.log('[PRODUCT_CATEGORIZATION] Categories:', product.categories);
        
        // Check if any category includes the product type, case insensitive
        // First try exact matches (after trimming and case conversion)
        if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase() === 'CARD')) {
          type = 'CARD';
          console.log('[PRODUCT_CATEGORIZATION] Categorized as CARD (exact match)');
        } else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase() === 'SIM')) {
          type = 'SIM';
          console.log('[PRODUCT_CATEGORIZATION] Categorized as SIM (exact match)');
        } else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase() === 'TRAVELSIM')) {
          type = 'TRAVELSIM';
          console.log('[PRODUCT_CATEGORIZATION] Categorized as TRAVELSIM (exact match)');
        } 
        // Then try partial matches, but be more specific about SIM to avoid false positives
        else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase().includes('SIM') && 
                                      !cat.trim().toUpperCase().includes('YEARSIM'))) {
          type = 'SIM';
          console.log('[PRODUCT_CATEGORIZATION] Categorized as SIM (partial match)');
        }
        // Special case for Yearsim products - these should be SIM type
        else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase().includes('YEARSIM')) ||
                product.sku?.toLowerCase().includes('yearsim')) {
          type = 'SIM';
          console.log('[PRODUCT_CATEGORIZATION] Categorized as SIM (yearsim product)');
        }
        // If all else fails, use default
        else {
          console.log('[PRODUCT_CATEGORIZATION] No matching category found, using DEFAULT');
        }
        
        allProductTypes = [type];
        groupedProducts = { [type]: [product] };
        console.log('[PRODUCT_CATEGORIZATION] Final product type:', type);
        console.log('[PRODUCT_CATEGORIZATION] Grouped products:', Object.keys(groupedProducts));
      }
    }

    // Log what we're working with
    logProductCheckout("Initializing product checkout process", { 
      productTypes: allProductTypes,
      totalProducts: allProducts?.length || 0,
      orderId,
      hasAddressData: !!addressData
    });
    
    if (allProductTypes && allProductTypes.length > 0) {
      // Get a normalized list of product types (capitalized)
      const normalizedTypes = allProductTypes.map(type => type.toUpperCase());
      
      // Build combined steps for all product types
      const combinedSteps = buildCombinedCheckoutSteps(normalizedTypes);
      setCheckoutSteps(combinedSteps);
      
      logProductCheckout("Built combined checkout steps", {
        types: normalizedTypes,
        stepCount: combinedSteps.length,
        steps: combinedSteps.map(s => s.id)
      });
      
      // Set the current product type based on the first step
      if (combinedSteps.length > 0) {
        setCurrentProductType(combinedSteps[0].type);
        
        // Find the first step that has an implemented form component
        // or is a common step (payment/checkout)
        const firstImplementedStepIndex = combinedSteps.findIndex(step => 
          stepFormComponents[step.id] || step.type === 'COMMON'
        );
        
        // If no implemented steps found, start at the common steps (payment/checkout)
        if (firstImplementedStepIndex === -1) {
          const firstCommonStepIndex = combinedSteps.findIndex(step => 
            step.type === 'COMMON'
          );
          
          if (firstCommonStepIndex !== -1) {
            setCurrentStep(firstCommonStepIndex);
            setCurrentProductType(combinedSteps[firstCommonStepIndex].type);
            logProductCheckout("No data collection steps implemented, skipping to payment");
          }
        } else if (firstImplementedStepIndex > 0) {
          // Start at the first implemented step
          setCurrentStep(firstImplementedStepIndex);
          setCurrentProductType(combinedSteps[firstImplementedStepIndex].type);
          logProductCheckout("Starting at first implemented step", { 
            stepIndex: firstImplementedStepIndex,
            stepId: combinedSteps[firstImplementedStepIndex].id
          });
        }
      }
    } else {
      // Default steps if no types specified
      setCheckoutSteps(defaultCheckoutSteps);
      
      // For default steps, go straight to payment if other steps aren't implemented
      const paymentStepIndex = defaultCheckoutSteps.findIndex(step => step.id === 'payment');
      if (paymentStepIndex !== -1) {
        setCurrentStep(paymentStepIndex);
      }
    }
  }, [allProductTypes, allProducts, groupedProducts, productType, products, product, orderId, addressData]);
  
  // Handle step completion
  const handleCompleteStep = () => {
    const currentStepData = checkoutSteps[currentStep];
    
    logProductCheckout("Completing step", { 
      stepIndex: currentStep,
      stepId: currentStepData?.id,
      stepType: currentStepData?.type
    });
    
    setCompletedSteps([...completedSteps, currentStep]);
    
    if (currentStep < checkoutSteps.length - 1) {
      // Find the next step that has an implemented form component or is a common step
      let nextStep = currentStep + 1;
      while (
        nextStep < checkoutSteps.length - 1 && 
        !stepFormComponents[checkoutSteps[nextStep].id] && 
        checkoutSteps[nextStep].type !== 'COMMON'
      ) {
        // Skip this step as it's not implemented
        logProductCheckout("Skipping unimplemented step", {
          stepIndex: nextStep,
          stepId: checkoutSteps[nextStep].id
        });
        setCompletedSteps(prev => [...prev, nextStep]);
        nextStep++;
      }
      
      setCurrentStep(nextStep);
      
      // Update the current product type for the next step
      if (checkoutSteps[nextStep]) {
        setCurrentProductType(checkoutSteps[nextStep].type);
      }
    }
  };
  
  // Handle going back to previous step
  const handleBackStep = () => {
    logProductCheckout("Going back to previous step", { 
      fromStep: currentStep,
      toStep: currentStep - 1
    });
    
    if (currentStep > 0) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      
      // Update the current product type for the previous step
      if (checkoutSteps[prevStep]) {
        setCurrentProductType(checkoutSteps[prevStep].type);
      }
    }
  };
  
  // Check if we can go back
  const canGoBack = currentStep > 0;
  
  // Get products for the current step's product type
  const getProductsForCurrentStep = () => {
    const stepType = checkoutSteps[currentStep]?.type;
    
    if (stepType === 'COMMON') {
      // Return all products for common steps
      return allProducts;
    }
    
    // Return products for this type, or empty array if none
    return groupedProducts[stepType] || [];
  };
  
  // Render the active step form with back button
  const renderStepForm = () => {
    const currentStepData = checkoutSteps[currentStep];
    if (!currentStepData) return null;
    
    const StepForm = stepFormComponents[currentStepData.id];
    const currentStepProducts = getProductsForCurrentStep();
    
    return (
      <div className="p-4 bg-gray-50 rounded-md max-h-[70vh] overflow-y-auto">
        {currentStepData.type !== 'COMMON' && (
          <div className="mb-4 bg-blue-50 p-2 rounded-md">
            <p className="text-sm text-blue-700">
              <FaInfoCircle className="inline-block mr-1" /> 
              Thông tin cho sản phẩm loại: <strong>{currentStepData.type}</strong>
            </p>
          </div>
        )}
        
        {StepForm ? (
          <StepForm 
            onComplete={handleCompleteStep} 
            orderId={orderId} 
            addressData={addressData}
            onAddressChange={onAddressChange}
            saveAddressToProfile={saveAddressToProfile}
            // Pass all products of the current step's type
            products={currentStepProducts}
            // For backwards compatibility
            product={currentStepProducts.length > 0 ? currentStepProducts[0] : null}
            selectedPaymentMethod={selectedPaymentMethod}
            setSelectedPaymentMethod={setSelectedPaymentMethod}
            selectedCurrency={selectedCurrency}
            productType={currentStepData.type}
            // Additional props for PaymentSection
            totalForCurrentStore={totalForCurrentStore}
            filteredCartItems={filteredCartItems}
            storeObject={storeObject}
            saveOrderToLocalStorage={saveOrderToLocalStorage}
            cart={cart}
            productCheckoutVisible={true}
            context={context}
            orderCompleted={orderCompleted}
            setOrderCompleted={setOrderCompleted}
            allowedPaymentMethods={allowedPaymentMethods}
            checkoutSteps={checkoutSteps}
            currentStep={currentStep}
            groupedProducts={groupedProducts}
            errorMessage={errorMessage}
            setErrorMessage={setErrorMessage}
          />
        ) : (
          <>
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Không cần thu thập dữ liệu đặc biệt cho loại sản phẩm này. Tiến hành thanh toán.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex justify-center mt-5">
              <button
                onClick={handleCompleteStep}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Tiếp tục đến thanh toán
              </button>
            </div>
          </>
        )}
      </div>
    );
  };
  
  // Function to format currency
  const formatCurrency = (amount) => {
    const currencySymbol = selectedCurrency || '';
    return `${currencySymbol}${amount.toLocaleString()}`;
  };

  // Add payment method selection handler
  const handlePaymentMethodChange = (methodId) => {
    logProductCheckout("Payment method changed", { from: selectedPaymentMethod, to: methodId });
    setSelectedPaymentMethod(methodId);
  };

  // Get available payment methods for current currency
  const availablePaymentMethods = getPaymentMethodsByCurrentcy(selectedCurrency);

  // Filter by allowed payment methods from store configuration
  const filteredPaymentMethods = allowedPaymentMethods
    ? availablePaymentMethods.filter(method => allowedPaymentMethods.includes(method.id))
    : availablePaymentMethods;

  // Render product summary grouped by type
  const renderProductSummary = () => {
    return (
      <div className="mt-4 mb-6">
        <h3 className="text-lg font-medium mb-3">Sản phẩm</h3>
        {Object.entries(groupedProducts).map(([type, products]) => (
          <div key={type} className="mb-4">
            <h4 className="font-medium text-gray-700 mb-2">{type} ({products.length})</h4>
            <div className="space-y-3 pl-2 md:pl-4">
              {products.map((product, index) => (
                <div key={`${product.sku}-${index}`} className="flex items-center border-b pb-3">
                  <div className="flex-shrink-0 w-12 h-12 md:w-16 md:h-16 bg-gray-100 rounded overflow-hidden mr-2 md:mr-3">
                    {product.image && (
                      <img 
                        src={Array.isArray(product.image) ? product.image[0] : product.image} 
                        alt={product.name} 
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm md:text-base truncate">{product.name}</h4>
                    <p className="text-xs md:text-sm text-gray-600">SKU: {product.sku}</p>
                    <div className="flex justify-between mt-1 text-xs md:text-sm">
                      <span>x{product.quantity}</span>
                      <span className="font-medium">{selectedCurrency}{product.price.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // If no steps, show a loading indicator
  if (!checkoutSteps || checkoutSteps.length === 0) {
    return (
      <div className="flex justify-center items-center p-10">
        <FaSpinner className="animate-spin mr-2 text-blue-500" />
        <span>Loading checkout process...</span>
      </div>
    );
  }

  return (
    <div className="product-checkout-process w-full">
      <div className="mb-6 relative">
        {/* Show a toggle button for display mode (scroll or multi-row) */}
        <div className="flex justify-end mb-2">
          <button 
            onClick={() => setUseMultipleRows(prev => !prev)}
            className="text-xs text-blue-600 flex items-center"
            id="display-toggle"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
            </svg>
            {useMultipleRows ? "Hiển thị 1 dòng" : "Hiển thị nhiều dòng"}
          </button>
        </div>

        {/* Add scrolling indicators for single row mode */}
        {!useMultipleRows && (
          <>
            <div className="absolute left-0 inset-y-0 items-center pointer-events-none z-10 flex">
              <div className="w-6 h-full bg-gradient-to-r from-white to-transparent"></div>
            </div>
            <div className="absolute right-0 inset-y-0 items-center pointer-events-none z-10 flex">
              <div className="w-6 h-full bg-gradient-to-l from-white to-transparent"></div>
            </div>

            {/* Visual scroll indicator arrows for mobile */}
            {checkoutSteps.length > 3 && (
              <>
                <div className="absolute left-0 inset-y-0 flex items-center z-20 pointer-events-none">
                  <div className="bg-white/80 p-1 rounded-full shadow-sm flex items-center justify-center sm:hidden animate-pulse">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </div>
                </div>
                <div className="absolute right-0 inset-y-0 flex items-center z-20 pointer-events-none">
                  <div className="bg-white/80 p-1 rounded-full shadow-sm flex items-center justify-center sm:hidden animate-pulse">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </>
            )}
          </>
        )}

        {/* Container for steps - either flex-wrap or scrollable */}
        <div 
          className={`flex items-center ${useMultipleRows ? 'flex-wrap gap-3 justify-center' : 'space-x-0.5 md:space-x-2 overflow-x-auto pb-4 no-scrollbar max-w-full snap-x snap-mandatory'}`}
        >
          {checkoutSteps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div 
                className={`flex flex-col items-center flex-shrink-0 ${useMultipleRows ? 'mb-2' : 'snap-start'} ${
                  index <= Math.max(...completedSteps, currentStep) ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'
                }`}
                style={{ minWidth: useMultipleRows ? '70px' : '50px' }}
                onClick={() => {
                  // Only allow navigating to completed steps or current step
                  if (index <= Math.max(...completedSteps, currentStep)) {
                    setCurrentStep(index);
                    // Update the current product type
                    if (checkoutSteps[index]) {
                      setCurrentProductType(checkoutSteps[index].type);
                    }
                    logProductCheckout("Navigated to step", { toStep: index });
                  }
                }}
              >
                <div 
                  className={`w-5 h-5 md:w-8 md:h-8 rounded-full flex items-center justify-center ${
                    completedSteps.includes(index) && index !== currentStep
                      ? 'bg-green-500 text-white'
                      : index === currentStep
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {completedSteps.includes(index) && index !== currentStep ? (
                    <FaCheckCircle className="text-[8px] md:text-sm" />
                  ) : (
                    <div className="text-[10px] md:text-sm font-medium">{index + 1}</div>
                  )}
                </div>
                <span className="text-[8px] md:text-xs mt-1 text-center w-12 md:w-20 truncate">
                  {step.title}
                </span>
                {step.type !== 'COMMON' && (
                  <span className="text-[6px] md:text-xs text-gray-500 mt-0.5">
                    {step.type}
                  </span>
                )}
              </div>
              {!useMultipleRows && index < checkoutSteps.length - 1 && (
                <div className="h-0.5 bg-gray-200 w-2 md:w-4 lg:flex-grow lg:min-w-[20px] flex-shrink-0"></div>
              )}
            </React.Fragment>
          ))}
        </div>
        
        {/* Add touch indicator for mobile scroll mode */}
        {!useMultipleRows && (
          <div className="flex justify-center sm:hidden mt-1">
            <div className="flex space-x-1">
              {checkoutSteps.map((_, i) => (
                <div 
                  key={i} 
                  className={`w-1 h-1 rounded-full ${
                    i === currentStep ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                ></div>
              ))}
            </div>
          </div>
        )}

        {/* Add a scroll hint for mobile - auto-hide after 5 seconds */}
        {!useMultipleRows && checkoutSteps.length > 3 && showScrollHint && (
          <div className="flex justify-center items-center mt-1 bg-blue-50 p-1 rounded text-xs text-blue-600 animate-pulse sm:hidden">
            <span>← Vuốt để xem các bước khác →</span>
          </div>
        )}
      </div>
      
      {/* Current step */}
      {renderStepForm()}
    </div>
  );
};

export default ProductCheckoutProcess; 