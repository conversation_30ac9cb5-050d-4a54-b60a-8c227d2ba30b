import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { isCustomerAuthenticated, getCustomerInfo } from '../utils/customerAuth';
import { updateCustomerProfile, updateOrderInformation } from '../utils/customerAPI';

// Utility for logging
const logProductCheckout = (message, data = null) => {
  console.log(`[PRODUCT_CHECKOUT] ${message}`, data ? data : '');
};

const DeliveryMethodForm = ({ onComplete, orderId, addressData, onAddressChange, saveAddressToProfile }) => {
  const [deliveryMethod, setDeliveryMethod] = useState(null);
  const [location, setLocation] = useState('');
  const [date, setDate] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [preferredMethod, setPreferredMethod] = useState(null);
  const [error, setError] = useState(null);
  const [saveToProfile, setSaveToProfile] = useState(true);
  
  // Check for previously saved delivery preferences
  useEffect(() => {
    if (isCustomerAuthenticated()) {
      const customerInfo = getCustomerInfo();
      if (customerInfo && customerInfo.deliveryPreferences) {
        // Get preferred delivery method from customer profile
        if (customerInfo.deliveryPreferences.method) {
          setDeliveryMethod(customerInfo.deliveryPreferences.method);
          setPreferredMethod(customerInfo.deliveryPreferences.method);
        }
        
        // Get preferred location if available
        if (customerInfo.deliveryPreferences.preferredLocation && 
            customerInfo.deliveryPreferences.method === 'store') {
          setLocation(customerInfo.deliveryPreferences.preferredLocation);
        }
        
        // Get notes if available
        if (customerInfo.deliveryPreferences.notes) {
          setNotes(customerInfo.deliveryPreferences.notes);
        }
      }
      
      // Pre-fill with address data if available
      if (addressData && addressData.street) {
        setLocation(addressData.street + 
          (addressData.district ? ', ' + addressData.district : '') + 
          (addressData.city ? ', ' + addressData.city : '')
        );
      }
    }
    
    // Set default date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    setDate(tomorrow.toISOString().split('T')[0]);
    
  }, [addressData]);
  
  const handleDeliveryMethodChange = (method) => {
    setDeliveryMethod(method);
    
    // Clear location when changing methods
    if (method !== 'address' && deliveryMethod === 'address') {
      setLocation('');
    } else if (method === 'address' && addressData && addressData.street) {
      // Set location to address if switching to address method
      setLocation(addressData.street + 
        (addressData.district ? ', ' + addressData.district : '') + 
        (addressData.city ? ', ' + addressData.city : '')
      );
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Validate form
      if (!deliveryMethod) {
        throw new Error('Vui lòng chọn phương thức nhận hàng');
      }
      
      if ((deliveryMethod === 'address' || deliveryMethod === 'store') && !location) {
        throw new Error('Vui lòng nhập địa chỉ nhận hàng');
      }
      
      // Build delivery data
      const deliveryData = {
        orderId,
        deliveryMethod,
        deliveryDate: date,
        notes
      };
      
      if (deliveryMethod === 'address' || deliveryMethod === 'store') {
        deliveryData.location = location;
      }
      
      logProductCheckout("Submitting delivery method", deliveryData);
      
      // Update order with delivery information
      const result = await updateOrderInformation(orderId, {
        deliveryInfo: deliveryData
      });
      
      // Save delivery preferences to customer profile if authenticated
      if (isCustomerAuthenticated() && saveToProfile) {
        const customerInfo = getCustomerInfo();
        
        if (customerInfo) {
          const deliveryPreferences = {
            method: deliveryMethod,
            notes
          };
          
          if (deliveryMethod === 'store') {
            deliveryPreferences.preferredLocation = location;
          }
          
          const profileUpdateResult = await updateCustomerProfile(customerInfo.id, {
            deliveryPreferences
          });
          
          logProductCheckout("Updated customer delivery preferences", profileUpdateResult);
          
          // If this is address delivery, save the address to the profile
          if (deliveryMethod === 'address' && addressData && addressData.street) {
            await saveAddressToProfile(addressData);
          }
        }
      }
      
      // Complete this step
      onComplete();
      
    } catch (err) {
      setError(err.message || 'Có lỗi xảy ra khi lưu thông tin giao hàng');
      logProductCheckout("Error in delivery form", err);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="delivery-method-form">
      <h3 className="text-lg font-medium mb-4">Phương thức nhận hàng</h3>
      
      {error && (
        <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4 flex items-center">
          <FaExclamationTriangle className="mr-2" />
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Chọn phương thức nhận hàng:</label>
          <div className="flex flex-wrap gap-3">
            <button
              type="button"
              className={`py-2 px-4 rounded-md border ${
                deliveryMethod === 'store' 
                  ? 'bg-blue-50 border-blue-500 text-blue-700' 
                  : 'border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => handleDeliveryMethodChange('store')}
            >
              Nhận tại cửa hàng
            </button>
            <button
              type="button"
              className={`py-2 px-4 rounded-md border ${
                deliveryMethod === 'address' 
                  ? 'bg-blue-50 border-blue-500 text-blue-700' 
                  : 'border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => handleDeliveryMethodChange('address')}
            >
              Giao đến địa chỉ
            </button>
            <button
              type="button"
              className={`py-2 px-4 rounded-md border ${
                deliveryMethod === 'digital' 
                  ? 'bg-blue-50 border-blue-500 text-blue-700' 
                  : 'border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => handleDeliveryMethodChange('digital')}
            >
              Nhận qua Email
            </button>
          </div>
        </div>
        
        {(deliveryMethod === 'store' || deliveryMethod === 'address') && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              {deliveryMethod === 'store' ? 'Chọn cửa hàng:' : 'Địa chỉ giao hàng:'}
            </label>
            <input
              type="text"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder={deliveryMethod === 'store' ? 'Tên cửa hàng hoặc địa chỉ' : 'Địa chỉ giao hàng'}
            />
            {deliveryMethod === 'address' && (
              <div className="mt-2">
                <button
                  type="button"
                  className="text-blue-600 text-sm hover:underline"
                  onClick={() => {
                    if (onAddressChange) {
                      // Trigger address form display in parent component
                      onAddressChange({
                        target: {
                          name: 'showAddressForm',
                          value: true
                        }
                      });
                    }
                  }}
                >
                  Thay đổi địa chỉ giao hàng
                </button>
              </div>
            )}
          </div>
        )}
        
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Ngày nhận hàng:</label>
          <input
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
            min={new Date().toISOString().split('T')[0]} // Prevent selecting past dates
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Ghi chú:</label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
            rows="3"
            placeholder="Ghi chú về việc giao hàng (không bắt buộc)"
          ></textarea>
        </div>
        
        {/* {isCustomerAuthenticated() && (
          <div className="mb-4 flex items-center">
            <input
              type="checkbox"
              id="savePreference"
              checked={saveToProfile}
              onChange={(e) => setSaveToProfile(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="savePreference" className="text-sm">
              Lưu tùy chọn này cho lần sau
            </label>
          </div>
        )} */}
        
        <div className="flex justify-between mt-4">
          <button
            type="button"
            onClick={() => onComplete()}
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
          >
            Bỏ qua
          </button>
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
            disabled={loading}
          >
            {loading ? (
              <>
                <FaSpinner className="animate-spin mr-2" />
                Đang xử lý...
              </>
            ) : (
              'Lưu phương thức'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default DeliveryMethodForm; 