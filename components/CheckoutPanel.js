import React, { useState, useEffect, useContext } from "react"
import { SiteContext } from "../context/mainContext"
import DENOMINATION from "../utils/currencyProvider"
import Button from "./Button"
import { Elements } from "@stripe/react-stripe-js"
import { loadStripe } from "@stripe/stripe-js"
import PaymentFormStripe from "./PaymentFormStripe"
import PaymentFormMomo from "./PaymentFormMomo"
import PaymentFormZaloPay from "./PaymentFormZaloPay"
import PaymentFormBankTransfer from "./PaymentFormBankTransfer"
import PaymentFormCOD from "./PaymentFormCOD"
import PaymentFormPayPal from "./PaymentFormPayPal"
import PaymentFormInquiry from "./PaymentFormInquiry"
import PaymentFormTimesCity from "./PaymentFormTimesCity"
import PaymentFormTaiwan from "./taiwan/payment/PaymentFormsTaiwan"
import TaiwanBankTransferForm from "./taiwan/payment/methods/BankTransfer"
import SevenEleveniBON from "./taiwan/payment/methods/711/SevenEleveniBON"
import AffiliatePanel from "./AffiliatePanel"
import UserLocation from "../utils/userLocation"
import FamilyMartPaymentForm from "./taiwan/payment/methods/FamilyMart/FamilyMartPayment"
import PaymentFormVietnamBankTransfer from "./PaymentFormVietnamBankTransfer"
import { getCustomerInfo, isCustomerAuthenticated, getCustomerId } from "../utils/customerAuth"
import PaymentFormComponents from "./paymentFormMapping";
import currencyToPaymentMethods from "./paymentFormMapping";
import getCurrencyDisplayName from "./paymentFormMapping";
import { logCheckoutProcess, logTransaction, logCheckoutEvent } from "../utils/checkoutLogger"



const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)

// Add logging utility for CheckoutPanel
const logCheckoutPanel = (message, data = null) => {
  console.log(`[CHECKOUT_PANEL] ${message}`, data ? data : '');
  // Also use the centralized checkout logger
  return logCheckoutProcess('checkout_panel', { message, data });
}

const CheckoutPanel = ({ storeObject, cart, isVisible, currencyTotal, currency }) => {
  logCheckoutPanel("Initializing CheckoutPanel", { 
    storeId: storeObject?.storeId,
    isVisible,
    cartSize: cart?.length,
    currency,
    currencyTotal,
    storePaymentMethods: storeObject.paymentmethods
  });
  
  // State for payment form visibility and selection
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [orderCompleted, setOrderCompleted] = useState(false);
  const [orderId, setOrderId] = useState("");
  const [discountCode, setDiscountCode] = useState("");
  const [appliedDiscount, setAppliedDiscount] = useState(0);
  const [customerInfo, setCustomerInfo] = useState(null);
  
  // Get context for cart syncing
  const { loadCartFromServer } = useContext(SiteContext);
  
  // Load customer info if authenticated
  useEffect(() => {
    if (isCustomerAuthenticated()) {
      const info = getCustomerInfo();
      setCustomerInfo(info);
      logCheckoutPanel("Customer info loaded", info);
    }
  }, []);
  
  // Get all payment methods that support this currency
  const getPaymentMethodsForCurrency = (currencyCode) => {
    // Get methods recommended for this currency
    const recommendedForCurrency = currencyToPaymentMethods[currencyCode] || 
                                  currencyToPaymentMethods.default;
    
    // Get methods configured for the store
    const configuredMethods = storeObject.paymentmethods ? 
      storeObject.paymentmethods.split(',').map(method => method.trim()) : 
      Object.keys(PaymentFormComponents);
    
    // Filter to get methods that are both recommended for the currency and configured for the store
    return configuredMethods.filter(method => 
      recommendedForCurrency.includes(method) && 
      PaymentFormComponents[method]
    );
  };
  
  // Get payment methods for the current currency
  const availablePaymentMethods = currency ? 
    getPaymentMethodsForCurrency(currency) : [];
  
  // Set initial payment method
  useEffect(() => {
    if (availablePaymentMethods.length > 0 && !selectedPaymentMethod) {
      setSelectedPaymentMethod(availablePaymentMethods[0]);
      // Don't automatically show form - wait for user selection
      setShowPaymentForm(false);
    } else if (availablePaymentMethods.length === 0) {
      setSelectedPaymentMethod('inquiry'); // Fallback to inquiry
      setShowPaymentForm(false);
    }
    
    // Don't generate an orderId here - it should be passed from checkout.js
    // Log a warning if no orderId is provided
    if (!orderId) {
      console.warn("CheckoutPanel: No orderId provided from checkout.js");
    }
  }, [currency, availablePaymentMethods]);
  
  const selectPaymentMethod = (methodKey) => {
    setSelectedPaymentMethod(methodKey);
    setShowPaymentForm(true);
    setErrorMessage(null);
    
    // Enhanced logging for payment method selection
    logCheckoutPanel("Payment method selected", { 
      method: methodKey,
      displayName: PaymentFormComponents[methodKey]?.displayName,
      currency,
      amount: currencyTotal,
      orderId
    });
    
    // Log as a checkout event
    logCheckoutEvent('PAYMENT_METHOD_SELECTED', {
      methodId: methodKey,
      name: PaymentFormComponents[methodKey]?.displayName || methodKey,
      currency,
      amount: currencyTotal,
      orderId,
      location: 'checkout_panel'
    });
    
    // Also log as a transaction if orderId exists
    if (orderId) {
      logTransaction(orderId, 'PAYMENT_METHOD_SELECTED', {
        paymentMethod: methodKey,
        currency,
        amount: currencyTotal
      });
    }
  };
  
  const handleDiscountCodeChange = (event) => {
    setDiscountCode(event.target.value);
  };
  
  const applyDiscount = async () => {
    logCheckoutPanel("Applying discount", { code: discountCode });
    // Discount logic here
  };
  
  const handleSubmit = async (event) => {
    event.preventDefault();
    logCheckoutPanel("Form submission initiated");
    
    // Sync cart with server before proceeding to payment
    if (loadCartFromServer) {
      try {
        logCheckoutPanel("Syncing cart with server before payment");
        await loadCartFromServer();
      } catch (error) {
        console.error('Error syncing cart with server:', error);
        // Continue with payment even if sync fails
      }
    }
    
    // Include customer ID in order data if authenticated
    if (isCustomerAuthenticated()) {
      // Add customer info to the form data or API call
      formData.customerId = getCustomerId();
      
      logCheckoutPanel("Adding customer ID to order", { 
        customerId: formData.customerId
      });
    }
    
    // Handle form submission
  };
  
  const goBackToPaymentSelection = () => {
    setShowPaymentForm(false);
  };
  
  // Get the selected payment form component
  const SelectedPaymentForm = selectedPaymentMethod ? 
    PaymentFormComponents[selectedPaymentMethod] : null;
  
  if (!isVisible) {
    return null;
  }
  
  return (
    <Elements stripe={stripePromise}>
      <div className="mt-8 border-t border-gray-300 pt-8">
        <h2 className="text-2xl font-bold mb-6">
          {currency ? `Thanh toán ${currencyTotal.toLocaleString()} ${currency}` : 'Thanh toán'}
        </h2>
        
        {!showPaymentForm ? (
          <div>
            <p className="text-gray-700 mb-4">Chọn phương thức thanh toán (Checkout):</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              {availablePaymentMethods.map(methodKey => {
                const method = PaymentFormComponents[methodKey];
                if (!method) return null;
                
                return (
                  <button
                    key={methodKey}
                    onClick={() => selectPaymentMethod(methodKey)}
                    className={`
                      p-4 border rounded-lg flex flex-col items-center justify-center
                      transition-all hover:border-gray-500
                      ${selectedPaymentMethod === methodKey 
                        ? 'border-2 border-gray-800' 
                        : 'border-gray-300'
                      }
                    `}
                  >
                    <span className="text-3xl mb-2">{method.icon}</span>
                    <span className="font-medium">{method.displayName}</span>
                  </button>
                );
              })}
            </div>
            
            {availablePaymentMethods.length === 0 && (
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded-md mb-6">
                <p className="font-medium">Không tìm thấy phương thức thanh toán phù hợp cho {getCurrencyDisplayName(currency)}</p>
                <p className="mt-2 text-sm">Vui lòng liên hệ với cửa hàng để biết thêm thông tin.</p>
              </div>
            )}
            
            {/* Discount code input */}
            <div className="mb-6">
              <label htmlFor="discountCode" className="block text-sm font-medium text-gray-700 mb-1">
                Mã giảm giá
              </label>
              <div className="flex">
                <input
                  type="text"
                  id="discountCode"
                  value={discountCode}
                  onChange={handleDiscountCodeChange}
                  className="flex-grow border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                  placeholder="Nhập mã giảm giá"
                />
                <button
                  onClick={applyDiscount}
                  className="bg-gray-800 text-white px-4 py-2 rounded-r-md hover:bg-gray-700"
                >
                  Áp dụng
                </button>
              </div>
            </div>
            
            {/* Proceed to checkout button */}
            <button
              onClick={() => selectedPaymentMethod && selectPaymentMethod(selectedPaymentMethod)}
              className="w-full bg-gray-800 text-white py-3 rounded-md hover:bg-gray-700 transition-colors"
              disabled={!selectedPaymentMethod || availablePaymentMethods.length === 0}
            >
              Tiếp tục thanh toán
            </button>
          </div>
        ) : (
          <div>
            {/* Payment form header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">
                {SelectedPaymentForm?.displayName || 'Thanh toán'}
              </h3>
              <button
                onClick={goBackToPaymentSelection}
                className="text-gray-600 hover:text-gray-800"
              >
                ← Quay lại
              </button>
            </div>
            
            {/* Error message */}
            {errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-800 rounded-md">
                {errorMessage}
              </div>
            )}
            
            {/* Payment form */}
            <form onSubmit={handleSubmit}>
              {SelectedPaymentForm && (
                <SelectedPaymentForm
                  storeObject={storeObject}
                  cart={cart}
                  amount={currencyTotal}
                  currency={currency}
                  orderId={orderId}
                  setErrorMessage={setErrorMessage}
                  setOrderCompleted={setOrderCompleted}
                  customerInfo={customerInfo}
                />
              )}
              
              {/* Submit button shown conditionally */}
              {!SelectedPaymentForm?.hideDefaultButton && (
                <button
                  type="submit"
                  className="w-full mt-6 bg-gray-800 text-white py-3 rounded-md hover:bg-gray-700 transition-colors"
                >
                  Hoàn tất đơn hàng
                </button>
              )}
            </form>
          </div>
        )}
        
        {/* Order completed message */}
        {orderCompleted && (
          <div className="mt-8 p-6 bg-green-50 border border-green-200 text-green-800 rounded-lg">
            <h3 className="text-xl font-bold mb-2">Đặt hàng thành công!</h3>
            <p>Mã đơn hàng của bạn: <span className="font-mono">{orderId}</span></p>
            <p className="mt-2">Chúng tôi đã gửi một email xác nhận đến địa chỉ email của bạn.</p>
            <button
              onClick={() => window.location.href = `/${storeObject.storePathname}`}
              className="mt-4 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
            >
              Tiếp tục mua sắm
            </button>
          </div>
        )}
      </div>
    </Elements>
  )
}

export default CheckoutPanel 