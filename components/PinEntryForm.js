import React, { useState } from 'react';
import { FaSpin<PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { updateOrderInformation } from '../utils/customerAPI';

const PinEntryForm = ({ onComplete, orderId }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Update order information if orderId provided
      if (orderId) {
        await updateOrderInformation(orderId, {
          pinEntryInfo: {
            phoneNumber,
            requestDate: new Date().toISOString(),
            status: 'requested'
          }
        });
      }
      
      // Continue with the next step
      onComplete();
    } catch (error) {
      console.error('Error processing PIN request:', error);
      setError('<PERSON><PERSON> lỗi x<PERSON>y ra khi xử lý yêu cầu mã PIN. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Số điện thoại
        </label>
        <input
          type="tel"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
          placeholder="Nhập số điện thoại"
          required
        />
      </div>
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 text-red-700 flex items-start">
          <FaExclamationTriangle className="flex-shrink-0 mt-0.5 mr-2" />
          <p className="text-sm">{error}</p>
        </div>
      )}
      <button
        type="submit"
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center"
        disabled={loading}
      >
        {loading ? <FaSpinner className="animate-spin mr-2" /> : null}
        {loading ? 'Đang xử lý...' : 'Gửi yêu cầu'}
      </button>
    </form>
  );
};

export default PinEntryForm; 