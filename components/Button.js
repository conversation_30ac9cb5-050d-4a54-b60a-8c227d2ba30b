import React from 'react';
import Image from './Image'

export default function Button({ title, onClick, full = false, small = false, imagesrc = ''}) {
  
  let classNames = "text-sm font-bold tracking-wider bg-transparent hover:bg-black text-black font-semibold hover:text-white border-2 border-black hover:border-transparent"
  
  if (full) {
    classNames = `${classNames} w-full`
  }

  if (small) {
    classNames += " py-2 px-1"; /* classNames += " py-2 px-4"; */
  } else {
    classNames += " py-4 px-12";
  }

  return (
    <button onClick={onClick} className={classNames}>
      <div>
        {title}
        {imagesrc && <Image src={imagesrc} width={20} height={20} />}
        {/* {imagesrc != '' && <Image src='https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/angry-customer.webp' width={20} height={20} />} */}
      </div>
    </button>
  )
}