import React from 'react';

const TravelSimGuide = () => {
  return (
    <div className="my-4 px-4 md:px-0 md:my-8">
      <div className="bg-yellow-400 py-4 md:py-6 px-4 mb-4 md:mb-6 rounded-lg shadow-md">
        <h2 className="text-white text-2xl md:text-3xl font-bold text-center">SIM DU LỊCH QUỐC TẾ</h2>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Step 1 */}
        <div className="bg-orange-500 text-white rounded-lg p-4 relative shadow-md transform transition-transform duration-300 hover:scale-105">
          <div className="flex items-center mb-3">
            <div className="bg-white rounded-full p-2 mr-3 text-orange-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
            </div>
            <h3 className="font-bold text-lg">Bước 1: Chọn điểm đến</h3>
          </div>
          <p className="text-sm md:text-base ml-11">
            <strong>Chọn quốc gia</strong> bạn sắp đến (ví dụ: Thái Lan, Hàn Quốc, Nhật Bản, Singapore, châu Âu...)
          </p>
          <p className="text-sm md:text-base mt-2 ml-11">
            Bạn cũng có thể chọn SIM đa quốc gia nếu hành trình đi qua nhiều nước.
          </p>
        </div>

        {/* Step 2 */}
        <div className="bg-gray-400 text-white rounded-lg p-4 relative shadow-md transform transition-transform duration-300 hover:scale-105">
          <div className="flex items-center mb-3">
            <div className="bg-white rounded-full p-2 mr-3 text-gray-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 9a2 2 0 10-4 0v5a2 2 0 104 0V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9h.01M15 9h.01M9 13h.01M15 13h.01M9 17h.01M15 17h.01" />
              </svg>
            </div>
            <h3 className="font-bold text-lg">Bước 2: Chọn loại SIM</h3>
          </div>
          <p className="text-sm md:text-base ml-11">
            <strong>SIM vật lý</strong>: giao SIM tận nơi - lắp vào điện thoại
          </p>
          <p className="text-sm md:text-base mt-2 ml-11">
            <strong>eSIM</strong>: quét mã QR, kích hoạt nhanh chóng, không cần thao tác SIM thật.
          </p>
        </div>

        {/* Step 3 */}
        <div className="bg-yellow-500 text-white rounded-lg p-4 relative shadow-md transform transition-transform duration-300 hover:scale-105">
          <div className="flex items-center mb-3">
            <div className="bg-white rounded-full p-2 mr-3 text-yellow-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="font-bold text-lg">Bước 3: Chọn gói cước</h3>
          </div>
          <p className="text-sm md:text-base ml-11">
            <strong>Chọn số ngày sử dụng</strong> phù hợp với chuyến đi (5, 7, 10, 15, 30 ngày...)
          </p>
          <p className="text-sm md:text-base mt-2 ml-11">
            Chọn mức dung lượng cần thiết (5GB, 10GB, không giới hạn...)
          </p>
        </div>

        {/* Step 4 */}
        <div className="bg-blue-500 text-white rounded-lg p-4 relative shadow-md transform transition-transform duration-300 hover:scale-105">
          <div className="flex items-center mb-3">
            <div className="bg-white rounded-full p-2 mr-3 text-blue-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 className="font-bold text-lg">Bước 4: Thông tin đặt hàng</h3>
          </div>
          <p className="text-sm md:text-base ml-11">
            <strong>Nhập họ tên, email và số điện thoại</strong> của bạn
          </p>
          <p className="text-sm md:text-base mt-2 ml-11">
            Với SIM vật lý, nhập địa chỉ nhận hàng
          </p>
          <p className="text-sm md:text-base mt-2 ml-11">
            Với eSIM, bạn sẽ nhận mã QR qua email sau khi thanh toán
          </p>
        </div>

        {/* Step 5 */}
        <div className="bg-green-600 text-white rounded-lg p-4 relative shadow-md transform transition-transform duration-300 hover:scale-105">
          <div className="flex items-center mb-3">
            <div className="bg-white rounded-full p-2 mr-3 text-green-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <h3 className="font-bold text-lg">Bước 5: Thanh toán</h3>
          </div>
          <p className="text-sm md:text-base ml-11">
            <strong>Thanh toán qua các hình thức tiện lợi</strong>: thẻ ngân hàng, ví điện tử, thẻ tín dụng hoặc chuyển khoản.
          </p>
          <p className="text-sm md:text-base mt-2 ml-11">
            Hệ thống tự động xác nhận ngay sau khi thanh toán và gửi email xác nhận.
          </p>
        </div>

        {/* Step 6 */}
        <div className="bg-orange-400 text-white rounded-lg p-4 relative shadow-md transform transition-transform duration-300 hover:scale-105">
          <div className="flex items-center mb-3">
            <div className="bg-white rounded-full p-2 mr-3 text-orange-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-bold text-lg">Bước 6: Kích hoạt & sử dụng</h3>
          </div>
          <p className="text-sm md:text-base ml-11">
            Với <strong>SIM vật lý</strong>: lắp SIM vào điện thoại tại điểm đến, bật chế độ roaming là có thể sử dụng.
          </p>
          <p className="text-sm md:text-base mt-2 ml-11">
            Với <strong>eSIM</strong>: quét mã QR theo hướng dẫn trong email, kích hoạt trên điện thoại trước hoặc khi đến nơi.
          </p>
        </div>
      </div>
      
      {/* Mobile help button */}
      <div className="fixed bottom-6 right-6 md:hidden">
        <button className="bg-blue-500 text-white p-4 rounded-full shadow-lg transform transition-transform hover:scale-110">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default TravelSimGuide; 