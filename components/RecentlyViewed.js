import { useEffect, useState, useMemo } from 'react'
import { slugify } from '../utils/helpers'
import Image from './Image'
import { useRouter } from 'next/router'

const RecentlyViewed = ({ currentProductSku, store, currentProduct, products = [] }) => {
  const [recentSkus, setRecentSkus] = useState([])
  const [isClient, setIsClient] = useState(false)
  const router = useRouter()

  // Create a memoized lookup map for products
  const productsMap = useMemo(() => {
    return products.reduce((acc, product) => {
      acc[product.sku] = product
      return acc
    }, {})
  }, [products])

  // Function to get recently viewed SKUs - only called client-side
  const getRecentlyViewedSkus = () => {
    if (typeof window === 'undefined') return []
    
    const stored = localStorage.getItem('recentlyViewed')
    return stored ? JSON.parse(stored) : []
  }

  // Function to update recently viewed products - only called client-side
  const updateRecentlyViewed = (sku) => {
    if (!sku || typeof window === 'undefined') return

    const recentSkus = getRecentlyViewedSkus()
    
    // Remove the current SKU if it exists in the list
    const filtered = recentSkus.filter(s => s !== sku)
    
    // Add the current SKU to the beginning
    const updated = [sku, ...filtered].slice(0, 5) // Keep only last 5 products
    
    localStorage.setItem('recentlyViewed', JSON.stringify(updated))
    setRecentSkus(updated)
  }

  // Mark when component is mounted on client
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle recently viewed products only after client-side mount
  useEffect(() => {
    if (!isClient) return
    
    // Update list when component mounts on client
    setRecentSkus(getRecentlyViewedSkus())

    // Update when current product changes
    if (currentProduct?.sku) {
      updateRecentlyViewed(currentProduct.sku)
    }

    // Handle route changes
    const handleRouteChange = () => {
      if (currentProductSku) {
        updateRecentlyViewed(currentProductSku)
      }
    }

    // Subscribe to router events
    router.events.on('routeChangeComplete', handleRouteChange)

    // Cleanup
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange)
    }
  }, [currentProduct, currentProductSku, router.events, isClient])

  // Get actual products from SKUs using the memoized map
  const recentProducts = recentSkus
    .map(sku => productsMap[sku])
    .filter(Boolean)

  // Don't render anything during server-side rendering
  if (!isClient) return null
  
  // Don't render if no recent products
  if (recentProducts.length < 1) return null

  return (
    <div className="max-w-5xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Sản phẩm đã xem</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {recentProducts.map((product) => (
            <a 
              key={product.sku}
              href={`/${store}/product/${slugify(product.sku)}`}
              className="group"
            >
              <div className="aspect-square overflow-hidden rounded-lg bg-gray-50 mb-2">
                {product.image?.[0] && (
                  <img
                    src={product.image[0]}
                    alt={product.name}
                    className="h-full w-full object-cover group-hover:opacity-75 transition-opacity"
                  />
                )}
              </div>
              <h3 className="text-sm font-medium text-gray-900 truncate">{product.name}</h3>
              {product.price && (
                <p className="text-sm text-orange-600">
                  {new Intl.NumberFormat('vi-VN').format(product.price)} {product.currency}
                </p>
              )}
            </a>
          ))}
        </div>
      </div>
    </div>
  )
}

export default RecentlyViewed