import React, { useState, useEffect } from 'react';
import { FaCheckCircle, FaCircle, FaChevronDown, FaChevronUp, FaSpinner, FaInfoCircle, FaExclamationTriangle, FaTimes, FaUndo, FaDownload, FaFilePdf } from 'react-icons/fa';
import { getCustomerInfo, isCustomerAuthenticated, createTemporaryCustomer } from '../utils/customerAuth';
import { 
  updateCustomerProfile, 
  updateOrderInformation, 
  uploadCustomerDocument,
  getCustomerDocuments
} from '../utils/customerAPI';
import DocumentPreviewModal from './DocumentPreviewModal';
import { 
  AVAILABLE_CURRENCIES, 
  getPaymentMethodsByCurrentcy, 
  getAllPaymentMethods,
  filterCartByCurrency 
} from '../utils/paymentUtils';
import {
  PaymentMonitor,
  CheckoutProgress,
  AbandonedCheckoutRecovery,
  startAutoSave,
  stopAutoSave,
  migrateOldProgress
} from '../utils/checkoutProgress';
import { generateCheckoutStatusPDF } from '../utils/pdfGenerator';
import DeliveryMethodForm from './DeliveryMethodForm';
import ActivationForm from './ActivationForm';
import AccountForm from './AccountForm';
import DocumentUploadForm from './DocumentUploadForm';
import RecipientInfoForm from './RecipientInfoForm';
import CheckoutForm from './CheckoutForm';
import PinEntryForm from './PinEntryForm';
import PaymentSection from './PaymentSection';

// Utility for logging
const logProductCheckout = (message, data = null) => {
  console.log(`[PRODUCT_CHECKOUT_SINGLE_PAGE] ${message}`, data ? data : '');
};

// Card checkout process steps
const cardCheckoutSteps = [
  { 
    id: "pin", 
    title: "XUẤT MÃ PIN/NHẬP SDT NẠP TỰ ĐỘNG", 
    description: "Nhập SĐT để nhận mã PIN hoặc nạp trực tiếp",
    type: "CARD",
    required: true
  },
  { 
    id: "account", 
    title: "TÀI KHOẢN KHÁCH HÀNG", 
    description: "Kiểm tra thông tin tài khoản",
    type: "CARD",
    required: true
  }
];

// SIM checkout process steps
const simCheckoutSteps = [
  { 
    id: "recipient", 
    title: "THÔNG TIN NGƯỜI NHẬN", 
    description: "Cung cấp thông tin người nhận SIM",
    type: "SIM",
    required: true
  },
  { 
    id: "documents", 
    title: "CUNG CẤP THÔNG TIN ĐẶT SIM", 
    description: "Tải lên giấy tờ cần thiết để đăng ký SIM",
    type: "SIM",
    required: true
  },
  { 
    id: "delivery", 
    title: "HÌNH THỨC NHẬN HÀNG", 
    description: "Chọn cách thức nhận SIM",
    type: "SIM",
    required: true
  },
  {
    id: "payment",
    title: "THANH TOÁN",
    description: "Hoàn tất thanh toán cho giao dịch",
    type: "SIM",
    required: true
  },
  { 
    id: "activation", 
    title: "KÍCH HOẠT SIM", 
    description: "Hướng dẫn kích hoạt SIM sau khi nhận",
    type: "SIM",
    required: false,
    informationOnly: true // This is just informational, not an actionable step
  }
];

// TravelSIM checkout process steps
const travelSimCheckoutSteps = [
  { 
    id: "destination", 
    title: "Chọn điểm đến", 
    description: "Chọn quốc gia/khu vực sử dụng SIM",
    type: "TRAVELSIM",
    required: true
  },
  { 
    id: "simType", 
    title: "Chọn loại SIM phù hợp", 
    description: "Chọn SIM vật lý hoặc eSIM",
    type: "TRAVELSIM",
    required: true
  },
  { 
    id: "duration", 
    title: "Chọn thời gian sử dụng & dung lượng", 
    description: "Chọn gói cước phù hợp",
    type: "TRAVELSIM",
    required: true
  },
  { 
    id: "orderInfo", 
    title: "Nhập thông tin đặt hàng", 
    description: "Cung cấp thông tin liên hệ",
    type: "TRAVELSIM",
    required: true
  }
];

// Common checkout steps for all product types
const commonCheckoutSteps = [
  {
    id: "payment",
    title: "THANH TOÁN",
    description: "Hoàn tất thanh toán cho giao dịch",
    type: "COMMON",
    required: true
  }
];

// Default checkout steps
const defaultCheckoutSteps = [
  { 
    id: "details", 
    title: "Chi tiết sản phẩm", 
    description: "Xem thông tin sản phẩm",
    type: "DEFAULT",
    required: true
  },
  { 
    id: "confirm", 
    title: "Xác nhận đơn hàng", 
    description: "Xác nhận thông tin đơn hàng",
    type: "DEFAULT",
    required: true
  },
  { 
    id: "payment", 
    title: "Thanh toán", 
    description: "Hoàn tất thanh toán",
    type: "DEFAULT",
    required: true
  }
];

// Map of step IDs to form components
const stepFormComponents = {
  "pin": PinEntryForm,
  "recipient": RecipientInfoForm,
  "documents": DocumentUploadForm,
  "delivery": DeliveryMethodForm,
  "activation": ActivationForm,
  "account": AccountForm,
  "payment": PaymentSection,
  "checkout": CheckoutForm
};

// Function to build combined checkout steps for multiple product types
const buildCombinedCheckoutSteps = (productTypes) => {
  let allSteps = [];
  
  // Add steps for each product type
  if (productTypes.includes('CARD')) {
    allSteps = [...allSteps, ...cardCheckoutSteps];
  }
  
  if (productTypes.includes('SIM')) {
    allSteps = [...allSteps, ...simCheckoutSteps];
  }
  
  if (productTypes.includes('TRAVELSIM')) {
    allSteps = [...allSteps, ...travelSimCheckoutSteps];
  }
  
  // Add only common steps that don't already exist
  commonCheckoutSteps.forEach(commonStep => {
    // Check if this step ID already exists in allSteps
    const stepExists = allSteps.some(step => step.id === commonStep.id);
    if (!stepExists) {
      allSteps.push(commonStep);
    }
  });
  
  // If no specific product types were found, use default steps
  if (allSteps.length === 0) {
    allSteps = defaultCheckoutSteps;
  }
  
  return allSteps;
};

// Main ProductCheckoutSinglePage component - All sections on one page
const ProductCheckoutSinglePage = ({
  allProductTypes = [], // All product types in the cart
  allProducts = [], // All products for the current currency
  groupedProducts = {}, // Products grouped by type
  orderId,
  storeObject,
  addressData,
  onAddressChange,
  saveAddressToProfile,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  selectedCurrency,
  totalForCurrentStore,
  filteredCartItems,
  cart,
  context,
  orderCompleted,
  setOrderCompleted,
  allowedPaymentMethods,
  saveOrderToLocalStorage,
  recoveredData, // Recovered checkout data for restoring section states
  // Support for backward compatibility
  products,
  productType,
  product
}) => {
  // State for tracking which sections are open/closed
  const [openSections, setOpenSections] = useState({});
  // State for tracking which sections are completed
  const [completedSections, setCompletedSections] = useState({});
  // State for form data from each section
  const [sectionData, setSectionData] = useState({});
  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  
  // Document-related states
  const [previewDocument, setPreviewDocument] = useState(null);
  const [uploadedDocuments, setUploadedDocuments] = useState({});

  // Build checkout steps based on product types
  const [checkoutSteps, setCheckoutSteps] = useState([]);
  
  // Payment monitoring states
  const [paymentInitiated, setPaymentInitiated] = useState(false);
  const [paymentMethodUsed, setPaymentMethodUsed] = useState(null);
  const [showPaymentWarning, setShowPaymentWarning] = useState(false);
  const [paymentWarningData, setPaymentWarningData] = useState(null);
  
  // Abandoned checkout recovery states
  const [showRecoveryModal, setShowRecoveryModal] = useState(false);
  const [recoveryData, setRecoveryData] = useState(null);

  // PDF download states
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [pdfMessage, setPdfMessage] = useState(null);

  // Initialize checkout steps and default states
  useEffect(() => {
    // Handle backward compatibility
    let finalProductTypes = [...allProductTypes];
    let finalProducts = [...allProducts];
    let finalGroupedProducts = { ...groupedProducts };

    if (productType) {
      finalProductTypes = [productType];
    }
    if (products && products.length > 0) {
      finalProducts = products;
      if (productType) {
        finalGroupedProducts = { [productType]: products };
      }
    }
    if (product) {
      finalProducts = [product];
      if (product.categories && Array.isArray(product.categories)) {
        let type = 'DEFAULT';
        
        logProductCheckout('Product categorization', {
          product: product.name || product.sku,
          categories: product.categories
        });
        
        // Categorize product based on categories
        if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase() === 'CARD')) {
          type = 'CARD';
        } else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase() === 'SIM')) {
          type = 'SIM';
        } else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase() === 'TRAVELSIM')) {
          type = 'TRAVELSIM';
        } else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase().includes('SIM') && 
                                      !cat.trim().toUpperCase().includes('YEARSIM'))) {
          type = 'SIM';
        } else if (product.categories.some(cat => typeof cat === 'string' && cat.trim().toUpperCase().includes('YEARSIM')) ||
                product.sku?.toLowerCase().includes('yearsim')) {
          type = 'SIM';
        }
        
        finalProductTypes = [type];
        finalGroupedProducts = { [type]: [product] };
        
        logProductCheckout('Final product categorization', {
          type,
          groupedProducts: Object.keys(finalGroupedProducts)
        });
      }
    }

    logProductCheckout("Initializing single page checkout", { 
      productTypes: finalProductTypes,
      totalProducts: finalProducts?.length || 0,
      orderId,
      hasAddressData: !!addressData
    });
    
    if (finalProductTypes && finalProductTypes.length > 0) {
      const normalizedTypes = finalProductTypes.map(type => type.toUpperCase());
      const combinedSteps = buildCombinedCheckoutSteps(normalizedTypes);
      setCheckoutSteps(combinedSteps);
      
      logProductCheckout("Built combined checkout steps", {
        types: normalizedTypes,
        stepCount: combinedSteps.length,
        steps: combinedSteps.map(s => s.id)
      });
      
      // Initialize sections state - all collapsed by default, except payment section
      const initialOpenState = {};
      const initialCompletedState = {};

      combinedSteps.forEach((step) => {
        // Payment section starts open and stays open
        initialOpenState[step.id] = step.id === 'payment' ? true : false;
        initialCompletedState[step.id] = false;
      });

      setOpenSections(initialOpenState);
      setCompletedSections(initialCompletedState);
    } else {
      const defaultSteps = defaultCheckoutSteps;
      setCheckoutSteps(defaultSteps);
      
      const initialOpenState = {};
      const initialCompletedState = {};

      defaultSteps.forEach((step) => {
        // Payment section starts open and stays open
        initialOpenState[step.id] = step.id === 'payment' ? true : false;
        initialCompletedState[step.id] = false;
      });

      setOpenSections(initialOpenState);
      setCompletedSections(initialCompletedState);
    }
  }, [allProductTypes, allProducts, groupedProducts, productType, products, product, orderId, addressData]);

  // Restore section completion states from recovered data
  useEffect(() => {
    if (recoveredData && recoveredData.progress) {
      const progress = recoveredData.progress;

      logProductCheckout("Restoring section states from recovered data", {
        orderId,
        recoveredOrderId: recoveredData.orderId,
        hasCompletedSections: !!progress.completedSections,
        hasSectionData: !!progress.sectionData,
        hasOpenSections: !!progress.openSections
      });

      // Restore completed sections
      if (progress.completedSections) {
        setCompletedSections(progress.completedSections);
        logProductCheckout("Restored completed sections", progress.completedSections);
      }

      // Restore section data
      if (progress.sectionData) {
        setSectionData(progress.sectionData);
        logProductCheckout("Restored section data", Object.keys(progress.sectionData));
      }

      // Restore open sections (but keep payment section open)
      if (progress.openSections) {
        setOpenSections(prev => ({
          ...progress.openSections,
          payment: true // Ensure payment section stays open
        }));
        logProductCheckout("Restored open sections", progress.openSections);
      }
    }
  }, [recoveredData, orderId]);

  // Payment monitoring useEffect
  useEffect(() => {
    if (orderId) {
      // Check existing payment status
      const existingPayment = PaymentMonitor.checkPaymentStatus(orderId);
      if (existingPayment) {
        setPaymentInitiated(true);
        setPaymentMethodUsed(existingPayment.paymentMethod);
        
        // Check for duplicate payment warnings
        const warningData = PaymentMonitor.getDuplicatePaymentWarning(orderId);
        if (warningData.isDuplicate) {
          setPaymentWarningData(warningData);
          setShowPaymentWarning(true);
        }
        
        logProductCheckout("Existing payment found", {
          orderId,
          paymentMethod: existingPayment.paymentMethod,
          status: existingPayment.status
        });
      }
    }
  }, [orderId]);

  // Abandoned checkout recovery useEffect
  useEffect(() => {
    if (orderId) {
      // Check if we just restored a checkout (cooldown period)
      const justRestored = sessionStorage.getItem('just_restored_checkout');
      const restoreTimestamp = sessionStorage.getItem('restore_timestamp');

      if (justRestored && restoreTimestamp) {
        const timeSinceRestore = Date.now() - parseInt(restoreTimestamp);
        const cooldownPeriod = 5000; // 5 seconds cooldown

        if (timeSinceRestore < cooldownPeriod) {
          console.log(`Recovery cooldown active: ${Math.ceil((cooldownPeriod - timeSinceRestore) / 1000)}s remaining`);
          // Clear the flags after cooldown
          setTimeout(() => {
            sessionStorage.removeItem('just_restored_checkout');
            sessionStorage.removeItem('restore_timestamp');
          }, cooldownPeriod - timeSinceRestore);
          return; // Skip recovery check during cooldown
        } else {
          // Clear expired flags
          sessionStorage.removeItem('just_restored_checkout');
          sessionStorage.removeItem('restore_timestamp');
        }
      }

      // Ensure any old localStorage data is migrated first
      migrateOldProgress();

      // IMPORTANT: Only show recovery modal if we're NOT already on a checkout page
      // The recovery feature should only be offered on the cart page, not during checkout
      const isOnCheckoutPage = window.location.pathname.includes('/checkout');

      if (!isOnCheckoutPage) {
        // Check for abandoned checkouts when component mounts (only on non-checkout pages)
        const recovery = AbandonedCheckoutRecovery.checkForRecoverableCheckout(orderId);
        if (recovery.hasRecoverable) {
          setRecoveryData(recovery);
          setShowRecoveryModal(true);

          logProductCheckout("Abandoned checkout found", {
            abandonedOrderId: recovery.checkout.orderId,
            abandonedTimestamp: recovery.checkout.timestamp
          });
        }
      } else {
        logProductCheckout("Skipping recovery check - already on checkout page", {
          currentPath: window.location.pathname,
          orderId
        });
      }

      // Start auto-save for current checkout progress
      startAutoSave(orderId, () => {
        return {
          completedSections,
          sectionData,
          openSections,
          checkoutSteps: checkoutSteps.map(step => step.id),
          completed: areAllRequiredSectionsCompleted(),
          timestamp: new Date().toISOString()
        };
      });

      // Cleanup auto-save on unmount
      return () => {
        stopAutoSave();
      };
    }
  }, [orderId, completedSections, sectionData]);

  // Check document completion status when component mounts or sectionData changes
  useEffect(() => {
    if (checkoutSteps.some(step => step.id === 'documents')) {
      const documentsData = sectionData.documents;
      if (documentsData && documentsData.completed !== undefined) {
        markSectionCompleted('documents', documentsData.completed);
      }
    }
  }, [sectionData.documents, checkoutSteps]);

  // Toggle section open/closed (accordion behavior - only one open at a time)
  // Special handling: Payment section never closes once opened
  const toggleSection = (sectionId) => {
    setOpenSections(prev => {
      const newState = {};

      // Close all sections first, except payment section
      Object.keys(prev).forEach(key => {
        if (key === 'payment') {
          // Payment section stays open once opened
          newState[key] = prev[key];
        } else {
          newState[key] = false;
        }
      });

      // If the clicked section was closed, open it (accordion behavior)
      // If it was already open, keep it closed (all sections closed)
      // Exception: Payment section never closes once opened
      if (!prev[sectionId]) {
        newState[sectionId] = true;
      } else if (sectionId === 'payment') {
        // Keep payment section open even if clicked when already open
        newState[sectionId] = true;
      }

      return newState;
    });
  };

  // Handle clicks on section header - prevent toggle when clicking on interactive elements
  const handleSectionHeaderClick = (e, sectionId) => {
    // Check if the clicked element or its parent is an interactive element
    const isInteractiveElement = e.target.closest('button, input, select, textarea, a, [role="button"]');

    logProductCheckout(`Section header clicked`, {
      sectionId,
      targetElement: e.target.tagName,
      targetClass: e.target.className,
      isInteractiveElement: !!isInteractiveElement,
      willToggle: !isInteractiveElement
    });

    // Only toggle section if not clicking on an interactive element
    if (!isInteractiveElement) {
      toggleSection(sectionId);
    }
  };

  // Mark section as completed
  const markSectionCompleted = (sectionId, isCompleted = true) => {
    setCompletedSections(prev => ({
      ...prev,
      [sectionId]: isCompleted
    }));
    logProductCheckout(`Section ${sectionId} marked as ${isCompleted ? 'completed' : 'incomplete'}`);
  };

  // Update section data
  const updateSectionData = (sectionId, data) => {
    setSectionData(prev => ({
      ...prev,
      [sectionId]: { ...prev[sectionId], ...data }
    }));
  };

  // Check if all required sections are completed
  const areAllRequiredSectionsCompleted = () => {
    const requiredSteps = checkoutSteps.filter(step => step.required !== false && !step.informationOnly);
    return requiredSteps.every(step => completedSections[step.id]);
  };

  // Handle payment initiation with monitoring
  const handlePaymentInitiation = async (paymentMethod, paymentData = {}) => {
    if (!orderId) {
      console.error("Cannot initiate payment: No order ID");
      return false;
    }

    // Check for existing payment
    const existingPayment = PaymentMonitor.checkPaymentStatus(orderId);
    if (existingPayment) {
      const warningData = PaymentMonitor.getDuplicatePaymentWarning(orderId);
      setPaymentWarningData(warningData);
      setShowPaymentWarning(true);
      return false;
    }

    // Mark payment as initiated
    const paymentRecord = PaymentMonitor.markPaymentInitiated(orderId, paymentMethod, {
      ...paymentData,
      totalAmount: totalForCurrentStore,
      currency: selectedCurrency,
      items: filteredCartItems?.length || 0
    });

    if (paymentRecord) {
      setPaymentInitiated(true);
      setPaymentMethodUsed(paymentMethod);

      // Save checkout progress as payment initiated (now async)
      try {
        await CheckoutProgress.saveProgress(orderId, {
          completedSections,
          sectionData,
          paymentInitiated: true,
          paymentMethod,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.warn("Failed to save checkout progress:", error);
      }

      logProductCheckout("Payment initiated", {
        orderId,
        paymentMethod,
        amount: totalForCurrentStore
      });

      return true;
    }

    return false;
  };

  // Handle recovery modal actions
  const handleRestoreCheckout = () => {
    if (recoveryData && recoveryData.checkout) {
      const restored = AbandonedCheckoutRecovery.restoreCheckout(recoveryData.checkout);
      
      if (restored.success) {
        const restoredOrderId = restored.orderId;
        
        logProductCheckout("Checkout restored", {
          orderId: restoredOrderId,
          currentOrderId: orderId,
          restoredSections: Object.keys(restored.progress.completedSections || {})
        });

        // If this is a different order, redirect to that order's checkout page
        if (restoredOrderId !== orderId) {
          // IMPORTANT: Dismiss the current checkout to prevent infinite loop
          AbandonedCheckoutRecovery.dismissAbandonedCheckout(orderId);
          
          // Set a flag to indicate we just restored (prevent immediate recovery dialog)
          sessionStorage.setItem('just_restored_checkout', 'true');
          sessionStorage.setItem('restore_timestamp', Date.now().toString());
          
          // Construct the checkout URL for the restored order
          const currentPath = window.location.pathname;
          const storePath = currentPath.split('/checkout')[0]; // Get the store path
          const restoredCheckoutUrl = `${storePath}/checkout?orderId=${restoredOrderId}`;
          
          console.log(`Redirecting to restored order: ${restoredCheckoutUrl}`);
          console.log(`Dismissed current order: ${orderId}`);
          window.location.href = restoredCheckoutUrl;
          return;
        }

        // If it's the same order, just restore the state
        const { progress } = restored;
        
        if (progress.completedSections) {
          setCompletedSections(progress.completedSections);
        }
        if (progress.sectionData) {
          setSectionData(progress.sectionData);
        }
        if (progress.openSections) {
          setOpenSections(progress.openSections);
        }

        setShowRecoveryModal(false);
        setRecoveryData(null);
      } else {
        console.error('Failed to restore checkout:', restored.error);
        alert('Không thể khôi phục quá trình thanh toán. Vui lòng thử lại.');
      }
    }
  };

  const handleDismissRecovery = async () => {
    if (recoveryData && recoveryData.checkout) {
      try {
        await AbandonedCheckoutRecovery.dismissAbandonedCheckout(recoveryData.checkout.orderId);
        logProductCheckout("Recovery dismissed", {
          dismissedOrderId: recoveryData.checkout.orderId
        });
      } catch (error) {
        console.warn("Failed to dismiss recovery on server:", error);
      }
    }

    setShowRecoveryModal(false);
    setRecoveryData(null);
  };

  // Handle payment warning actions
  const handleProceedWithPayment = () => {
    setShowPaymentWarning(false);
    setPaymentWarningData(null);
    // Allow user to proceed with new payment
  };

  const handleCancelPayment = () => {
    setShowPaymentWarning(false);
    setPaymentWarningData(null);
    // Keep existing payment status
  };

  // Handle report download
  const handleDownloadPDF = () => {
    setIsGeneratingPDF(true);
    setPdfMessage(null);

    try {
      logProductCheckout("Generating checkout status PDF", {
        orderId,
        completedSections: Object.keys(completedSections).filter(key => completedSections[key]),
        totalSections: checkoutSteps.length
      });

      const result = generateCheckoutStatusPDF({
        orderId,
        storeObject,
        checkoutSteps,
        completedSections,
        sectionData,
        groupedProducts,
        selectedCurrency,
        totalForCurrentStore,
        paymentInitiated,
        paymentMethodUsed,
        addressData
      });

      if (result.success) {
        setPdfMessage({
          type: 'success',
          text: result.message
        });

        logProductCheckout(`${result.type === 'html' ? 'HTML report' : 'PDF'} generated successfully`, {
          orderId,
          filename: result.filename,
          type: result.type || 'pdf'
        });
      } else {
        setPdfMessage({
          type: 'error',
          text: result.message
        });

        logProductCheckout("PDF generation failed", {
          orderId,
          error: result.error
        });
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      setPdfMessage({
        type: 'error',
        text: 'Có lỗi xảy ra khi tạo PDF. Vui lòng thử lại.'
      });

      logProductCheckout("PDF generation error", {
        orderId,
        error: error.message
      });
    } finally {
      setIsGeneratingPDF(false);

      // Clear message after 5 seconds
      setTimeout(() => {
        setPdfMessage(null);
      }, 5000);
    }
  };

  // Render form for specific step
  const renderSectionForm = (step) => {
    const FormComponent = stepFormComponents[step.id];
    
    if (!FormComponent) {
      return (
        <div className="p-4 text-center text-gray-500">
          <FaInfoCircle className="mx-auto mb-2" />
          <p>Form component cho "{step.title}" chưa được triển khai.</p>
        </div>
      );
    }

    const formProps = {
      orderId,
      storeObject,
      addressData,
      onAddressChange,
      selectedPaymentMethod,
      setSelectedPaymentMethod,
      selectedCurrency,
      totalForCurrentStore,
      filteredCartItems,
      cart,
      context,
      orderCompleted,
      setOrderCompleted,
      allowedPaymentMethods,
      saveOrderToLocalStorage,
      // Additional props for section-based form
      onComplete: (data) => {
        updateSectionData(step.id, data);
        // For documents section, check if completion status was provided
        if (step.id === 'documents' && data && typeof data === 'object' && 'completed' in data) {
          markSectionCompleted(step.id, data.completed);

          // Log document completion status for debugging
          logProductCheckout(`Documents section completion status`, {
            completed: data.completed,
            documentsUploaded: data.documentsUploaded,
            documentsApproved: data.documentsApproved,
            status: data.status
          });
        } else {
          markSectionCompleted(step.id, true);
        }
      },
      onDataChange: (data) => {
        updateSectionData(step.id, data);
      },
      initialData: sectionData[step.id] || {},
      isStandalone: true, // Indicate this is not part of a step wizard
      // Payment monitoring props (only for payment section)
      ...(step.id === 'payment' && {
        onPaymentInitiated: handlePaymentInitiation,
        paymentInitiated,
        paymentMethodUsed
      })
    };

    return <FormComponent {...formProps} />;
  };

  // Render product summary
  const renderProductSummary = () => {
    if (!groupedProducts || Object.keys(groupedProducts).length === 0) {
      return (
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-medium mb-3">Tóm tắt đơn hàng</h3>
          <p className="text-gray-500">Không có sản phẩm trong giỏ hàng</p>
        </div>
      );
    }

    return (
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-medium mb-3">Tóm tắt đơn hàng</h3>
        {Object.entries(groupedProducts).map(([type, products]) => (
          <div key={type} className="mb-4">
            <h4 className="font-medium text-gray-700 mb-2">{type} ({products.length})</h4>
            <div className="space-y-3 pl-2 md:pl-4">
              {products.map((product, index) => (
                <div key={`${product.sku}-${index}`} className="flex items-center border-b pb-3">
                  <div className="flex-shrink-0 w-12 h-12 md:w-16 md:h-16 bg-gray-100 rounded overflow-hidden mr-2 md:mr-3">
                    {product.image && (
                      <img 
                        src={Array.isArray(product.image) ? product.image[0] : product.image} 
                        alt={product.name} 
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm md:text-base truncate">{product.name}</h4>
                    <p className="text-xs md:text-sm text-gray-600">SKU: {product.sku}</p>
                    <div className="flex justify-between mt-1 text-xs md:text-sm">
                      <span>x{product.quantity}</span>
                      <span className="font-medium">{selectedCurrency}{product.price?.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
        <div className="border-t pt-3 mt-3">
          <div className="flex justify-between items-center text-lg font-semibold">
            <span>Tổng cộng:</span>
            <span>{selectedCurrency}{totalForCurrentStore?.toLocaleString()}</span>
          </div>
        </div>
      </div>
    );
  };

  // Handle final checkout submission
  const handleFinalCheckout = async () => {
    if (!areAllRequiredSectionsCompleted()) {
      setError("Vui lòng hoàn thành tất cả các phần bắt buộc trước khi thanh toán.");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Collect all section data
      const allData = Object.keys(sectionData).reduce((acc, sectionId) => {
        return { ...acc, ...sectionData[sectionId] };
      }, {});

      logProductCheckout("Starting final checkout", { allData, orderId });

      // Continue with existing checkout logic
      setOrderCompleted(true);
      
      if (saveOrderToLocalStorage) {
        await saveOrderToLocalStorage(allData);
      }

    } catch (err) {
      console.error("Error during final checkout:", err);
      setError("Có lỗi xảy ra trong quá trình thanh toán. Vui lòng thử lại.");
    } finally {
      setIsLoading(false);
    }
  };

  // If no steps, show a loading indicator
  if (!checkoutSteps || checkoutSteps.length === 0) {
    return (
      <div className="flex justify-center items-center p-10">
        <FaSpinner className="animate-spin mr-2 text-blue-500" />
        <span>Đang tải quá trình thanh toán...</span>
      </div>
    );
  }

  return (
    <div className="product-checkout-process w-full max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="mb-2">Hoàn tất đơn hàng</h2>
        <p className="text-gray-600">Vui lòng điền đầy đủ thông tin trong tất cả các phần bên dưới. Bạn có thể điền theo thứ tự bất kỳ.</p>

      </div>

      {/* Product Summary */}
      {renderProductSummary()}

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <FaExclamationTriangle className="text-red-500 mt-0.5 mr-2" />
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Progress Overview */}
      <div className="bg-blue-50 rounded-lg p-4 mb-6">
        <h3 className="font-medium mb-3">Tiến độ hoàn thành</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {checkoutSteps.map((step) => (
            <div 
              key={step.id} 
              className={`flex items-center text-sm p-2 rounded ${
                completedSections[step.id] 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}
            >
              {completedSections[step.id] ? (
                <FaCheckCircle className="mr-2 text-green-500" />
              ) : (
                <FaCircle className="mr-2 text-gray-400" />
              )}
              <span className="truncate">{step.title}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Checkout Sections */}
      <div className="space-y-4">
        {checkoutSteps.map((step, index) => (
          <div key={step.id} className="border border-gray-200 rounded-lg overflow-hidden">
            {/* Section Header */}
            <div 
              className={`flex items-center justify-between p-4 cursor-pointer transition-colors ${
                completedSections[step.id] 
                  ? 'bg-green-50 border-b border-green-200' 
                  : 'bg-white border-b border-gray-200'
              }`}
              onClick={(e) => handleSectionHeaderClick(e, step.id)}
            >
              <div className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full mr-3 ${
                  completedSections[step.id]
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {completedSections[step.id] ? (
                    <FaCheckCircle className="text-sm" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-lg">{step.title}</h3>
                  <p className="text-sm text-gray-600">{step.description}</p>
                  <div className="flex items-center mt-1">
                    {step.type !== 'COMMON' && (
                      <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded mr-2">
                        {step.type}
                      </span>
                    )}
                    {step.informationOnly ? (
                      <span className="inline-block bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">
                        Chỉ thông tin
                      </span>
                    ) : step.required !== false ? (
                      <span className="inline-block bg-red-100 text-red-600 text-xs px-2 py-1 rounded">
                        Bắt buộc
                      </span>
                    ) : (
                      <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                        Tùy chọn
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center">
                {completedSections[step.id] && (
                  <span className="text-green-600 text-sm mr-2">Hoàn thành</span>
                )}
                {step.id === 'payment' && (
                  <span className="text-blue-600 text-xs mr-2 bg-blue-50 px-2 py-1 rounded">
                    -
                  </span>
                )}
                {openSections[step.id] ? (
                  <FaChevronUp className={step.id === 'payment' ? 'text-blue-500' : 'text-gray-500'} />
                ) : (
                  <FaChevronDown className="text-gray-500" />
                )}
              </div>
            </div>

            {/* Section Content */}
            {openSections[step.id] && (
              <div
                className="p-4 bg-gray-50"
                onClick={(e) => {
                  // Prevent clicks within the section content from bubbling up to the header
                  logProductCheckout(`Section content clicked`, {
                    sectionId: step.id,
                    targetElement: e.target.tagName,
                    targetClass: e.target.className,
                    stoppingPropagation: true
                  });
                  e.stopPropagation();
                }}
              >
                {renderSectionForm(step)}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Order Status Summary */}
      <div className="mt-8 bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium mb-4">Tình trạng đơn hàng</h3>
        
        {/* Detailed Checklist */}
        <div className="mb-6">
          <h4 className="font-medium mb-3 text-gray-700">Trạng thái hoàn thành:</h4>
          <div className="space-y-3">
            {checkoutSteps.map((step) => {
              const isCompleted = completedSections[step.id];
              const isRequired = step.required !== false;
              
              let statusText = "";
              let statusColor = "";
              let icon = null;
              
              // Special handling for information-only steps (like SIM activation)
              if (step.informationOnly) {
                statusText = "HƯỚNG DẪN";
                statusColor = "text-blue-600";
                icon = <FaInfoCircle className="text-blue-500" />;
              }
              // Special handling for document section
              else if (step.id === 'documents') {
                const documentsData = sectionData.documents;

                if (isCompleted && documentsData?.documentsApproved) {
                  statusText = "Giấy tờ đã hoàn tất";
                  statusColor = "text-green-600";
                  icon = <FaCheckCircle className="text-green-500" />;
                } else if (isCompleted && documentsData?.documentsUploaded) {
                  statusText = "Đã tải lên - Đang chờ xét duyệt";
                  statusColor = "text-yellow-600";
                  icon = <FaSpinner className="text-yellow-500" />;
                } else if (isCompleted) {
                  statusText = "Đã tải lên - Đang chờ xét duyệt";
                  statusColor = "text-yellow-600";
                  icon = <FaSpinner className="text-yellow-500" />;
                } else if (isRequired) {
                  statusText = "Cần hoàn thành";
                  statusColor = "text-red-600";
                  icon = <FaExclamationTriangle className="text-red-500" />;
                } else {
                  statusText = "Tùy chọn";
                  statusColor = "text-gray-500";
                  icon = <FaInfoCircle className="text-gray-400" />;
                }
              }
              // Special handling for payment section
              else if (step.id === 'payment') {
                if (paymentInitiated) {
                  statusText = `Đã khởi tạo thanh toán - ${paymentMethodUsed}`;
                  statusColor = "text-blue-600";
                  icon = <FaSpinner className="text-blue-500" />;
                } else if (isCompleted) {
                  statusText = "Hoàn thành";
                  statusColor = "text-green-600";
                  icon = <FaCheckCircle className="text-green-500" />;
                } else if (isRequired) {
                  statusText = "Cần hoàn thành";
                  statusColor = "text-red-600";
                  icon = <FaExclamationTriangle className="text-red-500" />;
                } else {
                  statusText = "Tùy chọn";
                  statusColor = "text-gray-500";
                  icon = <FaInfoCircle className="text-gray-400" />;
                }
              } else {
                // Standard logic for other sections
                if (isCompleted) {
                  statusText = "Hoàn thành";
                  statusColor = "text-green-600";
                  icon = <FaCheckCircle className="text-green-500" />;
                } else if (isRequired) {
                  statusText = "Cần hoàn thành";
                  statusColor = "text-red-600";
                  icon = <FaExclamationTriangle className="text-red-500" />;
                } else {
                  statusText = "Tùy chọn";
                  statusColor = "text-gray-500";
                  icon = <FaInfoCircle className="text-gray-400" />;
                }
              }

              return (
                <div key={step.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center">
                    {icon}
                    <div className="ml-3">
                      <p className="font-medium text-sm">{step.title}</p>
                      <p className="text-xs text-gray-600">{step.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`text-sm font-medium ${statusColor}`}>
                      {statusText}
                    </span>
                    {step.id === 'documents' && isCompleted && (
                      <p className="text-xs text-gray-500 mt-1">
                        {sectionData.documents?.documentsApproved
                          ? "Giấy tờ đã được xét duyệt"
                          : "Thời gian xét duyệt: 24h"
                        }
                      </p>
                    )}
                    {step.id === 'payment' && paymentInitiated && (
                      <p className="text-xs text-gray-500 mt-1">
                        Đang chờ hoàn tất thanh toán
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Summary */}
        <div className="mb-4 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start">
            <FaInfoCircle className="text-blue-500 mt-1 mr-2" />
            <div>
              <p className="text-sm font-medium text-blue-800 mb-1">Tóm tắt trạng thái:</p>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Hoàn thành: {Object.values(completedSections).filter(Boolean).length}/{checkoutSteps.filter(step => !step.informationOnly).length} phần</li>
                <li>• Bắt buộc: {checkoutSteps.filter(step => step.required !== false && !step.informationOnly).filter(step => completedSections[step.id]).length}/{checkoutSteps.filter(step => step.required !== false && !step.informationOnly).length} phần</li>
                {sectionData.documents && (
                  <li>• Giấy tờ sẽ được xét duyệt trong vòng 24h làm việc</li>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="mb-4 p-4 border-l-4 border-green-500 bg-green-50">
          <h4 className="font-medium text-green-800 mb-2">Các bước tiếp theo:</h4>
          {areAllRequiredSectionsCompleted() ? (
            <div className="text-sm text-green-700 space-y-1">
              <p>1. ✅ Thông tin đã được thu thập đầy đủ</p>
              <p>2. 🔄 Thanh toán sẽ được xử lý qua phương thức đã chọn</p>
              <p>3. 📦 Đơn hàng sẽ được xử lý sau khi thanh toán thành công</p>
              <p>4. 🚚 Sản phẩm sẽ được giao theo hình thức đã chọn</p>
              {sectionData.documents && (
                <p>5. 📋 Giấy tờ sẽ được xét duyệt song song với việc giao hàng</p>
              )}
            </div>
                     ) : (
             <div className="text-sm text-yellow-700 space-y-1">
               <p>⚠️ Một số thông tin vẫn còn thiếu</p>
               <p>💡 Bạn có thể điền các phần theo thứ tự bất kỳ</p>
               <p>💳 Thanh toán vẫn có thể thực hiện được</p>
               <p>📋 Thông tin còn thiếu có thể bổ sung sau</p>
             </div>
           )}
         </div>

         {/* Order Completion Notice */}
         <div className="text-center p-4 bg-gray-50 rounded">
           <div>
             <p className="text-sm font-medium text-gray-700 mb-1">
               🎉 Đơn hàng sẽ tự động hoàn tất sau khi thanh toán thành công và giấy tờ được xét duyệt.
             </p>
             {!areAllRequiredSectionsCompleted() && (
               <p className="text-xs text-yellow-600 mt-2">
                 💡 Thông tin còn thiếu có thể được cung cấp sau khi thanh toán. Liên hệ với chúng tôi nếu cần hỗ trợ.
               </p>
             )}
           </div>
        </div>

        {/* Additional PDF Download Button */}
        <div className="mt-4 text-center">
          <button
            onClick={handleDownloadPDF}
            disabled={isGeneratingPDF}
            className={`pdf-download-btn inline-flex items-center px-6 py-3 rounded-lg text-sm font-medium ${
              isGeneratingPDF
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isGeneratingPDF ? (
              <>
                <FaSpinner className="animate-spin mr-2" />
                Đang tạo PDF...
              </>
            ) : (
              <>
                <FaDownload className="mr-2" />
                Tải xuống báo cáo trạng thái
              </>
            )}
          </button>
          <p className="text-xs text-gray-500 mt-2">
            Tải xuống báo cáo chi tiết về trạng thái đơn hàng (có thể in thành PDF)
          </p>
        </div>
      </div>

      {/* Collapse All Sections - Since we have accordion behavior, we only show collapse option */}
      {/* Note: Payment section will remain open even when collapsing all */}
      <div className="mt-4 flex justify-center">
        {Object.values(openSections).some(isOpen => isOpen) && (
          <button
            onClick={() => {
              const newState = {};
              checkoutSteps.forEach(step => {
                // Keep payment section open, collapse others
                newState[step.id] = step.id === 'payment' ? true : false;
              });
              setOpenSections(newState);
            }}
            className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
          >
            <FaChevronUp className="mr-1" />
            Thu gọn tất cả (trừ thanh toán)
          </button>
        )}
      </div>

      {/* Document Preview Modal */}
      {previewDocument && (
        <DocumentPreviewModal
          document={previewDocument}
          onClose={() => setPreviewDocument(null)}
        />
      )}

      {/* Payment Warning Modal */}
      {showPaymentWarning && paymentWarningData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <div className="flex items-center mb-4">
              <FaExclamationTriangle className="text-yellow-500 mr-3 text-xl" />
              <h3 className="text-lg font-semibold">Cảnh báo thanh toán trùng lặp</h3>
            </div>
            
            <div className="mb-6">
              <p className="text-gray-700 mb-3">{paymentWarningData.message}</p>
              <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                <p className="text-sm">
                  <strong>Phương thức trước:</strong> {paymentWarningData.previousMethod}
                </p>
                <p className="text-sm">
                  <strong>Thời gian:</strong> {paymentWarningData.previousTimestamp}
                </p>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleCancelPayment}
                className="flex-1 px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              >
                Hủy
              </button>
              <button
                onClick={handleProceedWithPayment}
                className="flex-1 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                Tiếp tục thanh toán mới
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Abandoned Checkout Recovery Modal */}
      {showRecoveryModal && recoveryData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <div className="flex items-center mb-4">
              <FaUndo className="text-blue-500 mr-3 text-xl" />
              <h3 className="text-lg font-semibold">Khôi phục quá trình thanh toán</h3>
            </div>
            
            <div className="mb-6">
              <p className="text-gray-700 mb-3">{recoveryData.message}</p>
              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <p className="text-sm">
                  <strong>Đơn hàng:</strong> {recoveryData.checkout.orderId}
                </p>
                <p className="text-sm">
                  <strong>Thời gian bỏ dở:</strong> {new Date(recoveryData.checkout.timestamp).toLocaleString()}
                </p>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleDismissRecovery}
                className="flex-1 px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
              >
                <FaTimes className="inline mr-1" />
                Bắt đầu lại
              </button>
              <button
                onClick={handleRestoreCheckout}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                <FaUndo className="inline mr-1" />
                Khôi phục
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Status Indicator */}
      {paymentInitiated && (
        <div className="fixed bottom-4 right-4 bg-green-100 border border-green-300 text-green-800 px-4 py-2 rounded-lg shadow-lg">
          <div className="flex items-center">
            <FaCheckCircle className="mr-2" />
            <div>
              <p className="text-sm font-medium">Đã khởi tạo thanh toán</p>
              <p className="text-xs">Phương thức: {paymentMethodUsed}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductCheckoutSinglePage; 