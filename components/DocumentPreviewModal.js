import React, { useState, useEffect } from 'react';
import { FaSpinner, FaTimes, FaDownload, FaExternalLinkAlt } from 'react-icons/fa';
import { getCustomerId } from '../utils/customerAuth';

/**
 * Document Preview Modal Component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.document - Document object with documentType and filename
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {function} props.onClose - Function to call when closing the modal
 */
const DocumentPreviewModal = ({ document, isOpen, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [documentUrl, setDocumentUrl] = useState('');
  const [documentType, setDocumentType] = useState('');

  useEffect(() => {
    if (!isOpen || !document) return;

    const loadDocument = async () => {
      setLoading(true);
      setError(null);

      try {
        if (!document.documentType || !document.filename) {
          throw new Error('Invalid document information');
        }

        // Get the customer ID
        const customerId = getCustomerId();
        if (!customerId) {
          throw new Error('Customer ID is required');
        }

        // Get the document URL with customer ID in the query params
        const url = `/api/documents/view/${document.documentType}/${document.filename}?customerId=${customerId}`;
        
        // Determine if it's an image or PDF based on filename
        const isPdf = document.filename.toLowerCase().endsWith('.pdf');
        setDocumentType(isPdf ? 'pdf' : 'image');
        
        // Set the document URL
        setDocumentUrl(url);
      } catch (error) {
        console.error('Error loading document preview:', error);
        setError('Failed to load document preview: ' + error.message);
      } finally {
        setLoading(false);
      }
    };

    loadDocument();
  }, [isOpen, document]);

  // If not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center border-b p-4">
          <h3 className="text-lg font-medium">
            {document && document.documentType === 'idCard' && 'CMND/CCCD/Hộ chiếu'}
            {document && document.documentType === 'photo' && 'Ảnh chân dung'}
            {document && document.documentType === 'proofOfResidence' && 'Giấy tờ chứng minh nơi cư trú'}
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={() => window.open(documentUrl, '_blank')}
              className="text-gray-700 hover:bg-gray-100 p-2 rounded"
              title="Mở trong tab mới"
            >
              <FaExternalLinkAlt />
            </button>
            <a
              href={documentUrl}
              download={document?.filename}
              className="text-gray-700 hover:bg-gray-100 p-2 rounded"
              title="Tải xuống"
            >
              <FaDownload />
            </a>
            <button
              onClick={onClose}
              className="text-gray-700 hover:bg-gray-100 p-2 rounded"
              title="Đóng"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Document Content */}
        <div className="flex-1 overflow-auto p-4 flex items-center justify-center bg-gray-100">
          {loading ? (
            <div className="flex flex-col items-center">
              <FaSpinner className="animate-spin h-10 w-10 text-blue-500 mb-2" />
              <p>Đang tải tài liệu...</p>
            </div>
          ) : error ? (
            <div className="text-red-500 text-center">
              <p>{error}</p>
              <button
                onClick={onClose}
                className="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Đóng
              </button>
            </div>
          ) : (
            <>
              {documentType === 'pdf' ? (
                <object
                  data={documentUrl}
                  type="application/pdf"
                  className="w-full h-full"
                >
                  <p>
                    Trình duyệt của bạn không hỗ trợ xem PDF.{' '}
                    <a 
                      href={documentUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline"
                    >
                      Tải xuống
                    </a>
                  </p>
                </object>
              ) : (
                <img
                  src={documentUrl}
                  alt="Document preview"
                  className="max-w-full max-h-full object-contain"
                  onLoad={() => setLoading(false)}
                  onError={() => setError('Failed to load image')}
                />
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex justify-end">
          <button
            onClick={onClose}
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  );
};

export default DocumentPreviewModal; 