import React, { useEffect } from 'react';

const TawkEmbed = ({
  propertyId = '67a2d3593a8427326079cf7a',
  widgetId = '1ija0j5e4'
}) => {
  useEffect(() => {
    // Initialize Tawk API variables
    window.Tawk_API = window.Tawk_API || {};
    window.Tawk_LoadStart = new Date();
    window.Tawk_API.embedded = `tawk_${propertyId}`;

    // Create and add script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://embed.tawk.to/${propertyId}/${widgetId}`;
    script.charset = 'UTF-8';
    script.setAttribute('crossorigin', '*');
    
    const firstScript = document.getElementsByTagName('script')[0];
    firstScript.parentNode.insertBefore(script, firstScript);

    // Add custom styles for mobile
    const style = document.createElement('style');
    style.textContent = `
      @media (max-width: 768px) {
        #tawk_${propertyId} iframe {
          max-width: 100% !important;
          width: 100% !important;
          margin: 0 auto !important;
        }
      }
    `;
    document.head.appendChild(style);

    // Cleanup
    return () => {
      if (firstScript.parentNode) {
        firstScript.parentNode.removeChild(script);
      }
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
      // Remove Tawk_API if it exists
      if (window.Tawk_API) {
        window.Tawk_API.remove();
      }
    };
  }, [propertyId, widgetId]);

  return (
    <div 
      id={`tawk_${propertyId}`} 
      className="w-full max-w-full md:max-w-2xl mx-auto px-4"
    ></div>
  );
};

export default TawkEmbed;
