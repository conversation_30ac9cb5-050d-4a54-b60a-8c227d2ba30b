import React, { useState, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';
import { chatApi } from '../../services/chatApi';
import { sheetsApi } from '../../services/sheetsApi';
import { 
  ChatMessage,
  ChatInput,
  ChatButton
} from './StyledChatComponents';

export const ChatCore = ({ 
  initialMessage = "Hello! I'm AbnAsia. How can I help you today?",
  className = "",
  containerClassName = ""
}) => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState('');
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [hasUserSentMessage, setHasUserSentMessage] = useState(false);
  const messagesContainerRef = useRef(null);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    setMessages([
      {
        content: initialMessage,
        sender: 'AbnAsia.org',
        timestamp: new Date().toISOString()
      }
    ]);
  }, [initialMessage]);

  const isNearBottom = () => {
    const container = messagesContainerRef.current;
    if (!container) return true;
    
    const threshold = 100; // pixels from bottom
    const bottomPosition = container.scrollHeight - container.scrollTop - container.clientHeight;
    return bottomPosition <= threshold;
  };

  const handleScroll = () => {
    setShouldAutoScroll(isNearBottom());
  };

  const scrollToBottom = () => {
    if (shouldAutoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  };

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  useEffect(() => {
    // Only auto-scroll if user has sent at least one message
    const lastMessage = messages[messages.length - 1];
    if (hasUserSentMessage && (lastMessage?.sender === 'user' || shouldAutoScroll)) {
      scrollToBottom();
    }
  }, [messages, streamingMessage, hasUserSentMessage]);

  
  const saveChatToHistory = async (messages) => {
    try {
      await sheetsApi.saveChat(messages);
    } catch (error) {
      console.error('Failed to save chat:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;
    
    // Set the flag when user sends their first message
    setHasUserSentMessage(true);
    
    const userMessage = {
      content: inputMessage,
      sender: 'user',
      timestamp: new Date().toISOString()
    };
    
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setInputMessage('');
    setIsLoading(true);
    setStreamingMessage('');

    const apiMessages = [
      {
        role: 'system',
        content: 'You are ABNCopilot, a smart, inquisitive and helpful personal assistant. You were created by AbnAsia.org. More information about you, the company or ABN apps can be found at https://abnasia.org. About ABN Asia: AiUTOMATING PEOPLE, ABN ASIA was founded by people with deep roots in academia, with work experience in the US, Holland, Hungary, Japan, South Korea, Singapore, and Vietnam. ABN Asia is where academia and technology meet opportunity. With our cutting-edge solutions and competent software development services, we are re helping businesses level up and take on the global scene. Our commitment: Faster. Better. More reliable. In most cases: Cheaper as well. Feel free to reach out to us whenever you require IT services, digital consulting, off-the-shelf software solutions, or if you would like to send us requests for proposals (RFPs). You can contact us at +*********** (Asia# Mobile, WhatsApp, Telegram, Viber, Zalo); +*********** (US# Mobile, WhatsApp, Telegram) <EMAIL> . We are ready to assist you with all your technology needs.'
      },
      ...updatedMessages.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: msg.content
      }))
    ];

    try {
      await chatApi.streamCompletion({
        messages: apiMessages,
        onToken: (accumulatedMessage) => {
          setStreamingMessage(accumulatedMessage);
        },
        onError: (error) => {
          const errorMessage = {
            content: "I apologize, but I'm having trouble connecting right now. Please try again later.",
            sender: 'AbnAsia.org',
            timestamp: new Date().toISOString()
          };
          setMessages(prev => [...prev, errorMessage]);
          setIsLoading(false);
          saveChatToHistory([...updatedMessages, errorMessage]);
        },
        onFinish: async (finalMessage) => {
          const botMessage = {
            content: finalMessage,
            sender: 'AbnAsia.org',
            timestamp: new Date().toISOString()
          };
          const finalMessages = [...updatedMessages, botMessage];
          setMessages(finalMessages);
          setStreamingMessage('');
          setIsLoading(false);
          await saveChatToHistory(finalMessages);
        }
      });
    } catch (error) {
      console.error('Failed to process message:', error);
      const errorMessage = {
        content: "An error occurred while processing your message. Please try again.",
        sender: 'AbnAsia.org',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
      setIsLoading(false);
    }
  };
  
  return (
    <div className={containerClassName}>
      <div 
        ref={messagesContainerRef}
        className={`h-96 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-blue-200/50 scrollbar-track-transparent ${className}`}
      >
        {messages.map((message, index) => (
          <ChatMessage 
            key={index}
            isUser={message.sender === 'user'}
          >
            {message.content}
          </ChatMessage>
        ))}
        
        {streamingMessage && (
          <ChatMessage isUser={false}>
            {streamingMessage}
          </ChatMessage>
        )}

        {isLoading && !streamingMessage && (
          <ChatMessage isUser={false}>
            <div className="flex gap-1">
              <div className="w-2 h-2 rounded-full border border-blue-400 animate-bounce" />
              <div className="w-2 h-2 rounded-full border border-blue-400 animate-bounce delay-100" />
              <div className="w-2 h-2 rounded-full border border-blue-400 animate-bounce delay-200" />
            </div>
          </ChatMessage>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="p-4 border-t border-dashed border-blue-500/30">
        <div className="flex gap-2">
          <ChatInput
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && !isLoading && handleSendMessage()}
            disabled={isLoading}
          />
          <ChatButton
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
          >
            <Send size={18} />
          </ChatButton>
        </div>
      </div>
    </div>
  );
};