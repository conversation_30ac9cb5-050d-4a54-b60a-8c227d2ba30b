import React, { useState } from 'react';
import Image from '../Image';

const TawkButton = ({ 
  propertyId = '67a2d3593a8427326079cf7a',
  widgetId = '1ija0j5e4',
  buttonText = 'Chat with us',
  full = false,
  small = false,
  imagesrc = ''
}) => {
  const [isLoaded, setIsLoaded] = useState(false);

  const loadTawkTo = () => {
    if (!isLoaded) {
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://embed.tawk.to/' + propertyId + '/' + widgetId;
      script.charset = 'UTF-8';
      script.setAttribute('crossorigin', '*');
      
      script.onload = () => {
        setIsLoaded(true);
        // Open the chat window after script loads
        if (window.Tawk_API) {
          window.Tawk_API.maximize();
          window.Tawk_API.showWidget();
          window.Tawk_API.popup();
        }
      };

      document.body.appendChild(script);
    } else if (window.Tawk_API) {
      // If already loaded, just open the chat
      window.Tawk_API.maximize();
      window.Tawk_API.showWidget();
      window.Tawk_API.popup();
    }
  };

  let classNames = "text-sm font-bold tracking-wider bg-transparent hover:bg-black text-black font-semibold hover:text-white border-2 border-black hover:border-transparent";
  
  if (full) {
    classNames = `${classNames} w-full`;
  }

  if (small) {
    classNames += " py-2 px-1";
  } else {
    classNames += " py-4 px-12";
  }

  return (
    <button 
      onClick={loadTawkTo}
      className={classNames}
    >
      <div>
        {buttonText}
        {imagesrc && <Image src={imagesrc} width={20} height={20} />}
      </div>
    </button>
  );
};

export default TawkButton; 