import React, { useState } from 'react';
import { ChatCore } from './ChatCore';
import { ChatHeader } from './ChatHeader';
import { MessageSquare, X } from 'lucide-react';

const InlineChat = ({ 
  className = "",
  title,
  initialMessage,
  miniMode = false
}) => {
  const [isMini, setIsMini] = useState(miniMode);
  const [hasStartedChat, setHasStartedChat] = useState(false);

  const handleStartChat = () => {
    setIsMini(false);
    setHasStartedChat(true);
  };

  const handleMinimize = () => {
    setIsMini(true);
  };

  if (isMini) {
    return (
      <div 
        className={`
          cursor-pointer 
          transition-all 
          duration-300 
          hover:scale-105
          flex 
          items-center 
          gap-3 
          p-4 
          bg-white 
          border 
          border-blue-500/30 
          rounded-lg 
          shadow-lg 
          ${className}
        `}
        onClick={handleStartChat}
      >
        <MessageSquare className="w-5 h-5 text-blue-500" />
        <span className="font-medium text-gray-700">
          {hasStartedChat ? 'Continue Chat' : 'Ai Chat'}
        </span>
      </div>
    );
  }

  return (
    <div className={`
      relative
      border 
      border-blue-500/30 
      rounded-lg 
      shadow-lg 
      ${className}
      transition-all 
      duration-300
    `}>
      <div className="absolute -top-3 -right-3">
        <button
          onClick={handleMinimize}
          className="
            p-1.5
            bg-white
            border
            border-gray-200
            rounded-full
            shadow-md
            hover:bg-gray-50
            transition-colors
          "
        >
          <X className="w-4 h-4 text-gray-500" />
        </button>
      </div>
      
      <ChatHeader title={title} />
      <ChatCore 
        initialMessage={initialMessage}
        containerClassName="border-t border-blue-500/30"
      />
    </div>
  );
};

export default InlineChat;