import React from 'react';

const StyledChatBox = ({ 
  isMinimized,
  children,
  className = ""
}) => {
  return (
    <div className={`
      fixed bottom-4 right-4 z-50
      bg-white/5 
      transition-all duration-500 ease-in-out transform
      ${isMinimized ? 'h-12 w-80' : 'h-[32rem] w-80'}
      border border-dashed border-blue-500/50 
      rounded-2xl relative backdrop-blur-sm
      ${className}
    `}>
      {/* Decorative corners */}
      <div className="absolute top-0 left-0 w-4 h-4 border-t border-l border-blue-500 rounded-tl-2xl" />
      <div className="absolute top-0 right-0 w-4 h-4 border-t border-r border-blue-500 rounded-tr-2xl" />
      <div className="absolute bottom-0 left-0 w-4 h-4 border-b border-l border-blue-500 rounded-bl-2xl" />
      <div className="absolute bottom-0 right-0 w-4 h-4 border-b border-r border-blue-500 rounded-br-2xl" />
      {children}
    </div>
  );
};

const ChatHeader = ({ children }) => (
  <div className="p-3 flex justify-between items-center rounded-t-2xl relative border-b border-dashed border-blue-500/30">
    {children}
  </div>
);

const ChatMessage = ({ isUser, children }) => (
  <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} animate-fade-in`}>
    <div className={`
      max-w-[80%] p-3 relative
      ${isUser ? 'text-blue-500 border-blue-500' : 'text-gray-600 border-gray-400'}
      border border-dashed rounded-2xl transition-all duration-300 hover:scale-[1.02]
    `}>
      {children}
      <div className="absolute top-0 left-0 w-2 h-2 -translate-x-1 -translate-y-1 border-t border-l border-current" />
      <div className="absolute top-0 right-0 w-2 h-2 translate-x-1 -translate-y-1 border-t border-r border-current" />
      <div className="absolute bottom-0 left-0 w-2 h-2 -translate-x-1 translate-y-1 border-b border-l border-current" />
      <div className="absolute bottom-0 right-0 w-2 h-2 translate-x-1 translate-y-1 border-b border-r border-current" />
    </div>
  </div>
);

const ChatInput = ({ disabled, value, onChange, onKeyPress }) => (
  <input
    type="text"
    value={value}
    onChange={onChange}
    onKeyPress={onKeyPress}
    placeholder="LISTENING..."
    className="flex-1 p-3 rounded-xl border border-dashed border-blue-500/50 
      focus:outline-none focus:border-blue-500 bg-transparent text-blue-500 
      placeholder-blue-300 transition-all duration-300"
    disabled={disabled}
  />
);

const ChatButton = ({ onClick, disabled, children, className = "" }) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`
      p-3 border border-dashed border-blue-500 text-blue-500 rounded-xl
      hover:bg-blue-500/10 disabled:opacity-50 disabled:cursor-not-allowed
      transform hover:scale-105 transition-all duration-300
      ${className}
    `}
  >
    {children}
  </button>
);

const FloatingButton = ({ onClick }) => (
  <button
    onClick={onClick}
    aria-label="Floating action button"
    className="p-4 border border-dashed border-blue-500 text-blue-500 rounded-full 
      hover:bg-blue-500/10 transform hover:scale-110 transition-all duration-300 relative"
  >
    <div className="absolute top-0 left-0 w-2 h-2 border-t border-l border-blue-500" />
    <div className="absolute top-0 right-0 w-2 h-2 border-t border-r border-blue-500" />
    <div className="absolute bottom-0 left-0 w-2 h-2 border-b border-l border-blue-500" />
    <div className="absolute bottom-0 right-0 w-2 h-2 border-b border-r border-blue-500" />
  </button>
);

export { 
  StyledChatBox,
  ChatHeader,
  ChatMessage,
  ChatInput,
  ChatButton,
  FloatingButton
};
