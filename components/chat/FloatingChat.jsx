// components/chat/FloatingChat.jsx
import React, { useState } from 'react';
import { MessageCircle } from 'lucide-react';
import { ChatCore } from './ChatCore';
import { ChatHeader } from './ChatHeader';
import { StyledChatBox, FloatingButton } from './StyledChatComponents';

const FloatingChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  const toggleChat = () => {
    setIsOpen(!isOpen);
    setIsMinimized(false);
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isOpen && (
        <StyledChatBox isMinimized={isMinimized}>
          <ChatHeader 
            showHistory={showHistory}
            onToggleHistory={() => setShowHistory(!showHistory)}
            isMinimized={isMinimized}
            onToggleMinimize={toggleMinimize}
            onClose={toggleChat}
          />
          
          {!isMinimized && <ChatCore />}
        </StyledChatBox>
      )}

      {!isOpen && (
        <FloatingButton onClick={toggleChat}>
          <MessageCircle size={24} />
        </FloatingButton>
      )}
    </div>
  );
};

export default FloatingChat;