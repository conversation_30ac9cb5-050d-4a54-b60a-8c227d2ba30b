import React, { useEffect } from 'react';

const TawkChat = ({ 
  propertyId = '67a2d3593a8427326079cf7a',
  widgetId = '1ija0j5e4'
}) => {
  useEffect(() => {
    // Load Tawk.to script
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://embed.tawk.to/' + propertyId + '/' + widgetId;
    script.charset = 'UTF-8';
    script.setAttribute('crossorigin', '*');
    
    // Add script to document
    document.body.appendChild(script);

    // Cleanup on unmount
    return () => {
      document.body.removeChild(script);
      // Remove Tawk_API if it exists
      if (window.Tawk_API) {
        window.Tawk_API.remove();
      }
    };
  }, [propertyId, widgetId]);

  return null; // Tawk.to creates its own UI
};

export default TawkChat; 