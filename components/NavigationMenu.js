'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

const NavigationMenu = ({ store }) => {
  const [mounted, setMounted] = useState(false);

  // Only render the menu after component has mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return placeholder with same structure but no content to avoid hydration errors
    return (
      <div className="flex-shrink-0 flex items-center relative">
        <button className="absolute left-0 z-10 bg-white bg-opacity-75 p-1 rounded-full shadow">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <div id="menu-scroll" className="flex items-center overflow-x-hidden scroll-smooth w-[calc(5*6rem)]">
          <button className="pl-4 text-sm font-medium min-w-[6rem]"></button>
          <button className="text-sm font-medium min-w-[6rem]"></button>
          <button className="text-sm font-medium min-w-[6rem]"></button>
          <button className="text-sm font-medium min-w-[6rem]"></button>
        </div>
        <button className="absolute right-0 z-10 bg-white bg-opacity-75 p-1 rounded-full shadow">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    );
  }

  return (
    <div className="flex-shrink-0 flex items-center relative">
      <button
        className="absolute left-0 z-10 bg-white bg-opacity-75 p-1 rounded-full shadow hover:bg-opacity-100"
        onClick={() => {
          const container = document.getElementById('menu-scroll');
          container.scrollLeft -= container.offsetWidth;
        }}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <div id="menu-scroll" className="flex items-center overflow-x-hidden scroll-smooth w-[calc(5*6rem)]">
        <Link href={store ? `/${store}/topup` : `/topup`}>
          <button className="pl-4 text-sm font-medium hover:text-orange-500 transition-colors min-w-[6rem]">
            NẠP THẺ
          </button>
        </Link>
        
        <Link href={store ? `/${store}/shopping-guide` : `/shopping-guide`}>
          <button className="text-sm font-medium hover:text-orange-500 transition-colors min-w-[6rem]">
            GUIDE
          </button>
        </Link>

        <Link href={store ? `/${store}/our-story` : `/our-story`}>
          <button className="text-sm font-medium hover:text-orange-500 transition-colors min-w-[6rem]">
            SIM
          </button>
        </Link>
    
        <Link href={store ? `/${store}/contact` : `/contact`}>
          <button className="text-sm font-medium hover:text-orange-500 transition-colors min-w-[6rem]">
            HOT LINE
          </button>
        </Link>
      </div>

      <button
        className="absolute right-0 z-10 bg-white bg-opacity-75 p-1 rounded-full shadow hover:bg-opacity-100"
        onClick={() => {
          const container = document.getElementById('menu-scroll');
          container.scrollLeft += container.offsetWidth;
        }}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  );
};

export default NavigationMenu;