# BarcodeDisplay Component

A consolidated component for displaying payment barcodes with multiple display modes and features.

## Supported Payment Methods

- **7-11 iBON**: Taiwan 7-Eleven convenience store payment system using iBON kiosks
- **FamilyMart**: FamilyMart convenience store payment system (not iBON)
- **Generic**: Flexible support for other payment barcode systems

## Features

- Support for multiple payment methods with proper naming
- Multiple display modes: inline, visual, table
- Automatic barcode generation using JsBarcode
- Download functionality for barcodes
- Responsive design
- Error handling

## Usage Examples

### 1. Inline Display (for tables and compact views)

```javascript
import BarcodeDisplay from '../components/payment/BarcodeDisplay';

// For iBON barcodes in table rows
<BarcodeDisplay.Inline barcodes={order.paymentInfo.barcodes} />

// For custom barcode objects
<BarcodeDisplay.Inline 
  barcodes={{
    barcode1: "1234567890",
    barcode2: "0987654321",
    barcode3: "1122334455"
  }} 
/>
```

### 2. Visual Display with Actual Barcodes

```javascript
// iBON specific display
<BarcodeDisplay.IBON 
  paymentInfo={order.paymentInfo}
  orderInfo={{
    orderId: order.id,
    amount: order.totalAmount,
    currency: order.currency,
    expiry: new Date(order.paymentInfo.orderExpireDate).toLocaleDateString()
  }}
  displayMode="visual"
/>

// FamilyMart specific display  
<BarcodeDisplay.FamilyMart 
  paymentBarcode="123456,789012,345678" // comma-separated string
  orderInfo={{
    orderId: order.id,
    amount: order.totalAmount,
    currency: order.currency
  }}
  displayMode="visual"
/>

// Generic visual display
<BarcodeDisplay.Visual 
  barcodes={{
    barcode1: "1234567890",
    barcode2: "0987654321", 
    barcode3: "1122334455"
  }}
  title="Payment Barcodes"
  showDownload={true}
/>
```

### 3. Single Barcode Display

```javascript
<BarcodeDisplay.Single 
  code="1234567890"
  label="Payment Code"
  options={{
    width: 3,
    height: 100
  }}
/>
```

## Component API

### BarcodeDisplay.Inline

Props:
- `barcodes` (object): Object with barcode1, barcode2, barcode3 properties
- `className` (string): Additional CSS classes

### BarcodeDisplay.Visual

Props:
- `barcodes` (object): Object with barcode1, barcode2, barcode3 properties
- `title` (string): Display title (default: "Payment Barcodes")
- `showDownload` (boolean): Show download button (default: true)
- `className` (string): Additional CSS classes
- `barcodeOptions` (object): JsBarcode options

### BarcodeDisplay.IBON

Props:
- `paymentInfo` (object): Payment info object containing barcodes
- `orderInfo` (object): Order information for display and download
- `displayMode` (string): "visual", "inline", or "table"
- `className` (string): Additional CSS classes

### BarcodeDisplay.FamilyMart

Props:
- `paymentBarcode` (string): Comma-separated barcode string
- `orderInfo` (object): Order information for display and download
- `displayMode` (string): "visual" or "inline"
- `className` (string): Additional CSS classes

### BarcodeDisplay.Single

Props:
- `code` (string): Barcode value
- `label` (string): Display label
- `options` (object): JsBarcode options
- `className` (string): Additional CSS classes

## Migration Guide

### From SevenEleveniBON Component

Before:
```javascript
// Manual barcode rendering
const barcode1Ref = useRef(null);
useEffect(() => {
  if (barcode1Ref.current && ibonCode1) {
    JsBarcode(barcode1Ref.current, ibonCode1, barcodeOptions);
  }
}, [ibonCode1]);

<svg ref={barcode1Ref}></svg>
```

After:
```javascript
<BarcodeDisplay.IBON 
  paymentInfo={{ barcodes: { barcode1: ibonCode1, barcode2: ibonCode2, barcode3: ibonCode3 }}}
  displayMode="visual"
/>
```

### From Customer Orders Component

Before:
```javascript
// Local BarcodeDisplay component
const BarcodeDisplay = ({ code, label }) => { /* ... */ };

{barcodes.map(barcode => (
  <BarcodeDisplay key={index} code={barcode.code} label={barcode.label} />
))}
```

After:
```javascript
import PaymentBarcodeDisplay from '../payment/BarcodeDisplay';

<PaymentBarcodeDisplay.IBON 
  paymentInfo={order.paymentInfo}
  orderInfo={orderInfo}
  displayMode="visual"
/>
```

### From Order Details Page

Before:
```javascript
// Manual inline display
<div className="space-y-1">
  {barcodes.barcode1 && <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">Barcode 1: {barcodes.barcode1}</div>}
  {barcodes.barcode2 && <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">Barcode 2: {barcodes.barcode2}</div>}
  {barcodes.barcode3 && <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">Barcode 3: {barcodes.barcode3}</div>}
</div>
```

After:
```javascript
<BarcodeDisplay.Inline barcodes={order.paymentInfo.barcodes} />
```

## Benefits

1. **Consistency**: All barcode displays use the same styling and behavior
2. **Maintainability**: Changes only need to be made in one place
3. **Reusability**: Can be used across different components and pages
4. **Feature Rich**: Built-in download, error handling, and responsive design
5. **Type Safety**: TypeScript support with proper interfaces
6. **Performance**: Optimized rendering and memory usage 