import React, { useRef, useEffect } from 'react';
import JsBarcode from 'jsbarcode';

/**
 * BarcodeDisplay Component
 * 
 * A consolidated component for displaying payment barcodes (iBON, FamilyMart, etc.)
 * Supports multiple display modes: inline, table, and full page with download
 */

// Default barcode generation options
const DEFAULT_BARCODE_OPTIONS = {
  format: "CODE128",
  lineColor: "#000",
  width: 2,
  height: 80,
  displayValue: true,
  margin: 10,
  fontSize: 12,
  textAlign: "center",
  textPosition: "bottom",
  background: "#ffffff"
};

/**
 * Single Barcode Component
 */
const SingleBarcode = ({ code, label, options = {}, className = "" }) => {
  const barcodeRef = useRef(null);
  
  useEffect(() => {
    if (barcodeRef.current && code) {
      try {
        JsBarcode(barcodeRef.current, code, { ...DEFAULT_BARCODE_OPTIONS, ...options });
      } catch (error) {
        console.error('Error generating barcode:', error);
        if (barcodeRef.current) {
          barcodeRef.current.innerHTML = '<p class="text-red-500 text-xs">Barcode generation failed</p>';
        }
      }
    }
  }, [code, options]);

  if (!code) return null;

  return (
    <div className={`barcode-item ${className}`}>
      {label && <p className="text-sm font-medium mb-2">{label}</p>}
      <div className="barcode-container bg-white border border-gray-200 rounded p-2 mb-2">
        <svg ref={barcodeRef} className="mx-auto"></svg>
      </div>
      <div className="text-xs text-gray-600 font-mono text-center">{code}</div>
    </div>
  );
};

/**
 * Inline Barcode Display (for tables and compact views)
 */
export const InlineBarcodeDisplay = ({ barcodes, className = "" }) => {
  if (!barcodes || !Object.keys(barcodes).some(key => barcodes[key])) {
    return <span className="text-gray-500 text-sm">No barcodes available</span>;
  }

      return (
      <div className={`space-y-1 ${className}`}>
        {barcodes.barcode1 && (
          <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            Mã vạch 1: {barcodes.barcode1}
          </div>
        )}
        {barcodes.barcode2 && (
          <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            Mã vạch 2: {barcodes.barcode2}
          </div>
        )}
        {barcodes.barcode3 && (
          <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            Mã vạch 3: {barcodes.barcode3}
          </div>
        )}
      </div>
    );
};

/**
 * Visual Barcode Display (with actual barcode images)
 */
export const VisualBarcodeDisplay = ({ 
  barcodes, 
  title = "Payment Barcodes",
  showDownload = true,
  className = "",
  barcodeOptions = {}
}) => {
  if (!barcodes || !Object.keys(barcodes).some(key => barcodes[key])) {
    return <div className="text-gray-500">Không có mã thanh toán</div>;
  }

  const generateBarcodeImage = (code) => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      try {
        JsBarcode(canvas, code, { ...DEFAULT_BARCODE_OPTIONS, ...barcodeOptions });
        resolve(canvas.toDataURL('image/png'));
      } catch (error) {
        console.error('Error generating barcode image:', error);
        resolve(null);
      }
    });
  };

  const downloadAllBarcodes = async (orderInfo = {}) => {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size
      canvas.width = 600;
      canvas.height = 800;
      
      // Fill background with white
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw frame
      ctx.strokeStyle = '#2563eb';
      ctx.lineWidth = 4;
      ctx.strokeRect(20, 20, canvas.width - 40, canvas.height - 40);

      // Draw title
      ctx.fillStyle = '#1e40af';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(title, canvas.width / 2, 80);

      let y = 120;

             // Generate and draw each barcode
       const barcodeList = [
         { code: barcodes.barcode1, label: 'Mã vạch 1' },
         { code: barcodes.barcode2, label: 'Mã vạch 2' },
         { code: barcodes.barcode3, label: 'Mã vạch 3' }
       ].filter(item => item.code);

      for (const item of barcodeList) {
        const tempCanvas = document.createElement('canvas');
        JsBarcode(tempCanvas, item.code, {
          ...DEFAULT_BARCODE_OPTIONS,
          ...barcodeOptions,
          width: 3,
          height: 60
        });

        // Draw label
        ctx.fillStyle = '#374151';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(item.label, canvas.width / 2, y);
        
        // Draw barcode
        const barcodeWidth = 400;
        const barcodeHeight = 60;
        ctx.drawImage(tempCanvas, (canvas.width - barcodeWidth) / 2, y + 10, barcodeWidth, barcodeHeight);
        
        y += 120;
      }

      // Add order information if provided
      if (orderInfo.orderId || orderInfo.amount || orderInfo.currency) {
        const infoBoxY = y + 20;
        const infoBoxHeight = 100;
        
        // Draw info box
        ctx.fillStyle = '#f3f4f6';
        ctx.fillRect(40, infoBoxY, canvas.width - 80, infoBoxHeight);
        ctx.strokeStyle = '#d1d5db';
        ctx.lineWidth = 1;
        ctx.strokeRect(40, infoBoxY, canvas.width - 80, infoBoxHeight);
        
        // Add order details
        ctx.fillStyle = '#374151';
        ctx.font = '16px Arial';
        ctx.textAlign = 'left';
        let infoY = infoBoxY + 25;
        
        if (orderInfo.orderId) {
          ctx.fillText(`Order ID: ${orderInfo.orderId}`, 60, infoY);
          infoY += 25;
        }
        if (orderInfo.amount && orderInfo.currency) {
          ctx.fillText(`Amount: ${orderInfo.amount?.toLocaleString()} ${orderInfo.currency}`, 60, infoY);
          infoY += 25;
        }
        if (orderInfo.expiry) {
          ctx.fillText(`Valid until: ${orderInfo.expiry}`, 60, infoY);
        }
      }

      // Convert to image and download
      const link = document.createElement('a');
      link.download = `payment-barcodes-${orderInfo.orderId || Date.now()}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Error downloading barcodes:', error);
      alert('Failed to download barcodes. Please try again.');
    }
  };

  return (
    <div className={`bg-gray-50 rounded-lg p-4 border ${className}`}>
      <h4 className="text-lg font-semibold mb-4 text-center">{title}</h4>
      
      <div className="space-y-6">
        {barcodes.barcode1 && (
          <SingleBarcode 
            code={barcodes.barcode1} 
            label="Mã vạch 1" 
            options={barcodeOptions}
            className="text-center"
          />
        )}
        {barcodes.barcode2 && (
          <SingleBarcode 
            code={barcodes.barcode2} 
            label="Mã vạch 2" 
            options={barcodeOptions}
            className="text-center"
          />
        )}
        {barcodes.barcode3 && (
          <SingleBarcode 
            code={barcodes.barcode3} 
            label="Mã vạch 3" 
            options={barcodeOptions}
            className="text-center"
          />
        )}
      </div>

      {showDownload && (
        <div className="mt-6 text-center">
          <button
            onClick={() => downloadAllBarcodes()}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded transition-colors"
          >
            Download
          </button>
        </div>
      )}
    </div>
  );
};

/**
 * 7-11 iBON Specific Display Component
 */
export const IBONBarcodeDisplay = ({ 
  paymentInfo, 
  orderInfo = {},
  displayMode = "visual", // "visual", "inline", "table"
  className = ""
}) => {
  if (!paymentInfo?.barcodes) {
    return <div className="text-gray-500">Không có mã vạch iBON</div>;
  }

  const barcodes = paymentInfo.barcodes;

  if (displayMode === "inline") {
    return <InlineBarcodeDisplay barcodes={barcodes} className={className} />;
  }

  if (displayMode === "table") {
    return (
      <div className={`space-y-1 ${className}`}>
        {barcodes.barcode1 && (
          <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            Mã vạch iBON 1: {barcodes.barcode1}
          </div>
        )}
        {barcodes.barcode2 && (
          <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            Mã vạch iBON 2: {barcodes.barcode2}
          </div>
        )}
        {barcodes.barcode3 && (
          <div className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            Mã vạch iBON 3: {barcodes.barcode3}
          </div>
        )}
      </div>
    );
  }

  return (
    <VisualBarcodeDisplay 
      barcodes={barcodes}
      title="Mã vạch thanh toán iBON (7-Eleven)"
      className={className}
      showDownload={true}
    />
  );
};

/**
 * FamilyMart Specific Display Component  
 */
export const FamilyMartBarcodeDisplay = ({ 
  paymentBarcode, 
  orderInfo = {},
  displayMode = "visual",
  className = ""
}) => {
  if (!paymentBarcode) {
    return <div className="text-gray-500">Không có mã vạch FamilyMart</div>;
  }

  // Parse FamilyMart barcode string (comma-separated)
  const barcodeArray = paymentBarcode.split(',').map(code => code.trim());
  const barcodes = {
    barcode1: barcodeArray[0] || '',
    barcode2: barcodeArray[1] || '',
    barcode3: barcodeArray[2] || ''
  };

  if (displayMode === "inline") {
    return <InlineBarcodeDisplay barcodes={barcodes} className={className} />;
  }

  return (
    <VisualBarcodeDisplay 
      barcodes={barcodes}
      title="Mã vạch thanh toán FamilyMart"
      className={className}
      showDownload={true}
    />
  );
};

// Main export - flexible barcode display component
const BarcodeDisplay = {
  Inline: InlineBarcodeDisplay,
  Visual: VisualBarcodeDisplay,
  IBON: IBONBarcodeDisplay,
  FamilyMart: FamilyMartBarcodeDisplay,
  Single: SingleBarcode
};

export default BarcodeDisplay; 