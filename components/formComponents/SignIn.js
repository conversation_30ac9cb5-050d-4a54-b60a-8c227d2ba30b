import React from 'react'
import { FaUser, FaLock, FaSignInAlt, FaShieldAlt, FaMobileAlt, FaSimCard, FaBroadcastTower, FaGlobeAmericas, FaFlag, FaWifi, FaSatellite, FaNetworkWired, FaPlane, FaMapMarkerAlt, FaCompass, FaCamera, FaSuitcase, FaPassport, FaRoute, FaHotel } from 'react-icons/fa'

class SignIn extends React.Component {
  state = {
    username: '', password: ''
  }
  
  onChange = (e) => {
    this.setState({ [e.target.name]: e.target.value})
  }
  
  handleKeyDown = (e) => {
    // Check if Enter key was pressed and handleEnterKey prop is true
    if (e.key === 'Enter' && this.props.handleEnterKey) {
      e.preventDefault()
      this.props.signIn(this.state)
    }
  }
  
  render() {
    return (
      <div className="min-h-screen bg-gradient-to-br from-cyan-50 via-blue-50 via-purple-50 to-pink-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Animated Travel & Mobile Background */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Floating Travel Elements */}
          <div className="absolute inset-0">
            {/* Planes flying across */}
            <FaPlane className="absolute top-16 left-0 h-8 w-8 text-blue-300 opacity-40 animate-pulse transform rotate-45" style={{animation: 'float 20s infinite linear'}} />
            <FaPlane className="absolute top-32 right-0 h-6 w-6 text-purple-300 opacity-30 transform -rotate-45" style={{animation: 'float-reverse 25s infinite linear'}} />
            
            {/* Travel Icons - Top Section */}
            <FaPassport className="absolute top-20 left-20 h-10 w-10 text-emerald-200 opacity-35 transform rotate-12 animate-bounce" style={{animationDelay: '0s', animationDuration: '3s'}} />
            <FaSuitcase className="absolute top-24 left-64 h-8 w-8 text-orange-200 opacity-40 transform -rotate-15" style={{animation: 'pulse 4s infinite'}} />
            <FaCamera className="absolute top-12 right-32 h-9 w-9 text-pink-200 opacity-35 transform rotate-20 animate-pulse" style={{animationDelay: '1s'}} />
            <FaHotel className="absolute top-40 right-16 h-7 w-7 text-indigo-200 opacity-30 transform -rotate-10" />
            
            {/* Mobile & SIM Elements */}
            <FaMobileAlt className="absolute top-28 left-40 h-12 w-12 text-blue-300 opacity-40 transform rotate-12 animate-pulse" style={{animationDelay: '2s'}} />
            <FaSimCard className="absolute top-48 left-16 h-8 w-8 text-cyan-200 opacity-45 transform -rotate-20" style={{animation: 'bounce 3s infinite', animationDelay: '1s'}} />
            <FaBroadcastTower className="absolute top-20 left-80 h-10 w-10 text-purple-200 opacity-30 transform rotate-45 animate-pulse" />
            
            {/* Middle Section - Travel Route */}
            <FaRoute className="absolute top-1/2 left-8 h-12 w-12 text-blue-200 opacity-25 transform rotate-90" style={{animation: 'pulse 5s infinite'}} />
            <FaCompass className="absolute top-1/2 left-32 h-10 w-10 text-emerald-200 opacity-35 transform rotate-45 animate-spin" style={{animationDuration: '10s'}} />
            <FaMapMarkerAlt className="absolute top-1/2 left-56 h-8 w-8 text-red-300 opacity-40 animate-bounce" style={{animationDelay: '0.5s'}} />
            <FaGlobeAmericas className="absolute top-1/2 right-20 h-14 w-14 text-blue-200 opacity-30 transform -rotate-12 animate-spin" style={{animationDuration: '15s'}} />
            
            {/* Network & Connectivity */}
            <FaWifi className="absolute top-1/2 right-48 h-8 w-8 text-green-300 opacity-35 transform rotate-30 animate-pulse" style={{animationDelay: '1.5s'}} />
            <FaSatellite className="absolute top-1/2 left-96 h-9 w-9 text-purple-200 opacity-30 transform rotate-90" style={{animation: 'float 8s infinite'}} />
            <FaNetworkWired className="absolute top-1/2 right-80 h-8 w-8 text-cyan-200 opacity-35 transform -rotate-45 animate-pulse" />
            
            {/* Bottom Section */}
            <FaMobileAlt className="absolute bottom-32 left-24 h-10 w-10 text-indigo-300 opacity-35 transform -rotate-30 animate-pulse" style={{animationDelay: '3s'}} />
            <FaSimCard className="absolute bottom-20 left-48 h-12 w-12 text-blue-200 opacity-40 transform rotate-45" style={{animation: 'bounce 4s infinite', animationDelay: '2s'}} />
            <FaFlag className="absolute bottom-28 left-80 h-8 w-8 text-red-200 opacity-35 transform -rotate-15 animate-pulse" />
            
            {/* Travel Destinations */}
            <FaMapMarkerAlt className="absolute bottom-40 right-32 h-10 w-10 text-emerald-300 opacity-30 animate-bounce" style={{animationDelay: '1s'}} />
            <FaCamera className="absolute bottom-16 right-56 h-8 w-8 text-pink-300 opacity-35 transform rotate-25" style={{animation: 'pulse 3s infinite'}} />
            <FaSuitcase className="absolute bottom-48 right-16 h-9 w-9 text-orange-200 opacity-30 transform -rotate-20 animate-pulse" style={{animationDelay: '2.5s'}} />
            
            {/* Additional Floating Elements */}
            <FaBroadcastTower className="absolute bottom-60 left-72 h-8 w-8 text-purple-200 opacity-25 transform rotate-75" style={{animation: 'float 6s infinite'}} />
            <FaPassport className="absolute bottom-72 right-72 h-7 w-7 text-emerald-200 opacity-30 transform rotate-30 animate-pulse" />
            <FaHotel className="absolute top-64 right-96 h-8 w-8 text-indigo-200 opacity-25 transform -rotate-25" />
            <FaCompass className="absolute bottom-80 left-96 h-6 w-6 text-blue-200 opacity-30 transform rotate-60 animate-spin" style={{animationDuration: '12s'}} />
          </div>
          
          {/* Animated Background Gradients */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-100/20 to-transparent animate-pulse" style={{animationDuration: '8s'}}></div>
          <div className="absolute inset-0 bg-gradient-to-l from-transparent via-purple-100/20 to-transparent animate-pulse" style={{animationDuration: '10s', animationDelay: '2s'}}></div>
        </div>
        
        {/* Custom Animations */}
        <style jsx>{`
          @keyframes float {
            0% { transform: translateX(-100px) translateY(0px) rotate(45deg); opacity: 0; }
            10% { opacity: 0.4; }
            90% { opacity: 0.4; }
            100% { transform: translateX(calc(100vw + 100px)) translateY(-20px) rotate(45deg); opacity: 0; }
          }
          @keyframes float-reverse {
            0% { transform: translateX(calc(100vw + 100px)) translateY(0px) rotate(-45deg); opacity: 0; }
            10% { opacity: 0.3; }
            90% { opacity: 0.3; }
            100% { transform: translateX(-100px) translateY(-15px) rotate(-45deg); opacity: 0; }
          }
        `}</style>

        <div className="w-full max-w-md relative z-10">
          {/* Enhanced Header with Travel Theme */}
          <div className="text-center mb-8">
            <div className="mx-auto h-20 w-20 bg-gradient-to-r from-cyan-500 via-blue-600 to-purple-600 rounded-3xl flex items-center justify-center mb-6 shadow-2xl transform hover:scale-105 transition-all duration-300 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
              <div className="relative flex items-center space-x-1">
                <FaMobileAlt className="h-6 w-6 text-white animate-pulse" style={{animationDelay: '0s'}} />
                <FaSimCard className="h-5 w-5 text-white animate-pulse" style={{animationDelay: '0.5s'}} />
                <FaGlobeAmericas className="h-4 w-4 text-white animate-pulse" style={{animationDelay: '1s'}} />
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-3 tracking-tight">
              Chào Mừng Trở Lại
            </h1>
            <p className="text-gray-700 font-medium text-lg flex items-center justify-center space-x-2">
              <FaPlane className="h-4 w-4 text-blue-500 animate-pulse" />
              <span>Đăng nhập vào hệ thống quản lý SIM & Mobile</span>
              <FaMapMarkerAlt className="h-4 w-4 text-red-500 animate-pulse" />
            </p>
          </div>

          {/* Enhanced Main Card */}
          <div className="bg-white/95 backdrop-blur-lg shadow-2xl rounded-3xl border border-white/30 overflow-hidden transform hover:scale-[1.02] transition-all duration-300 relative">
            {/* Animated Border Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-blue-500 via-purple-500 to-pink-500 rounded-3xl opacity-20 animate-pulse"></div>
            <div className="absolute inset-[1px] bg-white/95 backdrop-blur-lg rounded-3xl"></div>
            <div className="relative z-10">
              {/* Enhanced Card Header */}
              <div className="bg-gradient-to-r from-cyan-500 via-blue-600 via-purple-600 to-pink-500 px-8 py-8 relative overflow-hidden">
                {/* Animated Background Pattern */}
                <div className="absolute inset-0 opacity-20">
                  <FaPlane className="absolute top-2 right-4 h-6 w-6 text-white animate-pulse" style={{animationDelay: '0s'}} />
                  <FaMapMarkerAlt className="absolute bottom-2 left-4 h-4 w-4 text-white animate-pulse" style={{animationDelay: '1s'}} />
                  <FaCompass className="absolute top-4 left-1/2 h-5 w-5 text-white animate-spin" style={{animationDuration: '8s'}} />
                </div>
                <div className="flex items-center relative z-10">
                  <div className="flex items-center space-x-2 mr-4">
                    <FaMobileAlt className="h-7 w-7 text-white animate-pulse" style={{animationDelay: '0s'}} />
                    <FaSimCard className="h-6 w-6 text-white animate-pulse" style={{animationDelay: '0.5s'}} />
                    <FaGlobeAmericas className="h-5 w-5 text-white animate-pulse" style={{animationDelay: '1s'}} />
                  </div>
                  <div>
                    <h2 className="text-white text-xl font-bold tracking-wide">Đăng Nhập Hệ Thống</h2>
                    <p className="text-white/90 text-sm mt-1 flex items-center space-x-2">
                      <FaSuitcase className="h-3 w-3" />
                      <span>Quản lý SIM & Dịch vụ Mobile toàn cầu</span>
                      <FaRoute className="h-3 w-3" />
                    </p>
                  </div>
                </div>
              </div>

              {/* Enhanced Form Section */}
              <form className="px-8 pt-8 pb-8" onSubmit={(e) => {
                e.preventDefault()
                this.props.signIn(this.state)
              }}>
                {/* Enhanced Username Field */}
                <div className="mb-6">
                  <label className="block text-gray-800 text-sm font-bold mb-3 flex items-center space-x-2" htmlFor="username">
                    <FaPassport className="h-4 w-4 text-blue-500" />
                    <span>Tên Đăng Nhập</span>
                  </label>
                  <div className="relative group">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <FaUser className="h-5 w-5 text-gray-400 group-focus-within:text-blue-600 transition-colors duration-300" />
                    </div>
                    <input
                       onChange={this.onChange} 
                       name="username"
                       onKeyDown={this.handleKeyDown}
                       className="w-full pl-12 pr-4 py-4 text-gray-800 bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-blue-300 placeholder-gray-400 text-sm font-medium transform hover:scale-[1.01]" 
                       id="username" 
                       type="text" 
                       placeholder="Nhập tên đăng nhập của bạn" 
                     />
                    <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                      <FaMobileAlt className="h-4 w-4 text-gray-300 group-focus-within:text-blue-500 transition-colors duration-300" />
                    </div>
                  </div>
                </div>

                {/* Enhanced Password Field */}
                <div className="mb-8">
                  <label className="block text-gray-800 text-sm font-bold mb-3 flex items-center space-x-2" htmlFor="password">
                    <FaShieldAlt className="h-4 w-4 text-green-500" />
                    <span>Mật Khẩu</span>
                  </label>
                  <div className="relative group">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <FaLock className="h-5 w-5 text-gray-400 group-focus-within:text-green-600 transition-colors duration-300" />
                    </div>
                    <input
                       onChange={this.onChange} 
                       name="password"
                       onKeyDown={this.handleKeyDown}
                       className="w-full pl-12 pr-4 py-4 text-gray-800 bg-gradient-to-r from-gray-50 to-green-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-green-300 placeholder-gray-400 text-sm font-medium transform hover:scale-[1.01]" 
                       id="password" 
                       type="password" 
                       placeholder="Nhập mật khẩu của bạn" 
                     />
                    <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                      <FaSimCard className="h-4 w-4 text-gray-300 group-focus-within:text-green-500 transition-colors duration-300" />
                    </div>
                  </div>
                </div>

                {/* Enhanced Options Row */}
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center group">
                    <input 
                      id="remember_me" 
                      name="remember_me" 
                      type="checkbox" 
                      className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-lg transition-all duration-300 transform hover:scale-110" 
                    />
                    <label htmlFor="remember_me" className="ml-3 block text-sm font-semibold text-gray-700 flex items-center space-x-1 group-hover:text-blue-600 transition-colors duration-300">
                      <FaMapMarkerAlt className="h-3 w-3" />
                      <span>Ghi nhớ đăng nhập</span>
                    </label>
                  </div>
                  <a className="text-sm font-semibold text-blue-600 hover:text-purple-600 transition-colors duration-300 flex items-center space-x-1 hover:underline" href="#">
                    <FaCompass className="h-3 w-3" />
                    <span>Quên mật khẩu?</span>
                  </a>
                </div>

                {/* Enhanced Submit Button */}
                <div className="mb-6">
                  <button 
                    onClick={(e) => {
                      e.preventDefault()
                      this.props.signIn(this.state)
                    }} 
                    className="group relative w-full flex justify-center items-center py-5 px-6 border border-transparent text-sm font-bold rounded-xl text-white bg-gradient-to-r from-cyan-500 via-blue-600 via-purple-600 to-pink-500 hover:from-cyan-600 hover:via-blue-700 hover:via-purple-700 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:scale-[1.03] active:scale-[0.97] shadow-xl hover:shadow-2xl relative overflow-hidden" 
                    type="submit"
                  >
                    {/* Animated Background Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                    <div className="relative flex items-center space-x-3">
                      <FaPlane className="h-5 w-5 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                      <span>Đăng Nhập Hệ Thống</span>
                      <FaSignInAlt className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </button>
                </div>

                {/* Enhanced Security Notice */}
                <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 border border-blue-200 rounded-xl p-5 relative overflow-hidden">
                  {/* Animated Background Icons */}
                  <div className="absolute inset-0 opacity-10">
                    <FaGlobeAmericas className="absolute top-2 right-2 h-6 w-6 text-blue-500 animate-spin" style={{animationDuration: '10s'}} />
                    <FaSatellite className="absolute bottom-2 left-2 h-4 w-4 text-purple-500 animate-pulse" />
                  </div>
                  <div className="flex items-start relative z-10">
                    <div className="flex items-center space-x-2 mr-3 flex-shrink-0">
                      <FaShieldAlt className="h-6 w-6 text-blue-600 animate-pulse" />
                      <FaGlobeAmericas className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-blue-800 text-sm font-bold mb-2 flex items-center space-x-2">
                        <FaMobileAlt className="h-4 w-4" />
                        <span>Bảo Mật Dịch Vụ Mobile Toàn Cầu</span>
                        <FaSimCard className="h-4 w-4" />
                      </p>
                      <p className="text-blue-700 text-xs leading-relaxed flex items-center space-x-1">
                        <FaRoute className="h-3 w-3 flex-shrink-0" />
                        <span>Hệ thống bảo mật cao cho quản lý SIM, Mobile và dịch vụ viễn thông trên toàn thế giới.</span>
                        <FaFlag className="h-3 w-3 flex-shrink-0 text-red-500" />
                      </p>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>

          {/* Enhanced Footer */}
          <div className="text-center mt-8">
            <div className="flex items-center justify-center space-x-3 mb-2">
              <FaPlane className="h-4 w-4 text-blue-400 animate-pulse" />
              <FaGlobeAmericas className="h-5 w-5 text-green-400 animate-spin" style={{animationDuration: '8s'}} />
              <FaMobileAlt className="h-4 w-4 text-purple-400 animate-pulse" style={{animationDelay: '0.5s'}} />
              <FaSimCard className="h-4 w-4 text-cyan-400 animate-pulse" style={{animationDelay: '1s'}} />
            </div>
            <p className="text-gray-600 text-xs font-medium bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              © 2024 MAG GROUP - Mobile & SIM Services Worldwide. Tất cả quyền được bảo lưu.
            </p>
            <div className="flex items-center justify-center space-x-2 mt-2 text-gray-400">
              <FaMapMarkerAlt className="h-3 w-3" />
              <span className="text-xs">Kết nối toàn cầu</span>
              <FaRoute className="h-3 w-3" />
              <span className="text-xs">Dịch vụ không giới hạn</span>
              <FaCompass className="h-3 w-3" />
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default SignIn
