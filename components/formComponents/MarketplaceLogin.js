import React from 'react'
import { useRouter } from 'next/router'

class MarketplaceLogin extends React.Component {
    state = {
        username: '',
        password: '',
        email: '',
        formState: 'signIn' // signIn, signUp, confirmSignUp, forgotPassword
    }

    onChange = (e) => {
        this.setState({ [e.target.name]: e.target.value })
    }

    toggleFormState = (formState) => {
        this.setState({ formState })
    }

    handleSocialLogin = (provider) => {
        // Call appropriate social login handler from props
        switch (provider) {
            case 'facebook':
                this.props.facebookSignIn()
                break
            case 'apple':
                this.props.appleSignIn()
                break
            case 'google':
                this.props.googleSignIn()
                break
            case 'twitter':
                this.props.twitterSignIn()
                break
            default:
                break
        }
    }

    renderSocialButtons() {
        return (
            <div className="mb-6">
                <div className="flex gap-2 justify-center">
                    <button
                        onClick={() => this.handleSocialLogin('facebook')}
                        className="w-16 h-16 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center"
                        type="button"
                        title="Facebook"
                    >
                        <span className="text-xl font-bold">f</span>
                    </button>
                    <button
                        onClick={() => this.handleSocialLogin('apple')}
                        className="w-16 h-16 bg-black hover:bg-gray-800 text-white rounded-full flex items-center justify-center"
                        type="button"
                        title="Apple"
                    >
                        <span className="text-xl">🍎</span>
                    </button>
                    <button
                        onClick={() => this.handleSocialLogin('google')}
                        className="w-16 h-16 bg-red-600 hover:bg-red-700 text-white rounded-full flex items-center justify-center"
                        type="button"
                        title="Google"
                    >
                        <span className="text-xl font-bold">G</span>
                    </button>
                    <button
                        onClick={() => this.handleSocialLogin('twitter')}
                        className="w-16 h-16 bg-blue-400 hover:bg-blue-500 text-white rounded-full flex items-center justify-center"
                        type="button"
                        title="Twitter"
                    >
                        <span className="text-xl">🐦</span>
                    </button>
                </div>
            </div>
        )
    }

    renderSignIn() {
        return (
            <div className="w-full max-w-144">
                <form className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
                    {this.renderSocialButtons()}

                    <div className="relative my-6">
                        <div className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-300"></div>
                        </div>
                        <div className="relative flex justify-center text-sm">
                            <span className="px-2 bg-white text-gray-500">
                                Hoặc đăng nhập bằng số điện thoại
                            </span>
                        </div>
                    </div>

                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="username">
                            Số điện thoại
                        </label>
                        <input
                            onChange={this.onChange}
                            name="username"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            id="username"
                            type="text"
                            placeholder="Số điện thoại"
                        />
                    </div>
                    <div className="mb-6">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="password">
                            Mật khẩu
                        </label>
                        <input
                            onChange={this.onChange}
                            name="password"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
                            id="password"
                            type="password"
                            placeholder="******************"
                        />
                    </div>
                    <div className="flex items-center justify-between">
                        <button
                            onClick={() => this.props.signIn(this.state)}
                            className="bg-primary hover:bg-black text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            type="button"
                        >
                            Đăng nhập
                        </button>
                        <div className="flex flex-col text-right">
                            <a
                                className="inline-block align-baseline font-bold text-sm hover:text-primary mb-2"
                                href="#"
                                onClick={() => this.toggleFormState('forgotPassword')}
                            >
                                Quên mật khẩu?
                            </a>
                            <a
                                className="inline-block align-baseline font-bold text-sm hover:text-primary"
                                href="#"
                                onClick={() => this.toggleFormState('signUp')}
                            >
                                Đăng ký tài khoản mới
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        )
    }

    renderSignUp() {
        return (
            <div className="w-full max-w-144">
                <form className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="username">
                            Số điện thoại
                        </label>
                        <input
                            onChange={this.onChange}
                            name="username"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            id="username"
                            type="text"
                            placeholder="Số điện thoại"
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">
                            Email
                        </label>
                        <input
                            onChange={this.onChange}
                            name="email"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            id="email"
                            type="email"
                            placeholder="Email"
                        />
                    </div>
                    <div className="mb-6">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="password">
                            Mật khẩu
                        </label>
                        <input
                            onChange={this.onChange}
                            name="password"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
                            id="password"
                            type="password"
                            placeholder="******************"
                        />
                    </div>
                    <div className="flex items-center justify-between">
                        <button
                            onClick={() => this.props.signUp(this.state)}
                            className="bg-primary hover:bg-black text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            type="button"
                        >
                            Đăng ký
                        </button>
                        <a
                            className="inline-block align-baseline font-bold text-sm hover:text-primary"
                            href="#"
                            onClick={() => this.toggleFormState('signIn')}
                        >
                            Đã có tài khoản? Đăng nhập
                        </a>
                    </div>
                </form>
            </div>
        )
    }

    render() {
        return (
            <div className="flex flex-1 justify-center pt-8">
                <div className="w-full max-w-144">
                    <h1 className="text-3xl font-light mb-6 text-center">Đăng nhập</h1>
                    {this.state.formState === 'signIn' && this.renderSignIn()}
                    {this.state.formState === 'signUp' && this.renderSignUp()}
                </div>
            </div>
        )
    }
}

export default MarketplaceLogin 