import React, { useState, useEffect } from "react";
import { useStripe, useElements } from "@stripe/react-stripe-js";
import Image from "./Image";

const Input = ({ onChange, value, name, placeholder, type, min, required }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    type={type || "text"}
    placeholder={placeholder}
    name={name}
    min={min}
    required={required}
  />
);

const PaymentFormVietnamBankTransfer = ({ 
  storeObject, 
  cart, 
  amount, 
  currency, 
  orderId, 
  setErrorMessage, 
  setOrderCompleted, 
  customerInfo 
}) => {
  const [firstName, setFirstName] = useState(customerInfo?.firstName || "");
  const [lastName, setLastName] = useState(customerInfo?.lastName || "");
  const [email, setEmail] = useState(customerInfo?.email || "");
  const [phone, setPhone] = useState(customerInfo?.phone || "");
  const [address, setAddress] = useState(customerInfo?.address || "");
  const [transferInfo, setTransferInfo] = useState("");
  const [transferAmount, setTransferAmount] = useState(0);
  const [paymentQRCode, setPaymentQRCode] = useState("");
  const [processingPayment, setProcessingPayment] = useState(false);
  
  // Vietnam bank accounts information
  const bankAccounts = [
    {
      bankName: "Techcombank",
      accountName: "CONG TY TNHH TOAN CAU TAP DOAN MINH ANH",
      accountNumber: "**************",
      bankBranch: "Chi nhánh Sài Gòn",
      swiftCode: "VTCBVNVX"
    },
    {
      bankName: "Vietcombank",
      accountName: "CONG TY TNHH TOAN CAU TAP DOAN MINH ANH",
      accountNumber: "*************",
      bankBranch: "Chi nhánh Sài Gòn",
      swiftCode: "BFTVVNVX"
    }
  ];
  
  // Payment fee (can be adjusted based on requirements)
  const PAYMENT_FEE = 0;
  
  // Find QR code for Vietnam if available
  useEffect(() => {
    if (storeObject && storeObject.paymentQRs) {
      const vietnamQRs = storeObject.paymentQRs.find(q => q.country === "vietnam");
      if (vietnamQRs && vietnamQRs.qrs && vietnamQRs.qrs.length > 0) {
        setPaymentQRCode(vietnamQRs.qrs[0].qr_image);
      }
    }
  }, [storeObject]);
  
  // Calculate total amount including fee
  useEffect(() => {
    setTransferAmount(amount + PAYMENT_FEE);
  }, [amount]);
  
  // Log payment actions (useful for tracking)
  const logPayment = (action, data) => {
    console.log(`[VIETNAM_BANK_TRANSFER] ${action}`, data);
  };
  
  // Handle form submission
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setProcessingPayment(true);
    
    try {
      // Form validation
      if (!firstName || !lastName || !email || !phone) {
        setErrorMessage("Vui lòng điền đầy đủ thông tin");
        setProcessingPayment(false);
        return;
      }
      
      // Create order data
      const orderData = {
        type: "vietnambanktransfer",
        orderId,
        cart,
        amount: transferAmount,
        currency,
        customer: {
          firstName,
          lastName,
          email,
          phone,
          address
        },
        transferInfo,
        paymentStatus: "pending",
        storeId: storeObject?.storeId || ""
      };
      
      logPayment("Submitting order", orderData);
      
      // Here you would typically send this data to your backend API
      // For now, we'll simulate a successful order
      setTimeout(() => {
        setOrderCompleted(true);
        setProcessingPayment(false);
        logPayment("Order completed", { orderId });
      }, 1500);
      
    } catch (error) {
      console.error("Payment error:", error);
      setErrorMessage("Có lỗi xảy ra trong quá trình xử lý thanh toán. Vui lòng thử lại sau.");
      setProcessingPayment(false);
    }
  };
  
  return (
    <div className="w-full">
      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex items-center mb-2">
          <Image 
            src="https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/bank-transfer-logo.webp" 
            width={32} 
            height={32} 
            alt="Bank Transfer" 
            className="mr-2" 
          />
          <h2 className="text-xl font-bold">Chuyển khoản ngân hàng Việt Nam - {currency}</h2>
        </div>
        
        <div className="bg-white p-4 rounded-md border border-blue-100 mb-4">
          <h3 className="font-medium text-lg mb-2">Phí giao dịch: {PAYMENT_FEE} {currency}</h3>
          <p className="text-sm">
            Chuyển khoản ngân hàng là phương thức thanh toán an toàn và đáng tin cậy.
            Phương thức này có phí giao dịch {PAYMENT_FEE} {currency}. 
            Bạn sẽ cần chuyển khoản đến tài khoản ngân hàng của chúng tôi và cung cấp mã đơn hàng.
          </p>
        </div>
      </div>
      
      <div className="thong-tin-chuyen-tien mb-6 bg-white p-4 rounded-lg border">
        <h3 className="text-lg font-bold mb-3">Thông tin tài khoản nhận tiền</h3>
        
        <div className="bg-yellow-50 p-3 rounded-md mb-4 border border-yellow-200">
          <p className="font-medium">Mã đơn hàng: <span className="text-blue-600 font-bold">{orderId}</span></p>
          <p className="font-medium">Số tiền: <span className="text-red-600 font-bold">{transferAmount.toLocaleString()} {currency}</span></p>
          <p className="text-sm text-yellow-800 mt-2">
            Vui lòng sử dụng mã đơn hàng làm nội dung chuyển khoản để chúng tôi xác nhận thanh toán của bạn.
          </p>
        </div>
        
        <div>
          {bankAccounts.map((account, index) => (
            <div key={index} className="mb-4 p-3 border rounded-md">
              <h4 className="font-bold">{account.bankName}</h4>
              <p>Tên tài khoản: <span className="font-medium">{account.accountName}</span></p>
              <p>Số tài khoản: <span className="font-medium">{account.accountNumber}</span></p>
              <p>Chi nhánh: {account.bankBranch}</p>
              {account.swiftCode && <p>SWIFT Code: {account.swiftCode}</p>}
            </div>
          ))}
        </div>
        
        {paymentQRCode && (
          <div className="mt-4 text-center">
            <p className="mb-2 font-medium">Hoặc quét mã QR để chuyển khoản:</p>
            <Image src={paymentQRCode} width={200} height={200} className="mx-auto" alt="QR Code for payment" />
          </div>
        )}
      </div>
      
      <form onSubmit={handleFormSubmit}>
        <div className="thong-tin-ca-nhan mb-6">
          <h3 className="text-lg font-bold mb-3">Thông tin cá nhân</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Tên</label>
              <input
                type="text"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Họ</label>
              <input
                type="text"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Email</label>
              <input
                type="email"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Số điện thoại</label>
              <input
                type="tel"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                required
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700">Địa chỉ</label>
            <input
              type="text"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
            />
          </div>
        </div>
        
        <div className="thong-tin-chuyen-khoan mb-6">
          <h3 className="text-lg font-bold mb-3">Thông tin chuyển khoản</h3>
          <div>
            <label className="block text-sm font-medium text-gray-700">Thông tin chuyển khoản (nếu đã chuyển)</label>
            <textarea
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={transferInfo}
              onChange={(e) => setTransferInfo(e.target.value)}
              placeholder="Ví dụ: Đã chuyển từ tài khoản ... ngân hàng ... vào lúc ... với số tiền ..."
              rows={4}
            />
          </div>
        </div>
        
        <div className="flex justify-center">
          <button
            type="submit"
            className={`px-8 py-3 text-lg font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${processingPayment ? 'opacity-75 cursor-not-allowed' : ''}`}
            disabled={processingPayment}
          >
            {processingPayment ? 'Đang xử lý...' : 'Xác nhận đơn hàng'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PaymentFormVietnamBankTransfer; 