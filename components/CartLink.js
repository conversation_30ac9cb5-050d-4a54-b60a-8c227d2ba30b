import React, { useEffect, useState } from 'react'
import { ContextProviderComponent, SiteContext } from '../context/mainContext'
import { FaShoppingCart } from 'react-icons/fa'
import { useRouter } from 'next/router'

// Client-side only component with the simplest possible structure
function CartLink(props) {
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  
  // Only render after component is mounted client-side
  useEffect(() => {
    try {
      console.log("CartLink mounting, router query:", router.query);
      setMounted(true);
    } catch (error) {
      console.error("Error in CartLink mounting:", error);
    }
  }, [router.query]);
  
  // Don't render anything on the server
  if (!mounted) {
    return null;
  }
  
  try {
    // Get store from various sources with fallbacks
    const routerStore = router.query?.store;
    const contextStore = props.context?.store;
    const propsStore = props.store;
    
    // Use the first available store value with fallbacks
    const store = routerStore || contextStore || propsStore || '';
    
    const cartPath = store ? `/${store}/cart` : `/cart`;
    console.log("CartLink using store:", store, "path:", cartPath);
    
    // Use the simplest possible structure with a standard anchor tag
    return (
      <div className="cart-icon-container fixed sm:top-53 right-24 desktop:right-flexiblemargin top-40 z-10">
        <a href={cartPath}>
          <FaShoppingCart size={52} style={{ color: 'purple' }}/>
        </a>
      </div>
    );
  } catch (error) {
    console.error("Error rendering CartLink:", error);
    return null; // Fail gracefully
  }
}

function CartLinkWithContext(props) {
  try {
    return (
      <ContextProviderComponent>
        <SiteContext.Consumer>
          {context => <CartLink {...props} context={context} />}
        </SiteContext.Consumer>
      </ContextProviderComponent>
    );
  } catch (error) {
    console.error("Error in CartLinkWithContext:", error);
    return null; // Fail gracefully
  }
}

export default CartLinkWithContext