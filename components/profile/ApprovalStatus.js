import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faTimesCircle, faClock, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';

const ApprovalStatus = ({ status, reason, section, lastUpdated }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'approved':
        return {
          icon: faCheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          text: 'Đã duyệt',
          textColor: 'text-green-800'
        };
      case 'rejected':
        return {
          icon: faTimesCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          text: 'Từ chối',
          textColor: 'text-red-800'
        };
      case 'pending':
        return {
          icon: faClock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          text: 'Chờ duyệt',
          textColor: 'text-yellow-800'
        };
      case 'requires_update':
        return {
          icon: faExclamationTriangle,
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          text: 'Cần cập nhật',
          textColor: 'text-orange-800'
        };
      default:
        return {
          icon: faClock,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          text: 'Chưa xác định',
          textColor: 'text-gray-800'
        };
    }
  };

  const config = getStatusConfig(status);

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSectionName = (section) => {
    switch (section) {
      case 'personalDetails':
        return 'Thông tin cá nhân';
      case 'contactInfo':
        return 'Thông tin liên hệ';
      case 'travelInfo':
        return 'Thông tin du lịch';
      default:
        return section;
    }
  };

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full border ${config.bgColor} ${config.borderColor}`}>
      <FontAwesomeIcon 
        icon={config.icon} 
        className={`${config.color} mr-2 text-sm`} 
      />
      <span className={`text-sm font-medium ${config.textColor}`}>
        {config.text}
      </span>
      
      {/* Tooltip with additional information */}
      <div className="group relative ml-2">
        <div className="cursor-help">
          <FontAwesomeIcon 
            icon={faExclamationTriangle} 
            className="text-gray-400 text-xs" 
          />
        </div>
        
        {/* Tooltip content */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap">
          <div className="font-semibold">{getSectionName(section)}</div>
          {lastUpdated && (
            <div className="text-gray-300">Cập nhật: {formatDate(lastUpdated)}</div>
          )}
          {reason && (
            <div className="text-gray-300 mt-1 max-w-xs whitespace-normal">
              Lý do: {reason}
            </div>
          )}
          
          {/* Tooltip arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
        </div>
      </div>
    </div>
  );
};

export default ApprovalStatus;