import { DisplaySmall } from "."
import { slugify } from "../utils/helpers"

const HotProducts = ({ products, store }) => {
  return (
    <div className="my-8 flex flex-col lg:flex-row justify-between">
      {products.slice(0, 4).map((product, index) => {
        if (
          !product ||
          !product.image ||
          !product.categories ||
          product.image.length === 0
        ) {
          return null
        }
        return (
          <DisplaySmall
            key={index}
            imageSrc={
              Array.isArray(product.image) && product.image.length > 0
                ? product.image[0]
                : "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.1.webp"
            }
            title={product.name}
            subtitle={product.categories[0]}
            link={`${store}/product/${slugify(product.sku)}`}
            validity={product.validity}
            territory={product.territory}
          />
        )
      })}
    </div>
  )
}

export default HotProducts 