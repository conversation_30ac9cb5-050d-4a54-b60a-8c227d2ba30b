'use client'
// use this component in layout.tsx to customize kbar search
import { ReactNode, Suspense, useState, useEffect } from 'react'
import { KBarSearch } from './components/KBar'

interface SearchProviderProps {
  children: ReactNode
}

const SearchProvider = ({ children }: SearchProviderProps) => {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // During initial render, just return children to match server-side rendering
  if (!mounted) {
    return <>{children}</>
  }

  try {
    return (
      <Suspense fallback={<>{children}</>}>
        <KBarSearch>{children}</KBarSearch>
      </Suspense>
    )
  } catch (error) {
    console.error('Error in SearchProvider:', error)
    return <>{children}</>
  }
}

export default SearchProvider
