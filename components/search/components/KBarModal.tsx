'use client'
import { useParams } from 'next/navigation'
import { useState, useEffect } from 'react'
import {
  KBarPortal,
  KBarPositioner,
  KBarAnimator,
  KBarSearch,
  useMatches,
  KBarResults,
} from 'kbar'

export const KBarModal = () => {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Don't render anything on the server or during hydration
  if (!mounted) {
    return null
  }

  try {
    const { results } = useMatches()

    return (
      <KBarPortal>
        <KBarPositioner className="bg-gray-300/50 dark:bg-gray-900/50 p-4 backdrop-blur">
          <KBarAnimator className="w-full max-w-xl">
            <div className="overflow-hidden rounded-lg border border-gray-100 bg-gray-50 dark:border-gray-800 dark:bg-gray-900">
              <div className="flex items-center space-x-4 p-4">
                <span className="block text-gray-500 dark:text-gray-400">
                  <svg
                    className="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </span>
                <KBarSearch className="h-8 w-full bg-transparent text-gray-600 placeholder-gray-400 focus:outline-none dark:text-gray-200 dark:placeholder-gray-500" />
                <kbd className="hidden rounded border border-gray-100 bg-gray-100 px-2 py-1 text-xs font-medium text-gray-500 dark:border-gray-800 dark:bg-gray-800 dark:text-gray-400 sm:inline-block">
                  ESC
                </kbd>
              </div>
              <div className="border-t border-gray-100 px-2 py-4 dark:border-gray-800">
                <KBarResults
                  items={results}
                  onRender={({ item, active }) =>
                    typeof item === 'string' ? (
                      <div className="px-4 py-2 text-xs font-medium text-gray-400 dark:text-gray-500">
                        {item}
                      </div>
                    ) : (
                      <div
                        className={`flex cursor-pointer items-center rounded-lg px-4 py-2 ${
                          active
                            ? 'bg-gray-100 dark:bg-gray-800'
                            : 'text-gray-600 dark:text-gray-400'
                        }`}
                      >
                        {item.icon && <span className="mr-4 text-gray-400 dark:text-gray-500">{item.icon}</span>}
                        <div>
                          <div className={`${active ? 'text-gray-900 dark:text-gray-200' : ''}`}>
                            {item.name}
                          </div>
                          {item.subtitle && (
                            <div className="text-xs text-gray-400 dark:text-gray-500">{item.subtitle}</div>
                          )}
                        </div>
                        {item.shortcut?.length ? (
                          <div className="ml-auto flex items-center space-x-2">
                            {item.shortcut.map((sc: string) => (
                              <kbd
                                key={sc}
                                className="rounded border border-gray-100 bg-gray-100 px-2 py-1 text-xs font-medium text-gray-500 dark:border-gray-800 dark:bg-gray-800 dark:text-gray-400"
                              >
                                {sc}
                              </kbd>
                            ))}
                          </div>
                        ) : null}
                      </div>
                    )
                  }
                />
              </div>
            </div>
          </KBarAnimator>
        </KBarPositioner>
      </KBarPortal>
    )
  } catch (error) {
    console.error('Error rendering KBarModal:', error)
    return null // Return null to not render anything in case of error
  }
}
