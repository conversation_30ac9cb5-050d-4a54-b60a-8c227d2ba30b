'use client'
import { useState, useEffect, FC, ReactNode } from 'react'
import type { Action } from 'kbar'
import { KBarProvider } from 'kbar'
import { useRouter } from 'next/navigation.js'
import { KBarModal } from './KBarModal'

export interface KBarSearchProps {
  children: ReactNode
}

export interface KBarConfig {
  provider: 'kbar'
  kbarConfig: KBarSearchProps
}

/**
 * Command palette like search component with kbar - `ctrl-k` to open the palette.
 *
 * Default actions can be overridden by passing in an array of actions to `defaultActions`.
 * To load actions dynamically, pass in a `searchDocumentsPath` to a JSON file.
 * `onSearchDocumentsLoad` can be used to transform the JSON into actions.
 *
 * To toggle the modal or search from child components, use the search context:
 * ```
 * import { useKBar } from 'kbar'
 * const { query } = useKBar()
 * ```
 * See https://github.com/timc1/kbar/blob/main/src/types.ts#L98-L106 for typings.
 *
 * @param {*} { kbarConfig, children }
 * @return {*}
 */
export const KBarSearch: FC<KBarSearchProps> = ({ children }) => {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [actions] = useState<Action[]>([
    {
      id: 'home',
      name: 'Home',
      shortcut: ['h'],
      keywords: 'home index',
      perform: () => router.push('/'),
    },
    {
      id: 'profile',
      name: 'Profile',
      shortcut: ['p'],
      keywords: 'profile account settings',
      perform: () => router.push('/customer/profile'),
    },
    {
      id: 'sim',
      name: 'SIM Management',
      shortcut: ['s'],
      keywords: 'sim esim management settings',
      perform: () => router.push('/customer/sim'),
    },
    {
      id: 'topup',
      name: 'Top-Up',
      shortcut: ['t'],
      keywords: 'topup recharge balance',
      perform: () => router.push('/customer/topup'),
    },
    {
      id: 'orders',
      name: 'Orders',
      shortcut: ['o'],
      keywords: 'orders history transactions',
      perform: () => router.push('/customer/orders'),
    },
    {
      id: 'rewards',
      name: 'Rewards',
      shortcut: ['r'],
      keywords: 'rewards points offers',
      perform: () => router.push('/customer/rewards'),
    },
    {
      id: 'support',
      name: 'Support',
      shortcut: ['u'],
      keywords: 'support help assistance',
      perform: () => router.push('/customer/support'),
    },
  ])

  useEffect(() => {
    setMounted(true)
  }, [])

  // Always return children during initial render to match server output
  if (!mounted) {
    return <>{children}</>
  }

  try {
    return (
      <KBarProvider actions={actions}>
        <KBarModal />
        {children}
      </KBarProvider>
    )
  } catch (error) {
    console.error('Error rendering KBar:', error)
    // Fallback to just rendering children if KBar fails
    return <>{children}</>
  }
}

export default KBarSearch
