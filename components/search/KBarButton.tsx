'use client'
import React, { DetailedHTMLProps, HTMLAttributes, ReactNode, useState, useEffect } from 'react'
import { useKBar } from 'kbar'

interface KbarButtonProps {
  children: ReactNode
  [key: string]: any
}

/**
 * Button wrapper component that triggers the KBar modal on click.
 *
 * @return {*}
 */
export const KBarButton: React.FC<
  DetailedHTMLProps<HTMLAttributes<HTMLButtonElement>, HTMLButtonElement>
> = ({ children, ...rest }: KbarButtonProps) => {
  const [mounted, setMounted] = useState(false)
  
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // Return a simple button during SSR and hydration to avoid mismatches
  if (!mounted) {
    return <button {...rest}>{children}</button>
  }
  
  try {
    const { query } = useKBar()

    return (
      <button
        {...rest}
        onClick={() => {
          try {
            query.toggle()
          } catch (error) {
            console.error('Error toggling KBar query:', error)
          }
        }}
      >
        {children}
      </button>
    )
  } catch (error) {
    console.error('Error in KBarButton:', error)
    return <button {...rest}>{children}</button>
  }
}
