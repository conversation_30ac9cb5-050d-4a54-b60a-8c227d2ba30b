import { ContextProviderComponent, SiteContext } from "../context/mainContext"
import { FaUserSecret } from "react-icons/fa"
import { useContext, useState } from "react"

import Link from "next/link"
import { colors } from "../theme"
const { primary } = colors

function ProfileLink(props) {
  const { email, phone, getPreferredAffiliate } = useContext(SiteContext)
  const [showInfo, setShowInfo] = useState(true);
  const { store, sku } = props;

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  let contextAffiliate = getPreferredAffiliate(store, sku);

  return (
    <div className="fixed sm:top-53 right-24 desktop:right-flexiblemargin top-40 z-10">
      <div className="flex flex-1 justify-end pr-4 relative">
        {showInfo && (
          <div className="border rounded-md p-2 mr-2" style={{ backgroundColor: '#E7BF6D' }}> {/* #E7BF6D #86200D*/}
            <p>{email}</p>
            <p>{phone}</p>
            <p>{contextAffiliate ? "♡ " + contextAffiliate.affiliateid : "♡: KHÔNG ÁP DỤNG"}</p>
          </div>
        )}
        <FaUserSecret
          size={52}
          style={{ color: 'purple' }}
          onClick={toggleInfo}
        />
      </div>
    </div>
  )
}

function ProfileLinkWithContext(props) {
  return (
    <ContextProviderComponent>
      <SiteContext.Consumer>
        {(context) => <ProfileLink {...props} context={context} />}
      </SiteContext.Consumer>
    </ContextProviderComponent>
  )
}

export default ProfileLinkWithContext
