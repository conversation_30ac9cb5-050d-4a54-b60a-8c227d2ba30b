import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Save, X, Tag } from 'lucide-react';
import { FAQItem } from '@/shared/types/types';

interface Category {
  id: string;
  name: string;
}

const FAQEditor = () => {
  const [faqs, setFaqs] = useState<FAQItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentFAQ, setCurrentFAQ] = useState<Partial<FAQItem>>({
    question: '',
    answer: '',
    category: '',
  });
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCategoryInput, setShowCategoryInput] = useState(false);
  const [newCategory, setNewCategory] = useState('');

  useEffect(() => {
    fetchFAQs();
    fetchCategories();
  }, []);

  const fetchFAQs = async () => {
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${API_URL}/api/faqs`);
      if (!response.ok) throw new Error('Failed to fetch FAQs');
      const data = await response.json();
      setFaqs(data);
    } catch (err) {
      setError('Error loading FAQs');
      console.error(err);
    }
  };

  const fetchCategories = async () => {
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${API_URL}/api/faq-categories`);
      if (!response.ok) throw new Error('Failed to fetch categories');
      const data = await response.json();
      setCategories(data);
    } catch (err) {
      console.error(err);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentFAQ(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const url = isEditing
        ? `${API_URL}/api/faqs/${currentFAQ.id}`
        : `${API_URL}/api/faqs`;
      const method = isEditing ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(currentFAQ)
      });

      if (!response.ok) throw new Error('Failed to save FAQ');
      
      await fetchFAQs();
      resetForm();
    } catch (err) {
      setError('Error saving FAQ');
      console.error(err);
    }
  };

  const handleEdit = (faq: FAQItem) => {
    setCurrentFAQ(faq);
    setIsEditing(true);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this FAQ?')) return;
    
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${API_URL}/api/faqs/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete FAQ');
      
      await fetchFAQs();
    } catch (err) {
      setError('Error deleting FAQ');
      console.error(err);
    }
  };

  const handleAddCategory = async () => {
    if (!newCategory.trim()) return;
    
    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${API_URL}/api/faq-categories`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newCategory.trim() })
      });

      if (!response.ok) throw new Error('Failed to add category');
      
      await fetchCategories();
      setNewCategory('');
      setShowCategoryInput(false);
    } catch (err) {
      setError('Error adding category');
      console.error(err);
    }
  };

  const resetForm = () => {
    setCurrentFAQ({ question: '', answer: '', category: '' });
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Question</label>
          <input
            type="text"
            name="question"
            value={currentFAQ.question}
            onChange={handleInputChange}
            className="w-full p-2 border dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Answer</label>
          <textarea
            name="answer"
            value={currentFAQ.answer}
            onChange={handleInputChange}
            rows={4}
            className="w-full p-2 border dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
            required
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-1">
            <label className="block text-sm font-medium">Category</label>
            <button
              type="button"
              onClick={() => setShowCategoryInput(true)}
              className="text-sm text-indigo-500 hover:text-indigo-600 dark:text-indigo-400"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
          
          {showCategoryInput ? (
            <div className="flex gap-2 mb-2">
              <input
                type="text"
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                className="flex-1 p-2 border dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
                placeholder="New category name"
              />
              <button
                type="button"
                onClick={handleAddCategory}
                className="p-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
              >
                <Save className="w-4 h-4" />
              </button>
              <button
                type="button"
                onClick={() => setShowCategoryInput(false)}
                className="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ) : null}

          <select
            name="category"
            value={currentFAQ.category}
            onChange={handleInputChange}
            className="w-full p-2 border dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
            required
          >
            <option value="">Select a category</option>
            {categories.map(category => (
              <option key={category.id} value={category.name}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        <div className="flex gap-2">
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600"
          >
            {isEditing ? 'Update FAQ' : 'Add FAQ'}
          </button>
          {isEditing && (
            <button
              type="button"
              onClick={resetForm}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
            >
              Cancel
            </button>
          )}
        </div>
      </form>

      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">Existing FAQs</h3>
        <div className="space-y-4">
          {faqs.map((faq) => (
            <div
              key={faq.id}
              className="p-4 border dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium">{faq.question}</h4>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEdit(faq)}
                    className="p-1 text-blue-500 hover:text-blue-600"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(faq.id)}
                    className="p-1 text-red-500 hover:text-red-600"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {faq.answer}
              </p>
              <div className="flex items-center text-sm text-gray-500">
                <Tag className="w-4 h-4 mr-1" />
                {faq.category}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FAQEditor;