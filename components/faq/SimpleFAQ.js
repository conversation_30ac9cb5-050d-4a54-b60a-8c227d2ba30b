import React, { useState, useEffect } from 'react'

const SimpleFAQ = ({ data = [], title = "Frequently Asked Questions" }) => {
  const [openIndex, setOpenIndex] = useState(null)
  const [processedData, setProcessedData] = useState([])
  const [error, setError] = useState(null)

  useEffect(() => {
    try {
      // Handle case where data is not provided or invalid
      if (!data) {
        console.warn('FAQ data is null or undefined');
        setProcessedData([]);
        return;
      }

      // Handle case where data is an object with language keys
      if (typeof data === 'object' && !Array.isArray(data)) {
        // Try to use 'vi' or first available key
        const firstKey = Object.keys(data)[0];
        const selectedData = data['vi'] || data[firstKey] || [];
        
        if (Array.isArray(selectedData)) {
          setProcessedData(selectedData);
        } else {
          console.warn('FAQ data format is invalid', selectedData);
          setProcessedData([]);
        }
        return;
      }

      // Handle case where data is already an array
      if (Array.isArray(data)) {
        setProcessedData(data);
        return;
      }

      // Fallback for any other case
      console.warn('FAQ data format is unrecognized', typeof data);
      setProcessedData([]);
    } catch (err) {
      console.error('Error processing FAQ data:', err);
      setError(err.message);
      setProcessedData([]);
    }
  }, [data]);

  const toggleQuestion = (index) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-300 shadow-sm w-full mb-4 p-4">
        <p className="text-red-500">Error loading FAQ data: {error}</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg border shadow-sm w-full mb-4">
      <div className="flex flex-col space-y-1.5 p-6">
        <h3 className="text-lg font-semibold leading-none tracking-tight">
          {title}
        </h3>
      </div>
      <div className="p-6 pt-0">
        <div className="divide-y divide-gray-200">
          {processedData && processedData.length > 0 ? (
            processedData.map((item, index) => (
              <div key={index} className="py-3">
                <button
                  onClick={() => toggleQuestion(index)}
                  className="flex w-full justify-between text-left"
                >
                  <h3 className="font-medium text-gray-900">
                    {item.question}
                  </h3>
                  <span className="ml-6 flex items-center">
                    {openIndex === index ? (
                      <MinusIcon className="h-6 w-6" />
                    ) : (
                      <PlusIcon className="h-6 w-6" />
                    )}
                  </span>
                </button>
                {openIndex === index && (
                  <div className="mt-2">
                    <p className="text-gray-600">
                      {item.answer}
                    </p>
                  </div>
                )}
              </div>
            ))
          ) : (
            <p className="text-gray-500 py-2">No FAQ data available</p>
          )}
        </div>
      </div>
    </div>
  )
}

const PlusIcon = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className}
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
)

const MinusIcon = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className}
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 12h-15" />
  </svg>
)

export default SimpleFAQ 