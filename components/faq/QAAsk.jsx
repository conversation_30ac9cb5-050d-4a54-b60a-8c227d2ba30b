import { useState } from 'react'
import { toast } from 'react-toastify'
import { FaQuestion, FaRocketchat } from 'react-icons/fa'

export const QAAsk = ({ onQuestionSubmit }) => {
  const [newQuestion, setNewQuestion] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  const handleSubmitQuestion = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      toast.success('Cảm ơn bạn, chúng tôi đã nhận được câu hỏi và sẽ trả lời sau.', {
        position: 'bottom-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
      setNewQuestion('')
      if (onQuestionSubmit) {
        onQuestionSubmit(newQuestion)
      }
    } catch (error) {
      toast.error('<PERSON><PERSON> x<PERSON>y ra lỗi. <PERSON><PERSON> lòng thử lại.', {
        position: 'bottom-right',
        autoClose: 3000,
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="relative">
      {!isExpanded ? (
        <button
          onClick={() => setIsExpanded(true)}
          className="rounded-full bg-primary-500 p-4 text-white shadow-lg hover:opacity-80"
        >
          <FaRocketchat size={24} />
        </button>
      ) : (
        <div className="absolute bottom-0 right-0 w-80 rounded-lg bg-white p-4 shadow-lg dark:bg-gray-900">
          <div className="flex justify-end">
            <button
              onClick={() => setIsExpanded(false)}
              className="mb-2 text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>
          <form onSubmit={handleSubmitQuestion} className="flex flex-col gap-2">
            <textarea
              required
              value={newQuestion}
              onChange={(e) => setNewQuestion(e.target.value)}
              placeholder="Nhập câu hỏi của bạn tại đây..."
              className="w-full rounded-md border border-gray-300 p-2 text-black dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              rows={3}
            />
            <button
              type="submit"
              disabled={isSubmitting || !newQuestion}
              className="w-full rounded-md bg-primary-500 px-4 py-2 text-white transition hover:opacity-80 disabled:opacity-50"
            >
              {isSubmitting ? 'Đang gửi...' : 'Đặt câu hỏi'}
            </button>
          </form>
        </div>
      )}
    </div>
  )
}

export default QAAsk 