import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const FAQSection = ({ faqs, loading, error }) => {
  const [activeCategory, setActiveCategory] = useState(null);
  const [openQuestions, setOpenQuestions] = useState(new Set());
  const [lastOpenedId, setLastOpenedId] = useState(null);

  const categories = Array.from(new Set(faqs.map(faq => faq.category)));

  const toggleQuestion = (id) => {
    const newOpenQuestions = new Set(openQuestions);
    if (openQuestions.has(id)) {
      newOpenQuestions.delete(id);
      setLastOpenedId(null);
    } else {
      newOpenQuestions.add(id);
      setLastOpenedId(id);
    }
    setOpenQuestions(newOpenQuestions);
  };

  if (loading) {
    return (
      <div className="grid gap-6 sm:grid-cols-2">
        {[1, 2, 3, 4].map((n) => (
          <div key={n} className="animate-pulse">
            <div className="h-20 bg-gray-200 dark:bg-gray-700/50 rounded-2xl mb-3"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 dark:bg-gray-700/50 rounded-full w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700/50 rounded-full w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 rounded-2xl bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800">
        <div className="flex items-center justify-center text-red-600 dark:text-red-400">
          <span className="font-medium text-lg">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-10">
      {/* Categories */}
      <div className="flex flex-wrap gap-3 justify-center">
        {categories.map(category => (
          <button
            key={category}
            onClick={() => setActiveCategory(activeCategory === category ? null : category)}
            className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ease-out hover:scale-105 
              ${activeCategory === category
                ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg shadow-indigo-500/25'
                : 'bg-gray-100 dark:bg-gray-800/50 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700/50 border border-gray-200 dark:border-gray-700'
              }`}
          >
            {category}
          </button>
        ))}
      </div>

      {/* FAQ Grid */}
      <div className="grid gap-6 sm:grid-cols-2">
        {faqs
          .filter(faq => !activeCategory || faq.category === activeCategory)
          .map(faq => (
            <div
              key={faq.id}
              className={`group bg-white dark:bg-gray-800/50 border dark:border-gray-700/50 rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300
                ${lastOpenedId === faq.id ? 'ring-2 ring-indigo-500/50 dark:ring-indigo-400/50 ring-offset-4 ring-offset-white dark:ring-offset-gray-900' : ''}`}
            >
              <button
                onClick={() => toggleQuestion(faq.id)}
                className="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 dark:hover:bg-gray-800/80 transition-colors duration-300"
              >
                <span className="font-medium text-gray-900 dark:text-gray-100 group-hover:text-indigo-500 dark:group-hover:text-indigo-400 transition-colors duration-300">
                  {faq.question}
                </span>
                <div className="flex-shrink-0 ml-4">
                  {openQuestions.has(faq.id) ? (
                    <ChevronUp className="w-5 h-5 text-indigo-500 dark:text-indigo-400" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-400 group-hover:text-indigo-500 dark:text-gray-500 dark:group-hover:text-indigo-400" />
                  )}
                </div>
              </button>
              
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  openQuestions.has(faq.id) ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="p-6 bg-gray-50 dark:bg-gray-800/80 border-t dark:border-gray-700/50">
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default FAQSection;