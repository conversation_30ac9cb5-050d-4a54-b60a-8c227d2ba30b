'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from './card'
import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import QAAsk from './QAAsk'

export const QABox = ({ data, title = "Frequently Asked Questions" }) => {
  const [openIndex, setOpenIndex] = useState(null)

  const toggleQuestion = (index) => {
    setOpenIndex(openIndex === index ? null : index)
  }
  return (
    <>
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>          
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((item, index) => (
              <div key={index} className="py-3">
                <button
                  onClick={() => toggleQuestion(index)}
                  className="flex w-full justify-between text-left"
                >
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    {item.question}
                  </h3>
                  <span className="ml-6 flex items-center">
                    {openIndex === index ? (
                      <MinusIcon className="h-6 w-6" />
                    ) : (
                      <PlusIcon className="h-6 w-6" />
                    )}
                  </span>
                </button>
                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="mt-2"
                    >
                      <p className="text-gray-600 dark:text-gray-400">
                        {item.answer}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        </CardContent>
        <QAAsk />
      </Card>
      <ToastContainer 
        autoClose={3000} 
        position="top-right" 
        hideProgressBar={false} 
        newestOnTop={false} 
        closeOnClick={true} 
        rtl={false} 
        pauseOnFocusLoss={true} 
        draggable={true} 
        pauseOnHover={true} 
      />
    </>
  )
}

const PlusIcon = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className}
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
)

const MinusIcon = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className}
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 12h-15" />
  </svg>
)

export default QABox 