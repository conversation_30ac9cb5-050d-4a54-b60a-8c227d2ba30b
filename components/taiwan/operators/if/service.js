import axios from 'axios';
import crypto from 'crypto';
import config from './config.json';

// Helper for consistent logging
const logIFCardAPI = (message, data = null) => {
  const timestamp = new Date().toISOString();
  const logPrefix = `[IF CARD API ${timestamp}]`;
  
  if (data) {
    // Mask sensitive data before logging
    const maskedData = { ...data };
    if (maskedData.password) maskedData.password = '********';
    if (maskedData.key) maskedData.key = maskedData.key.substring(0, 4) + '****';
    if (maskedData.iv) maskedData.iv = maskedData.iv.substring(0, 4) + '****';
    
    console.log(`${logPrefix} ${message}`, JSON.stringify(maskedData, null, 2));
  } else {
    console.log(`${logPrefix} ${message}`);
  }
};

/**
 * Encrypt text using AES-128-CBC with multiple approaches
 * @param {string} text - Text to encrypt
 * @param {string} purpose - Why this is being encrypted (for logging)
 * @returns {Object} - Object with encryption results from all methods
 */
function getEncryptionVariants(text, purpose = 'unknown') {
  const variants = {};
  
  try {
    // Method 1: Use the original fixed key/IV from documentation
    const docsKey = 'ptWadzG6WfLMs7fi';
    const docsIV = 'ptWadzG6WfLMs7fi';
    variants.docsKey = encryptWithKeyIV(text, docsKey, docsIV);
    
    // Method 2: Use values from config file
    variants.configKey = encryptWithKeyIV(text, config.encryption.key, config.encryption.iv);
    
    // Method 3: Use password as key/IV
    const password = config.merchantPassword;
    const passwordKey = password.padEnd(16, ' ').substring(0, 16);
    const passwordIV = password.padEnd(16, ' ').substring(0, 16);
    variants.passwordKey = encryptWithKeyIV(text, passwordKey, passwordIV);
    
    // Method 4: Use the merchantID as key/IV
    const merchantKey = config.merchantID.padEnd(16, ' ').substring(0, 16);
    const merchantIV = config.merchantID.padEnd(16, ' ').substring(0, 16);
    variants.merchantKey = encryptWithKeyIV(text, merchantKey, merchantIV);
    
    logIFCardAPI(`Encryption variants for ${purpose}`, {
      text: text.substring(0, 4) + '*****',
      docsKey: variants.docsKey.substring(0, 10) + '****',
      configKey: variants.configKey.substring(0, 10) + '****',
      passwordKey: variants.passwordKey.substring(0, 10) + '****',
      merchantKey: variants.merchantKey.substring(0, 10) + '****'
    });
  } catch (error) {
    logIFCardAPI(`Error generating encryption variants for ${purpose}`, {
      error: error.message
    });
  }
  
  return variants;
}

/**
 * Internal helper for encryption with specific key/IV
 */
function encryptWithKeyIV(text, key, iv) {
  try {
    const cipher = crypto.createCipheriv(
      config.encryption.algorithm,
      key,
      iv
    );
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  } catch (error) {
    logIFCardAPI('Encryption error', { error: error.message });
    return null;
  }
}

/**
 * Activate a VRC card (recharge)
 * @param {Object} params - Card activation parameters
 * @param {string} params.phoneNumber - The phone number to recharge
 * @param {string} params.productId - The product ID/FETOfferID
 * @param {string} params.orderId - Optional order ID for reference
 * @returns {Promise<Object>} - Activation result
 */
export async function activateCard(params) {
  const {
    phoneNumber,
    productId,
    orderId
  } = params;
  
  const isDebugMode = process.env.NODE_ENV !== 'production';
  
  logIFCardAPI('Received VRC recharge request', { 
    phoneNumber: phoneNumber ? phoneNumber.substring(0, 4) + '***' + phoneNumber.slice(-3) : undefined,
    productId,
    orderId,
    debug: isDebugMode
  });
  
  try {
    // Set the API URL based on environment
    const API_URL = process.env.NODE_ENV === 'production' 
      ? config.liveUrl 
      : config.testUrl;
    
    logIFCardAPI('Using API endpoint', { 
      url: API_URL,
      environment: process.env.NODE_ENV,
      nodeVersion: process.version,
      hostname: require('os').hostname()
    });
    
    // Log merchant details (partially masked)
    logIFCardAPI('Merchant credentials', {
      merchantID: config.merchantID.substring(0, 4) + '*****',
      merchantIDLength: config.merchantID.length,
      passwordLength: config.merchantPassword.length,
      apiKeyLength: config.apiKey.length
    });
    
    // Generate multiple encryption variants
    const accountVariants = getEncryptionVariants(config.merchantID, 'account');
    const passwordVariants = getEncryptionVariants(config.merchantPassword, 'password');
    
    // Find product information
    const product = config.productCodes.find(p => p.id === productId);
    if (!product) {
      throw new Error(`Invalid product ID: ${productId}`);
    }
    
    // Try each variant of encryption, one by one
    const encryptionMethods = [
      { name: 'Documentation Key', account: accountVariants.docsKey, password: passwordVariants.docsKey },
      { name: 'Config Key', account: accountVariants.configKey, password: passwordVariants.configKey },
      { name: 'Password as Key', account: accountVariants.passwordKey, password: passwordVariants.passwordKey },
      { name: 'MerchantID as Key', account: accountVariants.merchantKey, password: passwordVariants.merchantKey }
    ];
    
    let successfulMethod = null;
    let lastError = null;
    let lastErrorResponse = null;

    for (const method of encryptionMethods) {
      if (!method.account || !method.password) continue;
      
      // Prepare the API request payload according to documentation
      const payload = {
        Account: method.account,
        Password: method.password,
        PhoneNumber: phoneNumber,
        FETOfferID: productId
      };
      
      logIFCardAPI(`Trying encryption method: ${method.name}`, {
        methodName: method.name,
        accountSample: method.account.substring(0, 10) + '****',
        passwordSample: method.password.substring(0, 10) + '****',
        phoneNumber: phoneNumber.substring(0, 4) + '****' + phoneNumber.slice(-2),
        productId
      });
      
      try {
        // Make the API request
        const rechargeResponse = await axios.post(
          API_URL, 
          payload, 
          {
            headers: {
              'Content-Type': 'application/json',
              'APIKEY': config.apiKey
            },
            timeout: 30000 // 30 second timeout
          }
        );
        
        logIFCardAPI(`Response received for method: ${method.name}`, {
          status: rechargeResponse.status,
          statusText: rechargeResponse.statusText,
          data: rechargeResponse.data
        });
        
        // If we get a successful code, we found our method!
        if (rechargeResponse.data && rechargeResponse.data.ReturnCode === '0000') {
          successfulMethod = method;
          
          // Successful response
          logIFCardAPI('SUCCESS: Card recharged successfully', {
            methodName: method.name,
            phoneNumber: phoneNumber ? phoneNumber.substring(0, 4) + '***' + phoneNumber.slice(-3) : undefined,
            transactionId: rechargeResponse.data.ReturnTXID
          });
          
          return {
            success: true,
            data: {
              resultCode: rechargeResponse.data.ReturnCode,
              message: rechargeResponse.data.ReturnMsg,
              activationDate: new Date().toISOString(),
              cardValue: product.price,
              transactionId: rechargeResponse.data.ReturnTXID || orderId,
              productId: productId,
              productName: product.name,
              encryptionMethod: method.name
            }
          };
        } else {
          // Store error for potential return
          lastError = rechargeResponse.data;
          
          // If this is not an authentication error, no need to try other methods
          // ReturnCode 0004 is authentication error, others are different kinds of errors
          if (rechargeResponse.data.ReturnCode !== '0004') {
            logIFCardAPI(`Stopping encryption tests - received non-auth error: ${rechargeResponse.data.ReturnCode}`, {
              methodName: method.name,
              returnCode: rechargeResponse.data.ReturnCode,
              returnMsg: rechargeResponse.data.ReturnMsg
            });
            
            // Return this error since it's not an auth issue
            return {
              success: false,
              error: rechargeResponse.data.ReturnMsg || 'Card activation provider returned an error',
              resultCode: rechargeResponse.data.ReturnCode,
              testedMethod: method.name
            };
          }
        }
      } catch (error) {
        logIFCardAPI(`API error for method: ${method.name}`, { 
          error: error.message,
          response: error.response?.data
        });
        
        lastErrorResponse = error.response?.data;
      }
    }
    
    // If we made it here, none of the methods worked
    logIFCardAPI('All encryption methods failed', { 
      lastError, 
      lastErrorResponse,
      testedMethods: encryptionMethods.map(m => m.name)
    });
    
    // Return the last error we got
    const errorMsg = lastError?.ReturnMsg || 'All encryption methods failed';
    const resultCode = lastError?.ReturnCode || 'unknown';
    
    return {
      success: false,
      error: errorMsg,
      resultCode,
      testedMethods: encryptionMethods.map(m => m.name)
    };
  } catch (error) {
    // Catch any other errors
    logIFCardAPI('Unexpected error processing request', { 
      message: error.message,
      stack: error.stack
    });
    
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again later.',
      errorMessage: error.message
    };
  }
}

/**
 * Get a list of available products
 * @returns {Array} - List of available products
 */
export function getProducts() {
  return config.productCodes.map(p => ({
    id: p.id,
    name: p.name,
    price: p.price
  }));
}

// No status check API mentioned in the documentation
// But we can provide a simulated status check based on transaction ID
export async function checkCardStatus(transactionId) {
  logIFCardAPI(`Checking card status for transaction ${transactionId}`);
  
  // Since there's no actual status check API in the documentation,
  // we'll simulate a response based on transaction ID format
  const isValidTxId = /^\d{8}\d{5}$/.test(transactionId); // Format: yyyyMMdd + 5-digit serial
  
  if (isValidTxId) {
    // This is a simulated response
    return {
      success: true,
      data: {
        status: 'completed',
        message: 'Transaction completed successfully',
        activationDate: new Date().toISOString(),
        transactionId
      }
    };
  } else {
    return {
      success: false,
      error: 'Invalid or unknown transaction ID',
      resultCode: '0004'
    };
  }
}

export default {
  activateCard,
  checkCardStatus,
  getProducts
}; 