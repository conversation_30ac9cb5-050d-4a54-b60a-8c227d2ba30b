.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .container {
    flex-direction: row;
    gap: 2rem;
  }
}

.formContainer {
  flex: 2;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.orderSummary {
  flex: 1;
  padding: 1rem;
  background-color: #f0f5ff;
  border: 1px solid #c2d1ff;
  border-radius: 0.5rem;
}

.formTitle, .summaryTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.form {
  width: 100%;
}

.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.input, .textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.textarea {
  min-height: 4rem;
  resize: vertical;
}

.errorMessage {
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #fee2e2;
  color: #b91c1c;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.orderInfo {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #ffffff;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

.orderInfoItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.orderInfoValue {
  font-weight: 600;
}

.costBreakdown {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #ffffff;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

.costItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.totalCost {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.totalValue {
  font-size: 1.125rem;
  color: #1e40af;
}

.submitButton {
  width: 100%;
  margin-top: 1rem;
}

.processingMessage {
  text-align: center;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
}

/* Order complete / Thank you styles */
.orderComplete {
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  padding: 1rem;
}

.thankYouContainer {
  background-color: #f0fdf4;
  border: 1px solid #86efac;
  border-radius: 0.5rem;
  padding: 2rem;
}

.thankYouTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #166534;
  text-align: center;
  margin-bottom: 1.5rem;
}

.orderReference {
  text-align: center;
  font-size: 1.125rem;
  margin-bottom: 1.5rem;
}

.storeInfo {
  background-color: #ffffff;
  border: 1px solid #d1fae5;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.paymentInfo {
  background-color: #ffffff;
  border: 1px solid #d1fae5;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.amountTotal {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

.paymentNoLabel, .expiryDate {
  margin-bottom: 0.5rem;
}

.barcodesContainer {
  background-color: #ffffff;
  border: 1px solid #d1fae5;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.barcodesTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.barcodes {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.barcodeWrapper {
  width: 100%;
  text-align: center;
}

.barcode {
  max-width: 100%;
  height: auto;
}

.paymentInstructions {
  background-color: #fffbeb;
  border: 1px solid #fef3c7;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.paymentInstructions h3 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.instructionsList {
  list-style-type: decimal;
  padding-left: 1.5rem;
}

.instructionsList li {
  margin-bottom: 0.5rem;
}

.thankYouMessage {
  text-align: center;
  margin-bottom: 1.5rem;
}

.buttonsContainer {
  display: flex;
  justify-content: center;
}

/* Animation for blinking text */
@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.blinking {
  animation: blink 2s infinite;
  color: #166534;
  font-weight: 600;
  text-align: center;
  padding: 0.5rem;
  background-color: #dcfce7;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
} 