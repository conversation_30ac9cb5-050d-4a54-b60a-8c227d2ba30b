import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Image from '../../../../Image';
import Button from '../../../../Button';
import { SiteContext } from '../../../../../context/mainContext';
import Link from 'next/link';
import config from './config.json';
import styles from './FamilyMartPayment.module.css';
import { getCustomerId, getCustomerInfo, isCustomerAuthenticated } from '../../../../../utils/customerAuth';
import FamilyMartBarcodeDisplay from './FamilyMartBarcodeDisplay';
// Import the checkout logger
import {
  logCheckoutProcess,
  logPaymentApiCall,
  logPaymentApiResponse,
  logTransaction,
  logCheckoutError
} from '../../../../../utils/checkoutLogger';

// Enhanced logging utility that uses the checkout logger
const logPayment = (message, data = null) => {
  // Use the checkout logger to log FamilyMart payment events
  return logCheckoutProcess('familymart_payment', { 
    message, 
    data,
    component: 'FamilyMartPaymentForm'
  });
};

const FamilyMartPaymentForm = ({
  handleSubmit,
  shopId = '',
  orderId = '',
  errorMessage,
  cart = [],
  paymentimage,
  onError,
  amount = 0,
  cartItems = [],
  setOrderCompleted,
  saveOrderToLocalStorage,
  currency = config.localization.currency,
  initialUserInfo = null
}) => {
  // Get payment fee from config
  const PAYMENT_FEE = config.payment.fee.amount;
  
  // Helper function to get localized text
  const t = (path) => {
    const keys = path.split('.');
    let value = config;
    for (const key of keys) {
      value = value[key];
      if (!value) return path;
    }
    return value.vi || value.en || path;
  };
  
  logPayment("Initializing FamilyMart Payment Form", { 
    orderId,
    shopId,
    amount
  });

  // Log component initialization with transaction information
  useEffect(() => {
    logTransaction(orderId, 'STARTED', {
      paymentMethod: 'familymart',
      amount,
      currency,
      storeId: shopId,
      paymentSystem: 'taiwan-familymart'
    });
    
    return () => {
      logPayment("FamilyMart payment form unmounting", { orderId });
    };
  }, [orderId, amount, currency, shopId]);

  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState(contextEmail || "");
  const [phone, setPhone] = useState(contextPhone || "");
  const [address, setAddress] = useState("");
  const [message, setMessage] = useState("");
  const [userInfo, setUserInfo] = useState(initialUserInfo);
  // Add required fields for FamilyMart API
  const [familyMartStore, setFamilyMartStore] = useState("FamilyMart Taiwan");
  const [storeCode, setStoreCode] = useState("FM" + Date.now().toString().substring(8));
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderDate, setOrderDate] = useState(new Date());
  const [isOrderProcessed, setIsOrderProcessed] = useState(false);
  const [paymentCode, setPaymentCode] = useState("");
  const [paymentBarcode, setPaymentBarcode] = useState("");
  const [paymentExpiry, setPaymentExpiry] = useState("");
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(!!initialUserInfo);

  // For phone card recharge products
  const [rechargePhone, setRechargePhone] = useState("");
  const [autoRecharge, setAutoRecharge] = useState(config.phone_recharge.default_auto_recharge);

  // Get product type from cart items if available
  const getProductType = () => {
    const phoneCards = [...config.phone_recharge.products.prepaid, ...config.phone_recharge.products.extension];
    
    for (const item of cart) {
      // Check product name or SKU for phone card products
      const isPhoneCard = phoneCards.some(code => 
        item.name?.includes(code) || item.sku?.includes(code)
      );
      
      if (isPhoneCard) {
        return item.name || item.sku;
      }
    }
    
    return null;
  };
  
  const productType = getProductType();
  const isPhoneRecharge = productType !== null;
  const isExtension = config.phone_recharge.products.extension.includes(productType);

  // Calculate total with fee
  const baseAmount = (cartItems && cartItems.length) ?
    cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0) :
    amount || 0;
  const totalWithFee = baseAmount + PAYMENT_FEE;

  const handleInputChange = (setter) => (e) => {
    logPayment(`Updating form field: ${e.target.name || e.target.id}`, { 
      field: e.target.name || e.target.id,
      value: e.target.value,
      orderId
    });
    setter(e.target.value);
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    logPayment("Form submission started", {
      firstName,
      lastName,
      fullName,
      email,
      phone,
      currency,
      isPhoneRecharge,
      rechargePhone: rechargePhone || phone,
      autoRecharge,
      familyMartStore,
      storeCode,
      orderId
    });
    
    // Log the transaction update
    logTransaction(orderId, 'PAYMENT_INITIATED', {
      paymentMethod: 'familymart',
      amount: totalWithFee,
      currency,
      customerInfo: {
        firstName,
        lastName,
        fullName: fullName || `${firstName} ${lastName}`,
        email,
        phone,
        address
      },
      productType: isPhoneRecharge ? productType : 'standard'
    });
    
    if (isSubmitting) {
      logPayment("Submission already in progress, skipping", { orderId });
      return;
    }
    
    // Validate required fields
    if (!fullName && (!firstName || !lastName)) {
      const errorMsg = "Vui lòng nhập tên của bạn";
      setErrorMessage(errorMsg);
      
      // Log validation error
      logCheckoutError('familymart-validation', errorMsg, {
        orderId,
        missingFields: ['name']
      });
      
      if (onError) onError(errorMsg);
      return;
    }

    if (!phone) {
      const errorMsg = "Vui lòng nhập số điện thoại của bạn";
      setErrorMessage(errorMsg);
      
      // Log validation error
      logCheckoutError('familymart-validation', errorMsg, {
        orderId,
        missingFields: ['phone']
      });
      
      if (onError) onError(errorMsg);
      return;
    }
    
    setIsSubmitting(true);

    try {
      // Generate unique order reference
      const uniqueOrderId = orderId 
      
      // Log warning if no orderId was provided
      if (!orderId) {
        logCheckoutError('familymart-missing-orderid', 'No orderId provided from checkout.js, generating fallback ID', {
          generatedId: uniqueOrderId
        });
      }
      
      logPayment("Using order ID from checkout", { 
        orderId,
        uniqueOrderId
      });
      
      // Prepare FamilyMart barcode API payload
      const payload = {
        MerchantID: config.api.credentials.merchant_id,
        MerchantTradeNo: uniqueOrderId,
        MerchantTradeDate: orderDate.toISOString(),
        TotalAmount: Math.round(totalWithFee),
        TradeDesc: `Đơn hàng #${uniqueOrderId}`,
        ItemName: cart.map(item => item.name).join('#').substring(0, 200) || "Sản phẩm",
        ReturnURL: `${window.location.origin}/api/payment/family-mart-callback`,
        ClientBackURL: `${window.location.origin}/orders`,
        PaymentInfoURL: `${window.location.origin}/api/payment/family-mart-notification`,
        PaymentType: 'CVS',
        ChoosePayment: 'CVS',
        StoreExpireDate: config.payment.limits.expiry_hours,
        Desc_1: '',
        Desc_2: '',
        Desc_3: '',
        Desc_4: '',
        CustomerName: isUserAuthenticated && userInfo ? 
          (userInfo.name || "Khách hàng") : 
          (fullName || `${firstName} ${lastName}`).trim() || "Khách hàng",
        CustomerPhone: isUserAuthenticated && userInfo ? 
          (userInfo.phone || phone).trim() : 
          phone.trim(),
        CustomerEmail: isUserAuthenticated && userInfo ? 
          (userInfo.email || email).trim() : 
          email.trim(),
        // Required fields for FamilyMart
        FamilyMartStore: familyMartStore,
        StoreCode: storeCode
      };
      
      // Set timeout for API call
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Hết thời gian yêu cầu API FamilyMart")), config.security.timeout)
      );
      
      // Call payment API
      logPayment("Preparing FamilyMart payment request", {
        orderId,
        uniqueOrderId,
        paymentData: { ...payload, MerchantID: '***' }
      });
      
      // Log API call
      logPaymentApiCall(
        'taiwan-familymart-api',
        '/api/payment/create-family-mart-payment',
        {
          ...payload,
          MerchantID: '***' // Hide sensitive data
        },
        { method: 'POST' }
      );
      
      const response = await Promise.race([
        axios.post('/api/payment/create-family-mart-payment', payload),
        timeoutPromise
      ]);
      
      // Log API response
      logPaymentApiResponse(
        'taiwan-familymart-api',
        '/api/payment/create-family-mart-payment',
        response.data,
        response.status
      );
      
      logPayment("FamilyMart API response received", {
        orderId,
        status: response.status,
        success: response.data?.success
      });
      
      if (response.data && response.data.success) {
        // Extract payment information
        const paymentData = response.data.data;
        setPaymentCode(paymentData.PaymentNo || '');
        setPaymentBarcode(paymentData.Barcode || '');
        setPaymentExpiry(paymentData.ExpireDate || '');
        
        // Log transaction update
        logTransaction(orderId, 'PAYMENT_CODE_GENERATED', {
          paymentMethod: 'familymart',
          uniquePaymentId: uniqueOrderId,
          paymentCode: paymentData.PaymentNo || '',
          paymentBarcode: paymentData.Barcode || '',
          expiryDate: paymentData.ExpireDate || ''
        });
        
        // Prepare the order data for our system
        const orderData = {
          orderId: uniqueOrderId,
          storeId: shopId,
          paymentMethod: "convenience_store",
          paymentSubMethod: "family_mart",
          currency: currency,
          customerInfo: {
            firstName,
            lastName,
            fullName: fullName || `${firstName} ${lastName}`,
            email: isUserAuthenticated && userInfo ? (userInfo.email || email) : email,
            phone: isUserAuthenticated && userInfo ? (userInfo.phone || phone) : phone,
            address: isUserAuthenticated && userInfo ? (userInfo.address || address) : address,
            message,
          },
          paymentInfo: {
            paymentCode: paymentData.PaymentNo || '',
            paymentBarcode: paymentData.Barcode || '',
            paymentExpiry: paymentData.ExpireDate || '',
            amount: totalWithFee,
            familyMartStore,
            storeCode
          },
          // Add phone recharge information if applicable
          rechargeInfo: isPhoneRecharge ? {
            productType,
            phoneNumber: rechargePhone || phone,
            autoRecharge,
            status: 'pending',
            isPhoneExtension: isExtension
          } : null,
          items: cartItems || cart,
          amount: totalWithFee,
          orderDate: orderDate.toISOString(),
          familyMartResponse: paymentData
        };

        logPayment("Sending order to API", { orderId: orderData.orderId });
        
        // Send to our backend with retry
        let backendSuccess = false;
        let retryCount = 0;
        let backendError = null;
        
        while (!backendSuccess && retryCount < 3) {
          try {
            // Log the API call
            logPaymentApiCall(
              'backend-api',
              '/api/submitOrder',
              orderData,
              { method: 'POST' }
            );
            
            const backendResponse = await axios.post("/api/submitOrder", orderData);
            
            // Log the API response
            logPaymentApiResponse(
              'backend-api',
              '/api/submitOrder',
              backendResponse.data,
              backendResponse.status
            );
            
            if (backendResponse.data && backendResponse.data.success) {
              backendSuccess = true;
              logPayment("Order submitted successfully", { 
                orderId: orderData.orderId,
                responseData: backendResponse.data
              });
              
              // Log transaction update
              logTransaction(orderData.orderId, 'ORDER_SAVED', {
                response: backendResponse.data,
                paymentMethod: 'familymart'
              });
            } else {
              backendError = new Error(backendResponse.data?.message || "Lỗi máy chủ không xác định");
              throw backendError;
            }
          } catch (error) {
            logPayment("Error in backend retry attempt", { 
              attempt: retryCount, 
              error: error.message,
              orderId: orderData.orderId
            });
            
            // Log the error
            logCheckoutError('familymart-backend-retry', error, {
              attempt: retryCount,
              orderId: orderData.orderId,
              paymentMethod: 'familymart'
            });
            
            backendError = error;
            retryCount++;
            
            if (retryCount < 3) {
              const backoffTime = Math.pow(2, retryCount) * 1000;
              logPayment(`Retrying backend in ${backoffTime}ms (attempt ${retryCount}/3)`, {
                orderId: orderData.orderId
              });
              await new Promise(resolve => setTimeout(resolve, backoffTime));
            }
          }
        }
        
        if (backendSuccess || paymentCode) {
          // Even if backend fails, we have payment info to show
          // Save order to local storage if available
          if (saveOrderToLocalStorage) {
            logPayment("Saving order to localStorage", { orderId: orderData.orderId });
            saveOrderToLocalStorage({
              orderId: orderData.orderId,
              status: 'Đang xử lý',
              customerInfo: {
                firstName,
                lastName,
                email,
                phone,
              },
              paymentMethod: "convenience_store",
              paymentInfo: {
                store: "FamilyMart",
                paymentCode: paymentData.PaymentNo || '',
                paymentExpiry: paymentData.ExpireDate || ''
              }
            });
          }
          
          setIsOrderProcessed(true);
          if (setOrderCompleted) {
            logPayment("Setting order as completed", { orderId: orderData.orderId });
            setOrderCompleted(true);
          }
        } else if (backendError) {
          // Show error message
          throw backendError;
        }
      } else {
        // Error response from API
        const errorMessage = response.data?.message || "Xử lý thanh toán thất bại";
        
        // Log API error
        logCheckoutError('familymart-api-error', errorMessage, {
          orderId,
          response: response.data
        });
        
        throw new Error(`Lỗi thanh toán FamilyMart: ${errorMessage}`);
      }
      
    } catch (error) {
      logPayment("Error submitting order", { 
        error: error.message,
        orderId
      });
      
      // Log detailed error
      logCheckoutError('familymart-payment-error', error, {
        orderId,
        customerInfo: {
          fullName: fullName || `${firstName} ${lastName}`,
          email,
          phone
        }
      });
      
      // Log transaction failure
      logTransaction(orderId, 'PAYMENT_FAILED', {
        paymentMethod: 'familymart',
        error: error.message
      });
      
      if (onError) {
        onError("Lỗi xử lý đơn hàng của bạn: " + (error.message || "Vui lòng thử lại."));
      } else {
        // Fallback error handling if onError prop is not provided
        alert("Lỗi xử lý đơn hàng của bạn: " + (error.message || "Vui lòng thử lại."));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatExpiryDate = (dateString) => {
    if (!dateString) return "";
    
    try {
      // Handle format YYYYMMDD
      if (/^\d{8}$/.test(dateString)) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        return `${year}/${month}/${day}`;
      }
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString;
      }
      
      return date.toLocaleDateString(config.localization.language, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };



  useEffect(() => {
    if (typeof window !== "undefined") {
      const authenticated = isCustomerAuthenticated();
      setIsUserAuthenticated(authenticated);
      
      if (authenticated) {
        const customerInfo = getCustomerInfo();
        setUserInfo(customerInfo);
        
        // Pre-fill form fields with customer information
        if (customerInfo) {
          if (customerInfo.name) {
            setFullName(customerInfo.name);
          }
          
          if (customerInfo.email) {
            setEmail(customerInfo.email);
          }
          
          if (customerInfo.phone) {
            setPhone(customerInfo.phone);
          }
          
          if (customerInfo.address) {
            setAddress(customerInfo.address);
          }
        }
      } else if (initialUserInfo) {
        // Initialize from props if available
        setUserInfo(initialUserInfo);
      }
    }
  }, []);

  // Initialize form fields from initialUserInfo if available
  useEffect(() => {
    if (initialUserInfo) {
      if (initialUserInfo.name) {
        setFullName(initialUserInfo.name);
      }
      
      if (initialUserInfo.email) {
        setEmail(initialUserInfo.email);
      }
      
      if (initialUserInfo.phone) {
        setPhone(initialUserInfo.phone);
      }
      
      if (initialUserInfo.address) {
        setAddress(initialUserInfo.address);
      }
    }
  }, [initialUserInfo]);

  if (isOrderProcessed) {
    logPayment("Showing thank you message for processed order", { orderId });
    return (
      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
        <h3 className="text-2xl font-bold text-green-800 mb-4">{t('thank_you.title')}</h3>
        <p className="mb-2">{t('thank_you.order_success').replace('{orderId}', orderId)}</p>
        
        {isPhoneRecharge && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <h4 className="text-xl font-bold text-yellow-800 mb-2">{t('phone_recharge.messages.title')}</h4>
            <p>{t('phone_recharge.messages.product')}: <strong>{productType}</strong></p>
            <p>{t('phone_recharge.messages.phone_number')}: <strong>{rechargePhone || phone}</strong></p>
            <p>{t('phone_recharge.messages.method')}: <strong>{autoRecharge ? t('phone_recharge.messages.auto_recharge') : t('phone_recharge.messages.manual_recharge')}</strong></p>
            
            <div className="mt-3">
              <p className="text-sm">
                {t('phone_recharge.messages.support_text')}
                <a 
                  href={`https://line.me/R/ti/p/@maggroup`} 
                  target="_blank"
                  rel="noopener noreferrer" 
                  className="ml-1 text-blue-600 hover:text-blue-800 font-bold"
                >
                  {t('phone_recharge.messages.contact_support')}
                </a>
              </p>
            </div>
          </div>
        )}
        
        <div className="payment-info bg-white p-4 rounded-lg border mb-4">
          <div className="flex items-center mb-4">
            <img src={config.assets.logo.url} alt="Logo FamilyMart" className="h-10 mr-3" />
            <h3 className="text-lg font-semibold">{t('payment.methods.0.name')}</h3>
          </div>
          
          {/* Display payment details */}
          <div className="mb-4 p-3 bg-blue-50 rounded-md">
            <p className="font-medium">Mã thanh toán: <strong>{paymentCode}</strong></p>
            <p className="font-medium">Ngày hết hạn: <strong>{formatExpiryDate(paymentExpiry)}</strong></p>
            <p className="font-medium">Số tiền: <strong>{totalWithFee} {currency}</strong></p>
          </div>
          
          {/* Barcode Display Component */}
          <FamilyMartBarcodeDisplay
            paymentBarcode={paymentBarcode}
            orderId={orderId}
            totalWithFee={totalWithFee}
            currency={currency}
            paymentCode={paymentCode}
            paymentExpiry={paymentExpiry}
            onSaveAndCopy={(barcodeData) => {
              logPayment("Barcode data saved and copied", barcodeData);
            }}
            className="mb-4"
          />
          
          <div className="mb-4">
            <h4 className="font-medium mb-2">{t('payment_instructions.title')}</h4>
            <div className="bg-yellow-50 p-2 rounded-md mb-3 border border-yellow-200">
              <p className="font-medium text-yellow-800">{t('payment_instructions.fee_notice')}: {PAYMENT_FEE} {currency}</p>
            </div>
            <ol className="list-decimal pl-5 space-y-2 text-sm">
              {config.payment.methods[0].steps.vi.map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ol>
          </div>
          
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <p className="text-sm font-medium text-yellow-800">
              {t('payment_instructions.expiry_notice').replace('{hours}', config.payment.limits.expiry_hours)}
            </p>
          </div>
        </div>
        
        <div className="mt-6">
          <p className="text-gray-600 mb-3">
            {t('thank_you.processing_notice')}
          </p>
          <div className="flex flex-wrap gap-3">
            <Link 
              href={`/${shopId}/orders`}
              className="flex items-center text-blue-600 hover:text-blue-800"
              onClick={() => {
                logPayment("View orders link clicked");
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm1 11a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {t('thank_you.view_order')}
            </Link>
            <a 
              href="https://line.me/R/ti/p/@maggroup" 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center text-green-600 hover:text-green-800"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
              </svg>
              {t('thank_you.contact_support')}
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex items-center mb-2">
          <Image 
            src={config.assets.logo.url}
            width={config.assets.logo.width}
            height={config.assets.logo.height}
            alt="FamilyMart" 
            className="mr-2" 
          />
          <h2 className="text-xl font-bold">{t('payment.methods.0.name')} - {currency}</h2>
        </div>
        
        <div className="bg-white p-4 rounded-md border border-blue-100 mb-4">
          <h3 className="font-medium text-lg mb-2">{t('payment_instructions.fee_notice')}: {PAYMENT_FEE} {currency}</h3>
          <p className="text-sm">
            {t('payment.methods.0.description')}.
            {t('payment_instructions.fee_notice')} {PAYMENT_FEE} {currency}. 
            {t('payment_instructions.expiry_notice').replace('{hours}', config.payment.limits.expiry_hours)}
          </p>
        </div>
        
        <div className="mt-3 bg-white p-3 rounded-md border border-blue-100">
          <h4 className="font-bold mb-1">{t('payment_instructions.title')}:</h4>
          <ol className="list-decimal ml-5 text-sm">
            {config.payment.methods[0].steps.vi.map((step, index) => (
              <li key={index} className="mb-1">{step}</li>
            ))}
          </ol>
          <div className="mt-3 text-sm bg-yellow-50 p-2 rounded-md">
            <p className="font-medium text-yellow-800">
              {t('payment_instructions.fee_notice')} {PAYMENT_FEE} {currency} {t('order_summary.payment_fee')}.
            </p>
          </div>
        </div>
      </div>

      {isUserAuthenticated && userInfo ? (
        <div className="border rounded-md p-4 bg-white mb-6">
          <h3 className="text-xl font-bold mb-4">Thông tin thanh toán của bạn</h3>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <p className="text-blue-700">Chúng tôi sẽ sử dụng thông tin từ tài khoản của bạn để xử lý thanh toán này</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6 mb-6">
            <div>
              <p className="text-sm text-gray-500 mb-1">Họ và tên</p>
              <p className="font-medium">{userInfo.name}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-1">Email</p>
              <p className="font-medium">{userInfo.email || "Chưa cung cấp"}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-1">Số điện thoại</p>
              <p className="font-medium">{userInfo.phone || "Chưa cung cấp"}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-1">Địa chỉ</p>
              <p className="font-medium">{userInfo.address || "Chưa cung cấp"}</p>
            </div>
          </div>
          
          <div className="border-t mt-6 pt-4 mb-4">
            <h4 className="font-medium mb-2">Tóm tắt đơn hàng</h4>
            <div className="space-y-2">
              <div className="flex justify-between"><span>Mã đơn hàng:</span> <span className="font-medium">{orderId}</span></div>
              <div className="flex justify-between"><span>Tổng đơn hàng:</span> <span>{baseAmount?.toLocaleString()} {currency}</span></div>
              <div className="flex justify-between"><span>Phí giao dịch:</span> <span className="text-orange-600">{PAYMENT_FEE.toLocaleString()} {currency}</span></div>
              <hr className="my-2"/>
              <div className="flex justify-between font-bold"><span>Tổng thanh toán:</span> <span>{totalWithFee?.toLocaleString()} {currency}</span></div>
            </div>
          </div>
          
          {errorMessage && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {errorMessage}
            </div>
          )}
          
          <div>
            <Button
              onClick={handleFormSubmit}
              title={isSubmitting ? "Đang xử lý..." : "Xác nhận và Thanh toán"}
              disabled={isSubmitting || totalWithFee <= 0}
              className="w-full"
            />
          </div>
        </div>
      ) : (
        <form onSubmit={handleFormSubmit}>
          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-4">Thông tin cá nhân</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">Họ</label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={firstName}
                  onChange={handleInputChange(setFirstName)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required={!fullName}
                />
              </div>
              
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">Tên</label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={lastName}
                  onChange={handleInputChange(setLastName)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required={!fullName}
                />
              </div>
            </div>
            
            <div className="mb-4">
              <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">Họ và tên đầy đủ</label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={fullName}
                onChange={handleInputChange(setFullName)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Hoặc nhập họ tên đầy đủ tại đây"
                required={!firstName && !lastName}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={email}
                  onChange={handleInputChange(setEmail)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Số điện thoại</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={phone}
                  onChange={handleInputChange(setPhone)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
            </div>
            
            {/* FamilyMart Specific Fields */}
            {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 bg-blue-50 p-3 rounded-lg">
              <div className="md:col-span-2">
                <h4 className="font-medium text-blue-800 mb-2">Thông tin FamilyMart</h4>
              </div>
              <div>
                <label htmlFor="familyMartStore" className="block text-sm font-medium text-gray-700 mb-1">Cửa hàng FamilyMart</label>
                <input
                  type="text"
                  id="familyMartStore"
                  name="familyMartStore"
                  value={familyMartStore}
                  onChange={handleInputChange(setFamilyMartStore)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Cửa hàng bạn dự định thanh toán</p>
              </div>
              <div>
                <label htmlFor="storeCode" className="block text-sm font-medium text-gray-700 mb-1">Mã cửa hàng</label>
                <input
                  type="text"
                  id="storeCode"
                  name="storeCode"
                  value={storeCode}
                  onChange={handleInputChange(setStoreCode)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Mã định danh của cửa hàng</p>
              </div>
            </div> */}
            
            <div className="mb-4">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">Địa chỉ</label>
              <textarea
                id="address"
                name="address"
                value={address}
                onChange={handleInputChange(setAddress)}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows="2"
              ></textarea>
            </div>

            {/* Phone recharge section - only show if products are phone cards */}
            {isPhoneRecharge && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <h4 className="font-bold mb-3">Thông tin nạp thẻ điện thoại</h4>
                <div className="mb-3">
                  <label className="block mb-1">Số điện thoại nạp tiền {!autoRecharge && "(Nạp thủ công)"}</label>
                  <input
                    type="tel"
                    id="rechargePhone"
                    name="rechargePhone"
                    value={rechargePhone}
                    onChange={handleInputChange(setRechargePhone)}
                    placeholder={phone}
                    className="w-full p-2 border rounded"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {isExtension 
                      ? "Gia hạn" 
                      : "Nạp tiền"}
                  </p>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="autoRecharge"
                    name="autoRecharge"
                    checked={autoRecharge}
                    onChange={(e) => setAutoRecharge(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="autoRecharge">
                    Tự động nạp tiền
                  </label>
                </div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <h3 className="text-xl font-bold mb-2">{t('order_summary.title')} ({currency})</h3>
            <div className="border rounded-md p-4">
              <div className="flex justify-between mb-2">
                <span>{t('order_summary.order_id')}:</span>
                <span>{orderId}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span>{t('order_summary.subtotal')}:</span>
                <span>{baseAmount?.toLocaleString()} {currency}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span>{t('order_summary.payment_fee')}:</span>
                <span className="text-orange-600 font-medium">{PAYMENT_FEE.toLocaleString()} {currency}</span>
              </div>
              <div className="flex justify-between font-bold">
                <span>{t('order_summary.total')}:</span>
                <span>{totalWithFee?.toLocaleString()} {currency}</span>
              </div>
            </div>
          </div>

          {errorMessage && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {errorMessage}
            </div>
          )}

          <Button
            onClick={handleFormSubmit}
            title={isSubmitting ? "Đang xử lý..." : "Xác nhận và Thanh toán"}
            disabled={isSubmitting}
            className="w-full"
          />
        </form>
      )}
    </div>
  );
};

export default FamilyMartPaymentForm; 