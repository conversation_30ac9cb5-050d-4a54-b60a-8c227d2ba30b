import React, { useRef, useEffect, useState } from 'react';
import JsBarcode from 'jsbarcode';
import config from './config.json';

const FamilyMartBarcodeDisplay = ({
  paymentBarcode,
  orderId,
  totalWithFee,
  currency,
  paymentCode,
  paymentExpiry,
  onSaveAndCopy,
  showDownloadButton = true,
  className = ""
}) => {
  const barcode1Ref = useRef(null);
  const barcode2Ref = useRef(null);
  const barcode3Ref = useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Format the barcode data string - split comma separated values
  const formatBarcodeData = (barcodeString) => {
    if (!barcodeString) return ['', '', ''];
    return barcodeString.split(',').map(code => code.trim());
  };

  // Format expiry date
  const formatExpiryDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Parse barcode data when payment is processed
  useEffect(() => {
    if (paymentBarcode) {
      // Dynamic barcode options based on screen size
      const barcodeOptions = {
        format: "CODE128",
        lineColor: "#000",
        width: isMobile ? 1.5 : 2, // Smaller width on mobile
        height: isMobile ? 60 : 80, // Smaller height on mobile
        displayValue: true,
        margin: isMobile ? 5 : 10, // Smaller margin on mobile
        fontSize: isMobile ? 12 : 14, // Smaller font on mobile
      };

      console.log("Rendering barcodes", { paymentBarcode, orderId });
      try {
        const [barcode1, barcode2, barcode3] = formatBarcodeData(paymentBarcode);
        
        if (barcode1 && barcode1Ref.current) {
          JsBarcode(barcode1Ref.current, barcode1, barcodeOptions);
        } else if (barcode1Ref.current) {
          barcode1Ref.current.innerHTML = '<p class="text-red-500 text-xs">Barcode 1 data missing</p>';
        }
        
        if (barcode2 && barcode2Ref.current) {
          JsBarcode(barcode2Ref.current, barcode2, barcodeOptions);
        } else if (barcode2Ref.current) {
          barcode2Ref.current.innerHTML = '<p class="text-red-500 text-xs">Barcode 2 data missing</p>';
        }
        
        if (barcode3 && barcode3Ref.current) {
          JsBarcode(barcode3Ref.current, barcode3, barcodeOptions);
        } else if (barcode3Ref.current) {
          barcode3Ref.current.innerHTML = '<p class="text-red-500 text-xs">Barcode 3 data missing</p>';
        }
      } catch (e) {
        console.error("JsBarcode rendering failed:", e);
        // Show a fallback message if barcode rendering fails
        if (barcode1Ref.current) {
          barcode1Ref.current.innerHTML = '<p class="text-red-500 text-xs">Hiển thị mã vạch thất bại</p>';
        }
      }
    }
  }, [paymentBarcode, orderId, isMobile]);

  const handleSaveAndCopy = () => {
    // Save barcode info to localStorage for persistence
    const barcodeData = {
      orderId,
      paymentBarcode,
      paymentCode,
      paymentExpiry,
      totalAmount: totalWithFee,
      currency,
      savedAt: new Date().toISOString()
    };
    localStorage.setItem(`familymart_barcode_${orderId}`, JSON.stringify(barcodeData));
    
    // Copy barcode info to clipboard
    const barcodeText = [
      `Đơn hàng: ${orderId}`,
      `Mã thanh toán: ${paymentCode}`,
      `Số tiền: ${totalWithFee.toLocaleString()} ${currency}`,
      `Ngày hết hạn: ${formatExpiryDate(paymentExpiry)}`,
      `Mã vạch: ${paymentBarcode}`
    ].filter(Boolean).join('\n');
    
    navigator.clipboard.writeText(barcodeText).then(() => {
      alert('Đã sao chép thông tin mã vạch và lưu vào máy!');
    }).catch(() => {
      alert('Đã lưu thông tin mã vạch vào máy!');
    });

    // Call custom callback if provided
    if (onSaveAndCopy) {
      onSaveAndCopy(barcodeData);
    }
  };

  const handleDownload = () => {
    try {
      // Create a canvas to combine all barcodes
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size
      canvas.width = 600;
      canvas.height = 800;
      
      // Fill background with white
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Add title
      ctx.font = 'bold 20px Arial';
      ctx.fillStyle = 'black';
      ctx.textAlign = 'center';
      ctx.fillText('Mã vạch thanh toán FamilyMart', canvas.width/2, 40);
      
      // Get barcode data from payment information
      const barcodes = formatBarcodeData(paymentBarcode);
      
      if (!barcodes || barcodes.length === 0) {
        throw new Error("Không tìm thấy dữ liệu mã vạch hợp lệ");
      }
      
      // Use JsBarcode directly on temporary canvas elements
      const generateBarcodeImage = (barcodeValue, index) => {
        return new Promise((resolve) => {
          try {
            if (!barcodeValue) {
              resolve(null);
              return;
            }
            
            // Create a temporary canvas for this barcode
            const tempCanvas = document.createElement('canvas');
            
            // Generate barcode directly on canvas
            JsBarcode(tempCanvas, barcodeValue, {
              format: "CODE128",
              lineColor: "#000",
              width: 2,
              height: 80,
              displayValue: true,
              margin: 10
            });
            
            // Use the canvas directly
            resolve({
              canvas: tempCanvas,
              index,
              value: barcodeValue
            });
          } catch (e) {
            console.error(`Error generating barcode ${index}:`, e);
            resolve(null);
          }
        });
      };
      
      // Generate all barcodes in parallel
      const barcodePromises = barcodes.map((value, index) => 
        generateBarcodeImage(value, index)
      );
      
      Promise.all(barcodePromises).then(results => {
        // Filter out failed barcode generations
        const validBarcodes = results.filter(result => result !== null);
        
        if (validBarcodes.length === 0) {
          throw new Error("Không thể tạo hình ảnh mã vạch");
        }
        
        let yOffset = 80;
        
        // Draw each barcode on the main canvas
        validBarcodes.forEach(({canvas: barcodeCanvas, index, value}) => {
          // Draw label
          ctx.font = 'bold 16px Arial';
          ctx.fillStyle = 'black';
          ctx.textAlign = 'center';
          ctx.fillText(`Mã vạch ${index + 1}:`, canvas.width/2, yOffset);
          
          // Calculate dimensions to maintain aspect ratio but fit width
          const scale = Math.min(canvas.width * 0.8 / barcodeCanvas.width, 1);
          const width = barcodeCanvas.width * scale;
          const height = barcodeCanvas.height * scale;
          
          // Draw barcode
          ctx.drawImage(barcodeCanvas, (canvas.width - width) / 2, yOffset + 20, width, height);
          
          // Add barcode value text
          ctx.font = '14px Arial';
          ctx.fillText(value, canvas.width/2, yOffset + height + 40);
          
          yOffset += height + 80;
        });
        
        // Add expiry and amount info
        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = 'red';
        ctx.fillText(`Mã vạch chỉ có hiệu lực trong ${config.payment.limits.expiry_hours / 24 || 7} ngày!`, canvas.width/2, yOffset + 20);
        
        // Ensure orderId is treated as a string - show full order ID
        const orderIdStr = String(orderId);
        ctx.font = '14px Arial';
        ctx.fillStyle = 'black';
        ctx.fillText(`${orderIdStr}`, canvas.width/2, yOffset + 60);
        ctx.fillText(`${totalWithFee.toLocaleString()} ${currency}`, canvas.width/2, yOffset + 80);
        
        // Generate download
        const link = document.createElement('a');
        link.download = `familymart-payment-${orderIdStr}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
      }).catch(error => {
        console.error("Error processing barcodes:", error);
        alert("Không thể tạo hình ảnh mã vạch. Vui lòng thử lại.");
      });
    } catch (error) {
      console.error("Error creating downloadable barcode image:", error);
      alert("Không thể tạo hình ảnh mã vạch. Vui lòng thử lại.");
    }
  };

  if (!paymentBarcode) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg p-3 sm:p-5 border border-gray-200 ${className}`}>
      <div className="text-center">
        <div className="flex items-center justify-center mb-4 sm:mb-6">
          <img src={config.assets.logo.url} alt="Logo FamilyMart" className="h-8 sm:h-10 mr-3" />
          <h4 className="text-base sm:text-lg font-semibold">Mã vạch thanh toán</h4>
        </div>
        
        {/* Payment details */}
        <div className="mb-4 sm:mb-6 p-3 bg-blue-50 rounded-md text-sm sm:text-base">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-left">
            <div className="sm:text-gray-600 font-medium sm:font-normal">Mã thanh toán:</div>
            <div className="font-medium break-all sm:text-right">{paymentCode}</div>
            <div className="sm:text-gray-600 font-medium sm:font-normal">Ngày hết hạn:</div>
            <div className="font-medium sm:text-right">{formatExpiryDate(paymentExpiry)}</div>
            <div className="sm:text-gray-600 font-medium sm:font-normal">Số tiền:</div>
            <div className="font-medium sm:text-right">{totalWithFee.toLocaleString()} {currency}</div>
          </div>
        </div>
        
        {/* Barcodes */}
        <div className="space-y-6 sm:space-y-8 mb-4 sm:mb-6">
          <div className="w-full max-w-full overflow-hidden">
            <p className="mb-2 font-medium text-sm sm:text-base">Mã vạch 1:</p>
            <div className="flex justify-center">
              <svg 
                ref={barcode1Ref} 
                className="max-w-full h-auto"
                style={{ maxWidth: '100%', height: 'auto' }}
              ></svg>
            </div>
          </div>
          
          <div className="w-full max-w-full overflow-hidden">
            <p className="mb-2 font-medium text-sm sm:text-base">Mã vạch 2:</p>
            <div className="flex justify-center">
              <svg 
                ref={barcode2Ref} 
                className="max-w-full h-auto"
                style={{ maxWidth: '100%', height: 'auto' }}
              ></svg>
            </div>
          </div>
          
          <div className="w-full max-w-full overflow-hidden">
            <p className="mb-2 font-medium text-sm sm:text-base">Mã vạch 3:</p>
            <div className="flex justify-center">
              <svg 
                ref={barcode3Ref} 
                className="max-w-full h-auto"
                style={{ maxWidth: '100%', height: 'auto' }}
              ></svg>
            </div>
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 mb-4">
          <button
            onClick={handleSaveAndCopy}
            className="flex-1 bg-blue-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            📱 Lưu & Sao chép thông tin
          </button>
          
          {showDownloadButton && (
            <button 
              onClick={handleDownload}
              className="flex-1 bg-green-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
            >
              📥 Tải xuống mã vạch
            </button>
          )}
        </div>
        
        {/* Expiry Notice */}
        <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200 mb-4">
          <p className="text-red-600 font-medium text-sm">
            Mã vạch chỉ có hiệu lực trong {config.payment.limits.expiry_hours / 24 || 7} ngày!
          </p>
        </div>
        
        {/* Order ID and Amount */}
        <div className="text-gray-600 text-sm">
          <p className="mb-1">{String(orderId)}</p>
          <p className="font-medium">{totalWithFee.toLocaleString()} {currency}</p>
        </div>
      </div>
    </div>
  );
};

export default FamilyMartBarcodeDisplay; 