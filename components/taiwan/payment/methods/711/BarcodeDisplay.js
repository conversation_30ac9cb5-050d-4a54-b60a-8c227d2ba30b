import React, { useRef, useEffect, useState } from 'react';
import JsBarcode from 'jsbarcode';
import styles from './SevenElevenPayment.module.css';

const BarcodeDisplay = ({
  ibonCode1,
  ibonCode2, 
  ibonCode3,
  orderId,
  totalWithFee,
  currency,
  ibonExpiryMinutes,
  setOrderCompleted,
  onSaveAndCopy,
  showCompleteButton = true,
  className = ""
}) => {
  const barcode1Ref = useRef(null);
  const barcode2Ref = useRef(null);
  const barcode3Ref = useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // useEffect to render barcodes when codes change
  useEffect(() => {
    // Dynamic barcode options based on screen size
    const barcodeOptions = {
      format: "CODE128",
      lineColor: "#000",
      width: isMobile ? 1.5 : 2, // Smaller width on mobile
      height: isMobile ? 60 : 80, // Smaller height on mobile
      displayValue: true,
      margin: isMobile ? 5 : 10, // Smaller margin on mobile
      fontSize: isMobile ? 12 : 14, // Smaller font on mobile
    };

    // Render barcodes when codes are available
    if (ibonCode1 || ibonCode2 || ibonCode3) {
      try {
        if (ibonCode1 && barcode1Ref.current) {
          JsBarcode(barcode1Ref.current, ibonCode1, barcodeOptions);
        } else if (barcode1Ref.current) {
          barcode1Ref.current.innerHTML = '<p class="text-red-500 text-xs">Barcode 1 data missing</p>';
        }
        if (ibonCode2 && barcode2Ref.current) {
          JsBarcode(barcode2Ref.current, ibonCode2, barcodeOptions);
        } else if (barcode2Ref.current) {
          barcode2Ref.current.innerHTML = '<p class="text-red-500 text-xs">Barcode 2 data missing</p>';
        }
        if (ibonCode3 && barcode3Ref.current) {
          JsBarcode(barcode3Ref.current, ibonCode3, barcodeOptions);
        } else if (barcode3Ref.current) {
          barcode3Ref.current.innerHTML = '<p class="text-red-500 text-xs">Barcode 3 data missing</p>';
        }
      } catch (e) {
        console.error("JsBarcode rendering failed:", e);
      }
    }
  }, [ibonCode1, ibonCode2, ibonCode3, isMobile]);

  const handleSaveAndCopy = () => {
    // Save barcode info to localStorage for persistence
    const barcodeData = {
      orderId,
      ibonCode1,
      ibonCode2, 
      ibonCode3,
      totalAmount: totalWithFee,
      currency,
      expiryMinutes: ibonExpiryMinutes,
      savedAt: new Date().toISOString()
    };
    localStorage.setItem(`ibon_barcode_${orderId}`, JSON.stringify(barcodeData));
    
    // Copy barcode info to clipboard
    const barcodeText = [
      `Đơn hàng: ${orderId}`,
      `Số tiền: ${totalWithFee.toLocaleString()} ${currency}`,
      ibonCode1 ? `Mã vạch 1: ${ibonCode1}` : '',
      ibonCode2 ? `Mã vạch 2: ${ibonCode2}` : '',
      ibonCode3 ? `Mã vạch 3: ${ibonCode3}` : ''
    ].filter(Boolean).join('\n');
    
    navigator.clipboard.writeText(barcodeText).then(() => {
      alert('Đã sao chép thông tin mã vạch và lưu vào máy!');
    }).catch(() => {
      alert('Đã lưu thông tin mã vạch vào máy!');
    });

    // Call custom callback if provided
    if (onSaveAndCopy) {
      onSaveAndCopy(barcodeData);
    }
  };

  const handleCompleteOrder = () => {
    if (setOrderCompleted) {
      console.log("User manually marking order as completed");
      setOrderCompleted(true);
    }
  };

  if (!ibonCode1 && !ibonCode2 && !ibonCode3) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg p-3 sm:p-4 border border-green-200 ${className}`}>
      <div className="text-center space-y-3 sm:space-y-4">
        <div className="flex flex-col items-center space-y-3 sm:space-y-4">
          <div className="w-full max-w-full overflow-hidden">
            <p className="text-sm font-medium mb-1">Mã vạch 1:</p>
            <div className="flex justify-center">
              <svg 
                ref={barcode1Ref} 
                className="max-w-full h-auto"
                style={{ maxWidth: '100%', height: 'auto' }}
              ></svg>
            </div>
          </div>
          <div className="w-full max-w-full overflow-hidden">
            <p className="text-sm font-medium mb-1">Mã vạch 2:</p>
            <div className="flex justify-center">
              <svg 
                ref={barcode2Ref} 
                className="max-w-full h-auto"
                style={{ maxWidth: '100%', height: 'auto' }}
              ></svg>
            </div>
          </div>
          <div className="w-full max-w-full overflow-hidden">
            <p className="text-sm font-medium mb-1">Mã vạch 3:</p>
            <div className="flex justify-center">
              <svg 
                ref={barcode3Ref} 
                className="max-w-full h-auto"
                style={{ maxWidth: '100%', height: 'auto' }}
              ></svg>
            </div>
          </div>
        </div>
        
        <div className="mt-3 sm:mt-4 grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
          <div className="sm:text-gray-600 font-medium sm:font-normal">Mã đơn hàng:</div>
          <div className="font-medium break-all text-left sm:text-right">{orderId}</div>
          <div className="sm:text-gray-600 font-medium sm:font-normal">Số tiền cần trả:</div>
          <div className="font-medium text-left sm:text-right">{totalWithFee?.toLocaleString()} {currency}</div>
          <div className="sm:text-gray-600 font-medium sm:font-normal">Hạn thanh toán:</div>
          <div className="font-medium text-red-600 text-left sm:text-right">{ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7} ngày</div>
        </div>
      </div>
      
      <div className="mt-3 sm:mt-4 text-center">
        <p className="text-xs text-green-600 mb-3">
          ✓ Mã vạch đã sẵn sàng để thanh toán tại 7-Eleven
        </p>
        
        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <button
            onClick={handleSaveAndCopy}
            className="flex-1 bg-blue-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            📱 Lưu & Sao chép thông tin
          </button>
          
          {showCompleteButton && (
            <button
              onClick={handleCompleteOrder}
              className="flex-1 bg-green-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
            >
              ✅ Đã lưu, hoàn tất đơn hàng
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default BarcodeDisplay; 