{"tw": {"tw711": {"card": {"merchantID": "827315300001", "merchantPassword": "@a827315300001", "linkID": "izmIFZckOoHe", "hashBase": "l9TH7sAMJsAZSJXg", "testCardNumbers": {"oneTime": ["****************", "****************"], "installment": ["****************", "3560512000000001", "3560562000000001"], "unionPay": ["6200000000000001"]}, "testUrl": "https://test.4128888card.com.tw/app", "liveUrl": "https://cocs.4128888card.com.tw", "returnURL": "https://sim.dailoanshop.net/tw/payment/card/result", "notifyURL": "https://sim.dailoanshop.net/tw/payment/card/notify", "limits": {"minAmount": 30, "maxAmount": 30000, "expiryHours": 24}, "fee": {"amount": 25, "currency": "NT$"}, "apiSpecifications": {"version": "1.24.1", "endpoints": {"payment": {"path": "/api/Collect", "method": "POST", "headers": {"Content-Type": "application/json"}}}, "requestFields": {"required": ["cust_id", "order_id", "amount", "timestamp", "link_id", "password", "customer_name", "customer_email", "customer_phone", "customer_address", "item_description", "test_mode", "return_url", "notify_url", "payment_method", "hash"], "optional": ["message"]}, "responseFields": {"success": {"resultCode": "0000", "paymentCode": "string", "expiryDate": "string", "paymentUrl": "string"}, "errorCodes": {"0001": "Invalid payment information", "0002": "Authentication error", "0003": "Invalid amount or exceeds limit"}}}, "security": {"hashAlgorithm": "sha256", "hashFormat": "${cust_id}|${data}|${hash_base}", "timeout": 30000}, "localization": {"currency": "NT$", "language": "vi", "dateFormat": {"display": "YYYY/MM/DD HH:mm", "api": "YYYYMMDDHHmm"}}}, "ibon": {"merchantID": "827315300001", "password": "W7529992P$", "apiPassword": "@a827315300001", "testUrl": "www.ccat.com.tw", "returnURL": "https://sim.dailoanshop.net/tw/payment/ibon/result", "clientBackURL": "https://sim.dailoanshop.net/tw/payment/ibon/result", "paymentInfoURL": "https://sim.dailoanshop.net/tw/payment/ibon/info", "clientRedirectURL": "https://sim.dailoanshop.net/tw/payment/ibon/redirect", "notifyURL": "https://sim.dailoanshop.net/tw/payment/ibon/notify", "customerURL": "https://sim.dailoanshop.net/tw/payment/ibon/customer", "apnURL": "https://sim.dailoanshop.net/api/payment/apn-callback", "language": "ZH-TW", "version": "2.0", "respondType": "JSON", "timeStamp": "", "merchantOrderNo": "", "amt": "", "itemDesc": "", "email": "", "no": "", "cardNo": "", "cardExpired": "", "cardCVC": "", "orderComment": "", "tokenTerm": "", "tokenTermDemand": "", "storeID": "", "storeName": "", "buyerName": "", "buyerPhone": "", "buyerEmail": "", "buyerAddr": "", "buyerZipCode": "", "encrypt": "1"}, "payment": {"methods": [{"id": "IBON", "name": {"en": "iBon Payment", "vi": "Thanh toán qua iBon"}, "description": {"en": "Pay at 7-Eleven iBon kiosk", "vi": "<PERSON>h to<PERSON> tại máy iBon của 7-Eleven"}, "steps": {"en": ["Visit any 7-Eleven convenience store in Taiwan", "Find the ibon kiosk machine (usually near the entrance)", "Select \"代碼付款\" (Payment by Code) on the touchscreen", "Enter your payment code when prompted", "Verify the order amount", "Confirm the payment details and receive a printed receipt", "Take the receipt to the cashier counter within 7 days", "Pay the cashier the amount shown on the receipt", "Keep your receipt as proof of payment"], "vi": ["<PERSON><PERSON><PERSON> b<PERSON><PERSON> kỳ cử<PERSON> hàng 7-Eleven nào tại <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (thường đặt gần cửa ra vào)", "Chọn \"代碼付款\" (<PERSON><PERSON> <PERSON><PERSON> bằng mã) trên màn hình cảm <PERSON>ng", "<PERSON><PERSON><PERSON><PERSON> mã thanh to<PERSON> khi đư<PERSON><PERSON> yêu cầu", "<PERSON><PERSON><PERSON> tra số tiền đơn hàng", "<PERSON><PERSON><PERSON> nhận thông tin thanh toán và nhận biên lai in", "<PERSON>g biên lai đến quầy thu ngân trong vòng 7 ngày", "<PERSON><PERSON> toán số tiền hiển thị trên biên lai", "<PERSON><PERSON><PERSON> biên lai làm bằng chứng thanh toán"]}}, {"id": "BARCODE", "name": {"en": "Barcode Payment", "vi": "Thanh toán bằng mã vạch"}, "description": {"en": "Pay using barcode at 7-Eleven counter", "vi": "Thanh toán bằng mã vạch tại quầy 7-Eleven"}, "steps": {"en": ["Visit any 7-Eleven convenience store in Taiwan", "Show the barcode to the cashier", "Pay the amount shown on the screen", "Keep your receipt as proof of payment"], "vi": ["<PERSON><PERSON><PERSON> b<PERSON><PERSON> kỳ cử<PERSON> hàng 7-Eleven nào tại <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> thị mã vạch cho nhân viên thu ngân", "<PERSON><PERSON> toán số tiền hiển thị trên màn hình", "<PERSON><PERSON><PERSON> biên lai làm bằng chứng thanh toán"]}}, {"id": "CARD", "name": {"en": "Credit/Debit Card", "vi": "Thẻ tín dụng/ghi nợ"}, "description": {"en": "Pay securely with credit or debit card", "vi": "<PERSON>h toán an toàn bằng thẻ tín dụng hoặc thẻ ghi nợ"}, "steps": {"en": ["Enter your card details on the secure payment page", "Complete the verification process if required (3D Secure)", "Receive instant payment confirmation", "Order will be processed immediately after successful payment"], "vi": ["<PERSON><PERSON><PERSON><PERSON> thông tin thẻ của bạn vào trang thanh toán bảo mật", "<PERSON><PERSON><PERSON> tất quy trình xác minh nếu đư<PERSON><PERSON> yêu cầu (3D Secure)", "<PERSON><PERSON><PERSON><PERSON> xác nhận thanh toán ngay lập tức", "<PERSON><PERSON><PERSON> hàng sẽ được xử lý ngay sau khi thanh toán thành công"]}, "cardTypes": ["VISA", "MASTERCARD", "JCB", "UNIONPAY"]}]}, "phoneRecharge": {"defaultAutoRecharge": true, "products": {"prepaid": ["PREPAID", "<PERSON><PERSON><PERSON> tiền điện thoại", "Phone Recharge"], "extension": ["EXTENSION", "<PERSON><PERSON> hạn đi<PERSON>n tho<PERSON>i", "Phone Extension"]}, "messages": {"title": {"en": "Phone Recharge Information", "vi": "Th<PERSON><PERSON> tin nạp tiền điện thoại"}, "product": {"en": "Product", "vi": "<PERSON><PERSON><PERSON> p<PERSON>m"}, "phoneNumber": {"en": "Phone Number", "vi": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "method": {"en": "Recharge Method", "vi": "<PERSON><PERSON><PERSON><PERSON> thứ<PERSON>"}, "autoRecharge": {"en": "Auto Recharge", "vi": "<PERSON><PERSON> động nạp"}, "manualRecharge": {"en": "Manual Recharge", "vi": "<PERSON><PERSON><PERSON> thủ công"}, "extension": {"en": "This is a phone extension service", "vi": "<PERSON><PERSON><PERSON> là dịch vụ gia hạn điện thoại"}, "recharge": {"en": "This is a phone recharge service", "vi": "<PERSON><PERSON><PERSON> là dịch vụ nạp tiền điện thoại"}, "autoRechargeDesc": {"en": "Your phone will be automatically recharged when the balance is low", "vi": "Điện thoại của bạn sẽ được tự động nạp khi số dư thấp"}, "manualRechargeDesc": {"en": "You will need to manually recharge your phone when the balance is low", "vi": "<PERSON><PERSON>n cần tự nạp tiền điện thoại khi số dư thấp"}, "supportText": {"en": "Need help? Contact our support team", "vi": "Cần hỗ trợ? <PERSON>ên hệ với đội ngũ hỗ trợ của chúng tôi"}, "contactSupport": {"en": "Contact Support", "vi": "<PERSON><PERSON><PERSON> hệ hỗ trợ"}}}, "assets": {"logo": {"url": "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/7-11-logo.webp", "width": 32, "height": 32}}, "form": {"title": {"en": "Personal Information", "vi": "Thông tin cá nhân"}, "fields": {"firstName": {"en": "First Name", "vi": "<PERSON><PERSON><PERSON>"}, "lastName": {"en": "Last Name", "vi": "Họ"}, "email": {"en": "Email", "vi": "Email"}, "phone": {"en": "Phone", "vi": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i"}, "address": {"en": "Address", "vi": "Địa chỉ"}, "message": {"en": "Message (Optional)", "vi": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>)"}}, "submit": {"en": "Complete Order", "vi": "<PERSON><PERSON><PERSON> tất đơn hàng"}, "submitting": {"en": "Processing...", "vi": "<PERSON><PERSON> xử lý..."}}, "orderSummary": {"title": {"en": "Order Summary", "vi": "<PERSON><PERSON><PERSON> tắt đơn hàng"}, "orderId": {"en": "Order ID", "vi": "<PERSON><PERSON> đơn hàng"}, "subtotal": {"en": "Subtotal", "vi": "<PERSON><PERSON><PERSON> tiền hàng"}, "paymentFee": {"en": "7-Eleven Payment Fee", "vi": "<PERSON><PERSON> toán 7-Eleven"}, "total": {"en": "Total", "vi": "<PERSON><PERSON>ng thanh toán"}}, "paymentInstructions": {"title": {"en": "Payment Instructions", "vi": "Hướng dẫn thanh toán"}, "feeNotice": {"en": "Transaction Fee", "vi": "<PERSON><PERSON> giao d<PERSON>ch"}, "expiryNotice": {"en": "Please complete your payment within {hours} hours. After this time, the payment code will expire and you'll need to place a new order.", "vi": "<PERSON>ui lòng hoàn tất thanh toán trong vòng {hours} giờ. <PERSON>u thời gian này, mã thanh toán sẽ hết hạn và bạn cần đặt đơn hàng mới."}}, "thankYou": {"title": {"en": "Thank you for your order!", "vi": "Cảm ơn bạn đã đặt hàng!"}, "orderSuccess": {"en": "Your order (#{orderId}) has been submitted successfully.", "vi": "<PERSON><PERSON><PERSON> hàng của bạn (#{orderId}) đã đ<PERSON><PERSON><PERSON> gửi thành công."}, "processingNotice": {"en": "Your order is being processed. You will receive a confirmation email shortly.", "vi": "Đơn hàng của bạn đang được xử lý. Bạn sẽ nhận được email xác nhận trong thời gian sớm nhất."}, "viewOrder": {"en": "View Your Order", "vi": "<PERSON><PERSON> đ<PERSON>n hàng của bạn"}, "contactSupport": {"en": "Contact Support", "vi": "<PERSON><PERSON><PERSON> hệ hỗ trợ"}}}}}