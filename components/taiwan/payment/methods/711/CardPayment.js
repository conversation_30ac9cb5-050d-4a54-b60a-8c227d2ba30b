import React, { useState, useEffect, useContext, useRef } from 'react';
import axios from 'axios';
import Image from '../../../../../components/Image';
import Button from '../../../../../components/Button';
import { SiteContext } from '../../../../../context/mainContext';
import Link from 'next/link';
import crypto from 'crypto';
import styles from './SevenElevenPayment.module.css'; // Reuse the existing styles
import config from './config.json';
import { useRouter } from 'next/router';
import { getCustomerId, getCustomerInfo, isCustomerAuthenticated } from '../../../../../utils/customerAuth';

// Destructure config values
const {
  tw: {
    tw711: {
      card: {
        merchantID,
        merchantPassword,
        linkID,
        hashBase,
        limits,
        fee,
        apiSpecifications,
        security,
        localization,
        testUrl,
        returnURL,
        notifyURL
      }
    }
  }
} = config;

// Credit/Debit card payment logos
const VISA_LOGO = "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Visa_Inc._logo.svg/1200px-Visa_Inc._logo.svg.png";
const MASTERCARD_LOGO = "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/1280px-Mastercard-logo.svg.png";
const JCB_LOGO = "https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/JCB_logo.svg/1280px-JCB_logo.svg.png";
const UNION_PAY_LOGO = "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/UnionPay_logo.svg/1280px-UnionPay_logo.svg.png";

// Add logging utility
const logCardPayment = (message, data = null) => {
  console.log(`[TAIWAN_7_11_CARD_PAYMENT] ${message}`, data ? JSON.stringify(data) : '');
};

// Generate a hash for 7-11 API authentication
const generateHash = (data) => {
  const message = security.hashFormat
    .replace('${cust_id}', merchantID)
    .replace('${data}', data)
    .replace('${hash_base}', hashBase);
  return crypto.createHash(security.hashAlgorithm).update(message).digest('hex');
};

// Add payment status tracking constants
const PAYMENT_STATUS = {
  INITIAL: 'initial',
  PROCESSING: 'processing',
  REDIRECT_READY: 'redirect_ready',
  ERROR: 'error'
};

// Debug helper function to force debug mode
const forceDebugMode = process.env.NODE_ENV !== 'production';

const CardPaymentForm = ({
  handleSubmit,
  shopId = '',
  orderId: initialOrderId = '',
  errorMessage: propErrorMessage,
  cart = [],
  paymentimage,
  onError,
  amount = 0,
  cartItems = [],
  setOrderCompleted,
  saveOrderToLocalStorage,
  currency = localization.currency,
}) => {
  // Get payment fee from config
  const PAYMENT_FEE = fee.amount;
  const router = useRouter();
  
  // Helper function to get localized text
  const t = (key, fallback) => {
    const keys = key.split('.');
    let value = config.tw.tw711;
    for (const k of keys) {
      value = value[k];
      if (!value) return fallback || key;
    }
    return value.vi || value.en || fallback || key;
  };
  
  logCardPayment("Initializing 7-Eleven Card Payment Form", { 
    orderId: initialOrderId,
    shopId,
    amount,
    initialOrderIdType: typeof initialOrderId
  });

  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext);
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState(contextEmail || "");
  const [phone, setPhone] = useState(contextPhone || "");
  const [address, setAddress] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderDate, setOrderDate] = useState(new Date());
  const [isOrderProcessed, setIsOrderProcessed] = useState(false);
  const [errorMessage, setErrorMessage] = useState(propErrorMessage || "");
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [processedOrderId, setProcessedOrderId] = useState(initialOrderId);
  const [redirectUrl, setRedirectUrl] = useState('');
  const [paymentResult, setPaymentResult] = useState(null);
  const [isFormReady, setIsFormReady] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState(PAYMENT_STATUS.INITIAL);
  const [debugInfo, setDebugInfo] = useState({});
  const [apiDebugInfo, setApiDebugInfo] = useState(null);
  const [showApiDebug, setShowApiDebug] = useState(false);

  // Fallback image in case main image fails to load
  const [visaImageError, setVisaImageError] = useState(false);

  // Calculate total with fee
  const baseAmount = amount || 0;
  const totalWithFee = baseAmount + PAYMENT_FEE;

  const handleInputChange = (setter) => (e) => {
    logCardPayment(`Updating form field: ${e.target.name || e.target.id}`, { 
      value: e.target.value,
    });
    setter(e.target.value);
  };

  // Login button handler
  const handleLoginClick = () => {
    // Store current page URL to redirect back after login
    localStorage.setItem('redirectAfterLogin', window.location.href);
    router.push('/login');
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage("");
    setPaymentStatus(PAYMENT_STATUS.PROCESSING);
    setApiDebugInfo(null);
    logCardPayment("Form submission started", {
      fullName,
      email,
      phone,
      currency,
      totalWithFee
    });
    
    if (isSubmitting) {
      logCardPayment("Submission already in progress, skipping");
      return;
    }

    // Validate required fields
    if (!fullName || !email || !phone) {
      const missingMsg = "Please fill in all required fields (Name, Email, Phone)";
      setErrorMessage(missingMsg);
      setPaymentStatus(PAYMENT_STATUS.ERROR);
      logCardPayment("Validation error", { error: missingMsg });
      if (onError) onError(missingMsg);
      return;
    }
    
    if (totalWithFee <= 0) {
      const invalidAmountMsg = "Invalid order amount.";
      setErrorMessage(invalidAmountMsg);
      setPaymentStatus(PAYMENT_STATUS.ERROR);
      logCardPayment("Validation error", { error: invalidAmountMsg });
      if (onError) onError(invalidAmountMsg);
      return;
    }

    setIsSubmitting(true);

    try {
      // Use the exact orderId from checkout.js with no modifications
      if (!initialOrderId) {
        throw new Error("No orderId provided from checkout.js");
      }
      
      logCardPayment("Using order ID from checkout:", { 
        orderId: initialOrderId
      });
      
      setProcessedOrderId(initialOrderId);

      // Prepare API payload
      const payload = {
        MerchantTradeNo: initialOrderId,
        TotalAmount: Math.round(totalWithFee),
        TradeDesc: `Order #${initialOrderId}`.substring(0, 100),
        ItemName: cart.map(item => item.name).join('#').substring(0, 200) || "Order items",
        CustomerName: fullName.trim(),
        CustomerPhone: phone,
        CustomerEmail: email,
        PaymentMethod: "card", // Specify card payment method
        debug: forceDebugMode // Always enable debug mode in development
      };
      
      logCardPayment("Calling 7-Eleven card payment API (/api/payment/create-7-11-card-payment)", { payload });
      const response = await axios.post('/api/payment/create-7-11-card-payment', payload);
      
      logCardPayment("7-Eleven Card API response received", response.data);
      
      // Store debug info if available
      if (response.data.debug) {
        setApiDebugInfo(response.data.debug);
        setShowApiDebug(true);
      }
      
      if (response.data && response.data.success) {
        const paymentData = response.data.data;

        // Set redirect URL for the card payment page
        setRedirectUrl(paymentData.paymentUrl || '');
        setDebugInfo({
          requestTime: new Date().toISOString(),
          paymentUrl: paymentData.paymentUrl,
          shortUrl: paymentData.shortUrl,
          expireDate: paymentData.expireDate,
          amount: paymentData.billAmount
        });
        
        logCardPayment("Payment URL received", { 
          paymentUrl: paymentData.paymentUrl,
          shortUrl: paymentData.shortUrl || 'N/A',
          expireDate: paymentData.expireDate
        });

        setPaymentStatus(PAYMENT_STATUS.REDIRECT_READY);

        // Prepare the order data for our system backend
        const orderDataForBackend = {
          orderId: initialOrderId,
          storeId: shopId,
          paymentMethod: "convenience_store",
          paymentSubMethod: "seven_eleven_card",
          currency: currency,
          customerInfo: { fullName, email, phone, address, message },
          paymentInfo: {
            paymentUrl: paymentData.paymentUrl,
            orderExpireDate: paymentData.expireDate,
            billAmount: paymentData.billAmount,
            fee: paymentData.fee,
            paymentType: 'CARD_PAYMENT',
            shortUrl: paymentData.shortUrl
          },
          items: cartItems || cart,
          totalAmount: totalWithFee,
          status: 'pending',
          date: new Date().toISOString(),
          notes: message || `${fullName} order via 7-11 card payment`
        };

        logCardPayment("Saving order data to local storage", orderDataForBackend);
        
        // Save order to local storage
        if (saveOrderToLocalStorage) {
          saveOrderToLocalStorage(orderDataForBackend);
        }

        // Set order as completed to continue the flow
        if (setOrderCompleted) {
          setOrderCompleted(true);
        }

        // Mark current form state as processed
        setIsOrderProcessed(true);

        // If we have a payment URL, redirect the user
        if (paymentData.paymentUrl) {
          // Log before redirect
          logCardPayment("Redirecting user to payment portal", { 
            url: paymentData.paymentUrl,
            redirectTime: new Date().toISOString() 
          });
          
          // Give a small delay for analytics or any other processes to complete
          // In debug mode, don't auto-redirect to allow viewing debug info
          if (!forceDebugMode && !showApiDebug) {
            setTimeout(() => {
              window.location.href = paymentData.paymentUrl;
            }, 1000);
          }
        } else {
          // Log missing URL issue
          logCardPayment("Payment URL is missing from API response", paymentData);
          setErrorMessage("Unable to get payment URL. Please contact support.");
          setPaymentStatus(PAYMENT_STATUS.ERROR);
        }
      } else {
        // Handle error case
        const errorMsg = response.data?.error || "Unable to process card payment. Please try again.";
        logCardPayment("API returned error", { 
          error: errorMsg, 
          response: response.data 
        });
        setErrorMessage(errorMsg);
        setPaymentStatus(PAYMENT_STATUS.ERROR);
        if (onError) onError(errorMsg);
        setIsSubmitting(false);
      }
    } catch (error) {
      logCardPayment("Payment API error", { 
        message: error.message, 
        response: error.response?.data 
      });
      
      const errorMsg = error.response?.data?.error || 
                      "Unable to connect to payment service. Please try again later.";
      
      // Store debug info if available in the error response
      if (error.response?.data?.debug) {
        setApiDebugInfo(error.response.data.debug);
        setShowApiDebug(true);
      }
      
      setErrorMessage(errorMsg);
      setPaymentStatus(PAYMENT_STATUS.ERROR);
      if (onError) onError(errorMsg);
      setIsSubmitting(false);
    }
  };

  // Check if user is authenticated on component mount
  useEffect(() => {
    const checkAuth = async () => {
      const isAuth = await isCustomerAuthenticated();
      setIsUserAuthenticated(isAuth);
      
      if (isAuth) {
        const custInfo = await getCustomerInfo();
        setUserInfo(custInfo);
        
        // Pre-fill form with user info
        if (custInfo) {
          if (custInfo.name) setFullName(custInfo.name);
          if (custInfo.email) setEmail(custInfo.email);
          if (custInfo.phone) setPhone(custInfo.phone);
          if (custInfo.address) setAddress(custInfo.address);
          
          // Set form as ready to trigger auto-submission
          setIsFormReady(true);
        }
      }
    };
    
    checkAuth();
  }, []);

  // Render debug information panel
  const renderDebugPanel = () => {
    if (!apiDebugInfo) return null;
    
    return (
      <div className="mt-6 border border-gray-200 rounded-lg p-4 bg-gray-50">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-bold text-red-600">API Debug Information</h3>
          <button 
            onClick={() => setShowApiDebug(!showApiDebug)}
            className="text-sm bg-gray-200 px-2 py-1 rounded hover:bg-gray-300"
          >
            {showApiDebug ? 'Hide Details' : 'Show Details'}
          </button>
        </div>
        
        {showApiDebug && (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-1">API Status</h4>
              <div className={`px-3 py-1 inline-block rounded-full text-sm ${
                apiDebugInfo.status === 'SUCCESS' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {apiDebugInfo.status || 'UNKNOWN'}
              </div>
            </div>
            
            {apiDebugInfo.error && (
              <div>
                <h4 className="font-medium mb-1 text-red-600">Error Information</h4>
                <div className="bg-red-50 p-3 rounded border border-red-200">
                  <p><span className="font-medium">Type:</span> {apiDebugInfo.error.type}</p>
                  <p><span className="font-medium">Message:</span> {apiDebugInfo.error.message}</p>
                  {apiDebugInfo.error.code && (
                    <p><span className="font-medium">Code:</span> {apiDebugInfo.error.code}</p>
                  )}
                </div>
              </div>
            )}
            
            <div>
              <h4 className="font-medium mb-1">API Endpoint</h4>
              <div className="bg-gray-100 p-2 rounded text-xs font-mono overflow-auto">
                {apiDebugInfo.apiDetails?.fullEndpoint || 'Not available'}
              </div>
            </div>
            
            {/* Rest of the original debug panel */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-1">Environment</h4>
                <p>{apiDebugInfo.apiDetails?.environment || 'Not available'}</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">Timestamp</h4>
                <p>{apiDebugInfo.requestTime || 'Not available'}</p>
              </div>
            </div>
            
            {apiDebugInfo.config && (
              <div>
                <h4 className="font-medium mb-1">Configuration</h4>
                <div className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  <pre>{JSON.stringify(apiDebugInfo.config, null, 2)}</pre>
                </div>
              </div>
            )}
            
            {apiDebugInfo.apiDetails?.payload && (
              <div>
                <h4 className="font-medium mb-1">Request Payload</h4>
                <div className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  <pre>{JSON.stringify(apiDebugInfo.apiDetails.payload, null, 2)}</pre>
                </div>
              </div>
            )}
            
            {apiDebugInfo.apiResponse && (
              <div>
                <h4 className="font-medium mb-1">API Response</h4>
                <div className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  <pre>{JSON.stringify(apiDebugInfo.apiResponse, null, 2)}</pre>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // If order is processed, show success or pending state
  if (isOrderProcessed) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-green-100 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800">Đơn hàng đã được xử lý</h2>
          <p className="text-gray-600 mt-2">
            Bạn sẽ được chuyển hướng đến trang thanh toán thẻ để hoàn tất giao dịch của mình.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mt-4">
            <p className="text-blue-800 font-medium text-sm">
              Trạng thái: {paymentStatus === PAYMENT_STATUS.REDIRECT_READY ? 'Sẵn sàng chuyển hướng' : 'Đang xử lý'}
            </p>
          </div>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg mt-4 mb-6">
          <h3 className="font-medium">Chi tiết đơn hàng:</h3>
          <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
            <div className="text-gray-600">Mã đơn hàng:</div>
            <div className="font-medium">{processedOrderId}</div>
            <div className="text-gray-600">Ngày đặt hàng:</div>
            <div className="font-medium">{orderDate.toLocaleString()}</div>
            <div className="text-gray-600">Tổng thanh toán:</div>
            <div className="font-medium">{totalWithFee?.toLocaleString()} {currency}</div>
          </div>
        </div>

        {redirectUrl && (
          <div className="text-center">
            <div className="bg-gray-100 p-2 rounded-md text-xs mb-4 overflow-hidden break-all">
              <p className="font-medium mb-1">Payment URL:</p>
              <p className="text-blue-600">{redirectUrl}</p>
            </div>
            <p className="mb-4">Nếu bạn không được chuyển hướng tự động, vui lòng nhấn nút bên dưới:</p>
            <a
              href={redirectUrl}
              className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 transition-colors inline-block"
              target="_self"
              onClick={() => logCardPayment("Manual redirect button clicked", { redirectUrl })}
            >
              Đến trang thanh toán
            </a>
          </div>
        )}

        {/* Render API debug panel */}
        {renderDebugPanel()}

        {/* Debug information panel - only visible in development */}
        {process.env.NODE_ENV !== 'production' && (
          <div className="mt-6 border border-gray-300 rounded-md p-3">
            <details>
              <summary className="font-medium text-sm cursor-pointer">Order Debug Information</summary>
              <div className="mt-2 text-xs bg-gray-50 p-2 rounded overflow-auto">
                <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
              </div>
            </details>
          </div>
        )}
      </div>
    );
  }
  
  // If user is authenticated, show loading while the form auto-submits
  if (isUserAuthenticated && userInfo) {
    return (
      <div className="border rounded-lg shadow-sm p-6 mb-6 bg-white">
        <h3 className="text-xl font-semibold mb-4 text-gray-800">Thông tin thanh toán của bạn</h3>
        
        <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
          <div className="flex items-center mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-blue-800 font-medium">Chúng tôi sẽ sử dụng thông tin từ tài khoản của bạn để xử lý thanh toán này</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6 mb-6">
          <div>
            <p className="text-sm text-gray-500 mb-1">Họ và tên</p>
            <p className="font-medium">{userInfo.name}</p>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-1">Email</p>
            <p className="font-medium">{userInfo.email || "Chưa cung cấp"}</p>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-1">Số điện thoại</p>
            <p className="font-medium">{userInfo.phone || "Chưa cung cấp"}</p>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-1">Địa chỉ</p>
            <p className="font-medium">{userInfo.address || "Chưa cung cấp"}</p>
          </div>
        </div>
        
        <div className="border-t pt-4 mb-4">
          <h4 className="font-medium mb-2">Tóm tắt đơn hàng</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between"><span>Mã đơn hàng:</span> <span className="font-medium">{initialOrderId}</span></div>
            <div className="flex justify-between"><span>Tổng đơn hàng:</span> <span>{baseAmount?.toLocaleString()} {currency}</span></div>
            <div className="flex justify-between"><span>Phí giao dịch:</span> <span className="text-orange-600">{PAYMENT_FEE.toLocaleString()} {currency}</span></div>
            <hr className="my-2"/>
            <div className="flex justify-between font-bold text-base"><span>Tổng thanh toán:</span> <span>{totalWithFee?.toLocaleString()} {currency}</span></div>
          </div>
        </div>
        
        {errorMessage && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
            {errorMessage}
          </div>
        )}
        
        <div className="flex flex-col space-y-3">
          <Button
            onClick={handleFormSubmit}
            title={isSubmitting ? "Đang xử lý..." : "Xác nhận và Thanh toán"}
            disabled={isSubmitting || totalWithFee <= 0}
            className="w-full text-lg py-3"
          />
          
          {/* <button 
            type="button"
            onClick={() => setIsUserAuthenticated(false)}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            Sửa đổi thông tin thanh toán
          </button> */}
        </div>
      </div>
    );
  }

  // Default form view for non-authenticated users
  return (
    <div className="w-full">
      <form onSubmit={handleFormSubmit} className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
          <div className="flex items-center mb-3">
            <div className="flex space-x-2 mr-3">
              <div className="h-8 w-10 bg-white rounded-md border flex items-center justify-center p-1">
                <img 
                  src={VISA_LOGO} 
                  alt="Visa" 
                  className="max-h-full max-w-full object-contain"
                  onError={() => setVisaImageError(true)}
                />
              </div>
              <div className="h-8 w-10 bg-white rounded-md border flex items-center justify-center p-1">
                <img 
                  src={MASTERCARD_LOGO} 
                  alt="Mastercard" 
                  className="max-h-full max-w-full object-contain"
                />
              </div>
              <div className="h-8 w-10 bg-white rounded-md border flex items-center justify-center p-1">
                <img 
                  src={JCB_LOGO} 
                  alt="JCB" 
                  className="max-h-full max-w-full object-contain"
                />
              </div>
            </div>
            <h3 className="text-lg font-bold">Thanh toán bằng thẻ tín dụng/ghi nợ</h3>
          </div>
          <p className="text-sm mb-3">
            Phương thức này cho phép bạn thanh toán an toàn bằng thẻ tín dụng hoặc thẻ ghi nợ.
            Giao dịch được bảo mật và xử lý qua cổng thanh toán của 7-Eleven Taiwan.
          </p>
          <div className="text-sm mt-2">
            <span className="font-medium">Phí giao dịch:</span> {PAYMENT_FEE} {currency}
          </div>
        </div>

        {errorMessage && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{errorMessage}</p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-yellow-50 border border-yellow-100 rounded-md p-4 mb-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Đã có tài khoản?</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Đăng nhập để sử dụng thông tin tài khoản của bạn và theo dõi đơn hàng dễ dàng hơn.</p>
              </div>
              <div className="mt-3">
                <button
                  type="button"
                  onClick={handleLoginClick}
                  className="inline-flex items-center px-3 py-1.5 border border-yellow-300 shadow-sm text-sm leading-4 font-medium rounded-md text-yellow-700 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  Đăng nhập
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="border rounded-lg p-4 mt-4">
          <h3 className="font-medium mb-2">Chi tiết thanh toán</h3>
          <div className="flex justify-between mb-2">
            <span>Tổng đơn hàng:</span>
            <span>{baseAmount?.toLocaleString()} {currency}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span>Phí thanh toán:</span>
            <span>{PAYMENT_FEE} {currency}</span>
          </div>
          <div className="flex justify-between font-bold">
            <span>Tổng thanh toán:</span>
            <span>{totalWithFee?.toLocaleString()} {currency}</span>
          </div>
        </div>

        <div className="space-y-2 mt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              isSubmitting ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang xử lý...
              </>
            ) : (
              "Xác nhận thanh toán"
            )}
          </button>
          <p className="text-xs text-gray-500 text-center">
            Bằng cách nhấn vào nút "Xác nhận thanh toán", bạn đồng ý với các điều khoản và điều kiện của chúng tôi.
          </p>
        </div>
      </form>
    </div>
  );
};

export default CardPaymentForm; 