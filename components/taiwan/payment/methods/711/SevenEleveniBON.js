import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Image from '../../../../Image';
import Button from '../../../../Button';
import { SiteContext } from '../../../../../context/mainContext';
import Link from 'next/link';
import crypto from 'crypto';
import config from './config.json';
import { useRouter } from 'next/router';
import { getCustomerId, getCustomerInfo, isCustomerAuthenticated } from '../../../../../utils/customerAuth';
import BarcodeDisplay from './BarcodeDisplay';

// Destructure config values
const {
  tw: {
    tw711: {
      card: {
        merchantID,
        merchantPassword,
        linkID,
        hashBase,
        limits,
        fee,
        apiSpecifications,
        security,
        localization
      },
      ibon: {
        merchantID: ibonMerchantID,
        hashKey,
        hashIV,
        returnURL,
        clientBackURL,
        paymentInfoURL,
        clientRedirectURL,
        notifyURL,
        customerURL,
        language,
        version,
        respondType
      },
      payment: { methods },
      phoneRecharge,
      assets,
      form,
      orderSummary,
      paymentInstructions,
      thankYou
    }
  }
} = config;

// Add enhanced logging utility
const logPayment = (message, data = null) => {
  const timestamp = new Date().toISOString();
  const prefix = `[TAIWAN_7_11_PAYMENT][${timestamp}]`;
  if (data) {
    try {
      console.log(`${prefix} ${message}`, typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
    } catch (e) {
      console.log(`${prefix} ${message} (Data too large to stringify)`);
      console.log(data);
    }
  } else {
    console.log(`${prefix} ${message}`);
  }
};

// Function to remove diacritics (accents) from text
const removeDiacritics = (text) => {
  if (!text) return '';
  
  // Normalize the string and replace diacritics
  return text.normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove combining diacritical marks
    .replace(/[^\x00-\x7F]/g, ''); // Remove any remaining non-ASCII characters
};

// Generate a hash for 7-11 API authentication
const generateHash = (data) => {
  const message = security.hashFormat
    .replace('${cust_id}', merchantID)
    .replace('${data}', data)
    .replace('${hash_base}', hashBase);
  return crypto.createHash(security.hashAlgorithm).update(message).digest('hex');
};

const formatCurrency = (amount, currency) => {
  return `${currency} ${amount?.toLocaleString()}`;
};

const SevenEleveniBON = ({
  handleSubmit,
  shopId = '',
  orderId: initialOrderId = '',
  errorMessage: propErrorMessage,
  cart = [],
  paymentimage,
  onError,
  amount = 0,
  cartItems = [],
  setOrderCompleted,
  saveOrderToLocalStorage,
  currency = localization.currency,
}) => {
  // Get payment fee from config
  const PAYMENT_FEE = fee.amount;
  const router = useRouter();
  
  // Helper function to get localized text
  const t = (key, fallback) => {
    const keys = key.split('.');
    let value = config.tw.tw711;
    for (const k of keys) {
      value = value[k];
      if (!value) return fallback || key;
    }
    return value.vi || value.en || fallback || key;
  };
  
  logPayment("Initializing 7-Eleven Payment Form", { 
    orderId: initialOrderId,
    shopId,
    amount,
  });

  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext);
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState(contextEmail || "");
  const [phone, setPhone] = useState(contextPhone || "");
  const [address, setAddress] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderDate, setOrderDate] = useState(new Date());
  const [isOrderProcessed, setIsOrderProcessed] = useState(false);
  const [errorMessage, setErrorMessage] = useState(propErrorMessage || "");
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState(null);

  // State for the 3 ibon barcodes and related info
  const [ibonCode1, setIbonCode1] = useState("");
  const [ibonCode2, setIbonCode2] = useState("");
  const [ibonCode3, setIbonCode3] = useState("");
  const [ibonExpiryMinutes, setIbonExpiryMinutes] = useState(null);
  const [processedOrderId, setProcessedOrderId] = useState(initialOrderId);

  // Refs for barcode SVG elements - moved to BarcodeDisplay component

  // Phone recharge state (keep if feature is used)
  const [rechargePhone, setRechargePhone] = useState("");
  const [autoRecharge, setAutoRecharge] = useState(phoneRecharge.defaultAutoRecharge);

  // Get product type from cart items if available
  const getProductType = () => {
    const phoneCards = [...phoneRecharge.products.prepaid, ...phoneRecharge.products.extension];
    
    for (const item of cart) {
      // Check product name or SKU for phone card products
      const isPhoneCard = phoneCards.some(code => 
        item.name?.includes(code) || item.sku?.includes(code)
      );
      
      if (isPhoneCard) {
        return item.name || item.sku;
      }
    }
    
    return null;
  };
  
  const productType = getProductType();
  const isPhoneRecharge = productType !== null;
  const isExtension = phoneRecharge.products.extension.includes(productType);

  // Calculate total with fee
  const baseAmount = amount || 0;
  const totalWithFee = baseAmount + PAYMENT_FEE;

  const handleInputChange = (setter) => (e) => {
    logPayment(`Updating form field: ${e.target.name || e.target.id}`, { 
      value: e.target.value,
    });
    setter(e.target.value);
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage("");
    
    // Log form state and inputs
    logPayment("Form submission started - FULL FORM STATE", {
      fullName,
      email,
      phone,
      address,
      message,
      currency,
      totalAmount: baseAmount,
      fee: PAYMENT_FEE,
      totalWithFee,
      initialOrderId,
      shopId,
      cart: cart.map(item => ({ name: item.name, sku: item.sku }))
    });
    
    if (isSubmitting) {
      logPayment("Submission already in progress, skipping");
      return;
    }

    // Validate required fields
    if (!fullName || !email || !phone) {
      const missingMsg = "Vui lòng điền đầy đủ thông tin bắt buộc (Họ tên, Email, Số điện thoại)";
      setErrorMessage(missingMsg);
      if (onError) onError(missingMsg);
      return;
    }
    
    if (totalWithFee <= 0) {
      const invalidAmountMsg = "Số tiền đơn hàng không hợp lệ.";
      setErrorMessage(invalidAmountMsg);
      if (onError) onError(invalidAmountMsg);
      return;
    }

    setIsSubmitting(true);

    // Use the passed orderId and only append a suffix for uniqueness in the payment system
    // Don't create a brand new orderId
    const orderIdStr = String(initialOrderId || '');
    /* const uniqueOrderId = initialOrderId 
      ? `${orderIdStr}_${Date.now()}_${Math.floor(Math.random() * 10000)}`.substring(0, 29)
      : `MAG-${Date.now()}_${Math.floor(Math.random() * 10000)}`.substring(0, 29);
    
    setProcessedOrderId(uniqueOrderId);
     */

    const uniqueOrderId = orderIdStr;  //Son: I want to keeo the orderId from the checkout page. That was already timestamp-based.

    logPayment("Using order ID from checkout", { 
      uniqueOrderId, 
      initialOrderId,
      shortTimestamp: Date.now() % 1000000,
      randomSuffix: Math.floor(Math.random() * 10000)
    });

    // Define payload outside try block so it's available in the catch block
    const customerNameSanitized = removeDiacritics(fullName.trim());
    const itemNameSanitized = cart.map(item => removeDiacritics(item.name || '')).join('#').substring(0, 200) || "Order items";
    const tradeDescSanitized = removeDiacritics(`Order #${uniqueOrderId}`).substring(0, 100);
    
    logPayment("Sanitized field values", {
      original: {
        fullName: fullName.trim(),
        itemNames: cart.map(item => item.name)
      },
      sanitized: {
        customerName: customerNameSanitized,
        itemName: itemNameSanitized,
        tradeDesc: tradeDescSanitized
      }
    });
    
    const payload = {
      MerchantTradeNo: uniqueOrderId,
      TotalAmount: Math.round(totalWithFee),
      TradeDesc: tradeDescSanitized,
      ItemName: itemNameSanitized,
      CustomerName: customerNameSanitized, // Use sanitized name (ASCII only)
      CustomerPhone: phone,
      CustomerEmail: email,
    };

    try {
      // Prepare API payload
      logPayment("Preparing API call to /api/payment/create-7-11-payment", { 
        payload,
        url: '/api/payment/create-7-11-payment',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      // Create a configured axios instance for debugging
      const debugAxios = axios.create();
      
      // Add request interceptor
      debugAxios.interceptors.request.use(
        config => {
          logPayment("Axios request configuration", {
            url: config.url,
            method: config.method,
            headers: config.headers,
            data: config.data
          });
          return config;
        },
        error => {
          logPayment("Axios request error interceptor", { error: error.message });
          return Promise.reject(error);
        }
      );
      
      // Add response interceptor
      debugAxios.interceptors.response.use(
        response => {
          logPayment("Axios response interceptor", {
            status: response.status,
            statusText: response.statusText,
            headers: response.headers,
            data: response.data
          });
          return response;
        },
        error => {
          logPayment("Axios response error interceptor", {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            statusText: error.response?.statusText,
            headers: error.response?.headers,
            data: error.response?.data,
            request: {
              url: error.config?.url,
              method: error.config?.method,
              headers: error.config?.headers,
              data: error.config?.data
            }
          });
          return Promise.reject(error);
        }
      );
      
      const apiCallStartTime = Date.now();
      logPayment("Sending API request now...");
      
      // Use our debug axios instance instead of the global one
      const response = await debugAxios.post('/api/payment/create-7-11-payment', payload);
      
      const apiCallDuration = Date.now() - apiCallStartTime;
      logPayment(`API response received in ${apiCallDuration}ms`, {
        success: response.data?.success || false,
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });
      
      if (response.data && response.data.success && response.data.data?.barcodes) {
        const paymentData = response.data.data;
        const barcodeInfo = paymentData.barcodes;

        // Set iBON codes from the direct response
        setIbonCode1(barcodeInfo.barcode1 || '');
        setIbonCode2(barcodeInfo.barcode2 || '');
        setIbonCode3(barcodeInfo.barcode3 || '');
        setIbonExpiryMinutes(barcodeInfo.expiresInMinutes || 10);

        // Prepare the order data for our system backend
        const orderDataForBackend = {
          orderId: uniqueOrderId,
          storeId: shopId,
          paymentMethod: "convenience_store",
          paymentSubMethod: "seven_eleven_ibon",
          currency: currency,
          customerInfo: { fullName, email, phone, address, message },
          paymentInfo: {
            barcodes: barcodeInfo,
            ibonPaymentCode: paymentData.ibonPaymentCode,
            ibonShopId: paymentData.ibonShopId,
            orderExpireDate: paymentData.expireDate,
            billAmount: paymentData.billAmount,
            fee: paymentData.fee,
            paymentType: paymentData.paymentType,
            shortUrl: paymentData.shortUrl
          },
          items: cartItems || cart,
          amount: baseAmount,
          fee: PAYMENT_FEE,
          totalAmount: totalWithFee,
          orderDate: orderDate.toISOString(),
          rechargeInfo: isPhoneRecharge ? {
            productType,
            phoneNumber: rechargePhone || phone,
            autoRecharge,
            status: 'pending',
            isPhoneExtension: isExtension
          } : null,
        };

        logPayment("Submitting processed order to backend (/api/submitOrder)", { orderId: orderDataForBackend.orderId });

        // Send to our backend (assuming /api/submitOrder exists)
        try {
          const backendResponse = await axios.post("/api/submitOrder", orderDataForBackend);
          if (!backendResponse.data || !backendResponse.data.success) {
            logPayment("Warning: Backend order submission failed or returned non-success.", backendResponse.data);
          } else {
            logPayment("Backend order submission successful.", backendResponse.data);
          }
        } catch (backendError) {
          logPayment("Error submitting order to backend:", backendError.response?.data || backendError.message);
          setErrorMessage("Đã tạo mã thanh toán, nhưng không thể hoàn tất hồ sơ đơn hàng. Vui lòng lưu thông tin thanh toán hoặc liên hệ hỗ trợ.");
        }

        // Save order summary to local storage if function provided
        if (saveOrderToLocalStorage) {
          logPayment("Saving order summary to localStorage");
          saveOrderToLocalStorage({
            orderId: uniqueOrderId,
            status: 'Pending Payment',
            customerInfo: { fullName, email, phone },
            paymentMethod: "seven_eleven_ibon",
            paymentInfo: {
              store: "7-Eleven",
              paymentExpiryMinutes: ibonExpiryMinutes,
              barcodes: barcodeInfo // Include barcode info in local storage
            },
            totalAmount: totalWithFee,
            currency: currency,
          });
        }

        // Don't call setOrderCompleted immediately to allow user to see barcodes
        // The user can manually mark as completed or we can add a "Complete" button
        logPayment("Barcodes received and displayed, order ready for payment but not marked as completed");
        
        // Update payment monitor status to show barcodes are ready but not yet completed
        if (typeof window !== 'undefined' && uniqueOrderId) {
          const { PaymentMonitor } = require('../../../../../utils/checkoutProgress');
          PaymentMonitor.updatePaymentStatus(uniqueOrderId, 'BARCODES_READY', {
            barcodes: barcodeInfo,
            readyAt: new Date().toISOString()
          });
        }

      } else {
        // Error response from 7-11 API
        const apiErrorMessage = response.data?.message || response.data?.error || "Payment processing failed";
        throw new Error(`7-Eleven API Error: ${apiErrorMessage}`);
      }

    } catch (error) {
      const errorStartTime = Date.now();
      logPayment("Error during form submission - BEGINNING ERROR ANALYSIS", { 
        name: error.name,
        message: error.message,
        code: error.code,
        stack: error.stack,
        isAxiosError: error.isAxiosError
      });
      
      // Log request details if available
      if (error.config) {
        logPayment("Failed request details", {
          url: error.config.url,
          method: error.config.method,
          headers: error.config.headers,
          data: error.config.data,
          timeout: error.config.timeout
        });
      }
      
      // Log response details if available
      if (error.response) {
        logPayment("Server response to failed request", {
          status: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data
        });
        
        // Special handling for 400 status
        if (error.response.status === 400) {
          logPayment("400 Bad Request Analysis", {
            requestBody: payload,
            responseData: error.response.data,
            possibleIssues: [
              "MerchantTradeNo format issue",
              "CustomerName contains unsupported characters",
              "TotalAmount out of accepted range",
              "Invalid phone number format",
              "Email format issue",
              "Missing required fields in backend processing"
            ]
          });
        }
      }
      
      const errorLogDuration = Date.now() - errorStartTime;
      logPayment(`Error analysis completed in ${errorLogDuration}ms`);
      
      // Create user-friendly error message
      let displayError = "Đã xảy ra lỗi. Vui lòng thử lại.";
      
      if (error.response) {
        const serverMessage = error.response.data?.message || error.response.data?.error || "";
        const statusCode = error.response.status;
        
        displayError = `Lỗi (${statusCode}): ${serverMessage || error.message}`;
      }
      
      setErrorMessage(displayError);
      if (onError) {
        onError(displayError);
      }
    } finally {
      logPayment("Payment submission process complete");
      setIsSubmitting(false);
    }
  };

  // Barcode rendering logic moved to BarcodeDisplay component

  // Load customer information if logged in
  useEffect(() => {
    if (typeof window !== "undefined") {
      const authenticated = isCustomerAuthenticated();
      setIsUserAuthenticated(authenticated);
      
      if (authenticated) {
        const customerInfo = getCustomerInfo();
        setUserInfo(customerInfo);
        
        // Pre-fill form fields with customer information
        if (customerInfo) {
          if (customerInfo.name) {
            setFullName(customerInfo.name);
          }
          
          if (customerInfo.email) {
            setEmail(customerInfo.email);
          }
          
          if (customerInfo.phone) {
            setPhone(customerInfo.phone);
          }
          
          if (customerInfo.address) {
            setAddress(customerInfo.address);
          }
        }
      }
      
      // Try to restore barcode data from localStorage if available
      const orderId = initialOrderId || uniqueOrderId;
      if (orderId) {
        const savedBarcodeData = localStorage.getItem(`ibon_barcode_${orderId}`);
        if (savedBarcodeData) {
          try {
            const barcodeData = JSON.parse(savedBarcodeData);
            logPayment("Restoring barcode data from localStorage", barcodeData);
            
            // Restore barcode codes
            if (barcodeData.ibonCode1) setIbonCode1(barcodeData.ibonCode1);
            if (barcodeData.ibonCode2) setIbonCode2(barcodeData.ibonCode2);
            if (barcodeData.ibonCode3) setIbonCode3(barcodeData.ibonCode3);
            if (barcodeData.expiryMinutes) setIbonExpiryMinutes(barcodeData.expiryMinutes);
            
            logPayment("Barcode data restored successfully");
          } catch (error) {
            logPayment("Error restoring barcode data", error);
          }
        }
      }
    }
  }, [initialOrderId]);

  // Handle login click
  const handleLoginClick = () => {
    // Store current page URL for redirecting back after login
    const returnUrl = window.location.pathname;
    localStorage.setItem('paymentReturnUrl', returnUrl);
    router.push(`/${shopId}/customer/login`);
  };

  if (isOrderProcessed) {
    logPayment("Rendering Thank You / Processed Order View", { processedOrderId });
    return (
      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
        <h3 className="text-2xl font-bold text-green-800 mb-4">{t('thank_you.title', 'Đã Nhận Đơn Hàng')}</h3>
        <p className="mb-2">{t('thank_you.order_success', 'Đơn hàng {orderId} của bạn cần thanh toán.').replace('{orderId}', processedOrderId)}</p>
        <p className="mb-4 text-sm text-gray-700">Vui lòng mang mã vạch sau đến bất kỳ cửa hàng 7-Eleven nào có máy ibon trong vòng <strong>{ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7} ngày</strong> để hoàn tất thanh toán.</p>

        {errorMessage && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {errorMessage}
          </div>
        )}

        {isPhoneRecharge && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <h4 className="text-xl font-bold text-yellow-800 mb-2">{t('phone_recharge.messages.title', 'Thông Tin Nạp Điện Thoại')}</h4>
            <p>{t('phone_recharge.messages.product', 'Sản phẩm')}: <strong>{productType}</strong></p>
            <p>{t('phone_recharge.messages.phone_number', 'Số điện thoại')}: <strong>{rechargePhone || phone}</strong></p>
            <p>{t('phone_recharge.messages.method', 'Phương thức')}: <strong>{autoRecharge ? t('phone_recharge.messages.auto_recharge', 'Nạp tự động') : t('phone_recharge.messages.manual_recharge', 'Nạp thủ công')}</strong></p>
            
            <div className="mt-3">
              <p className="text-sm">
                {t('phone_recharge.messages.support_text', 'Cần trợ giúp? Liên hệ hỗ trợ:')}
                <a 
                  href={`https://line.me/R/ti/p/@maggroup`} 
                  target="_blank"
                  rel="noopener noreferrer" 
                  className="ml-1 text-blue-600 hover:text-blue-800 font-bold"
                >
                  {t('phone_recharge.messages.contact_support', 'Liên hệ hỗ trợ')}
                </a>
              </p>
            </div>
          </div>
        )}
        
        <div className="payment-info bg-white p-4 rounded-lg border mb-4">
          <div className="flex items-center mb-4">
            <img src={assets.logo.url} alt="Logo 7-Eleven" className="h-10 mr-3" />
            <h3 className="text-lg font-semibold">{t('payment.methods.0.name', 'Thanh Toán 7-Eleven iBon')}</h3>
          </div>
          
          <div className="mb-6">
            <BarcodeDisplay 
              ibonCode1={ibonCode1}
              ibonCode2={ibonCode2}
              ibonCode3={ibonCode3}
              orderId={processedOrderId}
              totalWithFee={totalWithFee}
              currency={currency}
              ibonExpiryMinutes={ibonExpiryMinutes}
              setOrderCompleted={setOrderCompleted}
            />
          </div>
          
          <div className="mb-4">
            <h4 className="font-medium mb-2">{t('payment_instructions.title', 'Hướng dẫn thanh toán')}</h4>
            <div className="bg-yellow-50 p-2 rounded-md mb-3 border border-yellow-200">
              <p className="text-sm font-medium text-yellow-800">
                {t('payment_instructions.fee_notice', 'Phí giao dịch {fee} {currency} sẽ được thêm vào.').replace('{fee}', PAYMENT_FEE.toLocaleString()).replace('{currency}', currency)}
                {' '}
                {t('payment_instructions.expiry_notice', 'Mã vạch có hiệu lực trong vòng {days} ngày.').replace('{days}', ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7)}
              </p>
            </div>
            <ol className="list-decimal pl-5 space-y-2 text-sm">
              <li>Điền thông tin của bạn bên dưới.</li>
              <li>Nhấn 'Lấy Mã Thanh Toán'.</li>
              <li>Mang mã vạch đã tạo đến máy ibon của 7-Eleven trong vòng {ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7} ngày.</li>
              <li>Làm theo hướng dẫn trên máy để in phiếu thanh toán.</li>
              <li>Thanh toán tại quầy 7-Eleven.</li>
            </ol>
          </div>
          
          <div className="bg-red-50 p-3 rounded-lg border border-red-200">
            <p className="text-sm font-medium text-red-700">
              Quan trọng: Thanh toán phải được hoàn thành trong vòng {ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7} ngày kể từ khi tạo mã vạch, nếu không mã vạch sẽ hết hạn.
            </p>
          </div>
        </div>
        
        {/* <div className="mt-6">
          <div className="flex flex-wrap gap-3 justify-center">
            <Link href={`/orders`} className="text-blue-600 hover:text-blue-800 px-4 py-2 border border-blue-500 rounded">
              {t('thank_you.view_order', 'View My Orders')}
            </Link>
            <a
              href="https://line.me/R/ti/p/@maggroup"
              target="_blank"
              rel="noopener noreferrer"
              className="text-green-600 hover:text-green-800 px-4 py-2 border border-green-500 rounded"
            >
              {t('thank_you.contact_support', 'Contact Support')}
            </a>
            <button
              onClick={() => window.print()}
              className="text-gray-700 hover:text-black px-4 py-2 border border-gray-500 rounded"
            >
              Print Instructions
            </button>
          </div>
        </div> */}
      </div>
    );
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-4">
      <div className="text-center mb-6">
        <Image
          src={assets.logo.url}
          width={assets.logo.width * 1.5}
          height={assets.logo.height * 1.5}
          alt="Thanh Toán 7-Eleven"
          className="mx-auto mb-2"
        />
        <h2 className="text-2xl font-bold">{t('payment.methods.0.name', 'Thanh Toán 7-Eleven iBon')}</h2>
        <p className="text-sm text-gray-600 mt-1">
          {t('payment.methods.0.description', 'Thanh toán an toàn tại bất kỳ máy ibon của 7-Eleven.')}
        </p>
      </div>

      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <h3 className="font-medium text-lg mb-2">{t('payment_instructions.title', 'Chỉ dẫn thanh toán 7-Eleven')}</h3>
        <ol className="list-decimal ml-5 text-sm space-y-1">
          <li>Điền thông tin của bạn bên dưới.</li>
          <li>Nhấn 'Lấy Mã Thanh Toán'.</li>
          <li>Mang mã vạch đã tạo đến máy ibon của 7-Eleven trong vòng {ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7} ngày.</li>
          <li>Làm theo hướng dẫn trên máy để in phiếu thanh toán.</li>
          <li>Thanh toán tại quầy 7-Eleven.</li>
        </ol>
        <div className="mt-3 text-sm bg-yellow-50 p-2 rounded-md border border-yellow-200">
          <p className="font-medium text-yellow-800">
            {t('payment_instructions.fee_notice', 'Phí giao dịch {fee} {currency} sẽ được thêm vào.').replace('{fee}', PAYMENT_FEE.toLocaleString()).replace('{currency}', currency)}
            {' '}
            {t('payment_instructions.expiry_notice', 'Mã vạch có hiệu lực trong vòng {days} ngày.').replace('{days}', ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7)}
          </p>
        </div>
      </div>

      {/* Login/Register prompt when not authenticated */}
      {!isUserAuthenticated && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0 md:mr-6">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">Đăng nhập để điền thông tin tự động</h3>
              <p className="text-sm text-gray-700">
                Đăng nhập để chúng tôi có thể tự động điền thông tin cá nhân của bạn và làm cho quá trình thanh toán thuận tiện hơn.
              </p>
            </div>
            <div className="flex flex-col space-y-3">
              <button
                onClick={handleLoginClick}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Đăng nhập
              </button>
              <Link href={`/${shopId}/customer/register`}>
                <button className="px-6 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 transition-colors">
                  Đăng ký
                </button>
              </Link>
            </div>
          </div>
        </div>
      )}

      {isUserAuthenticated && userInfo ? (
        <div className="border rounded-lg shadow-sm p-6 mb-6 bg-white">
          <h3 className="text-xl font-semibold mb-4 text-gray-800">Thông tin thanh toán của bạn</h3>
          
          <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
            <div className="flex items-center mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-blue-800 font-medium">Chúng tôi sẽ sử dụng thông tin từ tài khoản của bạn để xử lý thanh toán này</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6 mb-6">
            <div>
              <p className="text-sm text-gray-500 mb-1">Họ và tên</p>
              <p className="font-medium">{userInfo.name}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-1">Email</p>
              <p className="font-medium">{userInfo.email || "Chưa cung cấp"}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-1">Số điện thoại</p>
              <p className="font-medium">{userInfo.phone || "Chưa cung cấp"}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-1">Địa chỉ</p>
              <p className="font-medium">{userInfo.address || "Chưa cung cấp"}</p>
            </div>
          </div>
          
          <div className="border-t pt-4 mb-4">
            <h4 className="font-medium mb-2">Tóm tắt đơn hàng</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between"><span>Mã đơn hàng:</span> <span className="font-medium">{initialOrderId}</span></div>
              <div className="flex justify-between"><span>Tổng đơn hàng:</span> <span>{baseAmount?.toLocaleString()} {currency}</span></div>
              <div className="flex justify-between"><span>Phí giao dịch:</span> <span className="text-orange-600">{PAYMENT_FEE.toLocaleString()} {currency}</span></div>
              <hr className="my-2"/>
              <div className="flex justify-between font-bold text-base"><span>Tổng thanh toán:</span> <span>{totalWithFee?.toLocaleString()} {currency}</span></div>
            </div>
          </div>
          
          {/* Display barcodes if received but order not fully processed */}
          {(ibonCode1 || ibonCode2 || ibonCode3) && !isOrderProcessed && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-green-800 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Mã vạch đã được tạo thành công!
              </h4>
              <p className="text-sm text-green-700 mb-4">
                Vui lòng mang các mã vạch sau đến bất kỳ cửa hàng 7-Eleven nào trong vòng {ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7} ngày để hoàn tất thanh toán.
              </p>
              
              <BarcodeDisplay 
                ibonCode1={ibonCode1}
                ibonCode2={ibonCode2}
                ibonCode3={ibonCode3}
                orderId={initialOrderId}
                totalWithFee={totalWithFee}
                currency={currency}
                ibonExpiryMinutes={ibonExpiryMinutes}
                setOrderCompleted={setOrderCompleted}
                showCompleteButton={false}
              />
            </div>
          )}

          {errorMessage && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
              {errorMessage}
            </div>
          )}
          
          <div className="flex flex-col space-y-3">
            {/* Only show payment button if no barcodes exist */}
            {!(ibonCode1 || ibonCode2 || ibonCode3) && (
              <Button
                onClick={handleFormSubmit}
                title={isSubmitting ? "Đang xử lý..." : "Xác nhận và Thanh toán"}
                disabled={isSubmitting || totalWithFee <= 0}
                className="w-full text-lg py-3"
              />
            )}
            
            {/* Show completion message if barcodes exist */}
            {(ibonCode1 || ibonCode2 || ibonCode3) && (
              <div className="w-full bg-green-100 text-green-800 py-3 px-4 rounded-lg text-center font-medium">
                ✅ Mã vạch đã được tạo - Vui lòng thanh toán tại 7-Eleven
              </div>
            )}
            
            {/* <button 
              type="button"
              onClick={() => setIsUserAuthenticated(false)}
              className="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              Sửa đổi thông tin thanh toán
            </button> */}
          </div>
        </div>
      ) : (
        <form onSubmit={handleFormSubmit} className="space-y-4">
          <div>
            <h3 className="text-xl font-semibold mb-3 border-b pb-2">{t('form.title', 'Thông Tin Của Bạn')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">{t('form.fields.full_name', 'Họ và Tên')}</label>
                <input type="text" id="fullName" name="fullName" value={fullName} onChange={handleInputChange(setFullName)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm" required />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">{t('form.fields.email', 'Email')}</label>
                <input type="email" id="email" name="email" value={email} onChange={handleInputChange(setEmail)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm" required />
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">{t('form.fields.phone', 'Số Điện Thoại')}</label>
                <input type="tel" id="phone" name="phone" value={phone} onChange={handleInputChange(setPhone)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm" required />
              </div>
              <div className="md:col-span-2">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">{t('form.fields.address', 'Địa chỉ')} (Tùy chọn)</label>
                <textarea id="address" name="address" value={address} onChange={handleInputChange(setAddress)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm" rows="3"></textarea>
              </div>
              <div className="md:col-span-2">
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">{t('form.fields.message', 'Ghi chú đơn hàng (Tùy chọn)')}</label>
                <textarea id="message" name="message" value={message} onChange={handleInputChange(setMessage)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm" rows="2"></textarea>
              </div>
            </div>
          </div>

          {isPhoneRecharge && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <h4 className="font-bold mb-3">{t('phone_recharge.messages.title', 'Thông Tin Nạp Điện Thoại')}</h4>
              <div className="mb-3">
                <label className="block mb-1">{t('phone_recharge.messages.phone_number', 'Số điện thoại')}: {!autoRecharge && `(${t('phone_recharge.messages.manual_recharge', 'Nạp thủ công')})`}</label>
                <input
                  type="tel"
                  id="rechargePhone"
                  name="rechargePhone"
                  value={rechargePhone}
                  onChange={handleInputChange(setRechargePhone)}
                  placeholder={phone}
                  className="w-full p-2 border rounded-md"
                />
                <p className="text-xs text-gray-500 mt-1">
                  {isExtension 
                    ? t('phone_recharge.messages.extension', 'Số mở rộng')
                    : t('phone_recharge.messages.recharge', 'Nạp thủ công')}
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoRecharge"
                  name="autoRecharge"
                  checked={autoRecharge}
                  onChange={(e) => setAutoRecharge(e.target.checked)}
                  className="mr-2"
                />
                <label htmlFor="autoRecharge">
                  {t('phone_recharge.messages.auto_recharge', 'Tự động nạp')}
                </label>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {autoRecharge 
                  ? t('phone_recharge.messages.auto_recharge_desc', 'Tự động nạp đã được bật. Hệ thống sẽ tự động nạp cho điện thoại của bạn.')
                  : t('phone_recharge.messages.manual_recharge_desc', 'Nạp thủ công đã được bật. Bạn cần nạp thủ công cho điện thoại của mình.')}
              </p>
            </div>
          )}
          
          <div className="mt-6">
            <h3 className="text-xl font-semibold mb-3 border-b pb-2">{t('order_summary.title', 'Tóm tắt đơn hàng')}</h3>
            <div className="border rounded-md p-4 space-y-2 text-sm">
              <div className="flex justify-between"><span>{t('order_summary.order_id', 'Mã đơn hàng')}:</span> <span className="font-medium">{initialOrderId}</span></div>
              <div className="flex justify-between"><span>{t('order_summary.subtotal', 'Tổng đơn hàng')}:</span> <span>{baseAmount?.toLocaleString()} {currency}</span></div>
              <div className="flex justify-between"><span>{t('order_summary.payment_fee', 'Phí giao dịch')}:</span> <span className="text-orange-600">{PAYMENT_FEE.toLocaleString()} {currency}</span></div>
              <hr/>
              <div className="flex justify-between font-bold text-base"><span>{t('order_summary.total', 'Tổng thanh toán')}:</span> <span>{totalWithFee?.toLocaleString()} {currency}</span></div>
            </div>
          </div>

          {/* Display barcodes if received but order not fully processed */}
          {(ibonCode1 || ibonCode2 || ibonCode3) && !isOrderProcessed && (
            <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-green-800 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Mã vạch đã được tạo thành công!
              </h4>
              <p className="text-sm text-green-700 mb-4">
                Vui lòng mang các mã vạch sau đến bất kỳ cửa hàng 7-Eleven nào trong vòng {ibonExpiryMinutes ? Math.floor(ibonExpiryMinutes/1440) : 7} ngày để hoàn tất thanh toán.
              </p>
              
              <BarcodeDisplay 
                ibonCode1={ibonCode1}
                ibonCode2={ibonCode2}
                ibonCode3={ibonCode3}
                orderId={initialOrderId}
                totalWithFee={totalWithFee}
                currency={currency}
                ibonExpiryMinutes={ibonExpiryMinutes}
                setOrderCompleted={setOrderCompleted}
                showCompleteButton={false}
              />
            </div>
          )}

          {errorMessage && (
            <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
              {errorMessage}
            </div>
          )}

          <div className="mt-6">
            {/* Only show payment button if no barcodes exist */}
            {!(ibonCode1 || ibonCode2 || ibonCode3) && (
              <Button
                type="submit"
                title={isSubmitting ? t('form.submitting', 'Đang xử lý...') : t('form.submit', 'Lấy Mã Thanh Toán')}
                disabled={isSubmitting || totalWithFee <= 0}
                className="w-full text-lg py-3"
              />
            )}
            
            {/* Show completion message if barcodes exist */}
            {(ibonCode1 || ibonCode2 || ibonCode3) && (
              <div className="w-full bg-green-100 text-green-800 py-3 px-4 rounded-lg text-center font-medium">
                ✅ Mã vạch đã được tạo - Vui lòng thanh toán tại 7-Eleven
              </div>
            )}
          </div>
        </form>
      )}
    </div>
  );
};

export default SevenEleveniBON; 