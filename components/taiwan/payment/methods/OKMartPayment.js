import React from 'react';
import Image from '../../../../components/Image';

// OK Mart payment logo
const OKMART_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/ok-mart-logo.webp";

const OKMartPayment = ({ amount, currency = "NT$" }) => {
  // Payment fee
  const PAYMENT_FEE = 25;
  
  // Calculate total with fee
  const totalWithFee = (amount || 0) + PAYMENT_FEE;
  
  return (
    <div className="w-full">
      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex items-center mb-2">
          <Image 
            src={OKMART_LOGO}
            width={32} 
            height={32} 
            alt="OK Mart" 
            className="mr-2" 
          />
          <h2 className="text-xl font-bold">Thanh toán tại OK Mart - {currency}</h2>
        </div>
        
        <div className="bg-white p-4 rounded-md border border-blue-100 mb-4">
          <h3 className="font-medium text-lg mb-2">Phí giao dịch: {PAYMENT_FEE} {currency}</h3>
          <p className="text-sm">
            Thanh toán tại cửa hàng OK Mart là phương thức thanh toán tiện lợi dành cho bạn.
            Phương thức này có phí giao dịch {PAYMENT_FEE} {currency}. 
            Sau khi hoàn tất đơn hàng, bạn sẽ nhận được mã thanh toán để đem đến cửa hàng OK Mart gần nhất.
          </p>
        </div>
        
        <div className="mt-3 bg-white p-3 rounded-md border border-blue-100">
          <h4 className="font-bold mb-1">Quy trình thanh toán:</h4>
          <ol className="list-decimal ml-5 text-sm">
            <li className="mb-1">Hoàn tất đơn hàng và nhận mã thanh toán</li>
            <li className="mb-1">Đến cửa hàng OK Mart gần nhất trong vòng 7 ngày</li>
            <li className="mb-1">Cung cấp mã thanh toán hoặc mã vạch cho nhân viên</li>
            <li className="mb-1">Thanh toán bằng tiền mặt và nhận biên lai</li>
            <li>Đơn hàng sẽ được xử lý sau khi thanh toán thành công</li>
          </ol>
          <div className="mt-3 text-sm bg-yellow-50 p-2 rounded-md">
            <p className="font-medium text-yellow-800">
              Lưu ý: Phí giao dịch {PAYMENT_FEE} {currency} đã được tính vào tổng thanh toán.
            </p>
          </div>
        </div>
      </div>
      
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <h3 className="text-lg font-semibold mb-2">Sắp ra mắt</h3>
        <p>
          Phương thức thanh toán OK Mart đang trong quá trình phát triển và sẽ sớm được ra mắt.
          Vui lòng sử dụng phương thức thanh toán khác như Chuyển khoản ngân hàng, 7-Eleven, hoặc FamilyMart.
        </p>
      </div>
      
      <div className="border rounded-lg p-4 mb-4">
        <h3 className="font-medium mb-2">Chi tiết thanh toán</h3>
        <div className="flex justify-between mb-2">
          <span>Tổng đơn hàng:</span>
          <span>{amount?.toLocaleString()} {currency}</span>
        </div>
        <div className="flex justify-between mb-2">
          <span>Phí thanh toán:</span>
          <span>{PAYMENT_FEE} {currency}</span>
        </div>
        <div className="flex justify-between font-bold">
          <span>Tổng thanh toán:</span>
          <span>{totalWithFee?.toLocaleString()} {currency}</span>
        </div>
      </div>
      
      <div className="text-sm text-gray-600">
        <p className="font-medium mb-1">Lưu ý:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Mã thanh toán có hiệu lực trong 3 ngày kể từ khi đặt hàng</li>
          <li>Đơn hàng sẽ được xử lý sau khi thanh toán được xác nhận</li>
          <li>Nếu bạn cần hỗ trợ, vui lòng liên hệ với chúng tôi qua email</li>
        </ul>
      </div>
    </div>
  );
};

export default OKMartPayment; 