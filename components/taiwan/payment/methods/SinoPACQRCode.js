import React, { useState, useEffect } from 'react';
import axios from 'axios';
import QRCode from 'qrcode';

const SinoPACQRCodePayment = ({
  orderId,
  amount,
  productName,
  onSuccess,
  onError
}) => {
  const [qrCodeData, setQrCodeData] = useState('');
  const [qrCodeImage, setQrCodeImage] = useState('');
  const [paymentUrl, setPaymentUrl] = useState('');
  const [expireTime, setExpireTime] = useState(null);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState('pending'); // pending, success, failed, expired

  // Generate QR code when component mounts
  useEffect(() => {
    generateQRCode();
  }, [orderId, amount]);

  // Timer countdown
  useEffect(() => {
    if (expireTime && timeLeft > 0) {
      const timer = setInterval(() => {
        const now = new Date().getTime();
        const difference = expireTime - now;
        
        if (difference > 0) {
          setTimeLeft(difference);
        } else {
          setTimeLeft(0);
          setPaymentStatus('expired');
          clearInterval(timer);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [expireTime, timeLeft]);

  const generateQRCode = async () => {
    setIsLoading(true);
    try {
      console.log('Generating QR code for SinoPAC mobile payment...');
      
      // Call our new QR code API endpoint
      const response = await axios.post('/api/payment/sinopac/qrcode', {
        OrderNo: orderId,
        Amount: amount,
        PrdtName: productName || 'QR Code Payment',
        ExpMinutes: '10' // 10 minute expiry
      });

      if (response.data && response.data.Status === 'S') {
        const result = response.data.Result;
        
        // Set QR code data
        setQrCodeData(result.QRCode);
        setPaymentUrl(result.PaymentURL);
        
        // Set expiry time
        if (result.ExpireDate) {
          const expiry = new Date(result.ExpireDate).getTime();
          setExpireTime(expiry);
          setTimeLeft(expiry - new Date().getTime());
        } else if (result.ExpMinutes) {
          const expiry = new Date().getTime() + (parseInt(result.ExpMinutes) * 60 * 1000);
          setExpireTime(expiry);
          setTimeLeft(expiry - new Date().getTime());
        }

        // Generate QR code image from the data
        if (result.QRCode) {
          // If QRCode is base64 encoded, decode it first
          let qrData = result.QRCode;
          
          // For demo purposes, if it's our mock data, use the payment URL
          if (result.PaymentURL && result.QRCode.includes('base64')) {
            qrData = result.PaymentURL;
          }
          
          const qrImageUrl = await QRCode.toDataURL(qrData, {
            width: 256,
            margin: 2,
            color: {
              dark: '#000000',
              light: '#FFFFFF'
            }
          });
          setQrCodeImage(qrImageUrl);
        }

        console.log('QR code generated successfully:', {
          orderId,
          qrCode: result.QRCode,
          paymentUrl: result.PaymentURL,
          expiry: result.ExpireDate || result.ExpMinutes
        });

      } else {
        throw new Error(response.data?.Message || 'Failed to generate QR code');
      }

    } catch (error) {
      console.error('Error generating QR code:', error);
      setPaymentStatus('failed');
      if (onError) {
        onError('Failed to generate QR code: ' + error.message);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (milliseconds) => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const refreshQRCode = () => {
    setPaymentStatus('pending');
    setQrCodeData('');
    setQrCodeImage('');
    setPaymentUrl('');
    setExpireTime(null);
    setTimeLeft(0);
    generateQRCode();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">正在產生 QR Code...</span>
      </div>
    );
  }

  if (paymentStatus === 'expired') {
    return (
      <div className="text-center p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="text-yellow-800 text-lg font-semibold mb-2">
          ⏰ QR Code 已過期
        </div>
        <p className="text-yellow-700 mb-4">
          支付 QR Code 已過期，請重新產生
        </p>
        <button
          onClick={refreshQRCode}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重新產生 QR Code
        </button>
      </div>
    );
  }

  if (paymentStatus === 'failed') {
    return (
      <div className="text-center p-6 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-red-800 text-lg font-semibold mb-2">
          ❌ QR Code 產生失敗
        </div>
        <p className="text-red-700 mb-4">
          無法產生支付 QR Code，請稍後再試
        </p>
        <button
          onClick={refreshQRCode}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重試
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white border rounded-lg p-6 max-w-md mx-auto">
      <div className="text-center">
        {/* Header */}
        <div className="mb-4">
          <h3 className="text-xl font-bold text-gray-800 mb-2">
            📱 掃碼支付
          </h3>
          <p className="text-gray-600 text-sm">
            使用手機掃描下方 QR Code 完成支付
          </p>
        </div>

        {/* Payment Info */}
        <div className="bg-blue-50 p-3 rounded-lg mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">訂單號碼:</span>
            <span className="font-medium">{orderId}</span>
          </div>
          <div className="flex justify-between text-sm mt-1">
            <span className="text-gray-600">支付金額:</span>
            <span className="font-bold text-blue-600">NT$ {amount?.toLocaleString()}</span>
          </div>
        </div>

        {/* QR Code */}
        <div className="mb-4">
          {qrCodeImage ? (
            <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
              <img 
                src={qrCodeImage} 
                alt="Payment QR Code" 
                className="w-48 h-48 mx-auto"
              />
            </div>
          ) : (
            <div className="w-48 h-48 mx-auto bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
              <span className="text-gray-500">QR Code</span>
            </div>
          )}
        </div>

        {/* Timer */}
        {timeLeft > 0 && (
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-1">QR Code 剩餘時間</div>
            <div className="text-2xl font-mono font-bold text-red-600">
              {formatTime(timeLeft)}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="text-left bg-gray-50 p-4 rounded-lg mb-4">
          <h4 className="font-semibold text-gray-800 mb-2">💡 支付步驟:</h4>
          <ol className="text-sm text-gray-700 space-y-1">
            <li>1. 開啟您的行動銀行 App 或支付 App</li>
            <li>2. 掃描上方 QR Code</li>
            <li>3. 確認支付金額及商家資訊</li>
            <li>4. 完成支付驗證</li>
            <li>5. 支付成功後頁面將自動更新</li>
          </ol>
        </div>

        {/* Alternative payment link */}
        {paymentUrl && (
          <div className="border-t pt-4">
            <p className="text-xs text-gray-500 mb-2">
              或點擊下方連結在手機上支付
            </p>
            <a
              href={paymentUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 text-sm underline"
            >
              開啟手機支付頁面
            </a>
          </div>
        )}

        {/* Refresh button */}
        <div className="mt-4">
          <button
            onClick={refreshQRCode}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重新產生 QR Code
          </button>
        </div>
      </div>
    </div>
  );
};

export default SinoPACQRCodePayment; 