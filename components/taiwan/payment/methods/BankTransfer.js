import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Image from '../../../../components/Image';
import Button from '../../../../components/Button';
import { SiteContext } from '../../../../context/mainContext';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { getCustomerId, getCustomerInfo, isCustomerAuthenticated } from '../../../../utils/customerAuth';
import QRCode from 'qrcode';

// Add logging utility
const logPayment = (message, data = null) => {
  console.log(`[TAIWAN_QR_PAYMENT] ${message}`, data ? data : '');
}

// Allow local API for testing
const getApiBase = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return '';
};

// Improved error handling for Axios requests
const safeAxiosRequest = async (url, data, options = {}, retries = 3) => {
  let retryCount = 0;
  let lastError = null;

  // Add element to display live request status
  if (typeof document !== 'undefined') {
    const existingDebugElement = document.getElementById('api-debug-status');
    if (!existingDebugElement) {
      const debugElement = document.createElement('div');
      debugElement.id = 'api-debug-status';
      debugElement.style.position = 'fixed';
      debugElement.style.bottom = '0';
      debugElement.style.left = '0';
      debugElement.style.right = '0';
      debugElement.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
      debugElement.style.color = 'lime';
      debugElement.style.padding = '10px';
      debugElement.style.fontSize = '12px';
      debugElement.style.fontFamily = 'monospace';
      debugElement.style.zIndex = '9999';
      debugElement.style.maxHeight = '200px';
      debugElement.style.overflow = 'auto';
      document.body.appendChild(debugElement);
    }
  }

  const updateDebugStatus = (message, isError = false) => {
    if (typeof document !== 'undefined') {
      const debugElement = document.getElementById('api-debug-status');
      if (debugElement) {
        const entry = document.createElement('div');
        entry.style.marginBottom = '5px';
        entry.style.color = isError ? 'red' : 'lime';
        entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        debugElement.appendChild(entry);
        debugElement.scrollTop = debugElement.scrollHeight;
      }
    }
    console.log(message);
  };

  updateDebugStatus(`Starting request to ${url}`);

  while (retryCount < retries) {
    try {
      logPayment(`Making request to ${url}`, { attempt: retryCount + 1 });
      updateDebugStatus(`Attempt ${retryCount + 1}/${retries}: ${url.split('/').pop()}`);
      
      const response = await axios({
        method: options.method || 'post',
        url,
        data,
        headers: options.headers || { 'Content-Type': 'application/json' },
        timeout: options.timeout || 30000, // 30 seconds default timeout
      });
      
      updateDebugStatus(`Success: ${url.split('/').pop()} - Status ${response.status}`);
      return response;
    } catch (error) {
      lastError = error;
      const errorMessage = `Request failed: ${url.split('/').pop()} - ${error.message}`;
      updateDebugStatus(errorMessage, true);
      
      // Add detailed error output
      if (error.response) {
        updateDebugStatus(`Status: ${error.response.status} - ${JSON.stringify(error.response.data)}`, true);
      } else if (error.request) {
        // Check for CORS specific errors
        if (error.message === 'Network Error' && !error.response) {
          updateDebugStatus(`CORS Error: Unable to make request to ${url}. This is likely due to CORS restrictions.`, true);
          updateDebugStatus(`Please ensure the request is being made through the backend proxy.`, true);
        } else {
          updateDebugStatus(`No response received - Network error or CORS issue`, true);
        }
      }
      
      logPayment(`Request failed (attempt ${retryCount + 1}/${retries})`, {
        url,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      
      // Don't retry for specific errors (like 400 Bad Request)
      if (error.response && [400, 401, 403].includes(error.response.status)) {
        throw error;
      }
      
      retryCount++;
      if (retryCount < retries) {
        // Exponential backoff
        const delay = Math.pow(2, retryCount) * 1000;
        updateDebugStatus(`Retrying in ${delay}ms...`);
        logPayment(`Retrying in ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // All retries failed
  updateDebugStatus(`All ${retries} attempts failed for ${url}`, true);
  throw lastError;
};

const Input = ({ onChange, value, name, placeholder, type, min, required }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    type={type || "text"}
    placeholder={placeholder}
    name={name}
    min={min}
    required={required}
  />
);

const TaiwanBankTransferForm = ({
  handleSubmit,
  shopId = '',
  orderId = '',
  errorMessage,
  cart = [],
  paymentimage,
  onError,
  amount = 0,
  cartItems = [],
  setOrderCompleted,
  saveOrderToLocalStorage,
  currency = "NT$"
}) => {
  // Add payment fee constant
  const PAYMENT_FEE = 5;
  const router = useRouter();
  
  // Add debug state
  const [debugInfo, setDebugInfo] = useState({});
  const [showDebug, setShowDebug] = useState(false);
  
  logPayment("Initializing Taiwan QR Code Payment Form", { 
    orderId,
    shopId,
    amount,
    orderIdType: typeof orderId
  });

  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState(contextEmail || "");
  const [phone, setPhone] = useState(contextPhone || "");
  const [address, setAddress] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderDate, setOrderDate] = useState(new Date());
  const [isOrderProcessed, setIsOrderProcessed] = useState(false);
  
  // QR Code specific states
  const [qrCodeImage, setQrCodeImage] = useState("");
  const [qrCodeData, setQrCodeData] = useState("");
  const [paymentUrl, setPaymentUrl] = useState("");
  const [paymentExpiry, setPaymentExpiry] = useState("");
  const [expireTime, setExpireTime] = useState(null);
  const [timeLeft, setTimeLeft] = useState(0);
  const [paymentStatus, setPaymentStatus] = useState('pending'); // pending, success, failed, expired
  
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  
  // New state for phone card recharge options
  const [rechargePhone, setRechargePhone] = useState("");
  const [autoRecharge, setAutoRecharge] = useState(true);
  const [rechargeCodeReceived, setRechargeCodeReceived] = useState("");

  // Timer countdown for QR code expiry
  useEffect(() => {
    if (expireTime && timeLeft > 0) {
      const timer = setInterval(() => {
        const now = new Date().getTime();
        const difference = expireTime - now;
        
        if (difference > 0) {
          setTimeLeft(difference);
        } else {
          setTimeLeft(0);
          setPaymentStatus('expired');
          clearInterval(timer);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [expireTime, timeLeft]);

  // Get product type from cart items if available
  const getProductType = () => {
    const phoneCards = ['IF150', 'IF350', 'IF599', 'IF3M', 'IF6M', 'IF1Y'];
    
    for (const item of cart) {
      // Check product name or SKU for phone card products
      const isPhoneCard = phoneCards.some(code => 
        item.name?.includes(code) || item.sku?.includes(code)
      );
      
      if (isPhoneCard) {
        return item.name || item.sku;
      }
    }
    
    return null;
  };
  
  const productType = getProductType();
  const isPhoneRecharge = productType !== null;

  const handleInputChange = (setter) => (e) => {
    logPayment(`Updating form field: ${e.target.name || e.target.id}`, { 
      value: e.target.value 
    });
    setter(e.target.value);
  };

  // Effect to get user info if authenticated
  useEffect(() => {
    if (typeof window !== "undefined") {
      const authenticated = isCustomerAuthenticated();
      setIsUserAuthenticated(authenticated);
      
      if (authenticated) {
        const customerInfo = getCustomerInfo();
        setUserInfo(customerInfo || {});
        
        // Pre-fill form fields with customer information
        if (customerInfo) {
          if (customerInfo.name) {
            // Try to split the name into first and last name
            const nameParts = customerInfo.name.split(' ');
            if (nameParts.length > 1) {
              setLastName(nameParts[0]);
              setFirstName(nameParts.slice(1).join(' '));
            } else {
              setFirstName(customerInfo.name);
            }
          }
          
          if (customerInfo.email) {
            setEmail(customerInfo.email);
          }
          
          if (customerInfo.phone) {
            setPhone(customerInfo.phone);
          }
          
          if (customerInfo.address) {
            setAddress(customerInfo.address);
          }
        }
      }
    }
  }, []);

  const handleLoginClick = () => {
    // Store current page URL for redirecting back after login
    const returnUrl = window.location.pathname;
    localStorage.setItem('paymentReturnUrl', returnUrl);
    router.push(`/${shopId}/customer/login`);
  };

  const formatTime = (milliseconds) => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const refreshQRCode = async () => {
    setPaymentStatus('pending');
    setQrCodeData('');
    setQrCodeImage('');
    setPaymentUrl('');
    setExpireTime(null);
    setTimeLeft(0);
    
    // Re-submit the form to generate new QR code
    const formEvent = { preventDefault: () => {} };
    await handleFormSubmit(formEvent, true); // Add refresh flag
  };

  const handleFormSubmit = async (e, isRefresh = false) => {
    e.preventDefault();
    
    // Use authenticated user info if available
    const submissionFirstName = isUserAuthenticated && userInfo.name ? 
      (userInfo.name.split(' ').length > 1 ? userInfo.name.split(' ').slice(1).join(' ') : userInfo.name) : 
      firstName;
    
    const submissionLastName = isUserAuthenticated && userInfo.name ? 
      (userInfo.name.split(' ').length > 1 ? userInfo.name.split(' ')[0] : '') : 
      lastName;
    
    const submissionEmail = isUserAuthenticated && userInfo.email ? userInfo.email : email;
    const submissionPhone = isUserAuthenticated && userInfo.phone ? userInfo.phone : phone;
    const submissionAddress = isUserAuthenticated && userInfo.address ? userInfo.address : address;
    
    // Enhanced debugging
    const debugData = {
      formData: {
        firstName: submissionFirstName,
        lastName: submissionLastName,
        email: submissionEmail,
        phone: submissionPhone,
        address: submissionAddress,
        message: message,
        currency,
        isPhoneRecharge,
        rechargePhone: rechargePhone || submissionPhone,
        autoRecharge,
        isAuthenticated: isUserAuthenticated,
        orderId,
        amount,
        cart: cart.map(item => ({
          name: item.name,
          sku: item.sku,
          price: item.price,
          quantity: item.quantity
        }))
      },
      environment: {
        apiBase: getApiBase(),
        timestamp: new Date().toISOString(),
        browser: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server',
        screen: typeof window !== 'undefined' ? {
          width: window.innerWidth,
          height: window.innerHeight
        } : null
      },
      requests: []
    };
    
    // Update debug info
    setDebugInfo(debugData);
    
    // Comprehensive console logging
    console.log('======= TAIWAN QR CODE PAYMENT DEBUG INFO =======');
    console.log('FORM DATA:', debugData.formData);
    console.log('ENVIRONMENT:', debugData.environment);
    
    logPayment("QR Code form submission started", debugData.formData);
    
    if (isSubmitting && !isRefresh) {
      logPayment("Submission already in progress, skipping");
      return;
    }
    
    setIsSubmitting(true);

    try {
      // Check that orderId is provided
      if (!orderId) {
        throw new Error("No orderId provided from checkout.js");
      }
      
      logPayment("Using order ID from checkout", { 
        orderId,
        timestamp: new Date().toISOString()
      });
      
      // Make sure we use window.location.origin correctly
      const apiBase = getApiBase();
      const returnUrl = `${apiBase}/api/payment/sinopac-callback`;
      
      // Call SinoPAC QR Code API with new structure
      try {
        // Prepare simple data for QR Code API
        const qrPayload = {
          OrderNo: orderId,
          Amount: Math.round(amount),
          PrdtName: cart.map(item => item.name).join(', ').substring(0, 50) || "QR Code Payment",
          Memo: message || "QR Code Payment",
          ExpMinutes: "10", // QR code expires in 10 minutes
          CustomerName: `${submissionFirstName} ${submissionLastName}`,
          CustomerPhone: submissionPhone,
          CustomerEmail: submissionEmail,
          CustomerAddress: submissionAddress,
          ReturnURL: returnUrl
        };

        // Add to debug info
        debugData.qrPayload = qrPayload;
        console.log('QR CODE PAYLOAD:', qrPayload);
        setDebugInfo({...debugData});

        // Set timeout for API call
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("SinoPAC QR Code API request timeout")), 30000)
        );

        const requestStartTime = new Date();

        // Update request tracking for API call
        debugData.requests.push({
          startTime: requestStartTime.toISOString(),
          endpoint: `${getApiBase()}/api/payment/sinopac/qrcode`,
          method: 'POST',
          requestData: qrPayload,
          headers: {
            'Content-Type': 'application/json'
          }
        });
        setDebugInfo({...debugData});

        const response = await Promise.race([
          safeAxiosRequest(`${getApiBase()}/api/payment/sinopac/qrcode`, qrPayload, {
            headers: {
              'Content-Type': 'application/json'
            }
          }),
          timeoutPromise
        ]);
        
        // Update request with response
        const requestEndTime = new Date();
        const lastRequestIndex = debugData.requests.length - 1;
        if (lastRequestIndex >= 0) {
          debugData.requests[lastRequestIndex].endTime = requestEndTime.toISOString();
          debugData.requests[lastRequestIndex].duration = requestEndTime - requestStartTime;
          debugData.requests[lastRequestIndex].response = response.data;
          debugData.requests[lastRequestIndex].status = response.status;
          debugData.requests[lastRequestIndex].success = true;
        }
        
        logPayment("SinoPAC QR Code API response received", response.data);
        console.log('QR CODE API RESPONSE:', response.data);
        setDebugInfo({...debugData});
        
        // Handle the response for QR Code payment
        if (response.data && response.data.Status === "S") {
          const result = response.data.Result;
          
          // Set QR code data
          setQrCodeData(result.QRCode);
          setPaymentUrl(result.PaymentURL);
          
          // Set expiry time
          if (result.ExpireDate) {
            const expiry = new Date(result.ExpireDate).getTime();
            setExpireTime(expiry);
            setTimeLeft(expiry - new Date().getTime());
          } else if (result.ExpMinutes) {
            const expiry = new Date().getTime() + (parseInt(result.ExpMinutes) * 60 * 1000);
            setExpireTime(expiry);
            setTimeLeft(expiry - new Date().getTime());
          }

          // Generate QR code image from the data
          if (result.QRCode) {
            let qrData = result.QRCode;
            
            // For demo purposes, if it's our mock data, use the payment URL
            if (result.PaymentURL && result.QRCode.includes('base64')) {
              qrData = result.PaymentURL;
            }
            
            const qrImageUrl = await QRCode.toDataURL(qrData, {
              width: 256,
              margin: 2,
              color: {
                dark: '#000000',
                light: '#FFFFFF'
              }
            });
            setQrCodeImage(qrImageUrl);
          }

          setPaymentExpiry(result.ExpireDate || "");
          
          // Add to debug info
          debugData.paymentData = result;
          console.log('QR CODE PAYMENT DATA:', result);
          setDebugInfo({...debugData});
          
          // Prepare the order data for our system
          const orderData = {
            orderId: orderId,
            storeId: shopId,
            paymentMethod: "qrcode",
            paymentSubMethod: "sinopac_mobile",
            currency: currency,
            customerInfo: {
              firstName: submissionFirstName,
              lastName: submissionLastName,
              email: submissionEmail,
              phone: submissionPhone,
              address: submissionAddress,
              message,
              rechargePhone: rechargePhone || submissionPhone,
              autoRecharge
            },
            qrCodeInfo: {
              qrCode: result.QRCode,
              paymentUrl: result.PaymentURL,
              expireDate: result.ExpireDate,
              expMinutes: result.ExpMinutes
            },
            items: cartItems || cart,
            amount: amount || 0,
            orderDate: orderDate.toISOString(),
            sinopacResponse: result
          };
          
          // Add to debug info
          debugData.orderData = orderData;
          console.log('ORDER DATA:', orderData);
          setDebugInfo({...debugData});

          logPayment("Sending order to API", { orderId: orderData.orderId });
          
          // Update request tracking for backend call
          const backendRequestStartTime = new Date();
          debugData.requests.push({
            startTime: backendRequestStartTime.toISOString(),
            endpoint: `${getApiBase()}/api/submitOrder`,
            method: 'POST',
            requestData: orderData
          });
          setDebugInfo({...debugData});
          
          // Send to our backend with improved error handling
          try {
            console.log('CALLING BACKEND API:', `${getApiBase()}/api/submitOrder`);
            const backendResponse = await safeAxiosRequest(`${getApiBase()}/api/submitOrder`, orderData);
            
            // Update request tracking
            const backendRequestEndTime = new Date();
            const backendRequestIndex = debugData.requests.length - 1;
            if (backendRequestIndex >= 0) {
              debugData.requests[backendRequestIndex].endTime = backendRequestEndTime.toISOString();
              debugData.requests[backendRequestIndex].duration = backendRequestEndTime - backendRequestStartTime;
              debugData.requests[backendRequestIndex].response = backendResponse.data;
              debugData.requests[backendRequestIndex].status = backendResponse.status;
              debugData.requests[backendRequestIndex].success = true;
            }
            
            console.log('BACKEND RESPONSE:', backendResponse.data);
            setDebugInfo({...debugData});
            
            if (backendResponse.data && backendResponse.data.success) {
              logPayment("Order submitted successfully", { 
                orderId: orderData.orderId,
                responseData: backendResponse.data
              });
              
              // Save order to local storage if available
              if (saveOrderToLocalStorage) {
                logPayment("Saving order to localStorage");
                saveOrderToLocalStorage({
                  orderId: orderData.orderId,
                  status: 'Processing',
                  customerInfo: {
                    firstName: submissionFirstName,
                    lastName: submissionLastName,
                    email: submissionEmail,
                    phone: submissionPhone,
                  },
                  paymentMethod: "qrcode",
                  paymentInfo: {
                    qrCode: result.QRCode,
                    paymentUrl: result.PaymentURL,
                    expireDate: result.ExpireDate,
                    expMinutes: result.ExpMinutes
                  }
                });
              }
              
              setIsOrderProcessed(true);
              if (setOrderCompleted) {
                logPayment("Setting order as completed");
                setOrderCompleted(true);
              }
            } else {
              // Add to debug info
              debugData.backendError = backendResponse.data?.message || "Unknown server error";
              console.log('BACKEND ERROR:', debugData.backendError);
              setDebugInfo({...debugData});
              
              // Local storage fallback if backend returns error but we have payment info
              if (saveOrderToLocalStorage) {
                logPayment("Backend error but saving order to localStorage as fallback");
                saveOrderToLocalStorage({
                  orderId: orderData.orderId,
                  status: 'Processing',
                  customerInfo: {
                    firstName: submissionFirstName,
                    lastName: submissionLastName,
                    email: submissionEmail,
                    phone: submissionPhone,
                  },
                  paymentMethod: "qrcode",
                  paymentInfo: {
                    qrCode: result.QRCode,
                    paymentUrl: result.PaymentURL,
                    expireDate: result.ExpireDate,
                    expMinutes: result.ExpMinutes
                  }
                });
                
                setIsOrderProcessed(true);
                if (setOrderCompleted) {
                  setOrderCompleted(true);
                }
              } else {
                throw new Error(backendResponse.data?.message || "Unknown server error");
              }
            }
          } catch (backendError) {
            // Update request tracking
            const backendRequestEndTime = new Date();
            const backendRequestIndex = debugData.requests.length - 1;
            if (backendRequestIndex >= 0) {
              debugData.requests[backendRequestIndex].endTime = backendRequestEndTime.toISOString();
              debugData.requests[backendRequestIndex].duration = backendRequestEndTime - backendRequestStartTime;
              debugData.requests[backendRequestIndex].error = backendError.message;
              debugData.requests[backendRequestIndex].success = false;
            }
            
            // Add to debug info
            debugData.backendError = backendError.message;
            console.log('BACKEND ERROR:', backendError);
            setDebugInfo({...debugData});
            
            // Even if backend fails, show payment info if available
            if (saveOrderToLocalStorage) {
              logPayment("Backend API failed but we have payment info to display", backendError);
              
              saveOrderToLocalStorage({
                orderId: orderData.orderId,
                status: 'Processing',
                customerInfo: {
                  firstName: submissionFirstName,
                  lastName: submissionLastName,
                  email: submissionEmail,
                  phone: submissionPhone,
                },
                paymentMethod: "qrcode",
                paymentInfo: {
                  qrCode: result.QRCode,
                  paymentUrl: result.PaymentURL,
                  expireDate: result.ExpireDate,
                  expMinutes: result.ExpMinutes
                }
              });
            }
            
            setIsOrderProcessed(true);
            if (setOrderCompleted) {
              setOrderCompleted(true);
            }
          }
        } else {
          throw new Error(response.data?.Message || 'Failed to generate QR code');
        }
        
      } catch (error) {
        // Update request with error
        const requestEndTime = new Date();
        const lastRequestIndex = debugData.requests.length - 1;
        if (lastRequestIndex >= 0) {
          debugData.requests[lastRequestIndex].endTime = requestEndTime.toISOString();
          debugData.requests[lastRequestIndex].duration = requestEndTime - requestStartTime;
          debugData.requests[lastRequestIndex].error = error.message;
          debugData.requests[lastRequestIndex].success = false;
        }
        
        // Add to debug info
        debugData.apiError = error.message;
        console.log('API ERROR:', error);
        setDebugInfo({...debugData});
        
        setPaymentStatus('failed');
        
        throw error;
      }
      
    } catch (error) {
      logPayment("Error submitting order", { 
        error: error.message,
        stack: error.stack
      });
      console.error("Error submitting order:", error);
      
      // Add to debug info
      debugData.finalError = error.message;
      debugData.errorStack = error.stack;
      console.log('FINAL ERROR:', error);
      setDebugInfo({...debugData});
      
      let errorMessage = error.message || "Please try again.";
      
      // Improve error messages for specific errors
      if (error.message === 'Network Error' || error.code === 'ECONNABORTED') {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      } else if (error.response && error.response.status === 404) {
        errorMessage = 'Payment service unavailable. Please try again later.';
      } else if (error.response && error.response.status === 500) {
        errorMessage = 'Server error. Our team has been notified and is working on the issue.';
      }
      
      if (onError) {
        onError("Error processing your order: " + errorMessage);
      } else {
        // Fallback error handling if onError prop is not provided
        alert("Error processing your order: " + errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatExpiryDate = (dateString) => {
    if (!dateString) return "";
    
    try {
      // Handle format YYYYMMDD
      if (/^\d{8}$/.test(dateString)) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        return `${year}/${month}/${day}`;
      }
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString;
      }
      
      return date.toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  // Calculate total with fee
  const baseAmount = (cartItems && cartItems.length) ?
    cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0) :
    amount || 0;
  const totalWithFee = baseAmount + PAYMENT_FEE;

  // Add this before the return statement
  const renderDebugInfo = () => {
    if (!showDebug) return null;
    
    return (
      <div className="mt-6 p-4 bg-black text-green-400 font-mono text-xs overflow-x-auto rounded">
        <div className="flex justify-between mb-2">
          <h3 className="text-white font-bold">Debug Information</h3>
          <button 
            onClick={() => navigator.clipboard.writeText(JSON.stringify(debugInfo, null, 2))}
            className="px-2 py-1 bg-gray-700 text-white rounded text-xs"
          >
            Copy
          </button>
        </div>
        <div>
          <h4 className="text-yellow-400 mt-2">Form Data:</h4>
          <pre>{JSON.stringify(debugInfo.formData || {}, null, 2)}</pre>
          
          <h4 className="text-yellow-400 mt-2">Environment:</h4>
          <pre>{JSON.stringify(debugInfo.environment || {}, null, 2)}</pre>
          
          {debugInfo.qrPayload && (
            <>
              <h4 className="text-yellow-400 mt-2">SinoPAC Payload:</h4>
              <pre>{JSON.stringify(debugInfo.qrPayload, null, 2)}</pre>
            </>
          )}
          
          {debugInfo.requests && debugInfo.requests.length > 0 && (
            <>
              <h4 className="text-yellow-400 mt-2">Network Requests:</h4>
              {debugInfo.requests.map((request, index) => (
                <div key={index} className="mb-2 p-2 bg-gray-900 rounded">
                  <p>Endpoint: {request.endpoint}</p>
                  <p>Method: {request.method}</p>
                  <p>Start Time: {request.startTime}</p>
                  <p>End Time: {request.endTime || 'N/A'}</p>
                  <p>Duration: {request.duration ? `${request.duration}ms` : 'N/A'}</p>
                  <p>Status: {request.success ? 
                    <span className="text-green-500">Success ({request.status})</span> : 
                    <span className="text-red-500">Failed</span>}
                  </p>
                  
                  {request.error && (
                    <div className="mt-1">
                      <p className="text-red-400">Error: {request.error}</p>
                    </div>
                  )}
                  
                  {request.response && (
                    <div className="mt-1">
                      <p className="text-blue-400">Response:</p>
                      <pre className="text-xs">{JSON.stringify(request.response, null, 2)}</pre>
                    </div>
                  )}
                </div>
              ))}
            </>
          )}
          
          {debugInfo.paymentData && (
            <>
              <h4 className="text-yellow-400 mt-2">Payment Data:</h4>
              <pre>{JSON.stringify(debugInfo.paymentData, null, 2)}</pre>
            </>
          )}
          
          {debugInfo.orderData && (
            <>
              <h4 className="text-yellow-400 mt-2">Order Data:</h4>
              <pre>{JSON.stringify(debugInfo.orderData, null, 2)}</pre>
            </>
          )}
          
          {debugInfo.finalError && (
            <>
              <h4 className="text-red-400 mt-2">Error:</h4>
              <pre>{debugInfo.finalError}</pre>
              <p className="text-xs text-gray-500">Stack trace:</p>
              <pre className="text-xs text-gray-500">{debugInfo.errorStack}</pre>
            </>
          )}
        </div>
      </div>
    );
  };

  if (isOrderProcessed) {
    logPayment("Showing QR code payment confirmation");
    return (
      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
        <h3 className="text-2xl font-bold text-green-800 mb-4">🎉 Cảm ơn bạn đã đặt hàng!</h3>
        <p className="mb-2">Đơn hàng của bạn (#{orderId}) đã được tạo thành công.</p>
        
        {/* QR Code Payment Section */}
        {qrCodeImage && (
          <div className="mt-4 p-4 bg-white border border-blue-200 rounded-md">
            <h4 className="text-lg font-bold text-blue-800 mb-4 text-center">📱 Quét mã QR để thanh toán</h4>
            
            {/* Payment Status */}
            {paymentStatus === 'expired' && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
                <div className="text-yellow-800 text-lg font-semibold mb-2">
                  ⏰ QR Code đã hết hạn
                </div>
                <p className="text-yellow-700 mb-3">
                  Mã QR đã hết hạn, vui lòng tạo mã mới để thanh toán
                </p>
                <button
                  onClick={refreshQRCode}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Đang tạo..." : "Tạo mã QR mới"}
                </button>
              </div>
            )}
            
            {paymentStatus === 'failed' && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-center">
                <div className="text-red-800 text-lg font-semibold mb-2">
                  ❌ Tạo QR Code thất bại
                </div>
                <p className="text-red-700 mb-3">
                  Không thể tạo mã QR thanh toán, vui lòng thử lại
                </p>
                <button
                  onClick={refreshQRCode}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Đang thử lại..." : "Thử lại"}
                </button>
              </div>
            )}

            {paymentStatus === 'pending' && qrCodeImage && (
              <>
                {/* Payment Info */}
                <div className="bg-blue-50 p-3 rounded-lg mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Mã đơn hàng:</span>
                    <span className="font-medium">{orderId}</span>
                  </div>
                  <div className="flex justify-between text-sm mt-1">
                    <span className="text-gray-600">Số tiền thanh toán:</span>
                    <span className="font-bold text-blue-600">{amount?.toLocaleString()} {currency}</span>
                  </div>
                </div>

                {/* QR Code */}
                <div className="text-center mb-4">
                  <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                    <img 
                      src={qrCodeImage} 
                      alt="Payment QR Code" 
                      className="w-48 h-48 mx-auto"
                    />
                  </div>
                </div>

                {/* Timer */}
                {timeLeft > 0 && (
                  <div className="text-center mb-4">
                    <div className="text-sm text-gray-600 mb-1">Thời gian còn lại</div>
                    <div className="text-2xl font-mono font-bold text-red-600">
                      {formatTime(timeLeft)}
                    </div>
                  </div>
                )}

                {/* Instructions */}
                <div className="text-left bg-gray-50 p-4 rounded-lg mb-4">
                  <h5 className="font-semibold text-gray-800 mb-2">💡 Hướng dẫn thanh toán:</h5>
                  <ol className="text-sm text-gray-700 space-y-1">
                    <li>1. Mở ứng dụng ngân hàng di động của bạn</li>
                    <li>2. Quét mã QR ở trên bằng camera</li>
                    <li>3. Xác nhận thông tin thanh toán</li>
                    <li>4. Hoàn tất giao dịch trong ứng dụng</li>
                    <li>5. Chờ xác nhận thanh toán thành công</li>
                  </ol>
                </div>

                {/* Alternative payment link */}
                {paymentUrl && (
                  <div className="border-t pt-4 text-center">
                    <p className="text-xs text-gray-500 mb-2">
                      Hoặc nhấn vào liên kết bên dưới để thanh toán trên điện thoại
                    </p>
                    <a
                      href={paymentUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm underline"
                    >
                      Mở trang thanh toán trên điện thoại
                    </a>
                  </div>
                )}

                {/* Refresh button */}
                <div className="mt-4 text-center">
                  <button
                    onClick={refreshQRCode}
                    className="text-sm text-gray-500 hover:text-gray-700 underline"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang tạo mã mới..." : "Tạo mã QR mới"}
                  </button>
                </div>
              </>
            )}
          </div>
        )}
        
        {/* Add debug toggle button */}
        <div className="mt-4 border-t pt-4">
          <button 
            onClick={() => setShowDebug(!showDebug)} 
            className="text-xs bg-gray-100 px-2 py-1 rounded"
          >
            {showDebug ? 'Ẩn thông tin debug' : 'Hiển thị thông tin debug'}
          </button>
          
          {renderDebugInfo()}
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Add debug toggle button at the top */}
      <div className="mb-4 flex justify-end">
        <button 
          onClick={() => setShowDebug(!showDebug)} 
          className="text-xs bg-gray-100 px-2 py-1 rounded"
        >
          {showDebug ? 'Ẩn debug' : 'Hiện debug'}
        </button>
      </div>
      
      <form className="space-y-4" onSubmit={handleFormSubmit}>
        <div className="thong-tin-ca-nhan mb-6">
          <h3 className="text-lg font-bold mb-3">📱 Thanh toán bằng QR Code</h3>
          <div className="bg-blue-50 p-3 rounded-lg mb-4">
            <p className="text-sm text-blue-800">
              <strong>🚀 Thanh toán nhanh chóng:</strong> Quét mã QR bằng ứng dụng ngân hàng để thanh toán tức thì!
            </p>
          </div>
          
          <h4 className="text-md font-semibold mb-3">Thông tin cá nhân</h4>
          
          {isUserAuthenticated ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6 mb-4">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Họ và tên</p>
                  <p className="font-medium">{userInfo.name || `${firstName} ${lastName}`.trim()}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500 mb-1">Email</p>
                  <p className="font-medium">{userInfo.email || email || "Chưa cung cấp"}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500 mb-1">Số điện thoại</p>
                  <p className="font-medium">{userInfo.phone || phone || "Chưa cung cấp"}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500 mb-1">Địa chỉ</p>
                  <p className="font-medium">{userInfo.address || address || "Chưa cung cấp"}</p>
                </div>
              </div>
              
              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700">Ghi chú (Tùy chọn)</label>
                <textarea
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                ></textarea>
              </div>
              
              <div className="mt-3 bg-green-50 p-3 rounded border border-green-200">
                <p className="text-sm text-green-700">
                  <span className="font-medium">Thông tin từ tài khoản của bạn sẽ được sử dụng để xử lý đơn hàng.</span>
                </p>
              </div>
            </>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Tên</label>
                  <input
                    type="text"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Họ</label>
                  <input
                    type="text"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Điện thoại</label>
                  <input
                    type="tel"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    required
                  />
                </div>
              </div>
              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700">Địa chỉ</label>
                <textarea
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                ></textarea>
              </div>
              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700">Ghi chú (Tùy chọn)</label>
                <textarea
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                ></textarea>
              </div>
            </>
          )}
        </div>

        <div className="tom-tat-don-hang mb-6">
          <h3 className="text-lg font-bold mb-3">Tóm tắt đơn hàng</h3>
          <div className="border rounded-md p-4">
            <div className="flex justify-between mb-2">
              <span>Mã đơn hàng:</span>
              <span>{orderId}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Tổng tiền hàng:</span>
              <span>{baseAmount?.toLocaleString()} {currency}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Phí thanh toán QR:</span>
              <span className="text-green-600 font-medium">Miễn phí</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Tổng thanh toán:</span>
              <span>{amount?.toLocaleString()} {currency}</span>
            </div>
          </div>
        </div>

        {errorMessage && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {errorMessage}
          </div>
        )}

        <div className="text-center">
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-md transition-colors"
            disabled={isSubmitting}
          >
            {isSubmitting ? "⏳ Đang tạo mã QR..." : "🎯 Tạo mã QR thanh toán"}
          </button>
        </div>

        {/* Debug info section */}
        {renderDebugInfo()}
      </form>
    </div>
  );
};

export default TaiwanBankTransferForm; 