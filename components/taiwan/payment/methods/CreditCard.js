import React, { useState, useEffect, useContext } from 'react';
import Image from '../../../../components/Image';
import Button from '../../../../components/Button';
import { SiteContext } from '../../../../context/mainContext';

// Credit/Debit card payment logos
const VISA_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo-visa-card.webp";
const MASTERCARD_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo-mastercard.jpg";
const JCB_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo-jcb.jpeg";

const CreditCardPayment = ({ amount, currency = "NT$" }) => {
  // Payment fee calculation - NT$43 or 3% of order value (whichever is greater)
  const FIXED_FEE = 43;
  const PERCENTAGE_FEE = 0.03;
  
  // Calculate fee based on the greater of the two
  const calculatedPercentageFee = Math.round((amount || 0) * PERCENTAGE_FEE);
  const paymentFee = Math.max(FIXED_FEE, calculatedPercentageFee);
  
  // Calculate total with fee
  const baseAmount = amount || 0;
  const totalWithFee = baseAmount + paymentFee;
  
  // Fallback image in case main image fails to load
  const [visaImageError, setVisaImageError] = useState(false);
  
  return (
    <div className="w-full">
      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex items-center mb-2">
          <div className="w-10 h-6 flex items-center justify-center bg-white rounded-md border border-gray-200 p-1 mr-2">
            {!visaImageError ? (
              <img 
                src={VISA_LOGO}
                alt="Visa Card" 
                className="max-h-full max-w-full object-contain"
                onError={() => setVisaImageError(true)}
              />
            ) : (
              <div className="text-xs font-bold text-blue-700">VISA</div>
            )}
          </div>
          <h2 className="text-xl font-bold">Thẻ Visa/Thẻ tín dụng/Thẻ ghi nợ - {currency}</h2>
        </div>
        
        <div className="bg-white p-4 rounded-md border border-blue-100 mb-4">
          <h3 className="font-medium text-lg mb-2">Phí giao dịch: {paymentFee} {currency}</h3>
          <p className="text-sm">
            Thanh toán bằng thẻ Visa, thẻ tín dụng hoặc thẻ ghi nợ của bạn.
            Phương thức này có phí giao dịch {paymentFee} {currency} (tương đương 3% giá trị đơn hàng).
            Giao dịch của bạn sẽ được bảo mật và xử lý ngay lập tức.
          </p>
        </div>
        
        <div className="mt-3 bg-white p-3 rounded-md border border-blue-100">
          <h4 className="font-bold mb-1">Quy trình thanh toán:</h4>
          <ol className="list-decimal ml-5 text-sm">
            <li className="mb-1">Nhập thông tin thẻ của bạn vào biểu mẫu bảo mật</li>
            <li className="mb-1">Xác minh thanh toán (có thể yêu cầu xác thực 3D Secure)</li>
            <li className="mb-1">Nhận xác nhận thanh toán tức thì</li>
            <li>Đơn hàng sẽ được xử lý ngay sau khi thanh toán thành công</li>
          </ol>
          <div className="mt-3 text-sm bg-yellow-50 p-2 rounded-md">
            <p className="font-medium text-yellow-800">
              Lưu ý: Phí giao dịch {paymentFee} {currency} (3% giá trị đơn hàng) đã được tính vào tổng thanh toán.
            </p>
          </div>
        </div>
      </div>
      
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <h3 className="text-lg font-semibold mb-2">Sắp ra mắt</h3>
        <p>
          Phương thức thanh toán bằng thẻ tín dụng/ghi nợ đang trong quá trình tích hợp và sẽ sớm được ra mắt.
          Vui lòng sử dụng phương thức thanh toán khác như Chuyển khoản ngân hàng, 7-Eleven, hoặc FamilyMart.
        </p>
      </div>
      
      <div className="border rounded-lg p-4 mb-4 mt-4">
        <h3 className="font-medium mb-2">Chi tiết thanh toán</h3>
        <div className="flex justify-between mb-2">
          <span>Tổng đơn hàng:</span>
          <span>{baseAmount?.toLocaleString()} {currency}</span>
        </div>
        <div className="flex justify-between mb-2">
          <span>Phí thanh toán (3%):</span>
          <span>{paymentFee} {currency}</span>
        </div>
        <div className="flex justify-between font-bold">
          <span>Tổng thanh toán:</span>
          <span>{totalWithFee?.toLocaleString()} {currency}</span>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-3 my-4 justify-center">
        <div className="h-10 flex items-center border p-2 rounded">
          <div className="flex items-center justify-center bg-white rounded-md h-full">
            {!visaImageError ? (
              <img 
                src={VISA_LOGO}
                alt="Visa" 
                className="h-full object-contain"
                onError={() => setVisaImageError(true)}
              />
            ) : (
              <div className="px-3 font-bold text-blue-700">VISA</div>
            )}
          </div>
        </div>
        <div className="h-10 flex items-center border p-2 rounded">
          <div className="flex items-center justify-center bg-white rounded-md h-full">
            <img 
              src={MASTERCARD_LOGO}
              alt="Mastercard" 
              className="h-full object-contain"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.parentNode.innerHTML = '<div class="px-3 font-bold text-red-600">MASTERCARD</div>';
              }}
            />
          </div>
        </div>
        <div className="h-10 flex items-center border p-2 rounded">
          <div className="flex items-center justify-center bg-white rounded-md h-full">
            <img 
              src={JCB_LOGO}
              alt="JCB" 
              className="h-full object-contain"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.parentNode.innerHTML = '<div class="px-3 font-bold text-green-600">JCB</div>';
              }}
            />
          </div>
        </div>
      </div>
      
      <div className="text-sm text-gray-600">
        <p className="font-medium mb-1">Lưu ý:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Thanh toán bằng thẻ tín dụng/ghi nợ được bảo mật theo tiêu chuẩn PCI DSS</li>
          <li>Không lưu trữ thông tin thẻ của bạn sau khi giao dịch hoàn tất</li>
          <li>Một số ngân hàng có thể yêu cầu xác thực bổ sung (3D Secure)</li>
        </ul>
      </div>
    </div>
  );
};

export default CreditCardPayment; 