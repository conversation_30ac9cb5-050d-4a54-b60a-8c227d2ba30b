import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Image from '../../../components/Image';
import Button from '../../../components/Button';
import { SiteContext } from '../../../context/mainContext';
import { v4 as uuidv4 } from 'uuid';
import Link from 'next/link';
import { 
  TaiwanBankTransferForm,
  SevenEleveniBON,
  CardPaymentForm,
  FamilyMartPaymentForm,
  OKMartPayment,
  /* HiLifePayment, */
  CreditCardPayment
} from './methods';

// Image URLs
const BANK_TRANSFER_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/bank-transfer-logo.webp";
const SEVEN_ELEVEN_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/7-11-logo.webp";
const FAMILY_MART_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/familymart-logo.webp";
const OK_MART_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/ok-mart-logo.webp";
/* const HI_LIFE_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/hi-life-logo.webp"; */
const CREDIT_CARD_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo-visa-card.webp";

const Input = ({ onChange, value, name, placeholder, type, min, required }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    type={type || "text"}
    placeholder={placeholder}
    name={name}
    min={min}
    required={required}
  />
);

const calculateTotalForCurrentStore = (cart, shopId) => {
  let total = 0;
  for (const item of cart) {
    if (item.store === shopId) {
      total += item.price * item.quantity;
    }
  }
  return total;
};

// Add logging utility for Taiwan Payment Form
const logTaiwanPayment = (message, data = null) => {
  console.log(`[TAIWAN_PAYMENT] ${message}`, data ? data : '');
}

// Payment Steps component to explain the process for each payment method
const PaymentSteps = ({ method }) => {
  let steps = [];

  switch (method) {
    case "bank":
      steps = [
        "Nhập thông tin đơn hàng và cá nhân",
        "Nhận thông tin tài khoản ngân hàng",
        "Chuyển khoản với nội dung là mã đơn hàng",
        "Hệ thống tự động xác nhận khi thanh toán thành công"
      ];
      break;
    case "7-11":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng 7-Eleven gần nhất",
        "Tìm máy ibon và chọn chức năng thanh toán",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break;
    case "family-mart":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng FamilyMart gần nhất",
        "Tìm máy FamiPort và chọn chức năng thanh toán",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break;
    case "ok-mart":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng OK Mart gần nhất",
        "Sử dụng máy thanh toán tại cửa hàng",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break;
    /* case "hi-life":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng Hi-Life gần nhất",
        "Sử dụng máy thanh toán tại cửa hàng",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break; */
  }

  return (
    <div className="bg-blue-50 p-4 rounded-lg mt-4 border border-blue-100">
      <h3 className="font-medium text-lg mb-2">Quy trình thanh toán</h3>
      <ol className="list-decimal pl-5 space-y-1">
        {steps.map((step, index) => (
          <li key={index} className="text-sm">{step}</li>
        ))}
      </ol>
    </div>
  );
};

const PaymentFormTaiwan = (props) => {
  const { currency = "NT$", amount, cart, shopId } = props;
  
  logTaiwanPayment("Initializing Taiwan Payment Form", { 
    orderId: props.orderId, 
    shopId: props.shopId, 
    amount: props.amount,
    currency
  });

  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext);
  const [selectedMethod, setSelectedMethod] = useState("bank");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState(contextEmail || "");
  const [phone, setPhone] = useState(contextPhone || "");
  const [address, setAddress] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderDate, setOrderDate] = useState(new Date());
  const [isOrderProcessed, setIsOrderProcessed] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");

  // Payment fee based on the selected method
  const fees = {
    bank: 5,
    "7-11": 25,
    "7-11-card": 25,
    "family-mart": 25,
    "ok-mart": 25,
    /* "hi-life": 25, */
    "credit-card": 43
  };

  // Effect to track method selection for debugging
  useEffect(() => {
    logTaiwanPayment("Payment method selected", { selectedMethod });
  }, [selectedMethod]);

  // Show the appropriate payment form based on the selected method
  const renderPaymentForm = () => {
    logTaiwanPayment("Rendering payment form", { selectedMethod });
    
    switch (selectedMethod) {
      case "bank":
        return <TaiwanBankTransferForm {...props} />;
      case "7-11":
        return <SevenEleveniBON {...props} />;
      case "7-11-card":
        return <CardPaymentForm {...props} />;
      case "family-mart":
        return <FamilyMartPaymentForm {...props} />;
      case "ok-mart":
        return <OKMartPayment amount={props.amount} currency={currency} />;
      /* case "hi-life":
        return <HiLifePayment amount={props.amount} currency={currency} />; */
      case "credit-card":
        return <CreditCardPayment amount={props.amount} currency={currency} />;
      default:
        return <TaiwanBankTransferForm {...props} />;
    }
  };

  const handleInputChange = (setter) => (e) => {
    logTaiwanPayment(`Updating form field: ${e.target.name || e.target.id}`, { 
      oldValue: e.target.value, 
      newValue: e.target.value 
    });
    setter(e.target.value);
  };

  // Effect to track state changes for debugging
  useEffect(() => {
    logTaiwanPayment("Form state updated", {
      firstName,
      lastName,
      email,
      phone,
      address,
      messageLength: message?.length,
      selectedMethod,
      isSubmitting,
      isOrderProcessed
    });
  }, [firstName, lastName, email, phone, address, message, selectedMethod, isSubmitting, isOrderProcessed]);

  const totalWithFee = (props.cartItems ? 
    props.cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0) : 
    props.amount || 0) + fees[selectedMethod];

  logTaiwanPayment("Calculated total with fee", { 
    baseAmount: props.amount, 
    fee: fees[selectedMethod], 
    total: totalWithFee,
    currency
  });

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    logTaiwanPayment("Form submission started", {
      firstName,
      lastName,
      email,
      phone,
      selectedMethod,
      currency
    });
    
    if (isSubmitting) {
      logTaiwanPayment("Submission already in progress, skipping");
      return;
    }
    
    setIsSubmitting(true);

    try {
      logTaiwanPayment("Preparing order data");
      // Prepare the order data
      const orderData = {
        orderId: props.orderId,
        storeId: props.shopId,
        paymentMethod: "taiwanpayment",
        paymentSubMethod: selectedMethod,
        currency: currency,
        customerInfo: {
          firstName,
          lastName,
          email,
          phone,
          address,
          message,
        },
        items: props.cartItems || props.cart,
        amount: totalWithFee,
        orderDate: orderDate.toISOString(),
      };

      logTaiwanPayment("Sending order to API", { 
        orderId: orderData.orderId,
        currency: orderData.currency 
      });
      
      // Try submission with exponential backoff
      const submitWithRetry = async (attempt = 0) => {
        try {
          const response = await axios.post("/api/submitOrder", orderData);
          logTaiwanPayment("API response received", response.data);
          
          if (response.data && response.data.success) {
            logTaiwanPayment("Order submitted successfully", { 
              orderId: orderData.orderId,
              responseData: response.data
            });
            
            // Save order to local storage if available
            if (props.saveOrderToLocalStorage) {
              logTaiwanPayment("Saving order to localStorage");
              props.saveOrderToLocalStorage({
                orderId: orderData.orderId,
                status: 'Processing',
                customerInfo: {
                  firstName,
                  lastName,
                  email,
                  phone,
                }
              });
            }
            
            setIsOrderProcessed(true);
            if (props.setOrderCompleted) {
              logTaiwanPayment("Setting order as completed");
              props.setOrderCompleted(true);
            }
            return true;
          } else {
            throw new Error(response.data?.message || "Unknown server error");
          }
        } catch (error) {
          logTaiwanPayment("Error in retry attempt", { attempt, error: error.message });
          
          if (attempt < 3) {
            // Exponential backoff: wait longer with each retry
            const backoffTime = Math.pow(2, attempt) * 1000;
            logTaiwanPayment(`Retrying in ${backoffTime}ms (attempt ${attempt + 1}/3)`);
            
            await new Promise(resolve => setTimeout(resolve, backoffTime));
            return submitWithRetry(attempt + 1);
          } else {
            logTaiwanPayment("Max retry attempts reached");
            throw error;
          }
        }
      };
      
      await submitWithRetry();
      
    } catch (error) {
      logTaiwanPayment("Error submitting order", { 
        error: error.message,
        stack: error.stack
      });
      console.error("Error submitting order:", error);
      
      if (props.onError) {
        props.onError("Error processing your order: " + (error.message || "Please try again."));
      } else {
        // Fallback error handling if onError prop is not provided
        alert("Error processing your order: " + (error.message || "Please try again."));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    // Log when component renders
    logTaiwanPayment("Taiwan Payment Form rendered", { 
      orderId: props.orderId,
      selectedMethod,
      isOrderProcessed
    });
    
    // Log when component unmounts
    return () => {
      logTaiwanPayment("Taiwan Payment Form unmounting");
    };
  }, []);

  // Payment methods available for Taiwan
  const paymentMethods = [
    {
      id: 'bank',
      name: 'Chuyển khoản ngân hàng',
      logo: BANK_TRANSFER_LOGO,
      desc: 'Chuyển khoản đến tài khoản ngân hàng tại Đài Loan',
      fee: fees.bank
    },
    {
      id: '7-11',
      name: '7-Eleven (iBON)',
      logo: SEVEN_ELEVEN_LOGO,
      desc: 'Thanh toán tại máy ibon của 7-Eleven',
      fee: fees["7-11"]
    },
    {
      id: '7-11-card',
      name: '7-Eleven (Thẻ tín dụng/ghi nợ)',
      logo: CREDIT_CARD_LOGO,
      desc: 'Thanh toán bằng thẻ tín dụng/ghi nợ qua cổng thanh toán 7-Eleven',
      fee: fees["7-11"]
    },
    {
      id: 'family-mart',
      name: 'FamilyMart',
      logo: FAMILY_MART_LOGO,
      desc: 'Thanh toán tại FamilyMart',
      fee: fees["family-mart"]
    },
    {
      id: 'ok-mart',
      name: 'OK Mart',
      logo: OK_MART_LOGO,
      desc: 'Thanh toán tại OK Mart',
      fee: fees["ok-mart"]
    },
    /* {
      id: 'hi-life',
      name: 'Hi-Life',
      logo: HI_LIFE_LOGO,
      desc: 'Thanh toán tại Hi-Life',
      fee: fees["hi-life"]
    } */
  ];

  if (isOrderProcessed) {
    logTaiwanPayment("Showing thank you message for processed order");
    return (
      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
        <h3 className="text-2xl font-bold text-green-800 mb-4">Cảm ơn bạn đã đặt hàng!</h3>
        <p className="mb-2">Đơn hàng của bạn (#{props.orderId}) đã được gửi thành công.</p>
        <p className="mb-4">
          Chúng tôi sẽ xử lý đơn hàng và gửi email cho bạn với hướng dẫn thanh toán theo phương thức
          đã chọn: <strong>{selectedMethod}</strong>.
        </p>
        <div className="mt-6">
          <Link href={`/orders`}>
            <a className="text-blue-600 hover:text-blue-800" onClick={() => {
              logTaiwanPayment("View orders link clicked");
            }}>Xem đơn hàng của bạn</a>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="payment-forms-container">
      <h2 className="text-xl font-bold mb-4">Chọn phương thức thanh toán</h2>
      
      <div className="flex flex-col md:flex-row gap-6">
        {/* Payment Methods Column */}
        <div className="md:w-1/3">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className={`flex items-center p-3 mb-2 rounded-lg cursor-pointer border transition-all ${
                  selectedMethod === method.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:bg-gray-50"
                }`}
                onClick={() => setSelectedMethod(method.id)}
              >
                <div className="flex-shrink-0 h-10 w-10 mr-3">
                  <img
                    src={method.logo}
                    alt={method.name}
                    className="h-full w-full object-contain"
                  />
                </div>
                <div className="flex-grow">
                  <h3 className="font-medium">{method.name}</h3>
                  <p className="text-sm text-gray-600">{method.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Payment Form Column */}
        <div className="md:w-2/3">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            {/* Fee and Total information */}
            <div className="mb-4 bg-gray-50 p-3 rounded border">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Tổng tiền sản phẩm:</span>
                <span className="font-medium">{currency} {(props.cartItems ? 
                  props.cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0) : 
                  props.amount || 0).toLocaleString()}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Phí thanh toán ({
                  selectedMethod === "bank" ? "Chuyển khoản ngân hàng" : 
                  selectedMethod === "7-11" ? "7-Eleven" :
                  selectedMethod === "family-mart" ? "FamilyMart" :
                  selectedMethod === "ok-mart" ? "OK Mart" :
                  /* selectedMethod === "hi-life" ? "Hi-Life" : */
                  selectedMethod === "credit-card" ? "Thẻ tín dụng/ghi nợ" : ""
                }):</span>
                <span className="font-medium text-orange-600">{currency} {fees[selectedMethod].toLocaleString()}</span>
              </div>
              <div className="flex justify-between pt-2 border-t">
                <span className="font-medium">Tổng thanh toán:</span>
                <span className="font-bold text-lg">{currency} {totalWithFee.toLocaleString()}</span>
              </div>
            </div>
            {renderPaymentForm()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentFormTaiwan;
