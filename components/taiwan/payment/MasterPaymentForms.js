import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import Image from '../../../components/Image';
import Button from '../../../components/Button';
import { SiteContext } from '../../../context/mainContext';
import { v4 as uuidv4 } from 'uuid';
import Link from 'next/link';
import { 
  TaiwanBankTransferForm,
  SevenEleveniBON,
  CardPaymentForm,
  FamilyMartPaymentForm,
  OKMartPayment,
  /* HiLifePayment, */
  CreditCardPayment
} from './methods';
// Import the checkout logger
import {
  logCheckoutProcess,
  logPaymentApiCall,
  logPaymentApiResponse,
  logTransaction,
  logCheckoutError
} from '../../../utils/checkoutLogger';

// Image URLs
const BANK_TRANSFER_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/bank-transfer-logo.webp";
const SEVEN_ELEVEN_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/7-11-logo.webp";
const FAMILY_MART_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/familymart-logo.webp";
const OK_MART_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/ok-mart-logo.webp";
/* const HI_LIFE_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/hi-life-logo.webp"; */
const CREDIT_CARD_LOGO = "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/logo-visa-card.webp";

const Input = ({ onChange, value, name, placeholder, type, min, required }) => (
  <input
    onChange={onChange}
    value={value}
    className="mt-2 text-sm shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
    type={type || "text"}
    placeholder={placeholder}
    name={name}
    min={min}
    required={required}
  />
);

const calculateTotalForCurrentStore = (cart, shopId) => {
  let total = 0;
  for (const item of cart) {
    if (item.store === shopId) {
      total += item.price * item.quantity;
    }
  }
  return total;
};

// Enhanced logging utility for Taiwan Payment Form that uses the checkout logger
const logTaiwanPayment = (message, data = null) => {
  // Use the checkout logger to log payment events
  return logCheckoutProcess('taiwan_payment', { 
    message, 
    data,
    component: 'MasterPaymentForms'
  });
}

// Payment Steps component to explain the process for each payment method
const PaymentSteps = ({ method }) => {
  let steps = [];

  switch (method) {
    case "bank":
      steps = [
        "Nhập thông tin đơn hàng và cá nhân",
        "Nhận thông tin tài khoản ngân hàng",
        "Chuyển khoản với nội dung là mã đơn hàng",
        "Hệ thống tự động xác nhận khi thanh toán thành công"
      ];
      break;
    case "7-11":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng 7-Eleven gần nhất",
        "Tìm máy ibon và chọn chức năng thanh toán",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break;
    case "family-mart":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng FamilyMart gần nhất",
        "Tìm máy FamiPort và chọn chức năng thanh toán",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break;
    case "ok-mart":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng OK Mart gần nhất",
        "Sử dụng máy thanh toán tại cửa hàng",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break;
    /* case "hi-life":
      steps = [
        "Hoàn tất đơn hàng và nhận mã thanh toán",
        "Đến cửa hàng Hi-Life gần nhất",
        "Sử dụng máy thanh toán tại cửa hàng",
        "Nhập mã thanh toán của bạn",
        "Mang biên lai đến quầy thu ngân và thanh toán",
        "Đơn hàng sẽ được xử lý tự động"
      ];
      break; */
  }

  return (
    <div className="bg-blue-50 p-4 rounded-lg mt-4 border border-blue-100">
      <h3 className="font-medium text-lg mb-2">Quy trình thanh toán</h3>
      <ol className="list-decimal pl-5 space-y-1">
        {steps.map((step, index) => (
          <li key={index} className="text-sm">{step}</li>
        ))}
      </ol>
    </div>
  );
};

const MasterPaymentForms = (props) => {
  const { 
    currency = "NT$", 
    amount, 
    cart, 
    shopId, 
    paymentMethod = "7-11", // Default payment method
    orderId
  } = props;
  
  logTaiwanPayment("Initializing Taiwan Payment Form", { 
    orderId: props.orderId, 
    shopId: props.shopId, 
    amount: props.amount,
    paymentMethod,
    currency
  });

  const { email: contextEmail, phone: contextPhone } = useContext(SiteContext);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState(contextEmail || "");
  const [phone, setPhone] = useState(contextPhone || "");
  const [address, setAddress] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderDate, setOrderDate] = useState(new Date());
  const [isOrderProcessed, setIsOrderProcessed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Payment fee based on the payment method
  const fees = {
    bank: 5,
    "7-11": 25,
    "7-11-card": 25,
    "family-mart": 25,
    "ok-mart": 25,
    /* "hi-life": 25, */
    "credit-card": 43,
    "taiwanbanktransfer": 5
  };

  // Effect to track method selection for debugging
  useEffect(() => {
    logTaiwanPayment("Payment method selected", { paymentMethod, orderId });
  }, [paymentMethod, orderId]);

  // Show the appropriate payment form based on the selected method
  const renderPaymentForm = () => {
    logTaiwanPayment("Rendering payment form", { paymentMethod, orderId });
    
    switch (paymentMethod) {
      case "bank":
        return <TaiwanBankTransferForm {...props} />;
      case "7-11":
        logTaiwanPayment("Rendering 7-11 payment form", { orderId });
        return <SevenEleveniBON {...props} />;
      case "family-mart":
        logTaiwanPayment("Rendering FamilyMart payment form", { orderId });
        return <FamilyMartPaymentForm {...props} />;
      case "7-11-card":
        return <CardPaymentForm {...props} />;
      case "ok-mart":
        return <OKMartPayment amount={props.amount} currency={currency} />;
      /* case "hi-life":
        return <HiLifePayment amount={props.amount} currency={currency} />; */
      case "credit-card":
        return <CreditCardPayment amount={props.amount} currency={currency} />;
      default:
        logTaiwanPayment("Payment method not recognized, falling back to bank transfer", { 
          requestedMethod: paymentMethod, 
          orderId 
        });
        return <TaiwanBankTransferForm {...props} />;
    }
  };

  const handleInputChange = (setter) => (e) => {
    logTaiwanPayment(`Updating form field: ${e.target.name || e.target.id}`, { 
      field: e.target.name || e.target.id,
      value: e.target.value,
      orderId
    });
    setter(e.target.value);
  };

  // Effect to track state changes
  useEffect(() => {
    logTaiwanPayment("Form state updated", {
      firstName,
      lastName,
      email,
      phone,
      address,
      messageLength: message?.length,
      paymentMethod,
      isSubmitting,
      isOrderProcessed,
      orderId
    });
  }, [firstName, lastName, email, phone, address, message, paymentMethod, isSubmitting, isOrderProcessed, orderId]);

  const totalWithFee = (props.cartItems ? 
    props.cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0) : 
    props.amount || 0) + fees[paymentMethod];

  logTaiwanPayment("Calculated total with fee", { 
    baseAmount: props.amount, 
    fee: fees[paymentMethod], 
    total: totalWithFee,
    currency,
    orderId
  });

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    logTaiwanPayment("Form submission started", {
      firstName,
      lastName,
      email,
      phone,
      paymentMethod,
      currency,
      orderId
    });
    
    if (isSubmitting) {
      logTaiwanPayment("Submission already in progress, skipping", { orderId });
      return;
    }
    
    setIsSubmitting(true);

    try {
      logTaiwanPayment("Preparing order data", { orderId });
      // Prepare the order data
      const orderData = {
        orderId: props.orderId,
        storeId: props.shopId,
        paymentMethod: "taiwanpayment",
        paymentSubMethod: paymentMethod,
        currency: currency,
        customerInfo: {
          firstName,
          lastName,
          email,
          phone,
          address,
          message,
        },
        items: props.cartItems || props.cart,
        amount: totalWithFee,
        orderDate: orderDate.toISOString(),
      };

      logTaiwanPayment("Sending order to API", { 
        orderId: orderData.orderId,
        currency: orderData.currency 
      });
      
      // Log the transaction initiation
      logTransaction(orderData.orderId, 'INITIATED', {
        paymentMethod: `taiwan-${paymentMethod}`,
        amount: totalWithFee,
        currency,
        customerInfo: {
          firstName,
          lastName,
          email,
          phone,
          address
        },
        items: orderData.items ? orderData.items.map(item => ({
          name: item.name,
          sku: item.sku,
          price: item.price,
          quantity: item.quantity
        })) : []
      });
      
      // Try submission with exponential backoff
      const submitWithRetry = async (attempt = 0) => {
        try {
          // Log the API call
          logPaymentApiCall(
            'taiwan-payment-api',
            '/api/submitOrder',
            orderData,
            { method: 'POST' }
          );
          
          const response = await axios.post("/api/submitOrder", orderData);
          
          // Log the API response
          logPaymentApiResponse(
            'taiwan-payment-api',
            '/api/submitOrder',
            response.data,
            response.status
          );
          
          logTaiwanPayment("API response received", {
            orderId: orderData.orderId,
            status: response.status,
            success: response.data?.success
          });
          
          if (response.data && response.data.success) {
            logTaiwanPayment("Order submitted successfully", { 
              orderId: orderData.orderId,
              responseData: response.data
            });
            
            // Log the transaction update
            logTransaction(orderData.orderId, 'PROCESSING', {
              response: response.data,
              paymentMethod: `taiwan-${paymentMethod}`
            });
            
            // Save order to local storage if available
            if (props.saveOrderToLocalStorage) {
              logTaiwanPayment("Saving order to localStorage", { orderId: orderData.orderId });
              props.saveOrderToLocalStorage({
                orderId: orderData.orderId,
                status: 'Processing',
                customerInfo: {
                  firstName,
                  lastName,
                  email,
                  phone,
                }
              });
            }
            
            setIsOrderProcessed(true);
            if (props.setOrderCompleted) {
              logTaiwanPayment("Setting order as completed", { orderId: orderData.orderId });
              props.setOrderCompleted(true);
            }
            return true;
          } else {
            throw new Error(response.data?.message || "Unknown server error");
          }
        } catch (error) {
          logTaiwanPayment("Error in retry attempt", { 
            attempt, 
            error: error.message,
            orderId: orderData.orderId
          });
          
          // Log the error
          logCheckoutError('taiwan-payment-retry', error, {
            attempt,
            orderId: orderData.orderId,
            paymentMethod: `taiwan-${paymentMethod}`
          });
          
          if (attempt < 3) {
            // Exponential backoff: wait longer with each retry
            const backoffTime = Math.pow(2, attempt) * 1000;
            logTaiwanPayment(`Retrying in ${backoffTime}ms (attempt ${attempt + 1}/3)`, {
              orderId: orderData.orderId
            });
            
            await new Promise(resolve => setTimeout(resolve, backoffTime));
            return submitWithRetry(attempt + 1);
          } else {
            logTaiwanPayment("Max retry attempts reached", { orderId: orderData.orderId });
            
            // Log transaction failure after max retries
            logTransaction(orderData.orderId, 'FAILED', {
              reason: 'max_retries_exceeded',
              error: error.message
            });
            
            throw error;
          }
        }
      };
      
      await submitWithRetry();
      
    } catch (error) {
      logTaiwanPayment("Error submitting order", { 
        error: error.message,
        orderId: props.orderId
      });
      
      // Log the error with detailed information
      logCheckoutError('taiwan-payment-submit', error, {
        orderId: props.orderId,
        paymentMethod: `taiwan-${paymentMethod}`,
        customerInfo: {
          firstName,
          lastName,
          email,
          phone
        }
      });
      
      if (props.onError) {
        props.onError("Error processing your order: " + (error.message || "Please try again."));
      } else {
        // Fallback error handling if onError prop is not provided
        alert("Error processing your order: " + (error.message || "Please try again."));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    // Log when component renders
    logTaiwanPayment("Taiwan Payment Form rendered", { 
      orderId: props.orderId,
      paymentMethod,
      isOrderProcessed
    });
    
    // Log when component unmounts
    return () => {
      logTaiwanPayment("Taiwan Payment Form unmounting", { orderId: props.orderId });
    };
  }, []);

  // Get payment method details
  const getPaymentMethodDetails = () => {
    const methodDetails = {
      'bank': {
        name: 'SINOPAC QR Payment',
        logo: BANK_TRANSFER_LOGO,
        desc: 'Thanh toán bằng mã QR động của ngân hàng SINOPAC',
      },
      '7-11': {
        name: '7-Eleven (iBON)',
        logo: SEVEN_ELEVEN_LOGO,
        desc: 'Thanh toán tại máy ibon của 7-Eleven',
      },
      '7-11-card': {
        name: '7-Eleven (Thẻ tín dụng/ghi nợ)',
        logo: CREDIT_CARD_LOGO,
        desc: 'Thanh toán bằng thẻ tín dụng/ghi nợ qua cổng thanh toán 7-Eleven',
      },
      'family-mart': {
        name: 'FamilyMart',
        logo: FAMILY_MART_LOGO,
        desc: 'Thanh toán tại FamilyMart',
      }
    };
    
    return methodDetails[paymentMethod] || methodDetails['bank'];
  };

  if (isOrderProcessed) {
    logTaiwanPayment("Showing thank you message for processed order", { orderId: props.orderId });
    return (
      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
        <h3 className="text-2xl font-bold text-green-800 mb-4">Cảm ơn bạn đã đặt hàng!</h3>
        <p className="mb-2">Đơn hàng của bạn (#{props.orderId}) đã được gửi thành công.</p>
        <p className="mb-4">
          Chúng tôi sẽ xử lý đơn hàng và gửi email cho bạn với hướng dẫn thanh toán theo phương thức
          đã chọn: <strong>{getPaymentMethodDetails().name}</strong>.
        </p>
        <div className="mt-6">
          <Link href={`/${shopId}/orders`}>
            <a className="text-blue-600 hover:text-blue-800" onClick={() => {
              logTaiwanPayment("View orders link clicked", { orderId: props.orderId });
            }}>Xem đơn hàng của bạn</a>
          </Link>
        </div>
      </div>
    );
  }

  const methodDetails = getPaymentMethodDetails();

  return (
    <div className="payment-form-container">
      <h2 className="text-xl font-bold mb-4">Thanh toán với {methodDetails.name}</h2>
      
      <div className="bg-white p-4 rounded-lg shadow-sm">
        {/* Payment method header */}
        <div className="flex items-center p-3 mb-4 rounded-lg border border-gray-200 bg-gray-50">
          <div className="flex-shrink-0 h-10 w-10 mr-3">
            <img
              src={methodDetails.logo}
              alt={methodDetails.name}
              className="h-full w-full object-contain"
            />
          </div>
          <div className="flex-grow">
            <h3 className="font-medium">{methodDetails.name}</h3>
            <p className="text-sm text-gray-600">{methodDetails.desc}</p>
          </div>
        </div>
        
        {/* Fee and Total information */}
        <div className="mb-4 bg-gray-50 p-3 rounded border">
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Tổng tiền sản phẩm:</span>
            <span className="font-medium">{currency} {(props.cartItems ? 
              props.cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0) : 
              props.amount || 0).toLocaleString()}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Phí thanh toán ({methodDetails.name}):</span>
            <span className="font-medium text-orange-600">{currency} {(fees[paymentMethod] !== undefined ? fees[paymentMethod] : 0).toLocaleString()}</span>
          </div>
          <div className="flex justify-between pt-2 border-t">
            <span className="font-medium">Tổng thanh toán:</span>
            <span className="font-bold text-lg">{currency} {totalWithFee.toLocaleString()}</span>
          </div>
        </div>

        {/* Payment steps for the selected method */}
        <PaymentSteps method={paymentMethod} />
        
        {/* Render the specific payment form */}
        {renderPaymentForm()}
      </div>
    </div>
  );
};

export default MasterPaymentForms;
