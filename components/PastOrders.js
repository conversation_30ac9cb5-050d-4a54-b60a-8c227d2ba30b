import Link from "next/link"
import { FaSpinner } from "react-icons/fa"

const PastOrders = ({ 
  showPastOrders, 
  pastOrders, 
  isPastOrdersLoading, 
  store,
  formatDate,
  getOrderStatusLabel,
  getOrderStatusColor 
}) => {
  if (!showPastOrders) {
    return null;
  }

  return (
    <div className="mb-4">
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <h3 className="font-medium">Đơn hàng gần đây ({pastOrders.length})</h3>
        </div>
        
        {isPastOrdersLoading ? (
          <div className="p-4 text-center">
            <FaSpinner className="animate-spin inline mr-2" />
            Đang tải đơn hàng...
          </div>
        ) : pastOrders.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <PERSON>h<PERSON>ng tìm thấy đơn hàng nào
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {pastOrders.map((order) => (
              <div key={order.id} className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                                          <span className="font-medium">#{order.id}</span>
                    <span className="ml-2 text-sm text-gray-500">{formatDate(order.createdAt || order.date)}</span>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${getOrderStatusColor(order.paymentStatus || order.status)}`}>
                    {getOrderStatusLabel(order.paymentStatus || order.status)}
                  </span>
                </div>
                
                {/* Order items summary */}
                <div className="mt-2 text-sm text-gray-600">
                  <p>Sản phẩm: {order.items ? order.items.length : 'N/A'}</p>
                  <p>Tổng tiền: {order.currency || ''}{order.total ? order.total.toLocaleString() : 'N/A'}</p>
                  {order.paymentMethod && (
                    <p>Phương thức thanh toán: {order.paymentMethod}</p>
                  )}
                </div>
                
                {/* View order details link */}
                <div className="mt-3">
                  <Link href={`/${store}/order/${order.id}`}>
                    <button className="text-xs px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded">
                      Xem chi tiết
                    </button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PastOrders; 