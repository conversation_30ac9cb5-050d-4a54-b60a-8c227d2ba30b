import React, { useState, useContext } from 'react';
import { setCustomerId } from '../utils/customerAuth';
import { SiteContext } from '../context/mainContext';

const LoginButton = ({ onLoginSuccess }) => {
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [customerIdInput, setCustomerIdInput] = useState('');
  const [showLoginForm, setShowLoginForm] = useState(false);
  
  // Get the forceCustomerCartSync method from context
  const { forceCustomerCartSync } = useContext(SiteContext);
  
  const handleLogin = async (e) => {
    e.preventDefault();
    
    if (!customerIdInput.trim()) {
      setErrorMessage('Please enter a valid customer ID');
      return;
    }
    
    setIsLoggingIn(true);
    setErrorMessage('');
    
    try {
      // Set the customer ID and link cart
      const customerId = customerIdInput.trim();
      const customerInfo = {
        name: "Demo Customer",
        email: `${customerId}@example.com`,
        loginDate: new Date().toISOString()
      };
      
      // Use site context method to force cart sync
      forceCustomerCartSync(customerId, customerInfo);
      
      // Hide the form
      setShowLoginForm(false);
      
      // Call the onLoginSuccess callback if provided
      if (onLoginSuccess) {
        onLoginSuccess(customerId, customerInfo);
      }
      
      // Reload the page to ensure all components reflect the logged-in state
      window.location.reload();
      
    } catch (error) {
      console.error('Login error:', error);
      setErrorMessage('An error occurred during login. Please try again.');
    } finally {
      setIsLoggingIn(false);
    }
  };
  
  return (
    <div className="login-container">
      {!showLoginForm ? (
        <button 
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => setShowLoginForm(true)}
        >
          Login
        </button>
      ) : (
        <div className="login-form p-4 bg-white shadow-md rounded-md border">
          <h3 className="text-lg font-medium mb-4">Customer Login</h3>
          
          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Customer ID:</label>
              <input
                type="text"
                value={customerIdInput}
                onChange={(e) => setCustomerIdInput(e.target.value)}
                className="w-full px-3 py-2 border rounded"
                placeholder="Enter your customer ID"
                required
              />
            </div>
            
            {errorMessage && (
              <div className="text-red-500 text-sm mb-4">{errorMessage}</div>
            )}
            
            <div className="flex justify-end gap-2">
              <button 
                type="button"
                className="px-3 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                onClick={() => setShowLoginForm(false)}
              >
                Cancel
              </button>
              <button 
                type="submit"
                className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                disabled={isLoggingIn}
              >
                {isLoggingIn ? 'Logging in...' : 'Login'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default LoginButton; 