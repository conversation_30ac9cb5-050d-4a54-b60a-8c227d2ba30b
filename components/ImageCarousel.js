import React, { useState } from 'react';
import Image from './Image';

function ImageCarousel({ imageUrls, ...props }) {
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!imageUrls || imageUrls.length === 0) {
    imageUrls = ['https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/shopme.logo.3.webp'];
  }

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex === imageUrls.length - 1 ? 0 : prevIndex + 1));
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? imageUrls.length - 1 : prevIndex - 1));
  };

  return (
    <div className="carousel-container relative w-full">
      <div className="carousel-image-container flex justify-center items-center">
        <Image src={imageUrls[currentIndex]} alt="ShopMe.top" className="w-auto h-auto object-contain" />
      </div>
      {imageUrls.length > 1 && (
        <>
          <button onClick={prevSlide} className="prev absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-1">
            &#10094;
          </button>
          <button onClick={nextSlide} className="next absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-1">
            &#10095;
          </button>
        </>
      )}
    </div>
  );
}

export default ImageCarousel;