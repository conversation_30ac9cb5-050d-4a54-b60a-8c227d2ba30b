import React from 'react';
import AddInventory from '../components/formComponents/AddInventory';
import ViewInventory from '../components/ViewInventory';
import ViewOrder from '../components/ViewOrder';
import ViewCustomer from '../components/ViewCustomer';
import fetchCategoriesOneShop from '../utils/categoryProviderOneShop';

class Inventory extends React.Component {
  state = {
    viewState: 'view', // Changed to viewState
    viewStateInventory: 'view',
    viewStateOrder: 'view',
    viewStateCustomer: 'view',
    inventory: [],
    order: [],
    customer: [],
    activeFilters: {
      category: null,
      stockLevel: null,
      priceRange: null,
      availability: null,
      currency: null,
    },
    categories: [],
    stockLevels: [
      { name: '<PERSON>àng sắp hết', value: 'low', threshold: 10 },
      { name: 'Còn hàng', value: 'in', threshold: 50 },
      { name: '<PERSON><PERSON>ng d<PERSON><PERSON> dào', value: 'high', threshold: Infinity }
    ],
    priceRanges: [
      { name: 'Dưới 500k', value: 'budget', min: 0, max: 500000 },
      { name: '500k-1 triệu', value: 'medium', min: 500000, max: 1000000 },
      { name: '1-3 triệu', value: 'high', min: 1000000, max: 3000000 },
      { name: 'Trên 3 triệu', value: 'premium', min: 3000000, max: Infinity }
    ],
    availabilityOptions: [
      { name: 'Còn hàng', value: 'active', status: "1" },
      { name: 'Ngừng kinh doanh', value: 'inactive', status: "0" }
    ],
    currencyOptions: [
      { name: 'VND', value: 'VND' },
      { name: 'USD', value: 'USD' }
    ],
  };

  componentDidMount() {
    this.fetchInventory();
    this.fetchCategories();
  }

  fetchInventory = async () => {
    try {
      const response = await fetch('/api/inventory');
      if (!response.ok) {
        throw new Error('Failed to fetch inventory');
      }
      const inventory = await response.json();
      
      // Process inventory items to ensure categories are properly formatted
      const processedInventory = inventory.map(item => {
        // If the item doesn't have categories field, initialize it as an empty array
        if (!item.categories) {
          item.categories = [];
        }
        
        // If item has a single category field but not categories array, add it to categories
        if (item.category && !Array.isArray(item.categories)) {
          item.categories = [item.category];
        }
        
        return item;
      });
      
      // Extract available currencies from inventory
      const currencies = new Set();
      processedInventory.forEach(item => {
        if (item.currency) {
          currencies.add(item.currency);
        }
      });
      
      // Update currency options if found in inventory
      if (currencies.size > 0) {
        const currencyOptions = Array.from(currencies).map(currency => ({
          name: currency,
          value: currency
        }));
        this.setState({ inventory: processedInventory, currencyOptions });
      } else {
        this.setState({ inventory: processedInventory });
      }
      
      console.log('Processed inventory:', processedInventory);
    } catch (error) {
      console.error('Error fetching inventory:', error);
      // You might want to show an error message to the user here
    }
  };

  fetchCategories = async () => {
    try {
      // If you have a store ID to use, pass it to fetchCategoriesOneShop
      // For now using null, adjust based on your app's needs
      const storeId = this.props.storeId || null;
      const categories = await fetchCategoriesOneShop(storeId);
      this.setState({ categories });
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // INVENTORY START
  toggleViewState = (viewStateInventory) => {
    this.setState(() => ({ viewStateInventory }));
  };

  addItem = (item) => {
    this.setState((prevState) => ({
      inventory: [...prevState.inventory, item],
    }));
  };

  updateItem = (id, updates) => {
    this.setState((prevState) => ({
      inventory: prevState.inventory.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));
  };

  removeItem = (id) => {
    this.setState((prevState) => ({
      inventory: prevState.inventory.filter((item) => item.id !== id),
    }));
  };

  getItem = (id) => {
    return this.state.inventory.find((item) => item.id === id);
  };

  getInventory = () => {
    return this.state.inventory;
  };

  searchInventory = (query) => {
    return this.state.inventory.filter((item) =>
      item.name.toLowerCase().includes(query.toLowerCase())
    );
  };

  sortInventory = (sortBy, order) => {
    return this.state.inventory.sort((a, b) => {
      if (a[sortBy] < b[sortBy]) return order === 'asc' ? -1 : 1;
      if (a[sortBy] > b[sortBy]) return order === 'asc' ? 1 : -1;
      return 0;
    });
  };

  filterInventory = (filters) => {
    return this.state.inventory.filter((item) =>
      Object.keys(filters).every((key) => item[key] === filters[key])
    );
  };

  // New filter functions
  getFilteredInventory = () => {
    const { inventory, activeFilters } = this.state;
    
    return inventory.filter(item => {
      // Filter by category
      if (activeFilters.category) {
        const categoryName = activeFilters.category.name;
        const categorySlug = activeFilters.category.slug;
        
        // No categories data on item
        if (!item.categories && !item.category) {
          return false;
        }
        
        // Direct category name/slug match (exact match case)
        if (item.category === categoryName || item.category === categorySlug) {
          return true;
        }
        
        // Check categories array (if it exists)
        if (Array.isArray(item.categories)) {
          // Check for string match
          for (const cat of item.categories) {
            if (typeof cat === 'string' && (cat === categoryName || this.slugifyCategory(cat) === categorySlug)) {
              return true;
            }
            // Check for object match
            if (typeof cat === 'object' && cat !== null) {
              if (cat.name === categoryName || cat.slug === categorySlug) {
                return true;
              }
            }
          }
        }
        
        // Last resort - check if category name appears in product name or description 
        // This is a fallback for special categories like "Weekend"
        if (categoryName === "Weekend" || categorySlug === "weekend") {
          return (
            (item.name && item.name.toLowerCase().includes("weekend")) || 
            (item.description && item.description.toLowerCase().includes("weekend"))
          );
        }
        
        return false;
      }
      
      // Filter by stock level
      if (activeFilters.stockLevel) {
        const level = this.state.stockLevels.find(l => l.value === activeFilters.stockLevel);
        if (level) {
          const prevLevel = this.state.stockLevels[this.state.stockLevels.indexOf(level) - 1];
          const minThreshold = prevLevel ? prevLevel.threshold : 0;
          
          // Get current inventory count from new or old structure
          const currentInventoryCount = item.inventory && typeof item.inventory === 'object' 
            ? (item.inventory.count || 0) 
            : (item.currentInventory || item.quantity || 0);
          
          if (currentInventoryCount <= minThreshold || currentInventoryCount > level.threshold) {
            return false;
          }
        }
      }
      
      // Filter by price range
      if (activeFilters.priceRange) {
        const range = this.state.priceRanges.find(r => r.value === activeFilters.priceRange);
        if (range && (item.price < range.min || item.price >= range.max)) {
          return false;
        }
      }
      
      // Filter by availability
      if (activeFilters.availability) {
        const option = this.state.availabilityOptions.find(o => o.value === activeFilters.availability);
        if (option && item.activestatus !== option.status) {
          return false;
        }
      }
      
      // Filter by currency
      if (activeFilters.currency && item.currency !== activeFilters.currency) {
        return false;
      }
      
      return true;
    });
  };

  // Helper method to slugify category for comparison
  slugifyCategory = (category) => {
    // Handle different types of input
    if (!category) return '';
    
    // If category is an object with a slug property, return that
    if (typeof category === 'object' && category.slug) {
      return category.slug;
    }
    
    // If category is an object with a name property, slugify the name
    if (typeof category === 'object' && category.name) {
      category = category.name;
    }
    
    // Simple slugify function to match what's used in categoryProviderOneShop
    return String(category).toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  toggleCategoryFilter = (category) => {
    console.log('Toggling category filter:', category.name);
    this.setState(prevState => {
      // Compare by slug instead of object reference
      const currentCategory = prevState.activeFilters.category;
      const isSameCategory = currentCategory && currentCategory.slug === category.slug;
      
      return {
        activeFilters: {
          ...prevState.activeFilters,
          category: isSameCategory ? null : category
        }
      };
    }, () => {
      console.log('Category filter updated:', 
        this.state.activeFilters.category ? this.state.activeFilters.category.name : 'none');
    });
  };

  toggleStockLevelFilter = (stockLevel) => {
    this.setState(prevState => ({
      activeFilters: {
        ...prevState.activeFilters,
        stockLevel: prevState.activeFilters.stockLevel === stockLevel ? null : stockLevel
      }
    }));
  };

  togglePriceRangeFilter = (priceRange) => {
    this.setState(prevState => ({
      activeFilters: {
        ...prevState.activeFilters,
        priceRange: prevState.activeFilters.priceRange === priceRange ? null : priceRange
      }
    }));
  };

  toggleAvailabilityFilter = (availability) => {
    this.setState(prevState => ({
      activeFilters: {
        ...prevState.activeFilters,
        availability: prevState.activeFilters.availability === availability ? null : availability
      }
    }));
  };

  toggleCurrencyFilter = (currency) => {
    this.setState(prevState => ({
      activeFilters: {
        ...prevState.activeFilters,
        currency: prevState.activeFilters.currency === currency ? null : currency
      }
    }));
  };

  clearFilters = () => {
    this.setState({
      activeFilters: {
        category: null,
        stockLevel: null,
        priceRange: null,
        availability: null,
        currency: null,
      }
    });
  };

  // INVENTORY END

  // ORDER START
  toggleViewState = (viewStateInventory) => {
    this.setState(() => ({ viewStateInventory }));
  };

  addItem = (item) => {
    this.setState((prevState) => ({
      order: [...prevState.order, item],
    }));
  };

  updateItem = (id, updates) => {
    this.setState((prevState) => ({
      order: prevState.order.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));
  };

  removeItem = (id) => {
    this.setState((prevState) => ({
      order: prevState.order.filter((item) => item.id !== id),
    }));
  };

  getItem = (id) => {
    return this.state.order.find((item) => item.id === id);
  };

  getOrder = () => {
    return this.state.order;
  };

  searchOrder = (query) => {
    return this.state.order.filter((item) =>
      item.name.toLowerCase().includes(query.toLowerCase())
    );
  };

  sortOrder = (sortBy, order) => {
    return this.state.order.sort((a, b) => {
      if (a[sortBy] < b[sortBy]) return order === 'asc' ? -1 : 1;
      if (a[sortBy] > b[sortBy]) return order === 'asc' ? 1 : -1;
      return 0;
    });
  };

  filterOrder = (filters) => {
    return this.state.order.filter((item) =>
      Object.keys(filters).every((key) => item[key] === filters[key])
    );
  };

  // ORDER END

  // CUSTOMER START
  toggleViewState = (viewStateInventory) => {
    this.setState(() => ({ viewStateInventory }));
  };

  addItem = (item) => {
    this.setState((prevState) => ({
      customer: [...prevState.customer, item],
    }));
  };

  updateItem = (id, updates) => {
    this.setState((prevState) => ({
      customer: prevState.customer.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));
  };

  removeItem = (id) => {
    this.setState((prevState) => ({
      customer: prevState.customer.filter((item) => item.id !== id),
    }));
  };

  getItem = (id) => {
    return this.state.customer.find((item) => item.id === id);
  };

  getCustomer = () => {
    return this.state.customer;
  };

  searchCustomer = (query) => {
    return this.state.customer.filter((item) =>
      item.name.toLowerCase().includes(query.toLowerCase())
    );
  };

  sortCustomer = (sortBy, order) => {
    return this.state.customer.sort((a, b) => {
      if (a[sortBy] < b[sortBy]) return order === 'asc' ? -1 : 1;
      if (a[sortBy] > b[sortBy]) return order === 'asc' ? 1 : -1;
      return 0;
    });
  };

  filterCustomer = (filters) => {
    return this.state.customer.filter((item) =>
      Object.keys(filters).every((key) => item[key] === filters[key])
    );
  };

  // CUSTOMER END

  render() {
    // Always calculate filteredInventory fresh on each render
    const filteredInventory = this.getFilteredInventory();
    console.log(`Rendering with ${filteredInventory.length} filtered items out of ${this.state.inventory.length} total items`);
    
    if (this.state.activeFilters.category) {
      console.log(`Active category filter: ${this.state.activeFilters.category.name}`);
    }
    
    return (
      <div className="bg-gray-50 min-h-screen p-3 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-4 sm:mb-6 lg:mb-8 border-b border-gray-200 pb-3 sm:pb-4">
            Quản lý kho hàng
          </h1>
          
          {this.state.viewState === 'view' ? (
            <div className="bg-white rounded-lg shadow-lg">
              {this.state.viewStateInventory === 'view' && (
                <div>
                  <div className="p-3 sm:p-4 lg:p-6 bg-white rounded-t-lg border-b border-gray-200">
                    <h2 className="text-lg sm:text-xl font-semibold text-gray-700 mb-4 sm:mb-6">Bộ lọc tìm kiếm</h2>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                      <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <h3 className="text-base sm:text-lg font-semibold mb-3 text-gray-700 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                          </svg>
                          Lọc theo danh mục
                        </h3>
                        <div className="flex flex-wrap gap-1 sm:gap-2">
                          {this.state.categories.map((category) => (
                            <button
                              key={category.slug}
                              onClick={() => this.toggleCategoryFilter(category)}
                              className={`px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-sm font-medium transition-all duration-200 ${
                                this.state.activeFilters.category && 
                                this.state.activeFilters.category.slug === category.slug
                                  ? 'bg-primary text-white shadow-md'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            >
                              {category.name}
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-gray-700 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clipRule="evenodd" />
                          </svg>
                          Lọc theo tồn kho
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {this.state.stockLevels.map((level) => (
                            <button
                              key={level.value}
                              onClick={() => this.toggleStockLevelFilter(level.value)}
                              className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
                                this.state.activeFilters.stockLevel === level.value
                                  ? 'bg-primary text-white shadow-md'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            >
                              {level.name}
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-gray-700 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                          </svg>
                          Lọc theo giá
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {this.state.priceRanges.map((range) => (
                            <button
                              key={range.value}
                              onClick={() => this.togglePriceRangeFilter(range.value)}
                              className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
                                this.state.activeFilters.priceRange === range.value
                                  ? 'bg-primary text-white shadow-md'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            >
                              {range.name}
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-gray-700 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Lọc theo tình trạng
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {this.state.availabilityOptions.map((option) => (
                            <button
                              key={option.value}
                              onClick={() => this.toggleAvailabilityFilter(option.value)}
                              className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
                                this.state.activeFilters.availability === option.value
                                  ? 'bg-primary text-white shadow-md'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            >
                              {option.name}
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-gray-700 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                          Lọc theo tiền tệ
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {this.state.currencyOptions.map((option) => (
                            <button
                              key={option.value}
                              onClick={() => this.toggleCurrencyFilter(option.value)}
                              className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
                                this.state.activeFilters.currency === option.value
                                  ? 'bg-primary text-white shadow-md'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            >
                              {option.name}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    {(this.state.activeFilters.category || 
                      this.state.activeFilters.stockLevel || 
                      this.state.activeFilters.priceRange || 
                      this.state.activeFilters.availability || 
                      this.state.activeFilters.currency) && (
                      <div className="mt-6 flex justify-end">
                        <button
                          onClick={this.clearFilters}
                          className="flex items-center px-4 py-2 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors duration-200 shadow-sm"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                          Xóa bộ lọc
                        </button>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-3 sm:p-4 lg:p-6">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-2">
                      <h2 className="text-lg sm:text-xl font-semibold text-gray-700">Danh sách sản phẩm</h2>
                      <span className="bg-primary text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm self-start sm:self-auto">
                        {filteredInventory.length} sản phẩm
                      </span>
                    </div>
                    {console.log('Passing filtered inventory to ViewInventory:', filteredInventory)}
                    <ViewInventory
                      inventory={filteredInventory}
                      updateItem={this.updateItem}
                      removeItem={this.removeItem}
                    />
                  </div>
                </div>
              )}
              {this.state.viewStateOrder === 'view' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-700 mb-6">Đơn hàng</h2>
                  <ViewOrder
                    orders={this.state.order} // Changed to this.state.order
                  // Include any necessary props for ViewOrder component
                  />
                </div>
              )}
              {this.state.viewStateCustomer === 'view' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-700 mb-6">Khách hàng</h2>
                  <ViewCustomer
                    customers={this.state.customer}
                  // Include any necessary props for ViewCustomer component
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-lg p-6">
              {this.state.viewStateInventory === 'add' && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-700 mb-6">Thêm sản phẩm mới</h2>
                  <AddInventory addItem={this.addItem} />
                </div>
              )}
              {/* {this.state.viewStateOrder === 'add' && (
                <AddOrder addItem={this.addItem} />
                // Include any necessary props for AddOrder component
              )}
              {this.state.viewStateCustomer === 'add' && (
                <AddCustomer addItem={this.addItem} />
                // Include any necessary props for AddCustomer component
              )} */}
            </div>
          )}
          
          <button
            onClick={this.props.signOut}
            className="mt-6 flex items-center bg-gray-700 hover:bg-black text-white font-medium py-2 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
            type="button"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V9.5a1 1 0 00-2 0V15H4V5h8.5a1 1 0 000-2H3z" clipRule="evenodd" />
              <path d="M11 11V9h2v6h2V9a2 2 0 00-2-2h-2a2 2 0 00-2 2v2h2z" />
            </svg>
            Đăng xuất
          </button>
        </div>
      </div>
    );
  }
}

export default Inventory;