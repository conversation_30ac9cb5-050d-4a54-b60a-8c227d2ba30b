import React, { useState, useEffect } from 'react';
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { FaLock, FaSpinner } from 'react-icons/fa';
import { v4 as uuidv4 } from 'uuid';

const CARD_ELEMENT_OPTIONS = {
  style: {
    base: {
      color: '#32325d',
      fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
      fontSmoothing: 'antialiased',
      fontSize: '16px',
      '::placeholder': {
        color: '#aab7c4',
      },
    },
    invalid: {
      color: '#fa755a',
      iconColor: '#fa755a',
    },
  },
  hidePostalCode: true,
};

const PaymentFormStripe = ({ 
  storeObject, 
  cart, 
  total, 
  currency = 'USD', 
  orderId = '',
  setErrorMessage, 
  setOrderCompleted 
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [billingDetails, setBillingDetails] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
  });

  useEffect(() => {
    // Log initialization for debugging
    console.log('[STRIPE] Initializing Stripe payment form', { 
      orderId, 
      storeId: storeObject?.storeId,
      currency
    });
    
    // Log a warning if no orderId is provided
    if (!orderId) {
      console.warn('[STRIPE] No orderId provided to PaymentFormStripe');
    }
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBillingDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      setErrorMessage('Payment system is still loading. Please try again.');
      return;
    }

    if (billingDetails.name.trim() === '' || 
        billingDetails.email.trim() === '') {
      setErrorMessage('Please fill in all required fields');
      return;
    }

    setIsProcessing(true);
    setErrorMessage('');

    try {
      // Create payment intent on server
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: total,
          currency: currency.toLowerCase(),
          orderId,
          storeId: storeObject.storeId,
          cartItems: cart,
          customerInfo: billingDetails
        }),
      });

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      const { clientSecret } = await response.json();

      // Complete payment with Stripe
      const cardElement = elements.getElement(CardElement);
      
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: billingDetails.name,
            email: billingDetails.email,
            phone: billingDetails.phone,
            address: {
              line1: billingDetails.address
            }
          },
        },
      });

      if (error) {
        throw new Error(error.message);
      } else if (paymentIntent.status === 'succeeded') {
        // Payment succeeded
        console.log('[STRIPE] Payment succeeded', paymentIntent);
        setOrderCompleted(true);
      } else {
        throw new Error(`Payment status: ${paymentIntent.status}`);
      }
    } catch (error) {
      console.error('[STRIPE] Payment error:', error);
      setErrorMessage(error.message || 'An error occurred during payment processing');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Credit card security notice */}
      <div className="bg-blue-50 p-4 rounded-md flex items-start border border-blue-100 mb-6">
        <span className="text-blue-700 mr-3 pt-1">
          <FaLock />
        </span>
        <div className="text-sm text-blue-800">
          <p className="font-medium">Secure payment processing</p>
          <p>Your payment information is processed securely. We do not store credit card details.</p>
        </div>
      </div>

      {/* Billing information */}
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            id="name"
            name="name"
            type="text"
            value={billingDetails.name}
            onChange={handleInputChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email Address *
          </label>
          <input
            id="email"
            name="email"
            type="email"
            value={billingDetails.email}
            onChange={handleInputChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            value={billingDetails.phone}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
            Billing Address
          </label>
          <input
            id="address"
            name="address"
            type="text"
            value={billingDetails.address}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Card details */}
      <div className="mt-6">
        <label htmlFor="card" className="block text-sm font-medium text-gray-700 mb-1">
          Card Details *
        </label>
        <div className="border border-gray-300 rounded-md p-3">
          <CardElement options={CARD_ELEMENT_OPTIONS} />
        </div>
      </div>

      {/* Payment button */}
      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className={`w-full mt-6 bg-gray-800 text-white py-3 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center ${
          isProcessing ? 'opacity-70 cursor-not-allowed' : ''
        }`}
      >
        {isProcessing ? (
          <>
            <FaSpinner className="animate-spin mr-2" />
            Processing...
          </>
        ) : (
          `Pay ${currency}${total.toLocaleString()}`
        )}
      </button>
    </form>
  );
};

// Special flag to hide the default submit button in CheckoutPanel
PaymentFormStripe.hideDefaultButton = true;

export default PaymentFormStripe; 