import { useState } from 'react'
import config from '../config.json'

const PaymentFormsTaiwan = ({ 
  order, 
  paymentMethod, 
  onPaymentSubmit, 
  onPaymentError 
}) => {
  const [cardNumber, setCardNumber] = useState('')
  const [cardExpiry, setCardExpiry] = useState('')
  const [cardCVC, setCardCVC] = useState('')
  const [installment, setInstallment] = useState('0')
  const [isProcessing, setIsProcessing] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsProcessing(true)

    try {
      // Validate card details
      if (!cardNumber || !cardExpiry || !cardCVC) {
        throw new Error('請填寫完整的信用卡資料')
      }

      // Validate card number format
      if (!/^\d{16}$/.test(cardNumber.replace(/\s/g, ''))) {
        throw new Error('信用卡號碼格式不正確')
      }

      // Validate expiry date format (MM/YY)
      if (!/^(0[1-9]|1[0-2])\/([0-9]{2})$/.test(cardExpiry)) {
        throw new Error('到期日格式不正確')
      }

      // Validate CVC format
      if (!/^\d{3,4}$/.test(cardCVC)) {
        throw new Error('安全碼格式不正確')
      }

      // Prepare payment data
      const paymentData = {
        merchantID: config.tw.card.merchantID,
        merchantPassword: config.tw.card.merchantPassword,
        orderID: order.id,
        amount: order.total,
        cardNumber: cardNumber.replace(/\s/g, ''),
        cardExpiry: cardExpiry,
        cardCVC: cardCVC,
        installment: installment,
        returnURL: config.tw.card.returnURL,
        notifyURL: config.tw.card.notifyURL
      }

      // Submit payment
      const test=false
      
      const response = await fetch(test ? config.tw.card.testUrl : config.tw.card.liveUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData)
      })

      if (!response.ok) {
        throw new Error('付款處理失敗')
      }

      const result = await response.json()
      onPaymentSubmit(result)

    } catch (error) {
      onPaymentError(error.message)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="max-w-md mx-auto p-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            信用卡號碼
          </label>
          <input
            type="text"
            value={cardNumber}
            onChange={(e) => setCardNumber(e.target.value)}
            placeholder="1234 5678 9012 3456"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
            maxLength="19"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              到期日
            </label>
            <input
              type="text"
              value={cardExpiry}
              onChange={(e) => setCardExpiry(e.target.value)}
              placeholder="MM/YY"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
              maxLength="5"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              安全碼
            </label>
            <input
              type="text"
              value={cardCVC}
              onChange={(e) => setCardCVC(e.target.value)}
              placeholder="123"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
              maxLength="4"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            分期付款
          </label>
          <select
            value={installment}
            onChange={(e) => setInstallment(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
          >
            <option value="0">一次付清</option>
            <option value="3">3期</option>
            <option value="6">6期</option>
            <option value="12">12期</option>
            <option value="18">18期</option>
            <option value="24">24期</option>
          </select>
        </div>

        <div className="mt-6">
          <button
            type="submit"
            disabled={isProcessing}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? '處理中...' : '確認付款'}
          </button>
        </div>
      </form>

      {/* Test Card Information */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-sm font-medium text-gray-900 mb-2">測試卡號</h3>
        <div className="space-y-2 text-xs text-gray-600">
          <p>一次付清：****************, ****************</p>
          <p>分期付款：****************, 3560512000000001, 3560562000000001</p>
          <p>銀聯卡：6200000000000001</p>
          <p className="mt-2">到期日及安全碼可任意填寫</p>
        </div>
      </div>
    </div>
  )
}

export default PaymentFormsTaiwan 