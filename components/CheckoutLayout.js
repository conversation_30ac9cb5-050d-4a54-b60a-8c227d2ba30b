import { useState, useEffect } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { FaArrowLeft, FaSpinner } from 'react-icons/fa'

// Shared checkout layout for all checkout flows
const CheckoutLayout = ({ 
  title, 
  children, 
  checkoutData,
  isProcessing = false
}) => {
  const router = useRouter()
  const { store } = router.query
  
  // Return to cart
  const handleBackToCart = () => {
    router.push(`/${store}/cart`)
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>{title || 'Checkout'}</title>
        <meta name="description" content="Complete your purchase" />
      </Head>
      
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex items-center">
          <button 
            onClick={handleBackToCart}
            className="mr-4 text-gray-600 hover:text-gray-900"
            aria-label="Back to cart"
            disabled={isProcessing}
          >
            <FaArrowLeft />
          </button>
          <h1 className="text-lg font-medium flex-1">{title || 'Checkout'}</h1>
        </div>
      </header>
      
      {/* Processing overlay */}
      {isProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg flex items-center">
            <FaSpinner className="animate-spin text-xl mr-3" />
            <span>Processing your order...</span>
          </div>
        </div>
      )}
      
      {/* Main content */}
      <main className="container mx-auto px-4 py-6">
        <div className="max-w-3xl mx-auto">
          {children}
        </div>
      </main>
      
      {/* Footer */}
      <footer className="mt-auto py-6 bg-white border-t">
        <div className="container mx-auto px-4 text-center text-sm text-gray-500">
          <p>© {new Date().getFullYear()} MAG GROUP. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

export default CheckoutLayout 