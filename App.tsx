import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import CustomerLogin from './components/auth/CustomerLogin';
import CustomerDashboard from './components/customer/CustomerDashboard';
import ProtectedRoute from './components/auth/ProtectedRoute';

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route path="/customer/login" element={<CustomerLogin />} />

        {/* Protected routes */}
        <Route
          path="/customer/dashboard"
          element={
            <ProtectedRoute>
              <CustomerDashboard />
            </ProtectedRoute>
          }
        />

        {/* Redirect root to customer dashboard */}
        <Route path="/" element={<Navigate to="/customer/dashboard" replace />} />
      </Routes>
    </Router>
  );
};

export default App; 